# Stage 1: Build the Angular app
FROM --platform=linux/amd64 node:18.19 AS build

# Set the working directory
WORKDIR /app

ENV API_URL=https://api.falcon-vss.com/api

# Copy the package files and install dependencies
COPY package*.json ./

# تعديل أمر التثبيت لزيادة وقت الانتظار وإضافة محاولات إعادة المحاولة
RUN npm config set fetch-retry-mintimeout 20000 \
    && npm config set fetch-retry-maxtimeout 120000 \
    && npm config set fetch-retries 5 \
    && npm install --legacy-peer-deps --no-fund --no-audit --prefer-offline

# Copy the Angular project files
COPY . .

# Build the Angular app
RUN npm run build

# Stage 2: Serve the Angular app using Nginx
# for mac
FROM --platform=linux/amd64 nginx:alpine

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY ./docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh
## Copy the build output from the previous stage to the Nginx web root
COPY --from=build /app/dist/FalconFramework/browser /usr/share/nginx/html

## Expose port 80
EXPOSE 80

## Start Nginx server
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
