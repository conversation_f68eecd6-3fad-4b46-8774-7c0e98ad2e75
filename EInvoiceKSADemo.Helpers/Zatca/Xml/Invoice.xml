<?xml version="1.0" encoding="utf-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
		 xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
		 xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
		 xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2">
	<cbc:ProfileID>reporting:1.0</cbc:ProfileID>
	<cbc:ID>@InvoiceNumber</cbc:ID>
	<cbc:UUID>@Id</cbc:UUID>
	<cbc:IssueDate>@IssueDate</cbc:IssueDate>
	<cbc:IssueTime>@IssueTime</cbc:IssueTime>
	<cbc:InvoiceTypeCode name="@TransactionTypeCode">@InvoiceTypeCode</cbc:InvoiceTypeCode>
	<cbc:DocumentCurrencyCode>SAR</cbc:DocumentCurrencyCode>
	<cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>
	<cbc:LineCountNumeric>@LinesCount</cbc:LineCountNumeric>
	<cac:BillingReference if="@ReferenceId">
		<cac:InvoiceDocumentReference>
			<cbc:ID>@ReferenceId</cbc:ID>
		</cac:InvoiceDocumentReference>
	</cac:BillingReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>ICV</cbc:ID>
		<cbc:UUID>@Order</cbc:UUID>
	</cac:AdditionalDocumentReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>PIH</cbc:ID>
		<cac:Attachment>
			<cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">@PreviousInvoiceHash</cbc:EmbeddedDocumentBinaryObject>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>

	<cac:AccountingSupplierParty model="Supplier">
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="@IdentityType">@IdentityNumber</cbc:ID>
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>@StreetName</cbc:StreetName>
				<cbc:BuildingNumber>@BuildingNumber</cbc:BuildingNumber>
				<cbc:PlotIdentification>@AdditionalStreetAddress</cbc:PlotIdentification>
				<cbc:CitySubdivisionName>@DistrictName</cbc:CitySubdivisionName>
				<cbc:CityName>@CityName</cbc:CityName>
				<cbc:PostalZone>@PostalCode</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>@CountryCode</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>@SellerTRN</cbc:CompanyID>
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>@SellerName</cbc:RegistrationName>
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty model="Customer">
		<cac:Party>
			<cac:PartyIdentification if="IdentityNumber">
				<cbc:ID schemeID="@IdentityType">@IdentityNumber</cbc:ID>
			</cac:PartyIdentification>
			<cac:PostalAddress if="CustomerName">
				<cbc:StreetName>@StreetName</cbc:StreetName>
				<cbc:BuildingNumber>@BuildingNumber</cbc:BuildingNumber>
				<cbc:PlotIdentification>@AdditionalStreetAddress</cbc:PlotIdentification>
				<cbc:CitySubdivisionName>@DistrictName</cbc:CitySubdivisionName>
				<cbc:CityName>@CityName</cbc:CityName>
				<cbc:PostalZone>@ZipCode</cbc:PostalZone>
				<cbc:CountrySubentity>@RegionName</cbc:CountrySubentity>
				<cac:Country>
					<cbc:IdentificationCode>SA</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>@VatRegNumber</cbc:CompanyID>
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity if="CustomerName">
				<cbc:RegistrationName>@CustomerName</cbc:RegistrationName>
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:PaymentMeans>
		<cbc:PaymentMeansCode>@PaymentMeansCode</cbc:PaymentMeansCode>
		<cbc:InstructionNote>@Notes</cbc:InstructionNote>
	</cac:PaymentMeans>
	<cac:AllowanceCharge if="Discount">
		<cbc:ChargeIndicator>false</cbc:ChargeIndicator>
		<cbc:AllowanceChargeReason>discount</cbc:AllowanceChargeReason>
		<cbc:Amount currencyID="SAR">@Discount</cbc:Amount>
		<cac:TaxCategory>
			<cbc:ID schemeID="UN/ECE 5305" schemeAgencyID="6">@DiscountTaxCategory</cbc:ID>
			<cbc:Percent>@Tax</cbc:Percent>
			<cac:TaxScheme>
				<cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">VAT</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SAR">@TaxAmount</cbc:TaxAmount>
		<cac:TaxSubtotal repeat="SubTotals">
			<cbc:TaxableAmount currencyID="SAR">@TotalWithoutTax</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="SAR">@TaxAmount</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>@TaxCategory</cbc:ID>
				<cbc:Percent>@Tax</cbc:Percent>
				<cbc:TaxExemptionReasonCode if="TaxCategoryReason">@TaxCategoryReasonCode</cbc:TaxExemptionReasonCode>
				<cbc:TaxExemptionReason if="TaxCategoryReason">@TaxCategoryReason</cbc:TaxExemptionReason>
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SAR">@TaxAmount</cbc:TaxAmount>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="SAR">@TotalWithoutTaxAndDiscount</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="SAR">@TotalWithoutTax</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="SAR">@TotalWithTax</cbc:TaxInclusiveAmount>
		<cbc:AllowanceTotalAmount currencyID="SAR">@Discount</cbc:AllowanceTotalAmount>
		<cbc:ChargeTotalAmount  currencyID="SAR">0.00</cbc:ChargeTotalAmount>
		<cbc:PrepaidAmount currencyID="SAR">0.00</cbc:PrepaidAmount>
		<cbc:PayableAmount currencyID="SAR">@TotalWithTax</cbc:PayableAmount>
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine  repeat="Lines">
		<cbc:ID>@Index</cbc:ID>
		<cbc:InvoicedQuantity unitCode="PCE">@Quantity</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="SAR">@TotalWithoutTax</cbc:LineExtensionAmount>
		<cac:AllowanceCharge if="LineDiscount">
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator>
			<cbc:AllowanceChargeReason>discount</cbc:AllowanceChargeReason>
			<cbc:Amount currencyID="SAR">@LineDiscount</cbc:Amount>
		</cac:AllowanceCharge>
		<cac:TaxTotal>
			<cbc:TaxAmount currencyID="SAR">@TaxAmount</cbc:TaxAmount>
			<cbc:RoundingAmount currencyID="SAR">@TotalWithTax</cbc:RoundingAmount>
		</cac:TaxTotal>
		<cac:Item>
			<cbc:Name>@ProductName</cbc:Name>
			<cac:SellersItemIdentification if="Id">
				<cbc:ID>@Id</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>@TaxCategory</cbc:ID>
				<cbc:Percent>@Tax</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SAR">@NetPrice</cbc:PriceAmount>
			<cbc:BaseQuantity  unitCode="PCE">1</cbc:BaseQuantity>
			<cac:AllowanceCharge if="PriceDiscount">
				<cbc:ChargeIndicator>false</cbc:ChargeIndicator>
				<cbc:AllowanceChargeReason>discount</cbc:AllowanceChargeReason>
				<cbc:Amount currencyID="SAR">@PriceDiscount</cbc:Amount>
				<cbc:BaseAmount currencyID="SAR">@GrossPrice</cbc:BaseAmount>
			</cac:AllowanceCharge>
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>