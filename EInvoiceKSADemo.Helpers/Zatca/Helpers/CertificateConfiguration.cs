﻿/*
 * Author  : <PERSON>
 * Email   : <EMAIL>
 * LinkedIn: https://www.linkedin.com/in/ahmoosa/
 * Date    : 26/9/2022
 */
using EInvoiceKSADemo.Helpers.Zatca.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EInvoiceKSADemo.Helpers.Zatca.Helpers
{
    public class CertificateConfiguration : ICertificateConfiguration
    {
        public CertificateDetails GetCertificateDetails()
        {
            // File & Cloud & Database 

            //Test Data
            var certificateDetails = new CertificateDetails
            {
                Certificate = Convert.ToBase64String(Encoding.UTF8.GetBytes("MIID9jCCA5ugAwIBAgITbwAAeCy9aKcLA99HrAABAAB4LDAKBggqhkjOPQQDAjBjMRUwEwYKCZImiZPyLGQBGRYFbG9jYWwxEzARBgoJkiaJk/IsZAEZFgNnb3YxFzAVBgoJkiaJk/IsZAEZFgdleHRnYXp0MRwwGgYDVQQDExNUU1pFSU5WT0lDRS1TdWJDQS0xMB4XDTIyMDQxOTIwNDkwOVoXDTI0MDQxODIwNDkwOVowWTELMAkGA1UEBhMCU0ExEzARBgNVBAoTCjMxMjM0NTY3ODkxDDAKBgNVBAsTA1RTVDEnMCUGA1UEAxMeVFNULS05NzA1NjAwNDAtMzEyMzQ1Njc4OTAwMDAzMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEYYMMoOaFYAhMO/steotfZyavr6p11SSlwsK9azmsLY7b1b+FLhqMArhB2dqHKboxqKNfvkKDePhpqjui5hcn0aOCAjkwggI1MIGaBgNVHREEgZIwgY+kgYwwgYkxOzA5BgNVBAQMMjEtVFNUfDItVFNUfDMtNDdmMTZjMjYtODA2Yi00ZTE1LWIyNjktN2E4MDM4ODRiZTljMR8wHQYKCZImiZPyLGQBAQwPMzEyMzQ1Njc4OTAwMDAzMQ0wCwYDVQQMDAQxMTAwMQwwCgYDVQQaDANUU1QxDDAKBgNVBA8MA1RTVDAdBgNVHQ4EFgQUO5ZiU7NakU3eejVa3I2S1B2sDwkwHwYDVR0jBBgwFoAUdmCM+wagrGdXNZ3PmqynK5k1tS8wTgYDVR0fBEcwRTBDoEGgP4Y9aHR0cDovL3RzdGNybC56YXRjYS5nb3Yuc2EvQ2VydEVucm9sbC9UU1pFSU5WT0lDRS1TdWJDQS0xLmNybDCBrQYIKwYBBQUHAQEEgaAwgZ0wbgYIKwYBBQUHMAGGYmh0dHA6Ly90c3RjcmwuemF0Y2EuZ292LnNhL0NlcnRFbnJvbGwvVFNaRWludm9pY2VTQ0ExLmV4dGdhenQuZ292LmxvY2FsX1RTWkVJTlZPSUNFLVN1YkNBLTEoMSkuY3J0MCsGCCsGAQUFBzABhh9odHRwOi8vdHN0Y3JsLnphdGNhLmdvdi5zYS9vY3NwMA4GA1UdDwEB/wQEAwIHgDAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwMwJwYJKwYBBAGCNxUKBBowGDAKBggrBgEFBQcDAjAKBggrBgEFBQcDAzAKBggqhkjOPQQDAgNJADBGAiEA7mHT6yg85jtQGWp3M7tPT7Jk2+zsvVHGs3bU5Z7YE68CIQD60ebQamYjYvdebnFjNfx4X4dop7LsEBFCNSsLY0IFaQ==")),
                PrivateKey = "MHQCAQEEIDyLDaWIn/1/g3PGLrwupV4nTiiLKM59UEqUch1vDfhpoAcGBSuBBAAKoUQDQgAEYYMMoOaFYAhMO/steotfZyavr6p11SSlwsK9azmsLY7b1b+FLhqMArhB2dqHKboxqKNfvkKDePhpqjui5hcn0Q==",
                CSR = null,
                ExpiredDate = DateTime.Now.AddYears(5),
                StartedDate = DateTime.Now,
                Secret = "Xlj15LyMCgSC66ObnEO/qVPfhSbs3kDTjWnGheYhfSs=",
                UserName = "TUlJRDFEQ0NBM21nQXdJQkFnSVRid0FBZTNVQVlWVTM0SS8rNVFBQkFBQjdkVEFLQmdncWhrak9QUVFEQWpCak1SVXdFd1lLQ1pJbWlaUHlMR1FCR1JZRmJHOWpZV3d4RXpBUkJnb0praWFKay9Jc1pBRVpGZ05uYjNZeEZ6QVZCZ29Ka2lhSmsvSXNaQUVaRmdkbGVIUm5ZWHAwTVJ3d0dnWURWUVFERXhOVVUxcEZTVTVXVDBsRFJTMVRkV0pEUVMweE1CNFhEVEl5TURZeE1qRTNOREExTWxvWERUSTBNRFl4TVRFM05EQTFNbG93U1RFTE1Ba0dBMVVFQmhNQ1UwRXhEakFNQmdOVkJBb1RCV0ZuYVd4bE1SWXdGQVlEVlFRTEV3MW9ZWGxoSUhsaFoyaHRiM1Z5TVJJd0VBWURWUVFERXdreE1qY3VNQzR3TGpFd1ZqQVFCZ2NxaGtqT1BRSUJCZ1VyZ1FRQUNnTkNBQVRUQUs5bHJUVmtvOXJrcTZaWWNjOUhEUlpQNGI5UzR6QTRLbTdZWEorc25UVmhMa3pVMEhzbVNYOVVuOGpEaFJUT0hES2FmdDhDL3V1VVk5MzR2dU1ObzRJQ0p6Q0NBaU13Z1lnR0ExVWRFUVNCZ0RCK3BId3dlakViTUJrR0ExVUVCQXdTTVMxb1lYbGhmREl0TWpNMGZETXRNVEV5TVI4d0hRWUtDWkltaVpQeUxHUUJBUXdQTXpBd01EYzFOVGc0TnpBd01EQXpNUTB3Q3dZRFZRUU1EQVF4TVRBd01SRXdEd1lEVlFRYURBaGFZWFJqWVNBeE1qRVlNQllHQTFVRUR3d1BSbTl2WkNCQ2RYTnphVzVsYzNNek1CMEdBMVVkRGdRV0JCU2dtSVdENmJQZmJiS2ttVHdPSlJYdkliSDlIakFmQmdOVkhTTUVHREFXZ0JSMllJejdCcUNzWjFjMW5jK2FyS2NybVRXMUx6Qk9CZ05WSFI4RVJ6QkZNRU9nUWFBL2hqMW9kSFJ3T2k4dmRITjBZM0pzTG5waGRHTmhMbWR2ZGk1ellTOURaWEowUlc1eWIyeHNMMVJUV2tWSlRsWlBTVU5GTFZOMVlrTkJMVEV1WTNKc01JR3RCZ2dyQmdFRkJRY0JBUVNCb0RDQm5UQnVCZ2dyQmdFRkJRY3dBWVppYUhSMGNEb3ZMM1J6ZEdOeWJDNTZZWFJqWVM1bmIzWXVjMkV2UTJWeWRFVnVjbTlzYkM5VVUxcEZhVzUyYjJsalpWTkRRVEV1WlhoMFoyRjZkQzVuYjNZdWJHOWpZV3hmVkZOYVJVbE9WazlKUTBVdFUzVmlRMEV0TVNneEtTNWpjblF3S3dZSUt3WUJCUVVITUFHR0gyaDBkSEE2THk5MGMzUmpjbXd1ZW1GMFkyRXVaMjkyTG5OaEwyOWpjM0F3RGdZRFZSMFBBUUgvQkFRREFnZUFNQjBHQTFVZEpRUVdNQlFHQ0NzR0FRVUZCd01DQmdnckJnRUZCUWNEQXpBbkJna3JCZ0VFQVlJM0ZRb0VHakFZTUFvR0NDc0dBUVVGQndNQ01Bb0dDQ3NHQVFVRkJ3TURNQW9HQ0NxR1NNNDlCQU1DQTBrQU1FWUNJUUNWd0RNY3E2UE8rTWNtc0JYVXovdjFHZGhHcDdycVNhMkF4VEtTdjgzOElBSWhBT0JOREJ0OSszRFNsaWpvVmZ4enJkRGg1MjhXQzM3c21FZG9HV1ZyU3BHMQ=="
            };

            //var certificateDetails = new CertificateDetails
            //{
            //    Certificate = "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",
            //    PrivateKey = "-----BEGIN EC PRIVATE KEY-----\r\nMHQCAQEEIGFZ8p2b+LW4TlRVr20pK5iOWb667z+SU7Ae6lN/POoloAcGBSuBBAAK\r\noUQDQgAEiBlMOchVipQr0g94RGyw/OzWrCST9Iz62kWahLH/XTSDttMvaBDMMh3b\r\nlXK2WmBjpgFfiRm+5LPUfhJwzyHS0A==\r\n-----END EC PRIVATE KEY-----",
            //    CSR = null,
            //    ExpiredDate = DateTime.Now.AddYears(5),
            //    StartedDate = DateTime.Now,
            //    Secret = "WIVWl5V4s9RQTLRfrtOsQkQep2Q02RP0BpgHWeGnUoU=",
            //    UserName = "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"
            //};

            //Simulation Test

            //var certificateDetails = new CertificateDetails
            //{
            //    Certificate = "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",
            //    PrivateKey = "-----BEGIN EC PRIVATE KEY-----\r\nMHQCAQEEIGnub2LAEHk9t8sBszbhe6A79/kM0/2ejWtRMPaYwQFCoAcGBSuBBAAK\r\noUQDQgAE4fkOrMGbz8Q9QOamyCLA57HpLcmdpbxeq2LCUT7Iz1wqUFZmVB9APMwf\r\n6h1lAHXVwAKdmBjpMbTd5L4iaF422g==\r\n-----END EC PRIVATE KEY-----",
            //    CSR = null,
            //    ExpiredDate = DateTime.Now.AddYears(5),
            //    StartedDate = DateTime.Now,
            //    Secret = "Ql2+BkNTOFphgrjAdifo96mvFZGH1Fl1RnB7A9RvdaY=",
            //    UserName = "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"
            //};

            return certificateDetails;
        }

        public CertificateDetails GetCertificateDetails(string companyId)
        {
            // select * from CertificateSettings where companyId == companyId;

            return new CertificateDetails
            {

            };
        }

        public async Task SaveCsrAndPK(InputCsrModel model)
        {
            var cert = new CertificateDetails
            {
                CSR = model.CSR,
                PrivateKey = model.PrivateKey,
            };

            //Save Cert to Database


        }

        public async Task UpdateCertificate(CSIDResultModel model)
        {
            var cert = GetCertificateDetails(); //model.CompanyId;
            if (cert != null)
            {
                cert.UserName = model.Certificate;
                cert.Certificate = model.Certificate;
                cert.Secret = model.Secret;
                cert.ExpiredDate = model.ExpiredDate;
                cert.StartedDate = model.StartedDate;
            }

            // Update Certificate Details in Database
        }
    }
}
