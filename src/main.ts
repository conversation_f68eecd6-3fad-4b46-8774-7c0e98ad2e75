import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { API_URLS } from './environments/appUrls';
import { environment } from './environments/environment';
import { licenseKey } from './app/license'; // Import the license key
/*import { registerLicense } from 'devextreme/licensing';*/

// Register your DevExtreme license key
/*registerLicense(licenseKey); // Use the imported license key*/

export function getApiUrls() {
   
  return environment.appUrls;

}
const providers = [
  { provide: API_URLS, useFactory: getApiUrls, deps: [] }
];

if (environment.production) {

  enableProdMode();
}

platformBrowserDynamic(providers)
  .bootstrapModule(AppModule)
  .catch((err) => console.error(err));

