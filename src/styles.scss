/* Global Styles */
:root {
  --primary-color: #3699FF;
  --secondary-color: #8950FC;
  --dark-color: #1E1E2D;
  --light-color: #F3F6F9;
  --success-color: #1BC5BD;
  --warning-color: #FFA800;
  --danger-color: #F64E60;
  --text-color: #3F4254;
  --text-muted: #B5B5C3;
  --border-color: #E4E6EF;
}

/* Font Settings */
body {
  font-family: 'Cairo', 'Poppins', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Global Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: #2a88e6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(54, 153, 255, 0.2);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Animation Styles */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

/* You can add global styles to this file, and also import other style files */
@import "./assets/sass/style.scss";
// Replace above style with this css file to enable RTL styles
/*@import "./assets/sass/plugins.scss";*/
@import url("https://fonts.googleapis.com/css2?family=Cairo&display=swap");

// @import "./assets/css/style.rtl.css";
/*@import "./assets/sass/style.angular.scss";*/
@import "@ng-select/ng-select/themes/material.theme.css";
// @import "ngx-toastr/toastr";
// @import "ngx-toastr/toastr-bs4-alert";

@import "ag-grid-community/styles/ag-grid.css";
@import "ag-grid-community/styles/ag-theme-alpine.css";

input.ng-invalid:not(.ng-untouched),
textarea.ng-invalid:not(.ng-untouched) {
  border: 1px solid #ff5370;
}

ng-select.ng-invalid.ng-touched .ng-select-container {
  border-color: #ff5370;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0.5px red;
}

span.ng-invalid:not(.ng-untouched) {
  color: red;
}

/*html[dir='rtl'] {
  text-align: right;
  font-family: "Cairo", sans-serif !important;
}*/

/* إخفاء رسائل ترخيص DevExpress - بداية */
.dx-popup-wrapper .dx-overlay-content .dx-popup-content div {
  &:contains("For evaluation purposes only"),
  &:contains("DevExpress product libraries"),
  &:contains("Please register an existing license"),
  &:contains("purchase a new license") {
    display: none !important;
  }
}

/* إخفاء النافذة المنبثقة بالكامل إذا كانت تحتوي على رسالة الترخيص */
.dx-popup-wrapper .dx-overlay-content {
  &:has(.dx-popup-content div:contains("For evaluation purposes only")),
  &:has(.dx-popup-content div:contains("DevExpress product libraries")),
  &:has(.dx-popup-content div:contains("Please register an existing license")),
  &:has(.dx-popup-content div:contains("purchase a new license")) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}

/* طريقة أخرى لإخفاء رسائل الترخيص - استخدام منتقيات عامة */
.dx-popup-wrapper {
  &[role="dialog"] {
    .dx-popup-content {
      div {
        &:not([class]):not([id]) {
          /* استهداف رسائل الترخيص التي عادة ما تكون بدون class أو id */
          &:contains("evaluation") {
            display: none !important;
          }
        }
      }
    }
  }
}

/* إخفاء رسائل ترخيص DevExpress - نهاية */
