// Performance Optimization Script for Falcon ERP
(function() {
  'use strict';

  // Preload critical resources
  function preloadCriticalResources() {
    const criticalResources = [
      './assets/media/logos/falconerp.png',
      './assets/media/illustrations/dashboard-preview.svg',
      'https://fonts.googleapis.com/css?family=Cairo:300,400,500,600,700',
      'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css'
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      
      if (resource.endsWith('.css')) {
        link.as = 'style';
      } else if (resource.endsWith('.js')) {
        link.as = 'script';
      } else if (resource.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) {
        link.as = 'image';
      } else {
        link.as = 'fetch';
        link.crossOrigin = 'anonymous';
      }
      
      link.href = resource;
      document.head.appendChild(link);
    });
  }

  // Lazy load images
  function setupLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }

  // Optimize font loading
  function optimizeFontLoading() {
    // Use font-display: swap for better performance
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: 'Cairo';
        font-display: swap;
      }
      @font-face {
        font-family: 'Inter';
        font-display: swap;
      }
    `;
    document.head.appendChild(style);
  }

  // Minimize layout shifts
  function minimizeLayoutShifts() {
    // Add aspect ratio containers for images
    const images = document.querySelectorAll('img:not([width]):not([height])');
    images.forEach(img => {
      img.addEventListener('load', function() {
        if (!this.width || !this.height) {
          this.style.aspectRatio = 'auto';
        }
      });
    });
  }

  // Optimize third-party scripts
  function optimizeThirdPartyScripts() {
    // Defer non-critical scripts
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach(script => {
      if (!script.hasAttribute('async') && !script.hasAttribute('defer')) {
        // Add defer to non-critical scripts
        if (!script.src.includes('google-analytics') && 
            !script.src.includes('gtag') &&
            !script.src.includes('env.js')) {
          script.defer = true;
        }
      }
    });
  }

  // Service Worker registration for caching
  function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('./sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  }

  // Critical CSS inlining
  function inlineCriticalCSS() {
    const criticalCSS = `
      /* Critical CSS for above-the-fold content */
      body {
        font-family: 'Cairo', sans-serif;
        margin: 0;
        padding: 0;
        direction: rtl;
      }
      
      .splash-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .hero-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 15px;
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);
  }

  // Resource hints
  function addResourceHints() {
    const hints = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//cdnjs.cloudflare.com' },
      { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
    ];

    hints.forEach(hint => {
      const link = document.createElement('link');
      link.rel = hint.rel;
      link.href = hint.href;
      if (hint.crossorigin) {
        link.crossOrigin = hint.crossorigin;
      }
      document.head.appendChild(link);
    });
  }

  // Performance monitoring
  function monitorPerformance() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0];
          
          // Log performance metrics
          console.log('Performance Metrics:', {
            'DNS Lookup': perfData.domainLookupEnd - perfData.domainLookupStart,
            'TCP Connection': perfData.connectEnd - perfData.connectStart,
            'Request': perfData.responseStart - perfData.requestStart,
            'Response': perfData.responseEnd - perfData.responseStart,
            'DOM Processing': perfData.domContentLoadedEventStart - perfData.responseEnd,
            'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
          });

          // Send to analytics if available
          if (typeof gtag !== 'undefined') {
            gtag('event', 'timing_complete', {
              name: 'load',
              value: Math.round(perfData.loadEventEnd - perfData.navigationStart)
            });
          }
        }, 0);
      });
    }
  }

  // Initialize optimizations
  function init() {
    // Run immediately
    addResourceHints();
    inlineCriticalCSS();
    optimizeFontLoading();
    
    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        preloadCriticalResources();
        setupLazyLoading();
        minimizeLayoutShifts();
        optimizeThirdPartyScripts();
        monitorPerformance();
      });
    } else {
      preloadCriticalResources();
      setupLazyLoading();
      minimizeLayoutShifts();
      optimizeThirdPartyScripts();
      monitorPerformance();
    }

    // Run when window loads
    window.addEventListener('load', () => {
      registerServiceWorker();
    });
  }

  // Start optimization
  init();

  console.log('Performance optimization initialized for Falcon ERP');
})();
