//
// Theme style
//

// Initialize
@import "init";

// Components
@import "./core/components/components";
/*@import "components/components";*/

// Layout
/*@import "./core/layout/base/layout";
@import "layout/layout";*/
.form-label {
  margin-bottom: 0 !important;
  min-width: 90px;
  white-space: nowrap !important;
  overflow: visible;
  flex-shrink: 0;
}

.form-control {
  flex-grow: 1;
  border: none !important;
  border-radius: unset !important;
  border-bottom: 1px solid var(--kt-input-border-color) !important;

  &:hover,
  &:focus {
    border-bottom: 2px solid var(--kt-input-border-color) !important;
  }
}

.form-control.required {
  border-bottom: 2px solid var(--kt-input-border-color) !important;

  &:hover,
  &:focus {
    border-bottom: 3px solid var(--kt-input-border-color) !important;
  }
}

.ng-select {
  flex-grow: 1;

  .ng-select-container {
    min-height: 42px !important;
    border-bottom: 1px solid var(--kt-input-border-color) !important;

    &:hover,
    &:focus-within {
      border-bottom: 2px solid var(--kt-input-border-color) !important;
    }
  }
}

.ng-select.required {
  .ng-select-container {
    border-bottom: 2px solid var(--kt-input-border-color) !important;

    &:hover,
    &:focus-within {
      border-bottom: 3px solid var(--kt-input-border-color) !important;
    }
  }
}

.form-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

[dir="rtl"] .form-label {
  margin-left: 5px;
  margin-right: 0 !important;
}

.name {
  font-size: 30px !important;
}
.name::placeholder {
  font-size: 30px !important;
  font-weight: bold !important;
}
@media (max-width: 600px) {
  .name {
    font-size: 20px !important;
  }
  .name::placeholder {
    font-size: 20px !important;
    font-weight: bold !important;
  }
}
@media (max-width: 520px) {
  .card {
    .card-header {
      padding-right: 0.3rem !important;
      padding-left: 0.3rem !important;
    }
  }
}

.fa-gear {
  font-size: 20px !important;
}
.user-inner {
  width: 275px !important;
}
@media (max-width: 500px) {
  .user-inner {
    width: 225px !important;
  }
  [dir="ltr"] {
    .user-inner {
      transform: translate3d(0px, 60px, 0px) !important;
    }
  }
}
.user-inner {
  width: 275px;
}
.nav-tabs .nav-item .nav-link {
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-tabs .nav-item .nav-link:hover {
  background-color: #007bff;
  color: #fff;
  border-radius: 5px;
}
