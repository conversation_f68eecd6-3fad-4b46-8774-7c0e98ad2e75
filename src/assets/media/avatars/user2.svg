<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>User Avatar 2</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F1416C" offset="0%"></stop>
            <stop stop-color="#E11D48" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="User-Avatar-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <circle id="Background" fill="url(#linearGradient-2)" cx="100" cy="100" r="100"></circle>
        
        <!-- Face -->
        <circle id="Face" fill="#FFD700" cx="100" cy="85" r="40"></circle>
        
        <!-- Hair -->
        <path d="M60,85 C60,85 60,45 100,45 C140,45 140,85 140,85" id="Hair-Top" fill="#5E4B28"></path>
        <path d="M60,85 C60,85 65,65 70,65 C70,65 70,85 60,85" id="Hair-Left" fill="#5E4B28"></path>
        <path d="M140,85 C140,85 135,65 130,65 C130,65 130,85 140,85" id="Hair-Right" fill="#5E4B28"></path>
        
        <!-- Eyes -->
        <circle id="Eye-Left" fill="#181C32" cx="85" cy="80" r="5"></circle>
        <circle id="Eye-Right" fill="#181C32" cx="115" cy="80" r="5"></circle>
        
        <!-- Smile -->
        <path d="M80,95 C80,105 90,115 100,115 C110,115 120,105 120,95" id="Smile" stroke="#181C32" stroke-width="3" stroke-linecap="round"></path>
        
        <!-- Body -->
        <path d="M100,130 L100,180 C70,180 60,160 60,140 L60,130 C60,125 65,120 70,120 L130,120 C135,120 140,125 140,130 L140,140 C140,160 130,180 100,180" id="Body" fill="#FFFFFF"></path>
        
        <!-- Dress -->
        <path d="M70,130 L70,180 C70,180 85,170 100,170 C115,170 130,180 130,180 L130,130 Z" id="Dress" fill="#F1416C"></path>
        <path d="M85,120 L85,140 L115,140 L115,120 Z" id="Collar" fill="#FFFFFF"></path>
    </g>
</svg>
