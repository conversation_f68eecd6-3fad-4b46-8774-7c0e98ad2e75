<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>About Falcon ERP</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3699FF" offset="0%"></stop>
            <stop stop-color="#2563EB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F1416C" offset="0%"></stop>
            <stop stop-color="#E11D48" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-3">
            <stop stop-color="#50CD89" offset="0%"></stop>
            <stop stop-color="#10B981" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFC700" offset="0%"></stop>
            <stop stop-color="#F59E0B" offset="100%"></stop>
        </linearGradient>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="About" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <rect id="Background" fill="#F5F8FA" x="0" y="0" width="800" height="600" rx="20"></rect>
        
        <!-- Office Building Illustration -->
        <g id="Office-Building" transform="translate(100, 50)">
            <!-- Building Structure -->
            <rect id="Building-Main" fill="#FFFFFF" x="150" y="100" width="400" height="400" rx="10"></rect>
            <rect id="Building-Shadow" fill="#E6E6E6" x="150" y="100" width="400" height="20"></rect>
            
            <!-- Windows -->
            <g id="Windows" transform="translate(180, 140)">
                <!-- Row 1 -->
                <rect class="window" fill="#E1F0FF" x="0" y="0" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="80" y="0" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="160" y="0" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="240" y="0" width="60" height="80" rx="5"></rect>
                
                <!-- Row 2 -->
                <rect class="window" fill="#E1F0FF" x="0" y="100" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="80" y="100" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="160" y="100" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="240" y="100" width="60" height="80" rx="5"></rect>
                
                <!-- Row 3 -->
                <rect class="window" fill="#E1F0FF" x="0" y="200" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="80" y="200" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="160" y="200" width="60" height="80" rx="5"></rect>
                <rect class="window" fill="#E1F0FF" x="240" y="200" width="60" height="80" rx="5"></rect>
            </g>
            
            <!-- Door -->
            <rect id="Door" fill="#3699FF" x="320" y="420" width="60" height="80" rx="5"></rect>
            <rect id="Door-Handle" fill="#FFFFFF" x="365" y="460" width="5" height="15" rx="2.5"></rect>
            
            <!-- Company Logo -->
            <circle id="Logo-Background" fill="url(#linearGradient-1)" cx="350" cy="200" r="60"></circle>
            <text id="Logo-Text" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">
                <tspan x="320" y="208">فالكون</tspan>
            </text>
            
            <!-- People -->
            <g id="Person-1" transform="translate(250, 440)">
                <circle id="Head" fill="#FFD700" cx="10" cy="10" r="10"></circle>
                <rect id="Body" fill="#3699FF" x="5" y="20" width="10" height="20" rx="5"></rect>
                <rect id="Legs" fill="#181C32" x="5" y="40" width="10" height="15"></rect>
            </g>
            
            <g id="Person-2" transform="translate(280, 440)">
                <circle id="Head" fill="#FFD700" cx="10" cy="10" r="10"></circle>
                <rect id="Body" fill="#F1416C" x="5" y="20" width="10" height="20" rx="5"></rect>
                <rect id="Legs" fill="#181C32" x="5" y="40" width="10" height="15"></rect>
            </g>
            
            <g id="Person-3" transform="translate(400, 440)">
                <circle id="Head" fill="#FFD700" cx="10" cy="10" r="10"></circle>
                <rect id="Body" fill="#50CD89" x="5" y="20" width="10" height="20" rx="5"></rect>
                <rect id="Legs" fill="#181C32" x="5" y="40" width="10" height="15"></rect>
            </g>
            
            <g id="Person-4" transform="translate(430, 440)">
                <circle id="Head" fill="#FFD700" cx="10" cy="10" r="10"></circle>
                <rect id="Body" fill="#7239EA" x="5" y="20" width="10" height="20" rx="5"></rect>
                <rect id="Legs" fill="#181C32" x="5" y="40" width="10" height="15"></rect>
            </g>
        </g>
        
        <!-- Decorative Elements -->
        <circle id="Decorative-Circle-1" fill="url(#linearGradient-1)" opacity="0.1" cx="100" cy="100" r="50"></circle>
        <circle id="Decorative-Circle-2" fill="url(#linearGradient-3)" opacity="0.1" cx="700" cy="500" r="70"></circle>
        
        <!-- Cloud Shapes -->
        <path d="M600,150 C620,130 650,130 670,150 C690,130 720,130 740,150 C760,170 760,200 740,220 C760,240 760,270 740,290 C720,310 690,310 670,290 C650,310 620,310 600,290 C580,270 580,240 600,220 C580,200 580,170 600,150 Z" id="Cloud-Shape" fill="#FFFFFF" opacity="0.5"></path>
        
        <!-- Text Elements -->
        <g id="Text-Elements" transform="translate(150, 500)">
            <rect id="Text-Box-1" fill="#FFFFFF" x="0" y="0" width="100" height="20" rx="5"></rect>
            <rect id="Text-Box-2" fill="#FFFFFF" x="120" y="0" width="150" height="20" rx="5"></rect>
            <rect id="Text-Box-3" fill="#FFFFFF" x="290" y="0" width="80" height="20" rx="5"></rect>
            <rect id="Text-Box-4" fill="#FFFFFF" x="390" y="0" width="120" height="20" rx="5"></rect>
        </g>
        
        <!-- Glow Effects -->
        <circle id="Glow-1" fill="#3699FF" opacity="0.2" filter="url(#filter-5)" cx="350" cy="200" r="100"></circle>
    </g>
</svg>
