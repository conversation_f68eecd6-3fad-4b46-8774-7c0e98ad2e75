<svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3699ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e88e5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50cd89;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#43a047;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="250" cy="200" r="180" fill="url(#bgGradient)" opacity="0.3"/>
  
  <!-- Person Figure -->
  <g transform="translate(200, 80)">
    <!-- Head -->
    <circle cx="50" cy="40" r="25" fill="#ffc107" stroke="#fff" stroke-width="3"/>
    <!-- Hair -->
    <path d="M25 35 Q50 15 75 35 Q70 25 50 25 Q30 25 25 35" fill="#6c757d"/>
    <!-- Face -->
    <circle cx="42" cy="35" r="2" fill="#343a40"/>
    <circle cx="58" cy="35" r="2" fill="#343a40"/>
    <path d="M45 45 Q50 50 55 45" stroke="#343a40" stroke-width="2" fill="none"/>
    
    <!-- Body -->
    <rect x="35" y="65" width="30" height="50" rx="15" fill="url(#primaryGradient)"/>
    
    <!-- Arms -->
    <rect x="15" y="75" width="20" height="8" rx="4" fill="#ffc107"/>
    <rect x="65" y="75" width="20" height="8" rx="4" fill="#ffc107"/>
    
    <!-- Legs -->
    <rect x="40" y="115" width="8" height="25" rx="4" fill="#6c757d"/>
    <rect x="52" y="115" width="8" height="25" rx="4" fill="#6c757d"/>
  </g>
  
  <!-- Phone Icon -->
  <g transform="translate(80, 150)">
    <rect x="0" y="0" width="40" height="60" rx="8" fill="url(#primaryGradient)" stroke="#fff" stroke-width="2"/>
    <rect x="5" y="8" width="30" height="40" rx="3" fill="#fff"/>
    <circle cx="20" cy="52" r="3" fill="#fff"/>
    <!-- Phone waves -->
    <path d="M45 15 Q55 20 45 25" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
    <path d="M45 20 Q50 22.5 45 25" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
  </g>
  
  <!-- Email Icon -->
  <g transform="translate(350, 160)">
    <rect x="0" y="0" width="50" height="35" rx="5" fill="url(#secondaryGradient)" stroke="#fff" stroke-width="2"/>
    <path d="M0 5 L25 20 L50 5" stroke="#fff" stroke-width="2" fill="none"/>
    <rect x="5" y="5" width="40" height="25" rx="3" fill="#fff" opacity="0.9"/>
    <!-- Email lines -->
    <line x1="10" y1="12" x2="30" y2="12" stroke="#6c757d" stroke-width="1"/>
    <line x1="10" y1="16" x2="35" y2="16" stroke="#6c757d" stroke-width="1"/>
    <line x1="10" y1="20" x2="25" y2="20" stroke="#6c757d" stroke-width="1"/>
  </g>
  
  <!-- Location Icon -->
  <g transform="translate(120, 280)">
    <path d="M20 0 C30 0 40 10 40 20 C40 35 20 50 20 50 C20 50 0 35 0 20 C0 10 10 0 20 0 Z" fill="url(#primaryGradient)" stroke="#fff" stroke-width="2"/>
    <circle cx="20" cy="20" r="8" fill="#fff"/>
    <circle cx="20" cy="20" r="4" fill="url(#primaryGradient)"/>
  </g>
  
  <!-- Chat Bubbles -->
  <g transform="translate(320, 280)">
    <!-- Bubble 1 -->
    <rect x="0" y="0" width="35" height="20" rx="10" fill="url(#primaryGradient)"/>
    <path d="M5 20 L10 25 L15 20" fill="url(#primaryGradient)"/>
    <circle cx="8" cy="10" r="2" fill="#fff"/>
    <circle cx="15" cy="10" r="2" fill="#fff"/>
    <circle cx="22" cy="10" r="2" fill="#fff"/>
    
    <!-- Bubble 2 -->
    <rect x="25" y="30" width="30" height="18" rx="9" fill="url(#secondaryGradient)"/>
    <path d="M45 48 L40 53 L35 48" fill="url(#secondaryGradient)"/>
    <line x1="30" y1="36" x2="45" y2="36" stroke="#fff" stroke-width="1"/>
    <line x1="30" y1="40" x2="50" y2="40" stroke="#fff" stroke-width="1"/>
  </g>
  
  <!-- Support/Headset Icon -->
  <g transform="translate(150, 50)">
    <circle cx="25" cy="25" r="20" fill="url(#secondaryGradient)" opacity="0.2"/>
    <!-- Headset -->
    <path d="M15 20 Q25 10 35 20 L35 30 Q35 35 30 35 L20 35 Q15 35 15 30 Z" fill="url(#primaryGradient)" stroke="#fff" stroke-width="2"/>
    <!-- Microphone -->
    <rect x="32" y="28" width="3" height="8" rx="1.5" fill="#6c757d"/>
    <circle cx="33.5" cy="38" r="2" fill="#6c757d"/>
    <!-- Sound waves -->
    <path d="M5 20 Q10 25 5 30" stroke="url(#primaryGradient)" stroke-width="1.5" fill="none" opacity="0.7"/>
    <path d="M45 20 Q40 25 45 30" stroke="url(#primaryGradient)" stroke-width="1.5" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="80" cy="80" r="3" fill="url(#primaryGradient)" opacity="0.6"/>
  <circle cx="420" cy="100" r="4" fill="url(#secondaryGradient)" opacity="0.6"/>
  <circle cx="450" cy="300" r="3" fill="url(#primaryGradient)" opacity="0.6"/>
  <circle cx="50" cy="350" r="2" fill="url(#secondaryGradient)" opacity="0.6"/>
  
  <!-- Connection Lines -->
  <path d="M120 190 Q180 160 240 180" stroke="url(#primaryGradient)" stroke-width="2" fill="none" opacity="0.3" stroke-dasharray="5,5"/>
  <path d="M280 180 Q320 200 370 180" stroke="url(#secondaryGradient)" stroke-width="2" fill="none" opacity="0.3" stroke-dasharray="5,5"/>
  
  <!-- Arabic Text Elements (Decorative) -->
  <g transform="translate(50, 320)" opacity="0.1">
    <text x="0" y="0" font-family="Arial" font-size="12" fill="url(#primaryGradient)">اتصل بنا</text>
  </g>
  
  <g transform="translate(380, 50)" opacity="0.1">
    <text x="0" y="0" font-family="Arial" font-size="10" fill="url(#secondaryGradient)">تواصل</text>
  </g>
</svg>
