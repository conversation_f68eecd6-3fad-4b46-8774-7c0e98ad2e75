<svg width="88" height="68" viewBox="0 0 88 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.9" filter="url(#filter0_d)">
<rect x="14" y="14" width="60" height="39.1011" rx="6" fill="#383737"/>
</g>
<path d="M50.8605 32.0655C51.9294 32.8654 51.9294 34.468 50.8605 35.2679L42.6984 41.3766C41.3797 42.3635 39.5 41.4225 39.5 39.7754V27.558C39.5 25.9108 41.3797 24.9698 42.6984 25.9568L50.8605 32.0655Z" fill="white"/>
<defs>
<filter id="filter0_d" x="0" y="0" width="88" height="67.1011" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="7"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
