body {
  margin: 0;
  padding: 0;
}

.splash-screen {
  position: absolute;
  z-index: 13f71b7;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-family: Helvetica, "sans-serif";
  background-color: #f2f3f8;
  color: #5e6278;
  line-height: 1;
  font-size: 14px;
  font-weight: 400;
}

.splash-screen span {
  color: #5e6278;
  transition: none !important;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
}

.splash-screen img {
  width: 100px;
  margin-left: calc(100vw - 100%);
  margin-bottom: 30px;
}

[data-theme="dark"] .splash-screen {
  background-color: #151521;
  color: #92929f;
}

[data-theme="dark"] .splash-screen span {
  color: #92929f;
}

#root {
  opacity: 1;
  transition: opacity 1s ease-in-out;
}
/* HTML: <div class="loader"></div> */
.loader {
  --w: 10ch;
  font-weight: bold;
  font-family: monospace;
  font-size: 30px;
  line-height: 1.4em;
  letter-spacing: var(--w);
  width: var(--w);
  overflow: hidden;
  white-space: nowrap;
  color: #3f71b7;
  text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
    calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
    calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
    calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
    calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  animation: l20 2s infinite linear;
}
.loader:before {
  content: "Loading...";
}

@keyframes l20 {
  9.09% {
    text-shadow: calc(0 * var(--w)) -10px #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  18.18% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) -10px #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  27.27% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) -10px #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  36.36% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) -10px #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  45.45% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) -10px #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  54.54% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) -10px #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  63.63% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) -10px #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  72.72% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) -10px #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  81.81% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) -10px #3f71b7, calc(-9 * var(--w)) 0 #3f71b7;
  }
  90.90% {
    text-shadow: calc(0 * var(--w)) 0 #3f71b7, calc(-1 * var(--w)) 0 #3f71b7,
      calc(-2 * var(--w)) 0 #3f71b7, calc(-3 * var(--w)) 0 #3f71b7,
      calc(-4 * var(--w)) 0 #3f71b7, calc(-5 * var(--w)) 0 #3f71b7,
      calc(-6 * var(--w)) 0 #3f71b7, calc(-7 * var(--w)) 0 #3f71b7,
      calc(-8 * var(--w)) 0 #3f71b7, calc(-9 * var(--w)) -10px #3f71b7;
  }
}
