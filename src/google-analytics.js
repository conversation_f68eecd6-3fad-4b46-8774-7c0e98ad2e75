// Google Analytics 4 Configuration
// Replace 'GA_MEASUREMENT_ID' with your actual Google Analytics 4 Measurement ID

(function() {
  // Check if Google Analytics should be loaded
  if (typeof window !== 'undefined' && !window.location.hostname.includes('localhost')) {
    
    // Google Analytics 4 Global Site Tag (gtag.js)
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID';
    document.head.appendChild(script1);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID', {
      // Enhanced measurement settings
      send_page_view: true,
      allow_google_signals: true,
      allow_ad_personalization_signals: true,
      
      // Custom parameters for Arabic content
      custom_map: {
        'custom_parameter_1': 'page_language',
        'custom_parameter_2': 'user_type'
      },
      
      // Set default language
      page_language: 'ar',
      
      // Cookie settings
      cookie_flags: 'SameSite=None;Secure',
      
      // Debug mode (set to false in production)
      debug_mode: false
    });

    // Track page views for SPA (Single Page Application)
    function trackPageView(url, title) {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: url,
        page_title: title
      });
    }

    // Track custom events
    function trackEvent(action, category, label, value) {
      gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value
      });
    }

    // Track user engagement
    function trackEngagement(engagement_time_msec) {
      gtag('event', 'user_engagement', {
        engagement_time_msec: engagement_time_msec
      });
    }

    // Track conversions
    function trackConversion(conversion_name, value, currency) {
      gtag('event', 'conversion', {
        send_to: 'GA_MEASUREMENT_ID/' + conversion_name,
        value: value,
        currency: currency || 'EGP'
      });
    }

    // Track form submissions
    function trackFormSubmission(form_name) {
      gtag('event', 'form_submit', {
        event_category: 'engagement',
        event_label: form_name
      });
    }

    // Track downloads
    function trackDownload(file_name) {
      gtag('event', 'file_download', {
        event_category: 'engagement',
        event_label: file_name
      });
    }

    // Track outbound links
    function trackOutboundLink(url) {
      gtag('event', 'click', {
        event_category: 'outbound',
        event_label: url,
        transport_type: 'beacon'
      });
    }

    // Make functions available globally
    window.gtag = gtag;
    window.trackPageView = trackPageView;
    window.trackEvent = trackEvent;
    window.trackEngagement = trackEngagement;
    window.trackConversion = trackConversion;
    window.trackFormSubmission = trackFormSubmission;
    window.trackDownload = trackDownload;
    window.trackOutboundLink = trackOutboundLink;

    // Auto-track some common events
    document.addEventListener('DOMContentLoaded', function() {
      // Track form submissions
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        form.addEventListener('submit', function() {
          const formName = this.getAttribute('name') || this.getAttribute('id') || 'unnamed_form';
          trackFormSubmission(formName);
        });
      });

      // Track outbound links
      const links = document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])');
      links.forEach(link => {
        link.addEventListener('click', function() {
          trackOutboundLink(this.href);
        });
      });

      // Track file downloads
      const downloadLinks = document.querySelectorAll('a[href$=".pdf"], a[href$=".doc"], a[href$=".docx"], a[href$=".xls"], a[href$=".xlsx"], a[href$=".zip"]');
      downloadLinks.forEach(link => {
        link.addEventListener('click', function() {
          const fileName = this.href.split('/').pop();
          trackDownload(fileName);
        });
      });
    });

    console.log('Google Analytics 4 initialized for Falcon ERP');
  }
})();
