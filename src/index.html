<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Falcon ERP</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="shortcut icon" href="./assets/media/logos/favicon.ico" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Cairo:300,400,500,600,700"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
    />
    <!-- تم استبدال أيقونات Ki-Outline بأيقونات Font Awesome -->
    <!-- SPLASH SCREEN-->
    <link
      rel="stylesheet"
      id="layout-styles-anchor"
      href="./assets/splash-screen.css"
    />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <script src="assets/env.js"></script>
    <script type="application/javascript">
      function getIP(json) {
        window.userIp = json.ip;
      }
    </script>

    <script
      type="application/javascript"
      src="https://ipinfo.io/?format=jsonp&callback=getIP"
    ></script>
    <style>
      body {
        font-family: "Cairo";
      }

      /* استهداف العنصر div بالضبط كما هو في الكود المشارك */
      div[style*="background-color: rgb(255, 114, 0)"][style*="z-index: 1500"][style*="text-align: center"],
      dx-license[style*="background-color: rgb(255, 114, 0)"],
      dx-license[version],
      dx-license {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        overflow: hidden !important;
        position: absolute !important;
        z-index: -9999 !important;
        pointer-events: none !important;
        clip: rect(0, 0, 0, 0) !important;
        margin: -1px !important;
        padding: 0 !important;
        border: 0 !important;
      }

      /* قواعد إضافية لاستهداف رسائل الترخيص */
      div[style*="rgb(255, 114, 0)"] {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        width: 0 !important;
        overflow: hidden !important;
        position: absolute !important;
        z-index: -9999 !important;
      }

      /* قواعد عامة لإخفاء رسائل الترخيص */
      .dx-popup-wrapper .dx-overlay-content .dx-popup-content div:contains("For evaluation purposes only"),
      .dx-popup-wrapper .dx-overlay-content .dx-popup-content div:contains("DevExpress product libraries"),
      .dx-popup-wrapper .dx-overlay-content .dx-popup-content div:contains("Please register an existing license"),
      .dx-popup-wrapper .dx-overlay-content .dx-popup-content div:contains("purchase a new license") {
        display: none !important;
      }

      /* إخفاء النافذة المنبثقة بالكامل */
      .dx-popup-wrapper[role="dialog"] {
        display: none !important;
      }
    </style>

    <!-- سكريبت محدث لإخفاء رسائل ترخيص DevExpress -->
    <script>
      // تنفيذ الكود بعد تحميل الصفحة
      document.addEventListener('DOMContentLoaded', function() {
        // إنشاء دالة لإخفاء رسائل الترخيص
        function hideLicenseMessages() {
          // استهداف عنصر dx-license
          var dxLicenseElements = document.querySelectorAll('dx-license');
          dxLicenseElements.forEach(function(el) {
            // إزالة العنصر تماماً من DOM
            if (el.parentNode) {
              el.parentNode.removeChild(el);
            }

            // في حالة فشل الإزالة، نقوم بإخفائه تماماً
            el.style.display = 'none';
            el.style.visibility = 'hidden';
            el.style.opacity = '0';
            el.style.height = '0';
            el.style.width = '0';
            el.style.overflow = 'hidden';
            el.style.position = 'absolute';
            el.style.zIndex = '-9999';
            el.style.pointerEvents = 'none';

            // تفريغ محتوى العنصر
            el.innerHTML = '';
          });

          // استهداف العنصر div بالضبط كما هو في الكود المشارك
          var orangeMessages = document.querySelectorAll('div[style*="background-color: rgb(255, 114, 0)"][style*="z-index: 1500"][style*="text-align: center"]');

          if (orangeMessages.length === 0) {
            // استهداف بديل إذا لم يتم العثور على العنصر بالمنتقي الدقيق
            orangeMessages = document.querySelectorAll('div[style*="background-color: rgb(255, 114, 0)"]');
          }

          orangeMessages.forEach(function(msg) {
            // إزالة العنصر تماماً من DOM
            if (msg.parentNode) {
              msg.parentNode.removeChild(msg);
            }

            // في حالة فشل الإزالة، نقوم بإخفائه تماماً
            msg.style.display = 'none';
            msg.style.visibility = 'hidden';
            msg.style.opacity = '0';
            msg.style.height = '0';
            msg.style.width = '0';
            msg.style.overflow = 'hidden';
            msg.style.position = 'absolute';
            msg.style.zIndex = '-9999';
            msg.style.pointerEvents = 'none';
            msg.style.clip = 'rect(0, 0, 0, 0)';
            msg.style.margin = '-1px';
            msg.style.padding = '0';
            msg.style.border = '0';

            // تفريغ محتوى العنصر
            msg.innerHTML = '';
          });

          // استهداف أي عنصر يحتوي على نص رسالة الترخيص
          var allElements = document.querySelectorAll('div, span');
          allElements.forEach(function(el) {
            if (el.textContent && (
                el.textContent.includes('For evaluation purposes only') ||
                el.textContent.includes('DevExpress product libraries') ||
                el.textContent.includes('Please register') ||
                el.textContent.includes('purchase a new license')
            )) {
              // إزالة العنصر تماماً من DOM
              if (el.parentNode) {
                el.parentNode.removeChild(el);
              }

              // في حالة فشل الإزالة، نقوم بإخفائه تماماً
              el.style.display = 'none';
              el.style.visibility = 'hidden';
              el.style.opacity = '0';
              el.style.height = '0';
              el.style.width = '0';
              el.style.overflow = 'hidden';
              el.style.position = 'absolute';
              el.style.zIndex = '-9999';
              el.style.pointerEvents = 'none';

              // تفريغ محتوى العنصر
              el.innerHTML = '';
            }
          });

          // البحث عن جميع النوافذ المنبثقة
          var popups = document.querySelectorAll('.dx-popup-wrapper');
          popups.forEach(function(popup) {
            // التحقق من محتوى النافذة المنبثقة
            var content = popup.textContent || '';
            if (content.includes('For evaluation purposes only') ||
                content.includes('DevExpress product libraries') ||
                content.includes('Please register') ||
                content.includes('purchase a new license')) {
              // إزالة النافذة المنبثقة تماماً من DOM
              if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
              }

              // في حالة فشل الإزالة، نقوم بإخفائها تماماً
              popup.style.display = 'none';
              popup.style.visibility = 'hidden';
              popup.style.opacity = '0';
              popup.style.pointerEvents = 'none';

              // تفريغ محتوى النافذة المنبثقة
              popup.innerHTML = '';
            }
          });
        }

        // إنشاء مراقب للتغييرات في DOM
        var observer = new MutationObserver(function(mutations) {
          hideLicenseMessages();
        });

        // بدء مراقبة التغييرات في DOM
        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true
        });

        // تنفيذ الدالة مرة واحدة عند بدء التشغيل
        hideLicenseMessages();

        // تنفيذ الدالة بشكل دوري كل 50 مللي ثانية
        setInterval(hideLicenseMessages, 50);

        // تنفيذ الدالة عند تحميل النافذة
        window.addEventListener('load', hideLicenseMessages);

        // تنفيذ الدالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', hideLicenseMessages);
      });

      // تعديل الدوال الأصلية في DevExpress
      (function() {
        // تنفيذ بعد تحميل النافذة
        window.addEventListener('load', function() {
          // إضافة CSS ديناميكي لإخفاء رسائل الترخيص
          var style = document.createElement('style');
          style.type = 'text/css';
          style.innerHTML = `
            div[style*="background-color: rgb(255, 114, 0)"][style*="z-index: 1500"][style*="text-align: center"],
            dx-license[style*="background-color: rgb(255, 114, 0)"],
            dx-license[version],
            dx-license {
              display: none !important;
              visibility: hidden !important;
              opacity: 0 !important;
              height: 0 !important;
              width: 0 !important;
              overflow: hidden !important;
              position: absolute !important;
              z-index: -9999 !important;
              pointer-events: none !important;
              clip: rect(0, 0, 0, 0) !important;
              margin: -1px !important;
              padding: 0 !important;
              border: 0 !important;
            }
          `;
          document.head.appendChild(style);

          // تنفيذ دالة إخفاء رسائل الترخيص
          var hideLicenseMessages = function() {
            // استهداف عنصر dx-license
            var dxLicenseElements = document.querySelectorAll('dx-license');
            dxLicenseElements.forEach(function(el) {
              if (el.parentNode) {
                el.parentNode.removeChild(el);
              }
            });

            // استهداف العناصر div ذات الخلفية البرتقالية
            var orangeMessages = document.querySelectorAll('div[style*="background-color: rgb(255, 114, 0)"][style*="z-index: 1500"][style*="text-align: center"]');
            orangeMessages.forEach(function(msg) {
              if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
              }
            });
          };

          // تنفيذ الدالة مرة واحدة عند تحميل النافذة
          hideLicenseMessages();

          // تنفيذ الدالة بشكل دوري
          setInterval(hideLicenseMessages, 50);

          // محاولة تعطيل وظيفة إنشاء عنصر dx-license
          if (window.customElements && window.customElements.define) {
            try {
              // حفظ الدالة الأصلية
              var originalDefine = window.customElements.define;

              // استبدال الدالة بنسخة معدلة
              window.customElements.define = function(name, constructor, options) {
                // منع تسجيل عنصر dx-license
                if (name.toLowerCase() === 'dx-license') {
                  return;
                }
                // استدعاء الدالة الأصلية لجميع العناصر الأخرى
                return originalDefine.call(this, name, constructor, options);
              };
            } catch (e) {
              console.error('فشل في تعطيل تسجيل عنصر dx-license:', e);
            }
          }
        });
      })();
    </script>

    <!-- إضافة مكتبة ApexCharts لدعم الرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
  </head>

  <body root id="kt_body" class="mat-typography">
    <!-- direction="rtl" dir="rtl" style="direction: rtl"  -->
    <!-- <body root id="kt_body" class="mat-typography" direction="rtl" dir="rtl" style="direction: rtl"></body> -->
    <!--begin::Theme mode setup on page load-->
    <script>
      if (document.documentElement) {
        var defaultThemeMode = "system";

        var hasKTName = document.body.hasAttribute("data-kt-name");
        var lsKey = "kt_" + (hasKTName ? name + "_" : "") + "theme_mode_value";
        var themeMode = localStorage.getItem(lsKey);
        if (!themeMode) {
          if (defaultThemeMode === "system") {
            themeMode = window.matchMedia("(prefers-color-scheme: dark)")
              .matches
              ? "dark"
              : "light";
          } else {
            themeMode = defaultThemeMode;
          }
        }

        document.documentElement.setAttribute("data-theme", themeMode);
      }
    </script>
    <!--end::Theme mode setup on page load-->
    <div id="splash-screen" class="splash-screen">
      <img
        src="./assets/media/logos/falcon-logo.png"
        alt="Falcon Logo"
      />
      <div class="loader"></div>
    </div>
  </body>
</html>
