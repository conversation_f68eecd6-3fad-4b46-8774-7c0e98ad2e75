import { CommonModule } from '@angular/common';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, ViewChild } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { StaffAssistantService } from '../StaffAssistant.service';


@Component({
    selector: 'app-staffattendance',
    //standalone: true,
    //imports: [
    //    CommonModule,
    //],
    templateUrl: './Staffattendance.component.html',
    styleUrl: './Staffattendance.component.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})


export class StaffattendanceComponent implements OnDestroy {
  viewListForm: FormGroup;
  data: ArrayStore;
  editorOptions: any;
  AnalyticAccounts: any[];
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ["print", "Print Custome"];
  actionsList: string[] = ["Export"]
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  searchCtrl: FormControl;
  projectlatitude: any;
  projectlongitude: any;
  latitude: any;
  longitude: any;
  clientDeviceInfo: any;
  accuracy: number =1000;
  difference: number =1000;
  loadingVisible = false;
  CostID: any;
  constructor(private fb: FormBuilder,private service: StaffAssistantService, private cdk: ChangeDetectorRef, private route: ActivatedRoute,) {
    service.baseUrl = `${environment.appUrls.StaffAssistant}Staffattendance`;

    this.viewListForm = this.fb.group({
      CostID: [null, Validators.required],
    });
    this.subscription.add(service.list().subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.AnalyticAccounts = r.info;
        this.cdk.detectChanges();
        this.CostID = (this.AnalyticAccounts?.[0]?.code ?? null)
       
      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['ID', 'Name'];

  }

  ngOnInit(): void {
    this.getDeviceId();
    // this.getLocation();
      this.viewListForm = new FormGroup({
      fromDate: new FormControl(formatDate(new Date(new Date()), "yyyy-MM-dd", "en")),
      toDate: new FormControl(formatDate(new Date(new Date()), "yyyy-MM-dd", "en")),
      clientDeviceInfo:new FormControl("***********"),
      CostID: new FormControl(this.CostID)
    });

    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.service.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.service.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.service.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.service.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }



  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();

  }

  @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;


  ngOnDestroy(): void {
    this.subscription.unsubscribe();

  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'falconData.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }



  loadData(r: any) {
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
   /* this.getLocation();*/
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }

  degreesToRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  getLocation(): void {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.latitude = position.coords.latitude;
          this.longitude = position.coords.longitude;
          this.accuracy = position.coords.accuracy;
          this.calculateDiff();
        },
        (error) => {
          console.error("Error getting location: ", error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0, } // تفعيل الدقة العالية
      );
    } else {
      console.log("Geolocation is not supported by this browser.");
    }
  }

  onAccountSelect(selectedItem: any) {
    if(!selectedItem) {
      this.difference = 1000;
      this.projectlatitude = undefined;
      this.projectlongitude = undefined;
      this.cdk.detectChanges();
      return;
    }
    this.CostID= selectedItem.code;
    
  }

  clearData() {
    this.viewListForm.get("CostID")?.reset();
    this.difference = 1000;
    this.projectlatitude = undefined;
    this.projectlongitude = undefined;
    this.cdk.detectChanges();
  }


  checkin() {
    if (this.difference > 300) {
      alert('انت خارج نطاق العمل ');

    }
    let form = this.viewListForm.value
    this.subscription.add(this.service.checkin({...form, clientDeviceInfo: this.clientDeviceInfo}).subscribe(r => {
      if (r.success) {
        this.clearData();

      //  this.data = r.data;
      //  this.cdk.detectChanges();
      }
    }));

  }

  checkOut() {
    if (this.difference > 300) {
      alert('انت خارج نطاق العمل ');

    }
    let form = this.viewListForm.value
    this.subscription.add(this.service.checkOut({...form, clientDeviceInfo: this.clientDeviceInfo}).subscribe(r => {
      if (r.success) {
        this.clearData();
        //  this.data = r.data;
      //  this.cdk.detectChanges();
      }
    }));

  }

  breakout() {
    if (this.difference > 300) {
      alert('انت خارج نطاق العمل ');

    }
    let form = this.viewListForm.value
    this.subscription.add(this.service.breakout({...form}).subscribe(r => {
      if (r.success) {
        this.clearData();
        //  this.data = r.data;
      //  this.cdk.detectChanges();
      }
    }));

  }

  breakin() {
    if (this.difference > 300) {
      alert('انت خارج نطاق العمل ');

    }
    let form = this.viewListForm.value
    this.subscription.add(this.service.breakin({...form}).subscribe(r => {
      if (r.success) {
        this.clearData();
        //  this.data = r.data;
        //  this.cdk.detectChanges();
      }
    }));

  }


  filter() {
    let form = this.viewListForm.value
    this.subscription.add(this.service.filter(form).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }

  calculateDiff() {
    if(!this.CostID) {
      this.difference = 1000;
      this.cdk.detectChanges();
      return;
    }
    this.loadingVisible = true;
    this.cdk.detectChanges();
    this.subscription.add(this.service.getProjectLocation(this.CostID).subscribe(r => {
      if (r.success) {

        this.projectlatitude = r.data.latitude;
        this.projectlongitude = r.data.longitude;
        const earthRadius = 6371;

        const latDiff = this.degreesToRadians(this.latitude - this.projectlatitude);
        const lonDiff = this.degreesToRadians(this.longitude - this.projectlongitude);
        const a =
          Math.sin(latDiff / 2) * Math.sin(latDiff / 2) +
          Math.cos(this.degreesToRadians(this.projectlatitude)) *
          Math.cos(this.degreesToRadians(this.projectlongitude)) *
          Math.sin(lonDiff / 2) *
          Math.sin(lonDiff / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        const distance = earthRadius * c; // Distance in kilometers
        this.difference =  parseFloat((distance * 1000).toFixed(2)); // Convert to meters

      }
      this.loadingVisible = false;
      this.cdk.detectChanges();
    }));
  }

  private getDeviceId() {
    const userAgent = navigator.userAgent;
      this.clientDeviceInfo = (window as any).userIp + "_"
        + this.getDeviceType(userAgent)  + "_"
        + this.getOperatingSystem(userAgent) + "_"
        + this.getOSVersion(userAgent);
      console.log(this.clientDeviceInfo)
  }

  getOperatingSystem(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('X11')) return 'UNIX';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (/iPhone|iPad|iPod/.test(userAgent)) return 'iOS';
    return 'Unknown OS';
  }

  getOSVersion(userAgent: string): string {
    const match = userAgent.match(/(Windows NT|Mac OS X|Android|CPU (?:iPhone )?OS) ([\d_\.]+)/);
    return match ? match[2].replace(/_/g, '.') : 'Unknown Version';
  }

  getDeviceType(userAgent: string): string {
    if (/Mobile|Android|iP(hone|od|ad)/.test(userAgent)) return 'Mobile';
    if (/Tablet/.test(userAgent)) return 'Tablet';
    return 'Desktop';
  }
}
