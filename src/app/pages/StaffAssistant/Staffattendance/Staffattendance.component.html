<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="card-header border-0 cursor-pointer w-100 mb-4">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">Attendance</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              (click)="filter()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.View" | translate }}
              <i class="fa fa-filter"></i>
            </button>

            <button
              (click)="getLocation()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.CurrentLocation" | translate }}
              <i class="fa fa-map-marker"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="main-inputs">
      <div class="row mb-2">
        <div class="form-group col-xl-3 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-3 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
      </div>
      <div class="row mb-2">
        <div class="form-group col-xl-6 col-md-4 col-sm-12">
          <label for="analyticaccounts" class="form-label">
            {{ "COMMON.CostName" | translate }}
          </label>
          <ng-select
            id="CostID"
            formControlName="CostID"
            bindLabel="nameAr"
            value="CostID"
            bindValue="code"
            [items]="AnalyticAccounts"
            [searchable]="true"
            (change)="onAccountSelect($event)"
          >
          </ng-select>
        </div>
      </div>

      <dx-load-panel
        #loadPanel
        shadingColor="rgba(0,0,0,0.4)"
        [position]="{ of: '#employee' }"
        [(visible)]="loadingVisible"
        [showIndicator]="true"
        [showPane]="true"
        [shading]="true"
        message="{{ 'COMMON.LOADING' | translate }}"
        [hideOnOutsideClick]="false"
      >
      </dx-load-panel>
      <div class="row mb-2">
        <div class="form-group col-xl-8 col-md-4 col-sm-12">
          <label for="ProjectLocation" class="form-label fw-bolder">
            {{ "COMMON.ProjectLocation" | translate }}
          </label>

          <label for="projectlatitude" class="form-label">
            {{ projectlatitude }}
          </label>

          <label for="projectlongitude" class="form-label">
            {{ projectlongitude }}
          </label>
        </div>
      </div>

      <div class="row mb-2">
        <div class="form-group col-xl-8 col-md-4 col-sm-12">
          <label for="CurrentLocation" class="form-label fw-bolder">
            {{ "COMMON.CurrentLocation" | translate }}
          </label>

          <label for="latitude" class="form-label">
            {{ latitude }}
          </label>

          <label for="longitude" class="form-label">
            {{ longitude }}
          </label>
        </div>
      </div>

      <div class="row mb-2">
        <div class="form-group col-xl-8 col-md-4 col-sm-12">
          <label for="CurrentLocation" class="form-label fw-bolder">
            {{ "COMMON.Accuracy" | translate }}
          </label>

          <label for="Accuracy" class="form-label">
            {{ accuracy }}
          </label>
        </div>
      </div>
      <div class="row mb-2">
        <div class="form-group col-xl-8 col-md-4 col-sm-12">
          <label for="CurrentLocation" class="form-label fw-bolder">
            {{ "COMMON.difference" | translate }}
          </label>

          <label for="Accuracy" class="form-label"> {{ difference }}M </label>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-2">
    <div class="form-group col-xl-8 col-md-4 col-sm-12">
      <a (click)="checkin()" class="btn btn-success m-1 btn-sm">
        {{ "COMMON.Checkin" | translate }}
        <i class="fa fa-sign-in"></i
      ></a>
      <a (click)="breakout()" class="btn btn-warning m-1 btn-sm"
        >{{ "COMMON.breakout" | translate }}
        <i class="fa fa-pause"></i>
      </a>
      <a (click)="breakin()" class="btn btn-info m-1 btn-sm"
        >{{ "COMMON.breakin" | translate }} <i class="fa fa-play"></i
      ></a>

      <a (click)="checkOut()" class="btn btn-danger m-1 btn-sm"
        >{{ "COMMON.CheckOut" | translate }}<i class="fa fa-sign-out"></i
      ></a>
    </div>
  </div>
</form>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
  (onSelectionChanged)="selectionChanged($event)"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="date"
    caption="Date"
    dataType="date"
    [format]="{ type: 'date', format: 'yyyy-MM-dd' }"
  >
  </dxi-column>

  <dxi-column dataField="dayName" [caption]="'COMMON.Day' | translate">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column
    dataField="checkIn"
    [caption]="'COMMON.Checkin' | translate"
  ></dxi-column>
  <dxi-column
    dataField="breakOut"
    [caption]="'COMMON.breakout' | translate"
  ></dxi-column>
  <dxi-column
    dataField="breakIn"
    [caption]="'COMMON.breakin' | translate"
  ></dxi-column>
  <dxi-column
    dataField="checkOut"
    [caption]="'COMMON.CheckOut' | translate"
  ></dxi-column>
  <dxi-column
    dataField="analyticCode"
    [caption]="'COMMON.Costid' | translate"
  ></dxi-column>
  <dxi-column
    dataField="overTime"
    [caption]="'COMMON.OverTime' | translate"
  ></dxi-column>
  <dxi-column
    dataField="hours"
    [caption]="'COMMON.Hours' | translate"
  ></dxi-column>
  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="date" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="hours" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
