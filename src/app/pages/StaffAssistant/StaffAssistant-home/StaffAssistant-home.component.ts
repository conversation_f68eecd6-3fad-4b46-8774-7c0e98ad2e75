import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { StaffAssistantService } from '../StaffAssistant.service';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-staff-assistant-home',
    templateUrl: './StaffAssistant-home.component.html',
    styleUrl: './StaffAssistant-home.component.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class StaffAssistantHomeComponent implements OnInit {
  // إحصائيات الحضور
  totalAttendanceDays: number = 0;
  presentDays: number = 0;
  lateDays: number = 0;
  absentDays: number = 0;

  // إحصائيات الطلبات
  totalRequests: number = 0;
  approvedRequests: number = 0;
  pendingRequests: number = 0;
  rejectedRequests: number = 0;

  // إحصائيات الحسابات
  totalBalance: number = 0;
  receivables: number = 0;
  payables: number = 0;

  // بيانات الرسوم البيانية
  attendanceByMonthData: any[] = [];
  requestsByTypeData: any[] = [];

  // الطلبات الأخيرة
  recentRequests: any[] = [];

  // حركات الحضور الأخيرة
  recentAttendance: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'تسجيل الحضور', icon: 'fa-sign-in-alt', route: '/staffassistant/attendance' },
    { title: 'تقديم طلب جديد', icon: 'fa-file-alt', route: '/staffassistant/myrequests' },
    { title: 'عرض الحسابات', icon: 'fa-wallet', route: '/staffassistant/myaccounts' }
  ];

  // الاشتراكات
  subscription = new Subscription();

  constructor(
    private http: HttpClient,
    private translate: TranslateService,
    private service: StaffAssistantService,
    private cdk: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات الحضور الوهمية
      this.totalAttendanceDays = 22;
      this.presentDays = 18;
      this.lateDays = 3;
      this.absentDays = 1;

      // بيانات الطلبات الوهمية
      this.totalRequests = 15;
      this.approvedRequests = 10;
      this.pendingRequests = 3;
      this.rejectedRequests = 2;

      // بيانات الحسابات الوهمية
      this.totalBalance = 5000;
      this.receivables = 3000;
      this.payables = 2000;

      // بيانات الرسوم البيانية الوهمية
      this.attendanceByMonthData = [
        { month: 'يناير', attendance: 21 },
        { month: 'فبراير', attendance: 19 },
        { month: 'مارس', attendance: 22 },
        { month: 'أبريل', attendance: 20 },
        { month: 'مايو', attendance: 21 },
        { month: 'يونيو', attendance: 18 }
      ];

      this.requestsByTypeData = [
        { type: 'إجازة', count: 8 },
        { type: 'إذن', count: 4 },
        { type: 'مهمة', count: 2 },
        { type: 'أخرى', count: 1 }
      ];

      // بيانات الطلبات الأخيرة الوهمية
      this.recentRequests = [
        { id: 'REQ-2023-001', type: 'إجازة', startDate: '2023-06-15', endDate: '2023-06-17', status: 'موافق عليه' },
        { id: 'REQ-2023-002', type: 'إذن', startDate: '2023-06-10', endDate: '2023-06-10', status: 'موافق عليه' },
        { id: 'REQ-2023-003', type: 'مهمة', startDate: '2023-06-05', endDate: '2023-06-06', status: 'موافق عليه' },
        { id: 'REQ-2023-004', type: 'إجازة', startDate: '2023-07-01', endDate: '2023-07-05', status: 'قيد الانتظار' },
        { id: 'REQ-2023-005', type: 'إذن', startDate: '2023-05-25', endDate: '2023-05-25', status: 'مرفوض' }
      ];

      // بيانات حركات الحضور الأخيرة الوهمية
      this.recentAttendance = [
        { date: '2023-06-15', checkIn: '08:05:00', checkOut: '17:00:00', status: 'حاضر' },
        { date: '2023-06-14', checkIn: '08:00:00', checkOut: '17:00:00', status: 'حاضر' },
        { date: '2023-06-13', checkIn: '08:30:00', checkOut: '17:00:00', status: 'متأخر' },
        { date: '2023-06-12', checkIn: '08:00:00', checkOut: '17:00:00', status: 'حاضر' },
        { date: '2023-06-11', checkIn: '', checkOut: '', status: 'غائب' }
      ];

      this.isLoading = false;
      this.cdk.detectChanges();
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    this.service.baseUrl = `${environment.appUrls.StaffAssistant}`;

    forkJoin({
      attendanceStats: this.http.get<any>('api/staffassistant/dashboard/attendance'),
      requestsStats: this.http.get<any>('api/staffassistant/dashboard/requests'),
      accountsStats: this.http.get<any>('api/staffassistant/dashboard/accounts'),
      recentRequests: this.http.get<any>('api/staffassistant/dashboard/recent-requests'),
      recentAttendance: this.http.get<any>('api/staffassistant/dashboard/recent-attendance')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        this.cdk.detectChanges();
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalAttendanceDays = results.attendanceStats.total;
        this.presentDays = results.attendanceStats.present;
        this.lateDays = results.attendanceStats.late;
        this.absentDays = results.attendanceStats.absent;

        this.totalRequests = results.requestsStats.total;
        this.approvedRequests = results.requestsStats.approved;
        this.pendingRequests = results.requestsStats.pending;
        this.rejectedRequests = results.requestsStats.rejected;

        this.totalBalance = results.accountsStats.total;
        this.receivables = results.accountsStats.receivables;
        this.payables = results.accountsStats.payables;

        this.recentRequests = results.recentRequests;
        this.recentAttendance = results.recentAttendance;

        // بيانات الرسوم البيانية
        this.attendanceByMonthData = results.attendanceStats.byMonth;
        this.requestsByTypeData = results.requestsStats.byType;
      }
      this.isLoading = false;
      this.cdk.detectChanges();
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة الطلب
  getRequestStatusClass(status: string): string {
    switch (status) {
      case 'موافق عليه':
        return 'badge-light-success';
      case 'قيد الانتظار':
        return 'badge-light-warning';
      case 'مرفوض':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون حالة الحضور
  getAttendanceStatusClass(status: string): string {
    switch (status) {
      case 'حاضر':
        return 'badge-light-success';
      case 'متأخر':
        return 'badge-light-warning';
      case 'غائب':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
