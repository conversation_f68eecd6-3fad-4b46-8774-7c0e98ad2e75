
<!-- بداية لوحة تحكم مساعد الموظفين -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.StaffAssistantDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.StaffAssistantHome' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.StaffAssistantModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف إحصائيات الحضور -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي أيام الحضور -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalAttendanceDays) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantTotalAttendanceDays' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-calendar-alt text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أيام الحضور -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(presentDays) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantPresentDays' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-check-circle text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أيام التأخير -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(lateDays) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantLateDays' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-clock text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أيام الغياب -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(absentDays) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantAbsentDays' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-danger">
                  <i class="fa fa-times-circle text-danger fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات الحضور -->

      <!-- بداية صف إحصائيات الطلبات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي الطلبات -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalRequests) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantTotalRequests' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-file-alt text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الطلبات الموافق عليها -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(approvedRequests) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantApprovedRequests' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-check text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الطلبات قيد الانتظار -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(pendingRequests) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantPendingRequests' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-hourglass-half text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الطلبات المرفوضة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(rejectedRequests) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantRejectedRequests' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-danger">
                  <i class="fa fa-times text-danger fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات الطلبات -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني للحضور -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.StaffAssistantAttendanceByMonth' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.StaffAssistantViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.StaffAssistantQuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantCommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف الطلبات الأخيرة وتوزيع الطلبات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الطلبات الأخيرة -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.StaffAssistantRecentRequests' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantLastFiveRequests' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.StaffAssistantViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantRequestID' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.StaffAssistantRequestType' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantStartDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantEndDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let request of recentRequests">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ request.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ request.type }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.startDate }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.endDate }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getRequestStatusClass(request.status)">{{ request.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- توزيع الطلبات حسب النوع -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.StaffAssistantRequestsByType' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantCurrentDistribution' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.StaffAssistantViewReport' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget2></app-charts-widget2>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الطلبات الأخيرة وتوزيع الطلبات -->

      <!-- بداية صف حركات الحضور الأخيرة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.StaffAssistantRecentAttendance' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.StaffAssistantLastFiveAttendance' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.StaffAssistantViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.StaffAssistantDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantCheckIn' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantCheckOut' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.StaffAssistantStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let attendance of recentAttendance">
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ attendance.date }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ attendance.checkIn }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ attendance.checkOut }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getAttendanceStatusClass(attendance.status)">{{ attendance.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف حركات الحضور الأخيرة -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم مساعد الموظفين -->
