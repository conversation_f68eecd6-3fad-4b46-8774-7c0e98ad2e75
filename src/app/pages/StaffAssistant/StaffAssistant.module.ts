import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { StaffAssistantHomeComponent } from './StaffAssistant-home/StaffAssistant-home.component';
import { StaffAssistantRoutingModule } from './StaffAssistant-routing.module';
import { CommonModule } from '@angular/common';
import { MyrequestsComponent } from './myrequests/myrequests.component';
import { MyaccountsComponent } from './myaccounts/myaccounts.component';
import { StaffattendanceComponent } from './Staffattendance/Staffattendance.component';
import { MyrequestsViewComponent } from './myrequests/myrequests-view/myrequests-view.component';

@NgModule({
  declarations: [
    StaffAssistantHomeComponent,
    MyrequestsComponent,
    StaffattendanceComponent,
    MyaccountsComponent,
    MyrequestsViewComponent,
  ],
  imports: [StaffAssistantRoutingModule, SharedModule, CommonModule],
  exports: [RouterModule],
})
export class StaffAssistantModule {}
