import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { StaffAssistantHomeComponent } from './StaffAssistant-home/StaffAssistant-home.component';
import { StaffattendanceComponent } from './Staffattendance/Staffattendance.component';
import { MyrequestsComponent } from './myrequests/myrequests.component';
import { MyaccountsComponent } from './myaccounts/myaccounts.component';
import { MyrequestsViewComponent } from './myrequests/myrequests-view/myrequests-view.component';

const routes: Routes = [
  {
    path: '',
    component: StaffAssistantHomeComponent,
  },
  {
    path: 'attendance',
    component: StaffattendanceComponent,
  },
  {
    path: 'myrequests_view',
    component: MyrequestsViewComponent,
  },
  {
    path: 'myrequests_view/:id',
    component: MyrequestsViewComponent,
  },
  {
    path: 'myrequests',
    component: MyrequestsComponent,
  },
  {
    path: 'myaccounts',
    component: MyaccountsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class StaffAssistantRoutingModule {}
