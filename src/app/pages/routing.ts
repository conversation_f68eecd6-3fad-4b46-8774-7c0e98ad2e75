import {Routes} from '@angular/router';
import {AuthGuard} from '../modules/auth/auth.guard';
import {AccountingModule} from "./accounting/accounting.module";
import {MaintenanceModule} from "./maintenance/maintenance.module";
import {ManufacturingModule} from "./manufacturing/manufacturing.module";
import {ProjectModule} from "./project/project.module";
import {DashboardModule} from "./dashboard/dashboard.module";
import {PermissionModule} from "./Permissions/permission.module";
import {InventoryModule} from "./inventory/inventory.module";
import {GeneralModule} from "./general/general.module";
import {SettingsModule} from "./settings/settings.module";
import {HRModule} from "./hr/hr.module";
import {PurchaseModule} from "./purchase/purchase.module";
import {CRMModule} from "./crm/crm.module";
import {RealestateModule} from "./realestate/realestate.module";
import {FleetModule} from "./fleet/fleet.module";
import {POSModule} from "./pos/pos.module";
import {SalesModule} from "./sales/sales.module";
import { HotelModule } from './hotel/hotel.module';
import { StaffAssistantModule } from './StaffAssistant/StaffAssistant.module';

const Routing: Routes = [
    {
        path: 'dashboard',
        canMatch: [AuthGuard],
        data: {tagId: 0, area: '/admin', roles: ['admin', 'accountants']},
        loadChildren: () => DashboardModule
    },
    {
        path: 'inventory',
        canMatch: [AuthGuard],
        loadChildren: () => InventoryModule, data: {tagId: 101},
    },
    {
        path: 'permissions',
        canMatch: [AuthGuard],
        loadChildren: () => PermissionModule, data: {tagId: 102, area: '/admin', roles: ['admin']},
    },
    {
        path: 'general',
        canMatch: [AuthGuard],
        loadChildren: () => GeneralModule, data: {tagId: 102},
    },
    {
        path: 'settings',
        canMatch: [AuthGuard],
        loadChildren: () => SettingsModule, data: {tagId: 0},
    },
    {
        path: 'hr',
        canMatch: [AuthGuard],
        loadChildren: () => HRModule, data: {tagId: 103},
    },
    {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
    },

    {
        path: 'purchases',
        canMatch: [AuthGuard],
        loadChildren: () => PurchaseModule, data: {tagId: 104},
    },
    {
        path: 'crm',
        canMatch: [AuthGuard],
        loadChildren: () => CRMModule,
        data: {tagId: 105},
    },
    {
        path: 'accounting',
        canMatch: [AuthGuard],
        loadChildren: () => AccountingModule,
        data: {tagId: 106},
    },
    {
        path: 'maintenance',
        canMatch: [AuthGuard],
        loadChildren: () => MaintenanceModule,
        data: {tagId: 107},
    },
    {
        path: 'manufacturing',
        canMatch: [AuthGuard],
        loadChildren: () => ManufacturingModule,
        data: {tagId: 108},
    },
    {
        path: 'project',
        canMatch: [AuthGuard],
        loadChildren: () => ProjectModule, data: {tagId: 109},
    },
    {
        path: 'realestate',
        canMatch: [AuthGuard],
        loadChildren: () => RealestateModule, data: {tagId: 110},
    },
    {
        path: 'fleet',
        canMatch: [AuthGuard],
        loadChildren: () => FleetModule, data: {tagId: 111},
    },
    {
        path: 'pos',
        canMatch: [AuthGuard],
        loadChildren: () => POSModule, data: {tagId: 112},
  },
  {
    path: 'hotel',
    canMatch: [AuthGuard],
    loadChildren: () => HotelModule, data: { tagId: 114 },
  },
    {
        path: 'sales',
        canMatch: [AuthGuard],
        loadChildren: () => SalesModule, data: {tagId: 113},
    },
    {
        path: '**',
        redirectTo: 'error/404',
  },
  {
    path: 'staffassistant',
    canMatch: [AuthGuard],
    loadChildren: () => StaffAssistantModule, data: { tagId: 115 },
  },

];

export {Routing};
