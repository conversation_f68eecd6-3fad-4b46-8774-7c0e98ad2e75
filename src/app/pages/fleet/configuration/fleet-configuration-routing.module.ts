import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {EmployeesComponent} from "./employees/employees.component";
import {DriversComponent} from "./drivers/drivers.component";
import {EquipmentComponent} from "./equipment/equipment.component";



const routes: Routes = [
  {
    path: '',
    redirectTo: 'employees',
    pathMatch: 'full'
  },
  {
    path: 'employees',
    component: EmployeesComponent
  },
  {
    path: 'drivers',
    component: DriversComponent
  },
  {
    path: 'equipment',
    component: EquipmentComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class fleetConfigurationRoutingModule {
}
