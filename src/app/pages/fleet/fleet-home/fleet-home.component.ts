import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-fleet-home',
    templateUrl: './fleet-home.component.html',
    styleUrls: ['./fleet-home.component.scss'],
    standalone: false
})
export class fleetHomeComponent implements OnInit {
  // إحصائيات المركبات
  totalVehicles: number = 0;
  activeVehicles: number = 0;
  inactiveVehicles: number = 0;
  underMaintenanceVehicles: number = 0;

  // إحصائيات السائقين
  totalDrivers: number = 0;
  activeDrivers: number = 0;
  onLeaveDrivers: number = 0;
  availableDrivers: number = 0;

  // إحصائيات الحركة
  totalMovements: number = 0;
  completedMovements: number = 0;
  pendingMovements: number = 0;
  cancelledMovements: number = 0;

  // بيانات الرسوم البيانية
  fuelConsumptionData: any[] = [];
  vehiclesByTypeData: any[] = [];
  movementsByStatusData: any[] = [];

  // حركات المركبات الأخيرة
  recentMovements: any[] = [];

  // فواتير المركبات الأخيرة
  recentInvoices: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'تسجيل حركة مركبة', icon: 'fa-car', route: '/fleet/equipment_movement/carsmovement' },
    { title: 'تسجيل حركة معدات', icon: 'fa-truck', route: '/fleet/equipment_movement/equipmentmovement' },
    { title: 'إصدار فاتورة سيارات', icon: 'fa-file-invoice', route: '/fleet/equipment_accounts/vehicle_invoice' },
    { title: 'ترحيل المديونية', icon: 'fa-exchange-alt', route: '/fleet/equipment_accounts/debt_transfer' },
    { title: 'تقرير المعدات', icon: 'fa-chart-bar', route: '/fleet/reports/equipment_report' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات المركبات الوهمية
      this.totalVehicles = 85;
      this.activeVehicles = 65;
      this.inactiveVehicles = 12;
      this.underMaintenanceVehicles = 8;

      // بيانات السائقين الوهمية
      this.totalDrivers = 45;
      this.activeDrivers = 38;
      this.onLeaveDrivers = 4;
      this.availableDrivers = 3;

      // بيانات الحركة الوهمية
      this.totalMovements = 120;
      this.completedMovements = 95;
      this.pendingMovements = 18;
      this.cancelledMovements = 7;

      // بيانات الرسوم البيانية الوهمية
      this.fuelConsumptionData = [
        { month: 'يناير', consumption: 2500 },
        { month: 'فبراير', consumption: 2300 },
        { month: 'مارس', consumption: 2700 },
        { month: 'أبريل', consumption: 2400 },
        { month: 'مايو', consumption: 2600 },
        { month: 'يونيو', consumption: 2800 }
      ];

      this.vehiclesByTypeData = [
        { type: 'سيارات', count: 45 },
        { type: 'شاحنات', count: 20 },
        { type: 'معدات ثقيلة', count: 15 },
        { type: 'أخرى', count: 5 }
      ];

      this.movementsByStatusData = [
        { status: 'مكتملة', count: 95 },
        { status: 'قيد التنفيذ', count: 18 },
        { status: 'ملغاة', count: 7 }
      ];

      // بيانات حركات المركبات الأخيرة الوهمية
      this.recentMovements = [
        { id: 'MOV-2023-001', vehicle: 'تويوتا كامري - أ ب ج 1234', driver: 'أحمد محمد', startDate: '2023-06-15', endDate: '2023-06-15', destination: 'الرياض - جدة', status: 'مكتملة' },
        { id: 'MOV-2023-002', vehicle: 'هيونداي سوناتا - د هـ و 5678', driver: 'محمد علي', startDate: '2023-06-14', endDate: '2023-06-16', destination: 'الدمام - الخبر', status: 'قيد التنفيذ' },
        { id: 'MOV-2023-003', vehicle: 'نيسان باترول - ح ط ي 9012', driver: 'خالد عبدالله', startDate: '2023-06-13', endDate: '2023-06-13', destination: 'جدة - مكة', status: 'مكتملة' },
        { id: 'MOV-2023-004', vehicle: 'فورد F-150 - ك ل م 3456', driver: 'عمر حسن', startDate: '2023-06-17', endDate: '2023-06-19', destination: 'الرياض - القصيم', status: 'قيد التنفيذ' },
        { id: 'MOV-2023-005', vehicle: 'مرسيدس E200 - ن س ع 7890', driver: 'سعد فهد', startDate: '2023-06-12', endDate: '2023-06-12', destination: 'الرياض - المدينة', status: 'مكتملة' }
      ];

      // بيانات فواتير المركبات الأخيرة الوهمية
      this.recentInvoices = [
        { id: 'INV-2023-001', vehicle: 'تويوتا كامري - أ ب ج 1234', date: '2023-06-15', amount: 1250, type: 'صيانة دورية', status: 'مدفوعة' },
        { id: 'INV-2023-002', vehicle: 'هيونداي سوناتا - د هـ و 5678', date: '2023-06-14', amount: 850, type: 'وقود', status: 'مدفوعة' },
        { id: 'INV-2023-003', vehicle: 'نيسان باترول - ح ط ي 9012', date: '2023-06-13', amount: 3200, type: 'إصلاح', status: 'معلقة' },
        { id: 'INV-2023-004', vehicle: 'فورد F-150 - ك ل م 3456', date: '2023-06-12', amount: 1500, type: 'قطع غيار', status: 'مدفوعة' },
        { id: 'INV-2023-005', vehicle: 'مرسيدس E200 - ن س ع 7890', date: '2023-06-11', amount: 2800, type: 'صيانة طارئة', status: 'معلقة' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      vehicleStats: this.http.get<any>('api/fleet/dashboard/vehicles'),
      driverStats: this.http.get<any>('api/fleet/dashboard/drivers'),
      movementStats: this.http.get<any>('api/fleet/dashboard/movements'),
      recentMovements: this.http.get<any>('api/fleet/dashboard/recent-movements'),
      recentInvoices: this.http.get<any>('api/fleet/dashboard/recent-invoices')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalVehicles = results.vehicleStats.total;
        this.activeVehicles = results.vehicleStats.active;
        this.inactiveVehicles = results.vehicleStats.inactive;
        this.underMaintenanceVehicles = results.vehicleStats.underMaintenance;

        this.totalDrivers = results.driverStats.total;
        this.activeDrivers = results.driverStats.active;
        this.onLeaveDrivers = results.driverStats.onLeave;
        this.availableDrivers = results.driverStats.available;

        this.totalMovements = results.movementStats.total;
        this.completedMovements = results.movementStats.completed;
        this.pendingMovements = results.movementStats.pending;
        this.cancelledMovements = results.movementStats.cancelled;

        this.recentMovements = results.recentMovements;
        this.recentInvoices = results.recentInvoices;

        // بيانات الرسوم البيانية
        this.fuelConsumptionData = results.vehicleStats.fuelConsumption;
        this.vehiclesByTypeData = results.vehicleStats.byType;
        this.movementsByStatusData = results.movementStats.byStatus;
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة الحركة
  getMovementStatusClass(status: string): string {
    switch (status) {
      case 'مكتملة':
        return 'badge-light-success';
      case 'قيد التنفيذ':
        return 'badge-light-warning';
      case 'ملغاة':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون حالة الفاتورة
  getInvoiceStatusClass(status: string): string {
    switch (status) {
      case 'مدفوعة':
        return 'badge-light-success';
      case 'معلقة':
        return 'badge-light-warning';
      case 'متأخرة':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
