import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {EquipmentBalanceReportComponent} from "./equipment-balance-report/equipment-balance-report.component";
import {EquipmentReportComponent} from "./equipment-report/equipment-report.component";



const routes: Routes = [
  {
    path: '',
    redirectTo: 'equipment_report',
    pathMatch: 'full'
  },
  {
    path: 'equipment_report',
    component: EquipmentReportComponent
  },
  {
    path: 'equipment_balance_report',
    component: EquipmentBalanceReportComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class fleetReportsRoutingModule {
}
