import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {DeptTransferComponent} from "./dept-transfer/dept-transfer.component";
import {VehicleInvoiceComponent} from "./vehicle-invoice/vehicle-invoice.component";
import {EquipmentInvoiceComponent} from "./equipment-invoice/equipment-invoice.component";



const routes: Routes = [
  {
    path: '',
    redirectTo: 'debt_transfer',
    pathMatch: 'full'
  },
  {
    path: 'debt_transfer',
    component: DeptTransferComponent
  },
  {
    path: 'vehicle_invoice',
    component: VehicleInvoiceComponent
  },
  {
    path: 'equipment_invoice',
    component: EquipmentInvoiceComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class fleetEquipmentAccountsRoutingModule {
}
