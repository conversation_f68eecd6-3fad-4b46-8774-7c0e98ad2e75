import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {SharedModule} from "../../shared/shared.module";
import { DeptTransferComponent } from './dept-transfer/dept-transfer.component';
import { VehicleInvoiceComponent } from './vehicle-invoice/vehicle-invoice.component';
import { EquipmentInvoiceComponent } from './equipment-invoice/equipment-invoice.component';
import {fleetEquipmentAccountsRoutingModule} from "./fleet-equipment-accounts-routing.module";

@NgModule({
  declarations: [

    DeptTransferComponent,
       VehicleInvoiceComponent,
       EquipmentInvoiceComponent
  ],
  imports: [fleetEquipmentAccountsRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class FleetEquipmentAccountsModule {}
