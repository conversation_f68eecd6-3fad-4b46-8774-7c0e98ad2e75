import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {SharedModule} from "../../shared/shared.module";
import {CarsmovementComponent} from "./carsmovement/carsmovement.component";
import {EquipmentmovementComponent} from "./equipmentmovement/equipmentmovement.component";
import {FleetEquipmentMovementRoutingModule} from "./fleet-equipment-movement-routing.module";

@NgModule({
  declarations: [CarsmovementComponent, EquipmentmovementComponent],
  imports: [FleetEquipmentMovementRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class FleetEquipmentMovementModule {}
