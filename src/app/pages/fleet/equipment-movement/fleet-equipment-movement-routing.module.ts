import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {EquipmentmovementComponent} from "./equipmentmovement/equipmentmovement.component";
import {CarsmovementComponent} from "./carsmovement/carsmovement.component";



const routes: Routes = [
  {
    path: '',
    redirectTo: 'equipment_movement',
    pathMatch: 'full'
  },
  {
    path: 'equipment_movement',
    component: EquipmentmovementComponent
  },
  {
    path: 'carsmovement',
    component: CarsmovementComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FleetEquipmentMovementRoutingModule {
}
