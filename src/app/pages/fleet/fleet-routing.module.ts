import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { fleetHomeComponent } from './fleet-home/fleet-home.component';



const routes: Routes = [

  {
    path: '',
    component: fleetHomeComponent
  },
  {
    path: 'configuration',
    loadChildren: () =>
        import('./configuration/fleet-configuration.module').then((m) => m.FleetConfigurationModule),
  },
  {
    path: 'equipment_movement',
    loadChildren: () =>
        import('./equipment-movement/fleet-equipment-movement.module').then((m) => m.FleetEquipmentMovementModule),
  },
  {
    path: 'equipment_accounts',
    loadChildren: () =>
        import('./equipment-accounts/fleet-equipment-accounts.module').then((m) => m.FleetEquipmentAccountsModule),
  },
  {
    path: 'reports',
    loadChildren: () =>
        import('./reports/fleet-reports.module').then((m) => m.FleetReportsModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class fleetRoutingModule {
}
