:host {
  width: 100%;
  height: 100%;
  display: block;
}

// Contenedor principal
.pos-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f8fa;
  overflow: hidden;
}

// Barra superior
.pos-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e4e6ef;
  height: 60px;

  .pos-header-left {
    display: flex;
    align-items: center;

    .pos-logo {
      display: flex;
      align-items: center;
      font-size: 1.25rem;
      font-weight: 600;
      color: #181c32;
    }
  }

  .pos-header-center {
    .btn-group {
      .btn {
        border-radius: 0.475rem;
        margin: 0 2px;

        &.active {
          background-color: #009ef7;
          color: #ffffff;
        }
      }
    }
  }

  .pos-header-right {
    display: flex;
    align-items: center;

    .input-group {
      width: 250px;
    }
  }
}

// Contenido principal
.pos-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

// Panel izquierdo - Carrito
.pos-left-panel {
  width: 35%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-right: 1px solid #e4e6ef;
  overflow: hidden;
}

// Lista de productos seleccionados
.pos-order-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;

  .pos-order-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #f5f8fa;

    .pos-order-item-number {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f8fa;
      border-radius: 4px;
      margin-right: 0.75rem;
      font-size: 0.85rem;
      color: #7e8299;
    }

    .pos-order-item-details {
      flex: 1;

      .pos-order-item-name {
        font-size: 0.95rem;
        color: #181c32;
        font-weight: 500;
      }

      .pos-order-item-badge {
        margin-top: 0.25rem;

        .badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }

    .pos-order-item-price {
      font-size: 0.95rem;
      color: #181c32;
      font-weight: 600;
      text-align: right;
      min-width: 80px;
    }
  }
}

// Resumen del pedido
.pos-order-summary {
  padding: 1rem;
  border-top: 1px solid #e4e6ef;
  border-bottom: 1px solid #e4e6ef;

  .pos-order-taxes, .pos-order-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .pos-order-taxes-label, .pos-order-total-label {
    font-size: 0.95rem;
    color: #7e8299;
  }

  .pos-order-taxes-value, .pos-order-total-value {
    font-size: 1rem;
    color: #181c32;
    font-weight: 600;
  }

  .pos-order-total {
    margin-top: 0.5rem;

    .pos-order-total-label, .pos-order-total-value {
      font-size: 1.15rem;
      font-weight: 700;
    }

    .pos-order-total-value {
      color: #009ef7;
    }
  }
}

// Sección de cliente
.pos-customer-section {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e4e6ef;

  .pos-customer-tab-buttons {
    display: flex;

    .pos-customer-tab {
      flex: 1;
      padding: 0.5rem;
      text-align: center;
      background-color: #f5f8fa;
      border: none;
      color: #7e8299;
      font-size: 0.95rem;
      cursor: pointer;

      &:first-child {
        border-top-left-radius: 0.475rem;
        border-bottom-left-radius: 0.475rem;
      }

      &:last-child {
        border-top-right-radius: 0.475rem;
        border-bottom-right-radius: 0.475rem;
      }

      &.active {
        background-color: #009ef7;
        color: #ffffff;
      }
    }
  }
}

// Teclado numérico
.pos-numpad {
  padding: 0.75rem;

  .pos-numpad-row {
    display: flex;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .pos-numpad-button {
      flex: 1;
      height: 50px;
      margin-right: 0.5rem;
      border: none;
      background-color: #f5f8fa;
      color: #181c32;
      font-size: 1.15rem;
      font-weight: 600;
      border-radius: 0.475rem;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }

      &.pos-numpad-function {
        background-color: #e4e6ef;
        font-size: 0.95rem;
      }

      &.pos-numpad-backspace {
        background-color: #f1416c;
        color: #ffffff;
      }
    }
  }
}

// Acciones rápidas
.pos-quick-actions {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  border-bottom: 1px solid #e4e6ef;

  .pos-quick-action-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: calc(25% - 0.5rem);
    height: 70px;
    background-color: #f5f8fa;
    border: none;
    border-radius: 0.475rem;
    color: #5e6278;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;

    i {
      color: #009ef7;
    }

    &:hover {
      background-color: #eef3f7;
    }
  }
}

// Botón de pago
.pos-payment-button-container {
  padding: 0.75rem;

  .pos-payment-button {
    width: 100%;
    height: 60px;
    border: none;
    background-color: #50cd89;
    color: #ffffff;
    font-size: 1.15rem;
    font-weight: 600;
    border-radius: 0.475rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: darken(#50cd89, 5%);
    }
  }
}

// Panel derecho - Productos
.pos-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Categorías de productos
.pos-categories {
  display: flex;
  padding: 0.75rem;
  border-bottom: 1px solid #e4e6ef;
  background-color: #ffffff;

  .pos-category-button {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-right: 0.75rem;
    border-radius: 0.475rem;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    &.pos-category-events {
      background-color: #f1416c;
      color: #ffffff;
    }

    &.pos-category-misc {
      background-color: #ffc700;
      color: #ffffff;
    }

    &.pos-category-desks {
      background-color: #7239ea;
      color: #ffffff;
    }

    &.pos-category-chairs {
      background-color: #f1416c;
      color: #ffffff;
    }
  }
}

// Productos
.pos-products {
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem;
  background-color: #f5f8fa;
}

// Cuadrícula de productos
.pos-product-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.375rem 0.75rem;

  &:last-child {
    margin-bottom: 0;
  }
}

// Elemento de producto
.pos-product-item {
  width: calc(20% - 0.75rem);
  margin: 0 0.375rem 0.75rem;
  background-color: #ffffff;
  border-radius: 0.475rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.05);
  }

  .pos-product-image {
    position: relative;
    padding-top: 100%;
    background-color: #f5f8fa;

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;

      &[src$="default-product.svg"] {
        object-fit: contain;
        padding: 15%;
        background-color: #f5f8fa;
      }
    }

    .pos-product-badge {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f1416c;
      color: #ffffff;
      font-size: 0.85rem;
      font-weight: 600;
      border-radius: 50%;
    }
  }

  .pos-product-name {
    padding: 0.75rem;
    font-size: 0.85rem;
    color: #181c32;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Responsive
@media (max-width: 1400px) {
  .pos-product-item {
    width: calc(25% - 0.75rem);
  }
}

@media (max-width: 1200px) {
  .pos-left-panel {
    width: 40%;
  }

  .pos-product-item {
    width: calc(33.333% - 0.75rem);
  }
}

@media (max-width: 992px) {
  .pos-left-panel {
    width: 45%;
  }

  .pos-product-item {
    width: calc(50% - 0.75rem);
  }
}

// Barra inferior
.pos-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  border-top: 1px solid #e4e6ef;
  height: 60px;
  box-shadow: 0 -0.25rem 1rem rgba(0, 0, 0, 0.03);

  .pos-footer-section {
    display: flex;
    align-items: center;
  }

  .pos-footer-info {
    flex: 1;

    .pos-footer-info-item {
      display: flex;
      align-items: center;
      margin-right: 1.5rem;
      color: #5e6278;
      font-size: 0.95rem;

      i {
        color: #009ef7;
      }
    }
  }

  .pos-footer-actions {
    .pos-footer-button {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      margin-right: 0.5rem;
      background-color: #f5f8fa;
      border: none;
      border-radius: 0.475rem;
      color: #5e6278;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        margin-right: 0;
      }

      i {
        color: #009ef7;
      }

      &:hover {
        background-color: #eef3f7;
      }
    }
  }

  .pos-footer-status {
    display: flex;
    align-items: center;
    margin-left: 1rem;

    .pos-footer-status-indicator {
      display: flex;
      align-items: center;
      margin-left: 1rem;
      color: #5e6278;
      font-size: 0.85rem;

      i {
        margin-right: 0.25rem;
        color: #a1a5b7;
      }

      &.online {
        i {
          color: #50cd89;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .pos-footer-info {
    .pos-footer-info-item:nth-child(2) {
      display: none;
    }
  }
}

@media (max-width: 992px) {
  .pos-footer-actions {
    .pos-footer-button:nth-child(2) {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .pos-content {
    flex-direction: column;
  }

  .pos-left-panel, .pos-right-panel {
    width: 100%;
    height: 50vh;
  }

  .pos-product-item {
    width: calc(33.333% - 0.75rem);
  }
  
  .pos-footer {
    flex-wrap: wrap;
    height: auto;
    padding: 0.5rem;
    
    .pos-footer-section {
      width: 100%;
      justify-content: center;
      margin-bottom: 0.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .pos-footer-info {
      order: 2;
    }
    
    .pos-footer-actions {
      order: 1;
    }
    
    .pos-footer-status {
      order: 3;
      margin-left: 0;
    }
  }
}
