<!-- Contenedor principal del terminal POS -->
<div class="pos-container">
  <!-- <PERSON><PERSON> superior -->
  <div class="pos-header">
    <div class="pos-header-left">
      <span class="pos-logo">
        <i class="fa fa-tablet fs-1 me-2"></i>
        {{ 'COMMON.POSTerminal' | translate }}
      </span>
    </div>
    <div class="pos-header-center">
      <div class="btn-group">
        <button type="button" class="btn btn-sm btn-light-primary active">{{ 'COMMON.Register' | translate }}</button>
        <button type="button" class="btn btn-sm btn-light-primary">{{ 'COMMON.Orders' | translate }}</button>
        <button type="button" class="btn btn-sm btn-light-primary">
          <i class="fa fa-th-large"></i>
        </button>
        <button type="button" class="btn btn-sm btn-light-primary">001</button>
      </div>
    </div>
    <div class="pos-header-right">
      <div class="input-group input-group-sm">
        <span class="input-group-text bg-light">
          <i class="fa fa-search fs-3"></i>
        </span>
        <input type="text" class="form-control form-control-sm"
               placeholder="{{ 'COMMON.SearchProducts' | translate }}"
               [(ngModel)]="searchQuery"
               (input)="filterBySearch(searchQuery)">
      </div>
      <button type="button" class="btn btn-sm btn-icon btn-light-primary ms-2">
        <i class="fa fa-barcode fs-3"></i>
      </button>
      <button type="button" class="btn btn-sm btn-icon btn-light-primary ms-2">
        <i class="fa fa-th fs-3"></i>
      </button>
    </div>
  </div>

  <!-- Contenido principal -->
  <div class="pos-content">
    <!-- Panel izquierdo - Carrito -->
    <div class="pos-left-panel">
      <!-- Productos seleccionados -->
      <div class="pos-order-list">
        <div class="pos-order-item" *ngFor="let item of cartItems; let i = index">
          <div class="pos-order-item-number">{{ i + 1 }}</div>
          <div class="pos-order-item-details">
            <div class="pos-order-item-name">{{ item.name }}</div>
            <div class="pos-order-item-badge" *ngIf="item.quantity && item.quantity > 1">
              <span class="badge badge-danger">{{ item.quantity }}</span>
            </div>
          </div>
          <div class="pos-order-item-price">$ {{ (item.price * (item.quantity || 1)).toFixed(2) }}</div>
        </div>
      </div>

      <!-- Resumen del pedido -->
      <div class="pos-order-summary">
        <div class="pos-order-taxes">
          <div class="pos-order-taxes-label">{{ 'COMMON.Taxes' | translate }}</div>
          <div class="pos-order-taxes-value">$ {{ taxes.toFixed(2) }}</div>
        </div>
        <div class="pos-order-total">
          <div class="pos-order-total-label">{{ 'COMMON.TotalAmount' | translate }}</div>
          <div class="pos-order-total-value">$ {{ total.toFixed(2) }}</div>
        </div>
      </div>

      <!-- Información del cliente -->
      <div class="pos-customer-section">
        <div class="pos-customer-tab-buttons">
          <button class="pos-customer-tab"
                  [class.active]="activeTab === 'customer'"
                  (click)="setActiveTab('customer')">
            {{ 'COMMON.CustomerTab' | translate }}
          </button>
          <button class="pos-customer-tab"
                  [class.active]="activeTab === 'note'"
                  (click)="setActiveTab('note')">
            {{ 'COMMON.NoteTab' | translate }}
          </button>
        </div>
      </div>

      <!-- Teclado numérico -->
      <div class="pos-numpad">
        <div class="pos-numpad-row">
          <button class="pos-numpad-button">1</button>
          <button class="pos-numpad-button">2</button>
          <button class="pos-numpad-button">3</button>
          <button class="pos-numpad-button pos-numpad-function">{{ 'COMMON.QtyButton' | translate }}</button>
        </div>
        <div class="pos-numpad-row">
          <button class="pos-numpad-button">4</button>
          <button class="pos-numpad-button">5</button>
          <button class="pos-numpad-button">6</button>
          <button class="pos-numpad-button pos-numpad-function">%</button>
        </div>
        <div class="pos-numpad-row">
          <button class="pos-numpad-button">7</button>
          <button class="pos-numpad-button">8</button>
          <button class="pos-numpad-button">9</button>
          <button class="pos-numpad-button pos-numpad-function">{{ 'COMMON.PriceButton' | translate }}</button>
        </div>
        <div class="pos-numpad-row">
          <button class="pos-numpad-button">+/-</button>
          <button class="pos-numpad-button">0</button>
          <button class="pos-numpad-button">.</button>
          <button class="pos-numpad-button pos-numpad-function pos-numpad-backspace">
            <i class="fa fa-window-close fs-2"></i>
          </button>
        </div>
      </div>

      <!-- Botones de acciones rápidas -->
      <div class="pos-quick-actions">
        <button class="pos-quick-action-button" (click)="discountOrder()">
          <i class="fa fa-percent fs-2 mb-1"></i>
          <span>{{ 'COMMON.Discount' | translate }}</span>
        </button>
        <button class="pos-quick-action-button" (click)="holdOrder()">
          <i class="fa fa-pause fs-2 mb-1"></i>
          <span>{{ 'COMMON.Hold' | translate }}</span>
        </button>
        <button class="pos-quick-action-button" (click)="voidOrder()">
          <i class="fa fa-window-close fs-2 mb-1"></i>
          <span>{{ 'COMMON.Void' | translate }}</span>
        </button>
        <button class="pos-quick-action-button" (click)="addNote()">
          <i class="fa fa-clipboard fs-2 mb-1"></i>
          <span>{{ 'COMMON.Note' | translate }}</span>
        </button>
      </div>
      
      <!-- Botón de pago -->
      <div class="pos-payment-button-container">
        <button class="pos-payment-button" (click)="processPayment()">
          {{ 'COMMON.PaymentButton' | translate }}
        </button>
      </div>
    </div>

    <!-- Panel derecho - Productos -->
    <div class="pos-right-panel">
      <!-- Categorías de productos -->
      <div class="pos-categories">
        <div class="pos-category-button"
             *ngFor="let category of categories"
             [ngClass]="'pos-category-' + category.id"
             [ngStyle]="{'background-color': category.color}"
             (click)="filterByCategory(category.id)">
          <i [class]="category.icon + ' fs-2 me-2'"></i>
          {{ category.name | translate }}
        </div>
      </div>

      <!-- Productos -->
      <div class="pos-products">
        <div class="pos-product-grid">
          <div class="pos-product-item"
               *ngFor="let product of getFilteredProducts()"
               (click)="addToCart(product)">
            <div class="pos-product-image">
              <img [src]="product.image" [alt]="product.name" (error)="handleImageError($event)">
              <div class="pos-product-badge" *ngIf="product.badge">{{ product.badge }}</div>
            </div>
            <div class="pos-product-name">{{ product.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barra inferior con información adicional y acciones comunes -->
  <div class="pos-footer">
    <div class="pos-footer-section pos-footer-info">
      <div class="pos-footer-info-item">
        <i class="fa fa-user-check fs-2 me-2"></i>
        <span>{{ 'COMMON.Cashier' | translate }}: Admin</span>
      </div>
      <div class="pos-footer-info-item">
        <i class="fa fa-calendar-alt fs-2 me-2"></i>
        <span>{{ currentDate | date:'dd/MM/yyyy HH:mm' }}</span>
      </div>
    </div>
    <div class="pos-footer-section pos-footer-actions">
      <button class="pos-footer-button" (click)="openDrawer()">
        <i class="fa fa-shopping-cart fs-2 me-1"></i>
        {{ 'COMMON.OpenDrawer' | translate }}
      </button>
      <button class="pos-footer-button" (click)="printLastReceipt()">
        <i class="fa fa-print fs-2 me-1"></i>
        {{ 'COMMON.LastReceipt' | translate }}
      </button>
      <button class="pos-footer-button" (click)="synchronizeData()">
        <i class="fa fa-cloud-download-alt fs-2 me-1"></i>
        {{ 'COMMON.Sync' | translate }}
      </button>
    </div>
    <div class="pos-footer-section pos-footer-status">
      <div class="pos-footer-status-indicator online">
        <i class="fa fa-wifi fs-2"></i>
        <span>{{ 'COMMON.Online' | translate }}</span>
      </div>
      <div class="pos-footer-status-indicator">
        <i class="fa fa-battery-half fs-2"></i>
      </div>
    </div>
  </div>
</div>
