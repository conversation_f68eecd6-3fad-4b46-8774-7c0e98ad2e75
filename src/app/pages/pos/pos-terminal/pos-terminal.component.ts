import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { POSService } from '../pos.service';
import { TranslateService } from '@ngx-translate/core';

interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
  category: string;
  quantity?: number;
  badge?: number;
}

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-pos-terminal',
  templateUrl: './pos-terminal.component.html',
  styleUrls: ['./pos-terminal.component.scss'],
  standalone: false
})
export class PosTerminalComponent implements OnInit, OnDestroy {
  // Suscripciones
  private subscriptions: Subscription[] = [];

  // Categorías de productos
  categories: Category[] = [
    { id: 'events', name: 'COMMON.Events', icon: 'fa fa-calendar-plus', color: '#f1416c' },
    { id: 'misc', name: 'COMMON.Misc', icon: 'fa fa-plus-square', color: '#ffc700' },
    { id: 'desks', name: 'COMMON.Desks', icon: 'fa fa-building', color: '#7239ea' },
    { id: 'chairs', name: 'COMMON.Chairs', icon: 'fa fa-chair', color: '#f1416c' }
  ];

  // Productos de ejemplo
  products: Product[] = [
    { id: 1, name: 'Acoustic Bloc Screens', price: 339.25, image: './assets/media/products/acoustic-bloc.jpg', category: 'desks', badge: 1 },
    { id: 2, name: 'Cabinet with Doors', price: 159.00, image: './assets/media/products/cabinet-doors.jpg', category: 'desks' },
    { id: 3, name: 'Conference Chair', price: 85.75, image: './assets/media/products/conference-chair.jpg', category: 'chairs' },
    { id: 4, name: 'Corner Desk Left Sit', price: 169.05, image: './assets/media/products/corner-desk-left.jpg', category: 'desks' },
    { id: 5, name: 'Corner Desk Right Sit', price: 169.05, image: './assets/media/products/corner-desk-right.jpg', category: 'desks', badge: 1 },
    { id: 6, name: 'Customizable Desk', price: 862.50, image: './assets/media/products/customizable-desk.jpg', category: 'desks', badge: 1 },
    { id: 7, name: 'Desk Combination', price: 245.30, image: './assets/media/products/desk-combination.jpg', category: 'desks' },
    { id: 8, name: 'Desk Organizer', price: 45.00, image: './assets/media/products/desk-organizer.jpg', category: 'misc' },
    { id: 9, name: 'Desk Pad', price: 2.28, image: './assets/media/products/desk-pad.jpg', category: 'misc', badge: 1 },
    { id: 10, name: 'Desk Stand with Screen', price: 375.00, image: './assets/media/products/desk-stand.jpg', category: 'desks' },
    { id: 11, name: 'Drawer', price: 28.75, image: './assets/media/products/drawer.jpg', category: 'misc' },
    { id: 12, name: 'Drawer Black', price: 28.75, image: './assets/media/products/drawer-black.jpg', category: 'misc', badge: 1 },
    { id: 13, name: 'Flipover', price: 2242.50, image: './assets/media/products/flipover.jpg', category: 'events', badge: 1 },
    { id: 14, name: 'Four Person Desk', price: 320.00, image: './assets/media/products/four-person-desk.jpg', category: 'desks' },
    { id: 15, name: 'Individual Workplace', price: 210.00, image: './assets/media/products/individual-workplace.jpg', category: 'desks' },
    { id: 16, name: 'Large Cabinet', price: 175.00, image: './assets/media/products/large-cabinet.jpg', category: 'misc' },
    { id: 17, name: 'Large Desk', price: 220.00, image: './assets/media/products/large-desk.jpg', category: 'desks' },
    { id: 18, name: 'Large Meeting Table', price: 450.00, image: './assets/media/products/large-meeting-table.jpg', category: 'desks' },
    { id: 19, name: 'LED Lamp', price: 35.00, image: './assets/media/products/led-lamp.jpg', category: 'misc' },
    { id: 20, name: 'Letter Tray', price: 5.52, image: './assets/media/products/letter-tray.jpg', category: 'misc', badge: 1 }
  ];

  // Productos en el carrito
  cartItems: Product[] = [];

  // Totales
  subtotal: number = 0;
  taxes: number = 0;
  total: number = 0;

  // Filtros
  selectedCategory: string = '';
  searchQuery: string = '';

  // Pestaña activa
  activeTab: string = 'customer';

  // Fecha actual
  currentDate: Date = new Date();

  constructor(
    private posService: POSService,
    private cdr: ChangeDetectorRef,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    // Inicializar componente
    this.initComponent();
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones para evitar memory leaks
    this.subscriptions.forEach(sb => sb.unsubscribe());
  }

  initComponent(): void {
    // Asegurarse de que todos los productos tengan una imagen
    this.products.forEach(product => {
      if (!product.image || product.image.trim() === '') {
        product.image = './assets/media/products/default-product.svg';
      }
    });

    // Inicializar el carrito con algunos productos de ejemplo
    this.addToCart(this.products.find(p => p.id === 12)!); // Drawer Black
    this.addToCart(this.products.find(p => p.id === 13)!); // Flipover
    this.addToCart(this.products.find(p => p.id === 1)!);  // Acoustic Bloc Screens
    this.addToCart(this.products.find(p => p.id === 5)!);  // Corner Desk Right Sit
    this.addToCart(this.products.find(p => p.id === 6)!);  // Customizable Desk
    this.addToCart(this.products.find(p => p.id === 9)!);  // Desk Pad
    this.addToCart(this.products.find(p => p.id === 20)!); // Letter Tray

    // Calcular totales
    this.calculateTotals();
  }

  // Agregar producto al carrito
  addToCart(product: Product): void {
    const existingItem = this.cartItems.find(item => item.id === product.id);

    if (existingItem) {
      existingItem.quantity = (existingItem.quantity || 1) + 1;
    } else {
      this.cartItems.push({...product, quantity: 1});
    }

    this.calculateTotals();
  }

  // Eliminar producto del carrito
  removeFromCart(productId: number): void {
    const index = this.cartItems.findIndex(item => item.id === productId);

    if (index !== -1) {
      this.cartItems.splice(index, 1);
      this.calculateTotals();
    }
  }

  // Actualizar cantidad de un producto en el carrito
  updateQuantity(productId: number, quantity: number): void {
    const item = this.cartItems.find(item => item.id === productId);

    if (item) {
      item.quantity = quantity;

      if (item.quantity <= 0) {
        this.removeFromCart(productId);
      } else {
        this.calculateTotals();
      }
    }
  }

  // Calcular totales
  calculateTotals(): void {
    this.subtotal = this.cartItems.reduce((total, item) => {
      return total + (item.price * (item.quantity || 1));
    }, 0);

    this.taxes = this.subtotal * 0.15; // 15% de impuestos
    this.total = this.subtotal + this.taxes;
  }

  // Filtrar productos por categoría
  filterByCategory(categoryId: string): void {
    this.selectedCategory = categoryId;
  }

  // Filtrar productos por búsqueda
  filterBySearch(query: string): void {
    this.searchQuery = query;
  }

  // Obtener productos filtrados
  getFilteredProducts(): Product[] {
    let filtered = this.products;

    if (this.selectedCategory) {
      filtered = filtered.filter(p => p.category === this.selectedCategory);
    }

    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(p => p.name.toLowerCase().includes(query));
    }

    return filtered;
  }

  // Cambiar pestaña
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  // Procesar pago
  processPayment(): void {
    console.log('Procesando pago:', {
      items: this.cartItems,
      subtotal: this.subtotal,
      taxes: this.taxes,
      total: this.total
    });

    // Aquí iría la lógica para procesar el pago
    alert('Pago procesado correctamente');

    // Limpiar carrito después del pago
    this.cartItems = [];
    this.calculateTotals();
  }

  // Aplicar descuento al pedido
  discountOrder(): void {
    // Si no hay productos en el carrito, no hacer nada
    if (this.cartItems.length === 0) return;
    
    // Mostrar diálogo simple para el descuento (podría reemplazarse con uno más elaborado)
    const discountPercent = prompt('Ingrese el porcentaje de descuento (1-100):', '10');
    if (discountPercent === null) return;
    
    const discount = parseFloat(discountPercent);
    if (isNaN(discount) || discount < 0 || discount > 100) {
      alert('Por favor ingrese un porcentaje válido entre 0 y 100');
      return;
    }
    
    // Aplicar descuento a cada producto
    this.cartItems.forEach(item => {
      const originalPrice = item.price;
      item.price = originalPrice * (1 - discount / 100);
    });
    
    this.calculateTotals();
    alert(`Descuento del ${discount}% aplicado al pedido`);
  }

  // Poner el pedido en espera
  holdOrder(): void {
    if (this.cartItems.length === 0) {
      alert('No hay productos en el carrito para poner en espera');
      return;
    }
    
    const orderName = prompt('Ingrese un nombre para este pedido:', 'Pedido ' + new Date().toLocaleTimeString());
    if (orderName === null) return;
    
    // Aquí guardaríamos el pedido en la base de datos o en localStorage
    console.log('Pedido en espera:', {
      name: orderName,
      items: this.cartItems,
      total: this.total
    });
    
    alert(`Pedido "${orderName}" puesto en espera`);
    this.cartItems = [];
    this.calculateTotals();
  }

  // Anular el pedido actual
  voidOrder(): void {
    if (this.cartItems.length === 0) {
      alert('No hay productos en el carrito para anular');
      return;
    }
    
    if (confirm('¿Está seguro que desea anular este pedido?')) {
      this.cartItems = [];
      this.calculateTotals();
      alert('Pedido anulado');
    }
  }

  // Agregar nota al pedido
  addNote(): void {
    const note = prompt('Ingrese una nota para este pedido:');
    if (note === null || note.trim() === '') return;
    
    console.log('Nota agregada al pedido:', note);
    alert('Nota agregada al pedido');
  }

  // Abrir cajón de efectivo
  openDrawer(): void {
    console.log('Abriendo cajón de efectivo');
    alert('Comando enviado para abrir el cajón');
  }

  // Imprimir último recibo
  printLastReceipt(): void {
    console.log('Imprimiendo último recibo');
    alert('Imprimiendo último recibo');
  }

  // Sincronizar datos con el servidor
  synchronizeData(): void {
    console.log('Sincronizando datos con el servidor');
    alert('Datos sincronizados correctamente');
  }

  // Manejar errores de carga de imágenes
  handleImageError(event: any): void {
    // Establecer la imagen predeterminada cuando hay un error de carga
    event.target.src = './assets/media/products/default-product.svg';
  }

  // Crear un nuevo producto con imagen predeterminada
  createNewProduct(productData: Partial<Product>): Product {
    const defaultImage = './assets/media/products/default-product.svg';

    // Generar un nuevo ID (normalmente esto vendría del backend)
    const newId = Math.max(...this.products.map(p => p.id)) + 1;

    // Crear el nuevo producto con valores predeterminados para los campos faltantes
    const newProduct: Product = {
      id: newId,
      name: productData.name || 'Nuevo Producto',
      price: productData.price || 0,
      image: productData.image || defaultImage,
      category: productData.category || 'misc',
      quantity: productData.quantity,
      badge: productData.badge
    };

    // Agregar el producto a la lista de productos
    this.products.push(newProduct);

    return newProduct;
  }
}
