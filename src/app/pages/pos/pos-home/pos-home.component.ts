import { Component, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { TranslationService } from '../../../modules/i18n/translation.service';

declare var KTApp: any;
declare var ApexCharts: any;

@Component({
    selector: 'app-pos-home',
    templateUrl: './pos-home.component.html',
    styleUrls: ['./pos-home.component.scss'],
    standalone: false
})
export class posHomeComponent implements OnInit, AfterViewInit, OnDestroy {
  // تخزين اشتراكات RxJS للتنظيف عند تدمير المكون
  private subscriptions: Subscription[] = [];

  // التاريخ الحالي لعرضه في الإحصائيات
  public today: Date = new Date();
  public currentLang: string;

  constructor(private translationService: TranslationService) { 
    this.currentLang = this.translationService.getSelectedLanguage();
  }
  
  // Método para cambiar el idioma
  changeLang(lang: string): void {
    this.translationService.setLanguage(lang);
    this.currentLang = lang;
  }

  ngOnInit(): void {
    // تهيئة المكون
  }

  ngAfterViewInit(): void {
    // تهيئة الصفحة بعد تحميل العرض
    // لقد تم استبدال الرسوم البيانية ببطاقات إحصائية
    // لا حاجة لمكتبة ApexCharts بعد الآن
  }

  ngOnDestroy(): void {
    // إلغاء جميع الاشتراكات لتجنب تسرب الذاكرة
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  // لقد تم استبدال الدوال الخاصة بالرسوم البيانية ببطاقات إحصائية ثابتة في ملف HTML
}
