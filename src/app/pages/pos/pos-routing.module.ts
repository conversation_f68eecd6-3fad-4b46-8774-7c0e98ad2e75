import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { posHomeComponent } from './pos-home/pos-home.component';
import { PosTerminalComponent } from './pos-terminal/pos-terminal.component';


const routes: Routes = [
  {
    path: '',
    component: posHomeComponent
  },
  {
    path: 'terminal',
    component: PosTerminalComponent
  },
  {
    path: 'pos',
    component: PosTerminalComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class posRoutingModule {
}
