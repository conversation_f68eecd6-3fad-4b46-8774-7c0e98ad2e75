
<a href="#" class="btn btn-light-primary btn-sm" data-kt-menu-trigger="hover" data-kt-menu-placement="bottom-end">
  Actions
  <span class="svg-icon svg-icon-5 m-0">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
            fill="currentColor" />
    </svg>
  </span>
</a>
<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
     data-kt-menu="true">

  <div class="menu-item px-3" *hasPermission="{action: 'print', module: moduleName }">
    <a class="menu-link px-3" target="_blank" href="/reports/units"
       data-kt-company-table-filter="delete_row">Print</a>
  </div>
  <div class="menu-item px-3" *hasPermission="{action: 'print', module: moduleName }">
    <a class="menu-link px-3" target="_blank" href="/reports/units?withLogo=true"
       data-kt-company-table-filter="delete_row">Print With Logo</a>
  </div>
  <div class="menu-item px-3">
    <a class="menu-link px-3" (click)="uploadExcel()" data-kt-company-table-filter="delete_row">
      Import from
      Excel
    </a>
  </div>
  <div class="menu-item px-3">
    <a class="menu-link px-3" (click)="exportToExcel()" data-kt-company-table-filter="delete_row">
      Export to
      Excel
    </a>
  </div>
</div>


<dx-data-grid id="gridcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="id"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true"
              [remoteOperations]="true"
              [repaintChangesOnly]="true"
              (onSaving)="onSaving($event)">

  <dxo-editing mode="batch"
               [allowAdding]="true"
               [allowDeleting]="true"
               [allowUpdating]="true">

  </dxo-editing>

  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="id" caption="Id" [allowEditing]="false"></dxi-column>
  <dxi-column dataField="nameAr" caption="Name Ar"></dxi-column>
  <dxi-column dataField="nameEn" caption="Name En"></dxi-column>
  <dxi-column dataField="citizen" caption="Citizen"></dxi-column>
 


  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>


  </dxo-summary>


</dx-data-grid>

