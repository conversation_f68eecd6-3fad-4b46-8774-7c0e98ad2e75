<dx-data-grid
  [dataSource]="data"
  id="gridContainer"
  [rtlEnabled]="true"
  keyExpr="OrderID"
  [showBorders]="true"
  (onEditorPreparing)="onEditorPreparing($event)"
  [columnAutoWidth]="true"
  [remoteOperations]="true"
  (onRowInserted)="onRowInserted($event)"
  (onInitNewRow)="initRow($event)"
  (onRowRemoved)="onRowRemoved($event)"
>
  <dxo-scrolling [mode]="scrollingMode"></dxo-scrolling>
  <dxo-editing
    mode="row"
    [allowAdding]="true"
    [allowDeleting]="true"
    [allowUpdating]="true"
    [confirmDelete]="false"
    [useIcons]="true"
    [newRowPosition]="newRowPosition"
    [changes]="changes"
    [(editRowKey)]="editRowKey"
  ></dxo-editing>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  ></dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>
  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="caseId"
    caption="{{ 'COMMON.AccountType' | translate }}"
  >
    <dxo-lookup
      [dataSource]="AccountThirdParty"
      displayExpr="nameAr"
      valueExpr="caseId"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="ThirdPartyAccountId"
    caption="{{ 'COMMON.AccName' | translate }}"
  >
    <dxo-lookup
      [dataSource]="ThirdPartyAccounts"
      displayExpr="nameAr"
      valueExpr="ThirdPartyAccountId"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="Value"
    caption="{{ 'COMMON.Value' | translate }}"
  ></dxi-column>

  <dxi-column dataField="costId" caption="{{ 'COMMON.CostName' | translate }}">
    <dxo-lookup
      [dataSource]="costCenter"
      displayExpr="nameAr"
      valueExpr="code"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="analyticAccountTypeId"
    caption="{{ 'COMMON.CostType' | translate }}"
  >
    <dxo-lookup
      [dataSource]="costGroup"
      displayExpr="name"
      valueExpr="id"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="financial_entity_TypeId"
    caption="{{ 'COMMON.financial_entity_Type' | translate }}"
  >
    <dxo-lookup
      [dataSource]="financial_entity_Types"
      displayExpr="name"
      valueExpr="id"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="financial_entity_Id"
    caption="{{ 'COMMON.financial_entity' | translate }}"
  >
    <dxo-lookup
      [dataSource]="FinancialEntities"
      displayExpr="nameAr"
      valueExpr="id"
    ></dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="Notes"
    caption="{{ 'COMMON.Notes' | translate }}"
  ></dxi-column>

  <dxo-header-filter>
    <dxo-search [enabled]="true"></dxo-search>
  </dxo-header-filter>

  <dxi-column type="buttons">
    <dxi-button
      icon="add"
      text="Add"
      [onClick]="onAddButtonClick"
      [visible]="isAddButtonVisible"
    ></dxi-button>
    <dxi-button name="delete"></dxi-button>
    <dxi-button name="save"></dxi-button>
    <dxi-button name="cancel"></dxi-button>
  </dxi-column>

  <dxo-toolbar>
    <dxi-item name="addRowButton" showText="always"></dxi-item>
  </dxo-toolbar>
</dx-data-grid>
