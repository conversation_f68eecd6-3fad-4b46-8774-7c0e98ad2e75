import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  Output,
} from '@angular/core';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import Guid from 'devextreme/core/guid';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from 'src/app/app.module';
import { InitNewRowEvent } from 'devextreme/ui/data_grid';
import {
  AccountingGridControleService,
  UOM,
} from './accounting-grid-controle.service';

let Product: any[];
let defualtUnit: number = 0;
let medumUnit: number = 0;
let smallUnit: number = 0;

type FirstArgument<T> = T extends (...args: any) => any
  ? Parameters<T>[0]
  : never;

@Component({
  selector: 'app-accounting-grid-controle',
  templateUrl: './accounting-grid-controle.component.html',
  styleUrls: ['./accounting-grid-controle.component.css'],
  standalone: false,
})
export class AccountingGridControleComponent implements OnDestroy {
  @Output()
  onDataUpdate = new EventEmitter<any>();

  moduleName = 'accounting.warehouses';

  Product: any[];
  AnalyticAccounts: any[];
  ThirdPartyAccounts: any[] = [];
  costCenter: any[];
  costGroup: any[];
  FinancialEntities: any[];

  financial_entity_Types: { id: number; name: string }[] = [
    { id: 1, name: 'Project' },
    { id: 2, name: 'Machine' },
    { id: 3, name: 'Assets' },
    { id: 4, name: 'Department' },
    { id: 5, name: 'Supplier' },
    { id: 6, name: 'Customer' },
    { id: 7, name: 'Cars' },
  ];

  data: any[];
  AccountThirdParty: any[];

  newRowPosition: DxDataGridTypes.NewRowPosition = 'pageBottom';

  scrollingMode: DxDataGridTypes.DataGridScrollMode = 'standard';
  pUnits: UOM[];
  changes: any = [];
  editRowKey: any = null;
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  financial_entity_TypeId: number = 0;

  searchExpr: any;
  subscription = new Subscription();
  maxId = 0;

  // gridColumns = ['ID', 'CompanyName', 'type'];
  constructor(
    private service: AccountingGridControleService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
    this.subscription.add(
      service.list().subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.maxId = this.data.length + 1;
          this.onDataUpdate.emit(this.data);
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getProductList().subscribe((r) => {
        if (r.success) {
          this.Product = r.data.slice(0, 100);
          Product = this.Product;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getAccountThirdParty().subscribe((r) => {
        if (r.success) {
          this.AccountThirdParty = r.data;
          this.cdk.detectChanges(); // تحديث واجهة المستخدم
        }
      })
    );
    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getAnalyticAccounts().subscribe((r) => {
        if (r.success) {
          this.AnalyticAccounts = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getProductUnits().subscribe((r) => {
        if (r.success) {
          this.pUnits = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['Product', 'ItemCode'];
    this.isGridBoxOpened = false;

    this.getFilteredunits = this.getFilteredunits.bind(this);
  }

  onAddButtonClick = ({ row }: DxDataGridTypes.ColumnButtonClickEvent) => {
    if (!row) {
      return;
    }
    const key = this.maxId++;
    this.changes = [
      {
        key,
        type: 'insert',
        insertAfterKey: row.key,
        data: { OrderID: key },
      },
    ];
    this.editRowKey = key;
    this.cdk.detectChanges();
  };
  onFinaTypeSelect(selectedItem: any) {
    this.FinancialEntities = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    // this.financial_entity_TypeId= selectedItem.id;

    this.subscription.add(
      this.service
        .getFinancialEntities(this.financial_entity_TypeId)
        .subscribe((r) => {
          if (r.success) {
            this.FinancialEntities = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  onAccountTypeSelect(selectedItem: any) {
    this.ThirdPartyAccounts = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    const Typeid = selectedItem.typId;

    this.subscription.add(
      this.service.getThirdPartyAccountId(Typeid).subscribe((r) => {
        if (r.success) {
          this.ThirdPartyAccounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  isAddButtonVisible({
    row,
  }: FirstArgument<DxDataGridTypes.ColumnButton['visible']>) {
    // return !row.isEditing;
    return row ? !row.isEditing : false;
  }
  async onRowInserted(e: DxDataGridTypes.RowInsertedEvent) {
    this.onDataUpdate.emit(this.data);
    await e.component.navigateToRow(e.key);
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onGridBoxOptionChanged(e: any) {
    if (e.name === 'value') {
      this.isGridBoxOpened = false;
      this.cdk.detectChanges();
    }
  }
  gridBox_displayExpr(item: any) {
    // alert(e);
    //  const item = this.gridBoxValue[0];
    return item ? item && `${item.CompanyName} <${item.ID}>` : undefined;
  }

  getFilteredunits(options: { data: UOM }) {
    return {
      store: this.pUnits,
      // filter: options.data ? ['id', '=', 1] : null,
      // filter: options.data ? ['id', '=', options.data.Id] : null,
      filter: options.data
        ? [
            ['id', '=', defualtUnit],
            'or',
            ['id', '=', medumUnit],
            'or',
            ['id', '=', smallUnit],
          ]
        : null,
    };
  }

  onEditorPreparing = (e: any) => {
    if (e.parentType === 'dataRow' && e.dataField === 'Item_Id') {
      if (!e.row.data) {
        e.row.data = {};
      }
      const itemId = e.row.data.Item_Id;
      const p = this.Product.find((p) => p.Item_Id == itemId);
      if (p) {
        defualtUnit = p.unitID;
        smallUnit = p.SmallUnitid;
        medumUnit = p.MedUnitid;
      }

      // if (p.UnitName) {
      //   this.pUnits.push({ NameAr: p.UnitName, Id: p.unitID,NameEn :p.UnitName });
      // }
      // if (p.SmallUnitName) {
      //   this.pUnits.push({ NameAr: p.SmallUnitName, Id: p.SmallUnitid ,NameEn :p.UnitName});
      // }
      // if (p.MedunitName) {
      //   this. pUnits.push({ NameAr: p.MedunitName, Id: p.MedUnitid ,NameEn :p.UnitName});
      // }
      // e.lookup.dataSource =  this.pUnits;
      this.cdk.detectChanges();
      //  e.editorOptions.disabled = e.row.data.Item_Id === undefined;

      //e.editorOptions.onValueChanged = (ev: any) => {
      //  let selectedItem = ev.component.option("selectedItem");
      //  e.setValue(selectedItem);
    }
    // }
  };
  onRowRemoved($event: any) {
    this.onDataUpdate.emit(this.data);
  }

  initRow(e: InitNewRowEvent) {
    e.data.OrderID = this.maxId++;
  }

  setProductValue(
    this: DxDataGridTypes.Column,
    newData: any,
    value: number,
    currentRowData: any
  ) {
    const p = Product.find((item) => item.Item_Id === value);
    defualtUnit = p.unitID;
    smallUnit = p.SmallUnitid;
    medumUnit = p.MedUnitid;
    newData.ItemCode = p?.Barcode;
    newData.unit_Id = null;
    this.defaultSetCellValue?.(newData, value, currentRowData);
  }

  setPrice(
    this: DxDataGridTypes.Column,
    newData: any,
    value: number,
    currentRowData: any
  ) {
    setSubTotal(this, newData, value, { ...currentRowData, Price: value });
  }

  setQuantity(
    this: DxDataGridTypes.Column,
    newData: any,
    value: number,
    currentRowData: any
  ) {
    setSubTotal(this, newData, value, { ...currentRowData, Quantity: value });
  }

  setDiscount(
    this: DxDataGridTypes.Column,
    newData: any,
    value: number,
    currentRowData: any
  ) {
    setSubTotal(this, newData, value, { ...currentRowData, Discount: value });
  }
}
platformBrowserDynamic().bootstrapModule(AppModule);

function setSubTotal(
  self: DxDataGridTypes.Column,
  newData: any,
  value: number,
  currentRowData: any
) {
  if (currentRowData.Quantity && currentRowData.Price) {
    newData.Subtotal = currentRowData.Quantity * currentRowData.Price;
    if (currentRowData.Discount > 0) {
      newData.Subtotal -= (currentRowData.Discount * newData.Subtotal) / 100;
    }
  } else {
    newData.Subtotal = 0;
  }
  self.defaultSetCellValue?.(newData, value, currentRowData);
}
