import {Component, OnInit,} from '@angular/core';
import notify from 'devextreme/ui/notify';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {GeneralService} from "../general.service";

@Component({
  selector: 'app-myprofile',
  templateUrl: './myprofile.component.html',
  styleUrl: './myprofile.component.scss',
  standalone: false
})
export class MyprofileComponent implements OnInit {
  profileForm: FormGroup;
  email: string;

  constructor(private generalService: GeneralService) {
    this.profileForm = new FormGroup<any>({
      firstName: new FormControl('', [Validators.required]),
      lastName: new FormControl('', [Validators.required]),
      phoneNumber: new FormControl('', [Validators.required])
    });
  }

  save() {
    if (this.profileForm.valid) {
      this.generalService.saveAccountInfo({...this.profileForm.value, email: this.email}).subscribe(data => {
        notify({message: 'Data saved', position: {at: 'bottom center', my: 'bottom center'}}, 'success');
      });
    }
  }

  ngOnInit(): void {
    this.generalService.getAccountInfo().subscribe(res => {
      this.email = res.data.email;
      this.profileForm.patchValue(res.data);
    })
  }

}
