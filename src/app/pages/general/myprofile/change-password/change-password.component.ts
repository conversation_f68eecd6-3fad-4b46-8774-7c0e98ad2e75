import {Component, ElementRef, ViewChild} from '@angular/core';
import {GeneralService} from "../../general.service";
import {FormControl, FormGroup, Validators} from "@angular/forms";

@Component({
  selector: 'change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
  standalone: false
})
export class ChangePasswordFormComponent {
  changePasswordForm: FormGroup;
  @ViewChild('close', { static: true }) closeBtn: ElementRef;

  constructor(private generalService: GeneralService) {
    this.changePasswordForm = new FormGroup<any>({
      currentPassword: new FormControl('', [Validators.required]),
      newPassword: new FormControl('', [Validators.required]),
      confirmNewPassword: new FormControl('', [Validators.required])
    });
  }


  save() {
    if(this.changePasswordForm.valid) {
      this.generalService.changePassword(this.changePasswordForm.value).subscribe(data=> {
        if(data.success) {
          this.changePasswordForm.reset();
          this.closeBtn.nativeElement.click();
        }
      });
    }
  }
}
