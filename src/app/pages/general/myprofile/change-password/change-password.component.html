<div class="modal" id="changePasswordModal" tabindex="-1">
  <form [formGroup]="changePasswordForm" class="form">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <b class="modal-title">Change Password</b>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group row mb-3">
              <label class="form-label">كلمة المرور الحالية</label>
              <input class="form-control" type="password" name="currentPassword" autocomplete="off"
                     formControlName="currentPassword" [ngClass]="{
        'is-invalid': changePasswordForm.controls['currentPassword'].invalid,
        'is-valid': changePasswordForm.controls['currentPassword'].valid
      }"/>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Password is required',
        control: changePasswordForm.controls['currentPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minlength',
        message: 'Password should have at least 3 symbols',
        control: changePasswordForm.controls['currentPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Password should have maximum 100 symbols',
        control: changePasswordForm.controls['currentPassword']
      }"></ng-container>
            </div>
            <div class="form-group row mb-3">
              <label class="form-label">كلمة المرور الجديدة</label>
              <input class="form-control" dir="ltr" type="password" name="newPassword" autocomplete="off"
                     formControlName="newPassword" [ngClass]="{
        'is-invalid': changePasswordForm.controls['newPassword'].invalid,
        'is-valid': changePasswordForm.controls['newPassword'].valid
      }"/>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Password is required',
        control: changePasswordForm.controls['newPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minlength',
        message: 'Password should have at least 3 symbols',
        control: changePasswordForm.controls['newPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Password should have maximum 100 symbols',
        control: changePasswordForm.controls['newPassword']
      }"></ng-container>
            </div>
            <div class="form-group row mb-3">
              <label class="form-label fw-bolder text-dark fs-6 mb-0">تأكيد كلمة المرور</label>
              <input class="form-control bg-transparent" dir="ltr" type="password" name="confirmNewPassword"
                     autocomplete="off"
                     formControlName="confirmNewPassword" [ngClass]="{
        'is-invalid': changePasswordForm.controls['confirmNewPassword'].invalid,
        'is-valid': changePasswordForm.controls['confirmNewPassword'].valid
      }"/>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Password is required',
        control: changePasswordForm.controls['confirmNewPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minlength',
        message: 'Password should have at least 3 symbols',
        control: changePasswordForm.controls['confirmNewPassword']
      }"></ng-container>
              <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Password should have maximum 100 symbols',
        control: changePasswordForm.controls['confirmNewPassword']
      }"></ng-container>
            </div>
        </div>
        <div class="modal-footer">

          <button type="submit" class="btn btn-primary" (click)="save()">
            Save
          </button>
          <button #close type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </form>
</div>
<ng-template #formError let-control="control" let-message="message" let-validation="validation">
  <ng-container *ngIf="control.hasError(validation) && (control.dirty || control.touched)">
    <div class="fv-plugins-message-container">
      <span role="alert">
        {{ message }}
      </span>
    </div>
  </ng-container>
</ng-template>
