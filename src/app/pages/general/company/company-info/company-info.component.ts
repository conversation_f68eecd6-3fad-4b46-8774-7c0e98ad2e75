import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON>ementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import { CompanyService } from './company.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-company-info',
  templateUrl: './company-info.component.html',
  styleUrls: ['./company-info.component.scss'],
  standalone: false,
})
export class CompanyInfoComponent implements OnInit, OnDestroy {
  companyForm: FormGroup;
  company: any;
  subscription = new Subscription();
  isLoading = false;
  branches: any[];
  jobs: any[];
  managers: any[];
  identityTypes: any[];
  countries: any[];
  companies: any[];
  DocumentTypeVersions: any[];
  selectedOption = 'developer';
  searchExpr: any;
  registrationtype: any[];
  subscriptions = new Subscription();
  activeTab: string = 'tab1';
  showOverlay = false;
  CompanyLogoPhoto: string | null = 'assets/media/avatars/background.jpg';
  AppBackgroundPhoto: string | null = 'assets/media/avatars/background.jpg';
  HeaderPhoto: string | null = 'assets/media/avatars/background.jpg';
  FooterPhoto: string | null = 'assets/media/avatars/background.jpg';

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  @ViewChild('fileInput1') fileInput1!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput2') fileInput2!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput3') fileInput3!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput4') fileInput4!: ElementRef<HTMLInputElement>;

  triggerFileInput1(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput1.nativeElement.click();
  }
  triggerFileInput2(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput2.nativeElement.click();
  }
  triggerFileInput3(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput3.nativeElement.click();
  }
  triggerFileInput4(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput4.nativeElement.click();
  }

  onCompanyLogoPhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.CompanyLogoPhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }
  onAppBackgroundPhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.AppBackgroundPhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }
  onHeaderPhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.HeaderPhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }
  onFooterPhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.FooterPhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }

  clearCompanyLogoPhoto(event: Event): void {
    event.stopPropagation();
    this.CompanyLogoPhoto = 'assets/media/avatars/background.jpg';
  }

  clearAppBackgroundPhoto(event: Event): void {
    event.stopPropagation();
    this.AppBackgroundPhoto = 'assets/media/avatars/background.jpg';
  }

  clearHeaderPhoto(event: Event): void {
    event.stopPropagation();
    this.HeaderPhoto = 'assets/media/avatars/background.jpg';
  }

  clearFooterPhoto(event: Event): void {
    event.stopPropagation();
    this.FooterPhoto = 'assets/media/avatars/background.jpg';
  }
  constructor(
    private service: CompanyService,
    private fb: FormBuilder,
    private translateService: TranslateService,
    private cdk: ChangeDetectorRef
  ) {
    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getCompanies().subscribe((r) => {
        if (r.success) {
          this.companies = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.managers = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getcountries().subscribe((r) => {
        if (r.success) {
          this.countries = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.searchExpr = ['id', 'name'];
    this.registrationtype = [
      {
        id: 'CRN',
        name: 'Commercial Registration Number',
      },
      {
        id: 'MOM',
        name: 'Momra License',
      },
      {
        id: 'MLS',
        name: 'MLSD License',
      },
      {
        id: 'SAG',
        name: 'Sagia License',
      },
      {
        id: 'OTH',
        name: 'Other ID',
      },
    ];
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    const today = new Date().toISOString().slice(0, 10);
    this.companyForm = this.fb.group({
      nameAr: [this.company?.nameAr, Validators.required],
      nameEn: [this.company?.nameEn, Validators.required],
      mobile: [this.company?.mobile, Validators.required],
      crn: [this.company?.crn, Validators.required],
      address: this.company?.address,
      email: [this.company?.email, Validators.email],
      header: this.company?.header,
      logo: this.company?.footer,
      footer: this.company?.footer,
      birthDate: [today],
      expiryDateOfZakatAndIncomeCertificate: [today],
      laborOfficeIssueDate: [today],
      LaborOfficeExpiryDate: [today],
      commercialRegistrationIssueDate: [today],
      commercialRegistrationExpiryDate: [today],
      classificationExpiryDate: [today],
      printDate: [today],
      expiredDate: [today],
      startedDate: [today],
      OptionControl: [this.selectedOption],
    });
  }

  save(form: any) {
    if (this.companyForm.valid) {
      if (!this.isLoading) {
        this.isLoading = true;
        const fd = new FormData();
        fd.append('nameAr', form.nameAr);
        fd.append('nameEn', form.nameEn);
        fd.append('crn', form.crn);
        fd.append('address', form.address);
        fd.append('mobile', form.mobile);
        fd.append('email', form.email);
        fd.append('logo', form.logo);
        fd.append('header', form.header);
        fd.append('footer', form.footer);
      }
    } else {
    }
  }

  loadInfo() {
    this.subscription.add(
      this.service.getInfo().subscribe((r) => {
        if (r.success) {
          this.company = r.data;
          this.initForm();
          this.cdk.detectChanges();
        }
      })
    );
  }

  upload(event: any, key: string) {
    this.companyForm.get(key)?.setValue(event.target.files[0]);
  }

  get isArabic() {
    return this.translateService.currentLang == 'ar';
  }
  exportToExcel() {
    this.subscriptions.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
