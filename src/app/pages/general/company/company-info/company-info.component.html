<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.CompanySetting" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="loadInfo()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.View" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save(companyForm.value)"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="companyForm"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="company" class="form-label">
                {{ "COMMON.Company" | translate }}
              </label>
              <ng-select
                id="company"
                formControlName="company"
                bindLabel="name"
                bindValue="id"
                [items]="companies"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="branch" class="form-label">
                {{ "COMMON.Branch" | translate }}
              </label>
              <ng-select
                id="branch"
                formControlName="branch"
                bindLabel="name"
                bindValue="id"
                [items]="branches"
              ></ng-select>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>

          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.Taxes" | translate }}
            </button>
          </li>

          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab3'"
              (click)="setActiveTab('tab3')"
            >
              {{ "COMMON.EgyptianElectronicInvoice" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab4'"
              (click)="setActiveTab('tab4')"
            >
              {{ "COMMON.SaudiEInvoice" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab5'"
              (click)="setActiveTab('tab5')"
            >
              {{ "COMMON.CompanyLogo" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab6'"
              (click)="setActiveTab('tab6')"
            >
              {{ "COMMON.Managers" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="country" class="form-label">
                  {{ "COMMON.Country" | translate }}
                </label>
                <ng-select
                  id="country"
                  formControlName="country"
                  bindLabel="countryName"
                  bindValue="id"
                  [items]="countries"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="generalManager" class="form-label">
                  {{ "COMMON.GeneralManager" | translate }}
                </label>
                <input
                  type="text"
                  name="generalManager"
                  id="generalManager"
                  class="form-control"
                  formControlName="generalManager"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="cardNumber" class="form-label">
                  {{ "COMMON.CardNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="cardNumber"
                  id="cardNumber"
                  class="form-control"
                  formControlName="cardNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.BirthDate" | translate }}
                </label>
                <input
                  id="birthDate"
                  type="date"
                  formControlName="birthDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="establishmentNumberInInterior" class="form-label">
                  {{ "COMMON.EstablishmentNumberInInterior" | translate }}
                </label>
                <input
                  type="text"
                  name="establishmentNumberInInterior"
                  id="establishmentNumberInInterior"
                  class="form-control"
                  formControlName="establishmentNumberInInterior"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="mailbox" class="form-label">
                  {{ "COMMON.Mailbox" | translate }}
                </label>
                <input
                  type="text"
                  name="mailbox"
                  id="mailbox"
                  class="form-control"
                  formControlName="mailbox"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="zipcode" class="form-label">
                  {{ "COMMON.Zipcode" | translate }}
                </label>
                <input
                  type="text"
                  id="zipcode"
                  name="zipcode"
                  class="form-control"
                  formControlName="zipcode"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="street" class="form-label">
                  {{ "COMMON.Street" | translate }}
                </label>
                <input
                  type="text"
                  name="street"
                  id="street"
                  class="form-control"
                  formControlName="street"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="phone" class="form-label">
                  {{ "COMMON.Phone" | translate }}
                </label>
                <input
                  type="text"
                  name="phone"
                  id="phone"
                  class="form-control"
                  formControlName="phone"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="fax" class="form-label">
                  {{ "COMMON.Fax" | translate }}
                </label>
                <input
                  type="text"
                  name="fax"
                  id="fax"
                  class="form-control"
                  formControlName="fax"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label
                  for="zakatAndIncomeCertificateUniqueNumber"
                  class="form-label"
                >
                  {{
                    "COMMON.ZakatAndIncomeCertificateUniqueNumber" | translate
                  }}
                </label>
                <input
                  type="text"
                  name="zakatAndIncomeCertificateUniqueNumber"
                  id="zakatAndIncomeCertificateUniqueNumber"
                  class="form-control"
                  formControlName="zakatAndIncomeCertificateUniqueNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="zakatAndIncomeCertificateNumber" class="form-label">
                  {{ "COMMON.ZakatAndIncomeCertificateNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="zakatAndIncomeCertificateNumber"
                  id="zakatAndIncomeCertificateNumber"
                  class="form-control"
                  formControlName="zakatAndIncomeCertificateNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{
                    "COMMON.ExpiryDateOfZakatAndIncomeCertificate" | translate
                  }}
                </label>
                <input
                  id="expiryDateOfZakatAndIncomeCertificate"
                  type="date"
                  formControlName="expiryDateOfZakatAndIncomeCertificate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="laborOfficeNumber" class="form-label">
                  {{ "COMMON.LaborOfficeNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="laborOfficeNumber"
                  id="laborOfficeNumber"
                  class="form-control"
                  formControlName="laborOfficeNumber"
                />
              </div>

              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.IssueDate" | translate }}
                </label>
                <input
                  id="laborOfficeIssueDate"
                  type="date"
                  formControlName="laborOfficeIssueDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="LaborOfficeExpiryDate"
                  type="date"
                  formControlName="LaborOfficeExpiryDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="commercialRegistrationNumber" class="form-label">
                  {{ "COMMON.CommercialRegistrationNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="commercialRegistrationNumber"
                  id="commercialRegistrationNumber"
                  class="form-control"
                  formControlName="commercialRegistrationNumber"
                />
              </div>

              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.IssueDate" | translate }}
                </label>
                <input
                  id="commercialRegistrationIssueDate"
                  type="date"
                  formControlName="commercialRegistrationIssueDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="commercialRegistrationExpiryDate"
                  type="date"
                  formControlName="commercialRegistrationExpiryDate"
                  class="form-control"
                />
              </div>
            </div>
          </div>
          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="salesTaxRegistrationNumber" class="form-label">
                  {{ "COMMON.SalesTaxRegistrationNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="salesTaxRegistrationNumber"
                  id="salesTaxRegistrationNumber"
                  class="form-control"
                  formControlName="salesTaxRegistrationNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="taxCardNumber" class="form-label">
                  {{ "COMMON.TaxCardNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="taxCardNumber"
                  id="taxCardNumber"
                  class="form-control"
                  formControlName="taxCardNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="taxFileNumber" class="form-label">
                  {{ "COMMON.TaxFileNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="taxFileNumber"
                  id="taxFileNumber"
                  class="form-control"
                  formControlName="taxFileNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="missions" class="form-label">
                  {{ "COMMON.Missions" | translate }}
                </label>
                <input
                  type="text"
                  name="missions"
                  id="missions"
                  class="form-control"
                  formControlName="missions"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="buildingNumber" class="form-label">
                  {{ "COMMON.BuildingNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="buildingNumber"
                  id="buildingNumber"
                  class="form-control"
                  formControlName="buildingNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="floorNumber" class="form-label">
                  {{ "CUSTOMER.FLOOR_NUMBER" | translate }}
                </label>
                <input
                  type="text"
                  name="floorNumber"
                  id="floorNumber"
                  class="form-control"
                  formControlName="floorNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="roomNumber" class="form-label">
                  {{ "COMMON.RoomNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="roomNumber"
                  id="roomNumber"
                  class="form-control"
                  formControlName="roomNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="city" class="form-label">
                  {{ "COMMON.City" | translate }}
                </label>
                <input
                  type="text"
                  name="city"
                  id="city"
                  class="form-control"
                  formControlName="city"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="location" class="form-label">
                  {{ "COMMON.Location" | translate }}
                </label>
                <input
                  type="text"
                  name="location"
                  id="location"
                  class="form-control"
                  formControlName="location"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="insuranceSubscriptionNumber" class="form-label">
                  {{ "COMMON.InsuranceSubscriptionNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="insuranceSubscriptionNumber"
                  id="insuranceSubscriptionNumber"
                  class="form-control"
                  formControlName="insuranceSubscriptionNumber"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="governorate" class="form-label">
                  {{ "COMMON.Governorate" | translate }}
                </label>
                <input
                  type="text"
                  name="governorate"
                  id="governorate"
                  class="form-control"
                  formControlName="governorate"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="tombstone1forAddress" class="form-label">
                  {{ "COMMON.Tombstone1forAddress" | translate }}
                </label>
                <input
                  type="text"
                  name="tombstone1forAddress"
                  id="tombstone1forAddress"
                  class="form-control"
                  formControlName="tombstone1forAddress"
                />
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <label for="tombstone2forAddress" class="form-label">
                  {{ "COMMON.Tombstone2forAddress" | translate }}
                </label>
                <input
                  type="text"
                  name="tombstone2forAddress"
                  id="tombstone2forAddress"
                  class="form-control"
                  formControlName="tombstone2forAddress"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="email" class="form-label">
                  {{ "COMMON.Email" | translate }}
                </label>
                <input
                  type="text"
                  name="email"
                  id="email"
                  class="form-control"
                  formControlName="email"
                />
              </div>

              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="classificationCertificate" class="form-label">
                  {{ "COMMON.ClassificationCertificate" | translate }}
                </label>
                <input
                  type="text"
                  name="classificationCertificate"
                  id="classificationCertificate"
                  class="form-control"
                  formControlName="classificationCertificate"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="classificationExpiryDate" class="form-label">
                  {{ "COMMON.ClassificationExpiryDate" | translate }}
                </label>
                <input
                  type="date"
                  name="classificationExpiryDate"
                  id="classificationExpiryDate"
                  class="form-control"
                  formControlName="classificationExpiryDate"
                />
              </div>
            </div>
          </div>
          <!-- Tab 3 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="eta_code" class="form-label"> eta_code </label>
                <input
                  type="text"
                  name="eta_code"
                  id="eta_code"
                  class="form-control"
                  formControlName="eta_code"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="eta_client_id" class="form-label">
                  eta_client_id
                </label>
                <input
                  type="text"
                  name="eta_client_id"
                  id="eta_client_id"
                  class="form-control"
                  formControlName="eta_client_id"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="eta_client_secret_1" class="form-label">
                  eta_client_secret_1
                </label>
                <input
                  type="text"
                  name="eta_client_secret_1"
                  id="eta_client_secret_1"
                  class="form-control"
                  formControlName="eta_client_secret_1"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="eta_client_secret_2" class="form-label">
                  eta_client_secret_2
                </label>
                <input
                  type="text"
                  name="eta_client_secret_2"
                  id="eta_client_secret_2"
                  class="form-control"
                  formControlName="eta_client_secret_2"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="FalconSignerKey" class="form-label">
                  Falcon Signer Key
                </label>
                <input
                  type="text"
                  name="FalconSignerKey"
                  id="FalconSignerKey"
                  class="form-control"
                  formControlName="FalconSignerKey"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="FalconSignerIp" class="form-label">
                  Falcon Signer IP
                </label>
                <input
                  type="text"
                  name="FalconSignerIp"
                  id="FalconSignerIp"
                  class="form-control"
                  formControlName="FalconSignerIp"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="UserName" class="form-label"> User Name </label>
                <input
                  type="text"
                  name="UserName"
                  id="UserName"
                  class="form-control"
                  formControlName="UserName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="Secret" class="form-label"> Secret </label>
                <input
                  type="text"
                  name="Secret"
                  id="Secret"
                  class="form-control"
                  formControlName="Secret"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="DocumentTypeVersion" class="form-label">
                  Document Type Version
                </label>
                <ng-select
                  id="DocumentTypeVersion"
                  formControlName="DocumentTypeVersion"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="DocumentTypeVersions"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="EStampPIN" class="form-label"> E Stamp PIN </label>
                <input
                  type="text"
                  name="EStampPIN"
                  id="EStampPIN"
                  class="form-control"
                  formControlName="EStampPIN"
                />
              </div>
            </div>
          </div>
          <!-- Tab 4 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab4'"
            [class.active]="activeTab === 'tab4'"
            *ngIf="activeTab === 'tab4'"
          >
            <div class="row mt-3">
              <div class="form-group col-xl-8 col-md-8 col-sm-12 mb-2">
                <label class="mx-10">
                  Developer
                  <input
                    type="radio"
                    formControlName="OptionControl"
                    value="developer"
                  />
                </label>
                <label class="mx-10">
                  Pre Production
                  <input
                    type="radio"
                    formControlName="OptionControl"
                    value="preProduction"
                  />
                </label>
                <label class="mx-10">
                  Production
                  <input
                    type="radio"
                    formControlName="OptionControl"
                    value="production"
                  />
                </label>
              </div>
            </div>

            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="identityType" class="form-label">
                  Identity Type
                </label>
                <ng-select
                  id="identityType"
                  formControlName="identityType"
                  bindLabel="identityType"
                  bindValue="id"
                  [items]="identityTypes"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="identityNo" class="form-label"> Identity No </label>
                <input
                  type="text"
                  name="identityNo"
                  id="identityNo"
                  class="form-control"
                  formControlName="identityNo"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="OTTP" class="form-label"> OTTP </label>
                <input
                  type="text"
                  name="OTTP"
                  id="OTTP"
                  class="form-control"
                  formControlName="OTTP"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="privateKey" class="form-label"> Private Key </label>
                <textarea
                  id="privateKey"
                  formControlName="privateKey"
                  class="form-control"
                  style="background-color: inherit"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="CSR" class="form-label"> CSR </label>
                <textarea
                  id="CSR"
                  formControlName="CSR"
                  class="form-control"
                  style="background-color: inherit"
                  rows="3"
                ></textarea>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="CSID" class="form-label"> CSID </label>
                <textarea
                  id="CSID"
                  formControlName="CSID"
                  class="form-control"
                  style="background-color: inherit"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="Secret" class="form-label"> Secret </label>
                <input
                  type="text"
                  name="Secret"
                  id="Secret"
                  class="form-control"
                  formControlName="Secret"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="privateKey" class="form-label"> Private Key </label>
                <textarea
                  id="privateKey"
                  formControlName="privateKey"
                  class="form-control"
                  style="background-color: inherit"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <div class="row mb-2">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label"> Started Date </label>
                <input
                  id="startedDate"
                  type="date"
                  formControlName="startedDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label"> Expired Date </label>
                <input
                  id="expiredDate"
                  type="date"
                  formControlName="expiredDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="PCName" class="form-label"> PC Name </label>
                <input
                  type="text"
                  name="PCName"
                  id="PCName"
                  class="form-control"
                  formControlName="PCName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <button
                  routerLink=""
                  class="btn btn-active-light-primary border border-gray-300"
                >
                  Onboarding CSID
                </button>
              </div>
            </div>
          </div>
          <!-- Tab 5 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab5'"
            [class.active]="activeTab === 'tab5'"
            *ngIf="activeTab === 'tab5'"
          >
            <div class="row mt-2">
              <!-- photo 1  -->
              <div class="col-md-6 col-sm-12 mb-3">
                <label class="form-label fs-5 fw-bold mb-2">{{
                  "COMMON.CompanyLogo" | translate
                }}</label>

                <div
                  class="photo-container position-relative d-flex flex-column align-items-center"
                  (mouseenter)="showOverlay = true"
                  (mouseleave)="showOverlay = false"
                >
                  <img
                    [src]="
                      CompanyLogoPhoto || 'assets/media/avatars/background.jpg'
                    "
                    alt="New Employee"
                    class="photo img-fluid rounded mb-2"
                  />
                  <div
                    class="overlay d-flex flex-column justify-content-end align-items-center"
                    *ngIf="showOverlay"
                  >
                    <div
                      class="button-group d-flex justify-content-between gap-2"
                    >
                      <button
                        class="btn btn-primary btn-sm"
                        (click)="triggerFileInput1($event)"
                      >
                        <i class="fa fa-edit fs-2"></i>
                      </button>
                      <button
                        class="btn btn-danger btn-sm"
                        (click)="clearCompanyLogoPhoto($event)"
                      >
                        <i class="fa fa-trash-o fs-2"></i>
                      </button>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    class="d-none"
                    #fileInput1
                    (change)="onCompanyLogoPhotoSelected($event)"
                  />
                </div>
              </div>
              <!-- End Photo 1  -->

              <!-- photo 2 -->
              <div class="col-md-6 col-sm-12 mb-3">
                <label class="form-label fs-5 fw-bold mb-2">{{
                  "COMMON.AppBackground" | translate
                }}</label>

                <div
                  class="photo-container position-relative d-flex flex-column align-items-center"
                  (mouseenter)="showOverlay = true"
                  (mouseleave)="showOverlay = false"
                >
                  <img
                    [src]="
                      AppBackgroundPhoto ||
                      'assets/media/avatars/background.jpg'
                    "
                    alt="New Employee"
                    class="photo img-fluid rounded mb-2"
                  />
                  <div
                    class="overlay d-flex flex-column justify-content-end align-items-center"
                    *ngIf="showOverlay"
                  >
                    <div
                      class="button-group d-flex justify-content-between gap-2"
                    >
                      <button
                        class="btn btn-primary btn-sm"
                        (click)="triggerFileInput2($event)"
                      >
                        <i class="fa fa-edit fs-2"></i>
                      </button>
                      <button
                        class="btn btn-danger btn-sm"
                        (click)="clearAppBackgroundPhoto($event)"
                      >
                        <i class="fa fa-trash-o fs-2"></i>
                      </button>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    class="d-none"
                    #fileInput2
                    (change)="onAppBackgroundPhotoSelected($event)"
                  />
                </div>
              </div>
              <!-- End Photo 2 -->
            </div>
            <div class="row mt-2">
              <!--  Photo 3 -->
              <div class="col-lg-12 col-md-12 col-sm-12 mb-3">
                <label class="form-label fs-5 fw-bold mb-2">{{
                  "COMMON.Header" | translate
                }}</label>

                <div
                  class="photo-containerwidth position-relative d-flex flex-column align-items-center"
                  (mouseenter)="showOverlay = true"
                  (mouseleave)="showOverlay = false"
                >
                  <img
                    [src]="HeaderPhoto || 'assets/media/avatars/background.jpg'"
                    alt="New Employee"
                    class="photo img-fluid rounded mb-2"
                  />
                  <div
                    class="overlay d-flex flex-column justify-content-end align-items-center"
                    *ngIf="showOverlay"
                  >
                    <div
                      class="button-group d-flex justify-content-between gap-2"
                    >
                      <button
                        class="btn btn-primary btn-sm"
                        (click)="triggerFileInput3($event)"
                      >
                        <i class="fa fa-edit fs-2"></i>
                      </button>
                      <button
                        class="btn btn-danger btn-sm"
                        (click)="clearHeaderPhoto($event)"
                      >
                        <i class="fa fa-trash-o fs-2"></i>
                      </button>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    class="d-none"
                    #fileInput3
                    (change)="onHeaderPhotoSelected($event)"
                  />
                </div>
              </div>
              <!-- End Photo 3 -->
            </div>
            <div class="row mt-2">
              <!--  Photo 4 -->
              <div class="col-lg-12 col-md-12 col-sm-12 mb-3">
                <label class="form-label fs-5 fw-bold mb-2">{{
                  "COMMON.Footer" | translate
                }}</label>

                <div
                  class="photo-containerwidth position-relative d-flex flex-column align-items-center"
                  (mouseenter)="showOverlay = true"
                  (mouseleave)="showOverlay = false"
                >
                  <img
                    [src]="FooterPhoto || 'assets/media/avatars/background.jpg'"
                    alt="New Employee"
                    class="photo img-fluid rounded mb-2"
                  />
                  <div
                    class="overlay d-flex flex-column justify-content-end align-items-center"
                    *ngIf="showOverlay"
                  >
                    <div
                      class="button-group d-flex justify-content-between gap-2"
                    >
                      <button
                        class="btn btn-primary btn-sm"
                        (click)="triggerFileInput4($event)"
                      >
                        <i class="fa fa-edit fs-2"></i>
                      </button>
                      <button
                        class="btn btn-danger btn-sm"
                        (click)="clearFooterPhoto($event)"
                      >
                        <i class="fa fa-trash-o fs-2"></i>
                      </button>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    class="d-none"
                    #fileInput4
                    (change)="onFooterPhotoSelected($event)"
                  />
                </div>
              </div>
              <!-- End Photo 4 -->
            </div>
          </div>

          <!-- Tab 6 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab6'"
            [class.active]="activeTab === 'tab6'"
            *ngIf="activeTab === 'tab6'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="job" class="form-label">
                  {{ "COMMON.Job" | translate }}
                </label>
                <ng-select
                  id="job"
                  formControlName="job"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="jobs"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="manager" class="form-label">
                  {{ "COMMON.Manager" | translate }}
                </label>
                <ng-select
                  id="manager"
                  formControlName="manager"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="managers"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="nameInPrint" class="form-label">
                  {{ "COMMON.NameInPrint" | translate }}
                </label>
                <input
                  type="text"
                  name="nameInPrint"
                  id="nameInPrint"
                  class="form-control"
                  formControlName="nameInPrint"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for=" idNumber" class="form-label">
                  {{ "CUSTOMER.ID_NUMBER" | translate }}
                </label>
                <input
                  type="text"
                  name="idNumber"
                  id="idNumber"
                  class="form-control"
                  formControlName="idNumber"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
