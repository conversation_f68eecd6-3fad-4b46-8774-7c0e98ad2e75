import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CompanyInfoComponent } from './company-info/company-info.component';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  DxDataGridModule,
  DxListModule,
  DxDropDownBoxModule,
  DxTagBoxModule,
  DxSelectBoxModule,
  DxCheckBoxModule,
  DxTextBoxModule,
  DxNumberBoxModule,
  DxSwitchModule,
  DxDateBoxModule,
  DxButtonModule,
  DxDropDownButtonModule,
  DxTabsModule,
} from 'devextreme-angular';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { CompanyListComponent } from './company-list/company-list.component';
import { WidgetsModule } from '../../../_metronic/partials/content/widgets/widgets.module';
import { PermissionsModule } from '../../../directives/permissions/permissions.module';
const routes: Routes = [
  {
    path: '',
    component: CompanyInfoComponent,
  },
];
@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    TranslateModule,
    ReactiveFormsModule,
    WidgetsModule,
    NgbPaginationModule,
    PermissionsModule,
    DxDataGridModule,
    DxListModule,
    DxDropDownBoxModule,
    DxTagBoxModule,
    DxSelectBoxModule,
    DxCheckBoxModule,
    DxTextBoxModule,
    DxNumberBoxModule,
    DxSwitchModule,
    DxDateBoxModule,
    DxTagBoxModule,
    DxButtonModule,
    DxDropDownButtonModule,
    DxTabsModule,
  ],
  declarations: [CompanyListComponent],
})
export class CompanyModule {}
