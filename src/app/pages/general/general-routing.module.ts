import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BranchListComponent } from './branches/branch-list/branch-list.component';
import { CompanyInfoComponent } from './company/company-info/company-info.component';
import { CompanyListComponent } from './company/company-list/company-list.component';
import { CurrenciesComponent } from './currencies/currencies.component';
import { generalHomeComponent } from './general-home/general-home.component';
import { CityesComponent } from './cityes/cityes.component';
import { NationalityComponent } from './nationality/nationality.component';
import { SettingsComponent } from '../settings/settings.component';
import { UsersessionsComponent } from './usersessions/usersessions.component';
import { MyprofileComponent } from './myprofile/myprofile.component';



export const routes: Routes = [
  {
    path: 'countries',
    loadChildren: () => import('./countries/country.module').then(u => u.CountryModule),
  },
  {
    path: '',
    component: generalHomeComponent
  },
  {
    path: 'company_list',
    component: CompanyListComponent
  },
  {
    path: 'Cityes',
    component: CityesComponent
  },
   {
    path: 'currencies',
    component: CurrenciesComponent
  },
  {
    path: 'branch_list',
    component: BranchListComponent
  },
  {
    path: 'company_info',
    component: CompanyInfoComponent
  },
  {
    path: 'nationality',
    component: NationalityComponent
  },
  {
    path: 'settings',
    component: SettingsComponent
  },
  {
    path: 'sessions',
    component: UsersessionsComponent
  },
 {
    path: 'myprofile',
    component: MyprofileComponent
  },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class generalRoutingModule {
}
