import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { generalHomeComponent } from './general-home/general-home.component';
import { generalRoutingModule } from './general-routing.module';
import { BranchListComponent } from './branches/branch-list/branch-list.component';
import { BranchComponent } from './branches/branch/branch.component';
import { CurrenciesComponent } from './currencies/currencies.component';
import { CompanyInfoComponent } from './company/company-info/company-info.component';
import { CompanyListComponent } from './company/company-list/company-list.component';
import { SharedModule } from '../shared/shared.module';
import { CityesComponent } from './cityes/cityes.component';
import { NationalityComponent } from './nationality/nationality.component';
import { MyprofileComponent } from './myprofile/myprofile.component';
import { ChangePasswordFormComponent } from './myprofile/change-password/change-password.component';

@NgModule({
  declarations: [
    generalHomeComponent,
    BranchListComponent,
    BranchComponent,
    CurrenciesComponent,
    CompanyInfoComponent,
    CompanyListComponent,
    CityesComponent,
    NationalityComponent,
    MyprofileComponent,
    ChangePasswordFormComponent,
  ],
  imports: [generalRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class GeneralModule {}
