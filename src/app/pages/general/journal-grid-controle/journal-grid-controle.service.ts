import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { createStore } from 'devextreme-aspnet-data-nojquery';

const url = 'https://js.devexpress.com/Demos/Mvc/api/DataGridWebApi';

const dataSource = createStore({
  key: 'OrderID',
  loadUrl: `${url}/Orders`,
  insertUrl: `${url}/InsertOrder`,
  updateUrl: `${url}/UpdateOrder`,
  deleteUrl: `${url}/DeleteOrder`,
  onBeforeSend: (method, ajaxOptions) => {
    ajaxOptions.xhrFields = { withCredentials: true };
  },
});

@Injectable()
export class Service {
  getDataSource() {
    return dataSource;
  }
}
export class Status {
  id: number;
  name: string;
}

export class UOM {
  Id: number;
  NameAr: string;
  NameEn: string;
}

const statuses: Status[] = [
  {
    id: 1,
    name: 'Not Started',
  },
  {
    id: 2,
    name: 'In Progress',
  },
  {
    id: 3,
    name: 'Deferred',
  },
  {
    id: 4,
    name: 'Need Assistance',
  },
  {
    id: 5,
    name: 'Completed',
  },
];
@Injectable({
  providedIn: 'root',
})
export class JournalGridControleService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query,
      },
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }

  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }

  getAnalyticAccounts(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts');
  }
  getAccountThirdParty(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty');
  }
  getThirdPartyAccountId(typId: any): Observable<any> {
    if (typId === 0) {
      return this.http.get<any>('api/ChartofAccounts/ChartofAccountsdropdown');
    } else if (typId === 2) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (typId === 3) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (typId === 4) {
      return this.http.get<any>('api/employees/EmployeesDropdown');
    } else if (typId === 5) {
      return this.http.get<any>('api/BankAccount');
    } else if (typId === 6) {
      return this.http.get<any>('api/CashBox');
    } else {
      throw new Error('Invalid entityId');
    }
  }

  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }
  getFinancialEntities(entityId: any): Observable<any> {
    if (entityId === 1) {
      return this.http.get<any>('api/Tenders/TendersDropdown');
    } else if (entityId === 2) {
      return this.http.get<any>('api/Machine/dropdown');
    } else if (entityId === 3) {
      return this.http.get<any>('api/Assets/dropdown');
    } else if (entityId === 4) {
      return this.http.get<any>('api/Departments');
    } else if (entityId === 5) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (entityId === 6) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (entityId === 7) {
      return this.http.get<any>('api/Cars/dropdown');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }

  getProductUnits(): Observable<any> {
    return this.http.get<any>('api/Units');
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  getStatuses() {
    return statuses;
  }
}
