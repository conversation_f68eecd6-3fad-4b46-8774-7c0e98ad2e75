import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { createStore } from 'devextreme-aspnet-data-nojquery';

const url = 'https://js.devexpress.com/Demos/Mvc/api/DataGridWebApi';

const dataSource = createStore({
  key: 'OrderID',
  loadUrl: `${url}/Orders`,
  insertUrl: `${url}/InsertOrder`,
  updateUrl: `${url}/UpdateOrder`,
  deleteUrl: `${url}/DeleteOrder`,
  onBeforeSend: (method, ajaxOptions) => {
    ajaxOptions.xhrFields = { withCredentials: true };
  },
});

@Injectable()
export class Service {
  getDataSource() {
    return dataSource;
  }
}
export class Status {
  id: number;
  name: string;
}

export class UOM {
  Id: number;
  NameAr: string;
  NameEn: string;
}

const statuses: Status[] = [
  {
    id: 1,
    name: 'Not Started',
  },
  {
    id: 2,
    name: 'In Progress',
  },
  {
    id: 3,
    name: 'Deferred',
  },
  {
    id: 4,
    name: 'Need Assistance',
  },
  {
    id: 5,
    name: 'Completed',
  },
];

@Injectable({
  providedIn: 'root',
})
export class ProductsGridControleService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query,
      },
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }

  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }

  getAnalyticAccounts(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccountList');
  }

  getProductUnits(): Observable<any> {
    return this.http.get<any>('api/Units/UnitList');
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  getStatuses() {
    return statuses;
  }
}
