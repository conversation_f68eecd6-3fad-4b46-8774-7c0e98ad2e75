import {ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import { ProductsGridControleService ,UOM} from './products-grid-controle.service';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import Guid from 'devextreme/core/guid';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from 'src/app/app.module';
import {InitNewRowEvent} from "devextreme/ui/data_grid";
import { TranslationService } from 'src/app/modules/i18n/translation.service';

let Product: any[];
let defualtUnit: number = 0;
let medumUnit: number = 0;
let smallUnit: number = 0;

type FirstArgument<T> = T extends (...args: any) => any ? Parameters<T>[0]: never;

@Component({
    selector: 'app-products-grid-controle',
    templateUrl: './products-grid-controle.component.html',
    styleUrls: ['./products-grid-controle.component.scss'],
    standalone: false
})
export class ProductsGridControleComponent implements OnDestroy, OnInit {
  @Output()
  onDataUpdate = new EventEmitter<any>();

  @Input()
  data: any[] = [];

  moduleName = 'inventory.warehouses';

  Product: any[];
  AnalyticAccounts: any[];

  newRowPosition: DxDataGridTypes.NewRowPosition = 'pageBottom';

  scrollingMode: DxDataGridTypes.DataGridScrollMode = 'standard';
  pUnits: UOM[];
  changes : any = [];
  editRowKey : any = null;
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  searchExpr: any;
  subscription = new Subscription();
  maxId = 0;
  isRtlEnabled: boolean;


  // gridColumns = ['ID', 'CompanyName', 'type'];
  constructor(
    private service: ProductsGridControleService, 
    private cdk: ChangeDetectorRef,
    private translationService: TranslationService
  ) {
    // تحديد اتجاه الداتا جريد بناءً على اللغة الحالية
    const currentLang = this.translationService.getSelectedLanguage();
    this.isRtlEnabled = currentLang === 'ar';
    
    service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
    this.subscription.add(service.list().subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.maxId = this.data.length + 1;
        this.onDataUpdate.emit(this.data);
        this.cdk.detectChanges();
      }

    }));


    this.subscription.add(service.getProductList().subscribe(r => {
      if (r.success) {
        this.Product = r.data.slice(0, 1000);
        Product = this.Product;
        this.cdk.detectChanges();

      }
    }));

    this.subscription.add(service.getAnalyticAccounts().subscribe(r => {
      if (r.success) {
        this.AnalyticAccounts = r.data;

        this.cdk.detectChanges();

      }
    }));

    this.subscription.add(service.getProductUnits().subscribe(r => {
      if (r.success) {
        this.pUnits = r.data;

        this.cdk.detectChanges();

      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['Product', 'productId'];
    this.isGridBoxOpened = false;

    this.getFilteredunits = this.getFilteredunits.bind(this);
  }

  ngOnInit() {
    // Process the initial data if exists
    if (this.data && this.data.length > 0) {
      this.maxId = Math.max(...this.data.map(item => item.lineNo || 0)) + 1;
      
      // Ensure product names and other display fields are correctly set
      this.data.forEach(item => {
        if (item.productId && !item.product && item.ProductName) {
          item.product = item.ProductName;
        }
      });
      
      // Emit updated data
      this.onDataUpdate.emit(this.data);
    }
  }

  onAddButtonClick = ({row}: DxDataGridTypes.ColumnButtonClickEvent) => {

    if (!row) {

      return;
    }
    const key =  this.maxId++;
    this.changes = [{
      key,
      type: 'insert',
      insertAfterKey: row.key,
      data: {lineNo: key}
    }];
    this.editRowKey = key;
    this.cdk.detectChanges();

  };

  isAddButtonVisible({ row }: FirstArgument<DxDataGridTypes.ColumnButton['visible']>) {
    // return !row.isEditing;
    return row ? !row.isEditing : false;
  }
  async onRowInserted(e: DxDataGridTypes.RowInsertedEvent) {
    this.onDataUpdate.emit(this.data);
    await e.component.navigateToRow(e.key);
  }

  onItemClick(e: any) {

  }


  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }




  onGridBoxOptionChanged(e: any) {
    if (e.name === 'value') {
      this.isGridBoxOpened = false;
      this.cdk.detectChanges();
    }
  }
  gridBox_displayExpr(item: any) {
    // alert(e);
    //  const item = this.gridBoxValue[0];
    return item ? (item && `${item.CompanyName} <${item.ID}>`) : undefined;
  }

  getFilteredunits(options: { data: any }) {

    // Don't filter units if no product is selected
    if (!options.data || !options.data.productId) {
      return {
        store: this.pUnits,
      };
    }

    // If a product is selected, show units relevant to that product
    if (defualtUnit || medumUnit || smallUnit) {
      return {
        store: this.pUnits,
        filter: [['unitId', '=', defualtUnit], 'or', ['unitId', '=', medumUnit], 'or', ['unitId', '=', smallUnit]],
      };
    }

    // Fallback to show all units
    return {
      store: this.pUnits,
    };
  }

  onEditorPreparing = (e: any) => {
   if (e.parentType === 'dataRow' && e.dataField === 'productId') {

     if(!e.row.data) {
         e.row.data = {};
     }
      const itemId = e.row.data.productId;
      const p = this.Product.find(p => p.productId == itemId);
      if(p) {
          defualtUnit = p.unitID;
          smallUnit = p.SmallUnitid;
          medumUnit = p.MedUnitid;
          
          // Ensure all product data is fully populated in the row
          if (e.row.data) {
            e.row.data.product = p.productName;
            e.row.data.ProductName = p.productName;
            e.row.data.unitID = p.unitID; 
          }
      }

      this.cdk.detectChanges();
      };
   // }
  }
  onRowRemoved($event: any) {
    this.onDataUpdate.emit(this.data);
  }

    initRow(e: InitNewRowEvent) {
        e.data.lineNo = this.maxId++;
        
        // If data was loaded from existing records, ensure display properties are set
        if (this.data && this.data.length > 0) {
            this.data.forEach(item => {
                // Ensure product display name is set
                if (item.productId && !item.product && item.ProductName) {
                    item.product = item.productName;
                }
            });
        }
    }

    setProductValue(this: DxDataGridTypes.Column, newData: any, value: number, currentRowData: any) {
        const p = Product.find(item => item.productId === value);
        if (p) {
            defualtUnit = p.unitID;
            smallUnit = p.SmallUnitid;
            medumUnit = p.MedUnitid;
            newData.product = p?.productName; 
            newData.ProductName = p?.productName; 
            newData.unitID = p?.unitID; 
        }
        this.defaultSetCellValue?.(newData, value, currentRowData);
    }

    setPrice(this: DxDataGridTypes.Column, newData: any, value: number, currentRowData: any) {
        setSubTotal(this, newData, value, {...currentRowData, price: value});
    }

    setQuantity(this: DxDataGridTypes.Column, newData: any, value: number, currentRowData: any) {
        setSubTotal(this, newData, value, {...currentRowData, quantity: value});
    }

    setDiscount(this: DxDataGridTypes.Column, newData: any, value: number, currentRowData: any) {
        setSubTotal(this, newData, value, {...currentRowData, discount: value});
    }
}
platformBrowserDynamic().bootstrapModule(AppModule);

function setSubTotal(self: DxDataGridTypes.Column, newData: any, value: number, currentRowData: any) {
    if(currentRowData.quantity && currentRowData.price) {
        newData.subtotal = currentRowData.quantity * currentRowData.price;
        if(currentRowData.discount > 0) {
            newData.subtotal -= (currentRowData.discount * newData.subtotal / 100) ;
        }
    } else {
        newData.subtotal = 0;
    }
    self.defaultSetCellValue?.(newData, value, currentRowData);
}
