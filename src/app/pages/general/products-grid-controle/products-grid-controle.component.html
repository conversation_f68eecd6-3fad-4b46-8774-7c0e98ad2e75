<dx-data-grid
  [dataSource]="data"
  id="gridContainer"
  [rtlEnabled]="isRtlEnabled"
  keyExpr="lineNo"
  [showBorders]="true"
  (onEditorPreparing)="onEditorPreparing($event)"
  [columnAutoWidth]="true"
  [remoteOperations]="true"
  (onRowInserted)="onRowInserted($event)"
  (onInitNewRow)="initRow($event)"
  (onRowRemoved)="onRowRemoved($event)"
>
  <dxo-scrolling [mode]="scrollingMode"></dxo-scrolling>
  <dxo-editing
    mode="row"
    [allowAdding]="true"
    [allowDeleting]="true"
    [allowUpdating]="true"
    [confirmDelete]="false"
    [useIcons]="true"
    [newRowPosition]="newRowPosition"
    [changes]="changes"
    [(editRowKey)]="editRowKey"
  ></dxo-editing>
  <dxo-pager [visible]="true"></dxo-pager>

  <dxi-column
    dataField="lineNo"
    caption="i"
    [width]="50"
    [allowEditing]="false"
  ></dxi-column>
  <dxi-column
    dataField="productId"
    caption="Item Code"
    [width]="150"
    [allowEditing]="false"
    [visible]="false"
  ></dxi-column>
  <dxi-column
    dataField="product"
    caption="Product"
    [width]="200"
    [setCellValue]="setProductValue"
  >
    <dxo-lookup
      [dataSource]="Product"
      displayExpr="productName"
      valueExpr="productId"
    >
    </dxo-lookup>
  </dxi-column>

  <dxi-column dataField="unitId" caption="UOM" [width]="100">
    <dxo-lookup
      [dataSource]="getFilteredunits"
      displayExpr="unitName"
      valueExpr="unitId"
    >
    </dxo-lookup>
  </dxi-column>

  <!--<dxi-column dataField="Account" caption="Account"></dxi-column>-->
  <dxi-column dataField="code" caption="Analytic Account" [width]="200">
    <dxo-lookup
      [dataSource]="AnalyticAccounts"
      displayExpr="analyticAccountName"
      valueExpr="analyticAccountId"
    >
    </dxo-lookup>
  </dxi-column>

  <dxi-column
    dataField="quantity"
    caption="Quantity"
    [width]="100"
    [setCellValue]="setQuantity"
  ></dxi-column>
  <dxi-column
    dataField="price"
    caption="Price"
    [width]="100"
    [setCellValue]="setPrice"
  ></dxi-column>
  <dxi-column
    dataField="discount"
    caption="Discount.%"
    [width]="100"
    [setCellValue]="setDiscount"
  ></dxi-column>
  <dxi-column dataField="taxes" caption="Taxes"></dxi-column>

  <dxi-column
    dataField="subtotal"
    caption="Subtotal"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
    dataField="serialNumber"
    caption="Serial Number"
    [allowEditing]="false"
  ></dxi-column>

  <dxi-column
  dataField="bonus"
  caption="Bonus"
  [allowEditing]="false"
></dxi-column>

<dxi-column
dataField="unitQuantity"
caption="Unit Quantity"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="unitPrice"
caption="Unit Price"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="defaultItemCost"
caption="Default Item Cost"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="unitCost"
caption="Unit Cost"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="rate"
caption="Rate"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="accountID"
caption="Account ID"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="warehouseID"
caption="Warehouse ID"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="storeAccount"
caption="Store Account"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="notesPerLine"
caption="Notes Per Line"
[allowEditing]="false"
></dxi-column>

<dxi-column
dataField="variantName"
caption="Variant Name"
[allowEditing]="false"
></dxi-column>


  <dxi-column type="buttons">
    <dxi-button
      icon="add"
      text="Add"
      [onClick]="onAddButtonClick"
      [visible]="isAddButtonVisible"
    ></dxi-button>
    <dxi-button name="delete"></dxi-button>
    <dxi-button name="save"></dxi-button>
    <dxi-button name="cancel"></dxi-button>
  </dxi-column>

  <dxo-toolbar>
    <dxi-item name="addRowButton" showText="always"></dxi-item>
  </dxo-toolbar>
</dx-data-grid>
