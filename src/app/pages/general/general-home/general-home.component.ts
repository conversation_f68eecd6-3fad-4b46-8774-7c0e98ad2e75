import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-general-home',
    templateUrl: './general-home.component.html',
    styleUrls: ['./general-home.component.scss'],
    standalone: false
})
export class generalHomeComponent implements OnInit {
  // إحصائيات الشركات
  totalCompanies: number = 0;
  activeBranches: number = 0;
  totalCurrencies: number = 0;
  totalCountries: number = 0;

  // إحصائيات المستخدمين
  totalUsers: number = 0;
  activeUsers: number = 0;
  adminUsers: number = 0;

  // إحصائيات النظام
  systemVersion: string = '';
  lastUpdate: string = '';
  databaseSize: string = '';

  // بيانات الرسوم البيانية
  userActivityData: any[] = [];
  systemUsageData: any[] = [];

  // آخر تسجيلات الدخول
  recentLogins: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة - تم تحديثها لاستخدام الترجمات
  quickLinks = [
    { title: this.translate.instant('COMMON.Companies'), icon: 'fa-building', route: '/general/company_list' },
    { title: this.translate.instant('COMMON.Branches'), icon: 'fa-code-branch', route: '/general/branch_list' },
    { title: this.translate.instant('COMMON.Currencies'), icon: 'fa-money-bill-wave', route: '/general/currencies' },
    { title: this.translate.instant('COMMON.Cities'), icon: 'fa-city', route: '/general/Cityes' },
    { title: this.translate.instant('COMMON.Countries'), icon: 'fa-globe', route: '/general/countries' },
    { title: this.translate.instant('COMMON.Settings'), icon: 'fa-cog', route: '/general/settings' },
    { title: this.translate.instant('COMMON.SystemStatus'), icon: 'fa-server', route: '/general/status' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات الشركات الوهمية
      this.totalCompanies = 5;
      this.activeBranches = 12;
      this.totalCurrencies = 8;
      this.totalCountries = 25;

      // بيانات المستخدمين الوهمية
      this.totalUsers = 45;
      this.activeUsers = 38;
      this.adminUsers = 5;

      // بيانات النظام الوهمية
      this.systemVersion = '2.5.3';
      this.lastUpdate = '2023-06-15';
      this.databaseSize = '1.2 GB';

      // بيانات الرسوم البيانية الوهمية - مع دعم الترجمة
      this.userActivityData = [
        { month: this.translate.instant('COMMON.January'), count: 120 },
        { month: this.translate.instant('COMMON.February'), count: 150 },
        { month: this.translate.instant('COMMON.March'), count: 180 },
        { month: this.translate.instant('COMMON.April'), count: 210 },
        { month: this.translate.instant('COMMON.May'), count: 240 },
        { month: this.translate.instant('COMMON.June'), count: 270 }
      ];

      this.systemUsageData = [
        { module: this.translate.instant('COMMON.Accounting'), usage: 35 },
        { module: this.translate.instant('COMMON.Inventory'), usage: 25 },
        { module: this.translate.instant('COMMON.HumanResources'), usage: 20 },
        { module: this.translate.instant('COMMON.Purchases'), usage: 15 },
        { module: this.translate.instant('COMMON.Sales'), usage: 5 }
      ];

      // بيانات آخر تسجيلات الدخول الوهمية - مع دعم الترجمة
      const successStatus = this.translate.instant('COMMON.Success');
      const failedStatus = this.translate.instant('COMMON.Failed');
      
      this.recentLogins = [
        { id: 1, username: 'admin', name: 'مدير النظام', date: '2023-06-20 09:30:00', ip: '*************', status: successStatus },
        { id: 2, username: 'user1', name: 'أحمد محمد', date: '2023-06-20 09:15:00', ip: '*************', status: successStatus },
        { id: 3, username: 'user2', name: 'محمد علي', date: '2023-06-20 08:45:00', ip: '*************', status: successStatus },
        { id: 4, username: 'user3', name: 'خالد عبدالله', date: '2023-06-19 17:30:00', ip: '*************', status: successStatus },
        { id: 5, username: 'user4', name: 'سارة أحمد', date: '2023-06-19 16:45:00', ip: '*************', status: failedStatus }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      companyStats: this.http.get<any>('api/general/dashboard/companies'),
      userStats: this.http.get<any>('api/general/dashboard/users'),
      systemStats: this.http.get<any>('api/general/dashboard/system'),
      recentLogins: this.http.get<any>('api/general/dashboard/recent-logins')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalCompanies = results.companyStats.total;
        this.activeBranches = results.companyStats.branches;
        this.totalCurrencies = results.companyStats.currencies;
        this.totalCountries = results.companyStats.countries;

        this.totalUsers = results.userStats.total;
        this.activeUsers = results.userStats.active;
        this.adminUsers = results.userStats.admin;

        this.systemVersion = results.systemStats.version;
        this.lastUpdate = results.systemStats.lastUpdate;
        this.databaseSize = results.systemStats.databaseSize;

        this.recentLogins = results.recentLogins;

        // بيانات الرسوم البيانية
        this.userActivityData = results.userStats.activity;
        this.systemUsageData = results.systemStats.usage;
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة تسجيل الدخول - محسنة لدعم الترجمة
  getLoginStatusClass(status: string): string {
    // نحصل على الترجمات الحالية للمقارنة
    const successStatus = this.translate.instant('COMMON.Success');
    const failedStatus = this.translate.instant('COMMON.Failed');
    
    switch (status) {
      case successStatus:
      case 'ناجح': // للتوافق مع البيانات القديمة
        return 'badge-light-success';
      case failedStatus:
      case 'فاشل': // للتوافق مع البيانات القديمة
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
