
<!-- بداية لوحة تحكم الإعدادات العامة -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.GeneralDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.GeneralHome' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.GeneralModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف إحصائيات الشركات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي الشركات -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalCompanies) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.GeneralTotalCompanies' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-building text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الفروع النشطة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(activeBranches) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.GeneralActiveBranches' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-code-branch text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- إجمالي العملات -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalCurrencies) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.GeneralTotalCurrencies' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-money-bill-wave text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- إجمالي الدول -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalCountries) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.GeneralTotalCountries' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-globe text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات الشركات -->

      <!-- بداية صف إحصائيات المستخدمين والنظام -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي المستخدمين -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalUsers) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.GeneralTotalUsers' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="d-flex align-items-center flex-wrap w-100">
                <div class="d-flex flex-column flex-grow-1 pe-8">
                  <div class="d-flex flex-wrap">
                    <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                      <div class="d-flex align-items-center">
                        <div class="fs-4 fw-bold">{{ formatNumber(activeUsers) }}</div>
                      </div>
                      <div class="fw-semibold fs-6 text-gray-400">{{ 'COMMON.GeneralActiveUsers' | translate }}</div>
                    </div>
                    <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 mb-3">
                      <div class="d-flex align-items-center">
                        <div class="fs-4 fw-bold">{{ formatNumber(adminUsers) }}</div>
                      </div>
                      <div class="fw-semibold fs-6 text-gray-400">{{ 'COMMON.GeneralAdminUsers' | translate }}</div>
                    </div>
                  </div>
                </div>
                <div class="symbol symbol-50px me-2">
                  <span class="symbol-label bg-light-primary">
                    <i class="fa fa-users text-primary fs-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- معلومات النظام -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.GeneralSystemInfo' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.GeneralSystemDetails' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-wrap">
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                  <div class="d-flex align-items-center">
                    <div class="fs-4 fw-bold">{{ systemVersion }}</div>
                  </div>
                  <div class="fw-semibold fs-6 text-gray-400">{{ 'COMMON.GeneralSystemVersion' | translate }}</div>
                </div>
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                  <div class="d-flex align-items-center">
                    <div class="fs-4 fw-bold">{{ lastUpdate }}</div>
                  </div>
                  <div class="fw-semibold fs-6 text-gray-400">{{ 'COMMON.GeneralLastUpdate' | translate }}</div>
                </div>
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 mb-3">
                  <div class="d-flex align-items-center">
                    <div class="fs-4 fw-bold">{{ databaseSize }}</div>
                  </div>
                  <div class="fw-semibold fs-6 text-gray-400">{{ 'COMMON.GeneralDatabaseSize' | translate }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات المستخدمين والنظام -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني لنشاط المستخدمين -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.GeneralUserActivity' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.GeneralLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.GeneralViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.GeneralQuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.GeneralCommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف الإحصائيات الأخيرة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.RecentStatistics' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.Daily' | translate }} / {{ 'COMMON.Weekly' | translate }} / {{ 'COMMON.Monthly' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="row g-5 g-xl-8">
                <!-- estadísticas diarias -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-primary">
                    <div class="card-body my-3">
                      <a class="card-title fw-bold text-primary fs-5 mb-3 d-block">{{ 'COMMON.Daily' | translate }} {{ 'COMMON.Stats' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">120</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Accounting' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">240</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Inventory' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">340</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Sales' | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- estadísticas semanales -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-success">
                    <div class="card-body my-3">
                      <a class="card-title fw-bold text-success fs-5 mb-3 d-block">{{ 'COMMON.Weekly' | translate }} {{ 'COMMON.Stats' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">840</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Accounting' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">1650</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Inventory' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">2380</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Sales' | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- estadísticas mensuales -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-info">
                    <div class="card-body my-3">
                      <a class="card-title fw-bold text-info fs-5 mb-3 d-block">{{ 'COMMON.Monthly' | translate }} {{ 'COMMON.Stats' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">3680</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Accounting' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">6420</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Inventory' | translate }}</span>
                      </div>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">9840</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.Sales' | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الإحصائيات الأخيرة -->

      <!-- بداية صف آخر تسجيلات الدخول -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.GeneralRecentLogins' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.GeneralLastFiveLogins' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.GeneralViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-50px">{{ 'COMMON.GeneralID' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.GeneralUsername' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.GeneralName' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.GeneralDate' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.GeneralIP' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.GeneralStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let login of recentLogins">
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ login.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ login.username }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ login.name }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ login.date }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ login.ip }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getLoginStatusClass(login.status)">{{ login.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف آخر تسجيلات الدخول -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم الإعدادات العامة -->
