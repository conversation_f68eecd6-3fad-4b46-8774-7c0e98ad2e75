import { NgModule } from '@angular/core';
import { CountryRoutingModule } from './country-routing.module';
/*import { WarehouseCreateComponent } from './warehouse-create/warehouse-create.component';*/
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';
/*import { WarehouseEditComponent } from './warehouse-edit/warehouse-edit.component';*/

@NgModule({
  imports: [CommonModule, CountryRoutingModule,
    ReactiveFormsModule, TranslateModule, NgSelectModule]
   })
export class CountryModule { }
