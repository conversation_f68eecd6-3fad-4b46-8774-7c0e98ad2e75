import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CountryListComponent } from './country-list.component';
import { ModalsModule } from '../../../../_metronic/partials';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslationModule } from 'src/app/modules/i18n';

@NgModule({
  declarations: [CountryListComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: CountryListComponent,
      },
    ]),
    ModalsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbPaginationModule,
    TranslationModule
  ],
})
export class CountryListModule { }
