<!--begin::Card-->
<div class="card">
  <!--begin::Card header-->
  <div class="card-header border-0 pt-6">
    <!--begin::Card title-->
    <div class="card-title">
      <!--begin::Search-->
      <div class="d-flex align-items-center position-relative my-1">
        <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
        <span class="svg-icon svg-icon-1 position-absolute ms-6">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                  transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
            <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                  fill="currentColor" />
          </svg>
        </span>
        <!--end::Svg Icon-->
        <input type="text" data-kt-company-table-filter="search" class="form-control form-control-solid w-250px ps-15"
               placeholder="{{ 'INVENTORY.WAREHOUSES.SEARCH' | translate  }}" [formControl]="searchCtrl" />
      </div>
      <!--end::Search-->
    </div>
    <div class="card-toolbar">

    </div>
  </div>
  <div class="card-body pt-0">
    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_Companies_table">
      <thead>
        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
          <th class="w-10px pe-2">
            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
              <input class="form-check-input" type="checkbox" data-kt-check="true"
                     data-kt-check-target="#kt_Companies_table .form-check-input" value="1" />
            </div>
          </th>
          <th class="min-w-125px" translate="COMMON.CONTRYNAMEEN"></th>
          <th class="min-w-125px" translate="COMMON.COUNTRYNAME"> </th>
          <th class="min-w-125px" translate="COMMON.CODEID"></th>
          <th class="text-end min-w-70px" translate="COMMON.ACTIONS"></th>
        </tr>
      </thead>
      <tbody class="fw-semibold text-gray-600">
        <tr *ngFor="let item of warehouses">
          <td>
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input" type="checkbox" value="1" />
            </div>
          </td>
          <td>
            <a class="text-gray-800 text-hover-primary mb-1" [routerLink]="['/inventory/warehouses',item.id]">
              {{item.contryNameEN }}
            </a>
          </td>
          <td>
            {{ item.countryName }}

          </td>
           
          <td>{{item.codeID}}</td>
         
          <td class="text-end">
            <a href="#" class="btn btn-sm btn-light btn-active-light-primary" data-kt-menu-trigger="click"
               data-kt-menu-placement="bottom-end">
              Actions
              <span class="svg-icon svg-icon-5 m-0">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                        fill="currentColor" />
                </svg>
              </span>
            </a>
            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-175px py-4"
                 data-kt-menu="true">
              <div class="menu-item px-3">
                <div class="menu-content fs-6 text-dark fw-bold px-3 py-4">Quick Actions</div>
              </div>
              <div class="separator mb-3 opacity-75"></div>
              <div class="menu-item px-3">
                <a class="menu-link px-3" (click)="remove(item)" data-kt-company-table-filter="delete_row">{{'COMMON.DELETE' | translate }}</a>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <!--end::Table-->
    <ngb-pagination *ngIf="pagesCount > 1" [collectionSize]="itemsCount" [pageSize]="20" [(page)]="currentPage"
                    size="lg"></ngb-pagination>
  </div>
  <!--end::Card body-->
</div>
<!--end::Card-->
<!--begin::Modals-->
<!--begin::Modal - Companies - Add-->



<div class="modal fade" id="kt_Companies_export_modal" tabindex="-1" aria-hidden="true">
  <!--begin::Modal dialog-->
  <div class="modal-dialog modal-dialog-centered mw-650px">
    <!--begin::Modal content-->
    <div class="modal-content">
      <!--begin::Modal header-->
      <div class="modal-header">
        <!--begin::Modal title-->
        <h2 class="fw-bold">Export Companies</h2>
        <!--end::Modal title-->
        <!--begin::Close-->
        <div id="kt_Companies_export_close" class="btn btn-icon btn-sm btn-active-icon-primary">
          <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
          <span class="svg-icon svg-icon-1">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
                    fill="currentColor" />
              <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                    fill="currentColor" />
            </svg>
          </span>
          <!--end::Svg Icon-->
        </div>
        <!--end::Close-->
      </div>
      <!--end::Modal header-->
      <!--begin::Modal body-->
      <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
        <!--begin::Form-->
        <form id="kt_Companies_export_form" class="form" action="#">
          <!--begin::Input group-->
          <div class="fv-row mb-10">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Select Export Format:</label>
            <!--end::Label-->
            <!--begin::Input-->
            <select data-control="select2" data-placeholder="Select a format" data-hide-search="true" name="format"
                    class="form-select form-select-solid">
              <option value="excell">Excel</option>
              <option value="pdf">PDF</option>
              <option value="cvs">CVS</option>
              <option value="zip">ZIP</option>
            </select>
            <!--end::Input-->
          </div>
          <!--end::Input group-->
          <!--begin::Input group-->
          <div class="fv-row mb-10">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Select Date Range:</label>
            <!--end::Label-->
            <!--begin::Input-->
            <input class="form-control form-control-solid" placeholder="Pick a date" name="date" />
            <!--end::Input-->
          </div>
          <!--end::Input group-->
          <!--begin::Row-->
          <div class="row fv-row mb-15">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Payment Type:</label>
            <!--end::Label-->
            <!--begin::Radio group-->
            <div class="d-flex flex-column">
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="1" checked="checked" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">All</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="2" checked="checked" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">Visa</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="3" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">Mastercard</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid">
                <input class="form-check-input" type="checkbox" value="4" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">American Express</span>
              </label>
              <!--end::Radio button-->
            </div>
            <!--end::Input group-->
          </div>
          <!--end::Row-->
          <!--begin::Actions-->
          <div class="text-center">
            <button type="reset" id="kt_Companies_export_cancel" class="btn btn-light me-3">Discard</button>
            <button type="submit" id="kt_Companies_export_submit" class="btn btn-primary">
              <span class="indicator-label">Submit</span>
              <span class="indicator-progress">
                Please wait...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </button>
          </div>
          <!--end::Actions-->
        </form>
        <!--end::Form-->
      </div>
      <!--end::Modal body-->
    </div>
    <!--end::Modal content-->
  </div>
  <!--end::Modal dialog-->
</div>
