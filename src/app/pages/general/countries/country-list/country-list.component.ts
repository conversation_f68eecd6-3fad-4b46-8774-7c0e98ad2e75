import { ChangeDetector<PERSON><PERSON>, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';
import { CountryService} from '../country.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';


@Component({
    selector: 'app-countrylist',
    templateUrl: './country-list.component.html',
    styleUrls: ['./country-list.component.scss'],
    standalone: false
})
export class CountryListComponent implements OnInit, OnDestroy {
  warehouses: any[] = [];
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [{ name: 'تمت', value: true }, { name: 'لم تتم', value: false }];
  editMode: boolean = false;
  pagesCount = 0;
  modalConfig: ModalConfig = {
    modalTitle: 'إضافة إستفسار جديد',
    dismissButtonLabel: 'حفظ',
    closeButtonLabel: 'إلغاء',
    onDismiss: () => new Promise((resolve, reject) => {
      if (this.newInquiryFrm.valid) {
        resolve(true);
      } else {
        this.newInquiryFrm.markAllAsTouched();
      }
    }),
    onClose: () => new Promise((resolve, reject) => {
      resolve(false);
    }),
  };
  @ViewChild('modal') private modalComponent: ModalComponent;
  constructor(private fb: FormBuilder,
    private CountryService: CountryService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required]
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.CountryService.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.CountryService.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.CountryService.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.CountryService.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }

  async openModal() {
    this.modalConfig.modalTitle = "إضافة بيانات مستخدم";
    this.newInquiryFrm.reset();
    this.editMode = false;
    const id = this.route.snapshot.params.id;
    if (id) {
      this.newInquiryFrm.get('companyId')?.setValue(id);
    }
    return await this.modalComponent.open().then(v => {
      if (v) {
        let form = this.newInquiryFrm.value
        delete form['id'];
        this.subscriptions.add(this.CountryService.create(form)
          .subscribe(r => {
            if (r.success) {
              this.warehouses.push(r.data);
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  async openModalForEdit(item: any) {
    this.editMode = true;
    this.modalConfig.modalTitle = "تحديث  إستفسار";
    this.newInquiryFrm.reset();
    this.newInquiryFrm.get("id")?.setValue(item.id);
    this.newInquiryFrm.get("notes")?.setValue(item.firstName);
    return await this.modalComponent.open().then(v => {
      if (v) {
        let form = this.newInquiryFrm.value
        this.subscriptions.add(this.CountryService.update(form.id, form)
          .subscribe(r => {
            if (r.success) {
              item.notes = r.data.notes;
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  remove(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.CountryService.delete(item.id)
          .subscribe(r => {
            if (r.success) {
              this.warehouses = this.warehouses.filter(c => c.id != item.id);
              this.cd.detectChanges();
            }
          }));
      }
    });
  }
  activate(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد ' + (item.isDone ? 'إعادة فتح المعاملة' : 'إغلاق المعاملة'),
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'تجاهل',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.CountryService.activate(item.id)
          .subscribe(r => {
            if (r.success) {
              item.isDone = !item.isDone;
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  filter() {
    this.subscriptions.add(this.CountryService.filter({ 'isDone': this.statusFilterCtrl.value, 'companyId': this.companyFilterCtrl.value }).subscribe(r => {
      if (r.success) {
        this._currentPage = 1;
        this.loadData(r);
        this.itemsCount = r.data.itemsCount;
      }
    }));
  }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(this.CountryService.list('', value).subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cd.detectChanges();
        MenuComponent.reinitialization();
      }
    }));
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.warehouses = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }

  exportToExcel() {
    this.subscriptions.add(this.CountryService.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'warehouses.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  uploadExcel() {
    var input = document.createElement('input');
    input.type = "file";
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(this.CountryService.importExcel(fd)
            .subscribe(e => {
              if (e) {
                this.currentPage = 1;
              }
            }));
        }
      }
    }
  }
}
