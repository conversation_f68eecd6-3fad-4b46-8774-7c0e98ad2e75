import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ContractsTypesComponent} from './contracts-types/contracts-types.component';
import {CustomersExtractsComponent} from './customers-extracts/customers-extracts.component';
import {SubcontractorsContractsComponent} from './subcontractors-contracts/subcontractors-contracts.component';
import {SubcontractorsExtractsComponent} from './subcontractors-extracts/subcontractors-extracts.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'contracts_types',
        pathMatch: 'full'
    },
    {
        path: 'contracts_types',
        component: ContractsTypesComponent,
    },
    {
        path: 'subcontractors_contracts',
        component: SubcontractorsContractsComponent,
    },

    {
        path: 'subcontractors_extracts',
        component: SubcontractorsExtractsComponent,
    },
    {
        path: 'customers_extracts',
        component: CustomersExtractsComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ProjectContractsAndExtractsRoutingModule {
}
