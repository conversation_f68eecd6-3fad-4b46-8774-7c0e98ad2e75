import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {ContractsTypesComponent} from './contracts-types/contracts-types.component';
import {CustomersExtractsComponent} from './customers-extracts/customers-extracts.component';
import {SubcontractorsContractsComponent} from './subcontractors-contracts/subcontractors-contracts.component';
import {SubcontractorsExtractsComponent} from './subcontractors-extracts/subcontractors-extracts.component';

import {ProjectContractsAndExtractsRoutingModule} from "./project-contracts-and-extracts-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
    declarations: [
        ContractsTypesComponent,
        SubcontractorsContractsComponent,
        SubcontractorsExtractsComponent,
        CustomersExtractsComponent,
    ],
    imports: [ProjectContractsAndExtractsRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class ProjectContractsAndExtractsModule {
}
