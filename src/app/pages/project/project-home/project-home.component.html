
<!-- بداية لوحة تحكم المشاريع -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.ProjectDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.Home' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.ProjectModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف إحصائيات المشاريع -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي المشاريع -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalProjects) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ProjectTotal' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-building text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المشاريع النشطة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(activeProjects) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ProjectActive' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-spinner text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المشاريع المكتملة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(completedProjects) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ProjectCompleted' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-check-circle text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المشاريع المعلقة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(pendingProjects) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ProjectPending' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-pause-circle text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات المشاريع -->

      <!-- بداية صف إحصائيات المناقصات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي المناقصات -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalTenders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TenderTotal' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-file-contract text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المناقصات الفائزة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(wonTenders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TenderWon' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-trophy text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المناقصات الخاسرة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(lostTenders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TenderLost' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-danger">
                  <i class="fa fa-times-circle text-danger fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المناقصات المعلقة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(pendingTenders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TenderPending' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-hourglass-half text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات المناقصات -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني لحالة المشاريع -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ProjectProgress' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.LastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.QuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.CommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف المشاريع الأخيرة وحالة المناقصات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- المشاريع الأخيرة -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.RecentProjects' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.LastFiveProjects' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.ProjectID' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.ProjectName' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.Client' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.Value' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.Progress' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.Status' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let project of recentProjects">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ project.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ project.name }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ project.client }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ formatNumber(project.value) }}</span>
                      </td>
                      <td>
                        <div class="d-flex flex-column w-100 me-2">
                          <div class="d-flex flex-stack mb-2">
                            <span class="text-muted me-2 fs-7 fw-bold">{{ project.progress }}%</span>
                          </div>
                          <div class="progress h-6px w-100">
                            <div class="progress-bar" [ngClass]="project.progress < 50 ? 'bg-warning' : (project.progress < 80 ? 'bg-primary' : 'bg-success')" role="progressbar" [style.width.%]="project.progress"></div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getStatusClass(project.status)">{{ project.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- حالة المناقصات -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.TenderStatus' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.CurrentStatus' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewReport' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget2></app-charts-widget2>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف المشاريع الأخيرة وحالة المناقصات -->

      <!-- بداية صف المناقصات الأخيرة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.RecentTenders' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.LastFiveTenders' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.TenderID' | translate }}</th>
                      <th class="min-w-200px">{{ 'COMMON.TenderName' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.Client' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.Value' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.Status' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let tender of recentTenders">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ tender.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ tender.name }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ tender.client }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ formatNumber(tender.value) }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getStatusClass(tender.status)">{{ tender.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف المناقصات الأخيرة -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم المشاريع -->
