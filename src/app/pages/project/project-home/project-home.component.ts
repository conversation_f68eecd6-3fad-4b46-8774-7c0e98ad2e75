import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-project-home',
    templateUrl: './project-home.component.html',
    styleUrls: ['./project-home.component.scss'],
    standalone: false
})
export class projectHomeComponent implements OnInit {
  // إحصائيات المشاريع
  totalProjects: number = 0;
  activeProjects: number = 0;
  completedProjects: number = 0;
  pendingProjects: number = 0;

  // إحصائيات المناقصات
  totalTenders: number = 0;
  wonTenders: number = 0;
  lostTenders: number = 0;
  pendingTenders: number = 0;

  // بيانات الرسوم البيانية
  projectStatusData: any[] = [];
  projectProgressData: any[] = [];
  tenderStatusData: any[] = [];

  // المشاريع الأخيرة
  recentProjects: any[] = [];

  // المناقصات الأخيرة
  recentTenders: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'إضافة مشروع جديد', icon: 'fa-plus-circle', route: '/project/configuration/tenders' },
    { title: 'إضافة مناقصة', icon: 'fa-file-contract', route: '/project/configuration/tenders' },
    { title: 'دراسة مناقصة', icon: 'fa-chart-line', route: '/project/tender_study/tender-models' },
    { title: 'أوامر التكليف', icon: 'fa-tasks', route: '/project/operations/assignment-order' },
    { title: 'المستخلصات', icon: 'fa-file-invoice-dollar', route: '/project/contracts_and_extracts/customers-extracts' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات المشاريع الوهمية
      this.totalProjects = 125;
      this.activeProjects = 48;
      this.completedProjects = 62;
      this.pendingProjects = 15;

      // بيانات المناقصات الوهمية
      this.totalTenders = 87;
      this.wonTenders = 32;
      this.lostTenders = 41;
      this.pendingTenders = 14;

      // بيانات الرسوم البيانية الوهمية
      this.projectStatusData = [
        { status: 'نشط', count: 48 },
        { status: 'مكتمل', count: 62 },
        { status: 'معلق', count: 15 }
      ];

      this.projectProgressData = [
        { month: 'يناير', completed: 5, active: 8 },
        { month: 'فبراير', completed: 7, active: 10 },
        { month: 'مارس', completed: 8, active: 12 },
        { month: 'أبريل', completed: 10, active: 15 },
        { month: 'مايو', completed: 12, active: 14 },
        { month: 'يونيو', completed: 15, active: 18 }
      ];

      this.tenderStatusData = [
        { status: 'فائز', count: 32 },
        { status: 'خاسر', count: 41 },
        { status: 'معلق', count: 14 }
      ];

      // بيانات المشاريع الأخيرة الوهمية
      this.recentProjects = [
        { id: 'PRJ-2023-001', name: 'تطوير مجمع سكني', client: 'شركة الإعمار', value: 12500000, progress: 75, status: 'نشط' },
        { id: 'PRJ-2023-002', name: 'بناء مدرسة', client: 'وزارة التعليم', value: 8750000, progress: 90, status: 'نشط' },
        { id: 'PRJ-2023-003', name: 'تجديد مستشفى', client: 'وزارة الصحة', value: 15000000, progress: 60, status: 'نشط' },
        { id: 'PRJ-2023-004', name: 'إنشاء طريق سريع', client: 'وزارة النقل', value: 25000000, progress: 40, status: 'نشط' },
        { id: 'PRJ-2023-005', name: 'تطوير حديقة عامة', client: 'أمانة المدينة', value: 5000000, progress: 100, status: 'مكتمل' }
      ];

      // بيانات المناقصات الأخيرة الوهمية
      this.recentTenders = [
        { id: 'TND-2023-001', name: 'مناقصة بناء مركز تجاري', client: 'شركة التطوير العقاري', value: 18000000, status: 'فائز' },
        { id: 'TND-2023-002', name: 'مناقصة تطوير شبكة مياه', client: 'شركة المياه الوطنية', value: 9500000, status: 'معلق' },
        { id: 'TND-2023-003', name: 'مناقصة بناء جسر', client: 'وزارة النقل', value: 30000000, status: 'خاسر' },
        { id: 'TND-2023-004', name: 'مناقصة تجديد مبنى إداري', client: 'وزارة المالية', value: 7500000, status: 'فائز' },
        { id: 'TND-2023-005', name: 'مناقصة إنشاء محطة طاقة', client: 'شركة الكهرباء', value: 45000000, status: 'معلق' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      projectStats: this.http.get<any>('api/project/dashboard/stats'),
      tenderStats: this.http.get<any>('api/project/dashboard/tenders'),
      recentProjects: this.http.get<any>('api/project/dashboard/recent-projects'),
      recentTenders: this.http.get<any>('api/project/dashboard/recent-tenders')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalProjects = results.projectStats.total;
        this.activeProjects = results.projectStats.active;
        this.completedProjects = results.projectStats.completed;
        this.pendingProjects = results.projectStats.pending;

        this.totalTenders = results.tenderStats.total;
        this.wonTenders = results.tenderStats.won;
        this.lostTenders = results.tenderStats.lost;
        this.pendingTenders = results.tenderStats.pending;

        this.recentProjects = results.recentProjects;
        this.recentTenders = results.recentTenders;

        // بيانات الرسوم البيانية
        this.projectStatusData = [
          { status: 'نشط', count: this.activeProjects },
          { status: 'مكتمل', count: this.completedProjects },
          { status: 'معلق', count: this.pendingProjects }
        ];

        this.tenderStatusData = [
          { status: 'فائز', count: this.wonTenders },
          { status: 'خاسر', count: this.lostTenders },
          { status: 'معلق', count: this.pendingTenders }
        ];
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة المشروع
  getStatusClass(status: string): string {
    switch (status) {
      case 'نشط':
      case 'فائز':
        return 'badge-light-success';
      case 'مكتمل':
        return 'badge-light-primary';
      case 'معلق':
        return 'badge-light-warning';
      case 'خاسر':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
