import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {CustomersComponent} from './customers/customers.component';
import {ConsultantsComponent} from './consultants/consultants.component';
import {TendersTypeComponent} from './tenders-type/tenders-type.component';
import {TypeOfAttitudeTenderComponent} from './type-of-attitude-tender/type-of-attitude-tender.component';
import {TenderActivityTypesComponent} from './tender-activity-types/tender-activity-types.component';
import {BusinessTypeComponent} from './business-type/business-type.component';
import {TendersComponent} from './tenders/tenders.component';
import {TendersDeductionsComponent} from './tenders-deductions/tenders-deductions.component';
import {CompetitorsTyepComponent} from './competitors-tyep/competitors-tyep.component';
import {CompetitorsComponent} from './competitors/competitors.component';

import {SharedModule} from '../../shared/shared.module';
import {projectConfigurationRoutingModule} from "./project-configuration-routing.module";

@NgModule({
    declarations: [
        CustomersComponent,
        ConsultantsComponent,
        TendersTypeComponent,
        TypeOfAttitudeTenderComponent,
        TenderActivityTypesComponent,
        BusinessTypeComponent,
        TendersComponent,
        TendersDeductionsComponent,
        CompetitorsTyepComponent,
        CompetitorsComponent,
    ],
    imports: [projectConfigurationRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class ProjectModule {
}
