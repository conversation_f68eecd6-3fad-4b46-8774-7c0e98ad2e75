import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BusinessTypeComponent} from './business-type/business-type.component';
import {CompetitorsTyepComponent} from './competitors-tyep/competitors-tyep.component';
import {CompetitorsComponent} from './competitors/competitors.component';

import {ConsultantsComponent} from './consultants/consultants.component';
import {CustomersComponent} from './customers/customers.component';
import {TenderActivityTypesComponent} from './tender-activity-types/tender-activity-types.component';
import {TenderArrangeTypesComponent} from './tender-arrange-types/tender-arrange-types.component';

import {TendersDeductionsComponent} from './tenders-deductions/tenders-deductions.component';
import {TendersTypeComponent} from './tenders-type/tenders-type.component';
import {TendersComponent} from './tenders/tenders.component';
import {TypeOfAttitudeTenderComponent} from './type-of-attitude-tender/type-of-attitude-tender.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'customers',
        pathMatch: 'full'
    },
    {
        path: 'customers',
        component: CustomersComponent,
    },
    {
        path: 'consultants',
        component: ConsultantsComponent,
    },
    {
        path: 'tenders_type',
        component: TendersTypeComponent,
    },
    {
        path: 'type_of_attitude_tender',
        component: TypeOfAttitudeTenderComponent,
    },
    {
        path: 'tender_arrange_types_',
        component: TenderArrangeTypesComponent,
    },
    {
        path: '_tender_activity_types',
        component: TenderActivityTypesComponent,
    },
    {
        path: 'business_type',
        component: BusinessTypeComponent,
    },
    {
        path: 'tenders',
        component: TendersComponent,
    },

    {
        path: 'tenders_deductions',
        component: TendersDeductionsComponent,
    },
    {
        path: 'competitors_tyep',
        component: CompetitorsTyepComponent,
    },
    {
        path: 'competitors',
        component: CompetitorsComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class projectConfigurationRoutingModule {
}
