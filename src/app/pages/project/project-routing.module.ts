import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {projectHomeComponent} from './project-home/project-home.component';

const routes: Routes = [
    {
        path: '',
        component: projectHomeComponent,
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/project-configuration.module').then((m) => m.ProjectModule),
    },
    {
        path: 'tender_study',
        loadChildren: () =>
            import('./tender-study/project-tender-study.module').then((m) => m.ProjectTenderStudyModule),
    },
    {
        path: 'operations',
        loadChildren: () =>
            import('./tender-operations/project-tender-operations.module').then((m) => m.ProjectTenderOperationsModule),
    },
    {
        path: 'contracts_and_extracts',
        loadChildren: () =>
            import('./contracts-and-extracts/project-contracts-and-extracts.module').then((m) => m.ProjectContractsAndExtractsModule),
    },
    {
        path: 'reports',
        loadChildren: () =>
            import('./reports/project-reports.module').then((m) => m.ProjectReportsModule),
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class projectRoutingModule {
}
