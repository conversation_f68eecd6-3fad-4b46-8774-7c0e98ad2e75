import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {AssignmentOrderComponent} from './assignment-order/assignment-order.component';
import {DrawingComponent} from './drawing/drawing.component';
import {SampleApprovalComponent} from './sample-approval/sample-approval.component';
import {projectTenderOperationsRoutingModule} from "./project-tender-operation-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
    declarations: [
        DrawingComponent,
        SampleApprovalComponent,
        AssignmentOrderComponent,
    ],
    imports: [projectTenderOperationsRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class ProjectTenderOperationsModule {
}
