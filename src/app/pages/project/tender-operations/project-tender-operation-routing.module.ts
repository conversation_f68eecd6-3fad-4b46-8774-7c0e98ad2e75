import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

import {AssignmentOrderComponent} from './assignment-order/assignment-order.component';
import {DrawingComponent} from './drawing/drawing.component';
import {SampleApprovalComponent} from './sample-approval/sample-approval.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'drawing',
        pathMatch: 'full'
    },
    {
        path: 'drawing',
        component: DrawingComponent,
    },

    {
        path: 'sample_approval',
        component: SampleApprovalComponent,
    },
    {
        path: 'assignment_order',
        component: AssignmentOrderComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class projectTenderOperationsRoutingModule {
}
