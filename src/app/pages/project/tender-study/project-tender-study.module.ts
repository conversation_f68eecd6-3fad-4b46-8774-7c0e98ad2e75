import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {BillsOfMaterialsStudyComponent} from './bills-of-materials-study/bills-of-materials-study.component';
import {BillsOfMaterialsComponent} from './bills-of-materials/bills-of-materials.component';
import {MainWorksComponent} from './main-works/main-works.component';
import {PriceAnalysisItemsComponent} from './price-analysis-items/price-analysis-items.component';
import {SubWorksComponent} from './sub-works/sub-works.component';
import {TenderComponentsComponent} from './tender-components/tender-components.component';
import {TenderGroupsTypesComponent} from './tender-groups-types/tender-groups-types.component';
import {TenderModelsComponent} from './tender-models/tender-models.component';
import {SharedModule} from '../../shared/shared.module';
import {projectTenderStudyRoutingModule} from "./project-tender-study-routing.module";

@NgModule({
    declarations: [
        TenderGroupsTypesComponent,
        TenderModelsComponent,
        TenderComponentsComponent,
        MainWorksComponent,
        SubWorksComponent,
        PriceAnalysisItemsComponent,
        BillsOfMaterialsComponent,
        BillsOfMaterialsStudyComponent,
    ],
    imports: [projectTenderStudyRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class ProjectTenderStudyModule {
}
