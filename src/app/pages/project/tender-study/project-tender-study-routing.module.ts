import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

import {BillsOfMaterialsStudyComponent} from './bills-of-materials-study/bills-of-materials-study.component';
import {BillsOfMaterialsComponent} from './bills-of-materials/bills-of-materials.component';
import {MainWorksComponent} from './main-works/main-works.component';
import {PriceAnalysisItemsComponent} from './price-analysis-items/price-analysis-items.component';
import {SubWorksComponent} from './sub-works/sub-works.component';
import {TenderComponentsComponent} from './tender-components/tender-components.component';
import {TenderGroupsTypesComponent} from './tender-groups-types/tender-groups-types.component';
import {TenderModelsComponent} from './tender-models/tender-models.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'tender_groups_types',
        pathMatch: 'full'
    },
    {
        path: 'tender_groups_types',
        component: TenderGroupsTypesComponent,
    },
    {
        path: 'tender_models',
        component: TenderModelsComponent,
    },

    {
        path: 'tender_components',
        component: TenderComponentsComponent,
    },
    {
        path: 'main_works',
        component: MainWorksComponent,
    },
    {
        path: 'sub_works',
        component: SubWorksComponent,
    },
    {
        path: 'price_analysis_items',
        component: PriceAnalysisItemsComponent,
    },
    {
        path: 'bills_of_materials',
        component: BillsOfMaterialsComponent,
    },
    {
        path: 'bills_of_materials_study',
        component: BillsOfMaterialsStudyComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class projectTenderStudyRoutingModule {
}
