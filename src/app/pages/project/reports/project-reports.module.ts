import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';

import {DetailedProjectItemComponent} from './detailed-project-item/detailed-project-item.component';
import {ExtractsComponent} from './extracts/extracts.component';
import {ProjectFinancialComponent} from './project-financial/project-financial.component';
import {ProjectValueComponent} from './project-value/project-value.component';
import {TenderStudyReportComponent} from './tender-study-report/tender-study-report.component';
import {
    TendersTechnicalAndFinancialReportComponent
} from './tenders-technical-and-financial-report/tenders-technical-and-financial-report.component';
import {TotalProjectItemComponent} from './total-project-item/total-project-item.component';
import {TotalProjectComponent} from './total-project/total-project.component';
import {projectReportsRoutingModule} from "./project-reports-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
    declarations: [
        TendersTechnicalAndFinancialReportComponent,
        TenderStudyReportComponent,
        TotalProjectComponent,
        ProjectFinancialComponent,
        ProjectValueComponent,
        ExtractsComponent,
        TotalProjectItemComponent,
        DetailedProjectItemComponent,
    ],
    imports: [projectReportsRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class ProjectReportsModule {
}
