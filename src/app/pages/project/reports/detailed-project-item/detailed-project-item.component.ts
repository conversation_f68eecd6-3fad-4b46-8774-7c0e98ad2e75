import { ChangeDetectorRef, Component, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { ProjectService } from '../../project.service';

@Component({
    selector: 'app-detailed-project-item',
    templateUrl: './detailed-project-item.component.html',
    styleUrls: ['./detailed-project-item.component.scss'],
    standalone: false
})
export class DetailedProjectItemComponent implements OnDestroy {

  data: any[];
  gridDataSource: any[];
  tempdata: 123456
  tempstring: "123456"
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  todayButton: any;
  nextDateButton: any;
  millisecondsInDay = 24 * 60 * 60 * 1000;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;

  constructor(private service: ProjectService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.hrReports}AttendanceReport`;
    this.subscription.add(service.list().subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getEmployeesDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();

    this.todayButton = {
      text: 'Today',
      onClick: () => {
        this.toDateValue = new Date().getTime();
      },
    };

    this.prevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue -= this.millisecondsInDay;
      },
    };

    this.nextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue += this.millisecondsInDay;
      },
    };
  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }

  filter() {
    const Tdates = new Date(this.toDateValue).toLocaleDateString();
    const Fdates = new Date(this.fromDateValue).toLocaleDateString();
    const employees = this.gridBoxValue.length > 0 ? this.gridBoxValue.map((e: any) => e.id) : [];
    this.subscription.add(this.service.list({ Tdates, Fdates, employees }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
}

