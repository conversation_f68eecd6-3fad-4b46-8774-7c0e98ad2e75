import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {DetailedProjectItemComponent} from './detailed-project-item/detailed-project-item.component';
import {ExtractsComponent} from './extracts/extracts.component';
import {ProjectFinancialComponent} from './project-financial/project-financial.component';
import {ProjectValueComponent} from './project-value/project-value.component';
import {TenderStudyReportComponent} from './tender-study-report/tender-study-report.component';
import {
    TendersTechnicalAndFinancialReportComponent
} from './tenders-technical-and-financial-report/tenders-technical-and-financial-report.component';
import {TotalProjectItemComponent} from './total-project-item/total-project-item.component';
import {TotalProjectComponent} from './total-project/total-project.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'tenders_technical_and_financial_report',
        pathMatch: 'full'
    },
    {
        path: 'tenders_technical_and_financial_report',
        component: TendersTechnicalAndFinancialReportComponent,
    },

    {
        path: 'tender_study__report',
        component: TenderStudyReportComponent,
    },
    {
        path: 'total_project',
        component: TotalProjectComponent,
    },

    {
        path: 'project_financial',
        component: ProjectFinancialComponent,
    },

    {
        path: 'project_value',
        component: ProjectValueComponent,
    },
    {
        path: 'extracts',
        component: ExtractsComponent,
    },
    {
        path: 'total_project_item',
        component: TotalProjectItemComponent,
    },

    {
        path: 'detailed__project_item',
        component: DetailedProjectItemComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class projectReportsRoutingModule {
}
