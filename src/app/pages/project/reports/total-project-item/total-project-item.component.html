
<div class="row mb-3">
    <div class="col-4">
      <div class="label">From</div>
      <div class="value">
        <dx-date-box stylingMode="outlined"
                     [(value)]="fromDateValue"
                     [inputAttr]="{ 'aria-label': 'Date' }">
          <dxi-button name="today"
                      location="before"
                      [options]="todayButton"></dxi-button>
          <dxi-button name="prevDate"
                      location="before"
                      [options]="prevDateButton"></dxi-button>
          <dxi-button name="nextDate"
                      location="after"
                      [options]="nextDateButton"></dxi-button>
          <dxi-button name="dropDown"></dxi-button>
        </dx-date-box>
      </div>
    </div>
  
    <div class="col-4">
      <div class="label">To</div>
      <div class="value">
        <dx-date-box stylingMode="outlined"
                     [(value)]="toDateValue"
                     [inputAttr]="{ 'aria-label': 'Date' }">
          <dxi-button name="today"
                      location="before"
                      [options]="todayButton"></dxi-button>
          <dxi-button name="prevDate"
                      location="before"
                      [options]="prevDateButton"></dxi-button>
          <dxi-button name="nextDate"
                      location="after"
                      [options]="nextDateButton"></dxi-button>
          <dxi-button name="dropDown"></dxi-button>
        </dx-date-box>
      </div>
    </div>
  
    <div class="col-4">
      <div class="label">Emp</div>
      <div class="value">
        <dx-drop-down-box [(value)]="gridBoxValue"
                          [inputAttr]="{ 'aria-label': 'Owner' }"
                          valueExpr="ID"
                          displayExpr="Name"
                          placeholder="Select a value..."
                          [showClearButton]="true"
                          [dataSource]="gridDataSource">
          <div *dxTemplate="let data of 'content'">
            <dx-data-grid [dataSource]="gridDataSource"
            [rtlEnabled]="true"  
                          [columns]="['name','job','depart']"
                          [selection]="{ mode: 'multiple' }"
                          [hoverStateEnabled]="true"
                          [paging]="{ enabled: true, pageSize: 10 }"
                          [filterRow]="{ visible: true }"
                          [scrolling]="{ mode: 'virtual' }"
                          [height]="345"
                          [(selectedRowKeys)]="gridBoxValue">
            </dx-data-grid>
          </div>
        </dx-drop-down-box>
      </div>
    </div>
  
  
  
  </div>
  
  <div class="row mb-3">
    <div class="col-12">
      <div class="col-2">
        <dx-button text="Filter"
                   type="default"
                   (onClick)="filter()"
                   class="validate"></dx-button>
  
        <div class="dx-field-value">
          <dx-drop-down-button text="Print"
                               [dropDownOptions]="{ width: 230 }"
                               icon="print"
                               [items]="PrintList"
                               (onItemClick)="onItemClick($event)"></dx-drop-down-button>
        </div>
      </div>
  
    </div>
    </div>
   
  <dx-data-grid id="employees"
  [rtlEnabled]="true"  
                [dataSource]="data"
                keyExpr="code"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true">
  
    <dxo-filter-row [visible]="true"
                    [applyFilter]="currentFilter"></dxo-filter-row>
  
    <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
    <dxo-paging [pageSize]="10"> </dxo-paging>
    <dxo-pager [visible]="true"
               [allowedPageSizes]="[5, 10, 'all']"
               [displayMode]="'compact'"
               [showPageSizeSelector]="true"
               [showInfo]="true"
               [showNavigationButtons]="true">
    </dxo-pager>
    <dxo-header-filter [visible]="true"></dxo-header-filter>
  
    <dxo-search-panel [visible]="true"
                      [highlightCaseSensitive]="true"></dxo-search-panel>
  
    <dxi-column dataField="code"></dxi-column>
    <dxi-column dataField="date"></dxi-column>
    <dxi-column dataField="DayCode" caption="Day"></dxi-column>
    <dxi-column dataField="timeIn" caption="Check In"></dxi-column>
    <dxi-column dataField="timeOut" caption="Check Out"></dxi-column>
    <dxi-column dataField="Explain" caption="Notes"></dxi-column>
    <dxi-column dataField="jobname" caption="job"></dxi-column>
    <dxi-column dataField="AccName" caption="Analytic Account"></dxi-column>
    <dxi-column dataField="depname" caption="Depart Name">
      <dxo-header-filter>
        <!--<dxo-search [enabled]="true"></dxo-search>-->
      </dxo-header-filter>
    </dxi-column>
  
  
    <dxi-column dataField="name">
      <dxo-header-filter>
        <!--<dxo-search [enabled]="true"-->
        <!--[searchExpr]="searchExpr"
        [editorOptions]="editorOptions"></dxo-search>-->
      </dxo-header-filter>
    </dxi-column>
    <dxo-export [enabled]="true"
                [formats]="['pdf','excel']">
    </dxo-export>
  
  
    <dxo-summary>
      <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
      <dxi-total-item column="date"
                      summaryType="min">
        [customizeText]="customizeDate"
      </dxi-total-item>
  
      <dxi-total-item column="val"
                      summaryType="sum"
                      valueFormat="currency">
      </dxi-total-item>
  
    </dxo-summary>
  
  </dx-data-grid>
  