import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { realestateHomeComponent } from './realestate-home/realestate-home.component';


const routes: Routes = [

  {
    path: '',
    component: realestateHomeComponent
  },
  {
    path: 'configuration',
    loadChildren: () =>
        import('./configuration/realestate-configuration.module').then((m) => m.RealestateConfigurationModule),
  },
  {
    path: 'contracts',
    loadChildren: () =>
        import('./contracts/realestate-contracts.module').then((m) => m.RealestateContractsModule),
  },
  {
    path: 'reports',
    loadChildren: () =>
        import('./reports/realestate-reports.module').then((m) => m.RealestateReportsModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class realestateRoutingModule {
}
