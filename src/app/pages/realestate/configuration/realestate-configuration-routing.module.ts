import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {MainPropertyComponent} from "./main-property/main-property.component";
import {RealEstateUnitsComponent} from "./real-estate-units/real-estate-units.component";


const routes: Routes = [

  {
    path: '',
    redirectTo: 'main-property',
    pathMatch: 'full'
  },
  {
    path: 'main-property',
    component: MainPropertyComponent
  },
  {
    path: 'real_estate_units',
    component: RealEstateUnitsComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RealestateConfigurationRoutingModule {
}
