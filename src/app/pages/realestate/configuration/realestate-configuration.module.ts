import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {RealEstateUnitsComponent} from './real-estate-units/real-estate-units.component';
import {MainPropertyComponent} from './main-property/main-property.component';
import {RealestateConfigurationRoutingModule} from "./realestate-configuration-routing.module";


@NgModule({
    declarations: [
        RealEstateUnitsComponent,
        MainPropertyComponent
    ],
    imports: [
        RealestateConfigurationRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class RealestateConfigurationModule {
}
