import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {RentContructComponent} from "./rent-contruct/rent-contruct.component";
import {RentContructExtensionComponent} from "./rent-contruct-extension/rent-contruct-extension.component";
import {RentReceiptVoucherComponent} from "./rent-receipt-voucher/rent-receipt-voucher.component";
import {RentContructSettlementComponent} from "./rent-contruct-settlement/rent-contruct-settlement.component";
import {MaintenanceAndServicesComponent} from "./maintenance-and-services/maintenance-and-services.component";
import {BookingAndContractingComponent} from "./booking-and-contracting/booking-and-contracting.component";
import {InstallmentReceiptVoucherComponent} from "./installment-receipt-voucher/installment-receipt-voucher.component";


const routes: Routes = [

  {
    path: '',
    redirectTo: 'rent_contruct',
    pathMatch: 'full'
  },
  {
    path: 'rent_contruct',
    component: RentContructComponent
  },
  {
    path: 'rent_contruct_extension',
    component: RentContructExtensionComponent
  },
  {
    path: 'rent_receipt_voucher',
    component: RentReceiptVoucherComponent
  },
  {
    path: 'rent_contruct_settlement',
    component: RentContructSettlementComponent
  },
  {
    path: 'maintenance_and_services',
    component: MaintenanceAndServicesComponent
  },
  {
    path: 'booking_and_contracting',
    component: BookingAndContractingComponent
  },
  {
    path: 'installment_receipt_voucher',
    component: InstallmentReceiptVoucherComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RealestateContractsRoutingModule {
}
