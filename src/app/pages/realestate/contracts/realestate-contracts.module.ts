import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {RentContructComponent} from './rent-contruct/rent-contruct.component';
import {RentContructExtensionComponent} from './rent-contruct-extension/rent-contruct-extension.component';
import {RentReceiptVoucherComponent} from './rent-receipt-voucher/rent-receipt-voucher.component';
import {RentContructSettlementComponent} from './rent-contruct-settlement/rent-contruct-settlement.component';
import {MaintenanceAndServicesComponent} from './maintenance-and-services/maintenance-and-services.component';
import {BookingAndContractingComponent} from './booking-and-contracting/booking-and-contracting.component';
import {InstallmentReceiptVoucherComponent} from './installment-receipt-voucher/installment-receipt-voucher.component';
import {RealestateContractsRoutingModule} from "./realestate-contracts-routing.module";


@NgModule({
    declarations: [
        BookingAndContractingComponent,
        InstallmentReceiptVoucherComponent,
        MaintenanceAndServicesComponent,
        RentContructComponent,
        RentContructExtensionComponent,
        RentContructSettlementComponent,
        RentReceiptVoucherComponent,
    ],
    imports: [
        RealestateContractsRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class RealestateContractsModule {
}
