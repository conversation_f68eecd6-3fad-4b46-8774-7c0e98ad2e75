import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-realestate-home',
    templateUrl: './realestate-home.component.html',
    styleUrls: ['./realestate-home.component.scss'],
    standalone: false
})
export class realestateHomeComponent implements OnInit {
  // إحصائيات العقارات
  totalProperties: number = 0;
  rentedProperties: number = 0;
  availableProperties: number = 0;
  underMaintenanceProperties: number = 0;

  // إحصائيات العقود
  totalContracts: number = 0;
  activeContracts: number = 0;
  expiredContracts: number = 0;
  expiringContracts: number = 0;

  // إحصائيات المدفوعات
  totalRevenue: number = 0;
  collectedPayments: number = 0;
  pendingPayments: number = 0;
  overduePayments: number = 0;

  // بيانات الرسوم البيانية
  revenueByMonthData: any[] = [];
  propertiesByTypeData: any[] = [];
  contractStatusData: any[] = [];

  // العقود الأخيرة
  recentContracts: any[] = [];

  // المدفوعات الأخيرة
  recentPayments: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'إضافة عقار جديد', icon: 'fa-building', route: '/realestate/configuration/real_estate_units' },
    { title: 'إضافة عقد إيجار', icon: 'fa-file-contract', route: '/realestate/contracts/rent_contruct' },
    { title: 'سند قبض إيجار', icon: 'fa-receipt', route: '/realestate/contracts/rent_receipt_voucher' },
    { title: 'تمديد عقد إيجار', icon: 'fa-calendar-plus', route: '/realestate/contracts/rent_contruct_extension' },
    { title: 'تسوية عقد إيجار', icon: 'fa-handshake', route: '/realestate/contracts/rent_contruct_settlement' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات العقارات الوهمية
      this.totalProperties = 120;
      this.rentedProperties = 85;
      this.availableProperties = 28;
      this.underMaintenanceProperties = 7;

      // بيانات العقود الوهمية
      this.totalContracts = 95;
      this.activeContracts = 78;
      this.expiredContracts = 12;
      this.expiringContracts = 5;

      // بيانات المدفوعات الوهمية
      this.totalRevenue = 1250000;
      this.collectedPayments = 950000;
      this.pendingPayments = 200000;
      this.overduePayments = 100000;

      // بيانات الرسوم البيانية الوهمية
      this.revenueByMonthData = [
        { month: 'يناير', revenue: 120000 },
        { month: 'فبراير', revenue: 135000 },
        { month: 'مارس', revenue: 125000 },
        { month: 'أبريل', revenue: 140000 },
        { month: 'مايو', revenue: 150000 },
        { month: 'يونيو', revenue: 145000 }
      ];

      this.propertiesByTypeData = [
        { type: 'شقق', count: 65 },
        { type: 'فلل', count: 25 },
        { type: 'مكاتب', count: 20 },
        { type: 'محلات تجارية', count: 10 }
      ];

      this.contractStatusData = [
        { status: 'نشط', count: 78 },
        { status: 'منتهي', count: 12 },
        { status: 'ينتهي قريباً', count: 5 }
      ];

      // بيانات العقود الأخيرة الوهمية
      this.recentContracts = [
        { id: 'CNT-2023-001', property: 'شقة - الرياض، حي النزهة', tenant: 'أحمد محمد', startDate: '2023-01-15', endDate: '2024-01-14', value: 45000, status: 'نشط' },
        { id: 'CNT-2023-002', property: 'فيلا - جدة، حي الشاطئ', tenant: 'محمد علي', startDate: '2023-02-01', endDate: '2024-01-31', value: 85000, status: 'نشط' },
        { id: 'CNT-2023-003', property: 'مكتب - الرياض، شارع العليا', tenant: 'شركة الأفق', startDate: '2023-03-10', endDate: '2024-03-09', value: 65000, status: 'نشط' },
        { id: 'CNT-2023-004', property: 'محل تجاري - الدمام، شارع الملك فهد', tenant: 'مؤسسة النور', startDate: '2022-12-01', endDate: '2023-06-30', value: 55000, status: 'ينتهي قريباً' },
        { id: 'CNT-2023-005', property: 'شقة - الرياض، حي الملز', tenant: 'سارة أحمد', startDate: '2022-06-15', endDate: '2023-06-14', value: 40000, status: 'منتهي' }
      ];

      // بيانات المدفوعات الأخيرة الوهمية
      this.recentPayments = [
        { id: 'PAY-2023-001', contract: 'CNT-2023-001', tenant: 'أحمد محمد', date: '2023-06-15', amount: 11250, type: 'إيجار ربع سنوي', status: 'مدفوع' },
        { id: 'PAY-2023-002', contract: 'CNT-2023-002', tenant: 'محمد علي', date: '2023-06-01', amount: 21250, type: 'إيجار ربع سنوي', status: 'مدفوع' },
        { id: 'PAY-2023-003', contract: 'CNT-2023-003', tenant: 'شركة الأفق', date: '2023-06-10', amount: 16250, type: 'إيجار ربع سنوي', status: 'مدفوع' },
        { id: 'PAY-2023-004', contract: 'CNT-2023-004', tenant: 'مؤسسة النور', date: '2023-06-01', amount: 13750, type: 'إيجار ربع سنوي', status: 'متأخر' },
        { id: 'PAY-2023-005', contract: 'CNT-2023-005', tenant: 'سارة أحمد', date: '2023-06-14', amount: 10000, type: 'إيجار ربع سنوي', status: 'مستحق' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      propertyStats: this.http.get<any>('api/realestate/dashboard/properties'),
      contractStats: this.http.get<any>('api/realestate/dashboard/contracts'),
      paymentStats: this.http.get<any>('api/realestate/dashboard/payments'),
      recentContracts: this.http.get<any>('api/realestate/dashboard/recent-contracts'),
      recentPayments: this.http.get<any>('api/realestate/dashboard/recent-payments')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalProperties = results.propertyStats.total;
        this.rentedProperties = results.propertyStats.rented;
        this.availableProperties = results.propertyStats.available;
        this.underMaintenanceProperties = results.propertyStats.underMaintenance;

        this.totalContracts = results.contractStats.total;
        this.activeContracts = results.contractStats.active;
        this.expiredContracts = results.contractStats.expired;
        this.expiringContracts = results.contractStats.expiring;

        this.totalRevenue = results.paymentStats.total;
        this.collectedPayments = results.paymentStats.collected;
        this.pendingPayments = results.paymentStats.pending;
        this.overduePayments = results.paymentStats.overdue;

        this.recentContracts = results.recentContracts;
        this.recentPayments = results.recentPayments;

        // بيانات الرسوم البيانية
        this.revenueByMonthData = results.paymentStats.byMonth;
        this.propertiesByTypeData = results.propertyStats.byType;
        this.contractStatusData = results.contractStats.byStatus;
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة العقد
  getContractStatusClass(status: string): string {
    switch (status) {
      case 'نشط':
        return 'badge-light-success';
      case 'منتهي':
        return 'badge-light-danger';
      case 'ينتهي قريباً':
        return 'badge-light-warning';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون حالة الدفع
  getPaymentStatusClass(status: string): string {
    switch (status) {
      case 'مدفوع':
        return 'badge-light-success';
      case 'مستحق':
        return 'badge-light-warning';
      case 'متأخر':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
