import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {AccountStatementReportComponent} from "./account-statement-report/account-statement-report.component";
import {ContractsReportComponent} from "./contracts-report/contracts-report.component";
import {ExpiredContractsReportComponent} from "./expired-contracts-report/expired-contracts-report.component";
import {
    TotalAccountStatementReportComponent
} from "./total-account-statement-report/total-account-statement-report.component";
import {UncollectedPaymentsReportComponent} from "./uncollected-payments-report/uncollected-payments-report.component";
import {UnitAccountReportComponent} from "./unit-account-report/unit-account-report.component";
import {UnitsReportComponent} from "./units-report/units-report.component";
import {RealestateReportsRoutingModule} from "./realestate-reports-routing.module";


@NgModule({
    declarations: [
        AccountStatementReportComponent,
        ContractsReportComponent,
        ExpiredContractsReportComponent,
        TotalAccountStatementReportComponent,
        UncollectedPaymentsReportComponent,
        UnitAccountReportComponent,
        UnitsReportComponent,
    ],
    imports: [
        RealestateReportsRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class RealestateReportsModule {
}
