import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {ExpiredContractsReportComponent} from "./expired-contracts-report/expired-contracts-report.component";
import {UnitsReportComponent} from "./units-report/units-report.component";
import {
  TotalAccountStatementReportComponent
} from "./total-account-statement-report/total-account-statement-report.component";
import {AccountStatementReportComponent} from "./account-statement-report/account-statement-report.component";
import {ContractsReportComponent} from "./contracts-report/contracts-report.component";
import {UnitAccountReportComponent} from "./unit-account-report/unit-account-report.component";
import {UncollectedPaymentsReportComponent} from "./uncollected-payments-report/uncollected-payments-report.component";


const routes: Routes = [

  {
    path: '',
    redirectTo: 'expired_contracts_report',
    pathMatch: 'full'
  },
  {
    path: 'expired_contracts_report',
    component: ExpiredContractsReportComponent
  },
  {
    path: 'units_report',
    component: UnitsReportComponent
  },
  {
    path: 'total_account_statement_report',
    component: TotalAccountStatementReportComponent
  },
  {
    path: 'account_statement_report',
    component: AccountStatementReportComponent
  },
  {
    path: 'contracts_report',
    component: ContractsReportComponent
  },
  {
    path: 'unit_account_report',
    component: UnitAccountReportComponent
  },
  {
    path: 'uncollected_payments_report',
    component: UncollectedPaymentsReportComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RealestateReportsRoutingModule {
}
