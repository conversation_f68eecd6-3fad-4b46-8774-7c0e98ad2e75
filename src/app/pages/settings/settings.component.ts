import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { SettingsService } from './settings.service';

@Component({
    selector: 'app-settings',
    templateUrl: './settings.component.html',
    styleUrls: ['./settings.component.scss'],
    standalone: false
})
export class SettingsComponent implements OnInit, OnDestroy {

  settings: any = {};
  subscription = new Subscription();
  settingFrm: FormGroup;
  constructor(private settingService: SettingsService,
    private fb: FormBuilder,
    private cdk: ChangeDetectorRef) {
    this.initForm({});
  }


  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  initForm(model: any) {
    this.settingFrm = this.fb.group({
      enablePasswordReset: model.enablePasswordReset,
      weightUnit: model.weightUnit,
      volumeUnit: model.volumeUnit
    });
  }

  ngOnInit(): void {
    this.subscription.add(this.settingService.get().subscribe(r => {
      if (r.success) {
        this.settings = r.data;
        this.initForm(this.settings);
        this.cdk.detectChanges();
        this.subscription.add(this.settingFrm.valueChanges.subscribe(value => {
          this.save();
        }));
      }
    }));
  }


  save() {
    if (this.settingFrm.value) {
      this.subscription.add(this.settingService.update(this.settingFrm.value)
        .subscribe(r => {
          if (r.success) {

          }
        }));
    } else {
      this.settingFrm.markAllAsTouched();
    }
  }

}
