import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.settings}`;
  }


  get(): Observable<any> {
    return this.http.get<any>(this.baseUrl);
  }

  update(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form);
  }
}
