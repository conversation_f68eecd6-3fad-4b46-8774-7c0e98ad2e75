<div class="">
  <form [formGroup]="settingFrm">
    <div class="card mb-5 mb-xl-10">
      <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
        data-bs-target="#kt_account_connected_accounts" aria-expanded="true"
        aria-controls="kt_account_connected_accounts">
        <div class="card-title m-0">
          <h3 class="fw-bolder m-0" translate="SETTINGS.PERMISSIONS"></h3>
        </div>
      </div>
      <div class="card-body border-top p-9">
        <div class="row">
          <div class="form-group row">
            <div class="d-flex col-md-1">
              <div class="form-check form-check-custom form-check-solid form-check-success form-switch">
                <div class="form-check form-check-custom form-check-solid form-switch mb-2 ">
                  <input class="form-check-input" type="checkbox" formControlName="enablePasswordReset" />
                </div>
              </div>
            </div>
            <div class="d-flex flex-column col-md-4">
              <span class="fw-bold text-dark" translate="SETTINGS.PASSWORDRESET"></span>
              <div class="fs-7 fw-semibold text-muted" translate="SETTINGS.PASSWORDRESETDESC"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card mb-5 mb-xl-10">
      <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
        data-bs-target="#kt_account_connected_accounts" aria-expanded="true"
        aria-controls="kt_account_connected_accounts">
        <div class="card-title m-0">
          <h3 class="fw-bolder m-0" translate="SETTINGS.UNITS.TITLE"></h3>
        </div>
      </div>
      <div class="card-body border-top p-9">
        <div class="row">
          <div class="form-group row col-md-6">
            <div class="d-flex flex-column mb-5  col-md-4">
              <h4 class="fw-bold text-dark" translate="SETTINGS.UNITS.WEIGHT.TITLE"></h4>
              <div class="fs-7 fw-semibold text-muted" translate="SETTINGS.UNITS.WEIGHT.DESC"></div>
            </div>
            <div class="d-flex col-md-8">
              <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                <input class="form-check-input" type="radio" [value]="0" formControlName="weightUnit" />
                <div class="form-check-label">
                  {{ 'SETTINGS.UNITS.WEIGHT.Kilograms' | uppercase |translate}}
                </div>
              </div>
              <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                <input class="form-check-input" type="radio" [value]="1" formControlName="weightUnit" />
                <div class="form-check-label">
                  {{ 'SETTINGS.UNITS.WEIGHT.pounds' | uppercase |translate}}
                </div>
              </div>
            </div>
          </div>
          <div class="form-group row col-md-6">
            <div class="d-flex flex-column mb-5  col-md-4">
              <h4 class="fw-bold text-dark" translate="SETTINGS.UNITS.VOLUME.TITLE"></h4>
              <div class="fs-7 fw-semibold text-muted" translate="SETTINGS.UNITS.VOLUME.DESC"></div>
            </div>
            <div class="d-flex col-md-8">
              <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                <input class="form-check-input" type="radio" [value]="0" formControlName="volumeUnit" />
                <div class="form-check-label">
                  {{ 'SETTINGS.UNITS.VOLUME.METER' | uppercase |translate}}
                </div>
              </div>
              <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                <input class="form-check-input" type="radio" [value]="1" formControlName="volumeUnit" />
                <div class="form-check-label">
                  {{ 'SETTINGS.UNITS.VOLUME.FEET' | uppercase |translate}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>