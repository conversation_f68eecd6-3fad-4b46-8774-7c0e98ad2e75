import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PermissionListComponent } from './permission-list/permission-list.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { RolesListComponent } from './roles-list/roles-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { UsersCreateComponent } from './users/users-create/users-create.component';
import { UsersEditComponent } from './users/users-edit/users-edit.component';

var routes: Routes = [
  {
    path: '',
    component: PermissionListComponent
  },
  {
    path: 'roles',
    component: RolesListComponent
  },
  {
    path: 'users',
    loadChildren: () => import('./users/users.module').then(u => u.UsersModule)
  }
];


@NgModule({
  declarations: [
    PermissionListComponent,
    RolesListComponent,
    UsersCreateComponent,
    UsersEditComponent
  ],
  imports: [
    CommonModule,
    NgSelectModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    InlineSVGModule,
    TranslateModule
  ],
  exports: [RouterModule],
})
export class PermissionModule { }
