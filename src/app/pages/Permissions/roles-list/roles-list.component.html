<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
    data-bs-target="#kt_account_connected_accounts" aria-expanded="true" aria-controls="kt_account_connected_accounts">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">Roles</h3>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <div class="row">
      <div class="row col-md-12 mb-10">
        <div class="col-md-2">
          <strong>{{ 'COMMON.ROLE' | translate }}</strong>
        </div>
        <div class="col-md-5">
          <input [formControl]="roleNameCtrl" class="form-control form-control-solid"  />
        </div>
        <div class="col-md-2">
          <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="saveRole()">
            <ng-container *ngIf="!isLoading" >{{ 'COMMON.SUBMIT' | translate }}</ng-container>
            <ng-container *ngIf="isLoading">
              <span clas="indicator-progress" [style.display]="'block'">
                Please wait...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </ng-container>
          </button>
        </div>
      </div>
      <hr />
      <div class="row col-md-12">
        <div class="table table-responsive" *ngIf="roles">
          <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 table-hover">
            <thead class="table-header">
              <tr>
                <td>{{'COMMON.ROLE' | translate }}</td>
                <th class="text-end min-w-70px" translate="COMMON.ACTIONS"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let r of roles">
                <td>{{r.roleName}}</td>
                <td class="text-end">
                  <a href="#" class="btn btn-sm btn-light btn-active-light-primary" data-kt-menu-trigger="click"
                     data-kt-menu-placement="bottom-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                              fill="currentColor" />
                      </svg>
                    </span>
                  </a>
                  <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                       data-kt-menu="true">
                    <div class="menu-item px-3">
                      <a class="menu-link px-3" (click)="remove(r)" data-kt-company-table-filter="delete_row" translate="COMMON.DELETE">
                      </a>
                    </div>
                    <div class="menu-item px-3">
                      <a class="menu-link px-3" (click)="edit(r)" data-kt-company-table-filter="edit_row" translate="COMMON.EDIT">
                      </a>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
