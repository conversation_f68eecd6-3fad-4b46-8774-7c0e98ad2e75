import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { RolesService } from './roles.service';

@Component({
    selector: 'app-roles-list',
    templateUrl: './roles-list.component.html',
    styleUrls: ['./roles-list.component.scss'],
    standalone: false
})
export class RolesListComponent implements OnInit, OnDestroy {
  roleNameCtrl: FormControl;
  roles: any[];
  isLoading = false;
  subscription = new Subscription();
  itemToEdit: any;
  constructor(private fb: FormBuilder, private rolesService: RolesService,
    private cdk: ChangeDetectorRef) {
    this.roleNameCtrl = this.fb.control(null, [Validators.required]);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngOnInit(): void {
    this.subscription.add(this.rolesService.list()
      .subscribe(r => {
        if (r.success) {
          this.roles = r.data;
          this.cdk.detectChanges();
        }
      }));
  }
  saveRole() {
    if (this.roleNameCtrl.valid) {
      this.isLoading = true;
      if (!this.itemToEdit) {
        this.subscription.add(this.rolesService.create({ roleName: this.roleNameCtrl.value })
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.roles.push(r.data);
              this.roleNameCtrl.setValue(undefined);
              this.cdk.detectChanges();
            }
          }));
      } else {
        this.update();
      }
    } else {
      this.roleNameCtrl.markAsTouched();
    }
  }
  remove(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscription.add(this.rolesService.delete(item.id)
          .subscribe(r => {
            if (r.success) {
              this.roles = this.roles.filter(c => c.id != item.id);
              this.cdk.detectChanges();
            }
          }));
      }
    });
  }

  edit(item: any) {
    if (item) {
      this.itemToEdit = item;
      this.roleNameCtrl.setValue(item.roleName);
    }
  }

  update() {
    if (this.itemToEdit) {
      this.subscription.add(this.rolesService.update(this.itemToEdit.id, { roleName: this.roleNameCtrl.value })
        .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
        .subscribe(r => {
          if (r.success) {
            this.itemToEdit.roleName = this.roleNameCtrl.value;
            this.cdk.detectChanges();
            this.itemToEdit = undefined;
          }
        }));
    }
  }
}
