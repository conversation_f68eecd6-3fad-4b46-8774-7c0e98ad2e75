import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { finalize } from 'rxjs';
import { PermissionListService } from './permission-list.service';
import { TokenKeys } from 'src/app/modules/auth/services';

@Component({
    selector: 'app-permission-list',
    templateUrl: './permission-list.component.html',
    styleUrls: ['./permission-list.component.scss'],
    standalone: false
})
export class PermissionListComponent implements OnInit {

  roles: any[] = [];
  roleCtrl: FormControl;
  actions: any[] = [];
  modules: any[] = [];
  moduleActions: any[] = [];
  isLoading = false;
  private _isCheckAll: any;
  currentModule: any;
  constructor(private fb: FormBuilder, private permissionService: PermissionListService,
    private cdk: ChangeDetectorRef) {
    this.roleCtrl = this.fb.control(null, [Validators.required]);
  }

  get isCheckAll() {
    return this._isCheckAll;
  }
  set isCheckAll(value) {
    this._isCheckAll = value;
  }
  ngOnInit(): void {
    this.permissionService.init()
      .subscribe(result => {
        if (result.success) {
          this.roles = result.data.roles;
          this.actions = result.data.actions;
          this.modules = result.data.modules;
          this.cdk.detectChanges();
        }
      });
    this.roleCtrl.valueChanges.subscribe(val => {
      this.moduleActions = [];
      if (val) {
        this.permissionService.getByRole(val)
          .subscribe(result => {
            if (result.success) {
              this.isCheckAll = false;
              this.moduleActions = result.data;
              this.cdk.detectChanges();
            }
          });
      } else {
        this.cdk.detectChanges();
      }
    });
  }
  saveActions() {
    if (this.roleCtrl.valid) {
      if (!this.isLoading) {
        this.isLoading = true;
        this.moduleActions.forEach(m => {
          m.role = this.roleCtrl.value;
        });
        this.permissionService.create(this.moduleActions)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              localStorage.setItem(TokenKeys.permissions, JSON.stringify(r.data));
            }
          });
      }
    } else {
      this.moduleActions = [];
      this.roleCtrl.markAsTouched();
    }
  }

  getStatus(module: any, action: any) {
    const selectedRoleModule = this.moduleActions.find((m) => m.role == this.roleCtrl.value);
    if (selectedRoleModule) {
      // && a.actions.find((ac: any) => ac == action)
      const selectedModule = this.moduleActions.find((a) => a.module == module.value && a[action] == true);
      if (selectedModule) {
        return true;
      }
    }
    return false;
  }

  setStatus(event: any, module: any, action: any) {
    const value = event.target.checked;
    if (!value) {
      this._isCheckAll = false;
    }
    let selectedModule = this.moduleActions.find((a) => a.module == module.value);
    if (!selectedModule) {
      if (value) {
        //actions: [action]
        selectedModule = { module: module.value, role: this.roleCtrl.value };
        selectedModule[action] = true;
        this.moduleActions.push(selectedModule);
      }
    } else {
      selectedModule[action] = value;
      //const selectedAction = selectedModule.actions.find((a: any) => a == action);
      //if (value) {
      //  if (!selectedAction) {
      //    // selectedModule.actions.push(action);

      //  }
      //} else {
      //  selectedModule.actions = selectedModule.actions.filter((a: any) => a != selectedAction);
      //}
    }
    if (module.children?.length > 0) {
      module.children.forEach((c: any) => {
        this.setStatus(event, c, action);
      });
    }
  }

  clearActions() {
    this.moduleActions.forEach(m => {
      m.role = this.roleCtrl.value;
      m.actions = []
    });
    this.cdk.detectChanges();
  }

  checkAll(event: any) {
    const value = event.target.checked;
    if (value) {
      this.moduleActions = [];
      this.modules.forEach(m => {
        this.moduleActions.push({ module: m.value });
        if (m.children) {
          m.children.forEach((c: any) => {
            this.moduleActions.push({ module: c.value });
          });
        }
      });
      this.moduleActions.forEach(m => {
        m.role = this.roleCtrl.value;
        m.actions = this.actions
      });
    } else {
      this.moduleActions.forEach(m => {
        m.role = this.roleCtrl.value;
        m.actions = []
      });
    }
    this.cdk.detectChanges();
  }

  setAllStatus(event: any, action: any) {
    this.modules.forEach(m => {
      this.setStatus(event, m, action);
    });
    //const value = event.target.checked;
    //if (value) {
    //  this.modules.forEach(m => {
    //    let selectedModule = this.moduleActions.find((a) => a.module == m.value);
    //    if (!selectedModule) {
    //      this.moduleActions.push({ module: m.value });
    //    }
    //    this.moduleActions.forEach(m => {
    //      if (!m.actions) {
    //        m.actions = [];
    //      }
    //      m.actions.push(action);
    //    });
    //  });

    //} else {
    //  this.moduleActions.forEach(m => {
    //    m.actions = m.actions.filter((a: any) => a != action);
    //  });
    //}
  }

  getAllStatus(action: string): boolean {
    let childrenCount = 0;
    this.modules.forEach(m => {
      childrenCount += m.children ? m.children?.length : 0;
    });
    // m.actions.find((a: any) => a == action)
    return this.moduleActions.filter(m => m[action] == true).length == (this.modules.length + childrenCount);
  }

  showChildren(module: any) {
    this.currentModule = module;
  }
}
