<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
       data-bs-target="#kt_account_connected_accounts" aria-expanded="true" aria-controls="kt_account_connected_accounts">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">Permissions</h3>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <div class="row">
      <div class="col-md-12 row">
        <div class="col-md-4">
          <ng-select placeholder="Select Role" bindLabel="roleName" bindValue="roleName" [formControl]="roleCtrl"
                     [items]="roles" [virtualScroll]="true"></ng-select>
          <span class="ng-invalid" *ngIf="roleCtrl.invalid && roleCtrl.touched">You Should Select Role </span>
        </div>
        <!--<div class="col-md-4 my-auto">
          <label class="form-check form-switch form-check-custom form-check-solid">
            <input class="form-check-input w-30px h-20px" type="checkbox" name="notifications" [checked]="isCheckAll"
              (change)="checkAll($event)" />
            <span class="form-check-label text-muted fs-6">Select All Actions</span>
          </label>
        </div>-->
      </div>
      <div class="table table-responsive " *ngIf="actions">
        <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 table-hover">
          <thead class="table-header">
            <tr>
              <th></th>
              <th> Modules </th>
              <th>
                <label class="form-check form-switch form-check-custom form-check-solid">
                  <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                         [checked]="getAllStatus('displayAction')" (change)="setAllStatus($event, 'displayAction')" />
                  <span class="px-2">Display</span>
                </label>
              </th>
              <th>
                <label class="form-check form-switch form-check-custom form-check-solid">
                  <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                         [checked]="getAllStatus('createAction')" (change)="setAllStatus($event, 'createAction')" />
                  <span class="px-2">Create</span>
                </label>
              </th>
              <th>
                <label class="form-check form-switch form-check-custom form-check-solid">
                  <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                         [checked]="getAllStatus('updateAction')" (change)="setAllStatus($event, 'updateAction')" />
                  <span class="px-2">Update</span>
                </label>
              </th>
              <th>
                <label class="form-check form-switch form-check-custom form-check-solid">
                  <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                         [checked]="getAllStatus('deleteAction')" (change)="setAllStatus($event, 'deleteAction')" />
                  <span class="px-2">Delete</span>
                </label>
              </th>
              <th>
                <label class="form-check form-switch form-check-custom form-check-solid">
                  <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                         [checked]="getAllStatus('printAction')" (change)="setAllStatus($event, 'printAction')" />
                  <span class="px-2">Print</span>
                </label>
              </th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let m of modules">
              <tr>
                <td class="table-icons">
                  <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr013.svg'"
                        *ngIf="currentModule != m && m.children?.length > 0" (click)="showChildren(m)"
                        class="svg-icon svg-icon-1 rotate-180"></span>
                  <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr014.svg'" *ngIf="currentModule == m"
                        (click)="showChildren(undefined)" class="svg-icon svg-icon-1 rotate-180"></span>
                </td>
                <td>{{ m.name }}</td>
                <td>
                  <label class="form-check form-switch form-check-custom form-check-solid">
                    <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                           [checked]="getStatus(m, 'displayAction')" (change)="setStatus($event, m, 'displayAction')" />
                  </label>
                </td>
                <td>
                  <label class="form-check form-switch form-check-custom form-check-solid">
                    <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                           [checked]="getStatus(m, 'createAction')" (change)="setStatus($event, m, 'createAction')" />
                  </label>
                </td>
                <td>
                  <label class="form-check form-switch form-check-custom form-check-solid">
                    <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                           [checked]="getStatus(m, 'updateAction')" (change)="setStatus($event, m, 'updateAction')" />
                  </label>
                </td>
                <td>
                  <label class="form-check form-switch form-check-custom form-check-solid">
                    <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                           [checked]="getStatus(m, 'deleteAction')" (change)="setStatus($event, m, 'deleteAction')" />
                  </label>
                </td>
                <td>
                  <label class="form-check form-switch form-check-custom form-check-solid">
                    <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                           [checked]="getStatus(m, 'printAction')" (change)="setStatus($event, m, 'printAction')" />
                  </label>
                </td>
              </tr>
              <ng-container *ngIf="currentModule == m">
                <tr *ngFor="let c of m.children" class="app-sub-modules">
                  <td></td>
                  <td><span style="padding:25px">{{ c.name }}</span></td>
                  <td>
                    <label class="form-check form-switch form-check-custom form-check-solid">
                      <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                             [checked]="getStatus(c, 'displayAction')" (change)="setStatus($event, c, 'displayAction')" />
                    </label>
                  </td>
                  <td>
                    <label class="form-check form-switch form-check-custom form-check-solid">
                      <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                             [checked]="getStatus(c, 'createAction')" (change)="setStatus($event, c, 'createAction')" />
                    </label>
                  </td>
                  <td>
                    <label class="form-check form-switch form-check-custom form-check-solid">
                      <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                             [checked]="getStatus(c, 'updateAction')" (change)="setStatus($event, c, 'updateAction')" />
                    </label>
                  </td>
                  <td>
                    <label class="form-check form-switch form-check-custom form-check-solid">
                      <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                             [checked]="getStatus(c, 'deleteAction')" (change)="setStatus($event, c, 'deleteAction')" />
                    </label>
                  </td>
                  <td>
                    <label class="form-check form-switch form-check-custom form-check-solid">
                      <input class="form-check-input w-30px h-20px" type="checkbox" name="permissions"
                             [checked]="getStatus(c, 'printAction')" (change)="setStatus($event, c, 'printAction')" />
                    </label>
                  </td>
                </tr>
              </ng-container>
            </ng-container>
          </tbody>
        </table>
        <hr />
        <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="saveActions()">
          <ng-container *ngIf="!isLoading">Save Changes</ng-container>
          <ng-container *ngIf="isLoading">
            <span clas="indicator-progress" [style.display]="'block'">
              Please wait...{{ " " }}
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </ng-container>
        </button>
        <button class="btn btn-outline-secondary" type="reset" (click)="clearActions()">
          Reset
        </button>
      </div>
    </div>
  </div>
</div>
