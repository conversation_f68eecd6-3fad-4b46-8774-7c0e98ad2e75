import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PermissionListService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.permissions}`;
  }

  getByRole(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  init(page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString()
      }
    });
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map((result: any) => {
        if (result.success) {

        }
        return result;
      }));
  }
}
