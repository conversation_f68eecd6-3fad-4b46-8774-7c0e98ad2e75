import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UsersService {


  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.users}`;
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString()
      }
    });
  }

  getRoles(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'roles/list');
  }

  getfalconusers(): Observable<any> {
    return this.http.get<any>('api/FalconUsers/Names');
  }

  getEmployees(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form);
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form);
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id);
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }

  filter(form: { roleName: any}): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }
}
