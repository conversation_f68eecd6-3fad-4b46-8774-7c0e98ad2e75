<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
       data-bs-target="#kt_account_connected_accounts" aria-expanded="true" aria-controls="kt_account_connected_accounts">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">Edit User</h3>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <div class="row">
      <form [formGroup]="newUserFrm" class="form" action="#" id="kt_modal_add_company_form"
            data-kt-redirect="../../demo1/dist/apps/Companies/list.html">

        <div class="me-n7 pe-7" id="kt_modal_add_company">
        
           <div class="row">
            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">

              <label class="required form-label">الاسم الأول</label>
              <input type="text" class="form-control"  name="firstName" value=""
                     formControlName="firstName" />

            </div>

            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">

              <label class="required form-label">الاسم الثاني</label>
              <input type="text" class="form-control"  name="lastName" value=""
                     formControlName="lastName" />

            </div>

          </div>

          <div class="row">
            <!--begin::Col-->
            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
              <!--begin::Label-->
              <label class="required form-label">الجوال</label>
              <!--end::Label-->
              <!--begin::Input-->
              <input class="form-control"  name="phoneNumber" value=""
                     formControlName="phoneNumber" [maxLength]="11" minlength="11" />
              <!--end::Input-->
            </div>

            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
              <label class="form-label">
                <span class="required">المجموعة</span>
              </label>
              <ng-select [multiple]="true" formControlName="roles" bindLabel="roleName" bindValue="roleName" [items]="roles"></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
              <label class="form-label">مستخدم فالكون</label>
              <ng-select formControlName="falconuserid" bindLabel="name" bindValue="id"
                [items]="falconuserid"></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
              <label class="required form-label">الموظف</label>
              <ng-select formControlName="employeeId" bindLabel="nameAr" bindValue="id"
                [items]="employeeId"></ng-select>
            </div>
          </div>


          <!--<div class="row g-9 mb-7" *ngIf="!editMode">
  <div class="fv-row col-md-6">-->
          <!--begin::Label-->
          <!--<label class="form-label">
    <span class="required">البريد الالكتروني</span>
    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
       title="Email address must be active"></i>
  </label>-->
          <!--end::Label-->
          <!--begin::Input-->
          <!--<input type="email" class="form-control form-control-solid"  name="email" value=""
  formControlName="email" />-->
          <!--end::Input-->
          <!--</div>
    <div class="col-md-6 fv-row">
      <label class="required form-label">كلمة المرور</label>
      <input type="password" class="form-control form-control-solid"  name="password"
             formControlName="password" />
    </div>
  </div>-->
          <!-- <div class="d-flex flex-column mb-7 fv-row">
    <label class="required form-label">العنوان</label>
    <input class="form-control form-control-solid"  name="address" value=""
      formControlName="address" />
  </div> -->
          <!-- <div class="fv-row mb-15">
    <label class="form-label">Description</label>
    <input type="text" class="form-control form-control-solid"  name="description" />
  </div> -->
          <div class="row">
            <div class="col-md-12 text-right">
              <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="save()">
                <ng-container *ngIf="!isLoading">Save Changes</ng-container>
                <ng-container *ngIf="isLoading">
                  <span clas="indicator-progress" [style.display]="'block'">
                    Please wait...{{ " " }}
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                  </span>
                </ng-container>
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>


