import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { UsersService } from '../users.service';

@Component({
    selector: 'app-users-edit',
    templateUrl: './users-edit.component.html',
    styleUrls: ['./users-edit.component.scss'],
    standalone: false
})
export class UsersEditComponent implements OnInit {

  roles: any[] = [];
  falconuserid: any[] = [];
  subscriptions = new Subscription();
  newUserFrm: FormGroup;
  isLoading: boolean = false;
  employeeId: any[] = [];
  constructor(private fb: FormBuilder,
    private userService: UsersService,
    private router: Router,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef) {
    this.newUserFrm = this.fb.group({
      id: null,
      firstName: [null, Validators.required],
      lastName: [null, Validators.required],
      phoneNumber: [null, [Validators.required, Validators.maxLength(11)]],
      roles: [null],
      falconuserid: [null],
      employeeId: [null],
    });
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.userService.details(id).subscribe(r => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cd.detectChanges();
          }
        }));
      this.subscriptions.add(this.userService.getfalconusers().subscribe(r => {
        if (r.success) {
          this.falconuserid = r.data;
          this.cd.detectChanges();
        }
      }));
    } else {
      this.router.navigate(['/permissions/users']);
    }
    this.subscriptions.add(this.userService.getEmployees().subscribe(r => {
      if (r.success) {
        this.employeeId = r.data;
        this.cd.detectChanges();
      }
    }));
  }

  fillForm(item: any) {
    this.newUserFrm.get("id")?.setValue(item.id);
    this.newUserFrm.get("firstName")?.setValue(item.firstName);
    this.newUserFrm.get("lastName")?.setValue(item.lastName);
    this.newUserFrm.get("phoneNumber")?.setValue(item.phoneNumber);
    this.newUserFrm.get("roles")?.setValue(item.roles.map((r: any) => r.roleName));
    this.newUserFrm.get("falconuserid")?.setValue(item.falconUserId);
    this.newUserFrm.get("employeeId")?.setValue(item.employeeId);
  }
  save() {
    if (this.newUserFrm.valid) {
      let form = this.newUserFrm.value
    
      this.subscriptions.add(this.userService.update(form.id, form)
        .pipe(finalize(() => { this.isLoading = false; this.cd.detectChanges(); }))
        .subscribe(r => {
          if (r.success) {
            this.router.navigate(['/permissions/users']);
          }
        }));
    } else {
      this.newUserFrm.markAllAsTouched();
    }
  }
}
