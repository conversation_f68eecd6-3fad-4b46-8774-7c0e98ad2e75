import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { UsersListComponent } from './users-list/users-list.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { UsersCreateComponent } from './users-create/users-create.component';
import { UsersEditComponent } from './users-edit/users-edit.component';

@NgModule({
  declarations: [UsersListComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: UsersListComponent,
      },
      {
        path: 'create',
        component: UsersCreateComponent,
      },
      {
        path: 'edit/:id',
        component: UsersEditComponent,
      },
      {
        path: ':id',
        component: UsersListComponent,
      },
    ]),
    ReactiveFormsModule,
    NgSelectModule,
    NgbPaginationModule
  ],
})
export class UsersModule { }
