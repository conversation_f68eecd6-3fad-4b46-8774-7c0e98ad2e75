import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { UsersService } from '../users.service';

@Component({
    selector: 'app-users-create',
    templateUrl: './users-create.component.html',
    styleUrls: ['./users-create.component.scss'],
    standalone: false
})
export class UsersCreateComponent implements OnInit, OnDestroy {

  roles: any[] = [];
  falconuserid: any[] = [];
  subscriptions = new Subscription();
  newUserFrm: FormGroup;
  isLoading = false;
  employeeId: any[] = [];
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private userService: UsersService,
    private router: Router) {
    this.newUserFrm = this.fb.group({
      firstName: [null, Validators.required],
      lastName: [null, Validators.required],
      email: [null, [Validators.email, Validators.required]],
      phoneNumber: [null, [Validators.required, Validators.maxLength(11)]],
      password: [null, Validators.required],
      roles: [null],
      falconuserid:[null],
      employeeId:[null],
    });
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.userService.getRoles().subscribe(r => {
      if (r.success) {
        this.roles = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscriptions.add(this.userService.getfalconusers().subscribe(r => {
      if (r.success) {
        this.falconuserid = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscriptions.add(this.userService.getEmployees().subscribe(r => {
      if (r.success) {
        this.employeeId = r.data;
        this.cdk.detectChanges();
      }
    }));
  }
  save() {
    if (this.newUserFrm.valid) {
      let form = this.newUserFrm.value
     
      this.subscriptions.add(this.userService.create(form)
        .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
        .subscribe(r => {
          if (r.success) {
            this.router.navigate(['/permissions/users']);
          }
        }));
    } else {
      this.newUserFrm.markAllAsTouched();
    }
  }
}
