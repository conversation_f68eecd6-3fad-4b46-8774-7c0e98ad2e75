import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';
import { UsersService } from '../users.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';


@Component({
    selector: 'app-userslist',
    templateUrl: './users-list.component.html',
    styleUrls: ['./users-list.component.scss'],
    standalone: false
})
export class UsersListComponent implements OnInit, OnDestroy {
  users: any[] = [];
  roles: any[] = [];
  subscriptions = new Subscription();
  searchCtrl: FormControl;
  roleFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  _currentPage = 1;
  itemsCount = 0;
  pagesCount = 0;
  constructor(private fb: FormBuilder,
    private route: ActivatedRoute,
    private userService: UsersService,
    private cd: ChangeDetectorRef) {
    this.searchCtrl = this.fb.control(null);
    this.roleFilterCtrl = this.fb.control(null);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.userService.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.userService.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.userService.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.userService.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }

  remove(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.userService.delete(item.id)
          .subscribe(r => {
            if (r.success) {
              this.users = this.users.filter(c => c.id != item.id);
              this.cd.detectChanges();
            }
          }));
      }
    });
  }
  activate(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد ' + (item.isActive ? 'إلغاء الحظر' : 'الحظر'),
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'تجاهل',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.userService.activate(item.id)
          .subscribe(r => {
            if (r.success) {
              item.isBlocked = !item.isBlocked;
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  filterUsers() {
    this.subscriptions.add(this.userService.filter({ 'roleName': this.roleFilterCtrl.value }).subscribe(r => {
      if (r.success) {
        this.loadData(r);
        this.itemsCount = r.data.itemsCount;
      }
    }));
  }

  getRoleName(r: string[]) {
    return r.map((role: any) => role.roleName).join(',');
  }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(this.userService.list('', value).subscribe(r => {
      if (r.success) {
        this.users = r.data;
        this.cd.detectChanges();
        MenuComponent.reinitialization();
      }
    }));
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.users = r.data;
    this.roles = r.info;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    } if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }
}
