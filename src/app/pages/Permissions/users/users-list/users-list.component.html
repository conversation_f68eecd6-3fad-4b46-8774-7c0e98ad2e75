<!--begin::Card-->
<div class="card">
  <!--begin::Card header-->
  <div class="card-header border-0 pt-6">
    <!--begin::Card title-->
    <div class="card-title">
      <!--begin::Search-->
      <div class="d-flex align-items-center position-relative my-1">
        <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
        <span class="svg-icon svg-icon-1 position-absolute ms-6">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
              transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
            <path
              d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
              fill="currentColor" />
          </svg>
        </span>
        <!--end::Svg Icon-->
        <input type="text" data-kt-company-table-filter="search" class="form-control form-control-solid w-250px ps-15"
          placeholder="البحث فى المستخدمين" [formControl]="searchCtrl" />
      </div>
      <!--end::Search-->
    </div>
    <!--begin::Card title-->
    <!--begin::Card toolbar-->
    <div class="card-toolbar">
      <!--begin::Toolbar-->
      <div class="d-flex justify-content-end" data-kt-company-table-toolbar="base">
        <!--begin::Filter-->
        <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-start">
          <!--begin::Svg Icon | path: icons/duotune/general/gen031.svg-->
          <span class="svg-icon svg-icon-2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                fill="currentColor" />
            </svg>
          </span>
          <!--end::Svg Icon-->تنقية النتائج</button>
        <!--begin::Menu 1-->
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true" id="kt-toolbar-filter">
          <!--begin::Header-->
          <div class="px-7 py-5">
            <div class="fs-4 text-dark fw-bold">الخيارات</div>
          </div>
          <!--end::Header-->
          <!--begin::Separator-->
          <div class="separator border-gray-200"></div>
          <!--end::Separator-->
          <!--begin::Content-->
          <div class="px-7 py-5">
            <!--begin::Input group-->
            <div class="mb-10">
              <!--begin::Label-->
              <label class="form-label fs-5 fw-semibold mb-3">المجموعة</label>
              <!--end::Label-->
              <!--begin::Options-->
              <div class="d-flex flex-column flex-wrap fw-semibold" data-kt-customer-table-filter="payment_type">
                <!--begin::Option-->
                <label class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                  <input [formControl]="roleFilterCtrl" class="form-check-input" type="radio" name="payment_type"
                    value="All" checked="checked" />
                  <span class="form-check-label text-gray-600">الجميع</span>
                </label>
                <!--end::Option-->
                <!--begin::Option-->
                <label class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5"
                  *ngFor="let role of roles">
                  <input [formControl]="roleFilterCtrl" class="form-check-input" type="radio" name="payment_type"
                    value="{{role.id}}" />
                  <span class="form-check-label text-gray-600">{{role.roleName}}</span>
                </label>
                <!--end::Option-->
              </div>
              <!--end::Options-->
            </div>
            <!--end::Input group-->
            <!--begin::Actions-->
            <div class="d-flex justify-content-end">
              <button type="reset" class="btn btn-light btn-active-light-primary me-2" data-kt-menu-dismiss="true"
                data-kt-customer-table-filter="reset">تجاهل</button>
              <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                data-kt-customer-table-filter="filter" (click)="filterUsers()">تطبيق</button>
            </div>
            <!--end::Actions-->
          </div>
          <!--end::Content-->
        </div>
        <!--begin::Add company-->
        <button type="button" class="btn btn-primary" routerLink="/permissions/users/create">
          إضافة جديد</button>
        <!--end::Add company-->

      </div>
      <!--end::Toolbar-->
      <!--begin::Group actions-->
      <div class="d-flex justify-content-end align-items-center d-none" data-kt-company-table-toolbar="selected">
        <div class="fw-bold me-5">
          <span class="me-2" data-kt-company-table-select="selected_count"></span>Selected
        </div>
        <button type="button" class="btn btn-danger" data-kt-company-table-select="delete_selected">Delete
          Selected</button>
      </div>
      <!--end::Group actions-->
    </div>
    <!--end::Card toolbar-->
  </div>
  <!--end::Card header-->
  <!--begin::Card body-->
  <div class="card-body pt-0">
    <!--begin::Table-->
    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_Companies_table">
      <!--begin::Table head-->
      <thead>
        <!--begin::Table row-->
        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
          <th class="w-10px pe-2">
            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
              <input class="form-check-input" type="checkbox" data-kt-check="true"
                data-kt-check-target="#kt_Companies_table .form-check-input" value="1" />
            </div>
          </th>
          <th class="min-w-125px">اسم المستخدم</th>
          <th class="min-w-125px">البريد الالكتروني</th>
          <th class="min-w-125px">الجوال</th>
          <th class="min-w-125px">المجموعة</th>
          <th class="min-w-125px">الحالة</th>
          <th class="min-w-125px">مستخدم فالكون</th>
          <th class="min-w-125px">الموظف</th>
          <th class="text-end min-w-70px">الإجراءات</th>
        </tr>
        <!--end::Table row-->
      </thead>
      <!--end::Table head-->
      <tbody class="fw-semibold text-gray-600">
        <tr *ngFor="let item of users">
          <td>
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input" type="checkbox" value="1" />
            </div>
          </td>
          <td>
            <a class="text-gray-800 text-hover-primary mb-1">
              {{item.fullName }}
            </a>
          </td>
          <td>
            <a class="text-gray-600 text-hover-primary mb-1">{{ item.email }}</a>
          </td>
          <td> {{ item.phoneNumber }} </td>
          <!-- <td>{{item.createdDate | date:'dd/MM/yyyy'}}</td> -->
          <td>
            <span class="badge badge-primary mx-1" *ngFor="let r of item.roles">{{ r.roleName }}</span>
          </td>
          <td>
            <span *ngIf="item.isBlocked" class="badge badge-light-danger">محظور</span>
            <span *ngIf="!item.isBlocked" class="badge badge-light-success">مفعل</span>
          </td>

          <td>
            <a class="text-gray-800 text-hover-primary mb-1">
              {{item.falconUserId }}
            </a>
          </td>
          
          <td>
            <a class="text-gray-800 text-hover-primary mb-1">
              {{item.employeeId }}
            </a>
          </td>

          <td class="text-end">
            <a class="btn btn-sm btn-light btn-active-light-primary" data-kt-menu-trigger="click"
              data-kt-menu-placement="bottom-end">
              Actions
              <!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
              <span class="svg-icon svg-icon-5 m-0">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                    fill="currentColor" />
                </svg>
              </span>
              <!--end::Svg Icon-->
            </a>
            <!--begin::Menu-->
            <div
              class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
              data-kt-menu="true">
              <div class="menu-item px-3">
                <a class="menu-link px-3" (click)="activate(item)"
                  data-kt-company-table-filter="delete_row">{{item.isBlocked ? 'إلغاء الحظر' : 'حظر'}}</a>
              </div>
              <!--begin::Menu item-->
              <div class="menu-item px-3">
                <a [routerLink]="['/permissions/users/edit/', item.id ]" class="menu-link px-3">تحديث</a>
              </div>
              <!--end::Menu item-->
              <!--begin::Menu item-->
              <div class="menu-item px-3">
                <a class="menu-link px-3" (click)="remove(item)" data-kt-company-table-filter="delete_row">حذف</a>
              </div>
              <!--end::Menu item-->
            </div>
            <!--end::Menu-->
          </td>
        </tr>
      </tbody>
    </table>
    <!--end::Table-->
    <ngb-pagination *ngIf="pagesCount > 1" [collectionSize]="itemsCount" [(page)]="currentPage"
      size="lg"></ngb-pagination>
  </div>
  <!--end::Card body-->
</div>



<!--end::Card-->
<!--begin::Modals-->
<!--begin::Modal - Companies - Add-->

<!--end::Modal - Companies - Add-->
<!--begin::Modal - Adjust Balance-->
<div class="modal fade" id="kt_Companies_export_modal" tabindex="-1" aria-hidden="true">
  <!--begin::Modal dialog-->
  <div class="modal-dialog modal-dialog-centered mw-650px">
    <!--begin::Modal content-->
    <div class="modal-content">
      <!--begin::Modal header-->
      <div class="modal-header">
        <!--begin::Modal title-->
        <h2 class="fw-bold">Export Companies</h2>
        <!--end::Modal title-->
        <!--begin::Close-->
        <div id="kt_Companies_export_close" class="btn btn-icon btn-sm btn-active-icon-primary">
          <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
          <span class="svg-icon svg-icon-1">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
                fill="currentColor" />
              <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                fill="currentColor" />
            </svg>
          </span>
          <!--end::Svg Icon-->
        </div>
        <!--end::Close-->
      </div>
      <!--end::Modal header-->
      <!--begin::Modal body-->
      <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
        <!--begin::Form-->
        <form id="kt_Companies_export_form" class="form" action="#">
          <!--begin::Input group-->
          <div class="fv-row mb-10">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Select Export Format:</label>
            <!--end::Label-->
            <!--begin::Input-->
            <select data-control="select2" data-placeholder="Select a format" data-hide-search="true" name="format"
              class="form-select form-select-solid">
              <option value="excell">Excel</option>
              <option value="pdf">PDF</option>
              <option value="cvs">CVS</option>
              <option value="zip">ZIP</option>
            </select>
            <!--end::Input-->
          </div>
          <!--end::Input group-->
          <!--begin::Input group-->
          <div class="fv-row mb-10">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Select Date Range:</label>
            <!--end::Label-->
            <!--begin::Input-->
            <input class="form-control form-control-solid" placeholder="Pick a date" name="date" />
            <!--end::Input-->
          </div>
          <!--end::Input group-->
          <!--begin::Row-->
          <div class="row fv-row mb-15">
            <!--begin::Label-->
            <label class="fs-5 fw-semibold form-label mb-5">Payment Type:</label>
            <!--end::Label-->
            <!--begin::Radio group-->
            <div class="d-flex flex-column">
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="1" checked="checked" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">All</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="2" checked="checked" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">Visa</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid mb-3">
                <input class="form-check-input" type="checkbox" value="3" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">Mastercard</span>
              </label>
              <!--end::Radio button-->
              <!--begin::Radio button-->
              <label class="form-check form-check-custom form-check-sm form-check-solid">
                <input class="form-check-input" type="checkbox" value="4" name="payment_type" />
                <span class="form-check-label text-gray-600 fw-semibold">American Express</span>
              </label>
              <!--end::Radio button-->
            </div>
            <!--end::Input group-->
          </div>
          <!--end::Row-->
          <!--begin::Actions-->
          <div class="text-center">
            <button type="reset" id="kt_Companies_export_cancel" class="btn btn-light me-3">Discard</button>
            <button type="submit" id="kt_Companies_export_submit" class="btn btn-primary">
              <span class="indicator-label">Submit</span>
              <span class="indicator-progress">Please wait...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
            </button>
          </div>
          <!--end::Actions-->
        </form>
        <!--end::Form-->
      </div>
      <!--end::Modal body-->
    </div>
    <!--end::Modal content-->
  </div>
  <!--end::Modal dialog-->
</div>
<!--end::Modal - New Card-->
<!--end::Modals-->