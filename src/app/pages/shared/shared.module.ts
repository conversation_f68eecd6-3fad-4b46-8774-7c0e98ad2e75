import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { WidgetsModule } from '../../_metronic/partials/content/widgets/widgets.module';
import {
  DxDataGridModule,
  DxListModule,
  DxDropDownBoxModule,
  DxTagBoxModule,
  DxSelectBoxModule,
  DxCheckBoxModule,
  DxTextBoxModule,
  DxNumberBoxModule,
  DxSwitchModule,
  DxDateBoxModule,
  DxButtonModule,
  DxDropDownButtonModule,
  DxSortableModule,
  DxScrollViewModule,
  DxFormModule,
  DxTextAreaModule,
  DxTabPanelModule,
  DxTabsModule,
  DxLoadPanelModule,
} from 'devextreme-angular';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { TranslateModule } from '@ngx-translate/core';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { PermissionsModule } from '../../directives/permissions/permissions.module';
import { RouterModule } from '@angular/router';
import { ModalsModule } from '../../_metronic/partials';
import { SendSmsComponent } from './send-sms/send-sms/send-sms.component';
import { SendEmailComponent } from './send-email/send-email/send-email.component';
import { SendTemplateComponent } from './send-whatsapp/send-template/send-template.component';
import { FormPhotoComponent } from './form-photo/form-photo.component';
import { DxFileUploaderModule } from 'devextreme-angular/ui/file-uploader';

@NgModule({
  exports: [
    CommonModule,
    NgSelectModule,
    ReactiveFormsModule,
    InlineSVGModule,
    TranslateModule,
    WidgetsModule,
    NgbPaginationModule,
    PermissionsModule,
    DxDataGridModule,
    DxListModule,
    DxDropDownBoxModule,
    DxTagBoxModule,
    DxSelectBoxModule,
    DxCheckBoxModule,
    DxTextBoxModule,
    DxNumberBoxModule,
    DxSwitchModule,
    DxDateBoxModule,
    DxButtonModule,
    DxDropDownButtonModule,
    FormsModule,
    DxSortableModule,
    ModalsModule,
    DxFormModule,
    DxTabPanelModule,
    DxTabsModule,
    SendSmsComponent,
    SendEmailComponent,
    SendTemplateComponent,
    DxLoadPanelModule,
    FormPhotoComponent,
  ],
  declarations: [
    SendSmsComponent,
    SendEmailComponent,
    SendTemplateComponent,
    FormPhotoComponent,
  ],
  imports: [
    CommonModule,
    DxScrollViewModule,
    NgSelectModule,
    ReactiveFormsModule,
    InlineSVGModule,
    TranslateModule,
    WidgetsModule,
    NgbPaginationModule,
    PermissionsModule,
    DxDataGridModule,
    DxListModule,
    DxDropDownBoxModule,
    DxTagBoxModule,
    DxSelectBoxModule,
    DxCheckBoxModule,
    DxTextBoxModule,
    DxNumberBoxModule,
    DxSwitchModule,
    DxDateBoxModule,
    DxTagBoxModule,
    DxButtonModule,
    DxDropDownButtonModule,
    DxTextAreaModule,
    RouterModule,
    DxLoadPanelModule,
    DxFileUploaderModule,
  ],
})
export class SharedModule {}
