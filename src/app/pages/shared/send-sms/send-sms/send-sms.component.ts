import {ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit} from '@angular/core';
import { finalize, Subscription } from 'rxjs';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import * as ExcelJS from 'exceljs';
import { SMSService } from '../sms.service';

@Component({
    selector: 'app-send-sms',
    templateUrl: './send-sms.component.html',
    styleUrls: ['./send-sms.component.scss'],
    standalone: false
})
export class SendSmsComponent implements OnInit, OnChanges {
  templates: any[] = [];
  subscriptions = new Subscription();
  phoneNumbers: any[] = [];
  item: any = null;
  sendForm: FormGroup;
  isLoading = false;
  columns: any[] = [];
  showExcelPreview = false;
  /*excelData: any[] = [];*/
  currentExcelPage: any = 0;
  pageSize: any = 10;
  isPreviewing: boolean = false;
  headerCol = false;
  bodyFromExcel = false;
  isUploading: boolean = false;
  recipientTypes: any[] = [];
  showSallaPreview = false;
  /* sallaCustomers: any[] = [];*/
  ItemsCount: any;
  /*  sallaPagesCount: any;*/
  headerSallaCol = false;
  headerZidCol = false;
  headerCustomerCol = false;
  /* sallaColumns: any[] = [];*/
  /* zidCustomers: any[] = [];*/
  /*  zidColumns: any[] = [];*/
  PagesCount: any;
  /*  zidItemsCount: any;*/
  showZidPreview: boolean = false;
  @Input() recipientType: any;
  @Input() griddata: any[] = [];
  showCustomersPreview: boolean;
  /*  customersPagesCount: any;*/
  /*  customerItemsCount: any;*/
  /*  customersColumns: any = [];*/
  providers: any[] = [{ key: 0, value: "Mobily" }, { key: 1, value: "UltrMsg" }, { key: 2, value: "Jawaly" }, { key: 3, value: "Yamamah" }, { key: 4, value: "Hisms" }, { key: 5, value: "Msegat" }, { key: 6, value: "Oursms" }, { key: 7, value: "Unifonic" }, { key: 8, value: "Zain" }];
  smstemplates: any[]
  showSuppliersPreview: boolean;
  /*  suppliersPagesCount: any;*/
  /*  supplierItemsCount: any;*/
  /*  suppliersColumns: any = [];*/


  showleadsPreview: boolean;
  /*leadsPagesCount: any;*/
  /*  leadsItemsCount: any;*/
  /*  leadsColumns: any = [];*/


  showstaffPreview: boolean;
  /*  staffPagesCount: any;*/
  /*  staffItemsCount: any;*/
  /*  staffColumns: any = [];*/


  selectedItemKeys: any;
  currentFilter: any;
  gridBoxValue: number[] = [];
  balance: any;
  resultMessage: any;

  constructor(private myService: SMSService,
    private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private toastr: ToastrService) {

    this.recipientTypes = [
      { text: 'Individual', value: 'individual' },
      { text: 'Excel', value: 'excel' },
      { text: 'Customers', value: 'Customers' },
      { text: 'Suppliers', value: 'Suppliers' },
      { text: 'Leads', value: 'Leads' },
      { text: 'Staff', value: 'Staff' },
      { text: 'Salla Customers', value: 'salla' },
      { text: 'Zid Customers', value: 'zid' },
      { text: 'Custom', value: 'custom' },


    ];

    this.subscriptions.add(myService.gettemplates().subscribe(r => {
      if (r.success) {
        this.smstemplates = r.data;
        this.cdk.detectChanges();
      }
    }));
  }

  ngOnChanges(changes: any) {
    this.griddata = changes.griddata.currentValue;
    this.constructColumns();
  }

  ngOnInit(): void {
    if(this.griddata && this.griddata.length) {
      this.columns = Object.keys(this.griddata[0]).map(d => {
        return { text: d.toString(), value: d };
      });
    }
    this.sendForm = this.fb.group({
      templateid: [null, Validators.required],
      recipientNumber: null,
      groupId: null,
      contacts: [],
      recipientType: [{value: this.recipientType, disabled: this.recipientType === 'custom'}, Validators.required],
      headerVariableValue: null,
      uploadedMediaId: null,
      bodyVariables: this.fb.array([]),
      senderNumber: [null, Validators.required],
      mobileColumn: null,
      contentType: null,
      fileName: null,
      campaignTitle: null,
      caseID: null,
      bodyText: null,
    });
    this.subscriptions.add(this.sendForm.get('caseID')?.valueChanges.subscribe(v => {
      this.subscriptions.add(this.myService.getBalance(v).subscribe(t => {
        if (t) {
          this.balance = t.data;
          this.cdk.detectChanges();
        }
      }));
    }));

    this.subscriptions.add(this.sendForm.get('templateid')?.valueChanges.subscribe(v => {
      this.subscriptions.add(this.myService.get_template_details(v).subscribe(t => {
        if (t) {
          this.sendForm.get('bodyText')?.setValue(t.data.template);
          this.cdk.detectChanges();
        }
      }));
    }));
    this.subscriptions.add(this.sendForm.get('recipientType')?.valueChanges.subscribe(v => {
      if (v != 'excel') {
        this.griddata = [];
        this.bodyFromExcel = false;
        this.headerCol = false;
        this.sendForm.get('contacts')?.setValue([]);
        this.columns = [];
        this.showExcelPreview = false;
        this.currentExcelPage = 0;
        this.isPreviewing = false;
        this.showExcelPreview = false;
      }
      if (v == 'salla') {
        this.loadSallaCustomers();
      } else {
        this.headerSallaCol = false;
        this.columns = [];
        this.griddata = [];
        this.ItemsCount = 0;
        this.PagesCount = 0;
        this.showSallaPreview = false;
      }
      if (v == 'zid') {
        this.loadZidCustomers();
      } else {
        this.headerZidCol = false;
        this.columns = [];
        this.griddata = [];
        this.ItemsCount = 0;
        this.PagesCount = 0;
        this.showZidPreview = false;
      }
      this.griddata = [];
      this.showCustomersPreview = false;
      this.showSuppliersPreview = false;
      this.showleadsPreview = false;
      this.showstaffPreview = false;
    }));
  }



  constructColumns() {
    if(this.griddata && this.griddata.length) {
      this.columns = Object.keys(this.griddata[0]).map(d => {
        return { text: d.toString(), value: d };
      });
    }
  }
  async onFileChange(event: any) {
    this.isUploading = true;
    const file = event.target.files[0];
    const workbook = new ExcelJS.Workbook();
    
    try {
      await workbook.xlsx.load(await file.arrayBuffer());
      const worksheet = workbook.worksheets[0];
      
      // Get columns from the first row
      this.columns = [];
      worksheet.getRow(1).eachCell((cell) => {
        const value = cell.value?.toString() || '';
        this.columns.push({ text: value, value: value });
      });

      // Get all data
      const excelData: Array<Array<string | number | null>> = [];
      worksheet.eachRow((row, rowNumber) => {
        const rowData: Array<string | number | null> = [];
        row.eachCell((cell) => {
          const cellValue = cell.value;
          // Convert cell value to appropriate type
          if (cellValue === undefined) {
            rowData.push(null);
          } else if (typeof cellValue === 'object' && cellValue !== null) {
            rowData.push(cellValue.toString());
          } else {
            rowData.push(cellValue as string | number);
          }
        });
        excelData.push(rowData);
      });

      // Set mobile column if found
      const mobileColumn = this.columns.find(col => 
        col.value.toLowerCase().includes('mobile') || 
        col.value.toLowerCase().includes('phone') || 
        col.value.toLowerCase().includes('whatsapp')
      );
      if (mobileColumn) {
        this.sendForm.get('mobileColumn')?.setValue(mobileColumn.value);
      }

      // Convert to grid data format
      this.griddata = excelData.slice(1).map(row => {
        const obj: { [key: string]: string | number | null } = {};
        this.columns.forEach((col, index) => {
          obj[col.value] = row[index];
        });
        return obj;
      });

      this.isUploading = false;
      this.cdk.detectChanges();
    } catch (error) {
      this.isUploading = false;
      this.toastr.error('Error reading Excel file');
      console.error('Error reading Excel file:', error);
    }
  }

  toggleExcelPreview() {
    this.isPreviewing = !this.isPreviewing;
    this.cdk.detectChanges();
    setTimeout(() => {
      this.showExcelPreview = !this.showExcelPreview;
    }, 100);
  }
  get pagedExcelData(): any[] {
    return this.griddata.slice(1).filter((d, index) => index < (this.currentExcelPage + this.pageSize) && index >= this.currentExcelPage);
  }
  stopPreviewLoading(last: any) {
    if (last) {
      if (this.isPreviewing) {
        this.isPreviewing = false;
        this.currentExcelPage = 0;
        this.cdk.detectChanges();
      }
    }
  }
  nextPreview() {
    if (this.currentExcelPage >= this.griddata.length) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage + this.pageSize;
  }
  prevPreview() {
    if (this.currentExcelPage <= 1) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage - this.pageSize;
  }

  send() {


    const phones = this.gridBoxValue.length > 0 ? this.gridBoxValue.map((e: any) => e.mobile) : [];
    const model = {
      phones,
      providerCaseId: this.sendForm.get('caseID')?.value,
      template: this.sendForm.get('bodyText')?.value,
      recipientType: this.sendForm.get('recipientType')?.value,
      recipientNumber: this.sendForm.get('recipientNumber')?.value,
    }
    this.subscriptions.add(this.myService.sendSms(model).subscribe(res => {
      if (res.success) {
        this.resultMessage = res.data;
        this.cdk.detectChanges();
      }
    }));

  };


  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }

  loadSallaCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showSallaPreview = preview;
      this.subscriptions.add(this.myService.getSallaCustomers()
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            this.columns.push(...[{ text: 'First Name', value: 'first_name' }, { text: 'Last Name', value: 'last_name' }, { text: 'Full Name', value: 'first_name & last_name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'country' }, { text: 'City', value: 'city' }]);
          }
        }));
    } else {
      this.showSallaPreview = !this.showSallaPreview;
    }
  }

  loadZidCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showZidPreview = preview;
      this.subscriptions.add(this.myService.getZidCustomers()
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            this.columns.push(...[{ text: 'Name', value: 'name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'city.country_name' }, { text: 'City', value: 'city.name' }]);
          }
        }));
    } else {
      this.showZidPreview = !this.showZidPreview;
    }
  }

  loadStaff(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showstaffPreview = preview;
      this.subscriptions.add(this.myService.getStaff()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            if (r.data) {
              this.columns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();

          }
        }));
    } else {
      this.showstaffPreview = !this.showstaffPreview;
    }
  }

  loadLeads(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showleadsPreview = preview;
      this.subscriptions.add(this.myService.getLeads()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            if (r.data) {
              this.columns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();

          }
        }));
    } else {
      this.showleadsPreview = !this.showleadsPreview;
    }
  }

  loadSuppliers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showSuppliersPreview = preview;
      this.subscriptions.add(this.myService.getSuppliers()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            if (r.data) {
              this.columns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();

          }
        }));
    } else {
      this.showSuppliersPreview = !this.showSuppliersPreview;
    }
  }

  loadCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showCustomersPreview = preview;
      this.subscriptions.add(this.myService.getCustomers()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.PagesCount = r.pagesCount;
            this.ItemsCount = r.itemsCount;
            if (r.data) {
              this.columns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();
            //this.columns.push(...[{ text: 'Name', value: 'name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'city.country_name' }, { text: 'City', value: 'city.name' }]);
          }
        }));
    } else {
      this.showCustomersPreview = !this.showCustomersPreview;
      this.griddata = [];
    }
  }

}
