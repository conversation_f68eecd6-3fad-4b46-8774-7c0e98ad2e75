

<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>{{'COMMON.SendSMS' | translate }}</strong>
    </div>
    <div class="row">
      <form [formGroup]="sendForm">
        <div class="row">


          <div class="col-xl-4 col-sm-12">

            <label for="templateName" class="form-label mb-0">
              {{'COMMON.RecipientType' | translate }}
            </label>
            <ng-select formControlName="recipientType" [items]="recipientTypes" bindLabel="text" bindValue="value"
                       class="pb-3"></ng-select>


            <p *ngIf="sendForm.get('recipientType')?.value == 'salla'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadSallaCustomers(!showSallaPreview)">
                {{ showSallaPreview ? 'Hide ':'Preview ' }} Salla Customers
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>
            <p *ngIf="sendForm.get('recipientType')?.value == 'zid'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadZidCustomers(!showZidPreview)">
                {{ showZidPreview ? 'Hide ':'Preview ' }} Zid Customers
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>  <p *ngIf="sendForm.get('recipientType')?.value == 'Customers'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadCustomers(!showCustomersPreview)">
                {{ showCustomersPreview ? 'Hide ':'Preview ' }} Customers
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>

            <!-- Suppliers -->
            <p *ngIf="sendForm.get('recipientType')?.value == 'Suppliers'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadSuppliers(!showCustomersPreview)">
                {{ showSuppliersPreview ? 'Hide ':'Preview ' }}  Suppliers
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>
            <!-- Leads -->
            <p *ngIf="sendForm.get('recipientType')?.value == 'Leads'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadLeads(!showleadsPreview)">
                {{ showleadsPreview ? 'Hide ':'Preview ' }}  Leads
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>
            <!-- Staff -->
            <p *ngIf="sendForm.get('recipientType')?.value == 'Staff'" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="loadStaff(!showstaffPreview)">
                {{ showstaffPreview ? 'Hide ':'Preview ' }}  Staff
                <span *ngIf="ItemsCount">({{ItemsCount}} Rows)</span>
              </a>
            </p>

          </div>


          <!-- individual -->
          <div class="mb-2 col-12" *ngIf="sendForm.get('recipientType')?.value == 'individual' ">
            <label for="recipientNumber" class="form-label">
              {{'COMMON.RecipientNumber' | translate }}
            </label>
            <input class="form-control" placeholder="Recipient Number" formControlName="recipientNumber">
          </div>

          <!-- excel -->

          <div class="mb-2 col-12" *ngIf="sendForm.get('recipientType')?.value == 'excel'">
            <input type="file" class="form-control" (change)="onFileChange($event)">
            <p *ngIf="griddata.length > 1" class="form-label">
              <a [routerLink]="[]" class="pointer float-end" (click)="toggleExcelPreview()">
                {{ showExcelPreview ? 'Hide ':'Preview ' }} Excel Data ({{ (griddata.length - 1 )+ ' Rows'}})
              </a>
            </p>
            <label class="form-label my-2 mb-0">
              {{'COMMON.RecipientNumber' | translate }}
            </label>
            <ng-select class="pb-3" formControlName="mobileColumn" [items]="columns"></ng-select>
          </div>

          <div class="col-xl-4 col-sm-12">


            <label class="required fs-6 fw-semibold mb-0">{{'COMMON.ServiceProvider' | translate }} </label>

            <ng-select formControlName="caseID" bindLabel="value" bindValue="key" [items]="providers"></ng-select>
            <p> {{'COMMON.Balance' | translate }}  :  {{balance}}</p>
            <p *ngIf="resultMessage">{{ resultMessage }}</p>
          </div>

          <div class="col-xl-4 col-sm-12">
            <label class="required fs-6 fw-semibold mb-0">{{'COMMON.TemplateName' | translate }}</label>

            <ng-select formControlName="templateid" bindLabel="name" bindValue="id" [items]="smstemplates"></ng-select>


          </div>

        </div>

        <div class="row mb-3">
          <div class="col-12">
            <label for="body" class="form-label">
              {{'COMMON.Template' | translate }}
            </label>
            <textarea class="form-control" rows="4" placeholder="Body Text" formControlName="bodyText"
                      maxlength="1040"></textarea>


          </div>
        </div>

        <div class="row mb-3" *ngIf="recipientType !== 'custom'">
          <div class="col-12">
            <!--keyExpr="id"-->
            <dx-data-grid id="gridcontrole"
            [rtlEnabled]="true"  
                          [dataSource]="griddata"
                          [showRowLines]="true"
                          [showBorders]="true"
                          [columnAutoWidth]="true"
                          (onSelectionChanged)="selectionChanged($event)"
                          [allowColumnResizing]="true"
                          [(selectedRowKeys)]="gridBoxValue">
              <dxo-selection mode="multiple"></dxo-selection>
              <dxo-filter-row [visible]="true"
                              [applyFilter]="currentFilter"></dxo-filter-row>

              <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
              <dxo-paging [pageSize]="20"  [enabled]="true"  [pageIndex]="1"> </dxo-paging>
              <dxo-pager [visible]="true"
                         [allowedPageSizes]="[20,50,100, 'all']"
                         [displayMode]="'compact'"
                         [showPageSizeSelector]="true"
                         [showInfo]="true"
                         [showNavigationButtons]="true">
              </dxo-pager>
              <dxo-header-filter [visible]="true"></dxo-header-filter>

              <dxo-search-panel [visible]="true"
                                [highlightCaseSensitive]="true"></dxo-search-panel>

              <dxi-column dataField="id" caption="id"></dxi-column>

              <dxi-column dataField="nameAr" caption="Name Ar"></dxi-column>
              <dxi-column dataField="nameEn" caption="Name En"></dxi-column>
              <dxi-column dataField="mobile" caption="Mobile"></dxi-column>


              <dxo-export [enabled]="true"
                          [formats]="['pdf','excel']">
              </dxo-export>

              <dxo-summary>
                <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
              </dxo-summary>




            </dx-data-grid>

          </div>
        </div>

        <div class="row ">
          <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="send()">
              <ng-container *ngIf="!isLoading">{{'COMMON.Send' | translate }}</ng-container>
              <ng-container *ngIf="isLoading">
                <span clas="indicator-progress" [style.display]="'block'">
                  Please wait...{{ " " }}
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
              </ng-container>
            </button>
          </div>
        </div>





      </form>
    </div>
  </div>
</div>







