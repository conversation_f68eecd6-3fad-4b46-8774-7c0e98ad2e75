import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SMSService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.SMSurl
}`;
  }
  getinstance(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + "SMSInstance/" + id);
  }

  get_template_details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + "SMSTemplate/" + id);
  }

  gettemplates(): Observable<any> {
    return this.http.get<any>(this.baseUrl + "SMSTemplate/", {

    });
  }


  sendSms(model:any): Observable<any> {
    return this.http.post<any>(this.baseUrl + "SMSInstance/Send",model);
  }


  getBalance(id:any): Observable<any> {
    return this.http.get<any>(this.baseUrl + "SMSInstance/Balance/"+id);
  }


  upload(file: any, phoneNumberId: any): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>("/api/wbtemplatemessages/upload?phoneNumberId=" + phoneNumberId, fd);
  }
  getSallaCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbsalla/customers');
  }
  getZidCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbzid/customers');
  }
  getCustomers(): Observable<any> {
    return this.http.get<any>('/api/customer');
  }
  getSuppliers(): Observable<any> {
    return this.http.get<any>('/api/Suppliers');
  }
  getLeads(): Observable<any> {
    return this.http.get<any>('/api/Leads');
  }
  getStaff(): Observable<any> {
    return this.http.get<any>('/api/Employees');
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }
  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }
}
