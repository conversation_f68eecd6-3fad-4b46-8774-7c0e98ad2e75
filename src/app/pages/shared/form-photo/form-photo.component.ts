import {
  Component, ElementRef, Input, OnInit,
} from '@angular/core';

@Component({
  selector: 'form-photo',
  templateUrl: './form-photo.component.html',
  styleUrls: ['./form-photo.component.scss'],
  standalone: false
})
export class FormPhotoComponent implements OnInit {
  @Input() link: string;

  @Input() editable = false;

  @Input() size = 124;

  imageUrl: string;

  hostRef = this.elRef.nativeElement;

  constructor(private elRef:ElementRef) {}

  ngOnInit() {
    this.imageUrl = `url('${this.link}')`;
  }
}
