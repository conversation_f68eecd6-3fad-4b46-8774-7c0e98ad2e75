import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export class Status {
  id: number;
  name: string;
}

const statuses: Status[] = [{
  id: 1, name: 'Not Started',
}, {
  id: 2, name: 'In Progress',
}, {
  id: 3, name: 'Deferred',
}, {
  id: 4, name: 'Need Assistance',
}, {
  id: 5, name: 'Completed',
},
];

@Injectable({
  providedIn: 'root'
})
export class InventoryService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}${id}`);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query
      }
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}names`);
  }

  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }

  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }

  getWarehouses(): Observable<any> {
    return this.http.get<any>('api/Warehouses');
  }

  /**
   * Get total products count for dashboard
   */
  getProductsCount(): Observable<any> {
    return this.http.get<any>('api/Products/count');
  }

  /**
   * Get low stock items for dashboard
   */
  getLowStockItems(): Observable<any> {
    return this.http.get<any>('api/Products/lowStock');
  }

  /**
   * Get total inventory value for dashboard
   */
  getInventoryValue(): Observable<any> {
    return this.http.get<any>('api/Inventory/totalValue');
  }

  /**
   * Get recent inventory transactions for dashboard
   */
  getRecentTransactions(): Observable<any> {
    return this.http.get<any>('api/Inventory/recentTransactions');
  }
  getProductCategories(): Observable<any> {
    return this.http.get<any>('api/ProductCategories');
  }
  getProductSubCategories(): Observable<any> {
    return this.http.get<any>('api/ProductSubCategories');
  }

  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }
  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }
  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}${id}`, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}${id}`)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(`${this.baseUrl}activate/${id}`, null);
  }

  batch(obj: any): Observable<any> {
    return this.http.post(`${this.baseUrl}batch/`, obj);
  }
  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(`${this.baseUrl}search`, form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(`${this.baseUrl}/filter`, form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(`${this.baseUrl}excel`, { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(`${this.baseUrl}excel`, form);
  }

  getStatuses() {
    return statuses;
  }
  getTradeClassification(): Observable<any> {
    return this.http.get<any>('/api/TradeClassification');
  }
}
