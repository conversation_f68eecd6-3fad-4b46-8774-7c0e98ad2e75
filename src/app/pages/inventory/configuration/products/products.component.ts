import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import ArrayStore from 'devextreme/data/array_store';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { ProductsService } from './productsService';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
  standalone: false,
})
export class ProductsComponent implements OnInit, OnDestroy {
  moduleName = 'Inventory.Products';
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [
    { name: 'تمت', value: true },
    { name: 'لم تتم', value: false },
  ];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
  selectedItemKeys: any = [];
  dataSource: ArrayStore;
  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  @ViewChild(DxDataGridComponent, { static: false })
  dataGrid: DxDataGridComponent;

  constructor(
    private fb: FormBuilder,
    private myService: ProductsService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef
  ) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required],
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(
      this.route.paramMap.subscribe((p) => {
        const id = p.get('id') || '';
        //if (id) {
        this.subscriptions.add(
          this.myService.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
        //  }
      })
    );
    this.searchCtrl.valueChanges.subscribe((v) => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(
          this.myService.search(v).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            }
          })
        );
      } else if (id) {
        this.subscriptions.add(
          this.myService.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      } else {
        this.subscriptions.add(
          this.myService.list('').subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      }
    });
  }

  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cd.detectChanges();
  }

  deleteRecords() {
    this.remove();
  }

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      icon: 'warning',
    }).then((r) => {
      if (r.isConfirmed) {
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(
            this.myService.delete(key).subscribe((r) => {
              if (r.success) {
                this.dataSource.remove(key);
                this.dataGrid.instance.refresh();
                this.cd.detectChanges();
              }
            })
          );
        });
      }
    });
  }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(
      this.myService.list('', value).subscribe((r) => {
        if (r.success) {
          //  this.dataSource = r.data;
          this.dataSource = new ArrayStore({
            key: 'id',
            data: r.data,
          });
          this.cd.detectChanges();
          MenuComponent.reinitialization();
        }
      })
    );
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.dataSource = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'warehouses.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  uploadExcel() {
    var input = document.createElement('input');
    input.type = 'file';
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(
            this.myService.importExcel(fd).subscribe((e) => {
              if (e) {
                this.currentPage = 1;
              }
            })
          );
        }
      }
    };
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }

  onPrint() {
    // استخدام نهج آمن للطباعة باستخدام نافذة منبثقة
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لاستخدام وظيفة الطباعة');
      return;
    }
    
    // إعداد محتوى صفحة الطباعة
    const printContent = `
      <html dir="rtl">
      <head>
        <title>قائمة المنتجات</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
          }
          h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
          }
          p.date {
            text-align: left;
            margin-bottom: 10px;
            font-size: 12px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
          }
          th {
            background-color: #f2f2f2;
          }
          .price {
            text-align: left;
            direction: ltr;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 10px;
          }
          @media print {
            @page {
              size: A4 landscape;
              margin: 1cm;
            }
          }
        </style>
      </head>
      <body>
        <h1>قائمة المنتجات</h1>
        <p class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        
        <table>
          <tr>
            <th>رقم المنتج</th>
            <th>الباركود</th>
            <th>الاسم بالعربية</th>
            <th>الاسم بالإنجليزية</th>
            <th>السعر</th>
          </tr>
          ${this.getProductsData().map(product => `
            <tr>
              <td>${product.id || ''}</td>
              <td>${product.barcode || ''}</td>
              <td>${product.nameAr || ''}</td>
              <td>${product.nameEn || ''}</td>
              <td class="price">${product.price ? product.price.toFixed(2) : '0.00'}</td>
            </tr>
          `).join('')}
        </table>
        
        <div class="footer">
          ${new Date().getFullYear()} Falcon ERP - جميع الحقوق محفوظة
        </div>
        
        <script>
          // الطباعة تلقائياً عند تحميل الصفحة
          window.onload = function() {
            window.print();
            setTimeout(function() {
              window.close();
            }, 500);
          };
        </script>
      </body>
      </html>
    `;
    
    // كتابة المحتوى إلى النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();
  }

  // دالة مساعدة للحصول على بيانات المنتجات من مصدر البيانات
  private getProductsData(): any[] {
    const products: any[] = [];
    
    if (this.dataSource) {
      // الحصول على جميع البيانات من مصدر البيانات
      this.dataSource.load().then(data => {
        data.forEach((item: any) => {
          products.push(item);
        });
      }).catch(error => {
        console.error('خطأ في تحميل بيانات المنتجات:', error);
      });
    }
    
    return products.length > 0 ? products : this.dataGrid.instance.getDataSource().items();
  }
}
