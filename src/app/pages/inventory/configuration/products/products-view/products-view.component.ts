import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON>ementR<PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, lastValueFrom, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { ProductsService } from '../productsService';
import DxDataGrid from 'devextreme/ui/data_grid';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import jsPDF from 'jspdf';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { saveAs } from 'file-saver-es';

@Component({
  selector: 'app-products-view',
  templateUrl: './products-view.component.html',
  styleUrls: ['./products-view.component.scss'],
  standalone: false,
})
export class ProductsViewComponent implements OnInit {
  moduleName = 'Inventory.Products';
  productPhoto: string | null = 'assets/media/avatars/blank.png';
  showOverlay = false;
  activeTab: string = 'tab1';
  roles: any[] = [];
  qualifications: any[] = [];
  commercialClassifications: any[] = [];

  lineManagers: any[] = [];
  documentTypes: any[] = [];
  statuses: any[] = [];
  maingroups: any[] = [];
  Supgroups: any[];
  accounts: any[];
  ActionDate = new Date().toISOString().slice(0, 10);
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];
  basicUnits: any = [];
  manufacturers: any[] = [];
  products: any[] = [];
  manufacturingTemplates: any[] = [];
  data: any[] = [];
  _currentPage = 1;
  itemsCount = 0;
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
  priceLists: any[] = [];
  suppliers: any[] = [];
  variants: any[] = [];
  variantsValue: any[] = [];
  taxTypes: any[] = [];
  taxTypes2: any[] = [];
  branches: any[] = [];
  companies: any[];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  @ViewChild('fileInput1') fileInput1!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput2') fileInput2!: ElementRef<HTMLInputElement>;

  triggerFileInput1(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput1.nativeElement.click();
  }

  onProductPhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.productPhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }

  clearProductPhoto(event: Event): void {
    event.stopPropagation();
    this.productPhoto = 'assets/media/avatars/blank.png';
  }
  onAccountSelect(selectedItem: any) {
    if (!selectedItem) {
      return;
    }

    this.Supgroups = this.maingroups
      .filter((r) => r.groupid === selectedItem.groupid)
      .flatMap((r) => r.productSubCategory);

    this.cdk.detectChanges();
    // this.subscription.add(this.service.getProductSubCategoriesById( this.groupId).subscribe(r => {
    //   if (r.success) {
    //     this.Supgroups = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: ProductsService,
    private router: Router
  ) {
    this.newdata = this.fb.group({
      id: [0],
      barcode: [null, Validators.required],
      nameEn: ["", Validators.required],
      nameAr: ["", Validators.required],
      classificationId: [null, Validators.required],
      categoryId: [null, Validators.required],
      subCategoryId: [null, Validators.required],
      accountCode: [null, Validators.required],
      shortCode: ["", Validators.required],
      branchId: [null, Validators.required],
      companyId: [null, Validators.required],
      unitId: [null, Validators.required],
      isStorable: [1, Validators.required],
      secondaryUnitId: [null],
      thirdUnitId: [null],
      secondaryUnitRate: [0],
      thirdUnitRate: [0],
      secondaryUnit: [0],
      itmRequestLimit: [0],
      maxQuantity: [0],
      minQuantity: [0],
      price: [0],
      wholesalePrice: [0],
      pruchasePriceFexed: [false],
      tax: [0],
      profitMargin: [0],
      additionalNotes: [''],
      actionDate: [new Date()],
      userName: [''],
      isFixedPrice: [false],
      flag: [false],
      purchasePrice: [0],
      wholesaleMargin: [0],
      tenderMargin: [0],
      tenderPrice: [0],
      highestPriceMargin: [0],
      highestPrice: [0],
      mediumPriceMargin: [0],
      mediumPrice: [0],
      lowPriceMargin: [0],
      lowPrice: [0],
      partNo: [''],
      descountPercent: [0],
      bonusPercentage: [0],
      manufacturerId: [null],
      manufacturerItemCode: [''],
      hasExpiryDate: [false],
      isActive: [true],
      isFrozen: [false],
      itm_stop_sell: [0],
      itm_stop_pur: [0],
      itm_print_barcode: [false],
      its_allow_discount: [0],
      itm_max_disc_per: [0],
      itm_max_disc_val: [0],
      itm_print_name: [0],
      itm_sell_unit: [0],
      storePlace: [''],
      waigt: [0],
      purchase: [0],
      quntity_Price: [''],
      width: [0],
      highet: [0],
      thikness: [0],
      countInBox: [0],
      imagePha: [''],
      isOffer: [false],
      always_in_stock: [false],
      imagePha2: [''],
      barcode2: [''],
      barcode3: [''],
      itemPrintName: [''],
      itemPrintNameEN: [''],
      isWeight: [false],
      anotherVat: [0],
      etaCodeType: [''],
      wooCommerceCode: [''],
      sallaCode: [''],
      zidCode: [''],
      etaCodeType2: [''],
      etaCodeType3: [''],
      etaCodeType4: [''],
      etaCodeType5: [''],
      etaCodeType6: [''],
      etaCodeType7: [''],
      etaCodeType8: [''],
      etaCodeType9: [''],
      etaCodeType10: [''],
      etaCode2: [''],
      etaCode3: [''],
      etaCode4: [''],
      etaCode5: [''],
      etaCode6: [''],
      etaCode7: [''],
      etaCode8: [''],
      etaCode9: [''],
      etaCode10: [''],
      vAT_Type: [''],
      taxCategoryReasonCode: [''],
      taxCategoryReason: [''],
      taxable: [true],
    });
    this.subscriptions.add(
      myService.getProductCategories().subscribe((r) => {
        if (r.success) {
          this.maingroups = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(
      myService.getUnits().subscribe((r) => {
        if (r.success) {
          this.basicUnits = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(
      myService.getTradeClassification().subscribe((r) => {
        if (r.success) {
          this.commercialClassifications = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(
      myService.getChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(
      myService.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(myService.getcompanies().subscribe(r => {
      if (r.success) {
        this.companies = r.data;
        this.cdk.detectChanges();
      }
    }));

  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    console.log('بيانات المنتج المستلمة:', item); // إضافة سجل للتصحيح

    // معالجة الحقول الخاصة التي تختلف أسماؤها بين الخادم والنموذج
    if (item.unit1Id !== null && item.unit1Id !== undefined) {
      this.newdata.get('unitId')?.setValue(item.unit1Id);
    }

    if (item.isStorable === null || item.isStorable === undefined) {
      this.newdata.get('isStorable')?.setValue(1);
    }

    // تعيين قيمة accountCode
    if (item.accountCode !== null && item.accountCode !== undefined) {
      this.newdata.get('accountCode')?.setValue(item.accountCode);
    }

    // تعبئة جميع الحقول المتوفرة في النموذج
    Object.keys(item).forEach((key) => {
      if (this.newdata.get(key)) {
        const value =
          item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });

    // معالجة العلاقات المتداخلة
    if (item.categoryId) {
      // تحديث قائمة الفئات الفرعية بناءً على الفئة المحددة
      this.Supgroups = this.maingroups
        .filter((r) => r.groupid === item.categoryId)
        .flatMap((r) => r.productSubCategory);
      
      // تأكد من تحديث واجهة المستخدم
      this.cdk.detectChanges();
    }

    // إذا كانت هناك صورة للمنتج
    if (item.imagePha && item.imagePha !== '') {
      this.productPhoto = item.imagePha;
    }

    // تعيين وضع التعديل
    this.editMode = true;
    
    // طباعة قيم النموذج بعد التعبئة للتصحيح
    console.log('قيم النموذج بعد التعبئة:', this.newdata.value);
    
    this.cdk.detectChanges();
  }

  save() {
    console.log('تم استدعاء دالة الحفظ');
    
    const id = this.route.snapshot.params.id;
    console.log('معرف المنتج:', id);
    
    if (id) {
      console.log('وضع التعديل');
      
      if (this.newdata.valid) {
        console.log('النموذج صحيح');
        let form = { ...this.newdata.value };
        
        // معالجة الأرقام العربية في الحقول الرقمية
        form = this.convertArabicNumbersToWestern(form);
        
        console.log('بيانات النموذج بعد التعديل:', form);
        
        // التأكد من أن حقل isStorable هو رقم
        if (form.isStorable && typeof form.isStorable === 'string') {
          form.isStorable = parseInt(form.isStorable, 10) || 1;
        }
        
        console.log('بيانات النموذج بعد التعديل:', form);
        
        this.isLoading = true; // تعيين حالة التحميل
        
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe(
              (r) => {
                console.log('استجابة التحديث:', r);
                if (r.success) {
                  this.router.navigate(['/inventory/configuration/products']);
                } else {
                  console.error('فشل التحديث:', r);
                  // إضافة رسالة خطأ للمستخدم
                  alert('حدث خطأ أثناء حفظ البيانات');
                }
              },
              (error) => {
                console.error('خطأ في طلب التحديث:', error);
                // إضافة رسالة خطأ للمستخدم
                alert('حدث خطأ أثناء الاتصال بالخادم');
                this.isLoading = false;
              }
            )
        );
      } else {
        console.log('النموذج غير صحيح');
        console.log('أخطاء النموذج:', this.getFormValidationErrors());
        this.newdata.markAllAsTouched();
      }
    } else {
      console.log('وضع الإضافة');
      
      if (this.newdata.valid) {
        console.log('النموذج صحيح');
        let form = { ...this.newdata.value };
        
        // معالجة الأرقام العربية في الحقول الرقمية
        form = this.convertArabicNumbersToWestern(form);
        
        console.log('بيانات النموذج بعد التعديل:', form);
        
        // التأكد من أن حقل isStorable هو رقم
        if (form.isStorable && typeof form.isStorable === 'string') {
          form.isStorable = parseInt(form.isStorable, 10) || 1;
        }
        
        console.log('بيانات النموذج بعد التعديل:', form);
        
        this.isLoading = true; // تعيين حالة التحميل
        
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe(
              (r) => {
                console.log('استجابة الإنشاء:', r);
                if (r.success) {
                  this.router.navigate(['/inventory/configuration/products']);
                } else {
                  console.error('فشل الإنشاء:', r);
                  // إضافة رسالة خطأ للمستخدم
                  alert('حدث خطأ أثناء حفظ البيانات');
                }
              },
              (error) => {
                console.error('خطأ في طلب الإنشاء:', error);
                // إضافة رسالة خطأ للمستخدم
                alert('حدث خطأ أثناء الاتصال بالخادم');
                this.isLoading = false;
              }
            )
        );
      } else {
        console.log('النموذج غير صحيح');
        console.log('أخطاء النموذج:', this.getFormValidationErrors());
        this.newdata.markAllAsTouched();
      }
    }
  }
  
  // دالة مساعدة لعرض أخطاء التحقق من صحة النموذج
  getFormValidationErrors(): { control: string; error: string; value: any }[] {
    const result: { control: string; error: string; value: any }[] = [];
    Object.keys(this.newdata.controls).forEach(key => {
      const controlErrors = this.newdata.get(key)?.errors;
      if (controlErrors) {
        Object.keys(controlErrors).forEach(keyError => {
          result.push({
            control: key,
            error: keyError,
            value: controlErrors[keyError]
          });
        });
      }
    });
    return result;
  }

  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
  onSaving(e: any) {
    e.cancel = true;
    if (e.changes.length) {
      e.changes.forEach((c: any) => {
        if (c.type == 'update') {
          let selected = this.data.find(d => d.id == c.key);
          if (selected) {
            for (const key in selected) {
              if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                c.data[key] = selected[key];
              }
            }
          }
        }
      });
      e.promise = this.processBatchRequest(e.changes, e.component);
    }
  }
    async processBatchRequest(changes: Array<{}>, component: DxDataGrid): Promise<any> {
      await lastValueFrom(this.myService.batch(changes));
      component.cancelEditData();
      await component.refresh(true);
      this.cdk.detectChanges();
      this.subscriptions.add(this.myService.list('', this.currentPage).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
    }
    get currentPage() {
      return this._currentPage;
    }
    set currentPage(value: any) {
      this._currentPage = value;
      this.subscriptions.add(this.myService.list('', value).subscribe(r => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          MenuComponent.reinitialization();
        }
      }));
    }
    loadData(r: any) {
      this.data = r.data;
      if (r.itemsCount) {
        this.itemsCount = r.itemsCount;
      }
      if (r.pagesCount) {
        this.pagesCount = r.pagesCount;
      }
      this.cdk.detectChanges();
      MenuComponent.reinitialization();
    }
      onExporting(e: any) {
        console.log(e);
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('warehouses');
        if (e.format == 'excel') {
          exportDataGrid({
            component: e.component,
            worksheet,
            autoFilterEnabled: true,
          }).then(() => {
            workbook.xlsx.writeBuffer().then((buffer: any) => {
              saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
            });
    
          });
        } else if (e.format == 'pdf') {
          const doc = new jsPDF();
          pdfGrid({
            jsPDFDocument: doc,
            component: e.component,
            indent: 5,
          }).then(() => {
            doc.save('warehouses.pdf');
          });
        }
        e.cancel = true;
      }

      onPrint() {
        // استخدام نهج آمن للطباعة باستخدام نافذة منبثقة
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          alert('يرجى السماح بالنوافذ المنبثقة لاستخدام وظيفة الطباعة');
          return;
        }
        
        // تحضير البيانات من النموذج
        const productData = this.newdata.value;
        
        // إعداد محتوى صفحة الطباعة
        const printContent = `
          <html dir="rtl">
          <head>
            <title>بيانات المنتج</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                direction: rtl;
                margin: 0;
                padding: 20px;
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
              }
              h1 {
                color: #333;
                margin: 0;
                font-size: 24px;
              }
              .date {
                text-align: left;
                margin: 5px 0 15px;
                font-size: 12px;
                color: #666;
              }
              .product-image {
                max-width: 200px;
                max-height: 200px;
                display: block;
                margin: 0 auto 20px;
                border: 1px solid #ddd;
              }
              .section {
                margin-bottom: 20px;
              }
              .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                background-color: #f0f0f0;
                padding: 5px;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
              }
              th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: right;
              }
              th {
                background-color: #f2f2f2;
                font-weight: bold;
                width: 30%;
              }
              .price {
                text-align: left;
                direction: ltr;
              }
              .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #777;
                border-top: 1px solid #ddd;
                padding-top: 10px;
              }
              @media print {
                @page {
                  size: A4;
                  margin: 1cm;
                }
                body {
                  margin: 0;
                  padding: 10px;
                }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>بطاقة المنتج</h1>
              <p class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            ${this.productPhoto && this.productPhoto !== 'assets/media/avatars/blank.png' ? 
              `<img src="${this.productPhoto}" alt="صورة المنتج" class="product-image">` : ''}
            
            <div class="section">
              <div class="section-title">المعلومات الأساسية</div>
              <table>
                <tr>
                  <th>رقم المنتج</th>
                  <td>${productData.id || '-'}</td>
                </tr>
                <tr>
                  <th>الباركود</th>
                  <td>${productData.barcode || '-'}</td>
                </tr>
                <tr>
                  <th>الاسم بالعربية</th>
                  <td>${productData.nameAr || '-'}</td>
                </tr>
                <tr>
                  <th>الاسم بالإنجليزية</th>
                  <td>${productData.nameEn || '-'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">معلومات التسعير</div>
              <table>
                <tr>
                  <th>سعر الشراء</th>
                  <td class="price">${productData.price ? productData.price.toFixed(2) : '0.00'}</td>
                </tr>
                <tr>
                  <th>سعر البيع</th>
                  <td class="price">${productData.sellPrice ? productData.sellPrice.toFixed(2) : '0.00'}</td>
                </tr>
                <tr>
                  <th>خاضع للضريبة</th>
                  <td>${productData.taxable ? 'نعم' : 'لا'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">المخزون</div>
              <table>
                <tr>
                  <th>قابل للتخزين</th>
                  <td>${productData.isStorable ? 'نعم' : 'لا'}</td>
                </tr>
                <tr>
                  <th>الحد الأدنى للطلب</th>
                  <td>${productData.orderLimit || '0'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">ملاحظات</div>
              <p>${productData.notes || 'لا توجد ملاحظات'}</p>
            </div>
            
            <div class="footer">
              ${new Date().getFullYear()} Falcon ERP - جميع الحقوق محفوظة
            </div>
            
            <script>
              // الطباعة تلقائياً عند تحميل الصفحة
              window.onload = function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              };
            </script>
          </body>
          </html>
        `;
        
        // كتابة المحتوى إلى النافذة الجديدة
        printWindow.document.write(printContent);
        printWindow.document.close();
      }

  // دالة مساعدة لتحويل الأرقام العربية إلى أرقام لاتينية
  private convertArabicNumbersToWestern(formData: any): any {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const westernNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    // الحقول الرقمية التي تحتاج إلى تحويل
    const numericFields = [
      'thirdUnitRate', 'bonusPercentage', 'itm_max_disc_per', 'secondaryUnitRate',
      'price', 'sellPrice', 'wholesalePrice', 'orderLimit', 'branchRate'
    ];
    
    const result = { ...formData };
    
    // تحويل القيم في الحقول الرقمية
    for (const field of numericFields) {
      if (result[field] && typeof result[field] === 'string') {
        let value = result[field].toString();
        
        // استبدال الأرقام العربية بالأرقام اللاتينية
        for (let i = 0; i < arabicNumbers.length; i++) {
          value = value.replace(new RegExp(arabicNumbers[i], 'g'), westernNumbers[i]);
        }
        
        // تحويل القيمة إلى رقم
        if (field === 'thirdUnitRate' || field === 'secondaryUnitRate') {
          result[field] = parseFloat(value) || 0;
        } else {
          result[field] = parseFloat(value) || 0;
        }
      }
    }
    
    return result;
  }
}
