<div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.AddProduct" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="submit"
              (click)="discard()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-close"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>
          <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.Products'
              }"
            >
              <a
                class="menu-link px-3"
                (click)="onPrint()"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="card-body border-top p-9">
      <form
        [formGroup]="newdata"
        class="form d-flex flex-wrap align-items-start gap-3"
        action="#"
        id="kt_modal_add_company_form"
        data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
      >
        <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
          <div class="main-inputs flex-grow-1">
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="NameAr" class="form-label">
                  {{ "COMPANY.NAMEAR" | translate }}
                </label>
                <input
                  type="text"
                  id="NameAr"
                  name="NameAr"
                  class="form-control"
                  formControlName="nameAr"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="NameEn" class="form-label">
                  {{ "COMPANY.NAMEEN" | translate }}
                </label>
                <input
                  type="text"
                  name="NameEn"
                  id="NameEn"
                  class="form-control"
                  formControlName="nameEn"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="classificationId" class="form-label">
                  {{ "COMMON.CommercialClassification" | translate }}
                </label>
                <ng-select
                  id="classificationId"
                  bindLabel="name"
                  bindValue="id"
                  formControlName="classificationId"
                  [items]="commercialClassifications"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="Barcode" class="form-label">
                  {{ "COMMON.Barcode" | translate }}
                </label>
                <input
                  type="text"
                  id="Barcode"
                  name="Barcode"
                  class="form-control"
                  formControlName="barcode"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="CategoryId" class="form-label">
                  {{ "COMMON.ProductCategories" | translate }}
                </label>
                <ng-select
                  id="CategoryId"
                  formControlName="categoryId"
                  bindLabel="groupname"
                  bindValue="groupid"
                  [items]="maingroups"
                  (change)="onAccountSelect($event)"
                ></ng-select>
              </div>
          
          
           <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="SubCategoryId" class="form-label">
                  {{ "COMMON.ProductSubCategories" | translate }}
                </label>
                <ng-select
                  id="SubCategoryId"
                  formControlName="subCategoryId"
                  bindLabel="subname"
                  bindValue="subid"
                  [items]="Supgroups"
                ></ng-select>
              </div>
        
             
             
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="AccountCode" class="form-label">
                  {{ "COMMON.AccName" | translate }}
                </label>
                <ng-select
                  id="AccountCode"
                  bindLabel="nameAr"
                  bindValue="code"
                  formControlName="accountCode"
                  [items]="accounts"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="shortCode" class="form-label">
                  {{ "CUSTOMER.SHORT_AR" | translate }}
                  <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  [ngClass]="{'is-invalid': newdata.get('shortCode')?.invalid && (newdata.get('shortCode')?.dirty || newdata.get('shortCode')?.touched)}"
                  formControlName="shortCode"
                />
                <div *ngIf="newdata.get('shortCode')?.invalid && (newdata.get('shortCode')?.dirty || newdata.get('shortCode')?.touched)" class="invalid-feedback">
                  <div *ngIf="newdata.get('shortCode')?.errors?.['required']">{{ "VALIDATION.REQUIRED_FIELD" | translate }}</div>
                </div>
              </div>
            </div>

              <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="branchId" class="form-label">{{ 'CUSTOMER.BRANCH' | translate }}</label>
                    <ng-select class="select-border-bottom w-100"
                              id="branchId"
                              formControlName="branchId"
                              bindLabel="name"
                              bindValue="id"
                              [items]="branches"
                              ></ng-select>
           
                 </div>
                 <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                  <label for="companyId" class="form-label">
                      {{'COMMON.Company' | translate}}
                  </label>
                  <ng-select
                          id="companyId"
                          formControlName="companyId"
                          bindLabel="name"
                          bindValue="id"
                          [items]="companies"></ng-select>
                </div>
              </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="isStorable"
                    value="product"
                    name="isStorable"
                  />
                  {{ "COMMON.Product" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="isStorable"
                    value="services"
                    name="isStorable"
                  />
                  {{ "COMMON.Services" | translate }}
                </label>
              </div>
            </div>
            <!-- <pre>{{ newdata.value | json }}</pre> -->

          </div>
          <!-- photo -->
          <div
            class="photo-container position-relative d-flex flex-column align-items-center"
            (mouseenter)="showOverlay = true"
            (mouseleave)="showOverlay = false"
          >
            <img
              [src]="productPhoto || 'assets/media/avatars/blank.png'"
              alt="New product"
              class="photo img-fluid rounded mb-2"
            />
            <div
              class="overlay d-flex flex-column justify-content-end align-items-center"
              *ngIf="showOverlay"
            >
              <div class="button-group d-flex justify-content-between gap-2">
                <button
                  class="btn btn-primary btn-sm"
                  (click)="triggerFileInput1($event)"
                >
                  <i class="fa fa-edit fs-2"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="clearProductPhoto($event)"
                >
                  <i class="fa fa-trash-o fs-2"></i>
                </button>
              </div>
            </div>
            <input
              type="file"
              accept="image/*"
              class="d-none"
              #fileInput1
              (change)="onProductPhotoSelected($event)"
            />
          </div>
          <!-- End Photo  -->

        </div>
  
        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab1'"
                (click)="setActiveTab('tab1')"
              >
                {{ "COMMON.BasicData" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab2'"
                (click)="setActiveTab('tab2')"
              >
                {{ "COMMON.SellingPrices" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab3'"
                (click)="setActiveTab('tab3')"
              >
                {{ "COMMON.PriceLists" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab4'"
                (click)="setActiveTab('tab4')"
              >
                {{ "COMMON.QuantityPrices" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab5'"
                (click)="setActiveTab('tab5')"
              >
                {{ "COMMON.SalesPriceByWarehouse" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab6'"
                (click)="setActiveTab('tab6')"
              >
                {{ "COMMON.Suppliers" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab7'"
                (click)="setActiveTab('tab7')"
              >
              {{ "COMMON.ItemComponents" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab8'"
                (click)="setActiveTab('tab8')"
              >
              Sides
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab9'"
                (click)="setActiveTab('tab9')"
              >
              {{ "COMMON.ProductHistory" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab10'"
                (click)="setActiveTab('tab10')"
              >
                {{ "COMMON.AddBarcode" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab11'"
                (click)="setActiveTab('tab11')"
              >
                {{ "COMMON.ItemProperties" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab12'"
                (click)="setActiveTab('tab12')"
              >
              API Link
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab13'"
                (click)="setActiveTab('tab13')"
              >
                {{ "COMMON.SystemCodes" | translate }}
              </button>
            </li>
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <!-- Tab 1 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab1'"
              [class.active]="activeTab === 'tab1'"
              *ngIf="activeTab === 'tab1'"
            >
            <div class="row">
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="length" class="form-label">
                  {{ "COMMON.Length" | translate }}
                </label>
                <input
                  type="text"
                  id="length"
                  name="length"
                  class="form-control"
                  formControlName="highet"
                  value="0"
                />
              </div>
              
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="width" class="form-label">
                  {{ "COMMON.Width" | translate }}
                </label>
                <input
                  type="text"
                  id="width"
                  name="width"
                  class="form-control"
                  formControlName="width"
                  value="0"
                />
              </div>
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="area" class="form-label">
                  {{ "COMMON.Area" | translate }}
                </label>
                <input
                  type="text"
                  id="area"
                  name="area"
                  class="form-control"
                  formControlName="area"
                  value="0"
                />
              </div>
            </div>

            
            <div class="row">
              
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="thickness" class="form-label">
                  {{ "COMMON.Thickness" | translate }}
                </label>
                <input
                  type="text"
                  id="thickness"
                  name="thickness"
                  class="form-control"
                  formControlName="thikness"
                  value="0"
                />
              </div>
            
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="weight" class="form-label">
                  {{ "COMMON.Weight" | translate }}
                </label>
                <input
                  type="text"
                  id="waigt"
                  name="waigt"
                  class="form-control"
                  formControlName="waigt"
                  value="0"
                />
              </div>
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <label for="unitsPerPackage" class="form-label">
                  {{ "COMMON.NumberOfUnitsPerPackage" | translate }}
                </label>
                <input
                  type="text"
                  id="unitsPerPackage"
                  name="unitsPerPackage"
                  class="form-control"
                  formControlName="countInBox"
                  value="0"
                />
              </div>
            </div>
            
              <div class="row">
                <div class="form-group col-xl-3 col-md-6 col-sm-12">
                  <label for="notes" class="form-label">
                    {{ "COMMON.Notes" | translate }}
                  </label>
                  <input
                    type="text"
                    id="notes"
                    name="notes"
                    class="form-control"
                    formControlName="additionalNotes"
                  />
                </div>

   
                <div class="form-group col-xl-3 col-md-6 col-sm-12">
                  <label for="storageLocation" class="form-label">
                    {{ "COMMON.StorageLocation" | translate }}
                  </label>
                  <input
                    type="text"
                    id="storageLocation"
                    name="storageLocation"
                    class="form-control"
                    formControlName="storePlace"
                  />
                </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-2 col-md-6 col-sm-12">
                  <label for="reorderLimit" class="form-label">
                    {{ "COMMON.ReorderLimit" | translate }}
                  </label>
                  <input
                    type="text"
                    id="reorderLimit"
                    name="reorderLimit"
                    class="form-control"
                    formControlName="itmRequestLimit"
                    value="0"

                  />
                </div>
              
                <div class="form-group col-xl-2 col-md-6 col-sm-12">
                  <label for="maxLimit" class="form-label">
                    {{ "COMMON.MaximumLimit" | translate }}
                  </label>
                  <input
                    type="text"
                    id="maxLimit"
                    name="maxLimit"
                    class="form-control"
                    formControlName="maxQuantity"
                    value="0"

                  />
                </div>
              
                <div class="form-group col-xl-2 col-md-6 col-sm-12">
                  <label for="minLimit" class="form-label">
                    {{ "COMMON.MinimumLimit" | translate }}
                  </label>
                  <input
                    type="text"
                    id="minLimit"
                    name="minLimit"
                    class="form-control"
                    formControlName="minQuantity"
                    value="0"

                  />
                </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                  <label for="printnameAr" class="form-label">
                    {{ "COMMON.PrintNameAr" | translate }}
                  </label>
                  <input
                    type="text"
                    id="printNameAr"
                    name="printNameAr"
                    class="form-control"
                    formControlName="itemPrintName"
                  />
                </div>
                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                  <label for="printnameEn" class="form-label">
                    {{ "COMMON.PrintNameEn" | translate }}
                  </label>
                  <input
                    type="text"
                    id="printnameEn"
                    name="printnameEn"
                    class="form-control"
                    formControlName="itemPrintNameEN"
                  />
                </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-2 col-md-4 col-sm-6">
                  <dx-check-box
                    formControlName="always_in_stock"
                    valueExpr="always_in_stock"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.AlwaysInStock" | translate }}
                  </label>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-6">
                  <dx-check-box
                    formControlName="itm_print_barcode"
                    valueExpr="itm_print_barcode"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.PrintBarcode" | translate }}
                  </label>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-6">
                  <dx-check-box
                    formControlName="flag"
                    valueExpr="flag"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.StopDealingThisProduct" | translate }}
                  </label>
                </div>
              </div>
            </div>
  
            <!-- Tab 2 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab2'"
              [class.active]="activeTab === 'tab2'"
              *ngIf="activeTab === 'tab2'"
            >
            <div class="row">
              <div class="form-group col-xl-2 col-md-4 col-sm-12 ">
                <label for="itm_sell_unit" class="form-label">
                  {{ "COMMON.DefaultSellingUnit" | translate }}
                </label>
                <ng-select
                  id="itm_sell_unit"
                  formControlName="itm_sell_unit"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="basicUnits"
                ></ng-select>
              </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-2 col-md-4 col-sm-12 ">
                  <label for="UnitId" class="form-label">
                    {{ "COMMON.BasicUnit" | translate }}
                  </label>
                  <ng-select
                    id="UnitId"
                    formControlName="unitId"
                    bindLabel="nameAr"
                    bindValue="id"
                    [items]="basicUnits"
                  ></ng-select>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="oldPrice" class="form-label">
                    {{ "COMMON.OldPrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="oldPrice"
                    name="oldPrice"
                    class="form-control"
                    formControlName="oldPrice"
                  />
                </div>
                
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="sellingPrice" class="form-label">
                    {{ "COMMON.SellingPrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="sellingPrice"
                    name="sellingPrice"
                    class="form-control"
                    formControlName="sellingPrice"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="minimumPrice" class="form-label">
                    {{ "COMMON.MinimumPrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="minimumPrice"
                    name="minimumPrice"
                    class="form-control"
                    formControlName="lowPrice"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="purchasePrice" class="form-label">
                    {{ "COMMON.PurchasePrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="purchasePrice"
                    name="purchasePrice"
                    class="form-control"
                    formControlName="purchasePrice"
                  />
                </div>
              </div>
              <div class="row">

                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="number" class="form-label">
                    {{ "COMMON.number" | translate }}
                  </label>
                  <input
                    type="text"
                    id="number"
                    name="number"
                    class="form-control"
                    formControlName="secondaryUnitRate"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12 ">
                  <ng-select
                    id="basicUnit"
                    formControlName="secondaryUnitId"
                    name="secondaryUnit"
                    bindLabel="nameAr"
                    bindValue="id"
                    [items]="basicUnits"
                  ></ng-select>
                  <label for="basicUnit" class="form-label">
                    {{ "COMMON.FromBasicUnit" | translate }}
                  </label>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="sellingPrice" class="form-label">
                    {{ "COMMON.SellingPrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="sellingPrice"
                    name="sellingPrice"
                    class="form-control"
                    formControlName="highestPrice"
                  />
                </div>
                
              </div>
              <div class="row">

                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="number" class="form-label">
                    {{ "COMMON.number" | translate }}
                  </label>
                  <input
                    type="text"
                    id="number"
                    name="number"
                    class="form-control"
                    formControlName="thirdUnitRate"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12 ">
                  <ng-select
                    id="basicUnit"
                    formControlName="thirdUnitId"
                    bindLabel="nameAr"
                    bindValue="id"
                    [items]="basicUnits"
                  ></ng-select>
                  <label for="basicUnit" class="form-label">
                    {{ "COMMON.FromBasicUnit" | translate }}
                  </label>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="sellingPrice" class="form-label">
                    {{ "COMMON.SellingPrice" | translate }}
                  </label>
                  <input
                    type="text"
                    id="sellingPrice"
                    name="sellingPrice"
                    class="form-control"
                    formControlName="mediumPrice"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-6">
                  <dx-check-box
                    formControlName="isFixedPrice"
                    valueExpr="fixedPrice"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.FixedPrice" | translate }}
                  </label>
                </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="bouns" class="form-label">
                    {{ "COMMON.Bouns" | translate }}
                  </label>
                  <input
                    type="text"
                    id="bouns"
                    name="bouns"
                    class="form-control"
                    formControlName="bonusPercentage"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="virtualProfitMargin" class="form-label">
                    {{ "COMMON.VirtualProfitMargin" | translate }}
                  </label>
                  <input
                    type="text"
                    id="virtualProfitMargin"
                    name="virtualProfitMargin"
                    class="form-control"
                    formControlName="profitMargin"
                  />
                  <span class="fs-2">%</span>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="taxCategory" class="form-label">
                    {{ "COMMON.TaxCategory" | translate }}
                  </label>
                  <input
                    type="text"
                    id="taxCategory"
                    name="taxCategory"
                    class="form-control"
                    formControlName="tax"
                  />
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="discountPercentage" class="form-label">
                    {{ "CUSTOMER.DISCOUNT_PERCENTAGE" | translate }}
                  </label>
                  <input
                    type="text"
                    id="discountPercentage"
                    name="discountPercentage"
                    class="form-control"
                    formControlName="discountPercent"
                  />
                  <span class="fs-2">%</span>
                </div>
              </div>
              <div class="row">
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="consumptionRate" class="form-label">
                    {{ "COMMON.ConsumptionRate" | translate }}
                  </label>
                  <input
                    type="text"
                    id="consumptionRate"
                    name="consumptionRate"
                    class="form-control"
                    formControlName="itm_max_disc_per"
                  />
                </div>

                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="wastePercentage" class="form-label">
                    {{ "COMMON.WastePercentage" | translate }}
                  </label>
                  <input
                    type="text"
                    id="wastePercentage"
                    name="wastePercentage"
                    class="form-control"
                    formControlName="itm_max_disc_val"
                  />
                  <span class="fs-2">%</span>
                </div>
                <div class="form-group col-xl-2 col-md-4 col-sm-12">
                  <label for="otherTax" class="form-label">
                    {{ "COMMON.OtherTax" | translate }}
                  </label>
                  <input
                    type="text"
                    id="otherTax"
                    name="otherTax"
                    class="form-control"
                    formControlName="anotherVat"
                  />
                </div>
              </div>
            </div>
            <!-- Tab 3 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab3'"
              [class.active]="activeTab === 'tab3'"
              *ngIf="activeTab === 'tab3'"
            >
            <dx-data-grid
            id="gridcontrole"
            [rtlEnabled]="true"
            [dataSource]="data"
            keyExpr="priceList"
            [showRowLines]="true"
            [showBorders]="true"
            [columnAutoWidth]="true"
            (onExporting)="onExporting($event)"
            [allowColumnResizing]="true"
            [remoteOperations]="true"
            [repaintChangesOnly]="true"
            (onSaving)="onSaving($event)"
          >
            <dxo-editing
              mode="batch"
              [allowAdding]="true"
              [allowDeleting]="true"
              [allowUpdating]="true"
            ></dxo-editing>
          
            <dxo-selection mode="single"></dxo-selection>
          
            <dxo-filter-row
              [visible]="true"
              [applyFilter]="currentFilter"
            ></dxo-filter-row>
          
            <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
          
            <dxo-paging [pageSize]="10"></dxo-paging>
          
            <dxo-pager
              [visible]="true"
              [allowedPageSizes]="[5, 10, 'all']"
              [displayMode]="'compact'"
              [showPageSizeSelector]="true"
              [showInfo]="true"
              [showNavigationButtons]="true"
            ></dxo-pager>
          
            <dxo-header-filter [visible]="true"></dxo-header-filter>
          
            <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
          
            <dxi-column dataField="priceList" caption="{{ 'COMMON.PriceList' | translate }}" [width]="200">
              <dxo-lookup [dataSource]="priceLists" displayExpr="name" valueExpr="id"></dxo-lookup>
            </dxi-column>
            <dxi-column dataField="basicUnit" caption="{{ 'COMMON.BasicUnit' | translate }}" [width]="200">
              <dxo-lookup [dataSource]="basicUnits" displayExpr="nameAr" valueExpr="id"></dxo-lookup>
            </dxi-column>
          
            <dxi-column dataField="minimumPrice" caption="{{ 'COMMON.MinimumPrice' | translate }}" ></dxi-column>
            <dxi-column dataField="sellingPrice" caption="{{ 'COMMON.SellingPrice' | translate }}" ></dxi-column>
            <dxi-column dataField="discountPercentage" caption="{{ 'COMMON.DiscountPercentage' | translate }}" ></dxi-column>
              <dxo-header-filter>
                <dxo-search [enabled]="true"></dxo-search>
              </dxo-header-filter>

          

          
            <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>

          </dx-data-grid>
          
              </div>

            <!-- Tab 4 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab4'"
              [class.active]="activeTab === 'tab4'"
              *ngIf="activeTab === 'tab4'"
            >
            <dx-data-grid
            id="gridcontrole"
            [rtlEnabled]="true"
            [dataSource]="data"
            keyExpr="price"
            [showRowLines]="true"
            [showBorders]="true"
            [columnAutoWidth]="true"
            (onExporting)="onExporting($event)"
            [allowColumnResizing]="true"
            [remoteOperations]="true"
            [repaintChangesOnly]="true"
            (onSaving)="onSaving($event)"
          >
            <dxo-editing
              mode="batch"
              [allowAdding]="true"
              [allowDeleting]="true"
              [allowUpdating]="true"
            ></dxo-editing>
          
            <dxo-selection mode="single"></dxo-selection>
          
            <dxo-filter-row
              [visible]="true"
              [applyFilter]="currentFilter"
            ></dxo-filter-row>
          
            <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
          
            <dxo-paging [pageSize]="10"></dxo-paging>
          
            <dxo-pager
              [visible]="true"
              [allowedPageSizes]="[5, 10, 'all']"
              [displayMode]="'compact'"
              [showPageSizeSelector]="true"
              [showInfo]="true"
              [showNavigationButtons]="true"
            ></dxo-pager>
          
            <dxo-header-filter [visible]="true"></dxo-header-filter>
          
            <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
          

            <dxi-column dataField="minimumPrice" caption="{{ 'COMMON.QuantityFrom' | translate }}" ></dxi-column>
            <dxi-column dataField="quantityTo" caption="{{ 'COMMON.QuantityTo' | translate }}" ></dxi-column>
            <dxi-column dataField="price" caption="{{ 'COMMON.Price' | translate }}" ></dxi-column>
              <dxo-header-filter>
                <dxo-search [enabled]="true"></dxo-search>
              </dxo-header-filter>

          

          
            <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>

          </dx-data-grid>
            </div>
            <!-- Tab 5 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab5'"
              [class.active]="activeTab === 'tab5'"
              *ngIf="activeTab === 'tab5'"
            >
            <dx-data-grid
            id="gridcontrole"
            [rtlEnabled]="true"
            [dataSource]="data"
            keyExpr="priceList"
            [showRowLines]="true"
            [showBorders]="true"
            [columnAutoWidth]="true"
            (onExporting)="onExporting($event)"
            [allowColumnResizing]="true"
            [remoteOperations]="true"
            [repaintChangesOnly]="true"
            (onSaving)="onSaving($event)"
          >
            <dxo-editing
              mode="batch"
              [allowAdding]="true"
              [allowDeleting]="true"
              [allowUpdating]="true"
            ></dxo-editing>
          
            <dxo-selection mode="single"></dxo-selection>
          
            <dxo-filter-row
              [visible]="true"
              [applyFilter]="currentFilter"
            ></dxo-filter-row>
          
            <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
          
            <dxo-paging [pageSize]="10"></dxo-paging>
          
            <dxo-pager
              [visible]="true"
              [allowedPageSizes]="[5, 10, 'all']"
              [displayMode]="'compact'"
              [showPageSizeSelector]="true"
              [showInfo]="true"
              [showNavigationButtons]="true"
            ></dxo-pager>
          
            <dxo-header-filter [visible]="true"></dxo-header-filter>
          
            <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
          
            <dxi-column dataField="priceList" caption="{{ 'COMMON.PriceList' | translate }}" [width]="200">
              <dxo-lookup [dataSource]="priceLists" displayExpr="name" valueExpr="id"></dxo-lookup>
            </dxi-column>
            <dxi-column dataField="basicUnit" caption="{{ 'COMMON.BasicUnit' | translate }}" [width]="200">
              <dxo-lookup [dataSource]="basicUnits" displayExpr="nameAr" valueExpr="id"></dxo-lookup>
            </dxi-column>
          
            <dxi-column dataField="minimumPrice" caption="{{ 'COMMON.MinimumPrice' | translate }}" ></dxi-column>
            <dxi-column dataField="sellingPrice" caption="{{ 'COMMON.SellingPrice' | translate }}" ></dxi-column>
            <dxi-column dataField="discountPercentage" caption="{{ 'COMMON.DiscountPercentage' | translate }}" ></dxi-column>
              <dxo-header-filter>
                <dxo-search [enabled]="true"></dxo-search>
              </dxo-header-filter>

          

          
            <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>

          </dx-data-grid>
            </div>
            <!-- Tab 6 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab6'"
              [class.active]="activeTab === 'tab6'"
              *ngIf="activeTab === 'tab6'"
            >
              <div class="row">
                <div class="form-group col-xl-4 col-md-4 col-sm-12">
                  <label for="manufacturer" class="form-label">
                    {{ "COMMON.Manufacturer" | translate }}
                  </label>
                  <ng-select
                    id="manufacturer"
                    bindLabel="name"
                    bindValue="id"
                    formControlName="manufacturerId"
                    [items]="manufacturers"
                  ></ng-select>
                </div>
                <div class="form-group col-xl-4 col-md-6 col-sm-12">
                  <label for="manufacturerCode" class="form-label">
                    {{ "COMMON.ManufacturerCode" | translate }}
                  </label>
                  <input
                    type="text"
                    id="manufacturerCode"
                    name="manufacturerCode"
                    class="form-control"
                    value="0"
                    formControlName="manufacturerItemCode"
                  />
                </div>

              </div>
              <div class="row">
   
                <dx-data-grid
                id="gridcontrole"
                [rtlEnabled]="true"
                [dataSource]="data"
                keyExpr="priceList"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true"
                [remoteOperations]="true"
                [repaintChangesOnly]="true"
                (onSaving)="onSaving($event)"
              >
                <dxo-editing
                  mode="batch"
                  [allowAdding]="true"
                  [allowDeleting]="true"
                  [allowUpdating]="true"
                ></dxo-editing>
              
                <dxo-selection mode="single"></dxo-selection>
              
                <dxo-filter-row
                  [visible]="true"
                  [applyFilter]="currentFilter"
                ></dxo-filter-row>
              
                <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
              
                <dxo-paging [pageSize]="10"></dxo-paging>
              
                <dxo-pager
                  [visible]="true"
                  [allowedPageSizes]="[5, 10, 'all']"
                  [displayMode]="'compact'"
                  [showPageSizeSelector]="true"
                  [showInfo]="true"
                  [showNavigationButtons]="true"
                ></dxo-pager>
              
                <dxo-header-filter [visible]="true"></dxo-header-filter>
              
                <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
              
                <dxi-column dataField="supplierId" caption="{{ 'COMMON.SupplierId' | translate }}" ></dxi-column>

                <dxi-column dataField="supplier" caption="{{ 'COMMON.Supplier' | translate }}" [width]="200">
                  <dxo-lookup [dataSource]="suppliers" displayExpr="name" valueExpr="id"></dxo-lookup>
                </dxi-column>
              
                <dxi-column dataField="supplierProductCode" caption="{{ 'COMMON.SupplierProductCode' | translate }}" ></dxi-column>
                <dxi-column dataField="supplierPrice" caption="{{ 'COMMON.SupplierPrice' | translate }}" ></dxi-column>
                <dxi-column dataField="supplierBonus" caption="{{ 'COMMON.SupplierBonus' | translate }}" ></dxi-column>
                <dxi-column dataField="supplierDiscount" caption="{{ 'COMMON.SupplierDiscount' | translate }}" ></dxi-column>
                <dxi-column dataField="offerQuantity" caption="{{ 'COMMON.OfferQuantity' | translate }}" ></dxi-column>
                  <dxo-header-filter>
                    <dxo-search [enabled]="true"></dxo-search>
                  </dxo-header-filter>
    
              
    
              
                <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
    
              </dx-data-grid>

              </div>
  

            </div>
            <!-- Tab 7 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab7'"
              [class.active]="activeTab === 'tab7'"
              *ngIf="activeTab === 'tab7'"
            >
   
            <div class="col-lg-12 col-md-12 ">
              <dx-data-grid
                id="gridcontrole2"
                [rtlEnabled]="true"
                [dataSource]="data"
                keyExpr="priceList"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true"
                [remoteOperations]="true"
                [repaintChangesOnly]="true"
                (onSaving)="onSaving($event)"
              >
                <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true"></dxo-editing>
                <dxo-selection mode="single"></dxo-selection>
                <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>
                <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
                <dxo-paging [pageSize]="10"></dxo-paging>
                <dxo-pager
                  [visible]="true"
                  [allowedPageSizes]="[5, 10, 'all']"
                  [displayMode]="'compact'"
                  [showPageSizeSelector]="true"
                  [showInfo]="true"
                  [showNavigationButtons]="true"
                ></dxo-pager>
                <dxo-header-filter [visible]="true"></dxo-header-filter>
                <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
                <dxi-column
                  dataField="productName"
                  caption="{{ 'COMMON.ProductName' | translate }}"
                  [width]="200"
                >
                  <dxo-lookup [dataSource]="products" displayExpr="name" valueExpr="id"></dxo-lookup>
                </dxi-column>
                <dxi-column dataField="quantity" caption="{{ 'COMMON.Quantity' | translate }}" [width]="200"></dxi-column>

                <dxi-column dataField="Price" caption="{{ 'COMMON.Price' | translate }}" [width]="200"></dxi-column>
                <dxi-column dataField="unit" caption="{{ 'COMMON.Unit' | translate }}" [width]="200">
                  <dxo-lookup [dataSource]="basicUnits" displayExpr="nameAr" valueExpr="id"></dxo-lookup>
                </dxi-column>
                <dxi-column
                  dataField="manufacturingTemplate"
                  caption="{{ 'COMMON.ManufacturingTemplate' | translate }}"
                  [width]="200"
                >
                  <dxo-lookup [dataSource]="manufacturingTemplates" displayExpr="name" valueExpr="id"></dxo-lookup>
                </dxi-column>
                <dxo-header-filter>
                  <dxo-search [enabled]="true"></dxo-search>
                </dxo-header-filter>
                <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
              </dx-data-grid>
            </div>

            </div>
            <!-- Tab 8 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab8'"
              [class.active]="activeTab === 'tab8'"
              *ngIf="activeTab === 'tab8'"
            >
            <div class="row">
              <!-- الجدول الأول -->
              <div class="col-lg-6 col-md-12 ">
                <h3 class="text-center">Side 1</h3>
                <dx-data-grid
                  id="gridcontrole1"
                  [rtlEnabled]="true"
                  [dataSource]="data"
                  keyExpr="priceList"
                  [showRowLines]="true"
                  [showBorders]="true"
                  [columnAutoWidth]="true"
                  (onExporting)="onExporting($event)"
                  [allowColumnResizing]="true"
                  [remoteOperations]="true"
                  [repaintChangesOnly]="true"
                  (onSaving)="onSaving($event)"
                >
                  <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true"></dxo-editing>
                  <dxo-selection mode="single"></dxo-selection>
                  <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>
                  <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
                  <dxo-paging [pageSize]="10"></dxo-paging>
                  <dxo-pager
                    [visible]="true"
                    [allowedPageSizes]="[5, 10, 'all']"
                    [displayMode]="'compact'"
                    [showPageSizeSelector]="true"
                    [showInfo]="true"
                    [showNavigationButtons]="true"
                  ></dxo-pager>
                  <dxo-header-filter [visible]="true"></dxo-header-filter>
                  <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
                  <dxi-column dataField="sideId" caption="SideId" [width]="200"></dxi-column>
                  <dxi-column
                    dataField="sideName" caption="{{ 'COMMON.Name' | translate }}"[width]="200">
                  </dxi-column>

                  <dxo-header-filter>
                    <dxo-search [enabled]="true"></dxo-search>
                  </dxo-header-filter>
                  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
                </dx-data-grid>
              </div>
            
              <!-- الجدول الثاني -->
              <div class="col-lg-6 col-md-12 ">
                <h3 class="text-center">Side 2</h3>
                <dx-data-grid
                  id="gridcontrole2"
                  [rtlEnabled]="true"
                  [dataSource]="data"
                  keyExpr="priceList"
                  [showRowLines]="true"
                  [showBorders]="true"
                  [columnAutoWidth]="true"
                  (onExporting)="onExporting($event)"
                  [allowColumnResizing]="true"
                  [remoteOperations]="true"
                  [repaintChangesOnly]="true"
                  (onSaving)="onSaving($event)"
                >
                  <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true"></dxo-editing>
                  <dxo-selection mode="single"></dxo-selection>
                  <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>
                  <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
                  <dxo-paging [pageSize]="10"></dxo-paging>
                  <dxo-pager
                    [visible]="true"
                    [allowedPageSizes]="[5, 10, 'all']"
                    [displayMode]="'compact'"
                    [showPageSizeSelector]="true"
                    [showInfo]="true"
                    [showNavigationButtons]="true"
                  ></dxo-pager>
                  <dxo-header-filter [visible]="true"></dxo-header-filter>
                  <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
                  <dxi-column dataField="sideId" caption="SideId" [width]="200"></dxi-column>
                  <dxi-column
                    dataField="sideName"
                    caption="{{ 'COMMON.Name' | translate }}"
                    [width]="200"
                  ></dxi-column>
              
             

                  <dxo-header-filter>
                    <dxo-search [enabled]="true"></dxo-search>
                  </dxo-header-filter>
                  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
                </dx-data-grid>
              </div>
            </div>
            
            
            </div>
            <!-- Tab 9 -->
            <div
              class="tab-pane fade mt-2"
              [class.show]="activeTab === 'tab9'"
              [class.active]="activeTab === 'tab9'"
              *ngIf="activeTab === 'tab9'"
            >

            </div>
            <!-- Tab 10 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab10'"
              [class.active]="activeTab === 'tab10'"
              *ngIf="activeTab === 'tab10'"
            >
            <div class="col-lg-12 col-md-12 ">
              <dx-data-grid
                id="gridcontrole2"
                [rtlEnabled]="true"
                [dataSource]="data"
                keyExpr="priceList"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true"
                [remoteOperations]="true"
                [repaintChangesOnly]="true"
                (onSaving)="onSaving($event)"
              >
                <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true"></dxo-editing>
                <dxo-selection mode="single"></dxo-selection>
                <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>
                <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
                <dxo-paging [pageSize]="10"></dxo-paging>
                <dxo-pager
                  [visible]="true"
                  [allowedPageSizes]="[5, 10,'all']"
                  [displayMode]="'compact'"
                  [showPageSizeSelector]="true"
                  [showInfo]="true"
                  [showNavigationButtons]="true"
                ></dxo-pager>
                <dxo-header-filter [visible]="true"></dxo-header-filter>
                <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel> 

                <dxi-column dataField="unit" caption="{{ 'COMMON.Unit' | translate }}" [width]="300">
                  <dxo-lookup [dataSource]="basicUnits" displayExpr="nameAr" valueExpr="id"></dxo-lookup>
                </dxi-column>
                <dxi-column dataField="factor" caption="{{ 'COMMON.Factor' | translate }}" [width]="300"></dxi-column>
                <dxi-column dataField="sellingPrice" caption="{{ 'COMMON.SellingPrice' | translate }}" [width]="300"></dxi-column>
                <dxi-column dataField="barcode" caption="{{ 'COMMON.Barcode' | translate }}" [width]="300"></dxi-column>

                <dxo-header-filter>
                  <dxo-search [enabled]="true"></dxo-search>
                </dxo-header-filter>
                <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
              </dx-data-grid>
            </div>
            </div>
            <!-- Tab 11 -->
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab11'"
              [class.active]="activeTab === 'tab11'"
              *ngIf="activeTab === 'tab11'"
            >

            <div class="col-lg-12 col-md-12 ">
              <dx-data-grid
                id="gridcontrole2"
                [rtlEnabled]="true"
                [dataSource]="data"
                keyExpr="priceList"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true"
                [remoteOperations]="true"
                [repaintChangesOnly]="true"
                (onSaving)="onSaving($event)"
              >
                <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true"></dxo-editing>
                <dxo-selection mode="single"></dxo-selection>
                <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>
                <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
                <dxo-paging [pageSize]="10"></dxo-paging>
                <dxo-pager
                  [visible]="true"
                  [allowedPageSizes]="[5, 10, 'all']"
                  [displayMode]="'compact'"
                  [showPageSizeSelector]="true"
                  [showInfo]="true"
                  [showNavigationButtons]="true"
                ></dxo-pager>
                <dxo-header-filter [visible]="true"></dxo-header-filter>
                <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel> 

                <dxi-column dataField="variantName" caption="{{ 'COMMON.VariantName' | translate }}" [width]="300">
                  <dxo-lookup [dataSource]="variants" displayExpr="name" valueExpr="id"></dxo-lookup>
                </dxi-column>
                <dxi-column dataField="variantValue" caption="{{ 'COMMON.variantValue' | translate }}" [width]="300">
                  <dxo-lookup [dataSource]="variantsValue" displayExpr="name" valueExpr="id"></dxo-lookup>
                </dxi-column>
                
                <dxo-header-filter>
                  <dxo-search [enabled]="true"></dxo-search>
                </dxo-header-filter>
                <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>
              </dx-data-grid>
            </div>

            </div>
            <!-- Tab 12 -->
           <div
                        class="tab-pane fade"
                        [class.show]="activeTab === 'tab12'"
                        [class.active]="activeTab === 'tab12'"
                        *ngIf="activeTab === 'tab12'"
                      >
                 <div class="row">
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="wooCommerceCode" class="form-label">

               WooCommerce Code
                          
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          formControlName="wooCommerceCode"
                        />
                      </div>

                    </div>
                    <div class="row">
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for=" sallaCode" class="form-label">

              Salla Code
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          formControlName="sallaCode"
                        />
                      </div>
                      
                    </div>
                    <div class="row">
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="zidCode" class="form-label">

              Zid Code
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          formControlName="zidCode"
                        />
                      </div>
                      
                    </div>
           </div>
             <!-- Tab 13 -->
           <div
                       class="tab-pane fade"
                       [class.show]="activeTab === 'tab13'"
                       [class.active]="activeTab === 'tab13'"
                       *ngIf="activeTab === 'tab13'"
                     >
         
                     <div class="row">
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="taxType" class="form-label">
                          {{ "COMMON.TaxType" | translate }}
                        </label>
                        <ng-select
                          id="taxType"
                          bindLabel="name"
                          bindValue="id"
                          formControlName="vATType"
                          [items]="taxTypes"
                        ></ng-select>
                      </div>
                      <div class="form-group col-xl-3 col-md-6 col-sm-12">
                        <ng-select
                          id="taxType2"
                          bindLabel="name"
                          bindValue="id"
                          formControlName="taxCategoryReason"
                          [items]="taxTypes2"
                        ></ng-select>
                      </div>
                      <div class="form-group col-xl-2 col-md-4 col-sm-6">
                        <dx-check-box
                          formControlName="taxable"
                          valueExpr="taxable"
                        ></dx-check-box>
                        <label class="form-label d-block p-2">
                          {{ "COMMON.Taxable" | translate }}
                        </label>
                      </div>
                    </div>
                    <div class="row">
                      <!-- Branch1 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch1" | translate }}</span>
                        <label for="codeTypeGS1OrEGS1" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS1"
                          name="codeTypeGS1OrEGS1"
                          class="form-control"
                          formControlName="etaCodeType"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS1" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS1"
                          name="codeGS1OrEGS1"
                          class="form-control"
                          formControlName="PartNo"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch2 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch2" | translate }}</span>
                        <label for="codeTypeGS1OrEGS2" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS2"
                          name="codeTypeGS1OrEGS2"
                          class="form-control"
                          formControlName="etaCodeType2"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS2" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS2"
                          name="codeGS1OrEGS2"
                          class="form-control"
                          formControlName="etaCode2"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch3 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch3" | translate }}</span>
                        <label for="codeTypeGS1OrEGS3" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS3"
                          name="codeTypeGS1OrEGS3"
                          class="form-control"
                          formControlName="etaCodeType3"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS3" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS3"
                          name="codeGS1OrEGS3"
                          class="form-control"
                          formControlName="etaCode3"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch4 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch4" | translate }}</span>
                        <label for="codeTypeGS1OrEGS4" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS4"
                          name="codeTypeGS1OrEGS4"
                          class="form-control"
                          formControlName="etaCodeType4"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS4" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS4"
                          name="codeGS1OrEGS4"
                          class="form-control"
                          formControlName="etaCode4"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch5 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch5" | translate }}</span>
                        <label for="codeTypeGS1OrEGS5" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS5"
                          name="codeTypeGS1OrEGS5"
                          class="form-control"
                          formControlName="etaCodeType5"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS5" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS5"
                          name="codeGS1OrEGS5"
                          class="form-control"
                          formControlName="etaCode5"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch6 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch6" | translate }}</span>
                        <label for="codeTypeGS1OrEGS6" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS6"
                          name="codeTypeGS1OrEGS6"
                          class="form-control"
                          formControlName="etaCodeType6"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS6" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS6"
                          name="codeGS1OrEGS6"
                          class="form-control"
                          formControlName="etaCode6"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch7 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch7" | translate }}</span>
                        <label for="codeTypeGS1OrEGS7" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS7"
                          name="codeTypeGS1OrEGS7"
                          class="form-control"
                          formControlName="etaCodeType7"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS7" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS7"
                          name="codeGS1OrEGS7"
                          class="form-control"
                          formControlName="etaCode7"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch8 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch8" | translate }}</span>
                        <label for="codeTypeGS1OrEGS8" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS8"
                          name="codeTypeGS1OrEGS8"
                          class="form-control"
                          formControlName="etaCodeType8"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS8" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS8"
                          name="codeGS1OrEGS8"
                          class="form-control"
                          formControlName="etaCode8"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch9 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch9" | translate }}</span>
                        <label for="codeTypeGS1OrEGS9" class="form-label">
                          {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeTypeGS1OrEGS9"
                          name="codeTypeGS1OrEGS9"
                          class="form-control"
                          formControlName="etaCodeType9"
                        />
                      </div>
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <label for="codeGS1OrEGS9" class="form-label">
                          {{ "COMMON.CodeGS1OrEGS" | translate }}
                        </label>
                        <input
                          type="text"
                          id="codeGS1OrEGS9"
                          name="codeGS1OrEGS9"
                          class="form-control"
                          formControlName="etaCode9"
                        />
                      </div>
                    </div>
                    
                    <div class="row">
                      <!-- Branch10 -->
                      <div class="form-group col-xl-3 col-md-4 col-sm-12">
                        <span class="form-label">{{ "COMMON.Branch10" | translate }}</span>
                      <label for="codeTypeGS1OrEGS9" class="form-label">
                        {{ "COMMON.CodeTypeGS1OrEGS" | translate }}
                      </label>
                      <input
                        type="text"
                        id="codeTypeGS1OrEGS9"
                        name="codeTypeGS1OrEGS9"
                        class="form-control"
                        formControlName="etaCodeType10"
                      />
                    </div>
                    <div class="form-group col-xl-3 col-md-4 col-sm-12">
                      <label for="codeGS1OrEGS9" class="form-label">
                        {{ "COMMON.CodeGS1OrEGS" | translate }}
                      </label>
                      <input
                        type="text"
                        id="codeGS1OrEGS9"
                        name="codeGS1OrEGS9"
                        class="form-control"
                        formControlName="etaCode10"
                      />
                    </div>
                  </div>
                    
         
          </div>
          </div>
        </div>
      </form>
    </div>
</div>