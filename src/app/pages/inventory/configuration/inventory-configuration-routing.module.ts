import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BarcodeGenerateComponent} from './barcode-generate/barcode-generate.component';
import {BonusComponent} from './bonus/bonus.component';
import {ManufacturersComponent} from './manufacturers/manufacturers.component';
import {OpteratinsTypesComponent} from './opteratins-types/opteratins-types.component';
import {ProductAttributesComponent} from './product-attributes/product-attributes.component';
import {ProductSubCategoriesComponent} from './product-sub-categories/product-sub-categories.component';
import {ProductsCategoriesComponent} from './products-categories/products-categories.component';
import {ProductsComponent} from './products/products.component';
import {TradeClassificationComponent} from './trade-classification/trade-classification.component';
import {UnitsComponent} from './units/units.component';
import {ProductAttributesValuesComponent} from './product-attributes-values/product-attributes-values.component';
import {ProductsViewComponent} from './products/products-view/products-view.component';
import {WarehouseListComponent} from "./warehouses/warehouse-list/warehouse-list.component";


export const routes: Routes = [
    {
        path: 'warehouses',
        component: WarehouseListComponent,
        data: {tagId: 101},
    },
    {
        path: '',
        redirectTo: 'opteratinsTypes',
        pathMatch: 'full'
    },
    {
        path: 'opteratinsTypes',
        component: OpteratinsTypesComponent
    },
    {
        path: 'productsCategories',
        component: ProductsCategoriesComponent
    },
    {
        path: 'product_sub_categories',
        component: ProductSubCategoriesComponent
    },
    {
        path: 'productAttributes',
        component: ProductAttributesComponent
    },

    {
        path: 'product_attributes_values',
        component: ProductAttributesValuesComponent
    },
    {
        path: 'products',
        component: ProductsComponent
    },
      {
        path: 'products_view/:id',
        component: ProductsViewComponent,
      },
    {
        path: 'products_view',
        component: ProductsViewComponent
    },
    {
        path: 'trade_classification',
        component: TradeClassificationComponent
    },
    {
        path: 'units',
        component: UnitsComponent
    },
    {
        path: 'manufacturers',
        component: ManufacturersComponent
    },
    {
        path: 'barcode_generate',
        component: BarcodeGenerateComponent
    },
    {
        path: 'bonus',
        component: BonusComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InventoryConfigurationRoutingModule {
}
