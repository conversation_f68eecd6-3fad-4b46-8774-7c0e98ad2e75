import { NgModule } from '@angular/core';
import {BarcodeGenerateComponent} from './barcode-generate/barcode-generate.component';
import {BonusComponent} from './bonus/bonus.component';
import {ManufacturersComponent} from './manufacturers/manufacturers.component';
import {OpteratinsTypesComponent} from './opteratins-types/opteratins-types.component';
import {ProductAttributesComponent} from './product-attributes/product-attributes.component';
import {ProductSubCategoriesComponent} from './product-sub-categories/product-sub-categories.component';
import {ProductsCategoriesComponent} from './products-categories/products-categories.component';
import {ProductsComponent} from './products/products.component';
import {TradeClassificationComponent} from './trade-classification/trade-classification.component';
import {UnitsComponent} from './units/units.component';
import {ProductAttributesValuesComponent} from './product-attributes-values/product-attributes-values.component';
import {ProductsViewComponent} from './products/products-view/products-view.component';
import {WarehouseListComponent} from "./warehouses/warehouse-list/warehouse-list.component";
import {InventoryConfigurationRoutingModule} from "./inventory-configuration-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {CommonModule} from "@angular/common";
@NgModule({
  imports: [CommonModule, InventoryConfigurationRoutingModule, SharedModule],
  declarations: [
    WarehouseListComponent,
    OpteratinsTypesComponent,
    ProductsCategoriesComponent,
    ProductAttributesComponent,
    ProductsComponent,
    TradeClassificationComponent,
    UnitsComponent,
    ManufacturersComponent,
    BarcodeGenerateComponent,
    BonusComponent,
    ProductSubCategoriesComponent,
    ProductAttributesValuesComponent,
    ProductsViewComponent,
  ],
})
export class InventoryConfigurationModule {}
