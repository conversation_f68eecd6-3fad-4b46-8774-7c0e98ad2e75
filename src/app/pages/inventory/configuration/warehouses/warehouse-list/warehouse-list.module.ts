import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { WarehouseListComponent } from './warehouse-list.component';
import { ModalsModule } from '../../../../_metronic/partials';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslationModule } from 'src/app/modules/i18n';
import { PermissionsModule } from 'src/app/directives/permissions/permissions.module';
import { DxButtonModule, DxDataGridModule, DxSwitchModule } from 'devextreme-angular';


@NgModule({
  declarations: [WarehouseListComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: WarehouseListComponent,
      },
    ]),
    PermissionsModule,
    ModalsModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgbPaginationModule,
    TranslationModule,
    DxDataGridModule,
    DxButtonModule,
    DxSwitchModule
  ],
})
export class WarehouseListModule { }
