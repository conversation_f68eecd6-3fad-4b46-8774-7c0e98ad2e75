import { ChangeDetectorR<PERSON>, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { InventoryService } from '../../inventory.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
    selector: 'app-replenishment-report',
    templateUrl: './replenishment-report.component.html',
    styleUrls: ['./replenishment-report.component.scss'],
    standalone: false
})

export class ReplenishmentReportComponent implements OnDestroy {
  moduleName = 'inventory.ReplenishmentReport';
  data: any[];
  gridDataSource: any[];
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  tradeTypeId: number = 0;
  tradeTypes: any[];
  maingroups: any[]= [];
  Supgroups:any[];
  mingroupid: number = 0;
  supgroupid: number=0;
  warehouses: any[];
  warehouse: number = 0;
  OptionControl ="allproducts"
    isRtl: boolean = document.documentElement.dir === 'rtl';
    printList: string[] = ['print', 'Print Custome'];
    menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
     actionsList: string[] = [
        'Export',
        'Send via SMS',
        'Send Via Email',
        'Send Via Whatsapp',
      ];
      modalConfig: ModalConfig = {
        modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
          return true;
        },
        dismissButtonLabel: 'Cancel',
      };
    
      smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
      emailModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Email',
      };
      whatsappModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Whatsapp',
      };
      @ViewChild('smsModal') private smsModal: ModalComponent;
      @ViewChild('emailModal') private emailModal: ModalComponent;
      @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(private service: InventoryService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.InventoryReports}replenishment_report`;
    // this.subscription.add(service.list().subscribe(r => {
    //   if (r.success) {
    //     this.data = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
    this.subscription.add(service.getEmployeesDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getProductCategories().subscribe(r => {
      if (r.success) {
        this.maingroups = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getWarehouses().subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getTradeClassification().subscribe(r => {
      if (r.success) {
        this.tradeTypes = r.data;
        this.cdk.detectChanges();
      }
    })
  );
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
   
  }
  onAccountSelect(selectedItem: any) {
    if(!selectedItem) {
      return;
    }
 
    this.Supgroups = this.maingroups
    .filter(r => r.groupid === selectedItem.groupid)
    .flatMap(r => r.productSubCategory);



    this.cdk.detectChanges();

    console.log(this.Supgroups);
    // this.subscription.add(this.service.getProductSubCategoriesById( this.groupId).subscribe(r => {
    //   if (r.success) {
    //     this.Supgroups = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));

  }
  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const CategoryId = this.mingroupid??0;
    const SubCategoryId = this.supgroupid??0;
    const WarehoseId = this.warehouse??0;
    const TradeTypeId = this.tradeTypeId??0;
    this.subscription.add(this.service.list({ CategoryId, SubCategoryId,WarehoseId,TradeTypeId }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
