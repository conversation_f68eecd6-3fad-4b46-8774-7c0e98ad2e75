import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { InventoryService } from '../../inventory.service';
import { formatDate } from '@angular/common';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
    selector: 'app-valuation-report',
    templateUrl: './valuation-report.component.html',
    styleUrls: ['./valuation-report.component.scss'],
    standalone: false
})

export class ValuationReportComponent implements OnDestroy {
  moduleName = 'inventory.valuationreport';
  data: any[];
  gridDataSource: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  year: any = 0;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  maingroups: any[]= [];
  Supgroups:any[];
  mingroupid: number = 0;
  supgroupid: number=0;
  warehouses: any[];
  warehouse: number = 0;
  businessclassifications: any[];
  evaluationbasedonId = 0;
  businessclassificationId= 0;
  lang: any;
  evaluationbasedons:{ id: number; nameAr: string }[] = [
    { id: 1, nameAr: 'اعلى سعر شراء'},
    { id: 2, nameAr: 'آخر سعر شراء' },
    { id: 3, nameAr: 'اعلى سعر بيع' },
    { id: 4, nameAr: 'اقل سعر بيع' },
    { id: 5, nameAr: 'سعر الشراء الموجود في كارت الصنف' },
    { id: 6, nameAr: 'سعر البيع الموجود في كارت الصنف المتوسط' },
    { id: 7, nameAr: 'اقل سعر موجود بكارت الصنف' },
    { id: 8, nameAr: 'المتوسط' },
  ];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  isLoading = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
   actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };

    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;


  constructor(private service: InventoryService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.InventoryReports}InventoryValuationReport`;
    // this.subscription.add(service.list().subscribe(r => {
    //   if (r.success) {
    //     this.data = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
    this.subscription.add(service.getEmployeesDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getProductCategories().subscribe(r => {
      if (r.success) {
        this.maingroups = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getWarehouses().subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cdk.detectChanges();
      }
    }));
    // this.subscription.add(service.getTradeClassification().subscribe(r => {
    //     if (r.success) {
    //       this.businessclassifications = r.data;
    //       this.cdk.detectChanges();
    //     }
    //   })
    // );
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );

  }
  onAccountSelect(selectedItem: any) {
    if(!selectedItem) {
      return;
    }

    this.Supgroups = this.maingroups
    .filter(r => r.groupid === selectedItem.groupid)
    .flatMap(r => r.productSubCategory);



    this.cdk.detectChanges();

    console.log(this.Supgroups);
    // this.subscription.add(this.service.getProductSubCategoriesById( this.groupId).subscribe(r => {
    //   if (r.success) {
    //     this.Supgroups = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));

  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  filter() {
    this.isLoading = true;
    console.log('Loading started:', this.isLoading);
    this.cdk.detectChanges(); // Force update UI

    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const CategoryId = this.mingroupid??0;
    const SubCategoryId = this.supgroupid??0;
    const WarehoseId = this.warehouse??0;
    const ValuationTypeid = this.evaluationbasedonId??0;
    const TradeTypeId = this.businessclassifications??0;
    const Language = this.lang;
    const Year = this.year;

    this.subscription.add(this.service.list({Language,Year, ToDate ,ValuationTypeid,TradeTypeId, CategoryId ,SubCategoryId , WarehoseId }).subscribe(r => {
      this.isLoading = false;
      console.log('Loading finished:', this.isLoading);
      if (r.success) {
        this.data = r.data;
      }
      this.cdk.detectChanges(); // Force update UI
    }, error => {
      this.isLoading = false;
      console.log('Loading error:', this.isLoading);
      this.cdk.detectChanges(); // Force update UI
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
