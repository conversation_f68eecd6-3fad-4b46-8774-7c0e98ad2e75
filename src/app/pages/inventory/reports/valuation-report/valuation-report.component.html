<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
    <h3 class="fw-bolder m-0">{{ "COMMON.valuationReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="d-flex align-items-center">
          <div class="btn-group">
            <button
              (click)="filter()"
              class="btn btn-sm btn-active-light-primary"
              [disabled]="isLoading"
            >
              {{ "COMMON.Filter" | translate }}
              <i *ngIf="!isLoading" class="fa fa-filter ms-1"></i>
            </button>
          </div>
          <div *ngIf="isLoading" class="ms-2">
            <i class="fa fa-spinner fa-pulse text-primary"></i>
          </div>
          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
        </div>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'inventory.valuationreport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="main-inputs">
    <div class="row">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>





    </div>
    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="Mingroupid" class="form-label">
          {{ "COMMON.ProductCategories" | translate }}
        </label>
        <ng-select
          id="mingroupid"
          [(ngModel)]="mingroupid"
          bindLabel="groupname"
          bindValue="groupid"
          [items]="maingroups"
          (change)="onAccountSelect($event)"
        ></ng-select>
      </div>


   <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="Supgroupid" class="form-label">
          {{ "COMMON.ProductSubCategories" | translate }}
        </label>
        <ng-select
          id="supgroupid"
          [(ngModel)]="supgroupid"
          bindLabel="en_Sub_Name"
          bindValue="subid"
          [items]="Supgroups"
        ></ng-select>
      </div>





    </div>
    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="warehouse" class="form-label">
          {{ "COMMON.Warehouse" | translate }}
        </label>
        <ng-select id="warehouse"
                   [(ngModel)]="warehouse"
                   bindLabel="name"
                   bindValue="id"
                   [items]="warehouses">
          </ng-select>
      </div>
      <!-- <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <label for="businessclassifications" class="form-label">
          {{ "COMMON.TradeType" | translate }}
        </label>
        <ng-select
          id="businessclassification"
          [(ngModel)]="businessclassificationId"
          bindLabel="name"
          bindValue="id"
          [items]="businessclassifications"
        ></ng-select>
      </div> -->

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="evaluationbasedonId" class="form-label">
          {{ "COMMON.ValuationType" | translate }}
        </label>
        <ng-select
          id="evaluationbasedonId"
          [(ngModel)]="evaluationbasedonId"
          bindLabel="nameAr"
          bindValue="id"
          [items]="evaluationbasedons"
        ></ng-select>
      </div>





    </div>

  </div>
</div>

<dx-data-grid id="girdcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="Item_Id"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">

  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>
                    <dxi-column
                    dataField="Item_Name"
                    caption="{{ 'COMMON.ProductName' | translate }}">
                    </dxi-column>
                    <dxi-column
                      dataField="Item_Id"
                      caption="{{ 'COMMON.ItemId' | translate }}"
                    ></dxi-column>
                    <dxi-column
                    dataField="Barcode"
                    [caption]="'COMMON.Barcode' | translate"
                  ></dxi-column>
                  <dxi-column
                    dataField="TradeType"
                    caption="{{ 'COMMON.TradeType' | translate }}"
                  ></dxi-column>
                  <dxi-column
                  dataField="itm_code2"
                  caption="{{ 'CUSTOMER.SHORT_AR' | translate }}"
                   ></dxi-column>
                       <dxi-column
                  dataField="rased"
                  caption="{{ 'COMMON.Balance' | translate }}"
                   ></dxi-column>
                    <dxi-column
                    dataField="groupname"
                    caption="{{ 'COMMON.ProductCategories' | translate }}"
                     ></dxi-column>
                         <dxi-column
                    dataField="subname"
                    caption="{{ 'COMMON.ProductSubCategories' | translate }}"
                     ></dxi-column>
            <dxi-column
                    dataField="theprice"
                    caption="{{ 'COMMON.Price' | translate }}"
                     ></dxi-column>
                         <dxi-column
                    dataField="Valtheprice"
                    caption="{{ 'COMMON.Total' | translate }}"
                     ></dxi-column>
  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>

</dx-data-grid>
