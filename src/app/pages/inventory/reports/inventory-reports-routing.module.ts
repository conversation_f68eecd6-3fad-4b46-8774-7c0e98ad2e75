import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BonusesReportsComponent} from './bonus-report/bonus-report.component';
import {ComponentCostReportComponent} from './component-cost-report/component-cost-report.component';
import {ConsumptionReportComponent} from './consumption-report/consumption-report.component';
import {ExpirationDateReportComponent} from './expiration-date-report/expiration-date-report.component';
import {
    MovementByOperationsReportComponent
} from './movement-by-operations-report/movement-by-operations-report.component';
import {MovementByStoreReportComponent} from './movement-by-store-report/movement-by-store-report.component';
import {OperationsControlReportComponent} from './operations-control-report/operations-control-report.component';
import {PriceControlReportComponent} from './price-control-report/price-control-report.component';
import {ProductsBalanceReportComponent} from './products-balance-report/products-balance-report.component';
import {ProductsReportComponent} from './products-report/products-report.component';
import {ProfitabilityReportComponent} from './profitability-report/profitability-report.component';
import {SrialReportComponent} from './srial-report/srial-report.component';
import {StagnantProductsReportComponent} from './stagnant-products-report/stagnant-products-report.component';
import {
    TotalsProductsBalanceReportComponent
} from './totals-products-balance-report/totals-products-balance-report.component';
import {ValuationReportComponent} from './valuation-report/valuation-report.component';
import {TotalValuationReportComponent} from './total-valuation-report/total-valuation-report.component';
import {ReplenishmentReportComponent} from "./replenishment-report/replenishment-report.component";


export const routes: Routes = [
    {
        path: '',
        redirectTo: 'products_report',
        pathMatch: 'full'
    },
    {
        path: 'products_report',
        component: ProductsReportComponent
    },
    {
        path: 'bonus_report',
        component: BonusesReportsComponent
    },
    {
        path: 'profitability_report',
        component: ProfitabilityReportComponent
    },
    {
        path: 'valuation_report',
        component: ValuationReportComponent
    },

    {
        path: 'total_valuation_report',
        component: TotalValuationReportComponent
    },
    {
        path: 'srial_report',
        component: SrialReportComponent
    },
    {
        path: 'products_balance_report',
        component: ProductsBalanceReportComponent
    },
    {
        path: 'totals_products_balance_report',
        component: TotalsProductsBalanceReportComponent
    },
    {
        path: 'movement_by_operations_report',
        component: MovementByOperationsReportComponent
    },
    {
        path: 'movement_by_store_report',
        component: MovementByStoreReportComponent
    },

    {
        path: 'replenishment_report',
        component: ReplenishmentReportComponent
    },
    {
        path: 'stagnant_products_report',
        component: StagnantProductsReportComponent
    },

    {
        path: 'expiration_date_report',
        component: ExpirationDateReportComponent
    },
    {
        path: 'component_cost_report',
        component: ComponentCostReportComponent
    },
    {
        path: 'price_control_report',
        component: PriceControlReportComponent
    },

    {
        path: 'consumption_report',
        component: ConsumptionReportComponent
    },
    {
        path: 'operations_control_report',
        component: OperationsControlReportComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InventoryReportsRoutingModule {
}
