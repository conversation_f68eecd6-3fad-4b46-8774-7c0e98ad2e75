
<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
    <h3 class="fw-bolder m-0">{{ "COMMON.ExpirationDateReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>

        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'ExpirationDateReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="main-inputs">
    <div class="row">

      <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label class="form-label">{{ "COMMON.ProductsExpiringon" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>





    </div>
    <div class="row">
      <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label for="warehouse" class="form-label">
          {{ "COMMON.Warehouse" | translate }}
        </label>
        <ng-select id="warehouse"
                   [(ngModel)]="warehouse"
                   bindLabel="name"
                   bindValue="id"
                   [items]="warehouses">
          </ng-select>
      </div>
      <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label for="product" class="form-label">
          {{ "COMMON.Product" | translate }}
        </label>
        <ng-select
          id="product"
          [(ngModel)]="productId"
          bindLabel="productName"
          bindValue="productId"
          [items]="products"
        ></ng-select>
      </div>






    </div>
    <div class="row">
      <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label for="Mingroupid" class="form-label">
          {{ "COMMON.ProductCategories" | translate }}
        </label>
        <ng-select
          id="mingroupid"
          [(ngModel)]="mingroupid"
          bindLabel="groupname"
          bindValue="groupid"
          [items]="maingroups"
          (change)="onAccountSelect($event)"
        ></ng-select>
      </div>


   <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label for="Supgroupid" class="form-label">
          {{ "COMMON.ProductSubCategories" | translate }}
        </label>
        <ng-select
          id="supgroupid"
          [(ngModel)]="supgroupid"
          bindLabel="en_Sub_Name"
          bindValue="subid"
          [items]="Supgroups"
        ></ng-select>
      </div>





    </div>


  </div>
</div>

<dx-data-grid id="girdcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="code"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">

  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="code"></dxi-column>
  <dxi-column dataField="date"></dxi-column>
  <dxi-column dataField="val"></dxi-column>
  <dxi-column dataField="Cut_Type"></dxi-column>
  <dxi-column dataField="Holiday" caption="Notes"></dxi-column>

  <dxi-column dataField="depname" caption="Depart Name">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>


  <dxi-column dataField="name">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"-->
      <!--[searchExpr]="searchExpr"
      [editorOptions]="editorOptions"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>
  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date"
                    summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val"
                    summaryType="sum"
                    valueFormat="currency">
    </dxi-total-item>

  </dxo-summary>

</dx-data-grid>
