import { NgModule } from '@angular/core';
import {BonusesReportsComponent} from './bonus-report/bonus-report.component';
import {ComponentCostReportComponent} from './component-cost-report/component-cost-report.component';
import {ConsumptionReportComponent} from './consumption-report/consumption-report.component';
import {ExpirationDateReportComponent} from './expiration-date-report/expiration-date-report.component';
import {
  MovementByOperationsReportComponent
} from './movement-by-operations-report/movement-by-operations-report.component';
import {MovementByStoreReportComponent} from './movement-by-store-report/movement-by-store-report.component';
import {OperationsControlReportComponent} from './operations-control-report/operations-control-report.component';
import {PriceControlReportComponent} from './price-control-report/price-control-report.component';
import {ProductsBalanceReportComponent} from './products-balance-report/products-balance-report.component';
import {ProductsReportComponent} from './products-report/products-report.component';
import {ProfitabilityReportComponent} from './profitability-report/profitability-report.component';
import {SrialReportComponent} from './srial-report/srial-report.component';
import {StagnantProductsReportComponent} from './stagnant-products-report/stagnant-products-report.component';
import {
  TotalsProductsBalanceReportComponent
} from './totals-products-balance-report/totals-products-balance-report.component';
import {ValuationReportComponent} from './valuation-report/valuation-report.component';
import {TotalValuationReportComponent} from './total-valuation-report/total-valuation-report.component';
import {CommonModule} from "@angular/common";
import {InventoryReportsRoutingModule} from "./inventory-reports-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ReplenishmentReportComponent} from "./replenishment-report/replenishment-report.component";

@NgModule({
  imports: [CommonModule, InventoryReportsRoutingModule, SharedModule],
  declarations: [
    BonusesReportsComponent,
    ComponentCostReportComponent,
    ProductsReportComponent,
    ConsumptionReportComponent,
    ExpirationDateReportComponent,
    MovementByOperationsReportComponent,
    MovementByStoreReportComponent,
    OperationsControlReportComponent,
    PriceControlReportComponent,
    ProductsBalanceReportComponent,
    ProfitabilityReportComponent,
    ReplenishmentReportComponent,
    SrialReportComponent,
    StagnantProductsReportComponent,
    TotalsProductsBalanceReportComponent,
    ValuationReportComponent,
    TotalValuationReportComponent,
  ],
})
export class InventoryReportsModule {}
