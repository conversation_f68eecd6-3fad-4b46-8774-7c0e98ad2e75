import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {InventoryHomeComponent} from './inventory-home/inventory-home.component';


export const routes: Routes = [
    {
        path: '',
        component: InventoryHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/inventory-configuration.module').then((m) => m.InventoryConfigurationModule),
    },
    {
        path: 'operations',
        loadChildren: () =>
            import('./operations/inventory-operations.module').then((m) => m.InventoryOperationsModule),
    },
    {
        path: 'adjustments',
        loadChildren: () =>
            import('./adjustments/inventory-adjustments.module').then((m) => m.InventoryAdjustmentsModule),
    },
    {
        path: 'reports',
        loadChildren: () =>
            import('./reports/inventory-reports.module').then((m) => m.InventoryReportsModule),
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InventoryRoutingModule {
}
