<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">
          {{ "COMMON.CustomersSamples" | translate }}
        </h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.CustomersSamples'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                (click)="printOpeningBalanceView()"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.CustomersSamples'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="serialId" class="form-label">
              {{ "COMMON.Serial" | translate }}
            </label>
            <input
              id="serialId"
              type="text"
              formControlName="serialId"
              class="form-control"
              min="1"
            />
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="sampleDescription" class="form-label">
              {{ "COMMON.SampleDescription" | translate }}
            </label>
            <input
              id="sampleDescription"
              type="text"
              formControlName="sampleDescription"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">

        </div>
      </div>

      <div class="row">
        <br />
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.Receipts" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.Delivery" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <!--grid controle-->

            <div class="row">
              <div class="form-group col-xl-2 col-md-2 col-sm-12">
                <label class="form-label">{{
                  "COMMON.ReceivingDate" | translate
                }}</label>
                <input
                  id="receivingDate"
                  type="date"
                  formControlName="receivingDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label for="customer" class="form-label">
                  {{ "COMMON.Customer" | translate }}
                </label>
                <ng-select
                  id="customer"
                  formControlName="customerId"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="customers"
                ></ng-select>
              </div>
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label for="employeeId" class="form-label">
                  {{ "COMMON.Employee" | translate }}
                </label>
                <ng-select
                  id="employeeId"
                  formControlName="employeeId"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="Employees"
                ></ng-select>
              </div>
            </div>

            <div class="row">
              <div [style.position]="'relative'">
                <button class="btn btn-sm btn-active-light-primary">
                  {{ "COMMON.SaveChanges" | translate }}
                  <i class="fa fa-save"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab2'"
          [class.active]="activeTab === 'tab2'"
          *ngIf="activeTab === 'tab2'"
        >
          <div class="row">
            <div class="form-group col-xl-2 col-md-2 col-sm-12 ">
              <label class="form-label">{{
                "COMMON.DeliveryDate" | translate
              }}</label>
              <input
                id="deliveryDate"
                type="date"
                formControlName="deliveryDate"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="recipientName" class="form-label">
                {{ "COMMON.RecipientName" | translate }}
              </label>
              <input
                id="recipientName"
                type="text"
                formControlName="recipientName"
                class="form-control"
              />
            </div>
          </div>

          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="nationalId" class="form-label">
                {{ "CUSTOMER.ID_NUMBER" | translate }}
              </label>
              <input
                id="nationalId"
                type="text"
                formControlName="nationalId"
                class="form-control"
              />
            </div>

            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="carNo" class="form-label">
                {{ "COMMON.CarNo" | translate }}
              </label>
              <input
                id="carNo"
                type="text"
                formControlName="carNo"
                class="form-control"
              />
            </div>
          </div>

          <div class="row">
            <div class="form-group col-xl-6 col-md-3 col-sm-12 ">
              <label class="form-label">{{ "COMMON.Notes" | translate }}</label>
              <input class="form-control" />
            </div>
          </div>
          <div class="row">
            <div [style.position]="'relative'">
              <button class="btn btn-sm btn-active-light-primary">
                {{ "COMMON.Delivery" | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
