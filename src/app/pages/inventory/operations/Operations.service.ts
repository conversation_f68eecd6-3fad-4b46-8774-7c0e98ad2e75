import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable, of, shareReplay } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class OperationsService {
  baseUrl: string;
  private warehousesCache: Observable<any>;
  private branchesCache: Observable<any>;
  private journalsCache: Observable<any>;
  private costCentersCache: Observable<any>;
  private costGroupsCache: Observable<any>;
  private operationTypesCache: Observable<any>;

  constructor(private http: HttpClient) {
    // this.baseUrl = `${environment.appUrls.Adjustments}`;
    console.log('OperationsService initialized');
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${id}`);
  }
  list(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${id}`, {
      params: {
        currentPage: page.toString(),
      },
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/names`);
  }

  getUsers(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/users/list`);
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/${id}`, form).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/${id}`).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  activate(id: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/activate/${id}`, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(`${this.baseUrl}/search`, form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(`${this.baseUrl}/filter`, form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(`${this.baseUrl}/excel`, { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(`${this.baseUrl}/excel`, form);
  }
  getWarehouses(): Observable<any> {
    if (!this.warehousesCache) {
      console.log('Fetching warehouses from API');
      this.warehousesCache = this.http.get<any>('api/Warehouses').pipe(
        shareReplay(1) // Cache the last emitted value
      );
    } else {
      console.log('Using cached warehouses data');
    }
    return this.warehousesCache;
  }

  getbranches(): Observable<any> {
    if (!this.branchesCache) {
      console.log('Fetching branches from API');
      this.branchesCache = this.http.get<any>('api/Branch').pipe(
        shareReplay(1)
      );
    } else {
      console.log('Using cached branches data');
    }
    return this.branchesCache;
  }

  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }

  getSuppliersDropdown(): Observable<any> {
    return this.http.get<any>('api/Suppliers/dropdown');
  }

  getCustDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/dropdown');
  }
  getFinancialEntities(entityId: any): Observable<any> {
    if (entityId === 1) {
      return this.http.get<any>('api/Tenders/TendersDropdown');
    } else if (entityId === 2) {
      return this.http.get<any>('api/Machine/dropdown');
    } else if (entityId === 3) {
      return this.http.get<any>('api/Assets/dropdown');
    } else if (entityId === 4) {
      return this.http.get<any>('api/Departments');
    } else if (entityId === 5) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (entityId === 6) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (entityId === 7) {
      return this.http.get<any>('api/Cars/dropdown');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }
  getJournals(): Observable<any> {
    if (!this.journalsCache) {
      console.log('Fetching journals from API');
      this.journalsCache = this.http.get<any>('api/Journal').pipe(
        shareReplay(1)
      );
    } else {
      console.log('Using cached journals data');
    }
    return this.journalsCache;
  }

  getSourcesByType(
    type: 'warehouse' | 'customer' | 'supplier' | 'employee'
  ): Observable<any> {
    console.log('Fetching data for type:', type);
    switch (type) {
      case 'warehouse':
        return this.getWarehouses(); // Use cached version
      case 'customer':
        return this.http.get<any>('api/Customer/dropdown');
      case 'supplier':
        return this.http.get<any>('api/Suppliers/dropdown');
      case 'employee':
        return this.http.get<any>('api/employees/EmployeesDropdown');
      default:
        return of([]); // fallback: empty array
    }
  }

  getCostCenters(): Observable<any> {
    if (!this.costCentersCache) {
      console.log('Fetching cost centers from API');
      this.costCentersCache = this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown').pipe(
        shareReplay(1)
      );
    } else {
      console.log('Using cached cost centers data');
    }
    return this.costCentersCache;
  }

  getOperationTypes(): Observable<any> {
    if (!this.operationTypesCache) {
      console.log('Fetching operation types from API');
      this.operationTypesCache = this.http.get<any>('api/operationTypes').pipe(
        shareReplay(1)
      );
    } else {
      console.log('Using cached operation types data');
    }
    return this.operationTypesCache;
  }

  getCostGroups(): Observable<any> {
    if (!this.costGroupsCache) {
      console.log('Fetching cost groups from API');
      this.costGroupsCache = this.http.get<any>(
        'api/AnalyticAccountsGroups/AnalyticAccountsGroupsDropdown'
      ).pipe(
        shareReplay(1)
      );
    } else {
      console.log('Using cached cost groups data');
    }
    return this.costGroupsCache;
  }

  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }
}
