<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.EmployeesCustody" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.EmployeesCustody'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.DeleteJournal" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.PostingJournal" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.PrintJournal" | translate }}
              </a>
            </div>

            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.EmployeesCustody'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
         
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">


          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label for="warehouse" class="form-label"> 
              {{ "COMMON.Warehouse" | translate }} 
            </label>
                        <ng-select
              id="warehouse"
              formControlName="warehouse"
              bindLabel="name"
              bindValue="id"
              [items]="warehouses"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.DocNo" | translate }}</label>
            <input  class="form-control"
            formControlName="docNo"
            type="text" 
            />
          </div>
          <div class="form-group col-xl-2 col-md-6 col-sm-12 ">

          <button   class="btn btn-sm btn-active-light-primary" title="بحث">
            <i class="fa fa-search"></i>
        </button>
        </div>
        </div>

        



        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.Product" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.OtherInfo" | translate }}
            </button>
              
            </li>

          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
  
              
                <div class="row">
                  <div class="form-group col-xl-3 col-md-4 col-sm-12">
                      <div class="form-label mb-1">{{ "COMMON.Total" | translate }}</div>
                      <input type="number" [value]="total" class="form-control no-shadow-input rows-field" readonly/>
                  </div>
  
                  <div class="form-group col-xl-3 col-md-4 col-sm-12">
                      <div class="form-label mb-1">{{ "COMMON.NumbeRofRows" | translate }}</div>
                      <input type="text" [value]="newdata.get('NumbeRofRows')?.value" class="form-control no-shadow-input rows-field" readonly/>
                   </div>
                 </div>
  
                </div>
  
            </div>
       
  
        <div
        class="tab-pane fade"
        [class.show]="activeTab === 'tab2'"
        [class.active]="activeTab === 'tab2'"
        *ngIf="activeTab === 'tab2'"
      >  
      <div class="row">
        <br>
      </div>
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-6 ">
          <label class="mx-6">
            <input
              type="radio"
              formControlName="optionControl"
              value="handingOvertoEmployee"
              name="optionGroup"
            />
            {{ "COMMON.HandingOvertoEmployee" | translate }}
          </label>
          <label class="mx-6">
            <input
              type="radio"
              formControlName="optionControl"
              value="receivingfromEmployee"
              name="optionGroup"
            />
            {{ "COMMON.ReceivingfromEmployee" | translate }}
          </label>

   </div>
   <div class="form-group col-xl-4 col-md-6 col-sm-12">
    <label for="withoutJournal" class="form-label"> 
      {{ "COMMON.WithoutJournal" | translate }} 
    </label>  
    <dx-check-box
      [value]="withoutJournal"
      formControlName="withoutJournal"
      valueExpr="withoutJournal"
    ></dx-check-box>
  </div>

      </div>
      <div class="row">
        
        <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
          <label for="recipient" class="form-label"> 
            {{ "COMMON.Recipient" | translate }} 
          </label>
                <ng-select
            id="recipient"
            formControlName="recipient"
            bindLabel="name"
            bindValue="id"
            [items]="recipients"
          ></ng-select>
        </div>
        <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
          <label for="journal" class="form-label">  {{ "COMMON.JournalName" | translate }} </label>
          <ng-select
            id="journal"
            formControlName="journal"
            bindLabel="name"
            bindValue="id"
            [items]="journals"
          ></ng-select>
        </div>
      </div>
      <div class="row">

      </div>
          </div>

      </div>

    </div>
  </div>
  </div>

</form>
