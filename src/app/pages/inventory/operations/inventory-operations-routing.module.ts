import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CustomersSamplesComponent} from './customers-samples/customers-samples.component';
import {DeliveryExecuteComponent} from './delivery-execute/delivery-execute.component';
import {DeliveryNoteComponent} from './delivery-note/delivery-note.component';
import {DeliveryRequestComponent} from './delivery-request/delivery-request.component';
import {DeliveryComponent} from './delivery/delivery.component';
import {EmployeesCustodyComponent} from './employees-custody/employees-custody.component';
import {OpeningBalanceComponent} from './opening-balance/opening-balance.component';
import {ReceiptsRequestComponent} from './receipts-request/receipts-request.component';
import {ReceiptsComponent} from './receipts/receipts.component';
import {TransfersComponent} from './transfers/transfers.component';
import { Opening_balance_viewComponent } from './opening-balance/opening_balance_view/opening_balance_view.component';
import { Receipts_request_viewComponent } from './receipts-request/receipts_request_view/receipts_request_view.component';
import { Delivery_request_viewComponent } from './delivery-request/delivery_request_view/delivery_request_view.component';
import { Receipts_viewComponent } from './receipts/receipts_view/receipts_view.component';
import { Delivery_viewComponent } from './delivery/delivery_view/delivery_view.component';
import { Transfers_viewComponent } from './transfers/transfers_view/transfers_view.component';
import { Delivery_execute_viewComponent } from './delivery-execute/delivery_execute_view/delivery_execute_view.component';
import { Delivery_note_viewComponent } from './delivery-note/delivery_note_view/delivery_note_view.component';
import { Customers_samples_viewComponent } from './customers-samples/customers_samples_view/customers_samples_view.component';
import { Employees_custody_viewComponent } from './employees-custody/employees_custody_view/employees_custody_view.component';


export const routes: Routes = [
    {
        path: '',
        redirectTo: 'opening_balance',
        pathMatch: 'full'
    },
    {
        path: 'opening_balance',
        component: OpeningBalanceComponent
    },
    {
        path: 'opening_balance_view',
        component: Opening_balance_viewComponent
    },
    {
        path: 'opening_balance_view/:id',
        component: Opening_balance_viewComponent
    },
    {
        path: 'delivery_request_view',
        component: Delivery_request_viewComponent
    },
    {
        path: 'delivery_request_view/:id',
        component: Delivery_request_viewComponent
    },
    {
        path: 'receipts_view',
        component: Receipts_viewComponent
    },
    {
        path: 'receipts_view/:id',
        component: Receipts_viewComponent
    },
    {
        path: 'delivery_view',
        component: Delivery_viewComponent
    },
    {
        path: 'delivery_view/:id',
        component: Delivery_viewComponent
    },
    {
        path: 'receipts_request_view',
        component: Receipts_request_viewComponent
    },
    {
        path: 'receipts_request_view/:id',
        component: Receipts_request_viewComponent
    },
    {
        path: 'transfers_view',
        component: Transfers_viewComponent
    },
    {
        path: 'transfers_view/:id',
        component: Transfers_viewComponent
    },
    {
        path: 'delivery_execute_view',
        component: Delivery_execute_viewComponent
    },
    {
        path: 'delivery_execute_view/:id',
        component: Delivery_execute_viewComponent
    },
    {
        path: 'delivery_note_view',
        component: Delivery_note_viewComponent
    },
    {
        path: 'delivery_note_view/:id',
        component: Delivery_note_viewComponent
    },
    {
        path: 'customers_samples_view',
        component: Customers_samples_viewComponent
    },
    {
        path: 'customers_samples_view/:id',
        component: Customers_samples_viewComponent
    },
    {
        path: 'employees_custody_view',
        component: Employees_custody_viewComponent
    },
    {
        path: 'employees_custody_view/:id',
        component: Employees_custody_viewComponent
    },
    {
        path: 'receipts_request',
        component: ReceiptsRequestComponent
    },
    {
        path: 'delivery_request',
        component: DeliveryRequestComponent
    },
    {
        path: 'receipts',
        component: ReceiptsComponent
    },
    {
        path: 'transfers',
        component: TransfersComponent
    },
    {
        path: 'delivery',
        component: DeliveryComponent
    },
    {
        path: 'delivery_execute',
        component: DeliveryExecuteComponent
    },

    {
        path: 'delivery_note',
        component: DeliveryNoteComponent
    },

    {
        path: 'customers_samples',
        component: CustomersSamplesComponent
    },
    {
        path: 'employees_custody',
        component: EmployeesCustodyComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InventoryOperationsRoutingModule {
}
