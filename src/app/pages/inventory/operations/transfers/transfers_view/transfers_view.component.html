<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.Transfers" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>
          <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.Transfers'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                (click)="printOpeningBalanceView()"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Transfers'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
         
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="fromwarehouseId" class="form-label">
              {{ "COMMON.FromWarehouse" | translate }}
            </label>
            <ng-select id="fromwarehouseId"
            formControlName="fromwarehouseId" 
             bindLabel="name" bindValue="id"
             [items]="warehouses"></ng-select>
          </div>
        
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="towarehouseId" class="form-label">
              {{ "COMMON.ToWarehouse" | translate }}
            </label>
            <ng-select id="towarehouseId"
                       formControlName="towarehouseId"
                       bindLabel="name"
                       bindValue="id"
                       [items]="warehouses"></ng-select>
          </div>
        </div>
      </div>




      <div class="row">
        <br>
        
      </div>




        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.ProductLabel" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.DataImport" | translate }}
            </button>
              
            </li>
            <li class="nav-item" role="presentation">  
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab3'"
              (click)="setActiveTab('tab3')"
            >
              {{ "COMMON.OtherInfo" | translate }}
            </button>
            </li>
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
  

                <div class="row">
                  <div class="form-group col-xl-3 col-md-4 col-sm-12">
                      <div class="form-label mb-1">{{ "COMMON.Total" | translate }}</div>
                      <input type="number" [value]="total" class="form-control no-shadow-input rows-field" readonly/>
                  </div>

                  <div class="form-group col-xl-3 col-md-4 col-sm-12">
                      <div class="form-label mb-1">{{ "COMMON.NumbeRofRows" | translate }}</div>
                      <input type="text" [value]="newdata.get('NumbeRofRows')?.value" class="form-control no-shadow-input rows-field" readonly/>
                  </div>
          </div>
  
                </div>
  
          </div>
          <div
        class="tab-pane fade"
        [class.show]="activeTab === 'tab2'"
        [class.active]="activeTab === 'tab2'"
        *ngIf="activeTab === 'tab2'"
      >  
      
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
          <label class="form-label">{{ "COMMON.ManufacturingOrder" | translate }}</label>
          <input type="text" class="form-control" />
        </div>
      
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label class="form-label">{{ "COMMON.DeliveryRequest" | translate }}</label>
          <input type="text" class="form-control" />
        </div>
      </div>

          </div>
          <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab3'"
          [class.active]="activeTab === 'tab3'"
          *ngIf="activeTab === 'tab3'"
        >
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
          <label for="branchId" class="form-label">  {{ "COMMON.Branch" | translate }} </label>
          <ng-select
            id="branchId"
            formControlName="branchId"
            bindLabel="name"
            bindValue="id"
            [items]="branches"
          ></ng-select>
        </div>
        <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
          <label class="form-label" 
            formControlName="ManualTransferNo">{{ "COMMON.ManualTransferNo" | translate }}
          </label>
          <input 
          type="text"
           class="form-control" />
          <button  
           class="btn btn-sm btn-active-light-primary" title="بحث">
           <i class="fa fa-search"></i>
        </button>
        </div>
      </div>
            <div class="row">

              <div class="row">
                <div class="form-group col-xl-8 col-md-6 col-sm-12 ">
                <label class="mx-6">
                <input
                 type="radio"
                 formControlName="optionControl2"
                 value="salesInvoice"
                 name="optionGroup2"
                 />
                 {{ "COMMON.SalesInvoice" | translate }}
                 </label>
              <label class="mx-6">
                <input
                  type="radio"
                  formControlName="optionControl2"
                  value="manufacturingOrder"
                  name="optionGroup2"
                />
                {{ "COMMON.SalesTaxInvoice" | translate }}
              </label>
              <label class="mx-6">
                <input
                  type="radio"
                  formControlName="optionControl2"
                  value="salesTaxInvoice"
                  name="optionGroup2"
                />
                {{ "COMMON.ManufacturingOrder" | translate }}
              </label>
                </div>
              </div>
              <div class="row">
                <br>
              </div>
              <div class="row">
                <div class="form-group col-xl-8 col-md-6 col-sm-12 ">
                  <label class="mx-6">
                    <input
                      type="radio"
                      formControlName="optionControl2"
                      value="deliveryOrder"
                      name="optionGroup2"
                    />
                    {{ "COMMON.Receipts" | translate }}
                  </label>
                  <label class="mx-6">
                    <input
                      type="radio"
                      formControlName="optionControl2"
                      value="fromPurchaseTaxInvoice"
                      name="optionGroup2"
                    />
                    {{ "COMMON.DeliveryRequest" | translate }}
                  </label>
            
            </div>
            <div class="row">
              <div class="form-group col-xl-8 col-md-12 col-sm-12">
                <label class="form-label">{{ "COMMON.Notes" | translate }}</label>
            <textarea
            name="notes"
            id="notes" 
            rows="2"
            class="form-control">
            </textarea>
              </div>
            </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</form>