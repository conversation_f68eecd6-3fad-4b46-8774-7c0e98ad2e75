import {ChangeDetectionStrategy,ChangeDetectorRef, Component,On<PERSON><PERSON>roy,OnInit,ViewChild,} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { OperationsService } from '../../Operations.service';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
@Component({
  selector: 'app-transfers_view',
  templateUrl: './transfers_view.component.html',
  styleUrls: ['./transfers_view.component.css'],
  standalone:false
})
export class Transfers_viewComponent implements OnInit ,OnDestroy {
  transfersForm: FormGroup;
  moduleName = 'Inventory.Transfers';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  warehouses: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  branches: [] = [];
  financialYear: any[];
  total = 0;
  activeTab: string = 'tab1';

   menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };

    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
    setActiveTab(tab: string): void {
      this.activeTab=tab;
  }
  constructor(
    private service: OperationsService,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute,
    private router: Router
  ) {
    // تعيين baseUrl بشكل صحيح
    service.baseUrl = `api/Transfers`;

    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );




  }

  ngOnInit() {
    const currentYear = new Date().getFullYear();

    this.newdata = new FormGroup({
      date: new FormControl(
        formatDate(new Date(currentYear, 0, 1), 'yyyy-MM-dd', 'en')
      ),
      warehouse: new FormControl(''),
      branchId: new FormControl(''),
      towarehouseId: new FormControl(''),
      fromwarehouseId: new FormControl(''),
      year: new FormControl( this.year = currentYear),
    });

    // استرجاع البيانات من الخادم
    const id = this.route.snapshot.params.id;
    if (id) {
      console.log('Loading transfer details for ID:', id);
      this.subscriptions.add(
        this.service.details(id).subscribe((r) => {
          if (r.success) {
            console.log('Transfer details loaded successfully:', r.data);
            this.fillForm(r.data);
            this.cdk.detectChanges();
          } else {
            console.error('Failed to load transfer details:', r);
          }
        }, error => {
          console.error('Error loading transfer details:', error);
        })
      );
    }
  }

  onItemClick(e: any) {}

  // دالة لتعبئة النموذج بالبيانات المسترجعة من الخادم
  fillForm(item: any) {
    console.log('Filling form with data:', item);

    // تعبئة البيانات الرئيسية
    Object.keys(item).forEach((key) => {
      if (this.newdata.get(key)) {
        const value = item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });

    // تعبئة بيانات الأصناف
    if (item.transferLines && item.transferLines.length > 0) {
      console.log('Setting transfer lines:', item.transferLines);

      // تحويل بيانات الأصناف إلى التنسيق المطلوب للجدول
      const transformedLines = item.transferLines.map((line: any, i: number) => ({
        lineNo: line.lineNo || i + 1,
        OrderID: i + 1,
        productId: line.productId,
        product: line.productName || '',
        productName: line.productName || '',
        unitId: line.unitId || line.salesUnitId || 0,
        unitName: line.unitName || line.saleUnitName || '',
        Price: line.price || 0,
        Quantity: line.quantity || 0,
        Subtotal: line.amount || 0,
      }));

      // تعيين البيانات المحولة إلى متغير data
      this.data = transformedLines;
      console.log('Data set to:', this.data);

      // تأكد من تحديث واجهة المستخدم
      setTimeout(() => {
        this.cdk.detectChanges();
        console.log('Change detection triggered');
      }, 100);
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/transfers']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/transfers']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }

    discard() {
      this.newdata.reset();
    }

  onDataUpdate(data: any) {
    this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discountPerc = value * 100/ this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountPercChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discount = this.total * value / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.taxPerc = value * 100 / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onVatChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.tax = this.total * value / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
    this.net = this.net.toFixed(2);
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
  printOpeningBalanceView() {
    // Create the print section
    let printContents = document.createElement('div');
    printContents.className = 'Transfers-print-container';

    // Create header section
    const header = document.createElement('div');
    header.className = 'print-header';

    // Company logo and info
    header.innerHTML = `
      <div class="company-info">
        <div class="logo">
          <img src="assets/media/logos/company-logo.png" alt="Company Logo" />
        </div>
        <div class="info">
          <h2>Transfers - التحويل بين المخازن</h2>
          <p>Document #: ${this.newdata.value.id || '---'}</p>
          <p>Date: ${formatDate(this.newdata.value.effectiveDate || new Date(), 'dd/MM/yyyy', 'en')}</p>
          <p>Warehouse: ${this.newdata.value.Warehouse || '---'}</p>
          <p>Branch: ${this.newdata.value.Branch || '---'}</p>
          <p>Year: ${this.newdata.value.financialYear || new Date().getFullYear()}</p>
        </div>
      </div>
    `;

    // Create table for products
    const table = document.createElement('table');
    table.className = 'products-table';

    // Add table header
    table.innerHTML = `
      <thead>
        <tr>
          <th>#</th>
          <th>Item Code - كود الصنف</th>
          <th>Item Name - اسم الصنف</th>
          <th>Unit - الوحدة</th>
          <th>Quantity - الكمية</th>
          <th>Price - السعر</th>
          <th>Total - الإجمالي</th>
        </tr>
      </thead>
      <tbody>
      </tbody>
    `;

    // Add rows for each product
    const tbody = table.querySelector('tbody');
    if (this.data && this.data.length > 0) {
      this.data.forEach((item, index) => {
        const quantity = item.Quantity || 0;
        const price = item.Price || 0;
        const total = quantity * price;

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.LineNo || index + 1}</td>
          <td>${item.productId || '---'}</td>
          <td>${item.productName || item.ProductName || '---'}</td>
          <td>${item.UnitName || '---'}</td>
          <td>${quantity}</td>
          <td>${price.toFixed(2)}</td>
          <td>${total.toFixed(2)}</td>
        `;
        tbody?.appendChild(row);
      });
    }

    // Create footer section with totals
    const footer = document.createElement('div');
    footer.className = 'print-footer';
    footer.innerHTML = `
      <div class="totals">
        <p><strong>Total Items - إجمالي الأصناف:</strong> ${this.data ? this.data.length : 0}</p>
        <p><strong>Total Amount - إجمالي المبلغ:</strong> ${this.total ? this.total.toFixed(2) : '0.00'}</p>
      </div>
      <div class="signatures">
        <div class="signature-box">
          <p>Prepared By - تم بواسطة: ________________</p>
        </div>
        <div class="signature-box">
          <p>Approved By - معتمد من: ________________</p>
        </div>
      </div>
      <div class="notes-section">
        <p><strong>Notes - ملاحظات:</strong> ${this.newdata.value.notes || ''}</p>
      </div>
    `;

    // Add all elements to the print contents
    printContents.appendChild(header);
    printContents.appendChild(table);
    printContents.appendChild(footer);

    // Add print CSS
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        .opening-balance-print-container, .opening-balance-print-container * {
          visibility: visible;
        }
        .opening-balance-print-container {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          padding: 20px;
          direction: ${document.dir === 'rtl' ? 'rtl' : 'ltr'};
        }
        .print-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 10px;
        }
        .company-info {
          display: flex;
          gap: 20px;
        }
        .company-info .logo {
          max-width: 100px;
        }
        .company-info .logo img {
          max-width: 100%;
        }
        .company-info .info h2 {
          margin-top: 0;
          color: #333;
        }
        .products-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .products-table th, .products-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: ${document.dir === 'rtl' ? 'right' : 'left'};
        }
        .products-table th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .products-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .print-footer {
          margin-top: 20px;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
        }
        .signature-box {
          width: 40%;
          border-top: 1px solid #999;
          padding-top: 5px;
          text-align: center;
        }
        .totals {
          text-align: ${document.dir === 'rtl' ? 'left' : 'right'};
        }
        .notes-section {
          margin-top: 20px;
          border-top: 1px dashed #ddd;
          padding-top: 10px;
        }
        @page {
          size: A4;
          margin: 10mm;
        }
      }
    `;

    // Append elements to body, print, then remove
    document.body.appendChild(style);
    document.body.appendChild(printContents);
    window.print();
    document.body.removeChild(style);
    document.body.removeChild(printContents);
  }
}

