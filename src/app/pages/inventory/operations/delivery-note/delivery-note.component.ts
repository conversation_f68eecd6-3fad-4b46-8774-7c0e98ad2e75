import { ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import { InventoryService } from '../../inventory.service';
import { environment } from '../../../../../environments/environment';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-delivery-note',
    templateUrl: './delivery-note.component.html',
    styleUrls: ['./delivery-note.component.scss'],
    standalone: false
})
export class DeliveryNoteComponent implements OnDestroy {
  moduleName = 'Inventory.DeliveryNote';
  viewListForm: FormGroup;
  data: ArrayStore;
  warehouses: any[];
  editorOptions: any;
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ["print", "Print Custome"];
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  searchCtrl: FormControl;
   isRtl: boolean = document.documentElement.dir === 'rtl';
    menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };
  
    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  

  constructor(private service: InventoryService, private cdk: ChangeDetectorRef, private route: ActivatedRoute,) {
    service.baseUrl = `${environment.appUrls.inventory}DeliveryNote`;

    this.subscription.add(service.list().subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getWarehouses().subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['ID', 'Name'];

  }

  ngOnInit(): void {

    const currentYear = new Date().getFullYear();
    this.viewListForm = new FormGroup({
      fromDate: new FormControl(formatDate(new Date(currentYear, 0, 1), "yyyy-MM-dd", "en")),
      toDate: new FormControl(formatDate(new Date(currentYear, 11, 31), "yyyy-MM-dd", "en")),
      warehouse: new FormControl(-1),
    });

    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.service.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.service.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.service.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.service.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }




  onItemClick(e: any) {

  }

  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }

  deleteRecords() {
    this.remove();
  }


  @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(this.service.delete(key)
            .subscribe(r => {
              if (r.success) {
                this.data.remove(key);
                this.dataGrid.instance.refresh();
                this.cdk.detectChanges();
              }
            }));
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'falconData.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  newbtn() {

  }

  loadData(r: any) {
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const Fdates = this.viewListForm.value.fromDate;
    const Tdates = this.viewListForm.value.toDate;
    const WarehousesId = this.viewListForm.value.warehouse;
    this.subscription.add(this.service.filter({ Tdates, Fdates, WarehousesId }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}

 