import {NgModule} from '@angular/core';
import {CustomersSamplesComponent} from './customers-samples/customers-samples.component';
import {DeliveryExecuteComponent} from './delivery-execute/delivery-execute.component';
import {DeliveryNoteComponent} from './delivery-note/delivery-note.component';
import {DeliveryRequestComponent} from './delivery-request/delivery-request.component';
import {DeliveryComponent} from './delivery/delivery.component';
import {EmployeesCustodyComponent} from './employees-custody/employees-custody.component';
import {OpeningBalanceComponent} from './opening-balance/opening-balance.component';
import {ReceiptsRequestComponent} from './receipts-request/receipts-request.component';
import {ReceiptsComponent} from './receipts/receipts.component';
import {TransfersComponent} from './transfers/transfers.component';
import {CommonModule} from "@angular/common";
import {InventoryOperationsRoutingModule} from "./inventory-operations-routing.module";
import {SharedModule} from "../../shared/shared.module";
import { Opening_balance_viewComponent } from './opening-balance/opening_balance_view/opening_balance_view.component';
import { Receipts_request_viewComponent } from './receipts-request/receipts_request_view/receipts_request_view.component';
import { Delivery_request_viewComponent } from './delivery-request/delivery_request_view/delivery_request_view.component';
import { Receipts_viewComponent } from './receipts/receipts_view/receipts_view.component';
import { Delivery_viewComponent } from './delivery/delivery_view/delivery_view.component';
import { Transfers_viewComponent } from './transfers/transfers_view/transfers_view.component';
import { Delivery_execute_viewComponent } from './delivery-execute/delivery_execute_view/delivery_execute_view.component';
import { Delivery_note_viewComponent } from './delivery-note/delivery_note_view/delivery_note_view.component';
import { Customers_samples_viewComponent } from './customers-samples/customers_samples_view/customers_samples_view.component';
import { Employees_custody_viewComponent } from './employees-custody/employees_custody_view/employees_custody_view.component';
import { ProductsGridControleModule } from '../../general/products-grid-controle/ProductsGridControle.module';
import { AgGridModule } from 'ag-grid-angular';

@NgModule({
    imports: [
        CommonModule, 
        InventoryOperationsRoutingModule, 
        SharedModule,
        ProductsGridControleModule,
        AgGridModule
    ],
    declarations: [
        OpeningBalanceComponent,
        ReceiptsRequestComponent,
        DeliveryRequestComponent,
        ReceiptsComponent,
        DeliveryComponent,
        TransfersComponent,
        DeliveryExecuteComponent,
        DeliveryNoteComponent,
        CustomersSamplesComponent,
        EmployeesCustodyComponent,
        Opening_balance_viewComponent,
        Receipts_request_viewComponent,
        Delivery_request_viewComponent,
        Receipts_viewComponent,
        Delivery_viewComponent,
        Transfers_viewComponent,
        Delivery_execute_viewComponent,
        Delivery_note_viewComponent,
        Customers_samples_viewComponent,
        Employees_custody_viewComponent
    ],
})
export class InventoryOperationsModule {
}
