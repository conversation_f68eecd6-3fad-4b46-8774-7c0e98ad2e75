import {ChangeDetectionStrategy,ChangeDetectorRef, Component,On<PERSON><PERSON>roy,OnInit,ViewChild,} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { OperationsService } from '../../Operations.service';
import { formatDate } from '@angular/common';
import { environment } from 'src/environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
@Component({
  selector: 'app-receipts_view',
  templateUrl: './receipts_view.component.html',
  styleUrls: ['./receipts_view.component.css'],
  standalone:false,

})
export class Receipts_viewComponent implements OnInit ,OnDestroy {

  newdata: FormGroup;
  moduleName = 'Inventory.Receipts';
  activeTab: string = 'tab1';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  warehouses: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  branches: [] = [];
  financialYear: any[];
  total = 0;
  // optionControl=null;
   menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };

    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
    setActiveTab(tab: string): void {
      try {
        console.log('Setting active tab:', tab);
        this.activeTab = tab;

        // تعيين التاب النشط في واجهة المستخدم
        setTimeout(() => {
          const tabElement = document.getElementById(tab);
          if (tabElement) {
            tabElement.click();
            console.log('Tab activated:', tab);
          } else {
            console.warn('Tab element not found:', tab);
          }
        }, 100);
      } catch (error) {
        console.error('Error setting active tab:', error);
      }
    }
  constructor(private service: OperationsService, private cdk: ChangeDetectorRef, private route: ActivatedRoute, private router: Router, private fb: FormBuilder) {
     service.baseUrl = `${environment.appUrls.inventory}Receipts`;
    //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
    //this.subscription.add(service.list().subscribe(r => {
    //  if (r.success) {
    //    this.data = r.data;
    //    this.cdk.detectChanges();
    //  }

    //}));


    //this.subscription.add(service.getProductList().subscribe(r => {
    //  if (r.success) {
    //    this.Product = r.data;

    //    this.cdk.detectChanges();

    //  }
    //}));

    //this.subscription.add(service.getAnalyticAccounts().subscribe(r => {
    //  if (r.success) {
    //    this.AnalyticAccounts = r.data;

    //    this.cdk.detectChanges();

    //  }
    //}));

    this.subscription.add(
      service. getOperationTypes().subscribe((r) => {
        if (r.success) {
          this.movementTypes = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );




  }

  createReceiptLineGroup(data?: any): FormGroup {
    return this.fb.group({
      receiptId: [data?.receiptId || 0],
      lineNo: [data?.lineNo || 0],
      productId: [data?.productId || 0],
      saleUnitName: [data?.saleUnitName || ''],
      price: [data?.price || 0],
      quantity: [data?.quantity || 0],
      amount: [data?.amount || 0],
      serialNumber: [data?.serialNumber || ''],
      bonus: [data?.bonus || 0],
      unitQuantity: [data?.unitQuantity || 0],
      unitPrice: [data?.unitPrice || 0],
      defualtItemCost: [data?.defualtItemCost || 0],
      unitCost: [data?.unitCost || 0],
      unitBalance: [data?.unitBalance || 0],
      rate: [data?.rate || 0],
      costAllItemOut: [data?.costAllItemOut || 0],
      margin: [data?.margin || 0],
      salesUnitId: [data?.salesUnitId || 0],
      costId: [data?.costId || 0],
      account_ID: [data?.account_ID || 0],
      warehouseId: [data?.warehouseId || 0],
      store_Account: [data?.store_Account || 0],
      notesPerLine: [data?.notesPerLine || ''],
      variantName: [data?.variantName || ''],
      manufacturingOrder: [data?.manufacturingOrder || 0],
      deliveryOrder: [data?.deliveryOrder || 0],
      scrap: [data?.scrap || 0],
    });
  }

  setReceiptLines(lines: any[]) {
    try {
      console.log('Setting receipt lines:', lines);
      const receiptFormArray = this.newdata.get('receiptLines') as FormArray;

      if (!receiptFormArray) {
        console.error('receiptFormArray is null or undefined');
        return;
      }

      // Clear existing items
      receiptFormArray.clear();

      // Add new items
      if (lines && lines.length > 0) {
        lines.forEach((line) => {
          receiptFormArray.push(this.createReceiptLineGroup(line));
        });
        console.log('Added', lines.length, 'lines to form array');
      } else {
        console.warn('No lines to add to form array');
      }
    } catch (error) {
      console.error('Error in setReceiptLines:', error);
    }
  }

  ngOnInit() {
    console.log('Receipts_viewComponent initialized');

    // تهيئة مصفوفة البيانات
    this.data = [];

    const currentYear = new Date().getFullYear();

    this.newdata = this.fb.group({
      date: [formatDate(new Date(currentYear, 0, 1), 'yyyy-MM-dd', 'en')],
      warehouseId: [null],  // تغيير اسم الحقل المخزن إلى warehouseId
      branchId: [''],
      movementTypeId: [null],
      optionControl: [null],
      optionControl2: [null],
      year: [currentYear],
      NumbeRofRows: [''], // إضافة حقل NumbeRofRows
      source: [null], // إضافة حقل source
      journal: [null], // إضافة حقل journal
      notes: [''], // إضافة حقل notes
      receiptLines: this.fb.array([])
    });

    // تعيين التاب النشط
    this.setActiveTab('tab1');

    // Check if there's an ID in the route parameters
    const id = this.route.snapshot.params.id;
    console.log('Route ID:', id);

    if (id) {
      console.log(`Loading details for receipt ID: ${id}`);
      this.subscription.add(
        this.service.details(id).subscribe(
          (r) => {
            if (r.success) {
              console.log(`Successfully loaded details for receipt ID: ${id}`);
              console.log('Response data:', r.data);
              // طباعة تفاصيل المخزن للتشخيص
              console.log('Warehouse ID from API:', r.data.warehouseId);
              console.log('Movement Type ID from API:', r.data.movementTypeId);
              console.log('Available warehouses:', this.warehouses);

              // تحقق من وجود بيانات الاستلام
              if (r.data && (r.data.receiptsLines || r.data.receiptLines)) {
                const linesArray = r.data.receiptsLines || r.data.receiptLines || [];
                console.log('Receipt lines from API:', linesArray);
                console.log('Receipt lines count:', linesArray.length);
              } else {
                console.warn('No receipt lines in response data');
              }

              this.fillForm(r.data);

              // تأكد من تحديث واجهة المستخدم
              setTimeout(() => {
                // تأكد من أن البيانات موجودة وتم تعيينها بشكل صحيح
                if (this.data && this.data.length > 0) {
                  console.log('Data is available and ready for display:', this.data);
                } else {
                  console.warn('Data is empty or not properly set');
                }

                // طباعة قيم النموذج بعد التعبئة
                console.log('Form values after fill:', this.newdata.value);
                console.log('Current warehouse ID in form:', this.newdata.get('warehouseId')?.value);
                
                // تحديث واجهة المستخدم
                this.cdk.detectChanges();
                console.log('Data after fillForm and timeout:', this.data);
                console.log('Data length after timeout:', this.data ? this.data.length : 0);
              }, 100);
            } else {
              console.error('Failed to load details:', r.message);
            }
          },
          (error) => {
            console.error('Error loading details:', error);
          }
        )
      );
    } else {
      console.log('No ID provided, initializing empty form');
    }
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }



  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }

  fillForm(item: any) {
    console.log('Received data:', item); // إضافة سجل لمعرفة البيانات المستلمة بالضبط

    try {
      // تعيين قيمة المخزن بشكل صريح للتأكد من ظهورها
      if (item.warehouseId) {
        console.log('Setting warehouse ID explicitly:', item.warehouseId);
        this.newdata.get('warehouseId')?.setValue(item.warehouseId);
      }
      
      // تعيين قيمة نوع الحركة بشكل صريح
      if (item.movementTypeId) {
        console.log('Setting movement type ID explicitly:', item.movementTypeId);
        this.newdata.get('movementTypeId')?.setValue(item.movementTypeId);
      }

      // تعبئة باقي حقول النموذج الرئيسي
      Object.keys(item).forEach((key) => {
        if (key !== 'receiptLines' && key !== 'receiptsLines' && 
            key !== 'warehouseId' && key !== 'movementTypeId' && // تم التعامل مع هذه الحقول بشكل منفصل أعلاه
            this.newdata.get(key)) {
          const value =
            item[key] !== undefined && item[key] !== null ? item[key] : '';
          this.newdata.get(key)?.setValue(value);
        }
      });
      
      // إعادة طباعة قيم النموذج للتأكد من تعيين القيم بشكل صحيح
      console.log('Form values after setting fields:', this.newdata.value);

      // تحويل البيانات إلى الشكل المطلوب للعرض في الجدول
      const transformedLines = [];

      // التعامل مع بنود الإيصال - نتحقق من كلا الاسمين المحتملين للخاصية
      const linesArray = item.receiptLines || item.receiptsLines || [];
      if (linesArray && Array.isArray(linesArray)) {
        console.log('Processing receipt lines:', linesArray.length);
        // طباعة عينة من أول سطر لفحص هيكل البيانات
        if (linesArray.length > 0) {
          console.log('First line sample:', linesArray[0]);
        }

        for (let i = 0; i < linesArray.length; i++) {
          const line = linesArray[i];
          // تحديد اسم المنتج (نتحقق من جميع الحقول المحتملة)
          const productName = line.productName || line.ProductName || line.product || line.Product || 
                              (line.product_Name) || line.item || line.itemName || '';
          
          console.log(`Line ${i+1}: productId=${line.productId}, found productName=${productName}`);
          
          transformedLines.push({
            lineNo: line.lineNo || i + 1,
            OrderID: i + 1,  // إضافة معرف ترتيبي مطلوب للداتا جريد
            productId: line.productId,
            // تعيين جميع أشكال اسم المنتج المحتملة لضمان التوافق
            product: productName,
            productName: productName,
            ProductName: productName,
            Product: productName,
            unitId: line.unitId || line.salesUnitId || 0,
            unitName: line.unitName || line.saleUnitName || '',
            // توحيد أسماء الحقول مع ما هو متوقع في واجهة المستخدم
            price: line.price || 0, // استخدام حرف صغير p
            Price: line.price || 0, // مع الاحتفاظ بالنسخة السابقة للتوافق
            quantity: line.quantity || 0, // استخدام حرف صغير q
            Quantity: line.quantity || 0, // مع الاحتفاظ بالنسخة السابقة للتوافق
            subtotal: line.amount || 0, // استخدام حرف صغير s
            Subtotal: line.amount || 0, // مع الاحتفاظ بالنسخة السابقة للتوافق
            serialNumber: line.serialNumber,
            bonus: line.bonus,
            unitQuantity: line.unitQuantity,
            unitPrice: line.unitPrice,
            defualtItemCost: line.defualtItemCost,
            unitCost: line.unitCost,
            unitBalance: line.unitBalance,
            rate: line.rate,
            costAllItemOut: line.costAllItemOut,
            margin: line.margin,
            costId: line.costId,
            account_ID: line.account_ID,
            warehouseId: line.warehouseId,
            store_Account: line.store_Account,
            notesPerLine: line.notesPerLine,
            variantName: line.variantName,
            manufacturingOrder: line.manufacturingOrder,
            deliveryOrder: line.deliveryOrder,
            scrap: line.scrap,
            analyticAccountId: line.analyticAccountId,
            analyticAccountName: line.analyticAccountName
          });
        }

        // تعيين البيانات المحولة إلى متغير data
        this.data = transformedLines;
        console.log("Data set to:", this.data);
        
        // إنشاء مرجع جديد للمصفوفة لضمان اكتشاف التغيير في المكون الفرعي
        this.data = [...this.data];
        // إطلاق حدث تحديث البيانات بشكل صريح
        this.onDataUpdate(this.data);

        console.log('Receipt lines transformed:', this.data.length);

        // تأخير تعبئة FormArray حتى يتم تهيئة النموذج بشكل كامل
        setTimeout(() => {
          try {
            // Set receiptLines for the form
            this.setReceiptLines(item.receiptLines);
            console.log('Receipt lines set to form array');
          } catch (error) {
            console.error('Error setting receipt lines to form array:', error);
          }
        }, 0);

        // تحديث إجمالي القيمة
        this.total = this.data.reduce((sum, item) => sum + (item.Subtotal || 0), 0);
      } else {
        // Initialize with empty array if receiptLines is null or not an array
        this.data = [];
        console.warn('لا توجد بيانات لبنود الاستلام أو أن البيانات فارغة');

        // تأخير تعبئة FormArray حتى يتم تهيئة النموذج بشكل كامل
        setTimeout(() => {
          try {
            this.setReceiptLines([]);
          } catch (error) {
            console.error('Error setting empty receipt lines to form array:', error);
          }
        }, 0);
      }

      // تأكد من تحديث واجهة المستخدم
      setTimeout(() => {
        this.cdk.detectChanges();
        console.log('Change detection triggered');
        console.log('Data after change detection:', this.data);
      }, 100);
    } catch (error) {
      console.error('Error in fillForm:', error);
    }
  }
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/receipts']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/receipts']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }

    discard() {
      this.newdata.reset();
    }

  onDataUpdate(data: any) {
    this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discountPerc = value * 100/ this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountPercChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discount = this.total * value / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.taxPerc = value * 100 / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onVatChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.tax = this.total * value / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
    this.net = this.net.toFixed(2);
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }

}
