/* Print-specific styling for Opening Balance */
@media print {
  /* Hide non-printable elements */
  .card-header, .btn-group, .menu, form, .dx-datagrid-headers, 
  .dx-datagrid-pager, .dx-datagrid-filter-row, .dx-datagrid-search-panel {
    display: none !important;
  }
  
  /* Full width for print */
  body {
    width: 100%;
    margin: 0;
    padding: 0;
    background-color: white;
  }
  
  /* Print header styling */
  .print-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }
  
  .print-header h2 {
    font-size: 18px;
    margin: 0;
    color: #000;
  }
  
  .print-header .company-info {
    font-size: 14px;
    color: #555;
  }
  
  .print-header .print-date {
    font-size: 12px;
    color: #777;
    margin-top: 5px;
  }
  
  /* Table style for printing */
  .print-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .print-table th {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 8px;
    text-align: right;
    font-size: 12px;
    font-weight: bold;
  }
  
  .print-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: right;
    font-size: 11px;
  }
  
  /* Alternating row colors */
  .print-table tr:nth-child(even) {
    background-color: #f9f9f9;
  }
  
  /* Summary section */
  .print-summary {
    margin-top: 20px;
    text-align: left;
    font-size: 12px;
  }
  
  /* Footer section */
  .print-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    padding: 10px;
    font-size: 10px;
    color: #777;
    border-top: 1px solid #ddd;
  }
  
  .print-footer .page-number:after {
    content: counter(page);
  }
  
  /* Company logo */
  .company-logo {
    max-height: 50px;
    margin-bottom: 10px;
  }
  
  /* Portrait orientation */
  @page {
    size: A4 portrait;
    margin: 1cm;
  }
}

/* Loading spinner for print preview */
.print-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.print-loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
