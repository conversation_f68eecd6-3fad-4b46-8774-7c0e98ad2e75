import {ChangeDetectionStrategy,ChangeDetectorRef, Component,On<PERSON><PERSON>roy,OnInit,ViewChild,} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { OperationsService } from '../../Operations.service';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-opening_balance_view',
  templateUrl: './opening_balance_view.component.html',
  styleUrls: ['./opening_balance_view.component.css'],
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Opening_balance_viewComponent implements OnInit, OnDestroy {
  newdata: FormGroup;
  moduleName = 'Inventory.Openingbalance';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  warehouses: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  branches: [] = [];
  financialYear: any[];
  total = 0;
  activeTab: string = 'tab1';

   menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };
  
    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
    setActiveTab(tab: string): void {
      this.activeTab=tab;
  }
  constructor(private service: OperationsService, 
              private cdk: ChangeDetectorRef,
              private router: Router,
              private fb: FormBuilder,
              private route: ActivatedRoute,
  ) {
    const today = new Date().toISOString().slice(0, 10);
    const currentYear = new Date().getFullYear();
    this.newdata = this.fb.group({
      // id: [0],
      today: [today],
      financialYear: [currentYear],
      warehouseId: [null],
      branchId: [null],
      NumbeRofRows: [''],
      dataBaseName: [''],
      notes: [''],
    });
    // Set the proper API URL for Openingbalance operations
    service.baseUrl = environment.apiUrl + '/Openingbalance';
    //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
    //this.subscription.add(service.list().subscribe(r => {
    //  if (r.success) {
    //    this.data = r.data;
    //    this.cdk.detectChanges();
    //  }

    //}));
   

    //this.subscription.add(service.getProductList().subscribe(r => {
    //  if (r.success) {
    //    this.Product = r.data;

    //    this.cdk.detectChanges();

    //  }
    //}));

    //this.subscription.add(service.getAnalyticAccounts().subscribe(r => {
    //  if (r.success) {
    //    this.AnalyticAccounts = r.data;

    //    this.cdk.detectChanges();

    //  }
    //}));

    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    ); 

    
   
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscription.add(
        this.service.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }
  fillForm(item: any) {
    console.log('Received data:', item); // إضافة سجل لمعرفة البيانات المستلمة بالضبط
    
    // تعبئة حقول النموذج الرئيسي
    if (this.newdata.get('today') && item.effectiveDate) {
      this.newdata.get('today')?.setValue(item.effectiveDate.split('T')[0]);
    }
    
    if (this.newdata.get('warehouseId') && item.warehouseId) {
      this.newdata.get('warehouseId')?.setValue(item.warehouseId);
    }
    
    if (this.newdata.get('branchId') && item.branchId) {
      this.newdata.get('branchId')?.setValue(item.branchId);
    }
    
    if (this.newdata.get('financialYear') && item.year) {
      this.newdata.get('financialYear')?.setValue(item.year);
    }
    
    if (this.newdata.get('notes') && item.notes) {
      this.newdata.get('notes')?.setValue(item.notes);
    }

    // التعامل مع الحقول الأخرى إذا لزم الأمر
    Object.keys(item).forEach((key: string) => {
      if (this.newdata.get(key) && !['effectiveDate', 'warehouseId', 'branchId', 'year', 'notes'].includes(key)) {
        const value =
          item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });

    // التعامل مع OpeningbalanceLines إذا كانت موجودة
    if (item.openingbalanceLines && Array.isArray(item.openingbalanceLines) && item.openingbalanceLines.length > 0) {
      // تعيين البيانات للداتا جريد
      this.data = item.openingbalanceLines.map((line: { [key: string]: any }, index: number) => ({
        ...line,
        OrderID: index + 1,  // إضافة معرف ترتيبي مطلوب للداتا جريد
        product: line.productName, // Match product field for display
        productName: line.productName, // Ensure product name field is set
      }));
      // تحديث إجمالي القيمة
      this.onDataUpdate(this.data);
    } else {
      console.warn('لا توجد بيانات لبنود فتح الرصيد أو أن البيانات فارغة');
      this.data = [];
    }
    
    this.cdk.detectChanges();
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/opening_balance']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/inventory/operations/opening_balance']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }

    discard() {
      this.newdata.reset();
    }
  
  onDataUpdate(data: any) {
    this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  discountChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discountPerc = value * 100/ this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountPercChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discount = this.total * value / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.taxPerc = value * 100 / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onVatChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.tax = this.total * value / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
    this.net = this.net.toFixed(2);
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }

  printOpeningBalanceView() {
    // Create the print section
    let printContents = document.createElement('div');
    printContents.className = 'opening-balance-print-container';
    
    // Create header section
    const header = document.createElement('div');
    header.className = 'print-header';
    
    // Company logo and info
    header.innerHTML = `
      <div class="company-info">
        <div class="logo">
          <img src="assets/media/logos/company-logo.png" alt="Company Logo" />
        </div>
        <div class="info">
          <h2>Opening Balance - رصيد افتتاحي</h2>
          <p>Document #: ${this.newdata.value.id || '---'}</p>
          <p>Date: ${formatDate(this.newdata.value.effectiveDate || new Date(), 'dd/MM/yyyy', 'en')}</p>
          <p>Warehouse: ${this.newdata.value.Warehouse || '---'}</p>
          <p>Branch: ${this.newdata.value.Branch || '---'}</p>
          <p>Year: ${this.newdata.value.financialYear || new Date().getFullYear()}</p>
        </div>
      </div>
    `;
    
    // Create table for products
    const table = document.createElement('table');
    table.className = 'products-table';
    
    // Add table header
    table.innerHTML = `
      <thead>
        <tr>
          <th>#</th>
          <th>Item Code - كود الصنف</th>
          <th>Item Name - اسم الصنف</th>
          <th>Unit - الوحدة</th>
          <th>Quantity - الكمية</th>
          <th>Price - السعر</th>
          <th>Total - الإجمالي</th>
        </tr>
      </thead>
      <tbody>
      </tbody>
    `;
    
    // Add rows for each product
    const tbody = table.querySelector('tbody');
    if (this.data && this.data.length > 0) {
      this.data.forEach((item, index) => {
        const quantity = item.Quantity || 0;
        const price = item.Price || 0;
        const total = quantity * price;
        
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.LineNo || index + 1}</td>
          <td>${item.productId || '---'}</td>
          <td>${item.productName || item.ProductName || '---'}</td>
          <td>${item.UnitName || '---'}</td>
          <td>${quantity}</td>
          <td>${price.toFixed(2)}</td>
          <td>${total.toFixed(2)}</td>
        `;
        tbody?.appendChild(row);
      });
    }
    
    // Create footer section with totals
    const footer = document.createElement('div');
    footer.className = 'print-footer';
    footer.innerHTML = `
      <div class="totals">
        <p><strong>Total Items - إجمالي الأصناف:</strong> ${this.data ? this.data.length : 0}</p>
        <p><strong>Total Amount - إجمالي المبلغ:</strong> ${this.total ? this.total.toFixed(2) : '0.00'}</p>
      </div>
      <div class="signatures">
        <div class="signature-box">
          <p>Prepared By - تم بواسطة: ________________</p>
        </div>
        <div class="signature-box">
          <p>Approved By - معتمد من: ________________</p>
        </div>
      </div>
      <div class="notes-section">
        <p><strong>Notes - ملاحظات:</strong> ${this.newdata.value.notes || ''}</p>
      </div>
    `;
    
    // Add all elements to the print contents
    printContents.appendChild(header);
    printContents.appendChild(table);
    printContents.appendChild(footer);
    
    // Add print CSS
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        .opening-balance-print-container, .opening-balance-print-container * {
          visibility: visible;
        }
        .opening-balance-print-container {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          padding: 20px;
          direction: ${document.dir === 'rtl' ? 'rtl' : 'ltr'};
        }
        .print-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 10px;
        }
        .company-info {
          display: flex;
          gap: 20px;
        }
        .company-info .logo {
          max-width: 100px;
        }
        .company-info .logo img {
          max-width: 100%;
        }
        .company-info .info h2 {
          margin-top: 0;
          color: #333;
        }
        .products-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .products-table th, .products-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: ${document.dir === 'rtl' ? 'right' : 'left'};
        }
        .products-table th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .products-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .print-footer {
          margin-top: 20px;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
        }
        .signature-box {
          width: 40%;
          border-top: 1px solid #999;
          padding-top: 5px;
          text-align: center;
        }
        .totals {
          text-align: ${document.dir === 'rtl' ? 'left' : 'right'};
        }
        .notes-section {
          margin-top: 20px;
          border-top: 1px dashed #ddd;
          padding-top: 10px;
        }
        @page {
          size: A4;
          margin: 10mm;
        }
      }
    `;
    
    // Append elements to body, print, then remove
    document.body.appendChild(style);
    document.body.appendChild(printContents);
    window.print();
    document.body.removeChild(style);
    document.body.removeChild(printContents);
  }
}
