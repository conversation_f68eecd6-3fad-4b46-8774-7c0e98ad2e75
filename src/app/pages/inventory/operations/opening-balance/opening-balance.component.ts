import {
  ChangeDetectorR<PERSON>,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import { InventoryService } from '../../inventory.service';
import { environment } from '../../../../../environments/environment';
import { Mo<PERSON>Component, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-opening-balance',
  templateUrl: './opening-balance.component.html',
  styleUrls: ['./opening-balance.component.scss', './opening-balance-print.css'],
  standalone: false,
})
export class OpeningBalanceComponent implements OnDestroy {
  moduleName = 'Inventory.Openingbalance';
  viewListForm: FormGroup;
  data: ArrayStore;
  warehouses: any[];
  editorOptions: any;
  searchExpr: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  searchCtrl = new FormControl(''); // Initialize the searchCtrl FormControl
  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: InventoryService,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute
  ) {
    // Set the correct API URL without the 'Inventory/' segment
    service.baseUrl = environment.apiUrl + '/Openingbalance/';

    this.subscription.add(
      service.list().subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['ID', 'Name'];
  }

  ngOnInit(): void {
    const currentYear = new Date().getFullYear();
    this.viewListForm = new FormGroup({
      fromDate: new FormControl(
        formatDate(new Date(currentYear, 0, 1), 'yyyy-MM-dd', 'en')
      ),
      toDate: new FormControl(
        formatDate(new Date(currentYear, 11, 31), 'yyyy-MM-dd', 'en')
      ),
      warehouse: new FormControl(-1),
    });

    // Fix the route parameter handling
    this.subscriptions.add(
      this.route.paramMap.subscribe((p) => {
        const id = p.get('id');
        if (id) {
          // Pass the id parameter properly
          this.subscriptions.add(
            this.service.details(id).subscribe((r) => {
              if (r.success) {
                this.loadData(r);
              }
            })
          );
        } else {
          // Load the list without an ID
          this.subscriptions.add(
            this.service.list().subscribe((r) => {
              if (r.success) {
                this.loadData(r);
              }
            })
          );
        }
      })
    );

    // Handle search functionality
    this.searchCtrl.valueChanges.subscribe((v) => {
      if (v) {
        this.subscriptions.add(
          this.service.search(v).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            }
          })
        );
      } else {
        // Default list without search term
        this.subscriptions.add(
          this.service.list().subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      }
    });
  }

  onItemClick(e: any) {}

  selectionChanged(data: any) {
    // تخزين المفاتيح المحددة
    if (data && data.selectedRowKeys && data.selectedRowKeys.length >= 0) {
      this.selectedItemKeys = [...data.selectedRowKeys];
      console.log('Selected rows:', this.selectedItemKeys);
    }
    // تطبيق التغييرات
    setTimeout(() => {
      this.cdk.detectChanges();
    }, 0);
  }

  deleteRecords() {
    this.remove();
  }

  @ViewChild(DxDataGridComponent, { static: false })
  dataGrid: DxDataGridComponent;

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      icon: 'warning',
    }).then((r) => {
      if (r.isConfirmed) {
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(
            this.service.delete(key).subscribe((r) => {
              if (r.success) {
                this.data.remove(key);
                this.dataGrid.instance.refresh();
                this.cdk.detectChanges();
              }
            })
          );
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'falconData.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  newbtn() {}

  loadData(r: any) {
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  exportToPDF() {
    const doc = new jsPDF();
    pdfGrid({
      jsPDFDocument: doc,
      component: this.dataGrid.instance,
      indent: 5,
    }).then(() => {
      doc.save('openingBalance.pdf');
    });
  }

  printOpeningBalanceList() {
    // Create a print-specific section for professional printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      Swal.fire({
        title: 'Error',
        text: 'Unable to open print window. Please check your popup settings.',
        icon: 'error',
      });
      return;
    }

    // Get the current date formatted
    const currentDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm', 'en');
    const gridData = this.data ? (this.data as any)._array || [] : [];

    // Generate the print content with header, data table, and footer
    let printContent = `
      <html dir="${document.documentElement.dir}">
        <head>
          <title>Opening Balance Report</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
              padding: 10px;
              border-bottom: 1px solid #ddd;
            }
            .print-header h2 {
              font-size: 18px;
              margin: 0;
              color: #000;
            }
            .print-header .company-info {
              font-size: 14px;
              color: #555;
            }
            .print-header .print-date {
              font-size: 12px;
              color: #777;
              margin-top: 5px;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
            }
            .print-table th {
              background-color: #f5f5f5;
              border: 1px solid #ddd;
              padding: 8px;
              text-align: ${document.documentElement.dir === 'rtl' ? 'right' : 'left'};
              font-size: 12px;
              font-weight: bold;
            }
            .print-table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: ${document.documentElement.dir === 'rtl' ? 'right' : 'left'};
              font-size: 11px;
            }
            .print-table tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .print-summary {
              margin-top: 20px;
              text-align: ${document.documentElement.dir === 'rtl' ? 'right' : 'left'};
              font-size: 12px;
            }
            .print-footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #777;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              @page {
                size: A4 portrait;
                margin: 1cm;
              }
            }
          </style>
        </head>
        <body onload="window.print(); window.setTimeout(function() { window.close(); }, 500)">
          <div class="print-header">
            <h2>Opening Balance Report</h2>
            <div class="company-info">Falcon ERP System</div>
            <div class="print-date">Generated on: ${currentDate}</div>
          </div>

          <table class="print-table">
            <thead>
              <tr>
                <th>Number</th>
                <th>Warehouse</th>
                <th>Effective Date</th>
                <th>Year</th>
                <th>Notes</th>
                <th>User Name</th>
                <th>Branch</th>
              </tr>
            </thead>
            <tbody>
    `;

    // Add data rows
    if (gridData && gridData.length > 0) {
      gridData.forEach((item: any) => {
        const effectiveDate = item.effectiveDate
          ? formatDate(new Date(item.effectiveDate), 'yyyy-MM-dd', 'en')
          : '';

        printContent += `
          <tr>
            <td>${item.id || ''}</td>
            <td>${item.warehouse || ''}</td>
            <td>${effectiveDate}</td>
            <td>${item.year || ''}</td>
            <td>${item.notes || ''}</td>
            <td>${item.userName || ''}</td>
            <td>${item.branch || ''}</td>
          </tr>
        `;
      });
    } else {
      printContent += `
        <tr>
          <td colspan="7" style="text-align: center;">No data available</td>
        </tr>
      `;
    }

    // Close the table and add summary and footer
    printContent += `
            </tbody>
          </table>

          <div class="print-summary">
            <p>Total Items: ${gridData.length}</p>
          </div>

          <div class="print-footer">
            <p>  ${new Date().getFullYear()} Falcon ERP. All rights reserved.</p>
          </div>
        </body>
      </html>
    `;

    // Write to the new window and initiate printing
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
  }

  filter() {
    const FromDate = this.viewListForm.value.fromDate;
    const ToDate = this.viewListForm.value.toDate;
    const WarehousesId = this.viewListForm.value.warehouse ?? 0;
    const BranchId = 0;
    const CompanyId = 0;
    this.subscription.add(
      this.service
        .filter({ ToDate, FromDate, WarehousesId, BranchId, CompanyId })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
