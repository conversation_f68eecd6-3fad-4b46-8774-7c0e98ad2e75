import { formatDate } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import jsPDF from 'jspdf';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { OperationsService } from '../../Operations.service';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'app-receipts_request_view',
  templateUrl: './receipts_request_view.component.html',
  styleUrls: ['./receipts_request_view.component.css'],
  standalone:false
})
export class Receipts_request_viewComponent implements OnInit {

  newdata: FormGroup;
  moduleName = 'Inventory.ReceiptsRequest';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  warehouses: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  suppliers: any[] = [];
  requestTybes: any[] = [
    { id: 1, requestType: 'طلب شراء' },
    { id: 2, requestType: 'طلب تحويل' },
    { id: 3, requestType: 'طلب مرتجع' },
    { id: 4, requestType: 'طلب آخر' }
  ];
  total = 0;
  activeTab: string = 'tab1';
  isLoading: boolean = false;
  setActiveTab(tab: string): void {
    this.activeTab=tab;
}

   menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };

    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(private service: OperationsService,
              private cdk: ChangeDetectorRef,
              private router: Router,
              private fb: FormBuilder,
              private route: ActivatedRoute,
  ) {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      id: [0], // إضافة حقل id للتمييز بين الإنشاء والتحديث
      date: [today, [Validators.required]],
      warehouseId: [null, [Validators.required]],
      invoiceNo: [''],
      supplierId: [null],
      requestType: [null],
      notes: [''],
      NumbeRofRows: [''],
      // إضافة حقول أخرى قد تكون مطلوبة
      products: [[]],
      total: [0]
    });

    // Set the proper API URL for ReceiptsRequest operations
    service.baseUrl = environment.apiUrl + '/ReceiptsRequest';

    //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
    //this.subscription.add(service.list().subscribe(r => {
    //  if (r.success) {
    //    this.data = r.data;
    //    this.cdk.detectChanges();
    //  }

    //}));


    // this.subscription.add(service.getProductList().subscribe(r => {
    //  if (r.success) {
    //    this.Product = r.data;

    //    this.cdk.detectChanges();

    //  }
    // }));

    //this.subscription.add(service.getAnalyticAccounts().subscribe(r => {
    //  if (r.success) {
    //    this.AnalyticAccounts = r.data;

    //    this.cdk.detectChanges();

    //  }
    //}));

    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getSuppliersDropdown().subscribe((r) => {
        if (r.success) {
          this.suppliers = r.data;
          this.cdk.detectChanges();
        }
      })
    );





  }


  ngOnInit(): void {
    // تحميل البيانات عند تحرير سجل موجود
    const id = this.route.snapshot.params.id;
    if (id) {
      // عرض مؤشر التحميل
      this.isLoading = true;

      this.subscription.add(
        this.service.details(id).subscribe({
          next: (r) => {
            if (r.success) {
              console.log('Loaded data:', r.data);
              this.fillForm(r.data);

              // تحديث المجموع إذا كانت هناك منتجات
              if (r.data.products && r.data.products.length > 0) {
                this.onDataUpdate(r.data.products);
              }

              // تحديث واجهة المستخدم
              this.cdk.detectChanges();
            } else {
              console.error('Failed to load data:', r.message);
              // يمكن إضافة رسالة خطأ هنا
            }
          },
          error: (err) => {
            console.error('Error loading data:', err);
            // يمكن إضافة رسالة خطأ هنا
          },
          complete: () => {
            // إخفاء مؤشر التحميل
            this.isLoading = false;
            this.cdk.detectChanges();
          }
        })
      );
    } else {
      // تهيئة نموذج جديد
      this.activeTab = 'tab1'; // تعيين التبويب النشط
    }
  }
  fillForm(item: any) {
    // تعيين قيمة id أولاً
    this.newdata.get('id')?.setValue(item.id || 0);

    // معالجة التاريخ بشكل خاص
    if (item.date) {
      // تحويل التاريخ إلى التنسيق المناسب (YYYY-MM-DD)
      const date = new Date(item.date);
      const formattedDate = date.toISOString().split('T')[0];
      this.newdata.get('date')?.setValue(formattedDate);
    }

    // معالجة باقي الحقول
    Object.keys(item).forEach((key) => {
      if (this.newdata.get(key) && key !== 'id' && key !== 'date') {
        const value =
          item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });

    // تعيين المنتجات إذا كانت موجودة
    if (item.products && Array.isArray(item.products)) {
      this.newdata.get('products')?.setValue(item.products);
    }

    // تعيين المجموع
    if (item.total) {
      this.total = item.total;
      this.newdata.get('total')?.setValue(item.total);
    }

    console.log('Form after filling:', this.newdata.value);
  }
  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }
    save() {
      // التحقق من صحة النموذج
      if (!this.newdata.valid) {
        // تحديد جميع الحقول كـ "touched" لإظهار رسائل الخطأ
        this.newdata.markAllAsTouched();

        // عرض رسالة خطأ للمستخدم
        alert('يرجى التحقق من البيانات المدخلة وملء جميع الحقول المطلوبة');
        return;
      }

      // عرض مؤشر التحميل
      this.isLoading = true;

      // تحضير البيانات للإرسال
      let formData = this.prepareFormData();

      // تحديد ما إذا كانت عملية إنشاء أو تحديث
      const id = this.route.snapshot.params.id;

      // اختيار الطريقة المناسبة (إنشاء أو تحديث)
      const apiCall = id
        ? this.service.update(id, formData)
        : this.service.create(formData);

      // إرسال الطلب إلى الخادم
      this.subscriptions.add(
        apiCall.pipe(
          finalize(() => {
            // إخفاء مؤشر التحميل بغض النظر عن النتيجة
            this.isLoading = false;
            this.cdk.detectChanges();
          })
        ).subscribe({
          next: (r) => {
            if (r.success) {
              // عرض رسالة نجاح
              alert('تم حفظ البيانات بنجاح');

              // الانتقال إلى صفحة القائمة
              this.router.navigate(['/inventory/operations/receipts_request']);
            } else {
              // عرض رسالة خطأ
              alert(`فشل حفظ البيانات: ${r.message || 'خطأ غير معروف'}`);
              console.error('Save failed:', r);
            }
          },
          error: (err) => {
            // عرض رسالة خطأ
            alert('حدث خطأ أثناء حفظ البيانات');
            console.error('Save error:', err);
          }
        })
      );
    }

    // دالة لتحضير البيانات قبل الإرسال
    prepareFormData(): any {
      const formValue = this.newdata.value;

      // تحويل التاريخ إلى التنسيق المناسب إذا لزم الأمر
      if (formValue.date) {
        // التأكد من أن التاريخ بالتنسيق المناسب
        const date = new Date(formValue.date);
        formValue.date = date.toISOString();
      }

      // إضافة المجموع
      formValue.total = this.total;

      // إزالة أي حقول غير مطلوبة
      if (!this.route.snapshot.params.id) {
        // في حالة الإنشاء، نحذف حقل id
        delete formValue.id;
      }

      console.log('Form data to submit:', formValue);
      return formValue;
    }

    discard() {
      // التأكد من رغبة المستخدم في تجاهل التغييرات
      if (confirm('هل أنت متأكد من رغبتك في تجاهل التغييرات؟')) {
        const id = this.route.snapshot.params.id;

        if (id) {
          // في حالة التعديل، نعيد تحميل البيانات الأصلية
          this.subscription.add(
            this.service.details(id).subscribe({
              next: (r) => {
                if (r.success) {
                  this.fillForm(r.data);
                  this.cdk.detectChanges();
                }
              }
            })
          );
        } else {
          // في حالة الإنشاء، نعيد تعيين النموذج إلى القيم الافتراضية
          const today = new Date().toISOString().slice(0, 10);
          this.newdata.reset({
            id: 0,
            date: today,
            warehouseId: null,
            invoiceNo: '',
            supplierId: null,
            requestType: null,
            notes: '',
            NumbeRofRows: '',
            products: [],
            total: 0
          });

          // إعادة تعيين المجموع
          this.total = 0;
        }

        // إعادة تعيين التبويب النشط
        this.activeTab = 'tab1';
      }
    }

  onDataUpdate(data: any) {
    this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discountPerc = value * 100/ this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountPercChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discount = this.total * value / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.taxPerc = value * 100 / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onVatChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.tax = this.total * value / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
    this.net = this.net.toFixed(2);
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
