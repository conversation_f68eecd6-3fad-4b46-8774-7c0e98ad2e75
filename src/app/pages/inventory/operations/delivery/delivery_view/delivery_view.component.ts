import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { OperationsService } from '../../Operations.service';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-delivery_view',
  templateUrl: './delivery_view.component.html',
  styleUrls: ['./delivery_view.component.css'],
  standalone: false,
})
export class Delivery_viewComponent implements OnInit, OnDestroy {
  newdata: FormGroup;
  moduleName = 'Inventory.Delivery';
  activeTab: string = 'tab1';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  sources: any[] = [];
  warehouses: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  branches: any[];
  costCenter: any[];
  costGroup: any[];
  Journals: any[];
  FinancialEntities: any[];
  sourceBindLabel: string = 'nameAr';
  deliveryLines: any[] = [];
  total = 0;
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  financial_entity_TypeId: any = 0;
  financial_entity_Id: any = 0;
  analyticAccountTypeId: any = 0;
  financial_entity_Types: { id: number; name: string }[] = [
    { id: 1, name: 'Project' },
    { id: 2, name: 'Machine' },
    { id: 3, name: 'Assets' },
    { id: 4, name: 'Department' },
    { id: 5, name: 'Supplier' },
    { id: 6, name: 'Customer' },
    { id: 7, name: 'Cars' },
  ];
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  constructor(
    private service: OperationsService,
    private cdk: ChangeDetectorRef,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    const currentYear = new Date().getFullYear();
    this.newdata = this.fb.group({
      optionControl: ['warehouse'],
      source: [null],
      warehouseId: [null,Validators.required],
      customer_id: [0],
      invoiceNO: [0],
      effectiveDate: [today],
      year: [currentYear],
      notes: [''],
      actionDate: [today],
      userName: [''],
      companyId: [0],
      branchId: [0],
      journal: [0],
      movementTypeId: [null],
      consumerID: [0],
      inVUsed: [0],
      docNO: [''],
      tO_WarehouseId: [0],
      projectID: [0],
      toSupplierId: [0],
      toEmpId: [0],
      destination_Type: [0],
      destTypeId: [0],
      NumbeRofRows: [''], // إضافة حقل NumbeRofRows
      optionControl2: [''], // إضافة حقل optionControl2
      deliveryLines: this.fb.array([]),
    });

    // Set the baseUrl for the service to ensure API calls work correctly
    service.baseUrl = `${environment.appUrls.inventory}Delivery`;

    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getJournals().subscribe((r) => {
        if (r.success) {
          this.journals = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCostGroups().subscribe((r) => {
        if (r.success) {
          this.costGroup = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service. getOperationTypes().subscribe((r) => {
        if (r.success) {
          this.movementTypes = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  createDeliveryLineGroup(data?: any): FormGroup {
    return this.fb.group({
      deliveryId: [data?.deliveryId || 0],
      lineNo: [data?.lineNo || 0],
      productId: [data?.productId || 0],
      saleUnitName: [data?.saleUnitName || ''],
      price: [data?.price || 0],
      quantity: [data?.quantity || 0],
      amount: [data?.amount || 0],
      serialNumber: [data?.serialNumber || ''],
      bonus: [data?.bonus || 0],
      unitQuantity: [data?.unitQuantity || 0],
      unitPrice: [data?.unitPrice || 0],
      defualtItemCost: [data?.defualtItemCost || 0],
      unitCost: [data?.unitCost || 0],
      unitBalance: [data?.unitBalance || 0],
      rate: [data?.rate || 0],
      costAllItemOut: [data?.costAllItemOut || 0],
      margin: [data?.margin || 0],
      salesUnitId: [data?.salesUnitId || 0],
      costId: [data?.costId || 0],
      account_ID: [data?.account_ID || 0],
      warehouseId: [data?.warehouseId || 0],
      store_Account: [data?.store_Account || 0],
      notesPerLine: [data?.notesPerLine || ''],
      variantName: [data?.variantName || ''],
      manufacturingOrder: [data?.manufacturingOrder || 0],
      deliveryOrder: [data?.deliveryOrder || 0],
      scrap: [data?.scrap || 0],
    });
  }
  setDeliveryLines(lines: any[]) {
    try {
      console.log('Setting delivery lines:', lines);
      const deliveryFormArray = this.newdata.get('deliveryLines') as FormArray;

      if (!deliveryFormArray) {
        console.error('deliveryFormArray is null or undefined');
        return;
      }

      // Clear existing items
      deliveryFormArray.clear();

      // Add new items
      if (lines && lines.length > 0) {
        lines.forEach((line) => {
          deliveryFormArray.push(this.createDeliveryLineGroup(line));
        });
        console.log('Added', lines.length, 'lines to form array');
      } else {
        console.warn('No lines to add to form array');
      }
    } catch (error) {
      console.error('Error in setDeliveryLines:', error);
    }
  }

  ngOnInit() {
    console.log('Delivery_viewComponent initialized');

    // this.newdata = this.fb.group({
    //   optionControl: ['warehouse'],
    //   source: [null],
    // });

    this.newdata.get('optionControl')?.valueChanges.subscribe((value: any) => {
      this.loadSources(value, false); // Pass false to avoid loading details again
    });

    const id = this.route.snapshot.params.id;
    console.log('Route ID:', id);

    // Load sources first
    this.loadSources(this.newdata.get('optionControl')?.value, true);

    // تعيين التاب النشط
    this.setActiveTab('tab1');

    // Then load details if ID exists (only once)
    if (id) {
      console.log(`Loading details for delivery ID: ${id}`);
      this.subscription.add(
        this.service.details(id).subscribe(
          (r) => {
            if (r.success) {
              console.log(`Successfully loaded details for delivery ID: ${id}`);
              console.log('Response data:', r.data);

              // تحقق من وجود بيانات التسليم
              if (r.data && r.data.deliveryLines) {
                console.log('Delivery lines from API:', r.data.deliveryLines);
                console.log('Delivery lines count:', r.data.deliveryLines.length);
              } else {
                console.warn('No delivery lines in response data');
              }

              this.fillForm(r.data);
              this.roles = r.roles;

              // تأكد من تحديث واجهة المستخدم
              setTimeout(() => {
                this.cdk.detectChanges();
                console.log('Data after fillForm and timeout:', this.data);
                console.log('Data length after timeout:', this.data ? this.data.length : 0);
              }, 100);
            } else {
              console.error('Failed to load details:', r.message);
            }
          },
          (error) => {
            console.error('Error loading details:', error);
          }
        )
      );
    } else {
      // For testing purposes, load sample data
      console.log('No ID provided, loading sample data');
      this.loadSampleDeliveryLines();

      // Log the data after loading sample data
      setTimeout(() => {
        console.log('Sample data loaded after timeout:', this.data);
        console.log('Sample data length after timeout:', this.data ? this.data.length : 0);
        this.cdk.detectChanges();
      }, 100);
    }
  }

  // Method to load sample delivery lines for testing
  loadSampleDeliveryLines() {
    // Sample data provided by the user
    const sampleData = {
      deliveryId: 8,
      lineNo: 2,
      productId: 7270,
      productName: "شاكوش 1كجم",
      unitId: 1,
      unitName: "حبة",
      price: 6,
      quantity: 2,
      amount: 11.66,
      serialNumber: "",
      bonus: 0,
      unitQuantity: 2,
      unitPrice: 5.83,
      defualtItemCost: 5.83,
      unitCost: 5.83,
      unitBalance: 6,
      rate: 1,
      costAllItemOut: 11.66,
      margin: 0,
      costId: 10101,
      account_ID: null,
      warehouseId: null,
      store_Account: null,
      notesPerLine: null,
      variantName: null,
      manufacturingOrder: 0,
      deliveryOrder: 0,
      scrap: 0,
      analyticAccountId: null,
      analyticAccountName: null
    };

    // Create sample data array with the format expected by app-products-grid-controle
    const sampleLines = [
      {
        lineNo: 2,
        OrderID: 2,
        productId: 7270,
        product: "شاكوش 1كجم",
        productName: "شاكوش 1كجم",
        unitId: 1,
        unitName: "حبة",
        Price: 6,
        Quantity: 2,
        Subtotal: 11.66,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 2,
        unitPrice: 5.83,
        defualtItemCost: 5.83,
        unitCost: 5.83,
        unitBalance: 6,
        rate: 1,
        costAllItemOut: 11.66,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 1,
        OrderID: 1,
        productId: 6778,
        product: "رولة نيلون ثقيل",
        productName: "رولة نيلون ثقيل",
        unitId: 1,
        unitName: "حبة",
        Price: 4,
        Quantity: 4,
        Subtotal: 15,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 4,
        unitPrice: 3.75,
        defualtItemCost: 3.75,
        unitCost: 3.75,
        unitBalance: 28,
        rate: 1,
        costAllItemOut: 15,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 3,
        OrderID: 3,
        productId: 7318,
        product: "شاكوش حديد 2 كيلو صيني",
        productName: "شاكوش حديد 2 كيلو صيني",
        unitId: 1,
        unitName: "حبة",
        Price: 9,
        Quantity: 2,
        Subtotal: 18.32,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 2,
        unitPrice: 9.16,
        defualtItemCost: 9.16,
        unitCost: 9.16,
        unitBalance: 7,
        rate: 1,
        costAllItemOut: 18.32,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 4,
        OrderID: 4,
        productId: 1066,
        product: "ازميل بيد روبل صغير",
        productName: "ازميل بيد روبل صغير",
        unitId: 1,
        unitName: "حبة",
        Price: 4,
        Quantity: 2,
        Subtotal: 7.52,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 2,
        unitPrice: 3.76,
        defualtItemCost: 3.76,
        unitCost: 3.76,
        unitBalance: 17,
        rate: 1,
        costAllItemOut: 7.52,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 5,
        OrderID: 5,
        productId: 5293,
        product: "قفـــل صيــنى 265",
        productName: "قفـــل صيــنى 265",
        unitId: 1,
        unitName: "حبة",
        Price: 8,
        Quantity: 1,
        Subtotal: 8,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 1,
        unitPrice: 8,
        defualtItemCost: 8,
        unitCost: 8,
        unitBalance: 11,
        rate: 1,
        costAllItemOut: 8,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 6,
        OrderID: 6,
        productId: 3401,
        product: "علبة مسمار صلب 5 سم أسود هولند",
        productName: "علبة مسمار صلب 5 سم أسود هولند",
        unitId: 6,
        unitName: "علبة",
        Price: 3,
        Quantity: 2,
        Subtotal: 6.2,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 2,
        unitPrice: 3.1,
        defualtItemCost: 3.1,
        unitCost: 3.1,
        unitBalance: 36,
        rate: 1,
        costAllItemOut: 6.2,
        margin: 0,
        costId: 10101
      },
      {
        lineNo: 7,
        OrderID: 7,
        productId: 7453,
        product: "سكــروب رقــم 8 ً",
        productName: "سكــروب رقــم 8 ً",
        unitId: 1,
        unitName: "حبة",
        Price: 1,
        Quantity: 3,
        Subtotal: 3.99,
        serialNumber: "",
        bonus: 0,
        unitQuantity: 3,
        unitPrice: 1.33,
        defualtItemCost: 1.33,
        unitCost: 1.33,
        unitBalance: 42,
        rate: 1,
        costAllItemOut: 3.99,
        margin: 0,
        costId: 10101
      }
    ];

    // Set data to the sample array
    this.data = sampleLines;

    console.log('Sample delivery lines loaded:', this.data);

    // Update the total
    this.total = this.data.reduce((sum, item) => sum + (item.Subtotal || 0), 0);

    // Notify the change detector
    this.cdk.detectChanges();
  }

  loadSources(type: 'warehouse' | 'customer' | 'supplier' | 'employee', loadDetails: boolean = false) {
    console.log(`Loading sources for type: ${type}`);
    this.service.getSourcesByType(type).subscribe((r: any) => {
      if (r.success) {
        console.log(`Successfully loaded ${r.data?.length || 0} sources for type: ${type}`);
        this.sources = r.data;
        type == 'warehouse'
          ? (this.sourceBindLabel = 'name')
          : (this.sourceBindLabel = 'nameAr');

        this.newdata.get('source')?.setValue(null);
        this.cdk.markForCheck();
      }
    });
  }
  onFinaTypeSelect(selectedItem: any) {
    this.FinancialEntities = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    // this.financial_entity_TypeId= selectedItem.id;

    this.subscription.add(
      this.service
        .getFinancialEntities(this.financial_entity_TypeId)
        .subscribe((r) => {
          if (r.success) {
            this.FinancialEntities = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }


  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }
  fillForm(item: any) {
    console.log('Received data:', item); // إضافة سجل لمعرفة البيانات المستلمة بالضبط

    try {
      // تعبئة حقول النموذج الرئيسي
      Object.keys(item).forEach((key) => {
        if (key !== 'deliveryLines' && this.newdata.get(key)) {
          const value =
            item[key] !== undefined && item[key] !== null ? item[key] : '';
          this.newdata.get(key)?.setValue(value);
        }
      });

      // تحويل البيانات إلى الشكل المطلوب للعرض في الجدول - بنفس طريقة opening_balance_view
      const transformedLines = [];

      // Handle deliveryLines separately
      if (item.deliveryLines && Array.isArray(item.deliveryLines)) {
        console.log('Processing delivery lines:', item.deliveryLines.length);

        for (let i = 0; i < item.deliveryLines.length; i++) {
          const line = item.deliveryLines[i];
          transformedLines.push({
            lineNo: line.lineNo || i + 1,
            OrderID: i + 1,  // إضافة معرف ترتيبي مطلوب للداتا جريد
            productId: line.productId,
            product: line.productName || '', // Match product field for display
            productName: line.productName || '', // Ensure product name field is set
            unitId: line.unitId || line.salesUnitId || 0,
            unitName: line.unitName || line.saleUnitName || '',
            Price: line.price,
            Quantity: line.quantity,
            Subtotal: line.amount,
            serialNumber: line.serialNumber,
            bonus: line.bonus,
            unitQuantity: line.unitQuantity,
            unitPrice: line.unitPrice,
            defualtItemCost: line.defualtItemCost,
            unitCost: line.unitCost,
            unitBalance: line.unitBalance,
            rate: line.rate,
            costAllItemOut: line.costAllItemOut,
            margin: line.margin,
            costId: line.costId,
            account_ID: line.account_ID,
            warehouseId: line.warehouseId,
            store_Account: line.store_Account,
            notesPerLine: line.notesPerLine,
            variantName: line.variantName,
            manufacturingOrder: line.manufacturingOrder,
            deliveryOrder: line.deliveryOrder,
            scrap: line.scrap,
            analyticAccountId: line.analyticAccountId,
            analyticAccountName: line.analyticAccountName
          });
        }

        // تعيين البيانات المحولة إلى متغير data
        this.data = transformedLines;

        console.log('Delivery lines transformed:', this.data.length);

        // تأخير تعبئة FormArray حتى يتم تهيئة النموذج بشكل كامل
        setTimeout(() => {
          try {
            // Set deliveryLines for the form
            this.setDeliveryLines(item.deliveryLines);
            console.log('Delivery lines set to form array');
          } catch (error) {
            console.error('Error setting delivery lines to form array:', error);
          }
        }, 0);

        // تحديث إجمالي القيمة
        this.total = this.data.reduce((sum, item) => sum + (item.Subtotal || 0), 0);
      } else {
        // Initialize with empty array if deliveryLines is null or not an array
        this.data = [];
        console.warn('لا توجد بيانات لبنود التسليم أو أن البيانات فارغة');

        // تأخير تعبئة FormArray حتى يتم تهيئة النموذج بشكل كامل
        setTimeout(() => {
          try {
            this.setDeliveryLines([]);
          } catch (error) {
            console.error('Error setting empty delivery lines to form array:', error);
          }
        }, 0);
      }

      // تأكد من تحديث واجهة المستخدم
      setTimeout(() => {
        this.cdk.detectChanges();
        console.log('Change detection triggered');
      }, 100);
    } catch (error) {
      console.error('Error in fillForm:', error);
    }
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.service
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/inventory/operations/delivery']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.service
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/inventory/operations/delivery']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }

  discard() {
    this.newdata.reset();
  }

  onDataUpdate(data: any) {
    console.log('Data updated from grid:', data);

    // Update the deliveryLines array with the new data
    this.deliveryLines = data;

    // Calculate total based on amount field
    this.total = data.reduce(
      (sum: any, item: any) => sum + (item.amount || item.Subtotal || 0),
      0
    );

    console.log('Total calculated:', this.total);

    // Update net amount
    this.net =
      this.total -
      (this.discount || 0) +
      (this.vat || 0) -
      (this.tax || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);

    // Update the form array
    const deliveryFormArray = this.newdata.get('deliveryLines') as FormArray;
    deliveryFormArray.clear();

    data.forEach((line: any) => {
      deliveryFormArray.push(this.createDeliveryLineGroup(line));
    });
  }

  discountChanged(event: any) {
    const value = +event.target.value;
    if (value) {
      this.discountPerc = (value * 100) / this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net =
      this.total -
      (value || 0) +
      (this.vat || 0) -
      (this.tax || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  discountPercChanged(event: any) {
    const value = +event.target.value;
    if (value) {
      this.discount = (this.total * value) / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net =
      this.total -
      (this.discount || 0) +
      (this.vat || 0) -
      (this.tax || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxChange(event: any) {
    const value = +event.target.value;
    if (value) {
      this.taxPerc = (value * 100) / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net =
      this.total -
      (this.discount || 0) +
      (this.vat || 0) -
      (value || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onVatChange(event: any) {
    const value = +event.target.value;
    this.net =
      this.total -
      (this.discount || 0) +
      (value || 0) -
      (this.tax || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if (value) {
      this.tax = (this.total * value) / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net =
      this.total -
      (this.discount || 0) +
      (+this.vat || 0) -
      (this.tax || 0) +
      (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }

  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net =
      this.total -
      (this.discount || 0) +
      (this.vat || 0) -
      (this.tax || 0) +
      (value || 0);
    this.net = this.net.toFixed(2);
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
  printOpeningBalanceView() {
    // Create the print section
    let printContents = document.createElement('div');
    printContents.className = 'opening-balance-print-container';

    // Create header section
    const header = document.createElement('div');
    header.className = 'print-header';

    // Company logo and info
    header.innerHTML = `
      <div class="company-info">
        <div class="logo">
          <img src="assets/media/logos/company-logo.png" alt="Company Logo" />
        </div>
        <div class="info">
          <h2>Delivery - اذن صرف</h2>
          <p>Document #: ${this.newdata.value.id || '---'}</p>
          <p>Date: ${formatDate(this.newdata.value.effectiveDate || new Date(), 'dd/MM/yyyy', 'en')}</p>
          <p>Warehouse: ${this.newdata.value.Warehouse || '---'}</p>
          <p>Branch: ${this.newdata.value.Branch || '---'}</p>
          <p>Year: ${this.newdata.value.financialYear || new Date().getFullYear()}</p>
        </div>
      </div>
    `;

    // Create table for products
    const table = document.createElement('table');
    table.className = 'products-table';

    // Add table header
    table.innerHTML = `
      <thead>
        <tr>
          <th>#</th>
          <th>Item Code - كود الصنف</th>
          <th>Item Name - اسم الصنف</th>
          <th>Unit - الوحدة</th>
          <th>Quantity - الكمية</th>
          <th>Price - السعر</th>
          <th>Total - الإجمالي</th>
        </tr>
      </thead>
      <tbody>
      </tbody>
    `;

    // Add rows for each product
    const tbody = table.querySelector('tbody');
    if (this.data && this.data.length > 0) {
      this.data.forEach((item, index) => {
        const quantity = item.Quantity || 0;
        const price = item.Price || 0;
        const total = quantity * price;

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.LineNo || index + 1}</td>
          <td>${item.productId || '---'}</td>
          <td>${item.productName || item.ProductName || '---'}</td>
          <td>${item.UnitName || '---'}</td>
          <td>${quantity}</td>
          <td>${price.toFixed(2)}</td>
          <td>${total.toFixed(2)}</td>
        `;
        tbody?.appendChild(row);
      });
    }

    // Create footer section with totals
    const footer = document.createElement('div');
    footer.className = 'print-footer';
    footer.innerHTML = `
      <div class="totals">
        <p><strong>Total Items - إجمالي الأصناف:</strong> ${this.data ? this.data.length : 0}</p>
        <p><strong>Total Amount - إجمالي المبلغ:</strong> ${this.total ? this.total.toFixed(2) : '0.00'}</p>
      </div>
      <div class="signatures">
        <div class="signature-box">
          <p>Prepared By - تم بواسطة: ________________</p>
        </div>
        <div class="signature-box">
          <p>Approved By - معتمد من: ________________</p>
        </div>
      </div>
      <div class="notes-section">
        <p><strong>Notes - ملاحظات:</strong> ${this.newdata.value.notes || ''}</p>
      </div>
    `;

    // Add all elements to the print contents
    printContents.appendChild(header);
    printContents.appendChild(table);
    printContents.appendChild(footer);

    // Add print CSS
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        .opening-balance-print-container, .opening-balance-print-container * {
          visibility: visible;
        }
        .opening-balance-print-container {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          padding: 20px;
          direction: ${document.dir === 'rtl' ? 'rtl' : 'ltr'};
        }
        .print-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 10px;
        }
        .company-info {
          display: flex;
          gap: 20px;
        }
        .company-info .logo {
          max-width: 100px;
        }
        .company-info .logo img {
          max-width: 100%;
        }
        .company-info .info h2 {
          margin-top: 0;
          color: #333;
        }
        .products-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .products-table th, .products-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: ${document.dir === 'rtl' ? 'right' : 'left'};
        }
        .products-table th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .products-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .print-footer {
          margin-top: 20px;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
        }
        .signature-box {
          width: 40%;
          border-top: 1px solid #999;
          padding-top: 5px;
          text-align: center;
        }
        .totals {
          text-align: ${document.dir === 'rtl' ? 'left' : 'right'};
        }
        .notes-section {
          margin-top: 20px;
          border-top: 1px dashed #ddd;
          padding-top: 10px;
        }
        @page {
          size: A4;
          margin: 10mm;
        }
      }
    `;

    // Append elements to body, print, then remove
    document.body.appendChild(style);
    document.body.appendChild(printContents);
    window.print();
    document.body.removeChild(style);
    document.body.removeChild(printContents);
  }
}

