<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.Delivery" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.Delivery'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                (click)="printOpeningBalanceView()"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>

            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs">
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12 mb-3">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="effectiveDate"
              type="date"
              formControlName="effectiveDate"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="movementType" class="form-label">
              {{ "COMMON.MovementType" | translate }}
            </label>
            <ng-select
              id="movementTypeId"
              formControlName="movementTypeId"
              bindLabel="name"
              bindValue="id"
              [items]="movementTypes"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="warehouseId" class="form-label">
              {{ "COMMON.Warehouse" | translate }}
            </label>
            <ng-select
              id="warehouseId"
              formControlName="warehouseId"
              bindLabel="name"
              bindValue="id"
              [items]="warehouses"
            ></ng-select>
          </div>

          <div class="form-group col-xl-2 col-md-6 col-sm-12">
            <label class="form-label">{{ "COMMON.DocNo" | translate }}</label>
            <input class="form-control"
             formControlName="docNO"
             type="text" />
          </div>
          <div class="form-group col-xl-2 col-md-6 col-sm-12">
            <button class="btn btn-sm btn-active-light-primary" title="بحث">
              <i class="fa fa-search"> </i>
            </button>
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label class="mx-6">
              <input
                type="radio"
                formControlName="optionControl"
                value="warehouse"
              />
              {{ "COMMON.Warehouse" | translate }}
            </label>
            <label class="mx-6">
              <input
                type="radio"
                formControlName="optionControl"
                value="customer"
              />
              {{ "COMMON.Customer" | translate }}
            </label>
            <label class="mx-6">
              <input
                type="radio"
                formControlName="optionControl"
                value="supplier"
              />
              {{ "COMMON.Supplier" | translate }}
            </label>

            <label class="mx-6">
              <input
                type="radio"
                formControlName="optionControl"
                value="employee"
              />
              {{ "COMMON.Employee" | translate }}
            </label>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="source" class="form-label">
              {{ "COMMON.Source" | translate }}
            </label>
            <ng-select
              id="source"
              formControlName="source"
              [bindLabel]="sourceBindLabel"
              bindValue="id"
              [items]="sources"
            ></ng-select>
          </div>
        </div>

        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab1'"
                (click)="setActiveTab('tab1')"
              >
                {{ "COMMON.Product" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab2'"
                (click)="setActiveTab('tab2')"
              >
                {{ "COMMON.DataImport" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab3'"
                (click)="setActiveTab('tab3')"
              >
                {{ "COMMON.OtherInfo" | translate }}
              </button>
            </li>
          </ul>

          <div class="tab-content" id="myTabContent">
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab1'"
              [class.active]="activeTab === 'tab1'"
              *ngIf="activeTab === 'tab1'"
            >
              <!--grid controle-->
              <app-products-grid-controle
                [data]="data"
                (onDataUpdate)="onDataUpdate($event)"
              ></app-products-grid-controle>


              <div class="row">
                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.Total" | translate }}</div>
                    <input type="number" [value]="total" class="form-control no-shadow-input rows-field" readonly/>
                </div>

                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.NumbeRofRows" | translate }}</div>
                    <input type="text" [value]="newdata.get('NumbeRofRows')?.value" class="form-control no-shadow-input rows-field" readonly/>
                </div>
        </div>
            </div>
          </div>

          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <br />
            </div>
            <div class="row">
              <div class="form-group col-xl-8 col-md-6 col-sm-12">
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="purchaseInvoice"
                    name="optionGroup2"
                  />
                  {{ "COMMON.PurchaseInvoice" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="purchaseTaxInvoice"
                    name="optionGroup2"
                  />
                  {{ "COMMON.PurchaseTaxInvoice" | translate }}
                </label>

                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="order"
                    name="optionGroup2"
                  />
                  {{ "COMMON.Order" | translate }}
                </label>
              </div>
            </div>
            <div class="row">
              <br />
            </div>
            <div class="row">
              <div class="form-group col-xl-8 col-md-6 col-sm-12">
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="salesInvoice"
                    name="optionGroup2"
                  />
                  {{ "COMMON.SalesInvoice" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="manufacturingOrder"
                    name="optionGroup2"
                  />
                  {{ "COMMON.SalesTaxInvoice" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="salesTaxInvoice"
                    name="optionGroup2"
                  />
                  {{ "COMMON.ManufacturingOrder" | translate }}
                </label>
              </div>
            </div>
            <div class="row">
              <br />
            </div>
            <div class="row">
              <div class="form-group col-xl-8 col-md-6 col-sm-12">
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="deliveryOrder"
                    name="optionGroup2"
                  />
                  {{ "COMMON.Receipts" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="optionControl2"
                    value="fromPurchaseTaxInvoice"
                    name="optionGroup2"
                  />
                  {{ "COMMON.DeliveryRequest" | translate }}
                </label>
              </div>
              <div class="row">
                <div class="form-group col-xl-8 col-md-12 col-sm-12">
                  <label class="form-label">{{
                    "COMMON.Notes" | translate
                  }}</label>
                  <textarea
                    name="notes"
                    id="notes"
                    rows="2"
                    class="form-control"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >
            <div class="row">
              <br />
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">{{
                  "COMMON.PurchaseInvoiceNumber" | translate
                }}</label>
                <input type="text" class="form-control" />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="branch" class="form-label">
                  {{ "COMMON.Branch" | translate }}
                </label>
                <ng-select
                  id="branch"
                  formControlName="branchId"
                  bindLabel="name"
                  bindValue="id"
                  [items]="branches"
                ></ng-select>
              </div>
            </div>
            <div class="row">

              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="journal" class="form-label">
                    {{ "COMMON.JournalName" | translate }}
                </label>
                <ng-select
                        id="journal"
                        formControlName="journal"
                        bindLabel="name"
                        bindValue="id"
                        [items]="journals">
                        </ng-select>
            </div>

              <!-- <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <div class="label p-2" style="color: red">Scrap</div>

                <dx-check-box
                  [value]="scrap"
                  formControlName="scrap"
                  valueExpr="scrap"
                ></dx-check-box>
              </div> -->
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="financial_entity_TypeId" class="form-label">
                  {{ "COMMON.financial_entity_Type" | translate }}
                </label>
                <ng-select
                  id="financial_entity_TypeId"
                  formControlName="financial_entity_TypeId"
                  bindLabel="name"
                  bindValue="id"
                  [items]="financial_entity_Types"
                  [searchable]="true"
                  (change)="onFinaTypeSelect($event)"
                >
                </ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="financial_entity_Id" class="form-label">
                  {{ "COMMON.financial_entity" | translate }}
                </label>
                <ng-select
                  id="financial_entity_Id"
                  formControlName="financial_entity_Id"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="FinancialEntities"
                ></ng-select>
              </div>
              <div class="row">
                <div class="form-group col-xl-4 col-md-6 col-sm-12">
                  <label class="form-label">{{
                    "COMMON.Responsible" | translate
                  }}</label>
                  <input
                    class="form-control"
                    formControlName="responsible"
                    type="text"
                  />
                </div>
                <div class="form-group col-xl-4 col-md-6 col-sm-12">
                  <label class="form-label">{{
                    "COMMON.Statement" | translate
                  }}</label>
                  <input
                    class="form-control"
                    formControlName="statement"
                    type="text"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
