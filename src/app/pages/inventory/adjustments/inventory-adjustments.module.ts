import { NgModule } from '@angular/core';

import {AdditionadjustmentsComponent} from './additionadjustments/additionadjustments.component';
import {DeductionsAdjustmentsComponent} from './deductions-adjustments/deductions-adjustments.component';
import {InventoryAdjustmentsComponent} from './inventory-adjustments/inventory-adjustments.component';
import {NetconsumerComponent} from './netconsumer/netconsumer.component';
import {SetavregecostComponent} from './setavregecost/setavregecost.component';
import {InventoryAdjustmentsRoutingModule} from "./inventory-adjustments-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {CommonModule} from "@angular/common";
import { Inventory_adjustments_viewComponent } from './inventory-adjustments/inventory_adjustments_view/inventory_adjustments_view.component';
import { Deductions_adjustments_viewComponent } from './deductions-adjustments/deductions_adjustments_view/deductions_adjustments_view.component';
import { Additionadjustments_viewComponent } from './additionadjustments/additionadjustments_view/additionadjustments_view.component';
import { Netconsumer_viewComponent } from './netconsumer/netconsumer_view/netconsumer_view.component';
import { Setavregecost_viewComponent } from './setavregecost/setavregecost_view/setavregecost_view.component';
import { ProductsGridControleModule } from '../../general/products-grid-controle/ProductsGridControle.module';

@NgModule({
  imports: [CommonModule, InventoryAdjustmentsRoutingModule, SharedModule,ProductsGridControleModule],
  declarations: [
    InventoryAdjustmentsComponent,
    DeductionsAdjustmentsComponent,
    AdditionadjustmentsComponent,
    NetconsumerComponent,
    SetavregecostComponent,
    Inventory_adjustments_viewComponent,
    Deductions_adjustments_viewComponent,
    Additionadjustments_viewComponent,
    Netconsumer_viewComponent,
    Setavregecost_viewComponent
  ],
})
export class InventoryAdjustmentsModule {}
