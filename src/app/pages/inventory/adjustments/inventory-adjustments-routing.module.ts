import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AdditionadjustmentsComponent} from './additionadjustments/additionadjustments.component';
import {DeductionsAdjustmentsComponent} from './deductions-adjustments/deductions-adjustments.component';
import {InventoryAdjustmentsComponent} from './inventory-adjustments/inventory-adjustments.component';
import {NetconsumerComponent} from './netconsumer/netconsumer.component';
import {SetavregecostComponent} from './setavregecost/setavregecost.component';
import { Inventory_adjustments_viewComponent } from './inventory-adjustments/inventory_adjustments_view/inventory_adjustments_view.component';
import { Deductions_adjustments_viewComponent } from './deductions-adjustments/deductions_adjustments_view/deductions_adjustments_view.component';
import { Additionadjustments_viewComponent } from './additionadjustments/additionadjustments_view/additionadjustments_view.component';
import { Netconsumer_viewComponent } from './netconsumer/netconsumer_view/netconsumer_view.component';
import { Setavregecost_viewComponent } from './setavregecost/setavregecost_view/setavregecost_view.component';


export const routes: Routes = [
    {
        path: '',
        redirectTo: 'inventory_adjustments',
        pathMatch: 'full'
    },
    {
        path: 'inventory_adjustments',
        component: InventoryAdjustmentsComponent
    },
    {
        path: 'deductions_adjustments',
        component: DeductionsAdjustmentsComponent
    },

    {
        path: 'inventory_adjustments_view',
        component: Inventory_adjustments_viewComponent
    },
    {
        path: 'inventory_adjustments_view/:id',
        component: Inventory_adjustments_viewComponent
    },
    {
        path: 'deductions_adjustments_view',
        component: Deductions_adjustments_viewComponent
    },
    {
        path: 'deductions_adjustments_view/:id',
        component: Deductions_adjustments_viewComponent
    },
    {
        path: 'additionadjustments_view',
        component: Additionadjustments_viewComponent
    },
    {
        path: 'additionadjustments_view/:id',
        component: Additionadjustments_viewComponent
    },

    {
        path: 'netconsumer_view',
        component: Netconsumer_viewComponent
    },
    {
        path: 'netconsumer_view/:id',
        component: Netconsumer_viewComponent
    },

    {
        path: 'setavregecost_view',
        component: Setavregecost_viewComponent
    },
    {
        path: 'setavregecost_view/:id',
        component: Setavregecost_viewComponent
    },


    {
        path: 'additionadjustments',
        component: AdditionadjustmentsComponent
    },
    {
        path: 'netconsumer',
        component: NetconsumerComponent
    },
    {
        path: 'setavregecost',
        component: SetavregecostComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class InventoryAdjustmentsRoutingModule {
}
