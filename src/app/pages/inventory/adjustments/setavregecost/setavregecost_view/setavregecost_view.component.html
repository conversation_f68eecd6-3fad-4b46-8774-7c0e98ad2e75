<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.SetAvregeCost" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.NetConsumer'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="uploadExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ImportFromExcel" | translate }}
              </a>
            </div>
            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.InventoryAdjustments'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs mb-5">
        <h3>{{ "COMMON.UpdateWarehouse" | translate }}</h3>
        <div class="row ">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="month" class="form-label">
              {{ "COMMON.Month" | translate }}
            </label>
            <ng-select
              id="month"
              formControlName="month"
              bindLabel="month"
              bindValue="month"
              [items]="months"
            ></ng-select>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label for="warehouse" class="form-label">
              {{ "COMMON.Warehouse" | translate }}
            </label>
             <ng-select
              id="warehouse"
              formControlName="warehouse"
              bindLabel="name"
              bindValue="id"
              [items]="warehouses"
            ></ng-select>
          </div>
        </div>

        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.UpdateProduct" | translate }}
            </button>
            </li>

          </ul>

          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >




                    <div class="row">


                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label class="form-label">{{ "COMMON.From" | translate }}</label>
                        <input id="fromDate"
                        type="date"
                        formControlName="fromDate"
                        class="form-control"
                      />
                      </div>
                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label class="form-label">{{ "COMMON.To" | translate }}</label>
                        <input id="toDate"
                        type="date"
                        formControlName="toDate"
                         class="form-control"
                          />
                      </div>
                    </div>
             <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="warehouse" class="form-label">
                  {{ "COMMON.Warehouse" | translate }}
                </label>
                            <ng-select
                  id="warehouse"
                  formControlName="warehouse"
                  bindLabel="name"
                  bindValue="id"
                  [items]="warehouses"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
                <label for="movementName" class="form-label" > {{ "COMMON.MovementName" | translate }}  </label>
                <ng-select
                  id="movementName"
                  formControlName="movementName"
                  bindLabel="name"
                  bindValue="id"
                  [items]="movementNames"
                ></ng-select>
              </div>
            </div>
     <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="productId" class="form-label">
          {{ "COMMON.ProductLabel" | translate }}
        </label>
        <ng-select
          id="productId"
          formControlName="productId"
          bindLabel="productName"
          bindValue="productId"
          [items]="products"
        ></ng-select>
      </div>
      <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
        <label for="average"
         class="form-label">{{ "COMMON.Average" | translate }}</label>
        <input  class="form-control"
        formControlName="average"
        id="average"
        type="text"/>
     </div>
     </div>
     <div class="row">
      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2" >{{ "COMMON.AllWarehouses" | translate }}</div>

        <dx-check-box
          [value]="allWarehouses"
          formControlName="allWarehouses"
          valueExpr="allWarehouses"
        ></dx-check-box>
      </div>
     </div>

                </div>

            </div>

  <!-- tab2 -->
        <div
        class="tab-pane fade"
        [class.show]="activeTab === 'tab2'"
        [class.active]="activeTab === 'tab2'"
        *ngIf="activeTab === 'tab2'"
      >
      <div class="row">
        <br>
      </div>
      <div class="row">
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="product" class="form-label">
            {{ "COMMON.Product" | translate }}
          </label>
          <ng-select
            id="product"
            formControlName="productId"
            bindLabel="productName"
            bindValue="productId"
            [items]="products"
          ></ng-select>
        </div>

      </div>
      <div class="row">
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <div class="label p-2" >{{ "COMMON.GroupByItemProperties" | translate }}</div>

          <dx-check-box
            [value]="byItemProperties"
            formControlName="byItemProperties"
            valueExpr="byItemProperties"
          ></dx-check-box>
        </div>
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <div class="label p-2" >{{ "COMMON.WithBalances" | translate }}</div>

          <dx-check-box
            [value]="withBalances"
            formControlName="withBalances"
            valueExpr="withBalances"
          ></dx-check-box>
        </div>
      </div>
          </div>
          <!-- tab3 -->
          <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab3'"
          [class.active]="activeTab === 'tab3'"
          *ngIf="activeTab === 'tab3'"
        >

        </div>
      </div>

    </div>
  </div>
  </div>

</form>