import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { finalize } from 'rxjs';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { AdjustmentsService } from '../../Adjustments.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
@Component({
  selector: 'app-netconsumer_view',
  templateUrl: './netconsumer_view.component.html',
  styleUrls: ['./netconsumer_view.component.css'],
  standalone:false
})
export class Netconsumer_viewComponent implements OnInit {
  newdata: FormGroup;
   moduleName = 'Inventory.InventoryAdjustments';
   activeTab: string = 'tab1';
   [x: string]: any;
   subscriptions = new Subscription();
   Products: any[];
   ProductId: number = 0;
   AnalyticAccounts: any[];
   Units: any[];
   data: any[];
   customers: any[];
   warehouses: any[];
   isGridBoxOpened: boolean;
   editorOptions: any;
   gridBoxValue: number[] = [1];
   subscription = new Subscription();
   currentFilter: any;
   branches: any[];
   costCenter: any[];
   costGroup: any[];
   FinancialEntities: any[];
   total = 0;
   maingroups: any[]= [];
   Supgroups:any[];
   // optionControl=null;
    menuOpen = false;
     toggleMenu() {
       this.menuOpen = !this.menuOpen;
     }
     actionsList: string[] = [
       'Export',
       'Send via SMS',
       'Send Via Email',
       'Send Via Whatsapp',
     ];
     modalConfig: ModalConfig = {
       modalTitle: 'Send Sms',
       modalSize: 'lg',
       hideCloseButton(): boolean {
         return true;
       },
       dismissButtonLabel: 'Cancel',
     };
  
     smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
     emailModalConfig: ModalConfig = {
       ...this.modalConfig,
       modalTitle: 'Send Email',
     };
     whatsappModalConfig: ModalConfig = {
       ...this.modalConfig,
       modalTitle: 'Send Whatsapp',
     };
     @ViewChild('smsModal') private smsModal: ModalComponent;
     @ViewChild('emailModal') private emailModal: ModalComponent;
     @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
     setActiveTab(tab: string): void {
       this.activeTab=tab;
   }
   constructor(
     private service: AdjustmentsService,
     private cdk: ChangeDetectorRef,
     private fb: FormBuilder,
     private route: ActivatedRoute,
     private router: Router
   ) {

     const currentYear = new Date().getFullYear();
     this.newdata = this.fb.group({
      fromDate: new FormControl(formatDate(new Date(currentYear, 0, 1), "yyyy-MM-dd", "en")),
      toDate: new FormControl(formatDate(new Date(currentYear, 11, 31), "yyyy-MM-dd", "en")),
       warehouse: [''],
       branchId: [''],
       year: [currentYear],
       movementType:[null, Validators.required],
       statement:[null, Validators.required],
       responsible: [null, Validators.required],
       ProductId: [null, Validators.required],
       byItemProperties: [false],
       withBalances: [false],
     });
 
     //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
 
     this.subscription.add(
       service.getWarehouses().subscribe((r) => {
         if (r.success) {
           this.warehouses = r.data;
 
           this.cdk.detectChanges();
         }
       })
     );
 


     this.subscription.add(
      service.getProductList().subscribe((r) => {
        if (r.success) {
          this.products = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(service.getProductCategories().subscribe(r => {
      if (r.success) {
        this.maingroups = r.data;
        this.cdk.detectChanges();
      }
    }));
   }

 
   ngOnInit() {
     const id = this.route.snapshot.params.id;
     if (id) {
       this.subscriptions.add(
         this.service.details(id).subscribe((r) => {
           if (r.success) {
             this.fillForm(r.data);
             this.roles = r.roles;
             this.cdk.detectChanges();
           }
         })
       );
     }
   }
   onFinaTypeSelect(selectedItem: any) {
     this.FinancialEntities = [];
 
     if (!selectedItem) {
       this.cdk.detectChanges();
       return;
     }
     // this.financial_entity_TypeId= selectedItem.id;
 
     this.subscription.add(
       this.service
         .getFinancialEntities(this.financial_entity_TypeId)
         .subscribe((r) => {
           if (r.success) {
             this.FinancialEntities = r.data;
             this.cdk.detectChanges();
           }
         })
     );
   }
 
   onItemClick(e: any) {}
   onAccountSelect(selectedItem: any) {
    if(!selectedItem) {
      return;
    }
 
    this.Supgroups = this.maingroups
    .filter(r => r.groupid === selectedItem.groupid)
    .flatMap(r => r.productSubCategory);



    this.cdk.detectChanges();

    console.log(this.Supgroups);
    // this.subscription.add(this.service.getProductSubCategoriesById( this.groupId).subscribe(r => {
    //   if (r.success) {
    //     this.Supgroups = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
    
  }
   ngOnDestroy(): void {
     this.subscription.unsubscribe();
   }
 
   onExporting(e: any) {
     console.log(e);
     const workbook = new Workbook();
     const worksheet = workbook.addWorksheet('Employees');
     if (e.format == 'excel') {
       exportDataGrid({
         component: e.component,
         worksheet,
         autoFilterEnabled: true,
       }).then(() => {
         workbook.xlsx.writeBuffer().then((buffer: any) => {
           saveAs(
             new Blob([buffer], { type: 'application/octet-stream' }),
             'taxinvoice.xlsx'
           );
         });
       });
     } else if (e.format == 'pdf') {
       const doc = new jsPDF();
       pdfGrid({
         jsPDFDocument: doc,
         component: e.component,
         indent: 5,
       }).then(() => {
         doc.save('taxinvoice.pdf');
       });
     }
     e.cancel = true;
   }
 
   handleSubmit($event: SubmitEvent) {
     console.log(this.customerForm.value);
     console.log($event);
   }
     save() {
       const id = this.route.snapshot.params.id;
       if (id) {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.service
               .update(id, form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cdk.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                  this.router.navigate(['/inventory/adjustments/netconsumer']);
                }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       } else {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.service
               .create(form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cdk.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                  this.router.navigate(['/inventory/adjustments/netconsumer']);
                 }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       }
     }
 
     discard() {
       this.newdata.reset();
     }
   
   onDataUpdate(data: any) {
     this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
   
   discountChanged(event: any) {
     const value = +event.target.value;
     if(value) {
       this.discountPerc = value * 100/ this.total;
       this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
     }
     this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   discountPercChanged(event: any) {
     const value = +event.target.value;
     if(value) {
       this.discount = this.total * value / 100;
       this.discount = parseFloat(this.discount.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onTaxChange(event: any) {
     const value = +event.target.value;
     if(value) {
       this.taxPerc = value * 100 / this.total;
       this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onVatChange(event: any) {
     const value = +event.target.value;
     this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onTaxPercChange(event: any) {
     const value = +event.target.value;
     if(value) {
       this.tax = this.total * value / 100;
       this.tax = parseFloat(this.tax.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onShapingChange(event: any) {
     const value = +event.target.value;
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
     this.net = this.net.toFixed(2);
   }
   exportToExcel() {
     this.subscription.add(
       this.service.exportExcel().subscribe((e) => {
         if (e) {
           const href = URL.createObjectURL(e);
           const link = document.createElement('a');
           link.setAttribute('download', 'payables.xlsx');
           link.href = href;
           link.click();
           URL.revokeObjectURL(href);
         }
       })
     );
   }
 
   openSmsModal() {
     this.smsModal.open();
   }
   openWhatsappModal() {
     this.whatsappModal.open();
   }
   openEmailModal() {
     this.emailModal.open();
   }
   uploadExcel() {
    var input = document.createElement('input');
    input.type = "file";
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(this.service.importExcel(fd)
            .subscribe(e => {
              if (e) {
                this.currentPage = 1;
              }
            }));
        }
      }
    }
  }


}
