<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.NetConsumer" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.NetConsumer'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="uploadExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ImportFromExcel" | translate }}
              </a>
            </div>
            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.InventoryAdjustments'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs mb-5">
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label class="form-label">{{
              "COMMON.FromDate" | translate
            }}</label>
            <input
              id="fromDate"
              type="date"
              formControlName="fromDate"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
            <input
              id="toDate"
              type="date"
              formControlName="toDate"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-8 col-md-6 col-sm-12 ">
            <label for="warehouse" class="form-label">
              {{ "COMMON.Warehouse" | translate }}
            </label>
            <ng-select
              id="warehouse"
              formControlName="warehouse"
              bindLabel="name"
              bindValue="id"
              [items]="warehouses"
            ></ng-select>
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="Mingroupid" class="form-label">
              {{ "COMMON.ProductCategories" | translate }}
            </label>
            <ng-select
              id="Mingroupid"
              formControlName="Mingroupid"
              bindLabel="groupname"
              bindValue="groupid"
              [items]="maingroups"
              (change)="onAccountSelect($event)"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="Supgroupid" class="form-label">
              {{ "COMMON.ProductSubCategories" | translate }}
            </label>
            <ng-select
              id="Supgroupid"
              formControlName="Supgroupid"
              bindLabel="en_Sub_Name"
              bindValue="subid"
              [items]="Supgroups"
            ></ng-select>
          </div>
        </div>

        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab1'"
                (click)="setActiveTab('tab1')"
              >
                {{ "COMMON.Product" | translate }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                [class.active]="activeTab === 'tab2'"
                (click)="setActiveTab('tab2')"
              >
                {{ "COMMON.OtherInfo" | translate }}
              </button>
            </li>

          </ul>

          <div class="tab-content" id="myTabContent">
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab1'"
              [class.active]="activeTab === 'tab1'"
              *ngIf="activeTab === 'tab1'"
            >
              <!--grid controle-->
              <app-products-grid-controle
                (onDataUpdate)="onDataUpdate($event)"
              ></app-products-grid-controle>


              <div class="row">
                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.Total" | translate }}</div>
                    <input type="number" [value]="total" class="form-control no-shadow-input rows-field" readonly/>
                </div>

                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.NumbeRofRows" | translate }}</div>
                    <input type="text" [value]="newdata.get('NumbeRofRows')?.value" class="form-control no-shadow-input rows-field" readonly/>
                 </div>
               </div>
            </div>
          </div>

          <!-- tab2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <br />
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="product" class="form-label">
                  {{ "COMMON.Product" | translate }}
                </label>
                <ng-select
                  id="product"
                  formControlName="productId"
                  bindLabel="productName"
                  bindValue="productId"
                  [items]="products"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <div class="label p-2">
                  {{ "COMMON.GroupByItemProperties" | translate }}
                </div>

                <dx-check-box
                  [value]="byItemProperties"
                  formControlName="byItemProperties"
                  valueExpr="byItemProperties"
                ></dx-check-box>
              </div>
              <div class="form-group col-xl-2 col-md-4 col-sm-12">
                <div class="label p-2">
                  {{ "COMMON.WithBalances" | translate }}
                </div>

                <dx-check-box
                  [value]="withBalances"
                  formControlName="withBalances"
                  valueExpr="withBalances"
                ></dx-check-box>
              </div>
            </div>
          </div>
          <!-- tab3 -->

        </div>
      </div>
    </div>
  </div>
</form>
