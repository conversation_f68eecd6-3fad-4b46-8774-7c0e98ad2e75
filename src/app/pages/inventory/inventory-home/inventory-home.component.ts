import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { Subscription } from 'rxjs';
import { InventoryService } from '../inventory.service';

@Component({
    selector: 'app-inventory-home',
    templateUrl: './inventory-home.component.html',
    styleUrls: ['./inventory-home.component.scss'],
    standalone: false
})

export class InventoryHomeComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription = new Subscription();

  // Dashboard data
  totalProducts: number = 0;
  totalWarehouses: number = 0;
  lowStockItems: number = 0;
  inventoryValue: number = 0;
  recentTransactions: any[] = [];

  constructor(
    private inventoryService: InventoryService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Load all dashboard data
   */
  loadDashboardData(): void {
    this.loadTotalProducts();
    this.loadTotalWarehouses();
    this.loadLowStockItems();
    this.loadInventoryValue();
    this.loadRecentTransactions();
  }

  /**
   * Load total products count
   */
  loadTotalProducts(): void {
    this.subscriptions.add(
      this.inventoryService.getProductsCount().subscribe(
        (response) => {
          if (response.success) {
            this.totalProducts = response.data;
            this.updateDOMElement('totalProducts', this.totalProducts.toString());
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading total products:', error);
        }
      )
    );
  }

  /**
   * Load total warehouses count
   */
  loadTotalWarehouses(): void {
    this.subscriptions.add(
      this.inventoryService.getWarehouses().subscribe(
        (response) => {
          if (response.success) {
            this.totalWarehouses = response.data.length;
            this.updateDOMElement('totalWarehouses', this.totalWarehouses.toString());
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading warehouses:', error);
        }
      )
    );
  }

  /**
   * Load low stock items count
   */
  loadLowStockItems(): void {
    this.subscriptions.add(
      this.inventoryService.getLowStockItems().subscribe(
        (response) => {
          if (response.success) {
            this.lowStockItems = response.data.length;
            this.updateDOMElement('lowStockItems', this.lowStockItems.toString());
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading low stock items:', error);
        }
      )
    );
  }

  /**
   * Load total inventory value
   */
  loadInventoryValue(): void {
    this.subscriptions.add(
      this.inventoryService.getInventoryValue().subscribe(
        (response) => {
          if (response.success) {
            this.inventoryValue = response.data;
            this.updateDOMElement('inventoryValue', '$' + this.formatCurrency(this.inventoryValue));
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading inventory value:', error);
        }
      )
    );
  }

  /**
   * Load recent inventory transactions
   */
  loadRecentTransactions(): void {
    this.subscriptions.add(
      this.inventoryService.getRecentTransactions().subscribe(
        (response) => {
          if (response.success) {
            this.recentTransactions = response.data;
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading recent transactions:', error);
        }
      )
    );
  }

  /**
   * Update DOM element with value
   */
  private updateDOMElement(elementId: string, value: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * Format currency value
   */
  private formatCurrency(value: number): string {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }
}
