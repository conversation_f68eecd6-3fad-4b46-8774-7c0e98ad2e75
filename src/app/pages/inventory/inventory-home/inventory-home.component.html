
<!-- Inventory Dashboard -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Quick Stats -->
  <div class="col-xl-12">
    <div class="card card-flush mb-5">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.InventoryOverview" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.InventoryStats" | translate }}</span>
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Total Products Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalProducts">0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TotalProducts" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-primary">
            <i class="fa fa-boxes text-primary fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Warehouses Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalWarehouses">0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TotalWarehouses" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-success">
            <i class="fa fa-warehouse text-success fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Low Stock Items Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="lowStockItems">0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.LowStockItems" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-warning">
            <i class="fa fa-exclamation-triangle text-warning fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Inventory Value Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="inventoryValue">$0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.InventoryValue" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-info">
            <i class="fa fa-dollar-sign text-info fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Charts Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Inventory Movement Chart -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.InventoryMovement" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.Last30Days" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <app-charts-widget1></app-charts-widget1>
      </div>
    </div>
  </div>

  <!-- Stock Distribution Chart -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.StockDistribution" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.ByCategory" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <app-charts-widget3></app-charts-widget3>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activities and Quick Access -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Recent Activities -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.RecentActivities" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.LatestInventoryTransactions" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget4 class="card-xl-stretch mb-xl-10"></app-lists-widget4>
      </div>
    </div>
  </div>

  <!-- Quick Access -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.QuickAccess" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.CommonInventoryOperations" | translate }}</span>
        </h3>
      </div>
      <div class="card-body pt-5">
        <div class="row g-3">
          <!-- Quick Access Buttons -->
          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/operations/receipts_view" class="card bg-light-primary hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-arrow-down text-primary fs-3x my-2"></i>
                <div class="text-primary fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewReceipt" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/operations/delivery_view" class="card bg-light-success hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-arrow-up text-success fs-3x my-2"></i>
                <div class="text-success fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewDelivery" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/operations/transfers_view" class="card bg-light-info hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-exchange-alt text-info fs-3x my-2"></i>
                <div class="text-info fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewTransfer" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/reports/valuation_report" class="card bg-light-warning hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-chart-bar text-warning fs-3x my-2"></i>
                <div class="text-warning fw-bold fs-5 mb-2 mt-5">{{ "COMMON.ValuationReport" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/configuration/products" class="card bg-light-danger hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-box text-danger fs-3x my-2"></i>
                <div class="text-danger fw-bold fs-5 mb-2 mt-5">{{ "COMMON.ManageProducts" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/inventory/configuration/warehouses" class="card bg-light-primary hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-warehouse text-primary fs-3x my-2"></i>
                <div class="text-primary fw-bold fs-5 mb-2 mt-5">{{ "COMMON.ManageWarehouses" | translate }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Low Stock Items Table -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <div class="col-xl-12">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.LowStockItems" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.ItemsNeedingRestock" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-tables-widget5 class="card-xl-stretch mb-xl-10"></app-tables-widget5>
      </div>
    </div>
  </div>
</div>