import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { accountingHomeComponent } from './home/<USER>';

const routes: Routes = [
  {
    path: '',
    component: accountingHomeComponent,
  },
  {
    path: 'accounting',
    loadChildren: () =>
      import('./accounting/accounting-accounting.module').then(
        (m) => m.AccountingAccountingModule
      ),
  },
  {
    path: 'analysis',
    loadChildren: () =>
      import('./analysis/accounting-analysis.module').then(
        (m) => m.AccountingAnalysisModule
      ),
  },
  {
    path: 'analytics',
    loadChildren: () =>
      import('./analytics/accounting-analytics.module').then(
        (m) => m.AccountingAnalyticsModule
      ),
  },
  {
    path: 'assets',
    loadChildren: () =>
      import('./assets/accounting-assets.module').then(
        (m) => m.AccountingAssetsModule
      ),
  },
  {
    path: 'configuration',
    loadChildren: () =>
      import('./configuration/accounting-configuration.module').then(
        (m) => m.AccountingConfigurationModule
      ),
  },
  {
    path: 'documentary',
    loadChildren: () =>
      import('./documentary/accounting-documentary.module').then(
        (m) => m.AccountingDocumentaryModule
      ),
  },
  {
    path: 'final_accounts',
    loadChildren: () =>
      import('./final-accounts/accounting-final-accounts.module').then(
        (m) => m.AccountingFinalAccountsModule
      ),
  },
  {
    path: 'letters',
    loadChildren: () =>
      import('./letters/accounting-letters.module').then(
        (m) => m.AccountingLettersModule
      ),
  },
  {
    path: 'loans',
    loadChildren: () =>
      import('./loans/accounting-loans.module').then(
        (m) => m.AccountingLoansModule
      ),
  },
  {
    path: 'opening',
    loadChildren: () =>
      import('./opening/accounting-opening.module').then(
        (m) => m.AccountingOpeningModule
      ),
  },
  {
    path: 'reports',
    loadChildren: () =>
      import('./reports/accounting-reports.module').then(
        (m) => m.AccountingReportsModule
      ),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountingRoutingModule {}
