import { AccountingService } from '../../accounting.service';
import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
/*import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';*/

import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { lastValueFrom, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import ArrayStore from 'devextreme/data/array_store';
import dxDataGrid from 'devextreme/ui/data_grid';
import { DxDataGridComponent } from 'devextreme-angular';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-opining-and-closing-journals',
  templateUrl: './opining-and-closing-journals.component.html',
  styleUrls: ['./opining-and-closing-journals.component.scss'],
  standalone: false,
})
export class OpiningAndClosingJournalsComponent implements OnInit, OnDestroy {
  moduleName = 'Accounting.OpiningandClosingJournals';
  data: any[] = [];
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [
    { name: 'تمت', value: true },
    { name: 'لم تتم', value: false },
  ];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
viewListForm: FormGroup;
fromDateValue: string;
toDateValue: string;
editorOptions: any;
gridBoxValue: number[] = [];
searchExpr: any;
journalId: any;
  dataa: any[] = [];
  branches: [] = [];
  costCenter: [] = [];
  Cashs: [] = [];
  currency: [] = [];
  financialYear: any[]= [];
  CashBoxId: number = 0;
  currencyId: any = 1;
  employees: any[];
  employeaccountclassification: any[];
  employeeId: number = 0;
  employeaccountclassificationId: number = 0;
  year: any ;
  aggregationbyfinancialentity: boolean = false;
  AggregationbySubAccounts: boolean = false;
  aggregationbycostcenters: boolean = false;
  closingentry: boolean = false;
  openingentry: boolean = false;
selectedItemKeys: any = [];
dataSource: ArrayStore;
isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
 toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};

smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
    private myService: AccountingService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef
  ) {

    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required],
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);


   
 

    this.subscriptions.add(
      this.myService.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cd.detectChanges();
        }
      })
    );
    this.subscriptions.add(this.myService.getfinancialYear().subscribe(r => {
      if (r.success) {
        this.financialYear = r.data;
        this.cd.detectChanges();
      }
    }));
    
    this.subscriptions.add(
      myService.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cd.detectChanges();
        }
      })
    );

    this.subscriptions.add(
      this.myService.getCashBox().subscribe((r) => {
        if (r.success) {
          this.Cashs = r.data;
          this.cd.detectChanges();
        }
      })
    );

    this.subscriptions.add(this.myService.getCurrency().subscribe(r => {
      if (r.success) {
        this.currency = r.data;
        this.cd.detectChanges();
      }
    }));

    this.subscriptions.add(
      this.myService.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cd.detectChanges();
        }
      })
    );

        this.editorOptions = { placeholder: 'Search name or code' };
        this.searchExpr = ['name', 'code'];
       
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    const currentYear = new Date().getFullYear();
    const fromDateValue = `${currentYear}-01-01`; // أول يوم في السنة
    const toDateValue = `${currentYear}-12-31`; // آخر يوم في السنة

    this.viewListForm = this.fb.group({
      fromDate: [fromDateValue],
      toDate: [toDateValue],
    }); 
    
  }



@ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;
  
// remove() {
//       Swal.fire({
//         title: 'هل حقاً تريد الحذف',
//         showConfirmButton: true,
//         showCancelButton: true,
//         confirmButtonText: 'نعم',
//         cancelButtonText: 'إلغاء',
//         customClass: {
//           confirmButton: 'btn btn-danger',
//           cancelButton: 'btn btn-light'
//         },
//         icon: 'warning'
//       }).then(r => {
//         if (r.isConfirmed) {
//           this.selectedItemKeys.forEach((key: any) => {
//             this.subscriptions.add(this.myService.delete(key)
//               .subscribe(r => {
//                 if (r.success) {
//                   this.data.remove(key);
//                   this.dataGrid.instance.refresh();
//                   this.cd.detectChanges();
//                 }
//               }));
//           });
//         }
//       });
//     }

//       deleteRecords() {
//     this.remove();
//   }

  onSaving(e: any) {
    e.cancel = true;
    if (e.changes.length) {
      e.changes.forEach((c: any) => {
        if (c.type == 'update') {
          let selected = this.dataa.find(d => d.id == c.key);
          if (selected) {
            for (const key in selected) {
              if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                c.data[key] = selected[key];
              }
            }
          }
        }
      });
      e.promise = this.processBatchRequest(e.changes, e.component);
    }
  }

  loadData(r: any) {
    this.data = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }

  // discard() {
  //     }
      
    
save() { }

  async processBatchRequest(changes: Array<{}>, component: dxDataGrid): Promise<any> {
    await lastValueFrom(this.myService.batch(changes));
    component.cancelEditData();
    await component.refresh(true);
    this.cd.detectChanges();
    this.subscriptions.add(this.myService.list(null, this.currentPage).subscribe(r => {
      if (r.success) {
        this.loadData(r);
      }
    }));
  }


  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('CustomersOpeningBalance');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'CustomersOpeningBalance.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('CustomersOpeningBalance.pdf');
      });
    }
    e.cancel = true;
  } 
    exportToExcel() {
            this.subscriptions.add(this.myService.exportExcel()
              .subscribe(e => {
                if (e) {
                  const href = URL.createObjectURL(e);
                  const link = document.createElement('a');
                  link.setAttribute('download', 'CustomersOpeningBalance.xlsx');
                  link.href = href;
                  link.click();
                  URL.revokeObjectURL(href);
                }
              }));
          }
        
        
          uploadExcel() {
            var input = document.createElement('input');
            input.type = "file";
            input.click();
            input.onchange = (e) => {
              if (input.files) {
                if (input.files[0]) {
                  const fd = new FormData();
                  fd.append('file', input.files[0]);
                  this.subscriptions.add(this.myService.importExcel(fd)
                    .subscribe(e => {
                      if (e) {
                        this.currentPage = 1;
                      }
                    }));
                }
              }
            }
          }
         set currentPage(value: any) {
            this._currentPage = value;
            this.subscriptions.add(this.myService.list('', value).subscribe(r => {
              if (r.success) {
              //  this.dataSource = r.data;
                this.dataSource = new ArrayStore({
                  key: 'id',
                  data: r.data,
                });
                this.cd.detectChanges();
                MenuComponent.reinitialization();
              }
            }));
          }
        
          get currentPage() {
            return this._currentPage;
          }

          
  filter() {
    const FromDate = this.viewListForm.value.fromDate;
    const ToDate = this.viewListForm.value.toDate;
    const CustomerId = this.viewListForm.value.customer;
    this.subscriptions.add(this.myService.list({ ToDate, FromDate, CustomerId }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cd.detectChanges();
      }
    }));
  }


  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
