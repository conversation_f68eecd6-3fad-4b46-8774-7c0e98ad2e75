<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.OpiningAndClosingJournals" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
          <!-- <button
          type="submit"
          (click)="discard()"
          class="btn btn-sm btn-active-light-primary"
        >
          {{ "COMMON.Cancel" | translate }}
          <i class="fa fa-close"></i>
        </button> -->
        <button
        type="submit"
        (click)="save()"
        class="btn btn-sm btn-active-light-primary"
      >
        {{ "COMMON.SaveChanges" | translate }}
        <i class="fa fa-save"></i>
      </button>

      <!-- <button
      type="submit"
      (click)="deleteRecords()"
      class="btn btn-sm btn-active-light-primary"
    >
      {{ "COMMON.DELETE" | translate }}
      <i class="fa fa-save"></i>
    </button>  -->

        </div>
  
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
  
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
  
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >{{ "COMMON.ExportToExcel"| translate }} </a>
          </div>
  
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportToExcel" | translate }}
            </a>
          </div>
  
          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.OpiningandClosingJournals'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
  
  
        </div>
      </div>
    </div>
  </div>

  <div class="card-body border-top">
 
  <div class="main-inputs">


        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label class="form-label">{{ "COMMON.From" | translate }}</label>
            <input
              id="fromDate"
              type="date"
              formControlName="fromDate"
              class="form-control"
              style="background-color: #f5f8fa"
            />
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label class="form-label">{{ "COMMON.To" | translate }}</label>
            <input
              id="toDate"
              type="date"
              formControlName="toDate"
              class="form-control"
              style="background-color: #f5f8fa"
            />
          </div>





        </div>


        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="financialYear" class="form-label">
              {{ "COMMON.Year" | translate }}
            </label>
            <ng-select
              id="financialYear"
              [(ngModel)]="year"
              bindLabel="yearName"
              bindValue="yearName"
              [items]="financialYear"
            ></ng-select>
          </div>
  
          <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="currencyid" class="form-label">
                  {{ "COMMON.Currency" | translate }}
                </label>
                <ng-select
                  id="currencyid"
                  [(ngModel)]="currencyId"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="currency"
                ></ng-select>
          </div>


        </div>

        <div class="row">
          
          <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-3">
            <label for="branch" class="form-label">
              {{ "COMMON.Branch" | translate }}
            </label>
            <ng-select
                    id="branch"
                    formControlName="branch"
                    bindLabel="name"
                    bindValue="id"
                    [items]="branches"></ng-select>
        </div>


          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="journalId" class="form-label">
              {{ "COMMON.JournalNO" | translate }}
            </label>
            <input
              id="journalId"
              type="text"
              [(ngModel)]="journalId"
              class="form-control"
              style="background-color: #f5f8fa"
            />
          </div>

        </div>

        <div class="row">
          <div class="form-group col-xl-2 col-md-4 col-sm-12">
            <div class="label p-2">
              {{ "COMMON.AggregationbyFinancialEntity" | translate }}
            </div>
    
            <dx-check-box
              [value]="aggregationbyfinancialentity"
              valueExpr="aggregationbyfinancialentity"
            ></dx-check-box>
          </div>

          <div class="form-group col-xl-2 col-md-4 col-sm-12">
            <div class="label p-2">{{ "COMMON.AggregationbySubAccounts" | translate }}</div>
    
            <dx-check-box
              [value]="AggregationbySubAccounts"
              valueExpr="AggregationbySubAccounts"
            ></dx-check-box>
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <div class="label p-2">{{ "COMMON.AggregationbyCostCenters" | translate }}</div>
    
            <dx-check-box
              [value]="aggregationbycostcenters"
              valueExpr="aggregationbycostcenters"
            ></dx-check-box>
          </div>


          <div class="form-group col-xl-2 col-md-4 col-sm-12">
            <div class="label p-2">{{ "COMMON.OpeningEntry" | translate }}</div>
    
            <dx-check-box
              [value]="openingentry"
              valueExpr="openingentry"
            ></dx-check-box>
          </div>


          <div class="form-group col-xl-2 col-md-4 col-sm-12">
            <div class="label p-2">{{ "COMMON.ClosingEntry" | translate }}</div>
    
            <dx-check-box
              [value]="closingentry"
              valueExpr="closingentry"
            ></dx-check-box>
          </div>




        </div>

        

      </div> 
  </div>
</form>


<dx-data-grid id="gridcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="store_id"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">
  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="store_name" caption="id"></dxi-column>
  <dxi-column dataField="store_address" caption="Bank Name"></dxi-column>
  <dxi-column dataField="store_address" caption="Address"></dxi-column>

  <!--<dxi-column dataField="emp_name" caption="emp name">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>-->

  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="store_name" summaryType="count"> </dxi-total-item>


  </dxo-summary>


</dx-data-grid>

