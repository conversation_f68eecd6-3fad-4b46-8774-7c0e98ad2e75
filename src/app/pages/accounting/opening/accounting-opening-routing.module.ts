import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BanksOpeningBalanceComponent } from './banks-opening-balance/banks-opening-balance.component';
import { CashBoxesOpeningBalanceComponent } from './cash-boxes-opening-balance/cash-boxes-opening-balance.component';
import { CustomersOpeningBalanceComponent } from './customers-opening-balance/customers-opening-balance.component';
import { EmployeesOpeningBalanceComponent } from './employees-opening-balance/employees-opening-balance.component';
import { OpiningAndClosingJournalsComponent } from './opining-and-closing-journals/opining-and-closing-journals.component';
import { SupplierOpeningBalanceComponent } from './supplier-opening-balance/supplier-opening-balance.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'cash_boxes_opening_balance',
    pathMatch: 'full',
  },
  {
    path: 'cash_boxes_opening_balance',
    component: CashBoxesOpeningBalanceComponent,
  },
  {
    path: 'employees_opening_balance',
    component: EmployeesOpeningBalanceComponent,
  },
  {
    path: 'banks_opening_balance',
    component: BanksOpeningBalanceComponent,
  },
  {
    path: 'customers_opening_balance',
    component: CustomersOpeningBalanceComponent,
  },
  {
    path: 'supplier_opening_balance',
    component: SupplierOpeningBalanceComponent,
  },
  {
    path: 'opining_and_closing_journals',
    component: OpiningAndClosingJournalsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountingOpeningRoutingModule {}
