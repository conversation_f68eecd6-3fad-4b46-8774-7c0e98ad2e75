/* Cash Boxes Opening Balance Component Styles */

/* تحسين تخطيط الليبل والحقول - منع التداخل */
.form-label {
  display: inline-block;
  width: 120px;
  min-width: 120px;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #3f4254;
  font-size: 0.95rem;
  line-height: 1.4;
  vertical-align: top;
  white-space: nowrap;
  overflow: visible;
  flex-shrink: 0;
}

/* تحسين المسافات بشكل طبيعي */
.row {
  margin-bottom: 0.75rem;
}

.row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

/* تحسين بسيط للحقول */
.form-control {
  flex-grow: 1;
  border: none !important;
  border-radius: unset !important;
  border-bottom: 1px solid var(--kt-input-border-color) !important;

  &:hover,
  &:focus {
    border-bottom: 2px solid var(--kt-input-border-color) !important;
  }
}

/* تحسين ng-select بشكل بسيط */
::ng-deep ng-select {
  flex-grow: 1;

  .ng-select-container {
    min-height: 42px !important;
    border: none !important;
    border-radius: unset !important;
    border-bottom: 1px solid var(--kt-input-border-color) !important;

    &:hover,
    &:focus-within {
      border-bottom: 2px solid var(--kt-input-border-color) !important;
    }
  }
}

/* تحسين الـ Card Header */
.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem;
  border-radius: 0.375rem 0.375rem 0 0;

  .card-title {
    h3 {
      color: #2c3e50;
      font-weight: 700;
      font-size: 1.5rem;
      margin: 0;
    }
  }

  .btn-group {
    display: flex;
    gap: 0.5rem;

    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 0.375rem;
      transition: all 0.3s ease;

      &.btn-active-light-primary {
        background-color: rgba(54, 153, 255, 0.1);
        color: #3699ff;
        border: 1px solid rgba(54, 153, 255, 0.2);

        &:hover {
          background-color: rgba(54, 153, 255, 0.2);
          transform: translateY(-1px);
        }

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }
}

/* تحسين منطقة الإدخالات الرئيسية */
.main-inputs {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

/* تحسين الـ Checkboxes */
::ng-deep dx-check-box {
  .dx-checkbox {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 2px solid #e4e6ef;
    background-color: white;
    transition: all 0.3s ease;

    &.dx-checkbox-checked {
      background-color: #3699ff;
      border-color: #3699ff;

      .dx-checkbox-icon {
        color: white;
        font-size: 12px;
      }
    }

    &:hover {
      border-color: #3699ff;
    }
  }
}

.label {
  font-weight: 500;
  color: #3f4254;
  font-size: 0.95rem;
}

/* تحسين الـ Data Grid */
::ng-deep dx-data-grid {
  .dx-datagrid {
    border: 1px solid #e4e6ef;
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

    .dx-datagrid-headers {
      background-color: #f8f9fa;
      border-bottom: 2px solid #e4e6ef;

      .dx-header-row {
        .dx-datagrid-table .dx-row > td {
          padding: 1rem 0.75rem;
          font-weight: 600;
          color: #3f4254;
          font-size: 0.875rem;
          border-right: 1px solid #e4e6ef;
        }
      }
    }

    .dx-datagrid-rowsview {
      .dx-row {
        &:nth-child(even) {
          background-color: #f8f9fa;
        }

        &:hover {
          background-color: rgba(54, 153, 255, 0.05);
        }

        .dx-datagrid-table .dx-row > td {
          padding: 0.875rem 0.75rem;
          border-right: 1px solid #e4e6ef;
          color: #3f4254;
          font-size: 0.875rem;
        }
      }
    }

    .dx-datagrid-filter-row {
      background-color: #f1f3f6;

      .dx-editor-cell {
        padding: 0.5rem;

        .dx-texteditor {
          .dx-texteditor-input {
            padding: 0.375rem 0.5rem;
            font-size: 0.8rem;
            border: 1px solid #e4e6ef;
            border-radius: 0.25rem;
          }
        }
      }
    }

    .dx-datagrid-pager {
      background-color: #f8f9fa;
      border-top: 1px solid #e4e6ef;
      padding: 1rem;

      .dx-pages {
        .dx-page {
          margin: 0 0.25rem;
          padding: 0.5rem 0.75rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;

          &.dx-selection {
            background-color: #3699ff;
            color: white;
          }

          &:hover:not(.dx-selection) {
            background-color: rgba(54, 153, 255, 0.1);
          }
        }
      }
    }
  }
}

/* تحسين الـ Export Options */
::ng-deep .dx-datagrid-export-button {
  .dx-button {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;

    &:hover {
      background-color: #218838;
      border-color: #1e7e34;
    }

    .dx-button-content {
      .dx-icon {
        margin-left: 0.5rem;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 992px) {
  .form-label {
    width: 100px;
    min-width: 100px;
    font-size: 0.875rem;
  }

  .card-header {
    padding: 1rem;

    .card-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      h3 {
        font-size: 1.25rem;
      }
    }

    .btn-group {
      flex-wrap: wrap;
    }
  }

  .main-inputs {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .form-label {
    width: 80px;
    min-width: 80px;
    font-size: 0.8rem;
  }

  .form-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;

    .form-label {
      width: auto;
      margin-bottom: 0.25rem;
    }
  }

  .card-header {
    padding: 0.75rem;

    .card-title {
      h3 {
        font-size: 1.125rem;
      }
    }

    .btn-group {
      .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
      }
    }
  }

  .main-inputs {
    padding: 0.75rem;
  }

  ::ng-deep dx-data-grid {
    .dx-datagrid {
      .dx-datagrid-headers {
        .dx-header-row {
          .dx-datagrid-table .dx-row > td {
            padding: 0.75rem 0.5rem;
            font-size: 0.8rem;
          }
        }
      }

      .dx-datagrid-rowsview {
        .dx-row {
          .dx-datagrid-table .dx-row > td {
            padding: 0.625rem 0.5rem;
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .card-header {
    .card-title {
      h3 {
        font-size: 1rem;
      }
    }

    .btn-group {
      width: 100%;

      .btn {
        flex: 1;
        text-align: center;
      }
    }
  }

  .row {
    margin-bottom: 0.5rem;
  }

  .form-group {
    margin-bottom: 0.75rem;
  }
}