<!-- Cash Boxes Opening Balance Page -->
<div class="cash-boxes-opening-balance-page">

  <!-- <PERSON> Header -->
  <div class="card-header border-0 cursor-pointer w-100">
    <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
      <h3 class="fw-bolder m-0">{{ "COMMON.CashBoxesOpeningBalance" | translate }}</h3>
      <div class="btn-group">
        <button
          (click)="filter()"
          class="btn btn-sm btn-active-light-primary"
        >
          <i class="fa fa-filter"></i>
          {{ "COMMON.Filter" | translate }}
        </button>
        <button
          type="submit"
          (click)="save()"
          class="btn btn-sm btn-active-light-primary"
        >
          <i class="fa fa-save"></i>
          {{ "COMMON.SaveChanges" | translate }}
        </button>

      <!-- <button
      type="submit"
      (click)="deleteRecords()"
      class="btn btn-sm btn-active-light-primary"
    >
      {{ "COMMON.DELETE" | translate }}
      <i class="fa fa-save"></i>
    </button>  -->

        </div>
  
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
  
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
  
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >{{ "COMMON.ExportToExcel"| translate }} </a>
          </div>
  
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportToExcel" | translate }}
            </a>
          </div>
  
          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.CashBoxesOpeningBalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
  
  
        </div>
      </div>
    </div>
  </div>

  <!-- Form Container -->
  <form [formGroup]="viewListForm" class="mb-3">
    <div class="card-body border-top">
      <div class="main-inputs">

        <!-- First Row: Main Selection Fields -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="CashBoxId" class="form-label">
              {{ "COMMON.CashBox" | translate }}
            </label>
            <ng-select
              id="CashBoxId"
              formControlName="CashBoxId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="Cashs"
            ></ng-select>
          </div>

          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="branch" class="form-label">
              {{ "COMMON.Branch" | translate }}
            </label>
            <ng-select
              id="branch"
              formControlName="branch"
              bindLabel="name"
              bindValue="id"
              [items]="branches"
            ></ng-select>
          </div>
        </div>

        <!-- Second Row: Currency and Year -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="currencyid" class="form-label">
              {{ "COMMON.Currency" | translate }}
            </label>
            <ng-select
              id="currencyid"
              formControlName="currencyId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="currency"
            ></ng-select>
          </div>

          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="financialYear" class="form-label">
              {{ "COMMON.Year" | translate }}
            </label>
            <ng-select
              id="financialYear"
              formControlName="year"
              bindLabel="yearName"
              bindValue="yearName"
              [items]="financialYear"
            ></ng-select>
          </div>
        </div>

        <!-- Third Row: Options and Settings -->
        <div class="row">
          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <div class="d-flex align-items-center gap-2">
              <dx-check-box
                [value]="showzerobalance"
                valueExpr="showzerobalance"
                formControlName="showzerobalance"
              ></dx-check-box>
              <label class="label mb-0">
                {{ "COMMON.ShowZeroBalance" | translate }}
              </label>
            </div>
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <div class="d-flex align-items-center gap-2">
              <dx-check-box
                [value]="postjournalentry"
                formControlName="postjournalentry"
                valueExpr="postjournalentry"
              ></dx-check-box>
              <label class="label mb-0">
                {{ "COMMON.PostJournalEntry" | translate }}
              </label>
            </div>
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <div class="d-flex align-items-center gap-2">
              <dx-check-box
                [value]="NotPostJournalEntries"
                formControlName="NotPostJournalEntries"
                valueExpr="NotPostJournalEntries"
              ></dx-check-box>
              <label class="label mb-0">
                {{ "COMMON.NotPostJournalEntry" | translate }}
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- Data Grid Section -->
  <div class="data-grid-container">
    <dx-data-grid
      id="gridcontrole"
      [rtlEnabled]="true"
      [dataSource]="data"
      keyExpr="store_id"
      [showRowLines]="true"
      [showBorders]="true"
      [columnAutoWidth]="true"
      (onExporting)="onExporting($event)"
      [allowColumnResizing]="true"
      [allowColumnReordering]="true"
      [rowAlternationEnabled]="true"
      [hoverStateEnabled]="true"
    >
  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

      <!-- Grid Columns -->
      <dxi-column
        dataField="store_name"
        caption="{{ 'COMMON.CashBoxName' | translate }}"
        [allowSorting]="true"
        [allowFiltering]="true"
      ></dxi-column>

      <dxi-column
        dataField="store_address"
        caption="{{ 'COMMON.Address' | translate }}"
        [allowSorting]="true"
        [allowFiltering]="true"
      ></dxi-column>

      <dxi-column
        dataField="opening_balance"
        caption="{{ 'COMMON.OpeningBalance' | translate }}"
        dataType="number"
        format="currency"
        [allowSorting]="true"
        [allowFiltering]="true"
      ></dxi-column>

  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="store_name" summaryType="count"> </dxi-total-item>


  </dxo-summary>


</dx-data-grid>

    </div>
  </form>

</div>
<!-- End Cash Boxes Opening Balance Page -->