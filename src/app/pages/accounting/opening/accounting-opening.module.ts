import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { RouterModule } from '@angular/router';
import { BanksOpeningBalanceComponent } from './banks-opening-balance/banks-opening-balance.component';
import { CashBoxesOpeningBalanceComponent } from './cash-boxes-opening-balance/cash-boxes-opening-balance.component';
import { CustomersOpeningBalanceComponent } from './customers-opening-balance/customers-opening-balance.component';
import { EmployeesOpeningBalanceComponent } from './employees-opening-balance/employees-opening-balance.component';
import { OpiningAndClosingJournalsComponent } from './opining-and-closing-journals/opining-and-closing-journals.component';
import { SupplierOpeningBalanceComponent } from './supplier-opening-balance/supplier-opening-balance.component';
import { AccountingOpeningRoutingModule } from './accounting-opening-routing.module';

@NgModule({
  declarations: [
    CashBoxesOpeningBalanceComponent,
    EmployeesOpeningBalanceComponent,
    BanksOpeningBalanceComponent,
    CustomersOpeningBalanceComponent,
    SupplierOpeningBalanceComponent,
    OpiningAndClosingJournalsComponent,
  ],
  imports: [
    SharedModule,
    AccountingOpeningRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingOpeningModule {}
