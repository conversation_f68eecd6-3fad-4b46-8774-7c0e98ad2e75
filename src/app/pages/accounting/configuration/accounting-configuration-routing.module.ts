import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AccountsRoutingComponent} from './accounts-routing/accounts-routing.component';
import {BankAccountComponent} from './bank-account/bank-account.component';
import {BanksComponent} from './banks/banks.component';
import {CashBoxesComponent} from './cash-boxes/cash-boxes.component';
import {ChartOfAccountsComponent} from './chart-of-accounts/chart-of-accounts.component';
import {ClosingPeriodComponent} from './closing-period/closing-period.component';
import {CostTypesComponent} from './cost-types/cost-types.component';
import {EstimatedBudgetComponent} from './estimated-budget/estimated-budget.component';
import {FinalAccountsTypesComponent} from './final-accounts-types/final-accounts-types.component';
import {
    FinancialAnalysisPreparationComponent
} from './financial-analysis-preparation/financial-analysis-preparation.component';
import {FiscalYearComponent} from './fiscal-year/fiscal-year.component';
import {JournalComponent} from './journal/journal.component';
import {PartnersComponent} from './partners/partners.component';
import {TaxGroupComponent} from './tax-group/tax-group.component';
import {TaxesComponent} from './taxes/taxes.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'cash_boxes',
        pathMatch: 'full'
    },
    {
        path: 'cash_boxes',
        component: CashBoxesComponent
    },
    {
        path: 'banks',
        component: BanksComponent
    },
    {
        path: 'bank_account',
        component: BankAccountComponent
    },
    {
        path: 'journal',
        component: JournalComponent
    },
    {
        path: 'final_accounts_types',
        component: FinalAccountsTypesComponent
    },
    {
        path: 'financial_analysis_preparation_',
        component: FinancialAnalysisPreparationComponent
    },
    {
        path: 'chart_of_accounts',
        component: ChartOfAccountsComponent
    },
    {
        path: 'accounts_routing',
        component: AccountsRoutingComponent
    },
    {
        path: 'estimated_budget',
        component: EstimatedBudgetComponent
    },
    {
        path: 'closing_period',
        component: ClosingPeriodComponent
    },
    {
        path: 'fiscal_year',
        component: FiscalYearComponent
    },
    {
        path: 'cost_types',
        component: CostTypesComponent
    },
    {
        path: 'partners',
        component: PartnersComponent
    },
    {
        path: 'tax_group',
        component: TaxGroupComponent
    },
    {
        path: 'taxes',
        component: TaxesComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingConfigurationRoutingModule {
}
