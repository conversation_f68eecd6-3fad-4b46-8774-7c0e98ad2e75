import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FiscalYearService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.FinancialYear+"/list"}`;
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }
  list(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl + `${id}`, {
      params: {
        currentPage: page.toString()
      }
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }

  getUsers(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'users/list');
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }
}
