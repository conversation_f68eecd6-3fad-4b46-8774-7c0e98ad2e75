import {NgModule} from '@angular/core';

import {CashBoxesComponent} from './cash-boxes/cash-boxes.component';
import {BanksComponent} from './banks/banks.component';
import {BankAccountComponent} from './bank-account/bank-account.component';
import {JournalComponent} from './journal/journal.component';
import {FinalAccountsTypesComponent} from './final-accounts-types/final-accounts-types.component';
import {
  FinancialAnalysisPreparationComponent
} from './financial-analysis-preparation/financial-analysis-preparation.component';
import {ChartOfAccountsComponent} from './chart-of-accounts/chart-of-accounts.component';
import {AccountsRoutingComponent} from './accounts-routing/accounts-routing.component';
import {EstimatedBudgetComponent} from './estimated-budget/estimated-budget.component';
import {ClosingPeriodComponent} from './closing-period/closing-period.component';
import {FiscalYearComponent} from './fiscal-year/fiscal-year.component';
import {CostTypesComponent} from './cost-types/cost-types.component';
import {PartnersComponent} from './partners/partners.component';
import {TaxGroupComponent} from './tax-group/tax-group.component';
import {TaxesComponent} from './taxes/taxes.component';
import {ChartofaccountComponent} from './chart-of-accounts/chartofaccount/chartofaccount.component';
import {SharedModule} from "../../shared/shared.module";
import {DxContextMenuModule, DxTreeViewModule} from "devextreme-angular";
import {RouterModule} from "@angular/router";
import {AccountingConfigurationRoutingModule} from "./accounting-configuration-routing.module";

@NgModule({
    declarations: [

        CashBoxesComponent,
        BanksComponent,
        BankAccountComponent,
        JournalComponent,
        FinalAccountsTypesComponent,
        FinancialAnalysisPreparationComponent,
        ChartOfAccountsComponent,
        AccountsRoutingComponent,
        EstimatedBudgetComponent,
        ClosingPeriodComponent,
        FiscalYearComponent,
        CostTypesComponent,
        PartnersComponent,
        TaxGroupComponent,
        TaxesComponent,
        ChartofaccountComponent,
    ],
    imports: [
        SharedModule,
        AccountingConfigurationRoutingModule,
        DxTreeViewModule,
        DxContextMenuModule,
    ],
    exports: [RouterModule],
})
export class AccountingConfigurationModule {
}
