
import { ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
/*import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';*/

import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { ChartOfAccountsService } from '../chart-of-accounts.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-banks',
    templateUrl: './chartofaccount.component.html',
    styleUrls: ['./chartofaccount.component.scss'],
    standalone: false
})
export class ChartofaccountComponent implements OnInit, OnDestroy {
  formData: FormGroup;
  initData: any;
  moduleName = 'accounting.chartofaccunt';
  data: any[] = [];
  finalAccounts: any[] = [];
  finalBalanceTypes: any[] = [];
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [{ name: 'تمت', value: true }, { name: 'لم تتم', value: false }];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
  emailValue: string;
  isLoading = false;
  searchExpr: any;
  rules: any;

  constructor(private fb: FormBuilder,
    private myService: ChartOfAccountsService,
    private translateService: TranslateService,
    private route: ActivatedRoute,
    private router: Router,
    private cd: ChangeDetectorRef) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required]
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
    this.emailValue = '<EMAIL>';
    this.rules = { X: /[02-9]/ };

    this.subscriptions.add(this.myService.finalAccounts().subscribe(r => {
      if (r.success) {
        this.finalAccounts = r.data;

        this.cd.detectChanges();

      }
    }));

    this.subscriptions.add(this.myService.finalBalanceTypes().subscribe(r => {
      if (r.success) {
        this.finalBalanceTypes = r.data;

        this.cd.detectChanges();

      }
    }));

  }
  valueChanged(data:any) {
    this.emailValue = `${data.value.replace(/\s/g, '').toLowerCase()}@corp.com`;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }


  //      public long ID { get; set; }

  //      public long ? AccCode { get; set; }

  //      public string AccName { get; set; }

  //      public long ? ParentID { get; set; }

  //      public bool ? AccType { get; set; }

  //      public int ? AccLevel { get; set; }

  //      public bool ? ISmain { get; set; }

  //      public int ? MezanyaID { get; set; }
  //      public int ? ACC_Type_ID { get; set; }
  //      public string LongCode { get; set; }
  //      public string AccName_En { get; set; }
  //      public string AccountGroup_ID { get; set; }
  //      public long ? ClassificationId { get; set; }
  //      public long ? SortID { get; set; }
  //      public bool ? Machine_Mandatory { get; set; }
  //      public bool ? IsMadeen { get; set; }
  //      public long ? CostID { get; set; }

  initForm() {
    this.formData = this.fb.group({
      id: [this.initData?.id, Validators.required],
      accCode: [this.initData?.accCode, Validators.required],
      accName: [this.initData?.accName, Validators.required],
      parentID: [this.initData?.parentID, Validators.required],
      accType: [this.initData?.accType, Validators.required],
      accName_En: [this.initData?.accName_En, Validators.required],
       isMain: false
    });
  }

  save(form: any) {
    if (this.formData.valid) {
      if (!this.isLoading) {
        this.isLoading = true;
       
this.subscriptions.add(this.myService.create(form).subscribe(r=> {

  if (r.success) {
     this.router.navigate(['']);
  }

}));  
 } else {
    }
  } }

  ngOnInit(): void {
    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.myService.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.myService.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.myService.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.myService.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }

  remove(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.myService.delete(item.id)
          .subscribe(r => {
            if (r.success) {
              this.data = this.data.filter(c => c.id != item.id);
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(this.myService.list('', value).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cd.detectChanges();
        MenuComponent.reinitialization();
      }
    }));
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.data = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }


  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Banks.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscriptions.add(this.myService.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'Banks.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  uploadExcel() {
    var input = document.createElement('input');
    input.type = "file";
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(this.myService.importExcel(fd)
            .subscribe(e => {
              if (e) {
                this.currentPage = 1;
              }
            }));
        }
      }
    }
  }


  get isArabic() {
    return this.translateService.currentLang == "ar";
  }

} 
