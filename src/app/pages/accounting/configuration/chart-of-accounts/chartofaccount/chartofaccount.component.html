

<form [formGroup]="formData">

  <div class="dx-fieldset">
    <div class="dx-field">
      <div class="dx-field-label">Name Ar</div>
      <div class="dx-field-value">
        <dx-text-box placeholder="Enter ccount Name Ar here..."
                     [inputAttr]="{ 'aria-label': 'accName' }"
                     [width]="350"></dx-text-box>
      </div>
    </div>
    <div class="dx-field">
      <div class="dx-field-label">Name En</div>
      <div class="dx-field-value">
        <dx-text-box formControlName="nameEn" placeholder="Enter ccount Name En here..."
                     [inputAttr]="{ 'aria-label': 'nameEn' }"
                     [width]="350"></dx-text-box>
      </div>
    </div>
    <div  class="dx-field">
      <div class="dx-field-label">Parent Account</div>
      <div class="dx-field-value">
        <dx-text-box value=""
                     [disabled]="true"
                     [showClearButton]="true"
                     [inputAttr]="{ 'aria-label': 'parentID' }"
                     [width]="350">
        </dx-text-box>
      </div>
    </div>

    <div class="dx-field">
      <div class="dx-field-label">Level</div>
      <div class="dx-field-value">
        <dx-text-box value=""
                     [disabled]="true"
                     [showClearButton]="true"
                     [inputAttr]="{ 'aria-label': 'accLevel' }"
                     [width]="100">
        </dx-text-box>
      </div>
    </div>

    <div class="dx-field">
      <div class="dx-field-label">Code</div>
      <div class="dx-field-value">
        <dx-text-box [inputAttr]="{ 'aria-label': 'accCode' }"
                     placeholder="Code"
                     [showClearButton]="true"
                     value="0"
                     [width]="100"></dx-text-box>
      </div>
    </div>
  </div>


  <div class="dx-field">
    <div class="dx-field-label">Final Account</div>
    <div class="dx-field-value">
      <dx-select-box [dataSource]="finalAccounts"
                     [displayExpr]="isArabic ? 'nameAr': 'nameEn'"
                     valueExpr="id"
                     [inputAttr]="{ 'aria-label': 'Name' }"
                     [searchMode]="'contains'"
                     [searchExpr]="searchExpr"
                     [searchTimeout]="200"
                     [minSearchLength]="0"
                     [searchEnabled]="true"
                     [width]="350">
        [showDataBeforeSearch]="false">
      </dx-select-box>
    </div>
  </div>


  <div class="dx-field">
    <div class="dx-field-label">Financial Statements</div>
    <div class="dx-field-value">
      <dx-select-box [dataSource]="finalBalanceTypes"
                     [displayExpr]="isArabic ? 'nameAr': 'nameEn'"
                     valueExpr="id"
                     [inputAttr]="{ 'aria-label': 'Name' }"
                     [searchMode]="'contains'"
                     [searchExpr]="searchExpr"
                     [searchTimeout]="200"
                     [minSearchLength]="0"
                     [searchEnabled]="true"
                     [width]="350">
        [showDataBeforeSearch]="false">
      </dx-select-box>
    </div>
  </div>


  <div class="row">
    <div class="col-6">
      <div class="dx-field">
        <div class="dx-field-label">Is Main ?</div>
        <div class="dx-field-value">
          <dx-switch formControlName="isMain" [value]="true"></dx-switch>
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="dx-field">
        <div class="dx-field-label">Is Debit ?</div>
        <div class="dx-field-value">
          <dx-switch [value]="false"></dx-switch>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="dx-field">
        <div class="dx-field-label">Require Analatyc Account ?</div>
        <div class="dx-field-value">
          <dx-switch [value]="true"></dx-switch>
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="dx-field-label">Require Equpment ?</div>
      <div class="dx-field-value">
        <dx-switch [value]="false"></dx-switch>
      </div>
    </div>
  </div>



  <div class="row">
    <div class="col-3 mt-5 p-1">
      <div class="text-left">
        <button type="submit"
                class="btn btn-primary btn-sm w-100"
                [disabled]="isLoading"
                (click)="save(formData.value)">
          <ng-container *ngIf="!isLoading">Save</ng-container>
          <ng-container *ngIf="isLoading">
            <span clas="indicator-progress" [style.display]="'block'">
              Please wait...{{ " " }}
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </ng-container>
        </button>
      </div>
    </div>

    <div class="col-3 mt-5 p-1">
      <div class="text-left">
        <button type="submit"
                class="btn btn-primary btn-sm w-100"
                [disabled]="isLoading"
                (click)="save(formData.value)">
          <ng-container *ngIf="!isLoading">Update</ng-container>
          <ng-container *ngIf="isLoading">
            <span clas="indicator-progress" [style.display]="'block'">
              Please wait...{{ " " }}
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </ng-container>
        </button>
      </div>
    </div>

  </div>

</form>
