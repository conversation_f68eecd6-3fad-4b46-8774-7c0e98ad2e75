

import { AccountingService } from '../../accounting.service';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
/*import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';*/

import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { ChartOfAccountsService, TreeNode } from './chart-of-accounts.service';
import { TranslateService } from '@ngx-translate/core';
import { DxContextMenuComponent, DxTreeViewComponent } from 'devextreme-angular';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'app-chart-of-accounts',
    templateUrl: './chart-of-accounts.component.html',
    styleUrls: ['./chart-of-accounts.component.scss'],
    standalone: false
})
export class ChartOfAccountsComponent implements OnInit, OnDestroy {
  moduleName = 'Accounting.ChartOfAccounts';
  data: any[] = [];
  menuItems: any[] = [];
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [{ name: 'تمت', value: true }, { name: 'لم تتم', value: false }];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
  treeData: TreeNode[];
  @ViewChild(DxTreeViewComponent, { static: false }) treeView: DxTreeViewComponent;

  @ViewChild(DxContextMenuComponent, { static: false }) contextMenu: DxContextMenuComponent;
  selectedTreeItem: any;
  logItems: any;
  constructor(private fb: FormBuilder,
    private myService: ChartOfAccountsService,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required]
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
    this.menuItems = [
      { id: 'expand', text: 'Expand category' },
      { id: 'collapse', text: 'Collapse category' },
      { id: 'details', text: 'Show details' },
      { id: 'copy', text: 'Copy Selected' },
      { id: 'create', text: 'Create New Account' },
      { id: 'update', text: 'Edit Account' },
      { id: 'delete', text: 'Delete Selectd Account' },
    ];
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.myService.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));

    this.subscriptions.add(this.myService.getTree().subscribe(r => {
      if (r.success) {
        this.treeData = this.arrayToTree(r.data);
        this.cd.detectChanges();
      }
    }));

    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.myService.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.myService.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.myService.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }

  remove(item: any) {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
      },
      icon: 'warning'
    }).then(r => {
      if (r.isConfirmed) {
        this.subscriptions.add(this.myService.delete(item.id)
          .subscribe(r => {
            if (r.success) {
              this.data = this.data.filter(c => c.id != item.id);
              this.cd.detectChanges();
            }
          }));
      }
    });
  }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(this.myService.list('', value).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cd.detectChanges();
        MenuComponent.reinitialization();
      }
    }));
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.data = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }


  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscriptions.add(this.myService.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'warehouses.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  uploadExcel() {
    var input = document.createElement('input');
    input.type = "file";
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(this.myService.importExcel(fd)
            .subscribe(e => {
              if (e) {
                this.currentPage = 1;
              }
            }));
        }
      }
    }
  }
  arrayToTree(inputArray: any[]): TreeNode[] {
    const nodeMap: { [key: number]: TreeNode } = {};

    // Create tree nodes and populate nodeMap
    for (const item of inputArray) {
      const { id, nameAr, nameEn, parentID } = item;
      nodeMap[id] = new TreeNode(id, nameAr, nameEn, parentID);
    }

    let root: TreeNode[] = [];

    // Build the tree by linking child nodes to their parent nodes
    for (const item of inputArray) {
      const { id, parentID } = item;
      const node = nodeMap[id];
      if (parentID === 0) {
        // If it's a root node, assign it to the root variable
        root.push(node)
      } else {
        // If it's not a root node, find its parent node using parentId and add it as a child
        const parent = nodeMap[parentID];
        if (parent) {
          parent.items.push(node);
        }
      }
    }

    return root;
  }

  treeViewItemContextMenu(e: any) {
    this.selectedTreeItem = e.itemData;

    const isRoot = e.itemData.parentId === 0;
    const hasItems = e.itemData.items.length > 0;
    const contextMenu = this.contextMenu.instance;
    contextMenu.option('items[6].visible', !isRoot);
    contextMenu.option('items[4].visible', hasItems);
    //contextMenu.option('items[2].visible', isRoot);
    //contextMenu.option('items[3].visible', isRoot);

    //contextMenu.option('items[0].disabled', e.node.expanded);
    //contextMenu.option('items[1].disabled', !e.node.expanded);
  }

  contextMenuItemClick(e: any) {
    let logEntry = '';
    const treeView = this.treeView.instance;
    switch (e.itemData.id) {
      case 'expand': {
        logEntry = `The '${this.selectedTreeItem.text}' group was expanded`;
        treeView.expandItem(this.selectedTreeItem.id);
        break;
      }
      case 'collapse': {
        logEntry = `The '${this.selectedTreeItem.text}' group was collapsed`;
        treeView.collapseItem(this.selectedTreeItem.id);
        break;
      }
      case 'details': {
        logEntry = `Details about '${this.selectedTreeItem.text}' were displayed`;
        break;
      }
      case 'copy': {
        this.copyMessage(this.selectedTreeItem.text);
        break;
      }
      case 'create': {
        this.router.navigate(['/accounting/chartofaccount/create', this.selectedTreeItem.id]);
        break;
      }
      case 'update': {
        this.router.navigate(['/accounting/chartofaccount/edit', this.selectedTreeItem.id]);
        break;
      }
      case 'delete': {
        this.subscriptions.add(this.myService.delete(this.selectedTreeItem.id + '').subscribe(r => {
          if (r.success) {

          }
        }));
        break;
      }

    }
    this.logItems.push(logEntry);
  }
  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.toastr.success('Copied to Clipboard!.');
  }

  get isArabic() {
    return this.translateService.currentLang == "ar";
  }
}
