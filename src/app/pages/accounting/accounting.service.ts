import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export class Status {
  id: number;
  name: string;
}

const statuses: Status[] = [{
  id: 1, name: 'Not Started',
}, {
  id: 2, name: 'In Progress',
}, {
  id: 3, name: 'Deferred',
}, {
  id: 4, name: 'Need Assistance',
}, {
  id: 5, name: 'Completed',
},
];

@Injectable({
  providedIn: 'root'
})
export class AccountingService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query
      }
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }

  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }

  getFinancialEntities(entityId : any): Observable<any> {
    if (entityId === 1) {
      return this.http.get<any>('api/Tenders/TendersDropdown');
    } else if (entityId === 2) {
      return this.http.get<any>('api/Machine/dropdown');
    } else if (entityId === 3) {
      return this.http.get<any>('api/Assets/dropdown');
    } else if (entityId === 4) {
      return this.http.get<any>('api/Departments');
    } else if (entityId === 5) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (entityId === 6) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (entityId === 7) {
      return this.http.get<any>('api/Cars/dropdown');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }


 
  getThirdPartyAccountId(typId : any): Observable<any> {
    if (typId === 0) {
      return this.http.get<any>('api/ChartofAccounts/ChartofAccountsdropdown');
    } else if (typId === 2) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (typId === 3) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (typId === 4) {
      return this.http.get<any>('api/employees/EmployeesDropdown');
    } else if (typId === 5) {
      return this.http.get<any>('api/BankAccount');
    } else if (typId === 6) {
      return this.http.get<any>('api/CashBox');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }





  
  getMainCustDropdown(): Observable<any> {
    return this.http.get<any>('api/MainCustomer/dropdown');
  }
  getCustDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/dropdown');
  }
  getPartners(): Observable<any> {
    return this.http.get<any>('api/Partners/PartnerDropdown');
  }
  getBankAccount(): Observable<any> {
    return this.http.get<any>('api/BankAccount');
  }
  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }
  getCustTypeDropdown(): Observable<any> {
    return this.http.get<any>('api/CustomersCategory');
  }
  getSupplierTypeDropdown(): Observable<any> {
    return this.http.get<any>('api/SupplierClassifications');
  }
  getAccountThirdParty(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty');
  }
  getCustactionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/Customer');
  }
  getBankActionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/Bank');
  }
    getCashBoxActionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/CashBox');
  }
  getSupplierActionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/Supplier');
  }
 getSEmpActionType(): Observable<any> {
   return this.http.get<any>('api/AccountThirdParty/Emp');
  }
  getSuppliersDropdown(): Observable<any> {
    return this.http.get<any>('api/Suppliers/dropdown');
  }
  getCurrency(): Observable<any> {
    return this.http.get<any>('api/Currency');
  }
  getbranches(): Observable<any> {
    return this.http.get<any>('api/Branch');
  }
  getUsers(): Observable<any> {
    return this.http.get<any>('api/FalconUsers');
  }
  getJournals(): Observable<any> {
    return this.http.get<any>('api/Journal');
  }
  getWarehouses(): Observable<any> {
    return this.http.get<any>('api/Warehouses');
  }
  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }
  getSalesPersons(): Observable<any> {
    return this.http.get<any>('api/Salesperson/SalespersonDropdown');
  }


  getMainChartOfAccounts(): Observable<any> {
    return this.http.get<any>('api/ChartofAccounts/MainChartofAccountsdropdown');
  }
  getChartOfAccounts(): Observable<any> {
    return this.http.get<any>('api/ChartofAccounts/ChartofAccountsdropdown');
  }

  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }

  getCostGroups(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccountsGroups/AnalyticAccountsGroupsDropdown');
  }
 

  getProjects(): Observable<any> {
    return this.http.get<any>('api/Tenders/TendersDropdown');
  }

  getDepartDropdown(): Observable<any> {
    return this.http.get<any>('api/Departments');
  }
  
  getCashBox(): Observable<any> {
    return this.http.get<any>('api/CashBox');
  }
  

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  batch(obj: any): Observable<any> {
    return this.http.post(this.baseUrl + 'batch/', obj);
  }

  getStatuses() {
    return statuses;
  }

}
