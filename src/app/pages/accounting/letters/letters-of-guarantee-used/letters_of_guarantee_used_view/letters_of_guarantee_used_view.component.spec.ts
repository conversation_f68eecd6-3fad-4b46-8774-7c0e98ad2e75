/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Letters_of_guarantee_used_viewComponent } from './letters_of_guarantee_used_view.component';

describe('Letters_of_guarantee_used_viewComponent', () => {
  let component: Letters_of_guarantee_used_viewComponent;
  let fixture: ComponentFixture<Letters_of_guarantee_used_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Letters_of_guarantee_used_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Letters_of_guarantee_used_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
