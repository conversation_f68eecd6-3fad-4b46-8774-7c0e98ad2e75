<form [formGroup]="newdata">

  <div class="card mb-5 mb-xl-10">
  
    <div class="card-header border-0 cursor-pointer w-100">
  
      <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
        <h3 class="fw-bolder m-0">{{ "COMMON.NewLettersOfGuaranteeUsed" | translate }}</h3>
   
        <div [style.position]="'relative'">
       <div class="btn-group">
       <button
         type="submit"
         (click)="discard()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.Cancel" | translate }}
         <i class="fa fa-close"></i>
       </button>
       <button
         type="submit"
         (click)="save()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.SaveChanges" | translate }}
         <i class="fa fa-save"></i>
       </button>
     </div>
     <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
               (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
     <button
       class="btn btn-icon btn-active-light-primary mx-2"
       (click)="toggleMenu()"
       data-bs-toggle="tooltip"
       data-bs-placement="top"
       data-bs-trigger="hover"
       title="Settings"
     >
       <i class="fa fa-gear"></i>
     </button>
     <div
       class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
       [class.show]="menuOpen"
       [style.position]="'absolute'"
       [style.top]="'100%'"
       [style.zIndex]="'1050'"
       [style.left]="isRtl ? '0' : 'auto'"
       [style.right]="!isRtl ? '0' : 'auto'"
     >
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           (click)="exportToExcel()"
           data-kt-company-table-filter="delete_row"
         >
           {{ "COMMON.ExportToExcel" | translate }}
         </a>
       </div>
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openSmsModal()"
         >
           {{ "COMMON.SendViaSMS" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openEmailModal()"
         >
           {{ "COMMON.SendViaEmail" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openWhatsappModal()"
         >
           {{ "COMMON.SendViaWhatsapp" | translate }}
         </a>
       </div>
  
       <div
         class="menu-item px-3"
         *hasPermission="{
           action: 'printAction',
           module: 'Accounting.LettersOfGuaranteeUsed'
         }"
       >
         <a
           class="menu-link px-3"
           target="_blank"
           href="/reports/warehouses"
           data-kt-company-table-filter="delete_row"
           >{{ "COMMON.Print" | translate }}</a
         >
       </div>
       <!-- <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'HR.Employees'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses?withLogo=true"
         data-kt-company-table-filter="delete_row"
         >Print With Logo</a
       >
     </div> -->
     </div>
   </div>
  
  </div>
  
    </div>

      <div class="card-body border-top p-9">

    <div class="main-inputs mb-5">

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.GuaranteeDate" | translate }}</label>
          <input
            id="GuaranteeDateValue"
            type="date"
            formControlName="GuaranteeDateValue"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.ExpirationDate" | translate }}</label>
          <input
            id="ExpirationDateValue"
            type="date"
            formControlName="ExpirationDateValue"
            class="form-control"
          />
        </div>


  

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="GuaranteeLetterTypeId" class="form-label">
            {{ "COMMON.GuaranteeLetterType" | translate }}
          </label>
          <ng-select
            id="GuaranteeLetterTypeId"
            [(ngModel)]="GuaranteeLetterTypeId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="GuaranteeLetters"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="bankAccountId" class="form-label">
            {{ "COMMON.Account" | translate }}
          </label>
          <ng-select
            id="bankAccountId"
            [(ngModel)]="bankAccountId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="Banks"
          ></ng-select>
        </div>

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="projectId" class="form-label">
            {{ "COMMON.Project" | translate }}
          </label>
          <ng-select
            id="projectId"
            [(ngModel)]="projectId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="projects"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="projectvalue" class="form-label">
            {{ "COMMON.ProjectValue" | translate }}
          </label>
          <input
            id="projectvalue"
            type="text"
            formControlName="projectvalue"
            name="projectvalue"
            class="form-control"
          />
        </div>



      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="ProjectTypeId" class="form-label">
            {{ "COMMON.ProjectType" | translate }}
          </label>
          <ng-select
            id="ProjectTypeId"
            [(ngModel)]="ProjectTypeId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="ProjectTypes"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="commission" class="form-label">
            {{ "COMMON.Commission" | translate }}
          </label>
          <input
            id="commission"
            type="text"
            formControlName="commission"
            name="commission"
            class="form-control"
          />

        </div>



      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="currencyid" class="form-label">
            {{ "COMMON.Currency" | translate }}
          </label>
          <ng-select
            id="currencyid"
            [(ngModel)]="currencyId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="currency"
          ></ng-select>
        </div>


        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="journal" class="form-label"> {{ "COMMON.JournalName" | translate }}</label>
          <ng-select
            id="journal"
            formControlName="journal"
            bindLabel="name"
            bindValue="id"
            [items]="Journals"
          ></ng-select>
        </div>

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="supplier" class="form-label">
            {{ "COMMON.Supplier" | translate }}
          </label>
          <ng-select
            id="supplier"
            [(ngModel)]="supplierId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="suppliers"
          ></ng-select>
        </div>


        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="costId" class="form-label">
            {{ "COMMON.CostName" | translate }}
          </label>
          <ng-select
            id="costId"
            formControlName="costId"
            bindLabel="nameAr"
            bindValue="code"
            [items]="costCenter"
          ></ng-select>
        </div>

      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="cashdeposit" class="form-label">
            {{ "COMMON.CashDeposit" | translate }}
          </label>
          <input
            id="cashdeposit"
            type="text"
            formControlName="cashdeposit"
            name="cashdeposit"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="cashdepositpercentage" class="form-label">
            {{ "COMMON.CashDepositPercentage" | translate }}
          </label>
          <input
            id="cashdepositpercentage"
            type="text"
            formControlName="cashdepositpercentage"
            name="cashdepositpercentage"
            class="form-control"
          />
        </div>

      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="guaranteenumber" class="form-label">
            {{ "COMMON.GuaranteeNumber" | translate }}
          </label>
          <input
            id="guaranteenumber"
            type="text"
            formControlName="guaranteenumber"
            name="guaranteenumber"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="transaction" class="form-label">
            {{ "COMMON.Transaction" | translate }}
          </label>
          <input
            id="transaction"
            type="text"
            formControlName="transaction"
            name="transaction"
            class="form-control"
          />
        </div>

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="guaranteevalue" class="form-label">
            {{ "COMMON.GuaranteeValue" | translate }}
          </label>
          <input
            id="guaranteevalue"
            type="text"
            formControlName="guaranteevalue"
            name="guaranteevalue"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="guaranteepercentage" class="form-label">
            {{ "COMMON.GuaranteePercentage" | translate }}
          </label>
          <input
            id="guaranteepercentage"
            type="text"
            formControlName="guaranteepercentage"
            name="guaranteepercentage"
            class="form-control"
          />
        </div>

      </div>








    </div>

  </div>

  
  </div>




</form>
