/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Changing_guarantee_value_viewComponent } from './changing_guarantee_value_view.component';

describe('Changing_guarantee_value_viewComponent', () => {
  let component: Changing_guarantee_value_viewComponent;
  let fixture: ComponentFixture<Changing_guarantee_value_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Changing_guarantee_value_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Changing_guarantee_value_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
