/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Renewal_warranty_period_viewComponent } from './renewal_warranty_period_view.component';

describe('Renewal_warranty_period_viewComponent', () => {
  let component: Renewal_warranty_period_viewComponent;
  let fixture: ComponentFixture<Renewal_warranty_period_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Renewal_warranty_period_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Renewal_warranty_period_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
