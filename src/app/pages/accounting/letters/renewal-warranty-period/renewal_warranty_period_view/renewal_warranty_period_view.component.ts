import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-renewal_warranty_period_view',
  templateUrl: './renewal_warranty_period_view.component.html',
  styleUrls: ['./renewal_warranty_period_view.component.css'],
  standalone:false,

})
export class Renewal_warranty_period_viewComponent implements OnInit {

  moduleName : "Accounting.RenewalWarrantyPeriod"
AssetsList: FormGroup;
   [x: string]: any;
   data: any[];
   isGridBoxOpened: boolean;
   editorOptions: any;
   gridBoxValue: number[] = [1];
   subscription = new Subscription();
   currentFilter: any;
   costCenterId: number = 0;
   costCenters: any[];
   AssetCategories: any[];
   AssetCategoryId: number = 0;
   branches: [] = [];
   branchid: any = 1;
   invoiceNoId: any= 0;
   NetAssetValueId: any= 0;
   AssetValueId: any= 0;
   AssetNameId: any= 0;
   DepreciationRateId: any= 0;
   ReferenceNumberId: any= 0;
   CodeId: any= 0;
   SerialId: any= 0;
   DepreciationMethodId: any= 0;
   FinancialEntities: any[];
   SerialNoId: any= 0;
   NotesId: any= 0;
   SalvageValueId: any= 0;
   ModelNoId: any= 0;
   CashBoxId: any = 1;
   activeTab: string = 'tab1';
   selectedItemKeys: any = [];
   financial_entity_TypeId: any = 0;
   financial_entity_Id: any = 0;
   analyticAccountTypeId: any = 0;
   financial_entity_Types: { id: number; name: string }[] = [
     { id: 1, name: 'Project' },
     { id: 2, name: 'Machine' },
     { id: 3, name: 'Assets' },
     { id: 4, name: 'Department' },
     { id: 5, name: 'Supplier' },
     { id: 6, name: 'Customer' },
     { id: 7, name: 'Cars' },
   ];

isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};

smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;

constructor(private service: AccountingService,    private fb: FormBuilder,
private cdk: ChangeDetectorRef,  private router: Router,private route: ActivatedRoute,) { 


    const today = new Date().toISOString().slice(0, 10); 

    const firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
    const firstDayFormatted = firstDayOfYear.toISOString().slice(0, 10); 
    
    this.newdata = this.fb.group({

      guaranteenumber: [null, Validators.required],
      notes: [null, Validators.required],  
      Commission: [null, Validators.required],
      currencyid: [null, Validators.required],
      costId: [null, Validators.required],
      d: [null, Validators.required],
      journal: [null, Validators.required],
      financial_entity_TypeI: [null, Validators.required],
      financial_entity_Id: [null, Validators.required],
      analyticAccountTypeId: [null, Validators.required],
      fromDateValue: [firstDayFormatted, Validators.required], 
      toDateValue: [today, Validators.required], 
    });
    
    
   
   
   this.subscription.add(
     service.getbranches().subscribe((r) => {
       if (r.success) {
         this.branches = r.data;
         this.cdk.detectChanges();
       }
     })
   );
   
   this.subscription.add(
     service.getCostCenters().subscribe((r) => {
       if (r.success) {
         this.costCenters = r.data;
         this.cdk.detectChanges();
       }
     })
   );

   this.subscription.add(service.getBankAccount().subscribe(r => {
    if (r.success) {
      this.Banks = r.data;
      this.cdk.detectChanges();

    }
  }));

  this.subscription.add(service.getCurrency().subscribe(r => {
    if (r.success) {
      this.currency = r.data;
      this.cdk.detectChanges();
    }
  }));

  this.subscription.add(
    service.getJournals().subscribe((r) => {
      if (r.success) {
        this.Journals = r.data;

        this.cdk.detectChanges();
      }
    })
  );


  this.subscription.add(
    service.getCostCenters().subscribe((r) => {
      if (r.success) {
        this.costCenter = r.data;
        this.cdk.detectChanges();
      }
    })
  );

  this.subscription.add(
    service.getSuppliersDropdown().subscribe((r) => {
      if (r.success) {
        this.suppliers = r.data;
        this.cdk.detectChanges();
      }
    })
  );


  this.subscription.add(service.getCurrency().subscribe(r => {
    if (r.success) {
      this.currency = r.data;
      this.cdk.detectChanges();
    }
  }));

  this.subscription.add(
    service.getJournals().subscribe((r) => {
      if (r.success) {
        this.Journals = r.data;

        this.cdk.detectChanges();
      }
    })
  );

  this.subscription.add(
    service.getCostCenters().subscribe((r) => {
      if (r.success) {
        this.costCenter = r.data;
        this.cdk.detectChanges();
      }
    })
  );


}



onFinaTypeSelect(selectedItem: any) {
  this.FinancialEntities = [];

  if (!selectedItem) {
    this.cdk.detectChanges();
    return;
  }
  // this.financial_entity_TypeId= selectedItem.id;

  this.subscription.add(
    this.service
      .getFinancialEntities(this.financial_entity_TypeId)
      .subscribe((r) => {
        if (r.success) {
          this.FinancialEntities = r.data;
          this.cdk.detectChanges();
        }
      })
  );
}

onAccountTypeSelect(selectedItem: any) {
  this.ThirdPartyAccounts = [];

  if (!selectedItem) {
    this.cdk.detectChanges();
    return;
  }
  const Typeid = selectedItem.typId;

  this.subscription.add(
    this.service.getThirdPartyAccountId(Typeid).subscribe((r) => {
      if (r.success) {
        this.ThirdPartyAccounts = r.data;
        this.cdk.detectChanges();
      }
    })
  );
}





   ngOnInit(): void {
   

  }
   
   
   discard() {
   }
   save() {
     const id = this.route.snapshot.params.id;
     if (id) {
     if (this.newdata.valid) {
     const form = this.newdata.value;
     this.subscriptions.add(
     this.myService
     .update(id, form)
     .pipe(
     finalize(() => {
     this.isLoading = false;
     this.cdk.detectChanges();
     })
     )
     .subscribe((r: { success: boolean }) => {
     if (r.success) {
     this.router.navigate(['/accounting/letters/renewal_warranty_period']);
     }
     })
     );
     } else {
     this.newdata.markAllAsTouched();
     }
     } else {
     if (this.newdata.valid) {
     const form = this.newdata.value;
     this.subscriptions.add(
     this.myService
     .create(form)
     .pipe(
     finalize(() => {
     this.isLoading = false;
     this.cdk.detectChanges();
     })
     )
     .subscribe((r: { success: boolean }) => {
     if (r.success) {
     this.router.navigate(['/accounting/letters/renewal_warranty_period']);
     }
     })
     );
     } else {
     this.newdata.markAllAsTouched();
     }
     }
     }
     
     setActiveTab(tab: string): void {
     this.activeTab = tab;
     }
     
     
     
     exportToExcel() {
     this.subscription.add(this.service.exportExcel()
     .subscribe(e => {
     if (e) {
     const href = URL.createObjectURL(e);
     const link = document.createElement('a');
     link.setAttribute('download', 'RenewalWarrantyPeriod.xlsx');
     link.href = href;
     link.click();
     URL.revokeObjectURL(href);
     }
     }));
     }
     
     openSmsModal() {
     this.smsModal.open();
     }
     openWhatsappModal() {
     this.whatsappModal.open();
     }
     openEmailModal() {
     this.emailModal.open();
     }

}
