<form [formGroup]="newdata">

  <div class="card mb-5 mb-xl-10">
  
    <div class="card-header border-0 cursor-pointer w-100">
  
      <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
        <h3 class="fw-bolder m-0">{{ "COMMON.NewLettersOfGuaranteeLimitations" | translate }}</h3>
   
        <div [style.position]="'relative'">
       <div class="btn-group">
       <button
         type="submit"
         (click)="discard()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.Cancel" | translate }}
         <i class="fa fa-close"></i>
       </button>
       <button
         type="submit"
         (click)="save()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.SaveChanges" | translate }}
         <i class="fa fa-save"></i>
       </button>
     </div>
     <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
               (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
     <button
       class="btn btn-icon btn-active-light-primary mx-2"
       (click)="toggleMenu()"
       data-bs-toggle="tooltip"
       data-bs-placement="top"
       data-bs-trigger="hover"
       title="Settings"
     >
       <i class="fa fa-gear"></i>
     </button>
     <div
       class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
       [class.show]="menuOpen"
       [style.position]="'absolute'"
       [style.top]="'100%'"
       [style.zIndex]="'1050'"
       [style.left]="isRtl ? '0' : 'auto'"
       [style.right]="!isRtl ? '0' : 'auto'"
     >
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           (click)="exportToExcel()"
           data-kt-company-table-filter="delete_row"
         >
           {{ "COMMON.ExportToExcel" | translate }}
         </a>
       </div>
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openSmsModal()"
         >
           {{ "COMMON.SendViaSMS" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openEmailModal()"
         >
           {{ "COMMON.SendViaEmail" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openWhatsappModal()"
         >
           {{ "COMMON.SendViaWhatsapp" | translate }}
         </a>
       </div>
  
       <div
         class="menu-item px-3"
         *hasPermission="{
           action: 'printAction',
           module: 'Accounting.LettersOfGuaranteeTypes'
         }"
       >
         <a
           class="menu-link px-3"
           target="_blank"
           href="/reports/warehouses"
           data-kt-company-table-filter="delete_row"
           >{{ "COMMON.Print" | translate }}</a
         >
       </div>
       <!-- <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'HR.Employees'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses?withLogo=true"
         data-kt-company-table-filter="delete_row"
         >Print With Logo</a
       >
     </div> -->
     </div>
   </div>
  
  </div>
  
    </div>

      <div class="card-body border-top p-9">

    <div class="main-inputs mb-5">

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDateValue"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDateValue"
            type="date"
            formControlName="toDateValue"
            class="form-control"
          />
        </div>


  

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="GuaranteeLetterTypeId" class="form-label">
            {{ "COMMON.GuaranteeLetterType" | translate }}
          </label>
          <ng-select
            id="GuaranteeLetterTypeId"
            [(ngModel)]="GuaranteeLetterTypeId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="GuaranteeLetters"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="bankAccountId" class="form-label">
            {{ "COMMON.Account" | translate }}
          </label>
          <ng-select
            id="bankAccountId"
            [(ngModel)]="bankAccountId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="Banks"
          ></ng-select>
        </div>

      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="cashdeposit" class="form-label">
            {{ "COMMON.CashDeposit" | translate }}
          </label>
          <input
            id="cashdeposit"
            type="text"
            [(ngModel)]="cashdeposit"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="cashdepositpercentage" class="form-label">
            {{ "COMMON.CashDepositPercentage" | translate }}
          </label>
          <input
            id="cashdepositpercentage"
            type="text"
            [(ngModel)]="cashdepositpercentage"
            class="form-control"
          />
        </div>

      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="facilityamount" class="form-label">
            {{ "COMMON.FacilityAmount" | translate }}
          </label>
          <input
            id="facilityamount"
            type="text"
            [(ngModel)]="facilityamount"
            class="form-control"
          />
        </div>

        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="commission" class="form-label">
            {{ "COMMON.Commission" | translate }}
          </label>
          <input
            id="commission"
            type="text"
            [(ngModel)]="commission"
            class="form-control"
          />
        </div>

      </div>


      <div class="row">


        <div class="form-group col-xl-8 col-md-2 col-sm-12">
          <label for="notes" class="form-label">
            {{ "COMMON.Notes" | translate }}
          </label>
          <input
            id="notes"
            type="text"
            [(ngModel)]="notes"
            class="form-control"
          />
        </div>

      </div>


    </div>

  </div>

  
  </div>




</form>
