import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';
import { ActivatedRoute, Router } from '@angular/router';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-letters_of_guarantee_limitations_view',
  templateUrl: './letters_of_guarantee_limitations_view.component.html',
  styleUrls: ['./letters_of_guarantee_limitations_view.component.css'],
  standalone:false,
})
export class Letters_of_guarantee_limitations_viewComponent implements OnInit {
  moduleName = 'Accounting.LettersOfGuaranteeLimitations';

 AssetsList: FormGroup;
   [x: string]: any;
   data: any[];
   isGridBoxOpened: boolean;
   editorOptions: any;
   gridBoxValue: number[] = [1];
   subscription = new Subscription();
   currentFilter: any;
   costCenterId: number = 0;
   costCenters: any[];
   AssetCategories: any[];
   AssetCategoryId: number = 0;
   branches: [] = [];
   branchid: any = 1;
   invoiceNoId: any= 0;
   NetAssetValueId: any= 0;
   AssetValueId: any= 0;
   AssetNameId: any= 0;
   DepreciationRateId: any= 0;
   ReferenceNumberId: any= 0;
   CodeId: any= 0;
   SerialId: any= 0;
   DepreciationMethodId: any= 0;
   SerialNoId: any= 0;
   NotesId: any= 0;
   SalvageValueId: any= 0;
   ModelNoId: any= 0;
   CashBoxId: any = 1;
   activeTab: string = 'tab1';
   selectedItemKeys: any = [];
   isRtl: boolean = document.documentElement.dir === 'rtl';
   menuOpen = false;
   toggleMenu() {
   this.menuOpen = !this.menuOpen;
   }
   actionsList: string[] = [
   'Export',
   'Send via SMS',
   'Send Via Email',
   'Send Via Whatsapp',
   'print',
   ];
   modalConfig: ModalConfig = {
   modalTitle: 'Send Sms',
   modalSize: 'lg',
   hideCloseButton(): boolean {
   return true;
   },
   dismissButtonLabel: 'Cancel',
   };
   
   smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
   emailModalConfig: ModalConfig = {
   ...this.modalConfig,
   modalTitle: 'Send Email',
   };
   whatsappModalConfig: ModalConfig = {
   ...this.modalConfig,
   modalTitle: 'Send Whatsapp',
   };
   @ViewChild('smsModal') private smsModal: ModalComponent;
   @ViewChild('emailModal') private emailModal: ModalComponent;
   @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
   
   constructor(private service: AccountingService,    private fb: FormBuilder,
   
   private cdk: ChangeDetectorRef,  private router: Router,    private route: ActivatedRoute,) { 
    
   
    const today = new Date().toISOString().slice(0, 10); 

    const firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
    const firstDayFormatted = firstDayOfYear.toISOString().slice(0, 10); 
    
    this.newdata = this.fb.group({
      cashdepositpercentage: [null, Validators.required],
      cashdeposit: [null, Validators.required],
      commission: [null, Validators.required],
      facilityamount: [null, Validators.required],
      bankAccountId: [null, Validators.required],
      GuaranteeLetterTypeId: [null, Validators.required],
      notes: [null, Validators.required],
      fromDateValue: [firstDayFormatted, Validators.required], 
      toDateValue: [today, Validators.required], 
    });
    
    
   
   
   this.subscription.add(
     service.getbranches().subscribe((r) => {
       if (r.success) {
         this.branches = r.data;
         this.cdk.detectChanges();
       }
     })
   );
   
   this.subscription.add(
     service.getCostCenters().subscribe((r) => {
       if (r.success) {
         this.costCenters = r.data;
         this.cdk.detectChanges();
       }
     })
   );

   this.subscription.add(service.getBankAccount().subscribe(r => {
    if (r.success) {
      this.Banks = r.data;
      this.cdk.detectChanges();

    }
  }));


   
   
   
   }
   
   ngOnInit(): void {
   

  }
   
   
   discard() {
   }
   save() {
     const id = this.route.snapshot.params.id;
     if (id) {
     if (this.newdata.valid) {
     const form = this.newdata.value;
     this.subscriptions.add(
     this.myService
     .update(id, form)
     .pipe(
     finalize(() => {
     this.isLoading = false;
     this.cdk.detectChanges();
     })
     )
     .subscribe((r: { success: boolean }) => {
     if (r.success) {
     this.router.navigate(['/accounting/letters/letters_of_guarantee_limitations']);
     }
     })
     );
     } else {
     this.newdata.markAllAsTouched();
     }
     } else {
     if (this.newdata.valid) {
     const form = this.newdata.value;
     this.subscriptions.add(
     this.myService
     .create(form)
     .pipe(
     finalize(() => {
     this.isLoading = false;
     this.cdk.detectChanges();
     })
     )
     .subscribe((r: { success: boolean }) => {
     if (r.success) {
     this.router.navigate(['/accounting/letters/letters_of_guarantee_limitations']);
     }
     })
     );
     } else {
     this.newdata.markAllAsTouched();
     }
     }
     }
     
     setActiveTab(tab: string): void {
     this.activeTab = tab;
     }
     
     
     
     exportToExcel() {
     this.subscription.add(this.service.exportExcel()
     .subscribe(e => {
     if (e) {
     const href = URL.createObjectURL(e);
     const link = document.createElement('a');
     link.setAttribute('download', 'LettersOfGuaranteeLimitations.xlsx');
     link.href = href;
     link.click();
     URL.revokeObjectURL(href);
     }
     }));
     }
     
     openSmsModal() {
     this.smsModal.open();
     }
     openWhatsappModal() {
     this.whatsappModal.open();
     }
     openEmailModal() {
     this.emailModal.open();
     }
 

}
