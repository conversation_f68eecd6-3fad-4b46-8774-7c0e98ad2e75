import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChangingGuaranteeValueComponent } from './changing-guarantee-value/changing-guarantee-value.component';
import { LettersOfGuaranteeBalanceComponent } from './letters-of-guarantee-balance/letters-of-guarantee-balance.component';
import { LettersOfGuaranteeLimitationsComponent } from './letters-of-guarantee-limitations/letters-of-guarantee-limitations.component';
import { LettersOfGuaranteeUsedComponent } from './letters-of-guarantee-used/letters-of-guarantee-used.component';
import { LettersOfGuaranteeComponent } from './letters-of-guarantee/letters-of-guarantee.component';
import { LettersOfGuaranteetypesComponent } from './letters-of-guaranteetypes/letters-of-guaranteetypes.component';
import { RenewalWarrantyPeriodComponent } from './renewal-warranty-period/renewal-warranty-period.component';
import { Letters_of_guaranteetypes_viewComponent } from './letters-of-guaranteetypes/letters_of_guaranteetypes_view/letters_of_guaranteetypes_view.component';
import { Exchange_request_viewComponent } from '../accounting/exchange-request/exchange_request_view/exchange_request_view.component';
import { Letters_of_guarantee_limitations_viewComponent } from './letters-of-guarantee-limitations/letters_of_guarantee_limitations_view/letters_of_guarantee_limitations_view.component';
import { Letters_of_guarantee_viewComponent } from './letters-of-guarantee/letters_of_guarantee_view/letters_of_guarantee_view.component';
import { Renewal_warranty_period_viewComponent } from './renewal-warranty-period/renewal_warranty_period_view/renewal_warranty_period_view.component';
import { Changing_guarantee_value_viewComponent } from './changing-guarantee-value/changing_guarantee_value_view/changing_guarantee_value_view.component';
import { Letters_of_guarantee_used_viewComponent } from './letters-of-guarantee-used/letters_of_guarantee_used_view/letters_of_guarantee_used_view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'letters_of_guaranteetypes',
    pathMatch: 'full',
  },
  {
    path: 'letters_of_guaranteetypes',
    component: LettersOfGuaranteetypesComponent,
  },
  {
    path: 'letters_of_guarantee_limitations',
    component: LettersOfGuaranteeLimitationsComponent,
  },
  {
    path: 'letters_of_guarantee',
    component: LettersOfGuaranteeComponent,
  },
  {
    path: 'letters_of_guarantee_used',
    component: LettersOfGuaranteeUsedComponent,
  },
  {
    path: 'letters_of_guarantee_balance',
    component: LettersOfGuaranteeBalanceComponent,
  },
  {
    path: 'renewal_warranty_period',
    component: RenewalWarrantyPeriodComponent,
  },
  {
    path: 'changing_guarantee_value',
    component: ChangingGuaranteeValueComponent,
  },

      {
  path: 'letters_of_guaranteetypes_view',
  component: Letters_of_guaranteetypes_viewComponent
    },
  {
    path: 'letters_of_guaranteetypes_view/:id',
   component: Letters_of_guaranteetypes_viewComponent

  },
      {
  path: 'letters_of_guarantee_limitations_view',
  component: Letters_of_guarantee_limitations_viewComponent
    },
  {
    path: 'letters_of_guarantee_limitations_view/:id',
   component: Letters_of_guarantee_limitations_viewComponent

  },
  

  {
    path: 'letters_of_guarantee_view',
    component: Letters_of_guarantee_viewComponent
      },
    {
      path: 'letters_of_guarantee_view/:id',
     component: Letters_of_guarantee_viewComponent
  
    },

  {
    path: 'renewal_warranty_period_view',
    component: Renewal_warranty_period_viewComponent
      },
    {
      path: 'renewal_warranty_period_view/:id',
     component: Renewal_warranty_period_viewComponent
  
    },
    
  {
    path: 'changing_guarantee_value_view',
    component: Changing_guarantee_value_viewComponent
      },
    {
      path: 'changing_guarantee_value_view/:id',
     component: Changing_guarantee_value_viewComponent
  
    },


  {
    path: 'letters_of_guarantee_used_view',
    component: Letters_of_guarantee_used_viewComponent
      },
    {
      path: 'letters_of_guarantee_used_view/:id',
     component: Letters_of_guarantee_used_viewComponent
  
    },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountingLettersRoutingModule {}
