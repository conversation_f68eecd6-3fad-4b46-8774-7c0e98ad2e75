/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Letters_of_guaranteetypes_viewComponent } from './letters_of_guaranteetypes_view.component';

describe('Letters_of_guaranteetypes_viewComponent', () => {
  let component: Letters_of_guaranteetypes_viewComponent;
  let fixture: ComponentFixture<Letters_of_guaranteetypes_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Letters_of_guaranteetypes_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Letters_of_guaranteetypes_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
