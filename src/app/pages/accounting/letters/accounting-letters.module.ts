import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { RouterModule } from '@angular/router';
import { ChangingGuaranteeValueComponent } from './changing-guarantee-value/changing-guarantee-value.component';
import { LettersOfGuaranteeBalanceComponent } from './letters-of-guarantee-balance/letters-of-guarantee-balance.component';
import { LettersOfGuaranteeLimitationsComponent } from './letters-of-guarantee-limitations/letters-of-guarantee-limitations.component';
import { LettersOfGuaranteeUsedComponent } from './letters-of-guarantee-used/letters-of-guarantee-used.component';
import { LettersOfGuaranteeComponent } from './letters-of-guarantee/letters-of-guarantee.component';
import { LettersOfGuaranteetypesComponent } from './letters-of-guaranteetypes/letters-of-guaranteetypes.component';
import { RenewalWarrantyPeriodComponent } from './renewal-warranty-period/renewal-warranty-period.component';
import { AccountingLettersRoutingModule } from './accounting-letters-routing.module';
import { Letters_of_guaranteetypes_viewComponent } from './letters-of-guaranteetypes/letters_of_guaranteetypes_view/letters_of_guaranteetypes_view.component';
import { Letters_of_guarantee_limitations_viewComponent } from './letters-of-guarantee-limitations/letters_of_guarantee_limitations_view/letters_of_guarantee_limitations_view.component';
import { Letters_of_guarantee_viewComponent } from './letters-of-guarantee/letters_of_guarantee_view/letters_of_guarantee_view.component';
import { Renewal_warranty_period_viewComponent } from './renewal-warranty-period/renewal_warranty_period_view/renewal_warranty_period_view.component';
import { Changing_guarantee_value_viewComponent } from './changing-guarantee-value/changing_guarantee_value_view/changing_guarantee_value_view.component';
import { Letters_of_guarantee_used_viewComponent } from './letters-of-guarantee-used/letters_of_guarantee_used_view/letters_of_guarantee_used_view.component';

@NgModule({
  declarations: [
    LettersOfGuaranteetypesComponent,
    LettersOfGuaranteeLimitationsComponent,
    LettersOfGuaranteeComponent,
    LettersOfGuaranteeUsedComponent,
    LettersOfGuaranteeBalanceComponent,
    RenewalWarrantyPeriodComponent,
    ChangingGuaranteeValueComponent,
    Letters_of_guaranteetypes_viewComponent,
    Letters_of_guarantee_limitations_viewComponent,
    Letters_of_guarantee_viewComponent,
    Renewal_warranty_period_viewComponent,
    Changing_guarantee_value_viewComponent,
    Letters_of_guarantee_used_viewComponent,
    
    
  ],
  imports: [
    SharedModule,
    AccountingLettersRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingLettersModule {}
