import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-aged-payable-report',
  templateUrl: './aged-payable-report.component.html',
  styleUrls: ['./aged-payable-report.component.scss'],
  standalone: false,
})
export class AgedPayableReportComponent implements OnDestroy {
  moduleName = 'Accounting.AgedPayableReport';
  data: any[];
  gridDataSource: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  todayButton: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  financialYear: any[];
  yearName: number = 0;
  actionType: any[];
  year: any;
  fromAccId: number = 0;
  toAccId: number = 0;
  Accounts: any[];
  caseId: any;
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}AgedPayableReport`;
    this.subscription.add(
      service.getSuppliersDropdown().subscribe((r) => {
        if (r.success) {
          this.Accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getSupplierActionType().subscribe((r) => {
        if (r.success) {
          this.actionType = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();

    this.todayButton = {
      text: 'Today',
      onClick: () => {
        this.toDateValue = new Date().getTime();
      },
    };

    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    const currentYear = new Date().getFullYear();
    this.year = currentYear;
this.year = currentYear;
this.toDateValue = formatDate(
  new Date(), // تاريخ اليوم الحالي
  'yyyy-MM-dd',
  'en'
);

  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const Year = this.year;
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const FromSupplierId = this.fromAccId ?? 0;
    const ToSupplierId = this.toAccId ?? 0;
    const CaseId = this.caseId ?? 0;
    this.subscription.add(
      this.service
        .list({
          Year,
          ToDate,
          FromSupplierId,
          ToSupplierId,
          CaseId,
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
