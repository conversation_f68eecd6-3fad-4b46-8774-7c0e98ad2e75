import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-monthly-account-report',
  templateUrl: './monthly-account-report.component.html',
  styleUrls: ['./monthly-account-report.component.scss'],
  standalone: false,
})
export class MonthlyAccountReportComponent implements OnDestroy {
  moduleName = 'Accounting.MonthlyAccountReport';
  data: any[];
  gridDataSource: any[];
  editorOptions: any;
  gridBoxValue: number[] = [];
  financialYear: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  fromAccId: number = 0;
  fromAccounts: any[];
  toAccId: number = 0;
  toAccounts: any[];
  mainAccId: number = 0;
  mainAccounts: any[];
  costCenterId: number = 0;
  costCenter: any[];
  currencyId: number = 1;
  currency: any[];
  mainCustomer: any[];
  MainCustomerId: any;
  customerId: any;
  year: any;
  customers: any[];
  Accounts: any[];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}MonthlyAccount`;


    this.subscription.add(
      service.getCurrency().subscribe((r) => {
        if (r.success) {
          this.currency = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getMainChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.mainAccounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.Accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    this.subscription.add(
      service.getMainCustDropdown().subscribe((r) => {
        if (r.success) {
          this.mainCustomer = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    const currentYear = new Date().getFullYear();
    this.year = currentYear;
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const MainAccountCode = this.mainAccId?? 0;
    const FromAccountCode = this.fromAccId?? 0;
    const ToAccountCode = this.toAccId?? 0;
    const CurrencyId = this.currencyId?? 1;
    const CostID = this.costCenterId?? 0;
    const Year = this.year;

    this.subscription.add(
      this.service.list({ CostID, ToAccountCode, FromAccountCode,MainAccountCode, CurrencyId,Year }).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
