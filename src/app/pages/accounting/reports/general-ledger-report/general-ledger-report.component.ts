import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import { Service } from 'src/app/pages/general/accounting-grid-controle/accounting-grid-controle.service';

@Component({
  selector: 'app-general-ledger-report',
  templateUrl: './general-ledger-report.component.html',
  styleUrls: ['./general-ledger-report.component.scss'],
  standalone: false,
})
export class GeneralLedgerReportComponent implements OnDestroy {
  moduleName = 'Accounting.GeneralLedgerReport';
  data: any[];
  gridDataSource: any[];
  customerId: any;
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  ThirdPartyAccountId: any = 0;
  ThirdPartyAccounts: number[] = [];
  year: any;
  financialYear: any[];
  currencyId: number = 0;
  currency: any[];
  costlist: any[];
  accountcodelist: any[];
  financial_entity_TypeId: number = 0;
  financial_entity_Id: number = 0;
  accounts: any[];
  mainAccId: number = 0;
  FinancialEntities: any[];
  mainAccounts: any[];
  analyticAccountTypeId: any = 0;
  costCenterId: number = 0;
  accountCode: any = 0;
  patentCode: any = 0;
  costCenters: any[];
  includeZeroBalances: boolean = false;
  showPreviousBalance: boolean = false;
  groupByFinancialEntity: boolean = false;
  groupBySubAccount: boolean = false;
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  financial_entity_Types: { id: number; name: string }[] = [
    { id: 1, name: 'Project' },
    { id: 2, name: 'Machine' },
    { id: 3, name: 'Assets' },
    { id: 4, name: 'Department' },
    { id: 5, name: 'Supplier' },
    { id: 6, name: 'Customer' },
    { id: 7, name: 'Cars' },
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}GeneralLedger`;
    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenters = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getMainChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.mainAccounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getCurrency().subscribe((r) => {
        if (r.success) {
          this.currency = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.gridDataSource = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }
  onFinaTypeSelect(selectedItem: any) {
    this.FinancialEntities = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    // this.financial_entity_TypeId= selectedItem.id;

    this.subscription.add(
      this.service
        .getFinancialEntities(this.financial_entity_TypeId)
        .subscribe((r) => {
          if (r.success) {
            this.FinancialEntities = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }

  onItemClick(e: any) {}
  calculateSelectedRow(
    options: DxDataGridTypes.CustomSummaryInfo<any, any>
  ): void {
    if (options.name === 'SelectedRowsSummary') {
      if (options.summaryProcess === 'start') {
        options.totalValue = 5;
      }
    }
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const Year = this.year ?? new Date().getFullYear();
    const Language = 'AR';
    const CurrencyId = this.currencyId ?? 1;
    const CostList = this.costlist ?? 1;
    const AccountCodeList = this.accountcodelist ?? 1;
    const AnalyticAccountId = this.costCenterId ?? 0;
    const PatentCode = this.patentCode ?? 0;
    const IncludeZeroBalances = this.includeZeroBalances ?? false;
    const FinancialEntityTypeId = this.financial_entity_TypeId ?? 0;
    const FinancialEntityId = this.financial_entity_Id ?? 0;
    const FinancialEntityName = this.financial_entity_Id ?? 0;
    const GroupByFinancialEntity = this.groupByFinancialEntity ?? 0;
    const GroupBySubAccount = this.groupBySubAccount ?? 0;
  const AccountCode = this.accountCode ?? 0;
    this.subscription.add(
      this.service
        .list({
          FromDate,
          Year,
          ToDate,
          Language,
          PatentCode,
          CurrencyId,
          IncludeZeroBalances,
          FinancialEntityTypeId,
          FinancialEntityId,
          FinancialEntityName,
          GroupByFinancialEntity,
          GroupBySubAccount,
          CostList,
          AccountCodeList,
          AnalyticAccountId,
          AccountCode,

        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
