import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { AccountingService } from '../../accounting.service';
import { FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-ledger-report',
  templateUrl: './ledger-report.component.html',
  styleUrls: ['./ledger-report.component.scss'],
  standalone: false,
})
export class LedgerReportComponent implements OnDestroy {
  moduleName = 'Accounting.LedgerReport';
  viewListForm: FormGroup;
  actionType: any[];
  data: any[];
  year: any = 0;
  currency: any[];
  SupplierType: any[];
  suppliers: any[];
  mainAccount: any[];
  accounts: any[];
  accountId: any = 0;
  costId: any = 0;
  costGroup: any[];
  FinancialEntities: any[];
  costCenter: any[];
  accountName: any[];
  fromDateValue: any;
  toDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  AccountThirdParty: number[] = [];
  ThirdPartyAccounts: number[] = [];
  searchExpr: any;
  ThirdPartyAccountId: any = 0;
  subscription = new Subscription();
  currentFilter: any;
  financialYear: any[];
  supplier: any;
  yearName: number = 0;
  caseId: any = 0;
  branchid: any = 1;
  SupplierId: number = 0;
  currencyId: any = 1;
  branches: [] = [];
  Banks: [] = [];
  isDetails: boolean = false;
  showPreviousBalance: boolean = false;
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  financial_entity_TypeId: any = 0;
  financial_entity_Id: any = 0;
  analyticAccountTypeId: any = 0;
  financial_entity_Types: { id: number; name: string }[] = [
    { id: 1, name: 'Project' },
    { id: 2, name: 'Machine' },
    { id: 3, name: 'Assets' },
    { id: 4, name: 'Department' },
    { id: 5, name: 'Supplier' },
    { id: 6, name: 'Customer' },
    { id: 7, name: 'Cars' },
  ];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }

  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}ledger`;

    this.subscription.add(
      service.getBankActionType().subscribe((r) => {
        if (r.success) {
          this.actionType = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCurrency().subscribe((r) => {
        if (r.success) {
          this.currency = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getAccountThirdParty().subscribe((r) => {
        if (r.success) {
          this.AccountThirdParty = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCostGroups().subscribe((r) => {
        if (r.success) {
          this.costGroup = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getBankAccount().subscribe((r) => {
        if (r.success) {
          this.Banks = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onFinaTypeSelect(selectedItem: any) {
    this.FinancialEntities = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    // this.financial_entity_TypeId= selectedItem.id;

    this.subscription.add(
      this.service
        .getFinancialEntities(this.financial_entity_TypeId)
        .subscribe((r) => {
          if (r.success) {
            this.FinancialEntities = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }

  onAccountTypeSelect(selectedItem: any) {
    this.ThirdPartyAccounts = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    const Typeid = selectedItem.typId;

    this.subscription.add(
      this.service.getThirdPartyAccountId(Typeid).subscribe((r) => {
        if (r.success) {
          this.ThirdPartyAccounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('BankAccount');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('BankAccount.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const CurrencyId = this.currencyId ?? 1;
    const Year = this.year;
    const CaseID = this.caseId ?? 0;
    const IsDetails = this.isDetails;
    const ShowPreviousBalance = this.showPreviousBalance;
    const Branchid = this.branchid ?? 1;
    const Supplier = this.supplier ?? 0;
    const Language = 'AR';
    const AccountCode = this.accountId ?? 0;
    const IsTotal = this.isDetails === false ? 1 : 0;
    const AnalyticAccountId = this.costId ?? 0;
    const FinancialEntityTypeId = this.financial_entity_TypeId ?? 0;
    const FinancialEntityId = this.financial_entity_Id ?? 0;
    const AnalyticAccountTypeId = this.analyticAccountTypeId ?? 0;
    const ThirdPartyId = this.ThirdPartyAccountId ?? 0;
    const CompanyId = 0;

    this.subscription.add(
      this.service
        .list({
          Year,
          FromDate,
          ToDate,
          AccountCode,
          IsDetails,
          CaseID,
          IsTotal,
          AnalyticAccountId,
          ShowPreviousBalance,
          FinancialEntityTypeId,
          CurrencyId,
          FinancialEntityId,
          AnalyticAccountTypeId,
          ThirdPartyId,
          Branchid,
          Language,
          Supplier,
          CompanyId,
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
