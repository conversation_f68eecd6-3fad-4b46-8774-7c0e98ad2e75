import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AccountsMirrorReportComponent} from './accounts-mirror-report/accounts-mirror-report.component';
import {AgedPayableReportComponent} from './aged-payable-report/aged-payable-report.component';
import {AgedReceivableReportComponent} from './aged-receivable-report/aged-receivable-report.component';
import {BankAccountsReportComponent} from './bank-accounts-report/bank-accounts-report.component';
import {CustomerAccountsReportComponent} from './customer-accounts-report/customer-accounts-report.component';
import {GeneralLedgerReportComponent} from './general-ledger-report/general-ledger-report.component';
import {JournalEntryReportComponent} from './journal-entry-report/journal-entry-report.component';
import {LedgerReportComponent} from './ledger-report/ledger-report.component';
import {MonthlyAccountReportComponent} from './monthly-account-report/monthly-account-report.component';
import {NotPostedTransactionsComponent} from './not-posted-transactions/not-posted-transactions.component';
import {PartnersAccountsReportComponent} from './partners-accounts-report/partners-accounts-report.component';
import {PayablesAccountsReportComponent} from './payables-accounts-report/payables-accounts-report.component';
import {PaymentsAndReceiptReportComponent} from './payments-and-receipt-report/payments-and-receipt-report.component';
import {PrepaidExpensesComponent} from './prepaid-expenses/prepaid-expenses.component';
import {SalariesPrintReportComponent} from './salaries-print-report/salaries-print-report.component';
import {SalesTaxesReportComponent} from './sales-taxes-report/sales-taxes-report.component';
import {SalespersonAccountReportComponent} from './salesperson-account-report/salesperson-account-report.component';
import {StaffAccountsReportComponent} from './staff-accounts-report/staff-accounts-report.component';
import {CashBoxAccountsReportComponent} from "./cash-box-accounts-report/cash-box-accounts-report.component";


const routes: Routes = [

    {
        path: '',
        redirectTo: 'bank_accounts_report',
        pathMatch: 'full'
    },
    {
        path: 'bank_accounts_report',
        component: BankAccountsReportComponent
    },
    {
        path: 'customer_accounts_report',
        component: CustomerAccountsReportComponent
    },
    {
        path: 'payables_accounts_report',
        component: PayablesAccountsReportComponent
    },
    {
        path: 'ledger_report',
        component: LedgerReportComponent
    },
    {
        path: 'staff_accounts_report',
        component: StaffAccountsReportComponent
    },
    {
        path: 'salesperson_account_report',
        component: SalespersonAccountReportComponent
    },
    {
        path: 'cash_box_accounts_report',
        component: CashBoxAccountsReportComponent
    },
    {
        path: 'partners_accounts_report',
        component: PartnersAccountsReportComponent
    },
    {
        path: 'monthly_account_report',
        component: MonthlyAccountReportComponent
    },
    {
        path: 'salaries_print_report',
        component: SalariesPrintReportComponent
    },
    {
        path: 'accounts_mirror_report',
        component: AccountsMirrorReportComponent
    },
    {
        path: 'sales_taxes_report',
        component: SalesTaxesReportComponent
    },
    {
        path: 'journal_entry_report',
        component: JournalEntryReportComponent
    },
    {
        path: 'aged_receivable_report',
        component: AgedReceivableReportComponent
    },
    {
        path: 'aged_payable_report',
        component: AgedPayableReportComponent
    },
    {
        path: 'general_ledger_report',
        component: GeneralLedgerReportComponent
    },
    {
        path: 'not_posted_transactions',
        component: NotPostedTransactionsComponent
    },
    {
        path: 'prepaid_expenses',
        component: PrepaidExpensesComponent
    },
    {
        path: 'payments_and_receipt_report',
        component: PaymentsAndReceiptReportComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingReportsRoutingModule {
}
