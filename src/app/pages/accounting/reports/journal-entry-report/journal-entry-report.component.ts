import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
    selector: 'app-journal-entry-report',
    templateUrl: './journal-entry-report.component.html',
    styleUrls: ['./journal-entry-report.component.scss'],
    standalone: false
})
export class JournalEntryReportComponent  implements OnDestroy {
  moduleName = 'Accounting.JournalEntryReport';
  data: any[];
  gridDataSource: any[];
  year: any ;
  toDateValue: any;
  financialYear: any[]= [];
  fromDateValue: any;
  accounts: any[];
  accountId: any = 0;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  // Journals: any[];
  currencyId: number = 0;
  currency: any[];
  SupplierType: any[];
  suppliers: any[];
  to: any;
  from: any;
  journalss: boolean = false;
  unbalcedJournals: boolean = false;
  missingJournals: boolean = false;  
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(private service: AccountingService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.AccountingReports}JournalEntryReport`;
  
    this.subscription.add(service.getCurrency().subscribe(r => {
      if (r.success) {
        this.currency = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscription.add(service.getfinancialYear().subscribe(r => {
      if (r.success) {
        this.financialYear = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscription.add(
      service.getChartOfAccounts().subscribe((r) => {
        if (r.success) {
          this.accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    
    // this.subscription.add(
    //   service.getJournals().subscribe((r) => {
    //     if (r.success) {
    //       this.Journals = r.data;

    //       this.cdk.detectChanges();
    //     }
    //   })
    // );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd','en');
    const Year = this.year;
    const Journals = this.journalss;
    const UnbalcedJournals = this.unbalcedJournals;
    const MissingJournals = this.missingJournals;
    
    this.subscription.add(this.service.list({ Year, FromDate, ToDate,MissingJournals ,UnbalcedJournals, Journals  }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
