import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-aged-receivable-report',
  templateUrl: './aged-receivable-report.component.html',
  styleUrls: ['./aged-receivable-report.component.scss'],
  standalone: false,
})
export class AgedReceivableReportComponent implements OnDestroy {
  moduleName = 'Accounting.AgedReceivableReport';
  data: any[];
  gridDataSource: any[];
  caseId: number = 0;
  toDateValue: any;
  actionType: any[];
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  fromAccId: number = 0;
  toAccId: number = 0;
  year: any = 0;
  customerId: any = 0;
  financialYear: any[];
  Accounts: any[];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}AgedReceivableReport`;

    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.Accounts = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustactionType().subscribe((r) => {
        if (r.success) {
          this.actionType = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    const currentYear = new Date().getFullYear();
    this.year = currentYear;
this.year = currentYear;
this.toDateValue = formatDate(
  new Date(), // تاريخ اليوم الحالي
  'yyyy-MM-dd',
  'en'
);
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  // formatDate(date: string): string | null {
  //   if (!date) return null;
  //   const parsedDate = new Date(date);

  //   // Ensure the date is valid
  //   if (isNaN(parsedDate.getTime())) {
  //     return null;
  //   }

  //   const year = parsedDate.getFullYear();
  //   const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
  //   const day = String(parsedDate.getDate()).padStart(2, '0');

  //   return `${year}-${month}-${day}`;
  // }

  filter() {
    const Year = this.year;
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const FromCustomerID = this.fromAccId ?? 0;
    const ToCustomerID = this.toAccId ?? 0;
    const CaseId = this.caseId ?? 0;
    this.subscription.add(
      this.service
        .list({
          Year,
          ToDate,
          FromCustomerID,
          ToCustomerID,
          CaseId,
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
