import {NgModule} from '@angular/core';
import {SharedModule} from "../../shared/shared.module";
import {AccountingRoutingModule} from "../accounting-routing.module";
import {DxContextMenuModule, DxTreeViewModule} from "devextreme-angular";
import {RouterModule} from "@angular/router";
import {AccountsMirrorReportComponent} from './accounts-mirror-report/accounts-mirror-report.component';
import {AgedPayableReportComponent} from './aged-payable-report/aged-payable-report.component';
import {AgedReceivableReportComponent} from './aged-receivable-report/aged-receivable-report.component';
import {BankAccountsReportComponent} from './bank-accounts-report/bank-accounts-report.component';
import {CustomerAccountsReportComponent} from './customer-accounts-report/customer-accounts-report.component';
import {GeneralLedgerReportComponent} from './general-ledger-report/general-ledger-report.component';
import {JournalEntryReportComponent} from './journal-entry-report/journal-entry-report.component';
import {LedgerReportComponent} from './ledger-report/ledger-report.component';
import {MonthlyAccountReportComponent} from './monthly-account-report/monthly-account-report.component';
import {NotPostedTransactionsComponent} from './not-posted-transactions/not-posted-transactions.component';
import {PartnersAccountsReportComponent} from './partners-accounts-report/partners-accounts-report.component';
import {PayablesAccountsReportComponent} from './payables-accounts-report/payables-accounts-report.component';
import {PaymentsAndReceiptReportComponent} from './payments-and-receipt-report/payments-and-receipt-report.component';
import {PrepaidExpensesComponent} from './prepaid-expenses/prepaid-expenses.component';
import {SalariesPrintReportComponent} from './salaries-print-report/salaries-print-report.component';
import {SalesTaxesReportComponent} from './sales-taxes-report/sales-taxes-report.component';
import {SalespersonAccountReportComponent} from './salesperson-account-report/salesperson-account-report.component';
import {StaffAccountsReportComponent} from './staff-accounts-report/staff-accounts-report.component';
import {
    CashBoxAccountsReportComponent
} from "./cash-box-accounts-report/cash-box-accounts-report.component";
import {AccountingReportsRoutingModule} from "./accounting-reports-routing.module";


@NgModule({
    declarations: [
        AccountsMirrorReportComponent,
        AgedPayableReportComponent,
        AgedReceivableReportComponent,
        BankAccountsReportComponent,
        CashBoxAccountsReportComponent,
        CustomerAccountsReportComponent,
        GeneralLedgerReportComponent,
        JournalEntryReportComponent,
        LedgerReportComponent,
        MonthlyAccountReportComponent,
        NotPostedTransactionsComponent,
        PartnersAccountsReportComponent,
        PayablesAccountsReportComponent,
        PaymentsAndReceiptReportComponent,
        PrepaidExpensesComponent,
        SalariesPrintReportComponent,
        SalesTaxesReportComponent,
        SalespersonAccountReportComponent,
        StaffAccountsReportComponent,
    ],
    imports: [
        SharedModule,
        AccountingReportsRoutingModule,
        DxTreeViewModule,
        DxContextMenuModule,
    ],
    exports: [RouterModule],
})
export class AccountingReportsModule {
}
