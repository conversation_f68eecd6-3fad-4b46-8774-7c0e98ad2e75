import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';
@Component({
    selector: 'app-salesperson-account-report',
    templateUrl: './salesperson-account-report.component.html',
    styleUrls: ['./salesperson-account-report.component.scss'],
    standalone: false
})
export class SalespersonAccountReportComponent implements OnDestroy {
  moduleName = 'Accounting.SalespersonAccountReport';
  viewListForm: FormGroup;
  actionType: any[];
  data: any[];
  currency: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  currentFilter: any;
  financialYear: any[];
  year: number = 0;
  caseId: any = 0;
  currencyId: number = 1;
  isDetails: boolean = false;
  ShowPreviousBalance: boolean = false;
  SalesPersonid: number = 0;
  SalesPersons: any[];
  Customers: any[];
  customerId: number = 0;
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;



  constructor(private service: AccountingService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.AccountingReports}BanckAccount`;

    this.subscription.add(service.getBankActionType().subscribe(r => {
      if (r.success) {
        this.actionType = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getCurrency().subscribe(r => {
      if (r.success) {
        this.currency = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscription.add(service.getCustDropdown().subscribe(r => {
      if (r.success) {
        this.Customers = r.data;
        this.cdk.detectChanges();
      }
    }));

    
    this.subscription.add(service.getfinancialYear().subscribe(r => {
      if (r.success) {
        this.financialYear = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscription.add(service.getSalesPersons().subscribe(r => {
      if (r.success) {
        this.SalesPersons = r.data;
        this.cdk.detectChanges();
      }
    }));
    
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );

    
  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('BankAccount');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('BankAccount.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const CurrencyId = this.currencyId ?? 1;
    const IsDetails = this.isDetails;
    const Year = this.year;
    const CaseID = this.caseId ?? 0;
    const ShowPreviousBalance = this.ShowPreviousBalance;
    const CustomerId = this.customerId ?? 0;
 


    this.subscription.add(this.service.list({ FromDate, ToDate, CurrencyId, IsDetails, Year, CaseID, ShowPreviousBalance, CustomerId }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
