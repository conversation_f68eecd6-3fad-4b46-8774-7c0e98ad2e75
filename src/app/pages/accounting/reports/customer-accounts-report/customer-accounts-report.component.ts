import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from '../../../../_metronic/partials';
import { FormControl, FormGroup } from '@angular/forms';
import { formatDate } from '@angular/common';
import {
  DxDataGridModule,
  DxDataGridTypes,
} from 'devextreme-angular/ui/data-grid';

@Component({
  selector: 'app-customer-accounts-report',
  templateUrl: './customer-accounts-report.component.html',
  styleUrls: ['./customer-accounts-report.component.scss'],
  standalone: false,
})
export class CustomerAccountsReportComponent implements OnDestroy {
  moduleName = 'Accounting.CustomerAccountsReport';
  customers: any[];
  customersType: any[];
  mainCustomer: any[];
  actionType: any[];
  data: any[];
  year: any;
  caseId: any = 0;
  supplierId: any = 0;
  customerId: any = 0;
  currencyId: any = 1;
  customerCategoryID:any = 0;
  fromDateValue: any;
  toDateValue: any;
  suppliers: any[];
  currency: any[];
  editorOptions: any;
  balance = 0;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  mainCustomerId:any = 0;
  financialYear: any[];
  isDetails: boolean = false;
  showPreviousBalance: boolean = false;
  branches: [] = [];
  branchid:  any = 1;
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(
    private service: AccountingService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.AccountingReports}CustomerAccount`;

    this.subscription.add(
      service.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.customers = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getSuppliersDropdown().subscribe((r) => {
        if (r.success) {
          this.suppliers = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getMainCustDropdown().subscribe((r) => {
        if (r.success) {
          this.mainCustomer = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustTypeDropdown().subscribe((r) => {
        if (r.success) {
          this.customersType = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustactionType().subscribe((r) => {
        if (r.success) {
          this.actionType = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCurrency().subscribe((r) => {
        if (r.success) {
          this.currency = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getfinancialYear().subscribe((r) => {
        if (r.success) {
          this.financialYear = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onItemClick(e: any) {}

  calculateSelectedRow(
    options: DxDataGridTypes.CustomSummaryInfo<any, any>
  ): void {
    if (options.name === 'SelectedRowsSummary') {
      if (options.summaryProcess === 'start') {
        options.totalValue = 5;
      }
    }
    const currentYear = new Date().getFullYear();
    
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('CustomerAccount');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('CustomerAccount.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'CustomerAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const Year = this.year;
    const CaseId = this.caseId?? 0;
    const CustomerId = this.customerId?? 0;
    const CurrencyId = this.currencyId ?? 1;
    const IsDetails = this.isDetails;
    const ShowPreviousBalance = this.showPreviousBalance; 
    const MainCustomerId = this.mainCustomerId ?? 0;
    const CustomerCategoryID = this.customerCategoryID ?? 0;
    const Branchid = this.branchid ?? 0;
    const SupplierId = this.supplierId ?? 0;
    this.subscription.add(
      this.service
        .list({
          Year,
          FromDate,
          ToDate,
          CaseId,
          CustomerId,
          CurrencyId,
          IsDetails,
          ShowPreviousBalance,
          Branchid,
          MainCustomerId,
          CustomerCategoryID,
          SupplierId
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
