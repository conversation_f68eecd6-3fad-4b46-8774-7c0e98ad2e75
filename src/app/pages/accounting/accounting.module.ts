import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import {AccountingRoutingModule} from "./accounting-routing.module";
import {accountingHomeComponent} from "./home/<USER>";

@NgModule({
  declarations: [
    accountingHomeComponent,
  ],
  imports: [
    SharedModule,
    AccountingRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingModule {}
