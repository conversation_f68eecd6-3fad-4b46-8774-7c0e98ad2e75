/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Analytic_accounts_groups_viewComponent } from './analytic_accounts_groups_view.component';

describe('Analytic_accounts_groups_viewComponent', () => {
  let component: Analytic_accounts_groups_viewComponent;
  let fixture: ComponentFixture<Analytic_accounts_groups_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Analytic_accounts_groups_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Analytic_accounts_groups_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
