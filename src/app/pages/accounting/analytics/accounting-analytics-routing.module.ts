import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AnalyticAccountsDetailedComponent} from './analytic-accounts-detailed/analytic-accounts-detailed.component';
import {AnalyticAccountsGroupsComponent} from './analytic-accounts-groups/analytic-accounts-groups.component';
import {AnalyticAccountsListComponent} from './analytic-accounts-list/analytic-accounts-list.component';
import {AnalyticAccountsMonthlyComponent} from './analytic-accounts-monthly/analytic-accounts-monthly.component';
import {AnalyticAccountsPoComponent} from './analytic-accounts-po/analytic-accounts-po.component';
import {AnalyticAccountsReportComponent} from './analytic-accounts-report/analytic-accounts-report.component';
import {AnalyticAccountsRfqComponent} from './analytic-accounts-rfq/analytic-accounts-rfq.component';
import {AnalyticAccountsTotalComponent} from './analytic-accounts-total/analytic-accounts-total.component';
import {IndirectCostsSettingComponent} from './indirect-costs-setting/indirect-costs-setting.component';
import {MonthlyNetConsumerComponent} from './monthly-net-consumer/monthly-net-consumer.component';
import {TotalConsumerComponent} from './total-consumer/total-consumer.component';
import { Analytic_accounts_groups_viewComponent } from './analytic-accounts-groups/analytic_accounts_groups_view/analytic_accounts_groups_view.component';
import { Indirect_costs_setting_viewComponent } from './indirect-costs-setting/indirect_costs_setting_view/indirect_costs_setting_view.component';


const routes: Routes = [
    {
        path: '',
        redirectTo: 'analytic_accounts_list',
        pathMatch: 'full'
    },
    {
        path: 'analytic_accounts_list',
        component: AnalyticAccountsListComponent
    },
    {
        path: 'indirect_costs_setting',
        component: IndirectCostsSettingComponent
    },
    {
        path: 'analytic_accounts_groups',
        component: AnalyticAccountsGroupsComponent
    },
    {
        path: 'analytic_accounts_detailed_',
        component: AnalyticAccountsDetailedComponent
    },
    {
        path: 'analytic_accounts_total',
        component: AnalyticAccountsTotalComponent
    },
    {
        path: 'analytic_accounts_monthly',
        component: AnalyticAccountsMonthlyComponent
    },
    {
        path: 'analytic_accounts_report',
        component: AnalyticAccountsReportComponent
    },
    {
        path: 'monthly_net_consumer',
        component: MonthlyNetConsumerComponent
    },
    {
        path: 'total_consumer',
        component: TotalConsumerComponent
    },

    {
        path: 'analytic_accounts_po',
        component: AnalyticAccountsPoComponent
    },
    {
        path: 'analytic_accounts_rfq',
        component: AnalyticAccountsRfqComponent
    },

     {
            path: 'analytic_accounts_groups_view',
            component: Analytic_accounts_groups_viewComponent
        },
        {
            path: 'analytic_accounts_groups_view/:id',
            component: Analytic_accounts_groups_viewComponent
        },

     {
            path: 'indirect_costs_setting_view',
            component: Indirect_costs_setting_viewComponent
        },
        {
            path: 'indirect_costs_setting_view/:id',
            component: Indirect_costs_setting_viewComponent
        },


];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingAnalyticsRoutingModule {
}
