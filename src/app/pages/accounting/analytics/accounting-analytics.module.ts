import { NgModule } from '@angular/core';
import { AnalyticAccountsDetailedComponent } from './analytic-accounts-detailed/analytic-accounts-detailed.component';
import { AnalyticAccountsGroupsComponent } from './analytic-accounts-groups/analytic-accounts-groups.component';
import { AnalyticAccountsListComponent } from './analytic-accounts-list/analytic-accounts-list.component';
import { AnalyticAccountsMonthlyComponent } from './analytic-accounts-monthly/analytic-accounts-monthly.component';
import { AnalyticAccountsPoComponent } from './analytic-accounts-po/analytic-accounts-po.component';
import { AnalyticAccountsReportComponent } from './analytic-accounts-report/analytic-accounts-report.component';
import { AnalyticAccountsRfqComponent } from './analytic-accounts-rfq/analytic-accounts-rfq.component';
import { AnalyticAccountsTotalComponent } from './analytic-accounts-total/analytic-accounts-total.component';
import { IndirectCostsSettingComponent } from './indirect-costs-setting/indirect-costs-setting.component';
import { MonthlyNetConsumerComponent } from './monthly-net-consumer/monthly-net-consumer.component';
import { TotalConsumerComponent } from './total-consumer/total-consumer.component';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { AccountingAnalyticsRoutingModule } from './accounting-analytics-routing.module';
import { RouterModule } from '@angular/router';
import { Analytic_accounts_groups_viewComponent } from './analytic-accounts-groups/analytic_accounts_groups_view/analytic_accounts_groups_view.component';
import { Indirect_costs_setting_viewComponent } from './indirect-costs-setting/indirect_costs_setting_view/indirect_costs_setting_view.component';

@NgModule({
  declarations: [
    AnalyticAccountsListComponent,
    IndirectCostsSettingComponent,
    AnalyticAccountsGroupsComponent,
    AnalyticAccountsDetailedComponent,
    AnalyticAccountsTotalComponent,
    AnalyticAccountsMonthlyComponent,
    AnalyticAccountsReportComponent,
    MonthlyNetConsumerComponent,
    TotalConsumerComponent,
    AnalyticAccountsPoComponent,
    AnalyticAccountsRfqComponent,
    Analytic_accounts_groups_viewComponent,
    Indirect_costs_setting_viewComponent,
  ],
  imports: [
    SharedModule,
    AccountingAnalyticsRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingAnalyticsModule {}
