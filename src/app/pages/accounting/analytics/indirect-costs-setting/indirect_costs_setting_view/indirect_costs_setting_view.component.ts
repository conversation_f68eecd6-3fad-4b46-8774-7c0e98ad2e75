import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
selector: 'app-indirect_costs_setting_view',
templateUrl: './indirect_costs_setting_view.component.html',
styleUrls: ['./indirect_costs_setting_view.component.css'],
standalone:false,
})
export class Indirect_costs_setting_viewComponent implements OnInit {
moduleName = 'Accounting.IndirectCostsSetting';
customerForm: FormGroup;
subscriptions = new Subscription();
activeTab: string = 'tab1';
data: any[] = [];
newdata: FormGroup;
countries: any[];
Governoraties: any[];
AccountOperatories: any[];
companies: any[];
Nationalities: any[];
SalesPersons: any[];
_currentPage = 1;
roles: any[] = [];
selectedItemKeys: any = [];
itemsCount = 0;
editMode: boolean = false;
pagesCount = 0;
currentFilter: any;
classifications: any[] = [];
SalesPersonid: number = 0;
Salestypes: any[];
isLoading = false;
isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};
smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;
constructor(
private fb: FormBuilder,
private cdk: ChangeDetectorRef,
private myService: AccountingService,
private route: ActivatedRoute,
private router: Router,
) {


  function formatDateLocal(date: Date): string {
    const year = date.getFullYear();
    const month = (`0${date.getMonth() + 1}`).slice(-2); 
    const day = (`0${date.getDate()}`).slice(-2);        
    return `${year}-${month}-${day}`;
  }


  const today = new Date().toISOString().substring(0, 10);
  const currentYear = new Date().getFullYear();
  const firstDayOfYear = formatDateLocal(new Date(currentYear, 0, 1));   
  const lastDayOfYear = formatDateLocal(new Date(currentYear, 11, 31)); 
  const action_date = new Date().toISOString(); 

  this.newdata = this.fb.group({
    groupname: [null, Validators.required],
            

});



    }

ngOnDestroy(): void {
      this.subscriptions.unsubscribe();
    }

ngOnInit(): void {
      const id = this.route.snapshot.params.id;
      if (id) {

        this.subscriptions.add(
          this.myService.details(id).subscribe((r) => {
            if (r.success) {
              this.fillForm(r.data);
              this.roles = r.roles;
              this.cdk.detectChanges();
            }
          })
        );
      }
    }

fillForm(item: any) {
Object.keys(item).forEach((key) => {
if (this.newdata.get(key)) {
const value =
item[key] !== undefined && item[key] !== null ? item[key] : '';
this.newdata.get(key)?.setValue(value);
}
});
}


save() {
const id = this.route.snapshot.params.id;
if (id) {
if (this.newdata.valid) {
let form = this.newdata.value;
this.subscriptions.add(
this.myService
.update(id, form)
.pipe(
finalize(() => {
this.isLoading = false;
this.cdk.detectChanges();
})
)
.subscribe((r) => {
if (r.success) {
this.router.navigate(['accounting/analytics/indirect_costs_setting']);
}
})
);
} else {
this.newdata.markAllAsTouched();
}
} else {
if (this.newdata.valid) {
let form = this.newdata.value;
this.subscriptions.add(
this.myService
.create(form)
.pipe(
finalize(() => {
this.isLoading = false;
this.cdk.detectChanges();
})
)
.subscribe((r) => {
if (r.success) {
this.router.navigate(['accounting/analytics/indirect_costs_setting']);
}
})
);
} else {
this.newdata.markAllAsTouched();
}
}
}


discard() {
this.newdata.reset();
}


get currentPage() {
return this._currentPage;
}

setActiveTab(tab: string): void {
this.activeTab = tab;
}

exportToExcel() {
this.subscriptions.add(
this.myService.exportExcel().subscribe((e) => {
if (e) {
              const href = URL.createObjectURL(e);
              const link = document.createElement('a');
              link.setAttribute('download', 'IndirectCostsSetting.xlsx');
              link.href = href;
              link.click();
              URL.revokeObjectURL(href);
            }
          })
        );
      }
    
openSmsModal() {
this.smsModal.open();
}

openWhatsappModal() {
this.whatsappModal.open();
}

openEmailModal() {
this.emailModal.open();
} 

}
