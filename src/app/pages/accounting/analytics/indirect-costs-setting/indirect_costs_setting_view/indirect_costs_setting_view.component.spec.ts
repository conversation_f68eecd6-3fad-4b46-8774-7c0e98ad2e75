/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Indirect_costs_setting_viewComponent } from './indirect_costs_setting_view.component';

describe('Indirect_costs_setting_viewComponent', () => {
  let component: Indirect_costs_setting_viewComponent;
  let fixture: ComponentFixture<Indirect_costs_setting_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Indirect_costs_setting_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Indirect_costs_setting_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
