import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {DocumentaryCreditLimitsComponent} from './documentary-credit-limits/documentary-credit-limits.component';
import {DocumentaryCreditsBalanceComponent} from './documentary-credits-balance/documentary-credits-balance.component';
import {DocumentaryCreditsTypeComponent} from './documentary-credits-type/documentary-credits-type.component';
import {DocumentaryCreditsComponent} from './documentary-credits/documentary-credits.component';
import {
    RenewalAccreditationPeriodComponent
} from "./renewal-accreditation-period/renewal-accreditation-period.component";
import {ChangeCreditValueComponent} from "./change-credit-value/change-credit-value.component";
import {ChartofaccountComponent} from "../configuration/chart-of-accounts/chartofaccount/chartofaccount.component";
import { Documentary_credits_type_viewComponent } from './documentary-credits-type/documentary_credits_type_view/documentary_credits_type_view.component';
import { Documentary_credit_limits_viewComponent } from './documentary-credit-limits/documentary_credit_limits_view/documentary_credit_limits_view.component';
import { Documentary_credits_viewComponent } from './documentary-credits/documentary_credits_view/documentary_credits_view.component';
import { Documentary_credits_balance_viewComponent } from './documentary-credits-balance/documentary_credits_balance_view/documentary_credits_balance_view.component';
import { Renewal_accreditation_period_viewComponent } from './renewal-accreditation-period/renewal_accreditation_period_view/renewal_accreditation_period_view.component';
import { Change_credit_value_viewComponent } from './change-credit-value/change_credit_value_view/change_credit_value_view.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'documentary_credits_type',
        pathMatch: 'full'
    },
    {
        path: 'documentary_credits_type',
        component: DocumentaryCreditsTypeComponent
    },
    {
        path: 'documentary_credit_limits',
        component: DocumentaryCreditLimitsComponent
    },
    {
        path: 'documentary_credits',
        component: DocumentaryCreditsComponent
    },
    {
        path: 'documentary_credits_balance',
        component: DocumentaryCreditsBalanceComponent
    },
    {
        path: 'renewal_accreditation_period',
        component: RenewalAccreditationPeriodComponent
    },
    {
        path: 'change_credit_value',
        component: ChangeCreditValueComponent
    },
    {
        path: 'chartofaccount/create/:id',
        component: ChartofaccountComponent
    },
    {
        path: 'chartofaccount/edit/:id',
        component: ChartofaccountComponent
    },
       {
        path: 'documentary_credits_type_view/:id',
       component: Documentary_credits_type_viewComponent
    
        },
        {
            path: 'documentary_credits_type_view',
            component: Documentary_credits_type_viewComponent
        },
        {
            path: 'documentary_credit_limits_view/:id',
           component: Documentary_credit_limits_viewComponent
        
            },
            {
                path: 'documentary_credit_limits_view',
                component: Documentary_credit_limits_viewComponent
            },
            {
                path: 'documentary_credits_view',
               component: Documentary_credits_viewComponent
                 },
               {
                 path: 'documentary_credits_view/:id',
                component:Documentary_credits_viewComponent
               },
            {
                path: 'documentary_credits_balance_view',
               component: Documentary_credits_balance_viewComponent
                 },
               {
                 path: 'documentary_credits_balance_view/:id',
                component:Documentary_credits_balance_viewComponent
               },
            {
                path: 'renewal_accreditation_period_view',
               component: Renewal_accreditation_period_viewComponent
                 },
               {
                 path: 'renewal_accreditation_period_view/:id',
                component:Renewal_accreditation_period_viewComponent
               },  
               {   path: 'change_credit_value_view',
               component: Change_credit_value_viewComponent
                 },
               {
                 path: 'change_credit_value_view/:id',
                component:Change_credit_value_viewComponent
               },                  
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingDocumentaryRoutingModule {
}
