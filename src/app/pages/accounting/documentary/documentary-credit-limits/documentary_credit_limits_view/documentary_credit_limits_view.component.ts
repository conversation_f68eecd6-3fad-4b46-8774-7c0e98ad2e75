import {
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { DocumentaryService } from '../../Documentary.service';
@Component({
  selector: 'app-documentary_credit_limits_view',
  templateUrl: './documentary_credit_limits_view.component.html',
  styleUrls: ['./documentary_credit_limits_view.component.css'],
  standalone: false
})
export class Documentary_credit_limits_viewComponent implements OnInit {
  newdata: FormGroup;
  moduleName = 'Accounting.DocumentaryCreditLimits';
  [x: string]: any;
  subscriptions = new Subscription();
  Product: any[];
  AnalyticAccounts: any[];
  data: any[];
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
   menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };

    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
    setActiveTab(tab: string): void {
      this.activeTab=tab;
  }
  constructor(
    private service: DocumentaryService,
    private cdk: ChangeDetectorRef,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);


 
    this.newdata = this.fb.group({
      date: [today],
      letterOfGuaranteetype:[null, Validators.required],
      bank:[null, Validators.required],
      facilityValue: [null, Validators.required],
      cashInsurance:[null, Validators.required],
      notes: [null, Validators.required],
      commission: [null, Validators.required],
      cashInsurancePercentage: [null, Validators.required],


    });

    //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;

  //   this.subscription.add(
  //    service.getDepartDropdown().subscribe((r) => {
  //      if (r.success) {
  //        this.departments = r.data;
  //        this.cdk.detectChanges();
  //      }
  //    })
  //  );

  //  this.subscription.add(
  //   service.getSuppliersDropdown().subscribe((r) => {
  //     if (r.success) {
  //       this.suppliers = r.data;
  //       this.cdk.detectChanges();
  //     }
  //   })
  // );
  //  this.subscription.add(
  //    service.getCustDropdown().subscribe((r) => {
  //      if (r.success) {
  //        this.customers = r.data;
  //        this.cdk.detectChanges();
  //      }
  //    })
  //  );
  //  this.subscription.add(service.getCurrency().subscribe(r => {
  //   if (r.success) {
  //     this.currency = r.data;
  //     this.cdk.detectChanges();
  //   }
  // }));
  //  this.subscription.add(service.getWarehouses().subscribe(r => {
  //   if (r.success) {
  //     this.warehouses = r.data;
  //     this.cdk.detectChanges();
  //   }
  // }));

  }


  ngOnInit() {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.service.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }


  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'taxinvoice.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('taxinvoice.pdf');
      });
    }
    e.cancel = true;
  }

  handleSubmit($event: SubmitEvent) {
    console.log(this.customerForm.value);
    console.log($event);
  }
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/accounting/documentary/documentary_credit_limits']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          let form = this.newdata.value;
          this.subscriptions.add(
            this.service
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r) => {
                if (r.success) {
                  this.router.navigate(['/accounting/documentary/documentary_credit_limits']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }

    discard() {
      this.newdata.reset();
    }
  
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }


}
