import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { RouterModule } from '@angular/router';

import { DocumentaryCreditLimitsComponent } from './documentary-credit-limits/documentary-credit-limits.component';
import { DocumentaryCreditsBalanceComponent } from './documentary-credits-balance/documentary-credits-balance.component';
import { DocumentaryCreditsTypeComponent } from './documentary-credits-type/documentary-credits-type.component';
import { DocumentaryCreditsComponent } from './documentary-credits/documentary-credits.component';
import { RenewalAccreditationPeriodComponent } from './renewal-accreditation-period/renewal-accreditation-period.component';
import { ChangeCreditValueComponent } from './change-credit-value/change-credit-value.component';
import { AccountingDocumentaryRoutingModule } from './accounting-documentary-routing.module';
import { Documentary_credits_type_viewComponent } from './documentary-credits-type/documentary_credits_type_view/documentary_credits_type_view.component';
import { Documentary_credit_limits_viewComponent } from './documentary-credit-limits/documentary_credit_limits_view/documentary_credit_limits_view.component';
import { Documentary_credits_viewComponent } from './documentary-credits/documentary_credits_view/documentary_credits_view.component';
import { Documentary_credits_balance_viewComponent } from './documentary-credits-balance/documentary_credits_balance_view/documentary_credits_balance_view.component';
import { Renewal_accreditation_period_viewComponent } from './renewal-accreditation-period/renewal_accreditation_period_view/renewal_accreditation_period_view.component';
import { Change_credit_value_viewComponent } from './change-credit-value/change_credit_value_view/change_credit_value_view.component';

@NgModule({
  declarations: [
    DocumentaryCreditsTypeComponent,
    DocumentaryCreditLimitsComponent,
    DocumentaryCreditsComponent,
    DocumentaryCreditsBalanceComponent,
    RenewalAccreditationPeriodComponent,
    ChangeCreditValueComponent,
    Documentary_credits_type_viewComponent,
    Documentary_credit_limits_viewComponent,
    Documentary_credits_viewComponent,
    Documentary_credits_balance_viewComponent,
    Renewal_accreditation_period_viewComponent,
    Change_credit_value_viewComponent

  ],
  imports: [
    SharedModule,
    AccountingDocumentaryRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingDocumentaryModule {}
