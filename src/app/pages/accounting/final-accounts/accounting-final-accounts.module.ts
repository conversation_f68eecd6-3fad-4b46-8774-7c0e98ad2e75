import {NgModule} from '@angular/core';
import {SharedModule} from "../../shared/shared.module";
import {AccountingRoutingModule} from "../accounting-routing.module";
import {DxContextMenuModule, DxTreeViewModule} from "devextreme-angular";
import {RouterModule} from "@angular/router";

import {BalanceSheetReportComponent} from './balance-sheet-report/balance-sheet-report.component';
import {CashFlowsReportComponent} from './cash-flows-report/cash-flows-report.component';
import {EquityChangesReportComponent} from './equity-changes-report/equity-changes-report.component';
import {
  EstimatedBudgetDeviationReportComponent
} from './estimated-budget-deviation-report/estimated-budget-deviation-report.component';
import {IncomeStatementReportComponent} from './income-statement-report/income-statement-report.component';
import {RevenuesAnalysisReportComponent} from './revenues-analysis-report/revenues-analysis-report.component';
import {TrialBalanceLevelsReportComponent} from './trial-balance-levels-report/trial-balance-levels-report.component';
import {TrialBalanceReportComponent} from './trial-balance-report/trial-balance-report.component';
import {
  CustomFinancialStatementsReportComponent
} from "../reports/custom-financial-statements-report/custom-financial-statements-report.component";
import {AccountingFinalAccountsRoutingModule} from "./accounting-final-accounts-routing.module";


@NgModule({
    declarations: [
        BalanceSheetReportComponent,
        CashFlowsReportComponent,
        CustomFinancialStatementsReportComponent,
        EquityChangesReportComponent,
        EstimatedBudgetDeviationReportComponent,
        IncomeStatementReportComponent,
        RevenuesAnalysisReportComponent,
        TrialBalanceLevelsReportComponent,
        TrialBalanceReportComponent,
    ],
    imports: [
        SharedModule,
        AccountingFinalAccountsRoutingModule,
        DxTreeViewModule,
        DxContextMenuModule,
    ],
    exports: [RouterModule],
})
export class AccountingFinalAccountsModule {
}
