import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BalanceSheetReportComponent} from './balance-sheet-report/balance-sheet-report.component';
import {CashFlowsReportComponent} from './cash-flows-report/cash-flows-report.component';
import {EquityChangesReportComponent} from './equity-changes-report/equity-changes-report.component';
import {
    EstimatedBudgetDeviationReportComponent
} from './estimated-budget-deviation-report/estimated-budget-deviation-report.component';
import {IncomeStatementReportComponent} from './income-statement-report/income-statement-report.component';
import {RevenuesAnalysisReportComponent} from './revenues-analysis-report/revenues-analysis-report.component';
import {TrialBalanceLevelsReportComponent} from './trial-balance-levels-report/trial-balance-levels-report.component';
import {TrialBalanceReportComponent} from './trial-balance-report/trial-balance-report.component';
import {
    CustomFinancialStatementsReportComponent
} from "../reports/custom-financial-statements-report/custom-financial-statements-report.component";


const routes: Routes = [
    {
        path: '',
        redirectTo: 'trial_balance_report',
        pathMatch: 'full'
    },
    {
        path: 'trial_balance_report',
        component: TrialBalanceReportComponent
    },
    {
        path: 'trial_balance_levels_report',
        component: TrialBalanceLevelsReportComponent
    },
    {
        path: 'revenues_analysis_report',
        component: RevenuesAnalysisReportComponent
    },
    {
        path: 'income_statement_report',
        component: IncomeStatementReportComponent
    },
    {
        path: 'balance_sheet_report',
        component: BalanceSheetReportComponent
    },
    {
        path: 'equity_changes_report',
        component: EquityChangesReportComponent
    },
    {
        path: 'custom_financial_statements_report',
        component: CustomFinancialStatementsReportComponent
    },
    {
        path: 'estimated_budget_deviation_report',
        component: EstimatedBudgetDeviationReportComponent
    },
    {
        path: 'cash_flows_report',
        component: CashFlowsReportComponent
    },

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingFinalAccountsRoutingModule {
}
