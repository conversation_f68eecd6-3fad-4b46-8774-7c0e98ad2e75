<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.TrialBalanceReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>

        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.TrialBalanceReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <div class="main-inputs">
    <div class="row">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
        <input
          id="fromDate"
          type="date"
          [(ngModel)]="fromDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>


    </div>

    <div class="row">

            <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="financialYear" class="form-label">
          {{ "COMMON.Year" | translate }}
        </label>
        <ng-select
          id="financialYear"
          formControlName="financialYear"
          bindLabel="yearName"
          bindValue="yearName"
          [items]="financialYear"
        ></ng-select>
      </div>

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="currencyid" class="form-label">
          {{ "COMMON.Currency" | translate }}
        </label>
        <ng-select
          id="currencyid"
          formControlName="currencyid"
          bindLabel="nameAr"
          bindValue="id"
          [items]="currency"
        ></ng-select>
      </div>

    </div>

    <div class="row">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="costCenterGroupId" class="form-label">
          {{ "COMMON.costCenterGroup" | translate }}
        </label>
        <ng-select
          id="costCenterGroupId"
          formControlName="costCenterGroupId"
          bindLabel="name"
          bindValue="id"
          [items]="costCenterGroup"
        ></ng-select>
      </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="costCenterId" class="form-label">
              {{ "COMMON.CostCenter" | translate }}
            </label>
            <ng-select
              id="costCenterId"
              formControlName="costCenterId"
              bindLabel="nameAr"
              bindValue="code"
              [items]="costCenters"
            ></ng-select>
          </div>


    </div>



    <div class="row">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="branchid" class="form-label">
          {{ "COMMON.Branch" | translate }}
        </label>
        <ng-select
          id="branchid"
          formControlName="branchid"
          bindLabel="name"
          bindValue="id"
          [items]="branches"
        ></ng-select>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">
          {{ "COMMON.ShowZeroBalance" | translate }}
        </div>

        <dx-check-box
          [value]="showZeroBalance"
          valueExpr="showZeroBalance"
        ></dx-check-box>
      </div>

    </div>


  </div>


</div>



<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="AccCode"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="AccCode"
    caption="{{ 'COMMON.AccCode' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="AccName"
    caption="{{ 'COMMON.AccName' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BalanceDaen"
    caption="{{ 'COMMON.BalanceCredit' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BeforDaen"
    caption="{{ 'COMMON.BeforDaen' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BeforMaden"
    caption="{{ 'COMMON.BeforMaden' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BrevBalanceDaen"
    caption="{{ 'COMMON.BrevBalanceDaen' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BrevBalanceMaden"
    caption="{{ 'COMMON.BrevBalanceMaden' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BrevTotalDaen"
    caption="{{ 'COMMON.BrevTotalDaen' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="BrevTotalMaden"
    caption="{{ 'COMMON.BrevTotalMaden' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="ISmain"
    caption="{{ 'COMMON.ISmain' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="SortID"
    caption="{{ 'COMMON.SortID' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="StartDaen"
    caption="{{ 'COMMON.StartDaen' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="StartMaden"
    caption="{{ 'COMMON.StartMaden' | translate }}"
  ></dxi-column>
  
  <dxi-column
    dataField="SumDaen"
    caption="{{ 'COMMON.SumDaen' | translate }}"
  ></dxi-column>

  <dxi-column
    dataField="SumMaden"
    caption="{{ 'COMMON.SumMaden' | translate }}"
  ></dxi-column>

    <dxi-column
    dataField="balanceMaden"
    caption="{{ 'COMMON.balanceMaden' | translate }}"
  ></dxi-column>

    <dxi-column
    dataField="totalDaen"
    caption="{{ 'COMMON.totalDaen' | translate }}"
  ></dxi-column>

    <dxi-column
    dataField="totalMaden"
    caption="{{ 'COMMON.totalMaden' | translate }}"
  ></dxi-column>


  <!--<dxi-column dataField="name">
  <dxo-header-filter>-->
  <!--<dxo-search [enabled]="true"-->
  <!--[searchExpr]="searchExpr"
  [editorOptions]="editorOptions"></dxo-search>-->
  <!--</dxo-header-filter>
  </dxi-column>-->
  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="maden" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>

