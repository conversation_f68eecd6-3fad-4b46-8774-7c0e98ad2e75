import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { FormGroup } from '@angular/forms';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-trial-balance-report',
  templateUrl: './trial-balance-report.component.html',
  styleUrls: ['./trial-balance-report.component.scss'],
  standalone: false,
})
export class TrialBalanceReportComponent implements OnDestroy {
moduleName = 'Accounting.TrialBalanceReport';
actionType: any[];
data: any[];
  year: any ;
  currency: any[];
  supplier: any[];
  fromDateValue: any;
  toDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  currentFilter: any;
  financialYear: any[]= [];
  costCenterId: number = 0;
  costCenters: any[];
  branchid: any = 1;
  costCenterGroupId: number = 0;
  costCenterGroup: any[];
  bankid: any;
  currencyId: any = 1;
  branches: [] = [];
  Banks: [] = [];
  loadingVisible = false;
  isDetails: boolean = false;
  showZeroBalance: boolean = false;
  bankAccountId: number = 0;
  actionsList: string[] = ["Export", "Send via SMS", "Send Via Email", "Send Via Whatsapp"];
  modalConfig: ModalConfig = {modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {return true},
    dismissButtonLabel: 'Cancel'
  };
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  smsModalConfig: ModalConfig = {...this.modalConfig, modalTitle: 'Send Sms'};
  emailModalConfig: ModalConfig = {...this.modalConfig, modalTitle: 'Send Email'};
  whatsappModalConfig: ModalConfig = {...this.modalConfig, modalTitle: 'Send Whatsapp'};
  @ViewChild('smsModal') private smsModal: ModalComponent
  @ViewChild('emailModal') private emailModal: ModalComponent
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent
  constructor(private service: AccountingService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.AccountingReports}TrialBalance`;
    this.subscription.add(service.getBankActionType().subscribe(r => {
      if (r.success) {
        this.actionType = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getCurrency().subscribe(r => {
      if (r.success) {
        this.currency = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscription.add(service.getbranches().subscribe(r => {
      if (r.success) {
        this.branches = r.data;
        this.cdk.detectChanges();

      }
    }));

this.subscription.add(
  service.getCostCenters().subscribe((r) => {
    if (r.success) {
      this.costCenters = r.data;
      this.cdk.detectChanges();
    }
  })
);

    this.subscription.add(service.getfinancialYear().subscribe(r => {
      if (r.success) {
        this.financialYear = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );

  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }


  ngOnInit(): void {


  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('BankAccount');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('BankAccount.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'BankAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  filter() {

    this.loadingVisible = true;
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const Year = this.year;
    const CurrencyId = this.currencyId;
    const CostGroupID  = this.costCenterGroupId;
    const ShowZeroBalance = this.showZeroBalance;
    const Branchid = this.branchid ?? 1;
    const CostID = this.costCenterId ??0;
    const Language = "AR";


    this.subscription.add(this.service.list({  

      FromDate,
      ToDate,
      CurrencyId,
      Language,
      ShowZeroBalance,
      Year,
      CostID,
      Branchid,
      CostGroupID,



        }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.loadingVisible = false;
  }

  openSmsModal() {
    this.smsModal.open()
  }
  openWhatsappModal() {
    this.whatsappModal.open()
  }
  openEmailModal() {
    this.emailModal.open()
  }
}
