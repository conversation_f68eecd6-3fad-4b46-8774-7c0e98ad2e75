<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.Loans" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Accounting.Loans',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.EXPDate" | translate }}</label>
            <input
              id="eXPDate"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>


        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.StatementDate" | translate }}</label>
            <input
              id="statementDate"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="duration" class="form-label">
              {{ "COMMON.Duration" | translate }}
            </label>
            <input
              type="text"
              id="duration"
              name="duration"
              class="form-control"
              formControlName="duration"
            />
          </div>
          
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="loanType" class="form-label">
              {{ "COMMON.LoanType" | translate }}
            </label>
            <ng-select
              id="loanType"
              formControlName="loanType"
              bindLabel="name"
              bindValue="id"
              [items]="loanTypeTypes"
            ></ng-select>
          </div>


          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="bank" class="form-label">
              {{ "COMMON.Bank" | translate }}
            </label>
            <ng-select
              id="bank"
              formControlName="bank"
              bindLabel="name"
              bindValue="id"
              [items]="banks"
            ></ng-select>
          </div>
        </div>

         <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="statementNumber" class="form-label">
              {{ "COMMON.StatementNumber" | translate }}
            </label>
            <input
              type="text"
              id="statementNumber"
              name="statementNumber"
              class="form-control"
              formControlName="statementNumber"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="statementValue" class="form-label">
              {{ "COMMON.StatementValue" | translate }}
            </label>
            <input
              type="text"
              id="statementValue"
              name="statementValue"
              class="form-control"
              formControlName="statementValue"
            />
          </div>

         </div>



        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.OtherInfo" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation">  
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.BorrowingCost" | translate }}
            </button>
            </li>
          </ul>
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="loanNumber" class="form-label">
                {{ "COMMON.LoanNumber" | translate }}
              </label>
              <input
                type="text"
                id="loanNumber"
                name="loanNumber"
                class="form-control"
                formControlName="loanNumber"
              />
            </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="loanValue" class="form-label">
                {{ "COMMON.LoanValue" | translate }}
              </label>
              <input
                type="text"
                id="loanValue"
                name="loanValue"
                class="form-control"
                formControlName="loanValue"
              />
            </div>
  
    
          </div>
          <div class="row">
                    <div class="form-group col-xl-4 col-md-6 col-sm-12">
                      <label for="duratiFinancingPercentageon" class="form-label">
                        {{ "COMMON.DuratiFinancingPercentageon" | translate }}
                      </label>
                      <input
                        type="text"
                        id="duratiFinancingPercentageon"
                        name="duratiFinancingPercentageon"
                        class="form-control"
                        formControlName="duratiFinancingPercentageon"
                      />
                    </div>
                  <div class="form-group col-xl-4 col-md-6 col-sm-12">
                    <label for="tax" class="form-label">
                        {{ "COMMON.TaxValue" | translate }}
                      </label>
                      <input
                        type="text"
                        id="tax"
                        name="tax"
                        class="form-control"
                        formControlName="tax"
                      />
            </div>
  
    
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="costId" class="form-label">
                {{ "COMMON.CostName" | translate }}
              </label>
              <ng-select
                id="costId"
                formControlName="costId"
                bindLabel="nameAr"
                bindValue="code"
                [items]="costCenter"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="transaction" class="form-label">
                {{ "COMMON.Transaction" | translate }}
              </label>
              <input
                type="text"
                id="transaction"
                name="transaction"
                class="form-control"
                formControlName="transaction"
              />
            </div>
  
            
          </div>
          <div class="row">

            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="supplier" class="form-label">
                {{ "COMMON.Supplier" | translate }}
              </label>
              <ng-select
                id="supplier"
                formControlName="supplier"
                bindLabel="nameAr"
                bindValue="id"
                [items]="suppliers"
              ></ng-select>
            </div>
            
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="currencyid" class="form-label">
                {{ "COMMON.Currency" | translate }}
              </label>
              <ng-select
                id="currencyid"
                formControlName="currencyid"
                bindLabel="nameAr"
                bindValue="id"
                [items]="currency"
              ></ng-select>
            </div>
          </div>
        <div class="row">
      <div class="form-group col-xl-4 col-md-6 col-sm-12">
        <label for="commission" class="form-label">
          {{ "COMMON.Commission" | translate }}
        </label>
        <input
          type="text"
          id="commission"
          name="commission"
          class="form-control"
          formControlName="commission"
        />
      </div>
      <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
        <label for="journal" class="form-label">
            {{ "COMMON.JournalName" | translate }}
        </label>
        <ng-select
                id="journal"
                formControlName="journal"
                bindLabel="name"
                bindValue="id"
                [items]="Journals"></ng-select>
    </div>

       </div>
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="project" class="form-label">
            {{ "COMMON.Project" | translate }}
          </label>
          <ng-select
            id="project"
            formControlName="project"
            bindLabel="name"
            bindValue="id"
            [items]="projects"
          ></ng-select>
        </div>
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="notes" class="form-label">
            {{ "COMMON.Notes" | translate }}
          </label>
          <textarea
            id="notes"
            name="notes"
            class="form-control"
            formControlName="notes"
            rows="2" 
          ></textarea>
        </div>
      </div>
            </div>
          </div>
          <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab2'"
          [class.active]="activeTab === 'tab2'"
          *ngIf="activeTab === 'tab2'"
        >
   



         </div>
        </div>

    </div>
  </div>
  </div>

</form>