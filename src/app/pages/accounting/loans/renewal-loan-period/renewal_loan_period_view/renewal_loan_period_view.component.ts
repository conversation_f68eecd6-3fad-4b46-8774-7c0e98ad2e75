import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { LoansService } from '../../Loans.service';
import { ActivatedRoute, Router } from '@angular/router';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { Workbook } from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import jsPDF from 'jspdf';

@Component({
  selector: 'app-renewal_loan_period_view',
  templateUrl: './renewal_loan_period_view.component.html',
  styleUrls: ['./renewal_loan_period_view.component.css'],
  standalone:false,
})
export class Renewal_loan_period_viewComponent implements OnInit {
newdata: FormGroup;
moduleName = 'Accounting.RenewalLoanPeriod';
[x: string]: any;
subscriptions = new Subscription();
Product: any[];
AnalyticAccounts: any[];
data: any[];
isGridBoxOpened: boolean;
editorOptions: any;
gridBoxValue: number[] = [1];
subscription = new Subscription();
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};

smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;
setActiveTab(tab: string): void {
this.activeTab=tab;
}
constructor(
private service: LoansService,
private cdk: ChangeDetectorRef,
private fb: FormBuilder,
private route: ActivatedRoute,
private router: Router
) {
const today = new Date().toISOString().slice(0, 10);


this.newdata = this.fb.group({
date: [today],
letterofCreditType:[null, Validators.required],
bank:[null, Validators.required],
letterofCreditNumber: [null, Validators.required],
cashInsurance:[null, Validators.required],
notes: [null, Validators.required],
commission: [null, Validators.required],
transaction: [null, Validators.required],
cashInsurancePercentage: [null, Validators.required],
journal: [null, Validators.required],
currencyid: [null, Validators.required],
loannumberid: [null, Validators.required],
supplier: [null, Validators.required],
costId: [null, Validators.required],
letterofCreditValue: [null, Validators.required],

});

this.subscription.add(service.getCurrency().subscribe(r => {
  if (r.success) {
    this.currency = r.data;
    this.cdk.detectChanges();
  }
}));

this.subscription.add(
  service.getCostCenters().subscribe((r) => {
    if (r.success) {
      this.costCenter = r.data;
      this.cdk.detectChanges();
    }
  })
);

this.subscription.add(
  service.getJournals().subscribe((r) => {
    if (r.success) {
      this.Journals = r.data;

      this.cdk.detectChanges();
    }
  })
);



  }


ngOnInit() {
const id = this.route.snapshot.params.id;
if (id) {
this.subscriptions.add(
this.service.details(id).subscribe((r) => {
if (r.success) {
this.fillForm(r.data);
this.roles = r.roles;
this.cdk.detectChanges();
}
})
);
}
}


onItemClick(e: any) {}

ngOnDestroy(): void {
this.subscription.unsubscribe();
}

onExporting(e: any) {
console.log(e);
const workbook = new Workbook();
const worksheet = workbook.addWorksheet('RenewalLoanPeriod');
if (e.format == 'excel') {
exportDataGrid({
component: e.component,
worksheet,
autoFilterEnabled: true,
}).then(() => {
workbook.xlsx.writeBuffer().then((buffer: any) => {
saveAs(
new Blob([buffer], { type: 'application/octet-stream' }),
'RenewalLoanPeriod.xlsx'
);
});
});
} else if (e.format == 'pdf') {
const doc = new jsPDF();
pdfGrid({
jsPDFDocument: doc,
component: e.component,
indent: 5,
}).then(() => {
doc.save('RenewalLoanPeriod.pdf');
});
}
e.cancel = true;
}

handleSubmit($event: SubmitEvent) {
console.log(this.customerForm.value);
console.log($event);
}
save() {
const id = this.route.snapshot.params.id;
if (id) {
if (this.newdata.valid) {
let form = this.newdata.value;
this.subscriptions.add(
this.service
.update(id, form)
.pipe(
finalize(() => {
this.isLoading = false;
this.cdk.detectChanges();
})
)
.subscribe((r) => {
if (r.success) {
this.router.navigate(['/accounting/loans/renewal_loan_period']);
}
})
);
} else {
this.newdata.markAllAsTouched();
}
} else {
if (this.newdata.valid) {
let form = this.newdata.value;
this.subscriptions.add(
this.service
.create(form)
.pipe(
finalize(() => {
this.isLoading = false;
this.cdk.detectChanges();
})
)
.subscribe((r) => {
if (r.success) {
this.router.navigate(['/accounting/loans/renewal_loan_period']);
}
})
);
} else {
this.newdata.markAllAsTouched();
}
}
}

discard() {
this.newdata.reset();
}

exportToExcel() {
this.subscription.add(
this.service.exportExcel().subscribe((e) => {
if (e) {
const href = URL.createObjectURL(e);
const link = document.createElement('a');
link.setAttribute('download', 'RenewalLoanPeriod.xlsx');
link.href = href;
link.click();
URL.revokeObjectURL(href);
}
})
);
}

openSmsModal() {
this.smsModal.open();
}

openWhatsappModal() {
this.whatsappModal.open();
}

openEmailModal() {
this.emailModal.open();
}


}

