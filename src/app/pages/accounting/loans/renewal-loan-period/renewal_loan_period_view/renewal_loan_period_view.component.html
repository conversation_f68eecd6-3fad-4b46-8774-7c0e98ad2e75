<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.RenewalLoanPeriod" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Accounting.DocumentaryCreditLimits',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.EXPDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>


        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="costId" class="form-label">
              {{ "COMMON.CostName" | translate }}
            </label>
            <ng-select
              id="costId"
              [(ngModel)]="costId"
              bindLabel="nameAr"
              bindValue="code"
              [items]="costCenter"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="currencyid" class="form-label">
              {{ "COMMON.Currency" | translate }}
            </label>
            <ng-select
              id="currencyid"
              formControlName="currencyid"
              bindLabel="nameAr"
              bindValue="id"
              [items]="currency"
            ></ng-select>
          </div>


          
        </div>
        
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="commission" class="form-label">
            {{ "COMMON.Commission" | translate }}
          </label>
          <input
            type="text"
            id="commission"
            name="commission"
            class="form-control"
            formControlName="commission"
          />
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
          <label for="journal" class="form-label">
              {{ "COMMON.JournalName" | translate }}
          </label>
          <ng-select
                  id="journal"
                  formControlName="journal"
                  bindLabel="name"
                  bindValue="id"
                  [items]="Journals"></ng-select>
      </div>

      </div>


  <div class="row">

    <div class="form-group col-xl-4 col-md-6 col-sm-12">
      <label for="loannumberid" class="form-label">
        {{ "COMMON.LoanNumber" | translate }}
      </label>
      <ng-select
        id="loannumberid"
        formControlName="loannumberid"
        bindLabel="nameAr"
        bindValue="id"
        [items]="loannumbers"
      ></ng-select>
    </div>

    <div class="form-group col-xl-4 col-md-6 col-sm-12">
      <label for="notes" class="form-label">
        {{ "COMMON.Notes" | translate }}
      </label>
      <textarea
        id="notes"
        name="notes"
        class="form-control"
        formControlName="notes"
        rows="2" 
      ></textarea>
    </div>
  </div>


    </div>
  </div>
  </div>

</form>