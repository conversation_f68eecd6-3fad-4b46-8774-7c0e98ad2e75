import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoanBalancesComponent } from './loan-balances/loan-balances.component';
import { LoanLimitsComponent } from './loan-limits/loan-limits.component';
import { LoanTypesComponent } from './loan-types/loan-types.component';
import { LoansRepaymentComponent } from './loans-repayment/loans-repayment.component';
import { LoansComponent } from './loans/loans.component';
import { RenewalLoanPeriodComponent } from './renewal-loan-period/renewal-loan-period.component';
import { Loan_types_viewComponent } from './loan-types/loan_types_view/loan_types_view.component';
import { Loan_limits_viewComponent } from './loan-limits/loan_limits_view/loan_limits_view.component';
import { Loans_viewComponent } from './loans/loans_view/loans_view.component';
import { Renewal_loan_period_viewComponent } from './renewal-loan-period/renewal_loan_period_view/renewal_loan_period_view.component';
import { LoansRepayment_viewComponent } from './loans-repayment/loans-repayment_view/loans-repayment_view.component';
import { Loan_balances_viewComponent } from './loan-balances/loan_balances_view/loan_balances_view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'loan_types',
    pathMatch: 'full',
  },
  {
    path: 'loan_types',
    component: LoanTypesComponent,
  },
  {
    path: 'loan_limits',
    component: LoanLimitsComponent,
  },
  {
    path: 'loans',
    component: LoansComponent,
  },
  {
    path: 'renewal_loan_period',
    component: RenewalLoanPeriodComponent,
  },
  {
    path: 'loans_repayment',
    component: LoansRepaymentComponent,
  },
  {
    path: 'loan_balances',
    component: LoanBalancesComponent,
  },
  {
    path: 'loan_types_view',
    component: Loan_types_viewComponent,
  },
  {
    path: 'loan_types_view/:id',
    component: Loan_limits_viewComponent,
  },
  {
    path: 'loan_limits_view/:id',
    component: Loan_types_viewComponent,
  },   
  {
    path: 'loan_limits_view',
    component: Loan_limits_viewComponent,
  },
  {
    path: 'loans_view/:id',
    component: Loans_viewComponent,
  },   
  {
    path: 'loans_view',
    component: Loans_viewComponent,
  },

  {
    path: 'renewal_loan_period_view/:id',
    component: Renewal_loan_period_viewComponent,
  },   
  {
    path: 'renewal_loan_period_view',
    component: Renewal_loan_period_viewComponent,
  },

   {
    path: 'loans-repayment_view/:id',
    component: LoansRepayment_viewComponent,
  },   
  {
    path: 'loans-repayment_view',
    component: LoansRepayment_viewComponent,
  }, 
  {
    path: 'loan_balances_view/:id',
    component: Loan_balances_viewComponent,
  },   
  {
    path: 'loan_balances_view',
    component: Loan_balances_viewComponent,
  }, 

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountingLoanRoutingModule {}
