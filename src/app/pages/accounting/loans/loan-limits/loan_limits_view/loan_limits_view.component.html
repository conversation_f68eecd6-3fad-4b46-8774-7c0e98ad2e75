<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.LoanLimits" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Accounting.LoanLimits',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>


        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="loanType" class="form-label">
              {{ "COMMON.LoanType" | translate }}
            </label>
            <ng-select
              id="loanType"
              formControlName="loanType"
              bindLabel="name"
              bindValue="id"
              [items]="loanTypes"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="bankAccountId" class="form-label">
              {{ "COMMON.Account" | translate }}
            </label>
            <ng-select
              id="bankAccountId"
              [(ngModel)]="bankAccountId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="Banks"
            ></ng-select>
          </div>

        </div>

        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="facilityValue" class="form-label">
              {{ "COMMON.FacilityValue" | translate }}
            </label>
            <input
              type="text"
              id="facilityValue"
              name="facilityValue"
              class="form-control"
              formControlName="facilityValue"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="commission" class="form-label">
              {{ "COMMON.Commission" | translate }}
            </label>
            <input
              type="text"
              id="commission"
              name="commission"
              class="form-control"
              formControlName="commission"
            />
          </div>

          
        </div>

      <div class="row">
        <div class="form-group col-xl-8 col-md-6 col-sm-12">
          <label for="notes" class="form-label">
            {{ "COMMON.Notes" | translate }}
          </label>
          <textarea
            id="notes"
            name="notes"
            class="form-control"
            formControlName="notes"
            rows="2" 
          ></textarea>
        </div>
      </div>



    </div>
  </div>
  </div>

</form>
