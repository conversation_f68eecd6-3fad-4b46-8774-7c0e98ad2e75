import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { RouterModule } from '@angular/router';
import { LoanBalancesComponent } from './loan-balances/loan-balances.component';
import { LoanLimitsComponent } from './loan-limits/loan-limits.component';
import { LoanTypesComponent } from './loan-types/loan-types.component';
import { LoansRepaymentComponent } from './loans-repayment/loans-repayment.component';
import { LoansComponent } from './loans/loans.component';
import { RenewalLoanPeriodComponent } from './renewal-loan-period/renewal-loan-period.component';
import { AccountingLoanRoutingModule } from './accounting-loans-routing.module';
import { Loan_types_viewComponent } from './loan-types/loan_types_view/loan_types_view.component';
import { Loan_limits_viewComponent } from './loan-limits/loan_limits_view/loan_limits_view.component';
import { Loans_viewComponent } from './loans/loans_view/loans_view.component';
import { Renewal_loan_period_viewComponent } from './renewal-loan-period/renewal_loan_period_view/renewal_loan_period_view.component';
import { LoansRepayment_viewComponent } from './loans-repayment/loans-repayment_view/loans-repayment_view.component';
import { Loan_balances_viewComponent } from './loan-balances/loan_balances_view/loan_balances_view.component';

@NgModule({
  declarations: [
    LoanTypesComponent,
    LoanLimitsComponent,
    LoansComponent,
    RenewalLoanPeriodComponent,
    LoansRepaymentComponent,
    LoanBalancesComponent,
    Loan_types_viewComponent,
    Loan_limits_viewComponent,
    Loans_viewComponent,
    Renewal_loan_period_viewComponent,
    LoansRepayment_viewComponent,
    Loan_balances_viewComponent
  ],
  imports: [
    SharedModule,
    AccountingLoanRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
  ],
  exports: [RouterModule],
})
export class AccountingLoansModule {}
