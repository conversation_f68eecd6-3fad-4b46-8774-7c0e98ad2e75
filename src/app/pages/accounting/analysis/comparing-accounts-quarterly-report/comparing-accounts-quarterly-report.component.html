
<div class="card-body border-top">

  
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">Comparing Accounts quarterly Report</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
         
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.ComparingAccountsquarterlyReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>



  <div class="main-inputs">
    <div class="row">
      <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <label for="SupplierType" class="form-label">
          {{ "COMMON.AccName" | translate }}
        </label>
        <ng-select
          id="SupplierType"
          formControlName="SupplierType"
          bindLabel="name"
          bindValue="id"
          [items]="suppliers"
        ></ng-select>
      </div> 

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <label for="currencyid" class="form-label">
          {{ "COMMON.Currency" | translate }}
        </label>
        <ng-select
          id="currencyid"
          formControlName="currencyid"
          bindLabel="nameAr"
          bindValue="id"
          [items]="currency"
        ></ng-select>
      </div>
    </div>

    <!-- <div class="row">
      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <a (click)="filter()" class="btn btn-primary m-1 btn-sm">{{
          "COMMON.View" | translate
        }}</a>
      </div>

      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <a
          href="#"
          class="btn btn-primary btn-sm"
          data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-end"
        >
          {{ "COMMON.Print" | translate }}
          <span class="svg-icon svg-icon-5 m-0">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                fill="currentColor"
              />
            </svg>
          </span>
        </a>

        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          data-kt-menu="true"
        >
          <div
            class="menu-item px-3"
            *hasPermission="{ action: 'print', module: moduleName }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >Print</a
            >
          </div>
          <div
            class="menu-item px-3"
            *hasPermission="{ action: 'print', module: moduleName }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.Print" | translate }}
            </a>
          </div>
        </div>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <a
          href="#"
          class="btn btn-primary btn-sm"
          data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-end"
        >
          {{ "COMMON.Actions" | translate }}
          <span class="svg-icon svg-icon-5 m-0">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                fill="currentColor"
              />
            </svg>
          </span>
        </a>

        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          data-kt-menu="true"
        >
          <div
            class="menu-item px-3"
            *hasPermission="{ action: 'print', module: moduleName }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >Print</a
            >
          </div>
          <div
            class="menu-item px-3"
            *hasPermission="{ action: 'print', module: moduleName }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>
        </div>
      </div>
    </div> -->

  </div>
</div>



<dx-data-grid
id="gridcontrole"
[rtlEnabled]="true"
[dataSource]="data"
keyExpr="invno_ormony_id"
[showRowLines]="true"
[showBorders]="true"
[columnAutoWidth]="true"
(onExporting)="onExporting($event)"
[allowColumnResizing]="true"
>
<dxo-filter-row
  [visible]="true"
  [applyFilter]="currentFilter"
></dxo-filter-row>

<dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
<dxo-paging [pageSize]="10"> </dxo-paging>
<dxo-pager
  [visible]="true"
  [allowedPageSizes]="[5, 10, 'all']"
  [displayMode]="'compact'"
  [showPageSizeSelector]="true"
  [showInfo]="true"
  [showNavigationButtons]="true"
>
</dxo-pager>
<dxo-header-filter [visible]="true"></dxo-header-filter>

<dxo-search-panel
  [visible]="true"
  [highlightCaseSensitive]="true"
></dxo-search-panel>

<dxi-column
  dataField="venderid"
  caption="{{ 'COMMON.SupplierId' | translate }}"
></dxi-column>
<dxi-column
  dataField="suppliers_name"
  caption="{{ 'COMMON.Name' | translate }}"
></dxi-column>
<dxi-column
  dataField="actionname"
  caption="{{ 'COMMON.ActionName' | translate }}"
></dxi-column>
<dxi-column
  dataField="invno_ormony_id"
  caption="{{ 'COMMON.ActionNo' | translate }}"
></dxi-column>
<dxi-column
  dataField="Invcomno"
  caption="{{ 'COMMON.Invcomno' | translate }}"
></dxi-column>
<dxi-column
  dataField="movedate"
  caption="{{ 'COMMON.Date' | translate }}"
></dxi-column>
<dxi-column
  dataField="maden"
  caption="{{ 'COMMON.Date' | translate }}"
></dxi-column>
<dxi-column
  dataField="daen"
  caption="{{ 'COMMON.Credit' | translate }}"
></dxi-column>
<dxi-column
  dataField="notes"
  caption="{{ 'COMMON.Notes' | translate }}"
></dxi-column>
<dxi-column
  dataField="address"
  caption="{{ 'COMMON.Address' | translate }}"
></dxi-column>

<dxi-column dataField="saletax" caption="{{ 'COMMON.VatNo' | translate }}">
  <dxo-header-filter>
    <!--<dxo-search [enabled]="true"></dxo-search>-->
  </dxo-header-filter>
</dxi-column>

<dxi-column
  dataField="caseName"
  caption="{{ 'COMMON.CaseName' | translate }}"
></dxi-column>
<dxi-column
  dataField="journal"
  caption="{{ 'COMMON.JournalNO' | translate }}"
></dxi-column>
<dxi-column
  dataField="docNo"
  caption="{{ 'COMMON.DocNo' | translate }}"
></dxi-column>
<dxi-column
  dataField="costid"
  caption="{{ 'COMMON.Costid' | translate }}"
></dxi-column>
<dxi-column
  dataField="costName"
  caption="{{ 'COMMON.Costid' | translate }}"
></dxi-column>

<dxi-column
  dataField="financial_entity_Type"
  caption="{{ 'COMMON.financial_entity_Type' | translate }}"
></dxi-column>
<dxi-column
  dataField="financial_entity_Id"
  caption="{{ 'COMMON.financial_entity_Id' | translate }}"
></dxi-column>
<dxi-column
  dataField="financial_entity"
  caption="{{ 'COMMON.financial_entity' | translate }}"
></dxi-column>

<!--<dxi-column dataField="name">
  <dxo-header-filter>-->
<!--<dxo-search [enabled]="true"-->
<!--[searchExpr]="searchExpr"
[editorOptions]="editorOptions"></dxo-search>-->
<!--</dxo-header-filter>
</dxi-column>-->
<dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

<dxo-summary>
  <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
  <dxi-total-item column="date" summaryType="min">
    [customizeText]="customizeDate"
  </dxi-total-item>

  <dxi-total-item column="maden" summaryType="sum" valueFormat="currency">
  </dxi-total-item>
</dxo-summary>
</dx-data-grid>
