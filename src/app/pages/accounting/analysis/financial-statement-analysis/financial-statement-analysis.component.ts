import { ChangeDetectorR<PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { AccountingService } from '../../accounting.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import { FormControl, FormGroup } from '@angular/forms';
import { formatDate } from '@angular/common';
@Component({
    selector: 'app-financial-statement-analysis',
    templateUrl: './financial-statement-analysis.component.html',
    styleUrls: ['./financial-statement-analysis.component.scss'],
    standalone: false
})
export class FinancialStatementAnalysisComponent implements OnDestroy {
  viewListForm: FormGroup;
  moduleName = 'Accounting.FinancialStatementAnalysis';
  data: any[];
  gridDataSource: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  todayButton: any;
  nextDateButton: any;
  millisecondsInDay = 24 * 60 * 60 * 1000;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }

  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;


  constructor(private service: AccountingService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.AccountingReports}FinancialStatementAnalysis`;
    this.subscription.add(service.list().subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getEmployeesDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();

    this.todayButton = {
      text: 'Today',
      onClick: () => {
        this.toDateValue = new Date().getTime();
      },
    };

    this.prevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue -= this.millisecondsInDay;
      },
    };

    this.nextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue += this.millisecondsInDay;
      },
    };
  }

  onItemClick(e: any) {

  }
  calculateSelectedRow(
    options: DxDataGridTypes.CustomSummaryInfo<any, any>
  ): void {
    if (options.name === 'SelectedRowsSummary') {
      if (options.summaryProcess === 'start') {
        options.totalValue = 5;
      }
    }
  }

  ngOnInit(): void {
    const currentYear = new Date().getFullYear();
    this.viewListForm = new FormGroup({
      fromDate: new FormControl(
        formatDate(new Date(currentYear, 0, 1), 'yyyy-MM-dd', 'en')
      ),
      toDate: new FormControl(
        formatDate(new Date(currentYear, 11, 31), 'yyyy-MM-dd', 'en')
      ),
      financialYear: new FormControl(currentYear),
      caseId: new FormControl(2),
      customer: new FormControl(0),
      isDetails: new FormControl('true'),
      showPreviousBalance: new FormControl('false'),
      customerCategoryID: new FormControl(0),
      branchid: new FormControl(1),
      supplierId: new FormControl(0),
      currencyid: new FormControl(1),
      mainCustomerId: new FormControl(0),
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('CustomerAccount');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('CustomerAccount.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'CustomerAccount.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  filter() {
    const Tdates = new Date(this.toDateValue).toLocaleDateString();
    const Fdates = new Date(this.fromDateValue).toLocaleDateString();
    const employees = this.gridBoxValue.length > 0 ? this.gridBoxValue.map((e: any) => e.id) : [];
    this.subscription.add(this.service.list({ Tdates, Fdates, employees }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }

}
