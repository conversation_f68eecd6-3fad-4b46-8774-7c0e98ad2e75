import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {AccountsSummaryComponent} from './accounts-summary/accounts-summary.component';
import {
    CompareAccountsMonthlyReportComponent
} from './compare-accounts-monthly-report/compare-accounts-monthly-report.component';
import {
    ComparingAccountsQuarterlyReportComponent
} from './comparing-accounts-quarterly-report/comparing-accounts-quarterly-report.component';
import {DepositsMovementReportComponent} from './deposits-movement-report/deposits-movement-report.component';
import {
    FinancialStatementAnalysisComponent
} from './financial-statement-analysis/financial-statement-analysis.component';

import {SharedModule} from '../../shared/shared.module';
import {DxContextMenuModule, DxTreeViewModule} from 'devextreme-angular';
import {AccountingAnalysisRoutingModule} from "./accounting-analysis-routing.module";

@NgModule({
    declarations: [
        AccountsSummaryComponent,
        CompareAccountsMonthlyReportComponent,
        ComparingAccountsQuarterlyReportComponent,
        DepositsMovementReportComponent,
        FinancialStatementAnalysisComponent,
    ],
    imports: [
        SharedModule,
        AccountingAnalysisRoutingModule,
        DxTreeViewModule,
        DxContextMenuModule,
    ],
    exports: [RouterModule],
})
export class AccountingAnalysisModule {
}
