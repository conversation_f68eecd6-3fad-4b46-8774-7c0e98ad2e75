import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AccountsSummaryComponent} from './accounts-summary/accounts-summary.component';
import {
    CompareAccountsMonthlyReportComponent
} from './compare-accounts-monthly-report/compare-accounts-monthly-report.component';
import {
    ComparingAccountsQuarterlyReportComponent
} from './comparing-accounts-quarterly-report/comparing-accounts-quarterly-report.component';
import {DepositsMovementReportComponent} from './deposits-movement-report/deposits-movement-report.component';
import {
    FinancialStatementAnalysisComponent
} from './financial-statement-analysis/financial-statement-analysis.component';


const routes: Routes = [
    {
        path: '',
        redirectTo: 'deposits_movement_report',
        pathMatch: 'full'
    },
    {
        path: 'deposits_movement_report',
        component: DepositsMovementReportComponent
    },
    {
        path: 'financial_statement_analysis',
        component: FinancialStatementAnalysisComponent
    },
    {
        path: 'compare_accounts_monthly_report',
        component: CompareAccountsMonthlyReportComponent
    },
    {
        path: 'comparing_accounts_quarterly_report',
        component: ComparingAccountsQuarterlyReportComponent
    },
    {
        path: 'accounts_summary',
        component: AccountsSummaryComponent
    },


];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingAnalysisRoutingModule {
}
