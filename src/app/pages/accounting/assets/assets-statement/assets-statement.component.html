
<form [formGroup]="viewListForm" class="mb-3">

  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.AssetsStatement" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
          <button
            [routerLink]="['/accounting/assets/assets_list_view', selectedItemKeys[0]]"
            [hidden]="
              selectedItemKeys.length > 1 || selectedItemKeys.length == 0
            "
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Accounting.AssetsList',
              action: 'updateAction'
            }"
          >
            {{ "COMMON.EDIT" | translate }}
            <i class="fa fa-edit"></i>
          </button>

          <button
            (click)="deleteRecords()"
            [hidden]="!selectedItemKeys.length"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Accounting.AssetsList',
              action: 'deleteAction'
            }"
          >
            {{ "COMMON.DELETE" | translate }}
            <i class="fa fa-trash"></i>
          </button>
        </div>

        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>

        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportToExcel" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.AssetsStatement'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card-body border-top">


    <div class="main-inputs">
      <div class="row">
        <div class="form-group col-xl-3 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.From" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-3 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.To" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>


        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="branchid" class="form-label">
            {{ "COMMON.Branch" | translate }}
          </label>
          <ng-select
            id="branchid"
            formControlName="branchid"
            bindLabel="name"
            bindValue="id"
            [items]="branches"
          ></ng-select>
        </div>
  
     
  

  

      </div>



    </div>
  </div>
</form>

<dx-data-grid id="employees"
              [dataSource]="data"
              [rtlEnabled]="true"
              keyExpr="code"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">

  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="code"></dxi-column>
  <dxi-column dataField="date"></dxi-column>
  <dxi-column dataField="DayCode" caption="Day"></dxi-column>
  <dxi-column dataField="timeIn" caption="Check In"></dxi-column>
  <dxi-column dataField="timeOut" caption="Check Out"></dxi-column>
  <dxi-column dataField="Explain" caption="Notes"></dxi-column>
  <dxi-column dataField="jobname" caption="job"></dxi-column>
  <dxi-column dataField="AccName" caption="Analytic Account"></dxi-column>
  <dxi-column dataField="depname" caption="Depart Name">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>


  <dxi-column dataField="name">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"-->
      <!--[searchExpr]="searchExpr"
      [editorOptions]="editorOptions"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>
  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date"
                    summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val"
                    summaryType="sum"
                    valueFormat="currency">
    </dxi-total-item>

  </dxo-summary>

</dx-data-grid>
