<form [formGroup]="newdata">

  <div class="card mb-5 mb-xl-10">

    <div class="card-header border-0 cursor-pointer w-100">
  
      <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
        <h3 class="fw-bolder m-0">{{ "COMMON.NewAssetsList" | translate }}</h3>
   
        <div [style.position]="'relative'">
       <div class="btn-group">
       <button
         type="submit"
         (click)="discard()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.Cancel" | translate }}
         <i class="fa fa-close"></i>
       </button>
       <button
         type="submit"
         (click)="save()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.SaveChanges" | translate }}
         <i class="fa fa-save"></i>
       </button>
     </div>
     <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
               (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
     <button
       class="btn btn-icon btn-active-light-primary mx-2"
       (click)="toggleMenu()"
       data-bs-toggle="tooltip"
       data-bs-placement="top"
       data-bs-trigger="hover"
       title="Settings"
     >
       <i class="fa fa-gear"></i>
     </button>
     <div
       class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
       [class.show]="menuOpen"
       [style.position]="'absolute'"
       [style.top]="'100%'"
       [style.zIndex]="'1050'"
       [style.left]="isRtl ? '0' : 'auto'"
       [style.right]="!isRtl ? '0' : 'auto'"
     >
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           (click)="exportToExcel()"
           data-kt-company-table-filter="delete_row"
         >
           {{ "COMMON.ExportToExcel" | translate }}
         </a>
       </div>
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openSmsModal()"
         >
           {{ "COMMON.SendViaSMS" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openEmailModal()"
         >
           {{ "COMMON.SendViaEmail" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openWhatsappModal()"
         >
           {{ "COMMON.SendViaWhatsapp" | translate }}
         </a>
       </div>
  
       <div
         class="menu-item px-3"
         *hasPermission="{
           action: 'printAction',
           module: 'Accounting.AssetsList'
         }"
       >
         <a
           class="menu-link px-3"
           target="_blank"
           href="/reports/warehouses"
           data-kt-company-table-filter="delete_row"
           >{{ "COMMON.Print" | translate }}</a
         >
       </div>
       <!-- <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'HR.Employees'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses?withLogo=true"
         data-kt-company-table-filter="delete_row"
         >Print With Logo</a
       >
     </div> -->
     </div>
   </div>
  
  </div>
  
    </div>


    <div class="card-body border-top p-9">

      <div class="main-inputs mb-5">
  
        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="ReferenceNumberId" class="form-label">
              {{ "COMMON.ReferenceNumber" | translate }}
            </label>
            <input
              id="ReferenceNumberId"
              type="text"
              formControlName="ReferenceNumberId"
              class="form-control"
            />
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="CodeId" class="form-label">
              {{ "COMMON.Code" | translate }}
            </label>
            <input
              id="CodeId"
              type="text"
              formControlName="CodeId"
              class="form-control"
            />
          </div>

        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="AssetNameId" class="form-label">
              {{ "COMMON.AssetName" | translate }}
            </label>
            <input
              id="AssetNameId"
              type="text"
              formControlName="AssetNameId"
              class="form-control"
            />
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="AssetValueId" class="form-label">
              {{ "COMMON.AssetValue" | translate }}
            </label>
            <input
              id="AssetValueId"
              type="text"
              formControlName="AssetValueId"
              class="form-control"
            />
          </div>


        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label class="form-label">{{ "COMMON.PurchaseDate" | translate }}</label>
            <input
              id="PurchaseDate"
              type="date"
              formControlName="PurchaseDate"
              class="form-control"
            />
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label class="form-label">{{ "COMMON.CommissioningDate" | translate }}</label>
            <input
              id="CommissioningDate"
              type="date"
              formControlName="CommissioningDate"
              class="form-control"
            />
          </div>





        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="costCenterId" class="form-label">
              {{ "COMMON.CostCenter" | translate }}
            </label>
            <ng-select
              id="costCenterId"
              formControlName="costCenterId"
              bindLabel="nameAr"
              bindValue="code"
              [items]="costCenters"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="branchid" class="form-label">
              {{ "COMMON.Branch" | translate }}
            </label>
            <ng-select
              id="branchid"
              formControlName="branchid"
              bindLabel="name"
              bindValue="id"
              [items]="branches"
            ></ng-select>
          </div>
          




        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="AssetCategoryId" class="form-label">
              {{ "COMMON.AssetCategory" | translate }}
            </label>
            <ng-select
              id="AssetCategoryId"
              formControlName="AssetCategoryId"
              bindLabel="nameAr"
              bindValue="code"
              [items]="AssetCategories"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="SerialNoId" class="form-label">
              {{ "COMMON.SerialNo" | translate }}
            </label>
            <input
              id="SerialNoId"
              type="text"
              formControlName="SerialNoId"
              class="form-control"
            />
          </div>

        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="DepreciationRateId" class="form-label">
              {{ "COMMON.DepreciationRate" | translate }}
            </label>
            <input
              id="DepreciationRateId"
              type="text"
              formControlName="DepreciationRateId"
              class="form-control"
            />
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="DepreciationMethodId" class="form-label">
              {{ "COMMON.DepreciationMethod" | translate }}
            </label>
            <input
              id="DepreciationMethodId"
              type="text"
              formControlName="DepreciationMethodId"
              class="form-control"
            />
          </div>


        </div>



        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="SerialId" class="form-label">
              {{ "COMMON.Serial" | translate }}
            </label>
            <input
              id="SerialId"
              type="text"
              formControlName="SerialId"
              class="form-control"
            />
          </div>


        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="invoiceNoId" class="form-label">
            {{ "COMMON.InvoiceNo" | translate }}
          </label>
          <input
            id="invoiceNoId"
            type="text"
            formControlName="invoiceNoId"
            class="form-control"
          />
        </div>

        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="ModelNoId" class="form-label">
              {{ "COMMON.ModelNo" | translate }}
            </label>
            <input
              id="ModelNoId"
              type="text"
              formControlName="ModelNoId"
              class="form-control"
            />
          </div>

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="NetAssetValueoId" class="form-label">
              {{ "COMMON.NetAssetValue" | translate }}
            </label>
            <input
              id="NetAssetValueId"
              type="text"
              formControlName="NetAssetValueId"
              class="form-control"
            />
          </div>


        </div>

        <div class="row">

          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="SalvageValueId" class="form-label">
              {{ "COMMON.SalvageValue" | translate }}
            </label>
            <input
              id="SalvageValueId"
              type="text"
              formControlName="SalvageValueId"
              class="form-control"
            />
          </div>

          
          <div class="form-group col-xl-4 col-md-2 col-sm-12">
            <label for="notes" class="form-label">
              {{ "COMMON.Notes" | translate }}
            </label>
            <input
              id="notes"
              type="text"
              formControlName="notes"
              class="form-control"
            />
          </div>


        </div>


        
      </div>
  
  
    </div>




  </div>



</form>