import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';

@Component({
  selector: 'app-assets_list_view',
  templateUrl: './assets_list_view.component.html',
  styleUrls: ['./assets_list_view.component.css'],
  standalone:false,
})
export class Assets_list_viewComponent implements OnInit {
  moduleName : "Accounting.AssetsList"
AssetsList: FormGroup;
[x: string]: any;
data: any[];
isGridBoxOpened: boolean;
editorOptions: any;
gridBoxValue: number[] = [1];
subscription = new Subscription();
currentFilter: any;
costCenterId: number = 0;
costCenters: any[];
AssetCategories: any[];
AssetCategoryId: number = 0;
branches: [] = [];
branchid: any = 1;
invoiceNoId: any= 0;
NetAssetValueId: any= 0;
AssetValueId: any= 0;
AssetNameId: any= 0;
DepreciationRateId: any= 0;
ReferenceNumberId: any= 0;
CodeId: any= 0;
SerialId: any= 0;
DepreciationMethodId: any= 0;
SerialNoId: any= 0;
NotesId: any= 0;
SalvageValueId: any= 0;
ModelNoId: any= 0;
CashBoxId: any = 1;
activeTab: string = 'tab1';
selectedItemKeys: any = [];
isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};

smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;

constructor(private service: AccountingService,    private fb: FormBuilder,

private cdk: ChangeDetectorRef,  private router: Router,    private route: ActivatedRoute,) { 
 

   const today = new Date().toISOString().slice(0, 10);
    const currentTime = new Date().toLocaleTimeString();


this.newdata = this.fb.group({

  PurchaseDate: [today, Validators.required],
  CommissioningDate: [today, Validators.required],
                          

});


this.subscription.add(
  service.getbranches().subscribe((r) => {
    if (r.success) {
      this.branches = r.data;
      this.cdk.detectChanges();
    }
  })
);

this.subscription.add(
  service.getCostCenters().subscribe((r) => {
    if (r.success) {
      this.costCenters = r.data;
      this.cdk.detectChanges();
    }
  })
);



}

ngOnInit() {
}


discard() {
}
save() {
  const id = this.route.snapshot.params.id;
  if (id) {
  if (this.newdata.valid) {
  const form = this.newdata.value;
  this.subscriptions.add(
  this.myService
  .update(id, form)
  .pipe(
  finalize(() => {
  this.isLoading = false;
  this.cdk.detectChanges();
  })
  )
  .subscribe((r: { success: boolean }) => {
  if (r.success) {
  this.router.navigate(['/accounting/assets/assets_list']);
  }
  })
  );
  } else {
  this.newdata.markAllAsTouched();
  }
  } else {
  if (this.newdata.valid) {
  const form = this.newdata.value;
  this.subscriptions.add(
  this.myService
  .create(form)
  .pipe(
  finalize(() => {
  this.isLoading = false;
  this.cdk.detectChanges();
  })
  )
  .subscribe((r: { success: boolean }) => {
  if (r.success) {
  this.router.navigate(['/accounting/assets/assets_list']);
  }
  })
  );
  } else {
  this.newdata.markAllAsTouched();
  }
  }
  }
  
  setActiveTab(tab: string): void {
  this.activeTab = tab;
  }
  
  
  
  exportToExcel() {
  this.subscription.add(this.service.exportExcel()
  .subscribe(e => {
  if (e) {
  const href = URL.createObjectURL(e);
  const link = document.createElement('a');
  link.setAttribute('download', 'AssetsList.xlsx');
  link.href = href;
  link.click();
  URL.revokeObjectURL(href);
  }
  }));
  }
  
  openSmsModal() {
  this.smsModal.open();
  }
  openWhatsappModal() {
  this.whatsappModal.open();
  }
  openEmailModal() {
  this.emailModal.open();
  }

}
