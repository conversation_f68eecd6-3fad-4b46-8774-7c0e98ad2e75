/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { Assets_list_viewComponent } from './assets_list_view.component';

describe('Assets_list_viewComponent', () => {
  let component: Assets_list_viewComponent;
  let fixture: ComponentFixture<Assets_list_viewComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Assets_list_viewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Assets_list_viewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
