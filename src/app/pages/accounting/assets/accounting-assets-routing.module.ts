import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AssetDisposalComponent} from './asset-disposal/asset-disposal.component';
import {AssetsDepreciationComponent} from './assets-depreciation/assets-depreciation.component';
import {AssetsListComponent} from './assets-list/assets-list.component';
import {AssetsStatementComponent} from './assets-statement/assets-statement.component';
import {DepreciationListComponent} from './depreciation-list/depreciation-list.component';
import { Assets_list_viewComponent } from './assets-list/assets_list_view/assets_list_view.component';


const routes: Routes = [
    {
        path: '',
        redirectTo: 'assets_list',
        pathMatch: 'full'
    },
    {
        path: 'assets_list',
        component: AssetsListComponent
    },
    {
        path: 'assets_depreciation',
        component: AssetsDepreciationComponent
    },
    {
        path: 'assets_statement',
        component: AssetsStatementComponent
    },
    {
        path: 'depreciation_list',
        component: DepreciationListComponent
    },
    {
        path: 'asset_disposal',
        component: AssetDisposalComponent
    },

    {
        path: 'assets_list_view',
        component: Assets_list_viewComponent
          },
        {
          path: 'assets_list_view/:id',
         component: Assets_list_viewComponent
        },

    
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingAssetsRoutingModule {
}
