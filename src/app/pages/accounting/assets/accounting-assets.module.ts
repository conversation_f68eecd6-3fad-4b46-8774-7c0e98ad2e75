import {NgModule} from '@angular/core';
import {AssetDisposalComponent} from './asset-disposal/asset-disposal.component';
import {AssetsDepreciationComponent} from './assets-depreciation/assets-depreciation.component';
import {AssetsListComponent} from './assets-list/assets-list.component';
import {AssetsStatementComponent} from './assets-statement/assets-statement.component';
import {DepreciationListComponent} from './depreciation-list/depreciation-list.component';
import {SharedModule} from "../../shared/shared.module";
import {DxContextMenuModule, DxTreeViewModule} from "devextreme-angular";
import {RouterModule} from "@angular/router";
import {AccountingAssetsRoutingModule} from "./accounting-assets-routing.module";
import { Assets_list_viewComponent } from './assets-list/assets_list_view/assets_list_view.component';

@NgModule({
    declarations: [
        AssetsListComponent,
        AssetsDepreciationComponent,
        AssetsStatementComponent,
        DepreciationListComponent,
        AssetDisposalComponent,
        Assets_list_viewComponent,
    ],
    imports: [
        SharedModule,
        AccountingAssetsRoutingModule,
        DxTreeViewModule,
        DxContextMenuModule,
    ],
    exports: [RouterModule],
})
export class AccountingAssetsModule {
}
