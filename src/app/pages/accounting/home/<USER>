
<!-- بداية لوحة تحكم الحسابات -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.AccountingDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.Home' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.AccountingModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف المؤشرات المالية الرئيسية -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي الإيرادات -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(totalRevenue) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.AccountingRevenue' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-money-bill-trend-up text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- إجمالي المصروفات -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(totalExpenses) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.AccountingExpenses' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-danger">
                  <i class="fa fa-money-bill-transfer text-danger fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- صافي الربح -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(netProfit) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.NetProfit' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-chart-line text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف المؤشرات المالية الرئيسية -->

      <!-- بداية صف المؤشرات المالية الثانوية -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الحسابات المدينة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(accountsReceivable) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.AccountingReceivables' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-file-invoice-dollar text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الحسابات الدائنة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(accountsPayable) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.AccountingPayables' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-file-invoice text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- رصيد النقدية -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatCurrency(cashBalance) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.AccountingCash' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-sack-dollar text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف المؤشرات المالية الثانوية -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني للإيرادات والمصروفات -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.AccountingRevenueAndExpenses' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.AccountingLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.AccountingQuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.AccountingCommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف المعاملات الأخيرة والتدفق النقدي -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- المعاملات الأخيرة -->
        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.AccountingRecentTransactions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.AccountingLastFiveTransactions' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.AccountingTransactionID' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.AccountingType' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.AccountingAmount' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.AccountingDate' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.AccountingStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let transaction of recentTransactions">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ transaction.id }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ transaction.type }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ formatCurrency(transaction.amount) }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ transaction.date }}</span>
                      </td>
                      <td>
                        <span [ngClass]="transaction.status === 'مرحل' ? 'badge badge-light-success' : 'badge badge-light-warning'">{{ transaction.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- التدفق النقدي -->
        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.CashFlow' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.AccountingLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.AccountingViewReport' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget2></app-charts-widget2>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف المعاملات الأخيرة والتدفق النقدي -->

      <!-- بداية صف التقارير المالية -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.AccountingFinancialReports' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.AccountingQuickFinancialReports' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.AccountingViewAllReports' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <div class="row g-5 g-xl-8">
                <!-- قائمة الدخل -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-warning">
                    <div class="card-body my-3">
                      <a href="/accounting/final_accounts" class="card-title fw-bold text-warning fs-5 mb-3 d-block">{{ 'COMMON.AccountingIncomeStatement' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">{{ formatCurrency(netProfit) }}</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.NetProfit' | translate }}</span>
                      </div>
                      <div class="d-flex align-items-center w-100 mt-4">
                        <span class="fw-bold text-gray-700 fs-6">{{ 'COMMON.AccountingViewReport' | translate }}</span>
                        <span class="ms-auto text-warning fw-bold fs-6">
                          <i class="fa fa-arrow-left"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- الميزانية العمومية -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-success">
                    <div class="card-body my-3">
                      <a href="/accounting/final_accounts" class="card-title fw-bold text-success fs-5 mb-3 d-block">{{ 'COMMON.AccountingBalanceSheet' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">{{ formatCurrency(totalRevenue + accountsReceivable) }}</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.AccountingTotalAssets' | translate }}</span>
                      </div>
                      <div class="d-flex align-items-center w-100 mt-4">
                        <span class="fw-bold text-gray-700 fs-6">{{ 'COMMON.AccountingViewReport' | translate }}</span>
                        <span class="ms-auto text-success fw-bold fs-6">
                          <i class="fa fa-arrow-left"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- التدفقات النقدية -->
                <div class="col-xl-4">
                  <div class="card card-xl-stretch mb-xl-8 bg-light-primary">
                    <div class="card-body my-3">
                      <a href="/accounting/final_accounts" class="card-title fw-bold text-primary fs-5 mb-3 d-block">{{ 'COMMON.AccountingCashFlowStatement' | translate }}</a>
                      <div class="py-1">
                        <span class="text-dark fs-1 fw-bold me-2">{{ formatCurrency(cashBalance) }}</span>
                        <span class="fw-semibold text-muted fs-7">{{ 'COMMON.AccountingNetCashFlow' | translate }}</span>
                      </div>
                      <div class="d-flex align-items-center w-100 mt-4">
                        <span class="fw-bold text-gray-700 fs-6">{{ 'COMMON.AccountingViewReport' | translate }}</span>
                        <span class="ms-auto text-primary fw-bold fs-6">
                          <i class="fa fa-arrow-left"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف التقارير المالية -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم الحسابات -->
