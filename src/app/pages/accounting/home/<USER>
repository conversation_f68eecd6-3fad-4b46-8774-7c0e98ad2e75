import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-accounting-home',
    templateUrl: './accounting-home.component.html',
    styleUrls: ['./accounting-home.component.scss'],
    standalone: false
})
export class accountingHomeComponent implements OnInit {
  // المؤشرات المالية
  totalRevenue: number = 0;
  totalExpenses: number = 0;
  netProfit: number = 0;
  accountsReceivable: number = 0;
  accountsPayable: number = 0;
  cashBalance: number = 0;

  // بيانات الرسوم البيانية
  revenueExpenseData: any[] = [];
  cashFlowData: any[] = [];
  accountsData: any[] = [];

  // المعاملات الأخيرة
  recentTransactions: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'إضافة قيد يومية', icon: 'fa-book', route: '/accounting/accounting/journal-entries' },
    { title: 'سند صرف', icon: 'fa-money-bill', route: '/accounting/accounting/cash-payments' },
    { title: 'سند قبض', icon: 'fa-hand-holding-usd', route: '/accounting/accounting/cash-receipts' },
    { title: 'تسوية عهدة', icon: 'fa-balance-scale', route: '/accounting/accounting/custody-settlement' },
    { title: 'إشعارات', icon: 'fa-bell', route: '/accounting/accounting/notices' }
  ];

  constructor(private translate: TranslateService) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات مالية وهمية
      this.totalRevenue = ********;
      this.totalExpenses = 8750000;
      this.netProfit = this.totalRevenue - this.totalExpenses;
      this.accountsReceivable = 3200000;
      this.accountsPayable = 1800000;
      this.cashBalance = 4500000;

      // بيانات الرسوم البيانية الوهمية
      this.revenueExpenseData = [
        { month: 'يناير', revenue: 950000, expenses: 650000 },
        { month: 'فبراير', revenue: 1000000, expenses: 700000 },
        { month: 'مارس', revenue: 1100000, expenses: 750000 },
        { month: 'أبريل', revenue: 1200000, expenses: 800000 },
        { month: 'مايو', revenue: 1150000, expenses: 850000 },
        { month: 'يونيو', revenue: 1300000, expenses: 900000 }
      ];

      this.cashFlowData = [
        { month: 'يناير', inflow: 1200000, outflow: 900000 },
        { month: 'فبراير', inflow: 1250000, outflow: 950000 },
        { month: 'مارس', inflow: 1350000, outflow: 1000000 },
        { month: 'أبريل', inflow: 1450000, outflow: 1050000 },
        { month: 'مايو', inflow: 1400000, outflow: 1100000 },
        { month: 'يونيو', inflow: 1550000, outflow: 1150000 }
      ];

      // معاملات حديثة وهمية
      this.recentTransactions = [
        { id: 'JE-2023-001', type: 'قيد يومية', amount: 250000, date: '2023-06-15', status: 'مرحل' },
        { id: 'CP-2023-045', type: 'سند صرف', amount: 125000, date: '2023-06-14', status: 'مرحل' },
        { id: 'CR-2023-078', type: 'سند قبض', amount: 350000, date: '2023-06-13', status: 'مرحل' },
        { id: 'JE-2023-002', type: 'قيد يومية', amount: 180000, date: '2023-06-12', status: 'غير مرحل' },
        { id: 'CS-2023-023', type: 'تسوية عهدة', amount: 50000, date: '2023-06-11', status: 'مرحل' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      financialSummary: this.http.get<any>('api/accounting/dashboard/summary'),
      recentTransactions: this.http.get<any>('api/accounting/dashboard/transactions'),
      chartData: this.http.get<any>('api/accounting/dashboard/charts')
    }).subscribe(results => {
      // معالجة النتائج
      this.totalRevenue = results.financialSummary.totalRevenue;
      this.totalExpenses = results.financialSummary.totalExpenses;
      this.netProfit = results.financialSummary.netProfit;
      this.accountsReceivable = results.financialSummary.accountsReceivable;
      this.accountsPayable = results.financialSummary.accountsPayable;
      this.cashBalance = results.financialSummary.cashBalance;

      this.recentTransactions = results.recentTransactions;
      this.revenueExpenseData = results.chartData.revenueExpense;
      this.cashFlowData = results.chartData.cashFlow;

      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatCurrency(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }
}
