import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ExchangeRequestComponent } from './exchange-request/exchange-request.component';
import { JournalForAuditComponent } from './journal-for-audit/journal-for-audit.component';
import { CashPaymentsComponent } from './cash-payments/cash-payments.component';
import { CashReceiptsComponent } from './cash-receipts/cash-receipts.component';
import { JournalEntriesComponent } from './journal-entries/journal-entries.component';
import { PostingFromPosComponent } from './posting-from-pos/posting-from-pos.component';
import { CustodySettlementComponent } from './custody-settlement/custody-settlement.component';
import { NoticesComponent } from './notices/notices.component';
import { ChequesBookComponent } from './cheques-book/cheques-book.component';
import { NotesReceivableComponent } from './notes-receivable/notes-receivable.component';
import { NotesPayableComponent } from './notes-payable/notes-payable.component';
import { SharedModule } from '../../shared/shared.module';
import { DxContextMenuModule, DxTreeViewModule } from 'devextreme-angular';
import { AccountingAccountingRoutingModule } from './accounting-accounting-routing.module';
import { BankPaymentsComponent } from './bank-payments/bank-payments.component';
import { BankReceiptsComponent } from './bank-receipts/bank-receipts.component';
import { Exchange_request_viewComponent } from './exchange-request/exchange_request_view/exchange_request_view.component';
import { Journal_for_audit__viewComponent } from './journal-for-audit/journal_for_audit__view/journal_for_audit__view.component';
import { Cash_payments_viewComponent } from './cash-payments/cash_payments_view/cash_payments_view.component';
import { Bank_payments_viewComponent } from './bank-payments/bank_payments_view/bank_payments_view.component';
import { Cash_receipts_viewComponent } from './cash-receipts/cash_receipts_view/cash_receipts_view.component';
import { Bank_receipts_viewComponent } from './bank-receipts/bank_receipts_view/bank_receipts_view.component';
import { Journal_entries_viewComponent } from './journal-entries/journal_entries_view/journal_entries_view.component';
import { Custody_settlement_viewComponent } from './custody-settlement/custody_settlement_view/custody_settlement_view.component';
import { Notices_viewComponent } from './notices/notices_view/notices_view.component';
import { Cheques_book_viewComponent } from './cheques-book/cheques_book_view/cheques_book_view.component';
import { Notes_receivable_viewComponent } from './notes-receivable/notes_receivable_view/notes_receivable_view.component';
import { AccountingGridControle } from '../../general/accounting-grid-controle/AccountingGridControle.module';
import { ProductsGridControleModule } from '../../general/products-grid-controle/ProductsGridControle.module';
import { JournalGridControle } from '../../general/journal-grid-controle/JournalGridControle.module';

@NgModule({
  declarations: [
    ExchangeRequestComponent,
    JournalForAuditComponent,
    CashPaymentsComponent,
    CashReceiptsComponent,
    JournalEntriesComponent,
    PostingFromPosComponent,
    CustodySettlementComponent,
    NoticesComponent,
    ChequesBookComponent,
    NotesReceivableComponent,
    NotesPayableComponent,
    BankPaymentsComponent,
    BankReceiptsComponent,
    Exchange_request_viewComponent,
    Journal_for_audit__viewComponent,
    Cash_payments_viewComponent,
    Bank_payments_viewComponent,
    Cash_receipts_viewComponent,
    Bank_receipts_viewComponent,
    Journal_entries_viewComponent,
    Custody_settlement_viewComponent,
    Notices_viewComponent,
    Cheques_book_viewComponent,
    Notes_receivable_viewComponent,
  ],
  imports: [
    SharedModule,
    AccountingAccountingRoutingModule,
    DxTreeViewModule,
    DxContextMenuModule,
    AccountingGridControle,
    ProductsGridControleModule,
    JournalGridControle,
  ],
  exports: [RouterModule],
})
export class AccountingAccountingModule {}
