import { AccountingService } from '../../accounting.service';
import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
/*import { ModalConfig, ModalComponent } from '../../../../_metronic/partials';*/

import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { finalize, lastValueFrom, Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import dxDataGrid from 'devextreme/ui/data_grid';

@Component({
  selector: 'app-posting-from-pos',
  templateUrl: './posting-from-pos.component.html',
  styleUrls: ['./posting-from-pos.component.scss'],
  standalone: false,
})
export class PostingFromPosComponent implements OnInit, OnDestroy {
  moduleName = 'Accounting.PostingFromPOS';
  viewListForm: FormGroup;
  newdata: FormGroup;
  branches: [] = [];
  Journals: any[];
  journalId: any;
  dataa: any[] = [];
  isLoading = false;
  data: ArrayStore;
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [
    { name: 'تمت', value: true },
    { name: 'لم تتم', value: false },
  ];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;
  activeTab: string = 'tab1';

  selectedItemKeys: any = [];
dataSource: ArrayStore;
isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};

smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
      private myService: AccountingService,
      private route: ActivatedRoute,
      private cd: ChangeDetectorRef,
      private router: Router,
  ) {


  
    
    const today = new Date();
const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
    
const formatDate = (date: Date) => 
  date.toISOString().split('T')[0];

this.newdata = this.fb.group({
  toDate: [formatDate(today), Validators.required],
  fromDate: [formatDate(firstDayOfYear), Validators.required],
});


    this.subscriptions.add(
      this.myService.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cd.detectChanges();
        }
      })
    );

    this.subscriptions.add(
      myService.getJournals().subscribe((r) => {
        if (r.success) {
          this.Journals = r.data;

          this.cd.detectChanges();
        }
      })
    );

    myService.baseUrl = `${environment.appUrls.accounting}ExchangeRequest`;
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required],
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(
      this.route.paramMap.subscribe((p) => {
        const id = p.get('id') || '';
        //if (id) {
        this.subscriptions.add(
          this.myService.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
        //  }
      })
    );
    this.searchCtrl.valueChanges.subscribe((v) => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(
          this.myService.search(v).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            }
          })
        );
      } else if (id) {
        this.subscriptions.add(
          this.myService.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      } else {
        this.subscriptions.add(
          this.myService.list('').subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      }
    });
  }

  loadData(r: any) {
    this.data = r.dataa;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }

 @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;
   
 remove() {
       Swal.fire({
         title: 'هل حقاً تريد الحذف',
         showConfirmButton: true,
         showCancelButton: true,
         confirmButtonText: 'نعم',
         cancelButtonText: 'إلغاء',
         customClass: {
           confirmButton: 'btn btn-danger',
           cancelButton: 'btn btn-light'
         },
         icon: 'warning'
       }).then(r => {
         if (r.isConfirmed) {
           this.selectedItemKeys.forEach((key: any) => {
             this.subscriptions.add(this.myService.delete(key)
               .subscribe(r => {
                 if (r.success) {
                   this.data.remove(key);
                   this.dataGrid.instance.refresh();
                   this.cd.detectChanges();
                 }
               }));
           });
         }
       });
     }
 
   onSaving(e: any) {
     e.cancel = true;
     if (e.changes.length) {
       e.changes.forEach((c: any) => {
         if (c.type == 'update') {
           let selected = this.dataa.find(d => d.id == c.key);
           if (selected) {
             for (const key in selected) {
               if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                 c.data[key] = selected[key];
               }
             }
           }
         }
       });
       e.promise = this.processBatchRequest(e.changes, e.component);
     }
   }
 



async processBatchRequest(changes: Array<{}>, component: dxDataGrid): Promise<any> {
await lastValueFrom(this.myService.batch(changes));
component.cancelEditData();
await component.refresh(true);
     this.cd.detectChanges();
this.subscriptions.add(this.myService.list(null, this.currentPage).subscribe(r => {
if (r.success) {
this.loadData(r);
    }
  }));
}

 

          set currentPage(value: any) {
             this._currentPage = value;
             this.subscriptions.add(this.myService.list('', value).subscribe(r => {
               if (r.success) {
               //  this.dataSource = r.data;
                 this.dataSource = new ArrayStore({
                   key: 'id',
                   data: r.data,
                 });
                 this.cd.detectChanges();
                 MenuComponent.reinitialization();
               }
             }));
           }
         
           get currentPage() {
             return this._currentPage;
           }
 
           
   filter() {
 
     this.subscriptions.add(this.myService.list({ }).subscribe(r => {
       if (r.success) {
         this.data = r.data;
         this.cd.detectChanges();
       }
     }));
   }
 
 


   
     save() {
       const id = this.route.snapshot.params.id;
       if (id) {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.myService
               .update(id, form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cd.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                   this.router.navigate([
                     '/accounting/accounting/cash_payments',
                   ]);
                 }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       } else {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.myService
               .create(form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cd.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                   this.router.navigate([
                     '/accounting/accounting/cash_payments',
                   ]);
                 }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       }
     }
     calculateTimeDifference() {
       const fromTime = this.newdata.get('fromTimeValue')?.value;
       const toTime = this.newdata.get('toTimeValue')?.value;
   
       if (fromTime && toTime) {
         const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
         const [toHours, toMinutes] = toTime.split(':').map(Number);
   
         const fromTotalMinutes = fromHours * 60 + fromMinutes;
         const toTotalMinutes = toHours * 60 + toMinutes;
   
         const difference = toTotalMinutes - fromTotalMinutes;
   
         if (difference >= 0) {
           const hours = Math.floor(difference / 60);
           const minutes = difference % 60;
           this.newdata.get('timeId')?.setValue(`${hours}h ${minutes}m`);
         } else {
           this.newdata.get('timeId')?.setValue('Invalid Time');
         }
       } else {
         this.newdata.get('timeId')?.setValue('');
       }
     }
     setActiveTab(tab: string): void {
       this.activeTab = tab;
     }
   

   
     discard() {
       this.newdata.reset();
     }
     exportToExcel() {
       this.subscriptions.add(
         this.myService.exportExcel().subscribe((e) => {
           if (e) {
             const href = URL.createObjectURL(e);
             const link = document.createElement('a');
             link.setAttribute('download', 'payables.xlsx');
             link.href = href;
             link.click();
             URL.revokeObjectURL(href);
           }
         })
       );
     }
     uploadExcel() {
       var input = document.createElement('input');
       input.type = 'file';
       input.click();
       input.onchange = (e) => {
         if (input.files) {
           if (input.files[0]) {
             const fd = new FormData();
             fd.append('file', input.files[0]);
             this.subscriptions.add(
               this.myService.importExcel(fd).subscribe((e) => {
                 if (e) {
                   this.currentPage = 1;
                 }
               })
             );
           }
         }
       };
     }
   
     onExporting(e: any) {
       console.log(e);
       const workbook = new Workbook();
       const worksheet = workbook.addWorksheet('cash_payments');
       if (e.format == 'excel') {
         exportDataGrid({
           component: e.component,
           worksheet,
           autoFilterEnabled: true,
         }).then(() => {
           workbook.xlsx.writeBuffer().then((buffer: any) => {
             saveAs(
               new Blob([buffer], { type: 'application/octet-stream' }),
               'cash_payments.xlsx'
             );
           });
         });
       } else if (e.format == 'pdf') {
         const doc = new jsPDF();
         pdfGrid({
           jsPDFDocument: doc,
           component: e.component,
           indent: 5,
         }).then(() => {
           doc.save('cash_payments.pdf');
         });
       }
       e.cancel = true;
     }
   
     openSmsModal() {
       this.smsModal.open();
     }
     openWhatsappModal() {
       this.whatsappModal.open();
     }
     openEmailModal() {
       this.emailModal.open();
     }

}
