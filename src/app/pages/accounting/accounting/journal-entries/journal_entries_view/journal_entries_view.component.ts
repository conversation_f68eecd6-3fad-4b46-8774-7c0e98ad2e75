import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, lastValueFrom, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';
import { ActivatedRoute, Router } from '@angular/router';
import dxDataGrid from 'devextreme/ui/data_grid';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import jsPDF from 'jspdf';

@Component({
  selector: 'app-journal_entries_view',
  templateUrl: './journal_entries_view.component.html',
  styleUrls: ['./journal_entries_view.component.css'],
  standalone:false,
})
export class Journal_entries_viewComponent implements OnInit {

  moduleName = 'Accounting.JournalEntries';
  branchid: any = 1;
  branches: [] = [];
  currencyId: number = 1;
  currency: any[];
  Journals: any[];
  closingentry: boolean = false;
  openingentry: boolean = false;
  bankAccountId: number = 0;
  Banks: [] = [];
  costCenterId: number = 0;
  costCenters: any[];
  financial_entity_TypeId: number = 0;
  financial_entity_Id: number = 0;
  FinancialEntities: any[];
  priceLists: any[] = [];
  basicUnits: any = [];
  activeTab: string = 'tab1';
  AccountThirdParty: any[];
  ThirdPartyAccounts: number[] = [];
  costCenter: any[];
  totaldebit: number = 0;
  totalcredit: number = 0;
  difference: number = 0;

  debit: any;
  credit: any;
  additionalInfo: any;
  
  

      newdata: FormGroup;
      isLoading = false;
      selectedItemKeys: any = [];
      timeId: number = 0;
      _currentPage = 1;
      data: any[] = [];
      editorOptions: any;
      searchExpr: any;
      fromnextDateButton: any;
      subscription = new Subscription();
      printList: string[] = ['print', 'Print Custome'];
      currentFilter: any;
      itemsCount = 0;
      pagesCount = 0;
    
      isRtl: boolean = document.documentElement.dir === 'rtl';
      menuOpen = false;
      toggleMenu() {
        this.menuOpen = !this.menuOpen;
      }
      actionsList: string[] = [
        'Export',
        'Send via SMS',
        'Send Via Email',
        'Send Via Whatsapp',
        'print',
      ];
      analyticAccountTypeId: any = 0;
      financial_entity_Types: { id: number; name: string }[] = [
        { id: 1, name: 'Project' },
        { id: 2, name: 'Machine' },
        { id: 3, name: 'Assets' },
        { id: 4, name: 'Department' },
        { id: 5, name: 'Supplier' },
        { id: 6, name: 'Customer' },
        { id: 7, name: 'Cars' },
      ];
      modalConfig: ModalConfig = {
        modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
          return true;
        },
        dismissButtonLabel: 'Cancel',
      };
    
      smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
      emailModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Email',
      };
      whatsappModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Whatsapp',
      };
      @ViewChild('smsModal') private smsModal: ModalComponent;
      @ViewChild('emailModal') private emailModal: ModalComponent;
      @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor( private service: AccountingService,
       private fb: FormBuilder,
          private route: ActivatedRoute,
          private cdk: ChangeDetectorRef,
          private router: Router) { 

            const today = new Date().toISOString().slice(0, 10);
            const currentTime = new Date().toLocaleTimeString();
        
            this.newdata = this.fb.group({
            dateValue: [today, Validators.required],
    
            });
    
    
            this.subscription.add(service.getbranches().subscribe(r => {
              if (r.success) {
                this.branches = r.data;
                this.cdk.detectChanges();
        
              }
            }));
    
    
            this.subscription.add(
              service.getCostCenters().subscribe((r) => {
                if (r.success) {
                  this.costCenter = r.data;
                  this.cdk.detectChanges();
                }
              })
            );
    
    
            this.subscription.add(service.getCurrency().subscribe(r => {
              if (r.success) {
                this.currency = r.data;
                this.cdk.detectChanges();
              }
            }));
    
            this.subscription.add(
              service.getJournals().subscribe((r) => {
                if (r.success) {
                  this.Journals = r.data;
                  this.cdk.detectChanges();
                }
              })
            );
    
            this.subscription.add(service.getBankAccount().subscribe(r => {
              if (r.success) {
                this.Banks = r.data;
                this.cdk.detectChanges();
        
              }
            }));
    
            this.subscription.add(
              service.getAccountThirdParty().subscribe((r) => {
                if (r.success) {
                  this.AccountThirdParty = r.data;
                  this.cdk.detectChanges(); // تحديث واجهة المستخدم
                }
              })
            );
    
    
    
            this.subscription.add(
              service.getCostCenters().subscribe((r) => {
                if (r.success) {
                  this.costCenters = r.data;
                  this.cdk.detectChanges();
                }
              })
            );

          }





  ngOnInit() {
  }


  onFinaTypeSelect(selectedItem: any) {
    this.FinancialEntities = [];

    if (!selectedItem) {
      this.cdk.detectChanges();
      return;
    }
    // this.financial_entity_TypeId= selectedItem.id;

    this.subscription.add(
      this.service
        .getFinancialEntities(this.financial_entity_TypeId)
        .subscribe((r) => {
          if (r.success) {
            this.FinancialEntities = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }

  updateTotals(journalGridData: any[]) {
    console.log(journalGridData); 
    this.totaldebit = journalGridData.reduce((sum, item) => sum + (item.مدين || 0), 0);
    this.totalcredit = journalGridData.reduce((sum, item) => sum + (item.دائن || 0), 0);
    this.difference = this.totalcredit - this.totaldebit;
  }

  
  onSaving(e: any) {
    e.cancel = true;
    if (e.changes.length) {
      e.changes.forEach((c: any) => {
        if (c.type == 'update') {
          let selected = this.data.find(d => d.id == c.key);
          if (selected) {
            for (const key in selected) {
              if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                c.data[key] = selected[key];
              }
            }
          }
        }
      });
      e.promise = this.processBatchRequest(e.changes, e.component);
    }
  }
      async processBatchRequest(changes: Array<{}>, component: dxDataGrid): Promise<any> {
        await lastValueFrom(this.service.batch(changes));
        component.cancelEditData();
        await component.refresh(true);
        this.cdk.detectChanges();
        this.subscription.add(this.service.list('', this.currentPage).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
      loadData(r: any) {
        this.data = r.data;
        if (r.itemsCount) {
          this.itemsCount = r.itemsCount;
        }
        if (r.pagesCount) {
          this.pagesCount = r.pagesCount;
        }
        this.cdk.detectChanges();
        MenuComponent.reinitialization();
      }

  set currentPage(value: any) {
    this._currentPage = value;
    this.subscription.add(
      this.service.list('', value).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          MenuComponent.reinitialization();
        }
      })
    );
  }

  get currentPage() {
    return this._currentPage;
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscription.add(
          this.service
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate([
                  '/hr/rewards/benefit_deduction_application',
                ]);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscription.add(
          this.service
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate([
                  '/hr/rewards/benefit_deduction_application',
                ]);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  calculateTimeDifference() {
    const fromTime = this.newdata.get('fromTimeValue')?.value;
    const toTime = this.newdata.get('toTimeValue')?.value;

    if (fromTime && toTime) {
      const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
      const [toHours, toMinutes] = toTime.split(':').map(Number);

      const fromTotalMinutes = fromHours * 60 + fromMinutes;
      const toTotalMinutes = toHours * 60 + toMinutes;

      const difference = toTotalMinutes - fromTotalMinutes;

      if (difference >= 0) {
        const hours = Math.floor(difference / 60);
        const minutes = difference % 60;
        this.newdata.get('timeId')?.setValue(`${hours}h ${minutes}m`);
      } else {
        this.newdata.get('timeId')?.setValue('Invalid Time');
      }
    } else {
      this.newdata.get('timeId')?.setValue('');
    }
  }

  
  addToGrid() {
    const newItem = {
      bankAccountId: this.bankAccountId,
      debit: this.debit,
      credit: this.credit,
      additionalInfo: this.additionalInfo,
      costCenterId: this.costCenterId,
      financial_entity_TypeId: this.financial_entity_TypeId,
      financial_entity_Id: this.financial_entity_Id
    };

    this.data = [...this.data, newItem]; // تحديث البيانات وإضافة عنصر جديد
  }
  
  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  uploadExcel() {
    var input = document.createElement('input');
    input.type = 'file';
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscription.add(
            this.service.importExcel(fd).subscribe((e) => {
              if (e) {
                this.currentPage = 1;
              }
            })
          );
        }
      }
    };
  }

    onExporting(e: any) {
      console.log(e);
      const workbook = new Workbook();
      const worksheet = workbook.addWorksheet('journal_for_audit_');
      if (e.format == 'excel') {
        exportDataGrid({
          component: e.component,
          worksheet,
          autoFilterEnabled: true,
        }).then(() => {
          workbook.xlsx.writeBuffer().then((buffer: any) => {
            saveAs(
              new Blob([buffer], { type: 'application/octet-stream' }),
              'journal_for_audit_.xlsx'
            );
          });
        });
      } else if (e.format == 'pdf') {
        const doc = new jsPDF();
        pdfGrid({
          jsPDFDocument: doc,
          component: e.component,
          indent: 5,
        }).then(() => {
          doc.save('journal_for_audit_.pdf');
        });
      }
      e.cancel = true;
    }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }


}
