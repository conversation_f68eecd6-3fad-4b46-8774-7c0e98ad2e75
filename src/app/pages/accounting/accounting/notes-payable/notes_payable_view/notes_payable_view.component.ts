import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, lastValueFrom, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { AccountingService } from '../../../accounting.service';
import { ActivatedRoute, Router } from '@angular/router';
import dxDataGrid from 'devextreme/ui/data_grid';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import jsPDF from 'jspdf';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';


@Component({
  selector: 'app-notes_payable_view',
  templateUrl: './notes_payable_view.component.html',
  styleUrls: ['./notes_payable_view.component.css'],
  standalone:false,
})
export class Notes_payable_viewComponent implements OnInit {
  moduleName = 'Accounting.NotesPayable';
 branchid: any = 1;
   CashBoxId: any = 1;
   branches: [] = [];
   actionType: any[];
   SalesPersons: any[];
   SalesPersonid: number = 0;
   Cashs: [] = [];
   CheckNumberid: any = 0;
   DeleteCheckAccountid: any = 0;
   currencyId: number = 1;
   currency: any[];
 
   priceLists: any[] = [];
   basicUnits: any = [];
   activeTab: string = 'tab1';
 
   AccountThirdParty: any[];
   ThirdPartyAccounts: number[] = [];
   costCenter: any[];
   costGroup: any[];
 
   debit: any;
   credit: any;
   additionalInfo: any;
   newdata: FormGroup;
   isLoading = false;
   selectedItemKeys: any = [];
   timeId: number = 0;
   _currentPage = 1;
   data: any[] = [];
   editorOptions: any;
   searchExpr: any;
   fromnextDateButton: any;
   subscription = new Subscription();
   printList: string[] = ['print', 'Print Custome'];
   currentFilter: any;
   itemsCount = 0;
   pagesCount = 0;
   Transfer: boolean = false;
   Check : boolean = false;
 
   isRtl: boolean = document.documentElement.dir === 'rtl';
   menuOpen = false;
   toggleMenu() {
     this.menuOpen = !this.menuOpen;
   }
   actionsList: string[] = [
     'Export',
     'Send via SMS',
     'Send Via Email',
     'Send Via Whatsapp',
     'print',
   ];
   modalConfig: ModalConfig = {
     modalTitle: 'Send Sms',
     modalSize: 'lg',
     hideCloseButton(): boolean {
       return true;
     },
     dismissButtonLabel: 'Cancel',
   };
 
   smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
   emailModalConfig: ModalConfig = {
     ...this.modalConfig,
     modalTitle: 'Send Email',
   };
   whatsappModalConfig: ModalConfig = {
     ...this.modalConfig,
     modalTitle: 'Send Whatsapp',
   };
   @ViewChild('smsModal') private smsModal: ModalComponent;
   @ViewChild('emailModal') private emailModal: ModalComponent;
   @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
 
   constructor(
      private service: AccountingService,
         private fb: FormBuilder,
         private route: ActivatedRoute,
         private cdk: ChangeDetectorRef,
         private router: Router,
   ) { const today = new Date().toISOString().slice(0, 10);
     const currentTime = new Date().toLocaleTimeString();
 
     this.newdata = this.fb.group({
       dateValue: [today, Validators.required],
     });
 
     this.subscription.add(
       service.getbranches().subscribe((r) => {
         if (r.success) {
           this.branches = r.data;
           this.cdk.detectChanges();
         }
       })
     );
 
     this.subscription.add(
       service.getCostCenters().subscribe((r) => {
         if (r.success) {
           this.costCenter = r.data;
           this.cdk.detectChanges();
         }
       })
     );
 
     this.subscription.add(service.getSalesPersons().subscribe(r => {
       if (r.success) {
         this.SalesPersons = r.data;
         this.cdk.detectChanges();
       }
     }));
 
     this.subscription.add(
       service.getCurrency().subscribe((r) => {
         if (r.success) {
           this.currency = r.data;
           this.cdk.detectChanges();
         }
       })
     );
 
     this.subscription.add(service.getBankActionType().subscribe(r => {
       if (r.success) {
         this.actionType = r.data;
         this.cdk.detectChanges();
       }
     }));
 
 
 
 
 
 
   }
 
 
 
   ngOnInit() {}
 
   fillForm(item: any) {
     // this.newdata.get('basicSalary')?.setValue(item.basicSalary);
     // this.newdata.get('changedSalary')?.setValue(item.changedSalary);
   }
 
   onSaving(e: any) {
     e.cancel = true;
     if (e.changes.length) {
       e.changes.forEach((c: any) => {
         if (c.type == 'update') {
           let selected = this.data.find((d) => d.id == c.key);
           if (selected) {
             for (const key in selected) {
               if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                 c.data[key] = selected[key];
               }
             }
           }
         }
       });
       e.promise = this.processBatchRequest(e.changes, e.component);
     }
   }
   async processBatchRequest(
     changes: Array<{}>,
     component: dxDataGrid
   ): Promise<any> {
     await lastValueFrom(this.service.batch(changes));
     component.cancelEditData();
     await component.refresh(true);
     this.cdk.detectChanges();
     this.subscription.add(
       this.service.list('', this.currentPage).subscribe((r) => {
         if (r.success) {
           this.loadData(r);
         }
       })
     );
   }
   loadData(r: any) {
     this.data = r.data;
     if (r.itemsCount) {
       this.itemsCount = r.itemsCount;
     }
     if (r.pagesCount) {
       this.pagesCount = r.pagesCount;
     }
     this.cdk.detectChanges();
     MenuComponent.reinitialization();
   }
 
   set currentPage(value: any) {
     this._currentPage = value;
     this.subscription.add(
       this.service.list('', value).subscribe((r) => {
         if (r.success) {
           this.data = r.data;
           this.cdk.detectChanges();
           MenuComponent.reinitialization();
         }
       })
     );
   }
 
   onAccountTypeSelect(selectedItem: any) {
     this.ThirdPartyAccounts = [];
 
     if (!selectedItem) {
       this.cdk.detectChanges();
       return;
     }
     const Typeid = selectedItem.typId;
 
     this.subscription.add(
       this.service.getThirdPartyAccountId(Typeid).subscribe((r) => {
         if (r.success) {
           this.ThirdPartyAccounts = r.data;
           this.cdk.detectChanges();
         }
       })
     );
   }
 
   get currentPage() {
     return this._currentPage;
   }
 
   save() {
     const id = this.route.snapshot.params.id;
     if (id) {
       if (this.newdata.valid) {
         let form = this.newdata.value;
         this.subscription.add(
           this.service
             .update(id, form)
             .pipe(
               finalize(() => {
                 this.isLoading = false;
                 this.cdk.detectChanges();
               })
             )
             .subscribe((r) => {
               if (r.success) {
                 this.router.navigate([
                   '/accounting/accounting/notes_receivable',
                 ]);
               }
             })
         );
       } else {
         this.newdata.markAllAsTouched();
       }
     } else {
       if (this.newdata.valid) {
         let form = this.newdata.value;
         this.subscription.add(
           this.service
             .create(form)
             .pipe(
               finalize(() => {
                 this.isLoading = false;
                 this.cdk.detectChanges();
               })
             )
             .subscribe((r) => {
               if (r.success) {
                 this.router.navigate([
                   '/accounting/accounting/notes_receivable',
                 ]);
               }
             })
         );
       } else {
         this.newdata.markAllAsTouched();
       }
     }
   }
   calculateTimeDifference() {
     const fromTime = this.newdata.get('fromTimeValue')?.value;
     const toTime = this.newdata.get('toTimeValue')?.value;
 
     if (fromTime && toTime) {
       const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
       const [toHours, toMinutes] = toTime.split(':').map(Number);
 
       const fromTotalMinutes = fromHours * 60 + fromMinutes;
       const toTotalMinutes = toHours * 60 + toMinutes;
 
       const difference = toTotalMinutes - fromTotalMinutes;
 
       if (difference >= 0) {
         const hours = Math.floor(difference / 60);
         const minutes = difference % 60;
         this.newdata.get('timeId')?.setValue(`${hours}h ${minutes}m`);
       } else {
         this.newdata.get('timeId')?.setValue('Invalid Time');
       }
     } else {
       this.newdata.get('timeId')?.setValue('');
     }
   }
   setActiveTab(tab: string): void {
     this.activeTab = tab;
   }
 
 
 
   discard() {
     this.newdata.reset();
   }
   exportToExcel() {
     this.subscription.add(
       this.service.exportExcel().subscribe((e) => {
         if (e) {
           const href = URL.createObjectURL(e);
           const link = document.createElement('a');
           link.setAttribute('download', 'notes_receivable.xlsx');
           link.href = href;
           link.click();
           URL.revokeObjectURL(href);
         }
       })
     );
   }
   uploadExcel() {
     var input = document.createElement('input');
     input.type = 'file';
     input.click();
     input.onchange = (e) => {
       if (input.files) {
         if (input.files[0]) {
           const fd = new FormData();
           fd.append('file', input.files[0]);
           this.subscription.add(
             this.service.importExcel(fd).subscribe((e) => {
               if (e) {
                 this.currentPage = 1;
               }
             })
           );
         }
       }
     };
   }
 
   onExporting(e: any) {
     console.log(e);
     const workbook = new Workbook();
     const worksheet = workbook.addWorksheet('notes_receivable');
     if (e.format == 'excel') {
       exportDataGrid({
         component: e.component,
         worksheet,
         autoFilterEnabled: true,
       }).then(() => {
         workbook.xlsx.writeBuffer().then((buffer: any) => {
           saveAs(
             new Blob([buffer], { type: 'application/octet-stream' }),
             'notes_receivable.xlsx'
           );
         });
       });
     } else if (e.format == 'pdf') {
       const doc = new jsPDF();
       pdfGrid({
         jsPDFDocument: doc,
         component: e.component,
         indent: 5,
       }).then(() => {
         doc.save('notes_receivable.pdf');
       });
     }
     e.cancel = true;
   }
 
   openSmsModal() {
     this.smsModal.open();
   }
   openWhatsappModal() {
     this.whatsappModal.open();
   }
   openEmailModal() {
     this.emailModal.open();
   }                                                                                                                                                                                                                                                                                                                                         
}
