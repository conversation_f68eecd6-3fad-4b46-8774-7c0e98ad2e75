import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CashPaymentsComponent} from './cash-payments/cash-payments.component';
import {CashReceiptsComponent} from './cash-receipts/cash-receipts.component';
import {ChequesBookComponent} from './cheques-book/cheques-book.component';
import {CustodySettlementComponent} from './custody-settlement/custody-settlement.component';
import {ExchangeRequestComponent} from './exchange-request/exchange-request.component';
import {JournalEntriesComponent} from './journal-entries/journal-entries.component';
import {JournalForAuditComponent} from './journal-for-audit/journal-for-audit.component';
import {NotesPayableComponent} from './notes-payable/notes-payable.component';
import {NotesReceivableComponent} from './notes-receivable/notes-receivable.component';
import {NoticesComponent} from './notices/notices.component';
import {PostingFromPosComponent} from './posting-from-pos/posting-from-pos.component';
import {BankPaymentsComponent} from "./bank-payments/bank-payments.component";
import {BankReceiptsComponent} from "./bank-receipts/bank-receipts.component";
import { Exchange_request_viewComponent } from './exchange-request/exchange_request_view/exchange_request_view.component';
import { Journal_for_audit__viewComponent } from './journal-for-audit/journal_for_audit__view/journal_for_audit__view.component';
import { Cash_payments_viewComponent } from './cash-payments/cash_payments_view/cash_payments_view.component';
import { Bank_payments_viewComponent } from './bank-payments/bank_payments_view/bank_payments_view.component';
import { Cash_receipts_viewComponent } from './cash-receipts/cash_receipts_view/cash_receipts_view.component';
import { Bank_receipts_viewComponent } from './bank-receipts/bank_receipts_view/bank_receipts_view.component';
import { Journal_entries_viewComponent } from './journal-entries/journal_entries_view/journal_entries_view.component';
import { Custody_settlement_viewComponent } from './custody-settlement/custody_settlement_view/custody_settlement_view.component';
import { Notices_viewComponent } from './notices/notices_view/notices_view.component';
import { Cheques_book_viewComponent } from './cheques-book/cheques_book_view/cheques_book_view.component';
import { Notes_receivable_viewComponent } from './notes-receivable/notes_receivable_view/notes_receivable_view.component';
import { Notes_payable_viewComponent } from './notes-payable/notes_payable_view/notes_payable_view.component';


const routes: Routes = [
    {
        path: '',
        redirectTo: 'exchange_request',
        pathMatch: 'full'
    },
    {
        path: 'exchange_request',
        component: ExchangeRequestComponent
    },
    {
        path: 'journal_for_audit_',
        component: JournalForAuditComponent
    },
    {
        path: 'cash_payments',
        component: CashPaymentsComponent
    },
    {
        path: 'bank_payments',
        component: BankPaymentsComponent
    },
    {
        path: 'cash_receipts',
        component: CashReceiptsComponent
    },
    {
        path: 'bank_receipts',
        component: BankReceiptsComponent
    },
    {
        path: 'journal_entries',
        component: JournalEntriesComponent
    },
    {
        path: 'posting_from_pos',
        component: PostingFromPosComponent
    },
    {
        path: 'custody_settlement',
        component: CustodySettlementComponent
    },
    {
        path: 'notices',
        component: NoticesComponent
    },
    {
        path: 'cheques_book',
        component: ChequesBookComponent
    },
    {
path: 'exchange_request_view',
component: Exchange_request_viewComponent
  },
{
  path: 'exchange_request_view/:id',
 component: Exchange_request_viewComponent
},
 {
path: 'journal_for_audit__view',
component: Journal_for_audit__viewComponent
  },
{
  path: 'journal_for_audit__view/:id',
 component: Journal_for_audit__viewComponent
},
 {
path: 'cash_payments_view',
component: Cash_payments_viewComponent
  },
{
  path: 'cash_payments_view/:id',
 component: Cash_payments_viewComponent
},
 {
path: 'bank_payments_view',
component: Bank_payments_viewComponent
  },
{
  path: 'bank_payments_view/:id',
 component: Bank_payments_viewComponent
},
 {
path: 'cash_receipts_view',
component: Cash_receipts_viewComponent
  },
{
  path: 'cash_receipts_view/:id',
 component: Cash_receipts_viewComponent
},
 {
path: 'bank_receipts_view',
component: Bank_receipts_viewComponent
  },
{
  path: 'bank_receipts_view/:id',
 component: Bank_receipts_viewComponent
},

 {
path: 'journal_entries_view',
component: Journal_entries_viewComponent
  },
{
  path: 'journal_entries_view/:id',
 component: Journal_entries_viewComponent
},
 {
path: 'custody_settlement_view',
component: Custody_settlement_viewComponent
  },
{
  path: 'custody_settlement_view/:id',
 component: Custody_settlement_viewComponent
},
 {
path: 'notices_view',
component: Notices_viewComponent
  },
{
  path: 'notices_view/:id',
 component: Notices_viewComponent
},
 {
path: 'cheques_book_view',
component: Cheques_book_viewComponent
  },
{
  path: 'cheques_book_view/:id',
 component: Cheques_book_viewComponent
},
 {
path: 'notes_receivable_view',
component: Notes_receivable_viewComponent
  },
{
  path: 'notes_receivable_view/:id',
 component: Notes_receivable_viewComponent
},
 {
path: 'notes_payable_view',
component: Notes_payable_viewComponent
  },
{
  path: 'notes_payable_view/:id',
 component: Notes_payable_viewComponent
},





    {
        path: 'notes_receivable',
        component: NotesReceivableComponent
    },
    {
        path: 'notes_payable_',
        component: NotesPayableComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountingAccountingRoutingModule {
}
