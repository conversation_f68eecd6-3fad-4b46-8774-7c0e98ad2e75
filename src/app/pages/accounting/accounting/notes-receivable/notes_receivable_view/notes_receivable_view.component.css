/* styles.css أو styles.scss */
.custom-btn {
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    font-size: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .custom-btn:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
  }
  
  .animated-btn {
    animation: fadeIn 1s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }


  .button-group {
    display: flex;
    gap: 5px;
  }
  .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
  }
  
  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
  }
  
  .switch input:checked + .slider {
    background-color: #4caf50;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }
  
  .switch input:checked + .slider:before {
    transform: translateX(26px);
  }
  .nav-tabs .nav-item .nav-link {
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  .nav-tabs .nav-item .nav-link:hover {
    background-color: #007bff;
    color: #fff;
    border-radius: 5px;
  }
  
  