<form [formGroup]="viewListForm" class="mb-3">

  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.NotesReceivable" | translate }} </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
          <button
            routerLink="/accounting/accounting/notes_receivable_view"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Accounting.NotesReceivable',
              action: 'createAction'
            }"
          >
            {{ "COMMON.Create" | translate }}
            <i class="fa fa-plus"></i>
          </button>
          <button
            [routerLink]="['/accounting/accounting/notes_receivable_view', selectedItemKeys[0]]"
            [hidden]="
              selectedItemKeys.length > 1 || selectedItemKeys.length == 0
            "
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Accounting.NotesReceivable',
              action: 'updateAction'
            }"
          >
            {{ "COMMON.EDIT" | translate }}
            <i class="fa fa-edit"></i>
          </button>

          <button
            (click)="deleteRecords()"
            [hidden]="!selectedItemKeys.length"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Accounting.NotesReceivable',
              action: 'deleteAction'
            }"
          >
            {{ "COMMON.DELETE" | translate }}
            <i class="fa fa-trash"></i>
          </button>
        </div>

        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>

        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportToExcel" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Accounting.NotesReceivable'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card-body border-top">


    <div class="main-inputs">

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.From" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.To" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>



      </div>

      <div class="row">

                
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="ChequeStatusid" class="form-label">
            {{ "COMMON.ChequeStatus" | translate }}
          </label>
          <ng-select
            id="ChequeStatusid"
            [(ngModel)]="ChequeStatusid"
            bindLabel="nameAr"
            bindValue="id"
            [items]="ChequeStatuses"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="CheckAccountid" class="form-label">
            {{ "COMMON.CurrentCheckAccount" | translate }}
          </label>
          <ng-select
            id="CheckAccountid"
            [(ngModel)]="CheckAccountid"
            bindLabel="nameAr"
            bindValue="Id"
            [items]="actionType"
          ></ng-select>
        </div>





      </div>


      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="SaveCheckAccountid" class="form-label">
            {{ "COMMON.PostSaveCheckAccount" | translate }}
          </label>
          <ng-select
            id="SaveCheckAccountid"
            [(ngModel)]="SaveCheckAccountid"
            bindLabel="nameAr"
            bindValue="Id"
            [items]="actionType"
          ></ng-select>
        </div>


        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="journal" class="form-label">{{ "COMMON.JournalName" | translate }}</label>
          <ng-select
            id="journal"
            formControlName="journal"
            bindLabel="name"
            bindValue="id"
            [items]="Journals"
          ></ng-select>
        </div>

      </div>

      <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="bankAccountId" class="form-label">
            {{ "COMMON.Account" | translate }}
          </label>
          <ng-select
            id="bankAccountId"
            [(ngModel)]="bankAccountId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="Banks"
          ></ng-select>
        </div>


        <div class="form-group col-xl-1 col-md-4 col-sm-12">
          <div class="label p-2">{{ "COMMON.Check" | translate }}</div>
  
          <dx-check-box
            [value]="Check"
            [(ngModel)]="Check"
            valueExpr="Check "
          ></dx-check-box>
        </div>

        <div class="form-group col-xl-1 col-md-4 col-sm-12">
          <div class="label p-2">{{ "COMMON.Transfer" | translate }}</div>
  
          <dx-check-box
            [value]="Transfer"
            [(ngModel)]="Transfer"
            valueExpr="Transfer"
          ></dx-check-box>
        </div>

      </div>


    </div>
  </div>
</form>


<dx-data-grid id="gridcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="store_id"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">
  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="store_name" caption="id"></dxi-column>
  <dxi-column dataField="store_address" caption="Bank Name"></dxi-column>
  <dxi-column dataField="store_address" caption="Address"></dxi-column>

  <!--<dxi-column dataField="emp_name" caption="emp name">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>-->

  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="store_name" summaryType="count"> </dxi-total-item>


  </dxo-summary>


</dx-data-grid>

