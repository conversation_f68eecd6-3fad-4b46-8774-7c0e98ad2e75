<form [formGroup]="newdata">

  <div class="card mb-5 mb-xl-10">

  <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
    <h3 class="fw-bolder m-0">{{"COMMON.NewExchangeRequest" | translate}}</h3>

    <div [style.position]="'relative'">
   <div class="btn-group">
   <button
     type="submit"
     (click)="discard()"
     class="btn btn-sm btn-active-light-primary"
   >
     {{ "COMMON.Cancel" | translate }}
     <i class="fa fa-close"></i>
   </button>
   <button
     type="submit"
     (click)="save()"
     class="btn btn-sm btn-active-light-primary"
   >
     {{ "COMMON.SaveChanges" | translate }}
     <i class="fa fa-save"></i>
   </button>
 </div>
 <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
           (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
 <button
   class="btn btn-icon btn-active-light-primary mx-2"
   (click)="toggleMenu()"
   data-bs-toggle="tooltip"
   data-bs-placement="top"
   data-bs-trigger="hover"
   title="Settings"
 >
   <i class="fa fa-gear"></i>
 </button>
 <div
   class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
   [class.show]="menuOpen"
   [style.position]="'absolute'"
   [style.top]="'100%'"
   [style.zIndex]="'1050'"
   [style.left]="isRtl ? '0' : 'auto'"
   [style.right]="!isRtl ? '0' : 'auto'"
 >
   <div class="menu-item px-3">
     <a
       class="menu-link px-3"
       (click)="exportToExcel()"
       data-kt-company-table-filter="delete_row"
     >
       {{ "COMMON.ExportToExcel" | translate }}
     </a>
   </div>
   <div class="menu-item px-3">
     <a
       class="menu-link px-3"
       data-kt-company-table-filter="delete_row"
       (click)="openSmsModal()"
     >
       {{ "COMMON.SendViaSMS" | translate }}
     </a>
   </div>

   <div class="menu-item px-3">
     <a
       class="menu-link px-3"
       data-kt-company-table-filter="delete_row"
       (click)="openEmailModal()"
     >
       {{ "COMMON.SendViaEmail" | translate }}
     </a>
   </div>

   <div class="menu-item px-3">
     <a
       class="menu-link px-3"
       data-kt-company-table-filter="delete_row"
       (click)="openWhatsappModal()"
     >
       {{ "COMMON.SendViaWhatsapp" | translate }}
     </a>
   </div>

   <div
     class="menu-item px-3"
     *hasPermission="{
       action: 'printAction',
       module: 'Accounting.ExchangeRequest'
     }"
   >
     <a
       class="menu-link px-3"
       target="_blank"
       href="/reports/warehouses"
       data-kt-company-table-filter="delete_row"
       >{{ "COMMON.Print" | translate }}</a
     >
   </div>
  
 </div>
</div>

</div>


<div class="card-body border-top p-9">

      <div class="main-inputs flex-grow-1">

      </div>

 


    <div class="row">

      <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            [class.active]="activeTab === 'tab1'"
            (click)="setActiveTab('tab1')"
          >
            {{ "COMMON.PaymentRequest" | translate }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            [class.active]="activeTab === 'tab2'"
            (click)="setActiveTab('tab2')"
          >
            {{ "COMMON.FinancialVerificationwithAttachments" | translate }}
          </button>
        </li>

      </ul>

      <div class="tab-content" id="myTabContent">
        
        <!-- Tab 1 -->
        <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab1'"
          [class.active]="activeTab === 'tab1'"
          *ngIf="activeTab === 'tab1'"
        >
    
<div class="row">

<div class="form-group col-xl-4 col-md-4 col-sm-12">
  <label for="department" class="form-label">
    {{ "COMMON.Department" | translate }}
  </label>
  <ng-select
    id="department"
    formControlName="departId"
    bindLabel="nameAr"
    bindValue="id"
    [items]="departments"
  ></ng-select>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12">
  <label for="employeeId" class="form-label">
    {{ "COMMON.EmployeeName" | translate }}
  </label>
  <ng-select
    id="employeeId"
    formControlName="employeeId"
    bindLabel="nameAr"
    bindValue="id"
    [items]="employees"
  ></ng-select>
</div>

</div>


<div class="row">

  <div class="form-group col-xl-4 col-md-4 col-sm-12">
    <label for="PaymentTypeId" class="form-label">
      {{ "COMMON.PaymentType" | translate }}
    </label>
    <ng-select
      id="paymenttypeId"
      formControlName="paymenttypeId"
      bindLabel="nameAr"
      bindValue="id"
      [items]="paymenttypes"
    ></ng-select>
  </div>

  <div class="form-group col-xl-4 col-md-4 col-sm-12">
    <label for="currencyid" class="form-label">
      {{ "COMMON.Currency" | translate }}
    </label>
    <ng-select
      id="currencyid"
      formControlName="currencyId"
      bindLabel="nameAr"
      bindValue="id"
      [items]="currency"
    ></ng-select>
  </div>


</div>
        
<div class="row">

  
<div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
  <label class="label">{{
    "COMMON.DocumentNumber" | translate}}</label>
  <input
  type="text"
  id="documentnumber"
  name="documentnumber"
  class="form-control"
  formControlName="documentnumber"
/>
</div>




<div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
  <label class="label">{{
    "COMMON.Value" | translate}}</label>
    <input
    type="text"
    id="value"
    name="value"
    class="form-control"
    formControlName="value"
  />
</div>

</div>

 <div class="row">

  <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3">
    <label class="label">{{
      "COMMON.Beneficiary" | translate}}</label>
    <input
    type="text"
    id="beneficiary"
    name="beneficiary"
    class="form-control"
    formControlName="beneficiary"
  />
</div>

  <div class="form-group col-xl-8 col-md-3 col-sm-12 mb-3">
    <label class="label">{{
      "COMMON.Regarding" | translate}}</label>
    <input
    type="text"
    id="regarding"
    name="regarding"
    class="form-control"
    formControlName="regarding"
  />
</div>

</div>


</div>


        <!-- Tab 2 -->
        <div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab2'"
          [class.active]="activeTab === 'tab2'"
          *ngIf="activeTab === 'tab2'"
        >

    <div class="row">

      <div class="form-group col-xl-4 col-md-3 col-sm-12 ">
        <label class="label">{{"COMMON.FileName" | translate}}</label>
        <input
        type="text"
        id="filename"
        name="filename"
        class="form-control"
        formControlName="filename"
      />
    </div>


    <div class="form-group col-xl-4 col-md-3 col-sm-12">
      <label for="fileInput" class="form-label">
        {{"COMMON.FileLocation" | translate}}
      </label>
      <div class="input-group">
        <input type="file" id="fileInput" class="form-control" (change)="onFileSelected($event)" />
      </div>
    </div>
    



    

    </div>

    <div class="row">

      <div class="form-group col-xl-12 col-md-3 col-sm-12 ">
        <label class="form-label">{{"COMMON.Notes" | translate}}</label>
        <input
        type="text"
        id="notes"
        name="notes"
        class="form-control"
        formControlName="notes"
      />
    </div>

    </div>

    <dx-data-grid id="gridcontrole"
[rtlEnabled]="true"
              [dataSource]="data"
              keyExpr="store_id"
              [showRowLines]="true"
              [showBorders]="true"
              [columnAutoWidth]="true"
              (onExporting)="onExporting($event)"
              [allowColumnResizing]="true">
  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true"
                  [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="6"> </dxo-paging>
  <dxo-pager [visible]="true"
             [allowedPageSizes]="[5, 10, 'all']"
             [displayMode]="'compact'"
             [showPageSizeSelector]="true"
             [showInfo]="true"
             [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true"
                    [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="store_name" caption="id"></dxi-column>
  <dxi-column dataField="store_address" caption=" File Name"></dxi-column>
  <dxi-column dataField="store_address" caption="File Location"></dxi-column>
 <dxi-column dataField="store_address" caption="Notes"></dxi-column>+
  <dxi-column dataField="store_address" caption=" Users"></dxi-column>
  <dxi-column dataField="store_address" caption="Addition Date"></dxi-column>
 <dxi-column dataField="store_address" caption="Edited By"></dxi-column>+

 
  <!--<dxi-column dataField="emp_name" caption="emp name">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>-->

  <dxo-export [enabled]="true"
              [formats]="['pdf','excel']">
  </dxo-export>


  <dxo-summary>
    <dxi-total-item column="store_name" summaryType="count"> </dxi-total-item>


  </dxo-summary>


</dx-data-grid>


 

        </div>


      </div>


    </div>


</div>

</div>

</form>







