import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-hotel-home',
    templateUrl: './hotel-home.component.html',
    styleUrls: ['./hotel-home.component.scss'],
    standalone: false
})
export class HotelHomeComponent implements OnInit {
  // إحصائيات الغرف
  totalRooms: number = 0;
  occupiedRooms: number = 0;
  availableRooms: number = 0;
  underMaintenanceRooms: number = 0;

  // إحصائيات الضيوف
  totalGuests: number = 0;
  checkedInGuests: number = 0;
  checkingOutToday: number = 0;
  reservationsToday: number = 0;

  // إحصائيات الإيرادات
  totalRevenue: number = 0;
  roomRevenue: number = 0;
  serviceRevenue: number = 0;
  pendingPayments: number = 0;

  // بيانات الرسوم البيانية
  occupancyRateData: any[] = [];
  revenueByServiceData: any[] = [];

  // الحجوزات القادمة
  upcomingReservations: any[] = [];

  // الفواتير الأخيرة
  recentInvoices: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'تسجيل دخول ضيف', icon: 'fa-user-check', route: '/hotel/check-in' },
    { title: 'تسجيل خروج ضيف', icon: 'fa-user-times', route: '/hotel/check-out' },
    { title: 'إضافة حجز جديد', icon: 'fa-calendar-plus', route: '/hotel/reservations' },
    { title: 'إدارة الغرف', icon: 'fa-door-open', route: '/hotel/rooms' },
    { title: 'إضافة خدمة', icon: 'fa-concierge-bell', route: '/hotel/services' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات الغرف الوهمية
      this.totalRooms = 50;
      this.occupiedRooms = 35;
      this.availableRooms = 12;
      this.underMaintenanceRooms = 3;

      // بيانات الضيوف الوهمية
      this.totalGuests = 42;
      this.checkedInGuests = 35;
      this.checkingOutToday = 8;
      this.reservationsToday = 10;

      // بيانات الإيرادات الوهمية
      this.totalRevenue = 25000;
      this.roomRevenue = 18000;
      this.serviceRevenue = 7000;
      this.pendingPayments = 3500;

      // بيانات الرسوم البيانية الوهمية
      this.occupancyRateData = [
        { month: 'يناير', rate: 65 },
        { month: 'فبراير', rate: 70 },
        { month: 'مارس', rate: 75 },
        { month: 'أبريل', rate: 80 },
        { month: 'مايو', rate: 85 },
        { month: 'يونيو', rate: 75 }
      ];

      this.revenueByServiceData = [
        { service: 'إقامة', amount: 18000 },
        { service: 'مطعم', amount: 3500 },
        { service: 'خدمة الغرف', amount: 1200 },
        { service: 'مغسلة', amount: 800 },
        { service: 'خدمات أخرى', amount: 1500 }
      ];

      // بيانات الحجوزات القادمة الوهمية
      this.upcomingReservations = [
        { id: 'RES-2023-001', guest: 'أحمد محمد', room: '101 - جناح فاخر', checkIn: '2023-06-20', checkOut: '2023-06-25', status: 'مؤكد' },
        { id: 'RES-2023-002', guest: 'محمد علي', room: '205 - غرفة مزدوجة', checkIn: '2023-06-21', checkOut: '2023-06-23', status: 'مؤكد' },
        { id: 'RES-2023-003', guest: 'خالد عبدالله', room: '310 - غرفة مفردة', checkIn: '2023-06-22', checkOut: '2023-06-24', status: 'بانتظار التأكيد' },
        { id: 'RES-2023-004', guest: 'سارة أحمد', room: '402 - جناح عائلي', checkIn: '2023-06-23', checkOut: '2023-06-28', status: 'مؤكد' },
        { id: 'RES-2023-005', guest: 'فهد سعد', room: '115 - غرفة مزدوجة', checkIn: '2023-06-24', checkOut: '2023-06-26', status: 'بانتظار التأكيد' }
      ];

      // بيانات الفواتير الأخيرة الوهمية
      this.recentInvoices = [
        { id: 'INV-2023-001', guest: 'أحمد محمد', room: '101', date: '2023-06-15', amount: 3500, type: 'إقامة', status: 'مدفوعة' },
        { id: 'INV-2023-002', guest: 'محمد علي', room: '205', date: '2023-06-14', amount: 2800, type: 'إقامة', status: 'مدفوعة' },
        { id: 'INV-2023-003', guest: 'خالد عبدالله', room: '310', date: '2023-06-13', amount: 450, type: 'خدمة الغرف', status: 'معلقة' },
        { id: 'INV-2023-004', guest: 'سارة أحمد', room: '402', date: '2023-06-12', amount: 350, type: 'مطعم', status: 'مدفوعة' },
        { id: 'INV-2023-005', guest: 'فهد سعد', room: '115', date: '2023-06-11', amount: 200, type: 'مغسلة', status: 'معلقة' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      roomStats: this.http.get<any>('api/hotel/dashboard/rooms'),
      guestStats: this.http.get<any>('api/hotel/dashboard/guests'),
      revenueStats: this.http.get<any>('api/hotel/dashboard/revenue'),
      upcomingReservations: this.http.get<any>('api/hotel/dashboard/upcoming-reservations'),
      recentInvoices: this.http.get<any>('api/hotel/dashboard/recent-invoices')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalRooms = results.roomStats.total;
        this.occupiedRooms = results.roomStats.occupied;
        this.availableRooms = results.roomStats.available;
        this.underMaintenanceRooms = results.roomStats.underMaintenance;

        this.totalGuests = results.guestStats.total;
        this.checkedInGuests = results.guestStats.checkedIn;
        this.checkingOutToday = results.guestStats.checkingOutToday;
        this.reservationsToday = results.guestStats.reservationsToday;

        this.totalRevenue = results.revenueStats.total;
        this.roomRevenue = results.revenueStats.room;
        this.serviceRevenue = results.revenueStats.service;
        this.pendingPayments = results.revenueStats.pending;

        this.upcomingReservations = results.upcomingReservations;
        this.recentInvoices = results.recentInvoices;

        // بيانات الرسوم البيانية
        this.occupancyRateData = results.roomStats.occupancyRate;
        this.revenueByServiceData = results.revenueStats.byService;
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة الحجز
  getReservationStatusClass(status: string): string {
    switch (status) {
      case 'مؤكد':
        return 'badge-light-success';
      case 'بانتظار التأكيد':
        return 'badge-light-warning';
      case 'ملغي':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون حالة الفاتورة
  getInvoiceStatusClass(status: string): string {
    switch (status) {
      case 'مدفوعة':
        return 'badge-light-success';
      case 'معلقة':
        return 'badge-light-warning';
      case 'متأخرة':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
