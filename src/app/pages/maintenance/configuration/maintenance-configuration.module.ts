import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {EquipmentListComponent} from './equipment-list/equipment-list.component';
import {MaintenanceTypesComponent} from './maintenance-types/maintenance-types.component';
import {MaintenanceConfigurationRoutingModule} from "./maintenance-configuration-routing.module";
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [
    EquipmentListComponent,
    MaintenanceTypesComponent,
  ],
  imports: [MaintenanceConfigurationRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class MaintenanceConfigurationModule {}
