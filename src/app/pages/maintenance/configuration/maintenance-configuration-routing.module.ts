import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {EquipmentListComponent} from './equipment-list/equipment-list.component';
import {MaintenanceTypesComponent} from './maintenance-types/maintenance-types.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'equipment_list',
        pathMatch: "full"
    },
    {
        path: 'equipment_list',
        component: EquipmentListComponent
    },
    {
        path: 'maintenance_types',
        component: MaintenanceTypesComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class MaintenanceConfigurationRoutingModule {
}
