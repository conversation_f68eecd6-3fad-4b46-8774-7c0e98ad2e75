
<!-- بداية لوحة تحكم الصيانة -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.MaintenanceDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.MaintenanceHome' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.MaintenanceModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف إحصائيات المعدات -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي المعدات -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalEquipment) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceTotalEquipment' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-cogs text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المعدات النشطة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(activeEquipment) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceActiveEquipment' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-check-circle text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المعدات التي تحتاج صيانة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(equipmentNeedsMaintenance) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceNeedsMaintenance' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-exclamation-triangle text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- المعدات تحت الصيانة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(equipmentUnderMaintenance) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceUnderMaintenance' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-tools text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات المعدات -->

      <!-- بداية صف إحصائيات الأعطال -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي الأعطال -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalMalfunctions) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceTotalMalfunctions' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-bug text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الأعطال التي تم إصلاحها -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(resolvedMalfunctions) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceResolvedMalfunctions' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-check text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الأعطال قيد المعالجة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(pendingMalfunctions) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenancePendingMalfunctions' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-clock text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الأعطال الحرجة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(criticalMalfunctions) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceCriticalMalfunctions' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-danger">
                  <i class="fa fa-radiation text-danger fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات الأعطال -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني لطلبات الصيانة -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.MaintenanceRequestsOverTime' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.MaintenanceViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.MaintenanceQuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceCommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف الأعطال الأخيرة وتوزيع الأعطال -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الأعطال الأخيرة -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.MaintenanceRecentMalfunctions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceLastFiveMalfunctions' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.MaintenanceViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-100px">{{ 'COMMON.MaintenanceID' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.MaintenanceEquipment' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.MaintenanceLocation' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.MaintenanceType' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.MaintenanceDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.MaintenancePriority' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.MaintenanceStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let malfunction of recentMalfunctions">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ malfunction.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ malfunction.equipment }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ malfunction.location }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ malfunction.type }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ malfunction.date }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getPriorityClass(malfunction.priority)">{{ malfunction.priority }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getMalfunctionStatusClass(malfunction.status)">{{ malfunction.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- توزيع الأعطال -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.MaintenanceMalfunctionsByType' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceCurrentDistribution' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.MaintenanceViewReport' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget2></app-charts-widget2>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الأعطال الأخيرة وتوزيع الأعطال -->

      <!-- بداية صف طلبات الصيانة الأخيرة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.MaintenanceRecentRequests' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.MaintenanceLastFiveRequests' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.MaintenanceViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.MaintenanceRequestID' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.MaintenanceEquipment' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.MaintenanceDepartment' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.MaintenanceRequestedBy' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.MaintenanceDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.MaintenanceStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let request of recentRequests">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ request.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ request.equipment }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.department }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.requestedBy }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.date }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getRequestStatusClass(request.status)">{{ request.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف طلبات الصيانة الأخيرة -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم الصيانة -->
