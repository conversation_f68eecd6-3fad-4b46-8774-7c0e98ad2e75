import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-maintenance-home',
    templateUrl: './maintenance-home.component.html',
    styleUrls: ['./maintenance-home.component.scss'],
    standalone: false
})
export class maintenanceHomeComponent implements OnInit {
  // إحصائيات الصيانة
  totalEquipment: number = 0;
  activeEquipment: number = 0;
  equipmentNeedsMaintenance: number = 0;
  equipmentUnderMaintenance: number = 0;

  // إحصائيات الأعطال
  totalMalfunctions: number = 0;
  resolvedMalfunctions: number = 0;
  pendingMalfunctions: number = 0;
  criticalMalfunctions: number = 0;

  // إحصائيات طلبات الصيانة
  totalRequests: number = 0;
  completedRequests: number = 0;
  pendingRequests: number = 0;
  rejectedRequests: number = 0;

  // بيانات الرسوم البيانية
  malfunctionsByTypeData: any[] = [];
  maintenanceRequestsData: any[] = [];
  equipmentStatusData: any[] = [];

  // الأعطال الأخيرة
  recentMalfunctions: any[] = [];

  // طلبات الصيانة الأخيرة
  recentRequests: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'تسجيل عطل جديد', icon: 'fa-exclamation-triangle', route: '/maintenance/maintenance/malfunctions' },
    { title: 'طلب صيانة', icon: 'fa-tools', route: '/maintenance/maintenance/maintenance_requests' },
    { title: 'معايرة جهاز', icon: 'fa-sliders-h', route: '/maintenance/maintenance/calibration_device' },
    { title: 'خطة الصيانة الوقائية', icon: 'fa-calendar-check', route: '/maintenance/preventive/maintenance_plan' },
    { title: 'عقود الصيانة', icon: 'fa-file-contract', route: '/maintenance/contracts/maintenance_contract' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات المعدات الوهمية
      this.totalEquipment = 245;
      this.activeEquipment = 187;
      this.equipmentNeedsMaintenance = 32;
      this.equipmentUnderMaintenance = 26;

      // بيانات الأعطال الوهمية
      this.totalMalfunctions = 156;
      this.resolvedMalfunctions = 112;
      this.pendingMalfunctions = 34;
      this.criticalMalfunctions = 10;

      // بيانات طلبات الصيانة الوهمية
      this.totalRequests = 210;
      this.completedRequests = 175;
      this.pendingRequests = 28;
      this.rejectedRequests = 7;

      // بيانات الرسوم البيانية الوهمية
      this.malfunctionsByTypeData = [
        { type: 'ميكانيكي', count: 65 },
        { type: 'كهربائي', count: 48 },
        { type: 'إلكتروني', count: 32 },
        { type: 'برمجي', count: 11 }
      ];

      this.maintenanceRequestsData = [
        { month: 'يناير', completed: 25, pending: 5 },
        { month: 'فبراير', completed: 30, pending: 4 },
        { month: 'مارس', completed: 28, pending: 6 },
        { month: 'أبريل', completed: 35, pending: 3 },
        { month: 'مايو', completed: 32, pending: 5 },
        { month: 'يونيو', completed: 38, pending: 7 }
      ];

      this.equipmentStatusData = [
        { status: 'نشط', count: 187 },
        { status: 'يحتاج صيانة', count: 32 },
        { status: 'تحت الصيانة', count: 26 }
      ];

      // بيانات الأعطال الأخيرة الوهمية
      this.recentMalfunctions = [
        { id: 'MLF-2023-001', equipment: 'مكيف هواء مركزي', location: 'الطابق الثالث', type: 'ميكانيكي', date: '2023-06-15', status: 'قيد المعالجة', priority: 'عالي' },
        { id: 'MLF-2023-002', equipment: 'مولد كهربائي', location: 'غرفة المولدات', type: 'كهربائي', date: '2023-06-14', status: 'تم الإصلاح', priority: 'عالي' },
        { id: 'MLF-2023-003', equipment: 'مضخة مياه', location: 'غرفة المضخات', type: 'ميكانيكي', date: '2023-06-13', status: 'تم الإصلاح', priority: 'متوسط' },
        { id: 'MLF-2023-004', equipment: 'نظام إنذار الحريق', location: 'المبنى الرئيسي', type: 'إلكتروني', date: '2023-06-12', status: 'قيد المعالجة', priority: 'حرج' },
        { id: 'MLF-2023-005', equipment: 'مصعد', location: 'المبنى الرئيسي', type: 'كهربائي', date: '2023-06-11', status: 'تم الإصلاح', priority: 'عالي' }
      ];

      // بيانات طلبات الصيانة الأخيرة الوهمية
      this.recentRequests = [
        { id: 'REQ-2023-001', equipment: 'مكيف هواء', department: 'قسم المبيعات', requestedBy: 'أحمد محمد', date: '2023-06-15', status: 'مكتمل' },
        { id: 'REQ-2023-002', equipment: 'طابعة', department: 'قسم المحاسبة', requestedBy: 'سارة أحمد', date: '2023-06-14', status: 'مكتمل' },
        { id: 'REQ-2023-003', equipment: 'جهاز كمبيوتر', department: 'قسم تكنولوجيا المعلومات', requestedBy: 'محمد علي', date: '2023-06-13', status: 'قيد التنفيذ' },
        { id: 'REQ-2023-004', equipment: 'نظام الإضاءة', department: 'قسم الموارد البشرية', requestedBy: 'فاطمة خالد', date: '2023-06-12', status: 'قيد التنفيذ' },
        { id: 'REQ-2023-005', equipment: 'مضخة مياه', department: 'قسم الخدمات', requestedBy: 'عمر حسن', date: '2023-06-11', status: 'مكتمل' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      equipmentStats: this.http.get<any>('api/maintenance/dashboard/equipment'),
      malfunctionStats: this.http.get<any>('api/maintenance/dashboard/malfunctions'),
      requestStats: this.http.get<any>('api/maintenance/dashboard/requests'),
      recentMalfunctions: this.http.get<any>('api/maintenance/dashboard/recent-malfunctions'),
      recentRequests: this.http.get<any>('api/maintenance/dashboard/recent-requests')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalEquipment = results.equipmentStats.total;
        this.activeEquipment = results.equipmentStats.active;
        this.equipmentNeedsMaintenance = results.equipmentStats.needsMaintenance;
        this.equipmentUnderMaintenance = results.equipmentStats.underMaintenance;

        this.totalMalfunctions = results.malfunctionStats.total;
        this.resolvedMalfunctions = results.malfunctionStats.resolved;
        this.pendingMalfunctions = results.malfunctionStats.pending;
        this.criticalMalfunctions = results.malfunctionStats.critical;

        this.totalRequests = results.requestStats.total;
        this.completedRequests = results.requestStats.completed;
        this.pendingRequests = results.requestStats.pending;
        this.rejectedRequests = results.requestStats.rejected;

        this.recentMalfunctions = results.recentMalfunctions;
        this.recentRequests = results.recentRequests;

        // بيانات الرسوم البيانية
        this.malfunctionsByTypeData = results.malfunctionStats.byType;
        this.maintenanceRequestsData = results.requestStats.byMonth;
        this.equipmentStatusData = [
          { status: 'نشط', count: this.activeEquipment },
          { status: 'يحتاج صيانة', count: this.equipmentNeedsMaintenance },
          { status: 'تحت الصيانة', count: this.equipmentUnderMaintenance }
        ];
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة العطل
  getMalfunctionStatusClass(status: string): string {
    switch (status) {
      case 'تم الإصلاح':
        return 'badge-light-success';
      case 'قيد المعالجة':
        return 'badge-light-warning';
      case 'معلق':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون أولوية العطل
  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'حرج':
        return 'badge-light-danger';
      case 'عالي':
        return 'badge-light-warning';
      case 'متوسط':
        return 'badge-light-primary';
      case 'منخفض':
        return 'badge-light-info';
      default:
        return 'badge-light-secondary';
    }
  }

  // طريقة مساعدة لتحديد لون حالة طلب الصيانة
  getRequestStatusClass(status: string): string {
    switch (status) {
      case 'مكتمل':
        return 'badge-light-success';
      case 'قيد التنفيذ':
        return 'badge-light-warning';
      case 'مرفوض':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }
}
