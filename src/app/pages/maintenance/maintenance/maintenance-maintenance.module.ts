import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {CalibrationDeviceComponent} from './calibration-device/calibration-device.component';
import {DepartmentConsumptionRateComponent} from './department-consumption-rate/department-consumption-rate.component';
import {MaintenanceRequestsComponent} from './maintenance-requests/maintenance-requests.component';
import {MalfunctionsComponent} from './malfunctions/malfunctions.component';
import {PeriodicTrafficComponent} from './periodic-traffic/periodic-traffic.component';
import {MaintenanceMaintenanceRoutingModule} from "./maintenance-maintenance-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
  declarations: [
    MalfunctionsComponent,
    DepartmentConsumptionRateComponent,
    MaintenanceRequestsComponent,
    PeriodicTrafficComponent,
    CalibrationDeviceComponent,
  ],
  imports: [MaintenanceMaintenanceRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class MaintenanceMaintenanceModule {}
