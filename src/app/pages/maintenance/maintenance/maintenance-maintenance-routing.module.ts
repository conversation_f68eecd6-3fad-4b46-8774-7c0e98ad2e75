import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CalibrationDeviceComponent} from './calibration-device/calibration-device.component';
import {DepartmentConsumptionRateComponent} from './department-consumption-rate/department-consumption-rate.component';
import {MaintenanceRequestsComponent} from './maintenance-requests/maintenance-requests.component';
import {MalfunctionsComponent} from './malfunctions/malfunctions.component';
import {PeriodicTrafficComponent} from './periodic-traffic/periodic-traffic.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'malfunctions',
        pathMatch: "full"
    },

    {
        path: 'malfunctions',
        component: MalfunctionsComponent
    },
    {
        path: 'department_consumption_rate',
        component: DepartmentConsumptionRateComponent
    },
    {
        path: 'maintenance_requests',
        component: MaintenanceRequestsComponent
    },
    {
        path: 'periodic_traffic',
        component: PeriodicTrafficComponent
    },

    {
        path: 'calibration_device',
        component: CalibrationDeviceComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class MaintenanceMaintenanceRoutingModule {
}
