import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {maintenanceHomeComponent} from './maintenance-home/maintenance-home.component';


const routes: Routes = [

    {
        path: '',
        component: maintenanceHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/maintenance-configuration.module').then((m) => m.MaintenanceConfigurationModule),
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/maintenance-configuration.module').then((m) => m.MaintenanceConfigurationModule),
    },
    {
        path: 'preventive',
        loadChildren: () =>
            import('./preventive/maintenance-preventive.module').then((m) => m.MaintenancePreventiveModule),
    },
    {
        path: 'contracts',
        loadChildren: () =>
            import('./contracts/maintenance-contracts.module').then((m) => m.MaintenanceContractsModule),
    },
    {
        path: 'maintenance',
        loadChildren: () =>
            import('./maintenance/maintenance-maintenance.module').then((m) => m.MaintenanceMaintenanceModule),
    }
    ,
    {
        path: 'reports',
        loadChildren: () =>
            import('./reports/maintenance-reports.module').then((m) => m.MaintenanceReportsModule),
    }

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class maintenanceRoutingModule {
}
