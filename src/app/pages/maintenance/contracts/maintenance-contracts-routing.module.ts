import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CallTypesComponent} from './call-types/call-types.component';
import {ContractsTypesComponent} from './contracts-types/contracts-types.component';
import {DeviceMalfunctionComponent} from './device-malfunction/device-malfunction.component';
import {DiagnosisStepsComponent} from './diagnosis-steps/diagnosis-steps.component';
import {FaultsStagesComponent} from './faults-stages/faults-stages.component';
import {JobOrderComponent} from './job-order/job-order.component';
import {MaintenanceContractComponent} from './maintenance-contract/maintenance-contract.component';
import {OpenCallComponent} from './open-call/open-call.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'contracts_types',
        pathMatch: "full"
    },
    {
        path: 'contracts_types',
        component: ContractsTypesComponent
    },

    {
        path: 'call_types',
        component: CallTypesComponent
    },
    {
        path: 'device_malfunction',
        component: DeviceMalfunctionComponent
    },

    {
        path: 'faults_stages',
        component: FaultsStagesComponent
    },

    {
        path: 'diagnosis__steps',
        component: DiagnosisStepsComponent
    },

    {
        path: 'maintenance_contract',
        component: MaintenanceContractComponent
    },

    {
        path: 'open_call',
        component: OpenCallComponent
    },

    {
        path: 'job_order',
        component: JobOrderComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class MaintenanceContractsRoutingModule {
}
