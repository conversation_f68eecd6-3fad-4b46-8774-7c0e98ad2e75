import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {CallTypesComponent} from './call-types/call-types.component';
import {ContractsTypesComponent} from './contracts-types/contracts-types.component';
import {DeviceMalfunctionComponent} from './device-malfunction/device-malfunction.component';
import {DiagnosisStepsComponent} from './diagnosis-steps/diagnosis-steps.component';
import {FaultsStagesComponent} from './faults-stages/faults-stages.component';
import {JobOrderComponent} from './job-order/job-order.component';
import {MaintenanceContractComponent} from './maintenance-contract/maintenance-contract.component';
import {OpenCallComponent} from './open-call/open-call.component';
import {MaintenanceContractsRoutingModule} from "./maintenance-contracts-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
  declarations: [
    ContractsTypesComponent,
    CallTypesComponent,
    DeviceMalfunctionComponent,
    FaultsStagesComponent,
    DiagnosisStepsComponent,
    MaintenanceContractComponent,
    OpenCallComponent,
    JobOrderComponent,
  ],
  imports: [MaintenanceContractsRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class MaintenanceContractsModule {}
