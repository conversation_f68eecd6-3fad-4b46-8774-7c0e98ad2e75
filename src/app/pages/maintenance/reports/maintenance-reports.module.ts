import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {CallsReportComponent} from './calls-report/calls-report.component';
import {ClientMalfunctionsReportComponent} from './client-malfunctions-report/client-malfunctions-report.component';
import {MaintenanceReportsRoutingModule} from "./maintenance-reports-routing.module";
import {SharedModule} from "../../shared/shared.module";

@NgModule({
  declarations: [
    CallsReportComponent,
    ClientMalfunctionsReportComponent,
  ],
  imports: [MaintenanceReportsRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class MaintenanceReportsModule {}
