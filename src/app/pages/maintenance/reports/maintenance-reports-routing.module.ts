import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CallsReportComponent} from './calls-report/calls-report.component';
import {ClientMalfunctionsReportComponent} from './client-malfunctions-report/client-malfunctions-report.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'calls_report',
        pathMatch: "full"
    },
    {
        path: 'calls_report',
        component: CallsReportComponent
    },
    {
        path: 'client_malfunctions_report',
        component: ClientMalfunctionsReportComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class MaintenanceReportsRoutingModule {
}
