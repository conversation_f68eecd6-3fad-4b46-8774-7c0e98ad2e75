
<!-- بداية لوحة تحكم التصنيع -->
<div class="d-flex flex-column flex-column-fluid">
  <!-- بداية العنوان -->
  <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
      <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
          {{ 'COMMON.ManufacturingDashboard' | translate }}
        </h1>
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
          <li class="breadcrumb-item text-muted">
            <a href="/dashboard" class="text-muted text-hover-primary">{{ 'COMMON.ManufacturingHome' | translate }}</a>
          </li>
          <li class="breadcrumb-item">
            <span class="bullet bg-gray-400 w-5px h-2px"></span>
          </li>
          <li class="breadcrumb-item text-muted">{{ 'COMMON.ManufacturingModule' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- نهاية العنوان -->

  <!-- بداية المحتوى -->
  <div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">

      <!-- بداية صف إحصائيات أوامر التصنيع -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي أوامر التصنيع -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalOrders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingTotalOrders' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-clipboard-list text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أوامر التصنيع النشطة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(activeOrders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingActiveOrders' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-cogs text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أوامر التصنيع المكتملة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(completedOrders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingCompletedOrders' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-check-circle text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- أوامر التصنيع المعلقة -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(pendingOrders) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingPendingOrders' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-clock text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات أوامر التصنيع -->

      <!-- بداية صف إحصائيات الإنتاج -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- إجمالي الإنتاج -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(totalProduction) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingTotalProduction' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-primary">
                  <i class="fa fa-industry text-primary fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الإنتاج اليومي -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(dailyProduction) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingDailyProduction' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-success">
                  <i class="fa fa-calendar-day text-success fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الإنتاج الأسبوعي -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(weeklyProduction) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingWeeklyProduction' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-info">
                  <i class="fa fa-calendar-week text-info fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- الإنتاج الشهري -->
        <div class="col-md-3 col-lg-3 col-xl-3 col-xxl-3 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <div class="card-title d-flex flex-column">
                <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{ formatNumber(monthlyProduction) }}</span>
                <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingMonthlyProduction' | translate }}</span>
              </div>
            </div>
            <div class="card-body d-flex flex-column justify-content-end pe-0">
              <div class="symbol symbol-50px me-2">
                <span class="symbol-label bg-light-warning">
                  <i class="fa fa-calendar-alt text-warning fs-2x"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف إحصائيات الإنتاج -->

      <!-- بداية صف الرسوم البيانية والإجراءات السريعة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- الرسم البياني لاتجاه الإنتاج -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ManufacturingProductionTrend' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingLastSixMonths' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ManufacturingViewDetails' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget1></app-charts-widget1>
            </div>
          </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ManufacturingQuickActions' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingCommonOperations' | translate }}</span>
              </h3>
            </div>
            <div class="card-body pt-5">
              <div class="d-flex flex-column gap-5">
                <div *ngFor="let link of quickLinks" class="d-flex align-items-center">
                  <div class="symbol symbol-40px me-4">
                    <div class="symbol-label fs-2 fw-semibold bg-light-primary text-primary">
                      <i class="fa {{ link.icon }}"></i>
                    </div>
                  </div>
                  <div class="d-flex flex-column">
                    <a [routerLink]="link.route" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ link.title }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف الرسوم البيانية والإجراءات السريعة -->

      <!-- بداية صف أوامر التصنيع الأخيرة وتوزيع الإنتاج -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <!-- أوامر التصنيع الأخيرة -->
        <div class="col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ManufacturingRecentOrders' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingLastFiveOrders' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ManufacturingViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingOrderID' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.ManufacturingProduct' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingQuantity' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingStartDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingDueDate' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.ManufacturingProgress' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let order of recentOrders">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ order.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ order.product }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ order.quantity }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ order.startDate }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ order.dueDate }}</span>
                      </td>
                      <td>
                        <div class="d-flex flex-column w-100 me-2">
                          <div class="d-flex flex-stack mb-2">
                            <span class="text-muted me-2 fs-7 fw-bold">{{ order.progress }}%</span>
                          </div>
                          <div class="progress h-6px w-100">
                            <div class="progress-bar" [ngClass]="order.progress < 50 ? 'bg-warning' : (order.progress < 80 ? 'bg-primary' : 'bg-success')" role="progressbar" [style.width.%]="order.progress"></div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getOrderStatusClass(order.status)">{{ order.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- توزيع الإنتاج حسب المراحل -->
        <div class="col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-md-5 mb-xl-10">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ManufacturingProductionByStage' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingCurrentDistribution' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ManufacturingViewReport' | translate }}</button>
              </div>
            </div>
            <div class="card-body">
              <app-charts-widget2></app-charts-widget2>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف أوامر التصنيع الأخيرة وتوزيع الإنتاج -->

      <!-- بداية صف طلبات المواد الأخيرة -->
      <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        <div class="col-xl-12">
          <div class="card card-flush h-md-100">
            <div class="card-header pt-5">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">{{ 'COMMON.ManufacturingRecentMaterialRequests' | translate }}</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ManufacturingLastFiveRequests' | translate }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ManufacturingViewAll' | translate }}</button>
              </div>
            </div>
            <div class="card-body pt-5">
              <div class="table-responsive">
                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                  <thead>
                    <tr class="fw-bold text-muted">
                      <th class="min-w-125px">{{ 'COMMON.ManufacturingRequestID' | translate }}</th>
                      <th class="min-w-150px">{{ 'COMMON.ManufacturingMaterial' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingQuantity' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingUnit' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.ManufacturingRequestDate' | translate }}</th>
                      <th class="min-w-125px">{{ 'COMMON.ManufacturingRequiredDate' | translate }}</th>
                      <th class="min-w-100px">{{ 'COMMON.ManufacturingStatus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let request of recentMaterialRequests">
                      <td>
                        <span class="text-dark fw-bold text-hover-primary d-block fs-6">{{ request.id }}</span>
                      </td>
                      <td>
                        <span class="text-dark fw-bold d-block fs-6">{{ request.material }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.quantity }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.unit }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.requestDate }}</span>
                      </td>
                      <td>
                        <span class="text-muted fw-semibold text-muted d-block fs-6">{{ request.requiredDate }}</span>
                      </td>
                      <td>
                        <span [ngClass]="'badge ' + getMaterialRequestStatusClass(request.status)">{{ request.status }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- نهاية صف طلبات المواد الأخيرة -->

    </div>
  </div>
  <!-- نهاية المحتوى -->
</div>
<!-- نهاية لوحة تحكم التصنيع -->
