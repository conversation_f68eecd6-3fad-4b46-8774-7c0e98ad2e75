import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
    selector: 'app-manufacturing-home',
    templateUrl: './manufacturing-home.component.html',
    styleUrls: ['./manufacturing-home.component.scss'],
    standalone: false
})
export class manufacturingHomeComponent implements OnInit {
  // إحصائيات أوامر التصنيع
  totalOrders: number = 0;
  activeOrders: number = 0;
  completedOrders: number = 0;
  pendingOrders: number = 0;

  // إحصائيات الإنتاج
  totalProduction: number = 0;
  dailyProduction: number = 0;
  weeklyProduction: number = 0;
  monthlyProduction: number = 0;

  // إحصائيات المواد
  totalMaterials: number = 0;
  availableMaterials: number = 0;
  reservedMaterials: number = 0;
  lowStockMaterials: number = 0;

  // بيانات الرسوم البيانية
  productionByStageData: any[] = [];
  productionTrendData: any[] = [];
  materialUsageData: any[] = [];

  // أوامر التصنيع الأخيرة
  recentOrders: any[] = [];

  // طلبات المواد الأخيرة
  recentMaterialRequests: any[] = [];

  // حالات التحميل
  isLoading: boolean = true;

  // الروابط السريعة
  quickLinks = [
    { title: 'إضافة أمر تصنيع', icon: 'fa-plus-circle', route: '/manufacturing/orders/orders' },
    { title: 'طلب مواد', icon: 'fa-boxes', route: '/manufacturing/orders/material_requests' },
    { title: 'متابعة مراحل الإنتاج', icon: 'fa-tasks', route: '/manufacturing/orders/production_stages_follow' },
    { title: 'خطة التصنيع', icon: 'fa-calendar-alt', route: '/manufacturing/orders/manufacturing_plan' },
    { title: 'تكلفة أمر شغل', icon: 'fa-calculator', route: '/manufacturing/orders/order_cost' }
  ];

  constructor(
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // لأغراض العرض، سنستخدم بيانات وهمية
    // في التنفيذ الحقيقي، ستقوم باستدعاء نقاط نهاية API الخاصة بك

    // محاكاة استدعاءات API
    setTimeout(() => {
      // بيانات أوامر التصنيع الوهمية
      this.totalOrders = 185;
      this.activeOrders = 68;
      this.completedOrders = 92;
      this.pendingOrders = 25;

      // بيانات الإنتاج الوهمية
      this.totalProduction = 12500;
      this.dailyProduction = 450;
      this.weeklyProduction = 3200;
      this.monthlyProduction = 12500;

      // بيانات المواد الوهمية
      this.totalMaterials = 320;
      this.availableMaterials = 245;
      this.reservedMaterials = 58;
      this.lowStockMaterials = 17;

      // بيانات الرسوم البيانية الوهمية
      this.productionByStageData = [
        { stage: 'قطع', count: 35 },
        { stage: 'تجميع', count: 28 },
        { stage: 'لحام', count: 15 },
        { stage: 'طلاء', count: 12 },
        { stage: 'فحص', count: 10 }
      ];

      this.productionTrendData = [
        { month: 'يناير', production: 9500 },
        { month: 'فبراير', production: 10200 },
        { month: 'مارس', production: 11000 },
        { month: 'أبريل', production: 10800 },
        { month: 'مايو', production: 11500 },
        { month: 'يونيو', production: 12500 }
      ];

      this.materialUsageData = [
        { material: 'معدن', usage: 45 },
        { material: 'بلاستيك', usage: 25 },
        { material: 'خشب', usage: 15 },
        { material: 'زجاج', usage: 10 },
        { material: 'أخرى', usage: 5 }
      ];

      // بيانات أوامر التصنيع الأخيرة الوهمية
      this.recentOrders = [
        { id: 'MFG-2023-001', product: 'كرسي مكتبي', quantity: 50, startDate: '2023-06-10', dueDate: '2023-06-25', progress: 75, status: 'قيد التنفيذ' },
        { id: 'MFG-2023-002', product: 'طاولة اجتماعات', quantity: 10, startDate: '2023-06-08', dueDate: '2023-06-20', progress: 90, status: 'قيد التنفيذ' },
        { id: 'MFG-2023-003', product: 'خزانة ملفات', quantity: 30, startDate: '2023-06-05', dueDate: '2023-06-18', progress: 100, status: 'مكتمل' },
        { id: 'MFG-2023-004', product: 'مكتب مدير', quantity: 5, startDate: '2023-06-12', dueDate: '2023-06-30', progress: 40, status: 'قيد التنفيذ' },
        { id: 'MFG-2023-005', product: 'رف كتب', quantity: 25, startDate: '2023-06-01', dueDate: '2023-06-15', progress: 100, status: 'مكتمل' }
      ];

      // بيانات طلبات المواد الأخيرة الوهمية
      this.recentMaterialRequests = [
        { id: 'MAT-2023-001', material: 'ألواح خشب', quantity: 100, unit: 'لوح', requestDate: '2023-06-14', requiredDate: '2023-06-18', status: 'معتمد' },
        { id: 'MAT-2023-002', material: 'مسامير', quantity: 5000, unit: 'قطعة', requestDate: '2023-06-13', requiredDate: '2023-06-17', status: 'معتمد' },
        { id: 'MAT-2023-003', material: 'دهان', quantity: 50, unit: 'لتر', requestDate: '2023-06-12', requiredDate: '2023-06-16', status: 'قيد المراجعة' },
        { id: 'MAT-2023-004', material: 'زجاج', quantity: 30, unit: 'لوح', requestDate: '2023-06-11', requiredDate: '2023-06-20', status: 'معتمد' },
        { id: 'MAT-2023-005', material: 'مقابض أبواب', quantity: 200, unit: 'قطعة', requestDate: '2023-06-10', requiredDate: '2023-06-15', status: 'مستلم' }
      ];

      this.isLoading = false;
    }, 1000);

    // في التنفيذ الحقيقي، ستستخدم استدعاءات API مثل:
    /*
    forkJoin({
      orderStats: this.http.get<any>('api/manufacturing/dashboard/orders'),
      productionStats: this.http.get<any>('api/manufacturing/dashboard/production'),
      materialStats: this.http.get<any>('api/manufacturing/dashboard/materials'),
      recentOrders: this.http.get<any>('api/manufacturing/dashboard/recent-orders'),
      recentMaterialRequests: this.http.get<any>('api/manufacturing/dashboard/recent-material-requests')
    }).pipe(
      catchError(error => {
        console.error('Error loading dashboard data', error);
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(results => {
      if (results) {
        // معالجة النتائج
        this.totalOrders = results.orderStats.total;
        this.activeOrders = results.orderStats.active;
        this.completedOrders = results.orderStats.completed;
        this.pendingOrders = results.orderStats.pending;

        this.totalProduction = results.productionStats.total;
        this.dailyProduction = results.productionStats.daily;
        this.weeklyProduction = results.productionStats.weekly;
        this.monthlyProduction = results.productionStats.monthly;

        this.totalMaterials = results.materialStats.total;
        this.availableMaterials = results.materialStats.available;
        this.reservedMaterials = results.materialStats.reserved;
        this.lowStockMaterials = results.materialStats.lowStock;

        this.recentOrders = results.recentOrders;
        this.recentMaterialRequests = results.recentMaterialRequests;

        // بيانات الرسوم البيانية
        this.productionByStageData = results.productionStats.byStage;
        this.productionTrendData = results.productionStats.trend;
        this.materialUsageData = results.materialStats.usage;
      }
      this.isLoading = false;
    });
    */
  }

  // طريقة مساعدة لتنسيق الأرقام
  formatNumber(amount: number): string {
    return amount.toLocaleString('ar-SA');
  }

  // طريقة مساعدة لتحديد لون حالة أمر التصنيع
  getOrderStatusClass(status: string): string {
    switch (status) {
      case 'مكتمل':
        return 'badge-light-success';
      case 'قيد التنفيذ':
        return 'badge-light-warning';
      case 'معلق':
        return 'badge-light-danger';
      default:
        return 'badge-light-info';
    }
  }

  // طريقة مساعدة لتحديد لون حالة طلب المواد
  getMaterialRequestStatusClass(status: string): string {
    switch (status) {
      case 'معتمد':
        return 'badge-light-success';
      case 'قيد المراجعة':
        return 'badge-light-warning';
      case 'مرفوض':
        return 'badge-light-danger';
      case 'مستلم':
        return 'badge-light-primary';
      default:
        return 'badge-light-info';
    }
  }
}
