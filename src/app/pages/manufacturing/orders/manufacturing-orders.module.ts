import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {ReceiptOrdersComponent} from './receipt-orders/receipt-orders.component';
import {OrderCostComponent} from './order-cost/order-cost.component';
import {OrdersComponent} from './orders/orders.component';
import {ProductionStagesFollowComponent} from './production-stages-follow/production-stages-follow.component';
import {AssemblyAndDisassemblyComponent} from './assembly-and-disassembly/assembly-and-disassembly.component';
import {MaterialRequestsComponent} from './material-requests/material-requests.component';
import {ManufacturingPlanComponent} from './manufacturing-plan/manufacturing-plan.component';
import {SharedModule} from '../../shared/shared.module';
import {manufacturingOrdersRoutingModule} from "./manufacturing-orders-routing.module";

@NgModule({
    declarations: [
        ReceiptOrdersComponent,
        OrderCostComponent,
        OrdersComponent,
        ProductionStagesFollowComponent,
        AssemblyAndDisassemblyComponent,
        MaterialRequestsComponent,
        ManufacturingPlanComponent,
    ],
    imports: [
        manufacturingOrdersRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class ManufacturingOrdersModule {
}
