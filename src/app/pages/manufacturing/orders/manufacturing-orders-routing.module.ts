import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AssemblyAndDisassemblyComponent} from './assembly-and-disassembly/assembly-and-disassembly.component';
import {ManufacturingPlanComponent} from './manufacturing-plan/manufacturing-plan.component';
import {MaterialRequestsComponent} from './material-requests/material-requests.component';
import {OrderCostComponent} from './order-cost/order-cost.component';
import {OrdersComponent} from './orders/orders.component';
import {ProductionStagesFollowComponent} from './production-stages-follow/production-stages-follow.component';
import {ReceiptOrdersComponent} from './receipt-orders/receipt-orders.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'receipt_orders',
        pathMatch: 'full'
    },
    {
        path: 'receipt_orders',
        component: ReceiptOrdersComponent
    },

    {
        path: 'order_cost',
        component: OrderCostComponent
    },

    {
        path: 'orders',
        component: OrdersComponent
    },
    {
        path: 'production_stages_follow',
        component: ProductionStagesFollowComponent
    },

    {
        path: 'assembly_and_disassembly',
        component: AssemblyAndDisassemblyComponent
    },

    {
        path: 'material_requests',
        component: MaterialRequestsComponent
    },
    {
        path: 'manufacturing_plan',
        component: ManufacturingPlanComponent
    },


];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class manufacturingOrdersRoutingModule {
}
