import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {manufacturingHomeComponent} from './manufacturing-home/manufacturing-home.component';


const routes: Routes = [

    {
        path: '',
        component: manufacturingHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/manufacturing-configuration.module').then((m) => m.ManufacturingConfigurationModule),
    }, {
        path: 'orders',
        loadChildren: () =>
            import('./orders/manufacturing-orders.module').then((m) => m.ManufacturingOrdersModule),
    }, {
        path: 'reports',
        loadChildren: () =>
            import('./reports/manufacturing-reports.module').then((m) => m.ManufacturingReportsModule),
    },

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class manufacturingRoutingModule {
}
