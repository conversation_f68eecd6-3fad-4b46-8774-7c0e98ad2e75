import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {manufacturingConfigurationRoutingModule} from "./manufacturing-configuration-routing.module";
import {ManufacturingStagesComponent} from "./manufacturing-stages/manufacturing-stages.component";

@NgModule({
    declarations: [
        ManufacturingStagesComponent,
    ],
    imports: [
        manufacturingConfigurationRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class ManufacturingConfigurationModule {
}
