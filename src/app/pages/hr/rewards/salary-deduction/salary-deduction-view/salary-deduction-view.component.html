<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.SalaryDeduction" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر التاريخ واسم الموظف -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>

          <!-- سطر نوع الخصم والرقم التسلسلي -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="deductionTypeId" class="form-label">
                {{ "COMMON.DeductionType" | translate }}
              </label>
              <ng-select
                id="deductionTypeId"
                formControlName="deductionTypeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="deductionTypes"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="serialId" class="form-label">
                {{ "COMMON.Serial" | translate }}
              </label>
              <input
                id="serialId"
                type="number"
                formControlName="serialId"
                class="form-control"
                style="background-color: inherit"
                min="1"
              />
            </div>
          </div>

          <!-- سطر إجمالي الراتب ونوع الخصم -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.TotalSalary" | translate }}</label>
              <div class="d-flex align-items-center mt-2">
                <label class="switch">
                  <input type="checkbox" formControlName="TotalSalary" />
                  <span class="slider"></span>
                </label>
                <span class="ms-3">{{ "COMMON.IncludeInTotal" | translate }}</span>
              </div>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.DeductionCategory" | translate }}</label>
              <div class="d-flex flex-wrap gap-3 mt-2">
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="deductionOptionControl"
                    value="penalty"
                    class="me-2"
                  />
                  {{ "COMMON.Penalty" | translate }}
                </label>
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="deductionOptionControl"
                    value="medical"
                    class="me-2"
                  />
                  {{ "COMMON.Medical" | translate }}
                </label>
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="deductionOptionControl"
                    value="salary payment"
                    class="me-2"
                  />
                  {{ "COMMON.SalaryPayment" | translate }}
                </label>
              </div>
              <div class="d-flex flex-wrap gap-3 mt-2">
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="deductionOptionControl"
                    value="sickness"
                    class="me-2"
                  />
                  {{ "COMMON.Sickness" | translate }}
                </label>
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="deductionOptionControl"
                    value="loans"
                    class="me-2"
                  />
                  {{ "COMMON.Advance" | translate }}
                </label>
              </div>
            </div>
          </div>

          <!-- سطر القيمة والعدد -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="valueID" class="form-label">
                {{ "COMMON.Value" | translate }}
              </label>
              <input
                id="valueID"
                type="number"
                formControlName="valueID"
                class="form-control"
                style="background-color: inherit"
                min="1"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="numberId" class="form-label">
                {{ "COMMON.Number" | translate }}
              </label>
              <input
                id="numberId"
                type="number"
                formControlName="numberId"
                class="form-control"
                style="background-color: inherit"
                min="1"
              />
            </div>
          </div>

          <!-- سطر الخصم من الراتب والملاحظات -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="deductionFromSalaryId" class="form-label">
                {{ "COMMON.DeductionFromSalary" | translate }}
              </label>
              <input
                id="deductionFromSalaryId"
                type="number"
                formControlName="deductionFromSalaryId"
                class="form-control"
                style="background-color: inherit"
                min="1"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="notesId" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                id="notesId"
                type="text"
                formControlName="notesId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
