.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.switch input:checked + .slider {
  background-color: #4caf50;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.switch input:checked + .slider:before {
  transform: translateX(26px);
}

/* تحسين بسيط وثابت للليبل والحقول */
.form-label {
  display: inline-block;
  width: 150px;
  min-width: 150px;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* تحسين المسافات بشكل بسيط */
.main-inputs .row {
  margin-bottom: 1.5rem;
}

.main-inputs .row:last-child {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 1rem;
}

/* تحسين بسيط للحقول */
.form-control {
  width: 100%;
}

/* تحسين ng-select بشكل بسيط */
::ng-deep ng-select {
  width: 100%;
}

/* تحسين أزرار الراديو */
.d-flex.flex-wrap.gap-3 label {
  margin-bottom: 0.5rem;
  font-weight: normal;
  cursor: pointer;
}

.d-flex.flex-wrap.gap-3 label input[type="radio"] {
  margin-right: 0.5rem;
}

[dir="rtl"] .d-flex.flex-wrap.gap-3 label input[type="radio"] {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* تحسين مظهر المفتاح */
.d-flex.align-items-center .switch {
  margin-right: 0.5rem;
}

[dir="rtl"] .d-flex.align-items-center .switch {
  margin-right: 0;
  margin-left: 0.5rem;
}
