import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BenefitDeductionApplicationComponent } from './benefit-deduction-application/benefit-deduction-application.component';
import { SalaryAdditionsComponent } from './salary-additions/salary-additions.component';
import { SalaryDeductionComponent } from './salary-deduction/salary-deduction.component';
import { BenefitDeductionApplicationViewComponent } from './benefit-deduction-application/benefit-deduction-application-view/benefit-deduction-application-view.component';
import { SalaryAdditionsViewComponent } from './salary-additions/salary-additions-view/salary-additions-view.component';
import { SalaryDeductionViewComponent } from './salary-deduction/salary-deduction-view/salary-deduction-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'benefit_deduction_application',
    pathMatch: 'full',
  },
  {
    path: 'benefit_deduction_application_view/:id',
    component: BenefitDeductionApplicationViewComponent,
  },
  {
    path: 'benefit_deduction_application_view',
    component: BenefitDeductionApplicationViewComponent,
  },
  {
    path: 'benefit_deduction_application',
    component: BenefitDeductionApplicationComponent,
  },
  {
    path: 'salary_additions_view/:id',
    component: SalaryAdditionsViewComponent,
  },
  {
    path: 'salary_additions_view',
    component: SalaryAdditionsViewComponent,
  },
  {
    path: 'salary_additions',
    component: SalaryAdditionsComponent,
  },
  {
    path: 'salary_deduction_view/:id',
    component: SalaryDeductionViewComponent,
  },
  {
    path: 'salary_deduction_view',
    component: SalaryDeductionViewComponent,
  },
  {
    path: 'salary_deduction',
    component: SalaryDeductionComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrRewardsRoutingModule {}
