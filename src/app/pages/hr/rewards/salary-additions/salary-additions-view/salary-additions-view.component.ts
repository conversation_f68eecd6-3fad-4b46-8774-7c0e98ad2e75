import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Rewards } from '../../Rewards.service';

@Component({
  selector: 'app-salary-additions-view',
  templateUrl: './salary-additions-view.component.html',
  styleUrls: ['./salary-additions-view.component.css'],
  standalone: false,
})
export class SalaryAdditionsViewComponent implements OnInit, OnDestroy {
  moduleName = 'HR.SalaryAdditions';
  roles: any[] = [];
  employees: any[];
  RewardTypes: any[] = [];
  costCenter: any[];
  incentives: any[] = [];

  employeeId: number = 0;
  costId: any = 0;
  incentiveId: number = 0;
  valueId: number = 0;
  notesId: number = 0;
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];
  timeId: number = 0;
  _currentPage = 1;
  data: any[] = [];
  editorOptions: any;
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  currentFilter: any;
  itemsCount = 0;
  pagesCount = 0;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: Rewards,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    const currentTime = new Date().toLocaleTimeString();

    this.newdata = this.fb.group({
      employeeId: [null, Validators.required],
      dateValue: [today, Validators.required],
    });
    this.subscriptions.add(
      myService.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      myService.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.costCenter = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.newdata
      .get('fromTimeValue')
      ?.valueChanges.subscribe(() => this.calculateTimeDifference());
    this.newdata
      .get('toTimeValue')
      ?.valueChanges.subscribe(() => this.calculateTimeDifference());

    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    this.newdata.get('basicSalary')?.setValue(item.basicSalary);
    this.newdata.get('changedSalary')?.setValue(item.changedSalary);
  }
  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(
      this.myService.list('', value).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          MenuComponent.reinitialization();
        }
      })
    );
  }

  get currentPage() {
    return this._currentPage;
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/rewards/salary_additions']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/rewards/salary_additions']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  calculateTimeDifference() {
    const fromTime = this.newdata.get('fromTimeValue')?.value;
    const toTime = this.newdata.get('toTimeValue')?.value;

    if (fromTime && toTime) {
      const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
      const [toHours, toMinutes] = toTime.split(':').map(Number);

      const fromTotalMinutes = fromHours * 60 + fromMinutes;
      const toTotalMinutes = toHours * 60 + toMinutes;

      const difference = toTotalMinutes - fromTotalMinutes;

      if (difference >= 0) {
        const hours = Math.floor(difference / 60);
        const minutes = difference % 60;
        this.newdata.get('timeId')?.setValue(`${hours}h ${minutes}m`);
      } else {
        this.newdata.get('timeId')?.setValue('Invalid Time');
      }
    } else {
      this.newdata.get('timeId')?.setValue('');
    }
  }

  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  uploadExcel() {
    var input = document.createElement('input');
    input.type = 'file';
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(
            this.myService.importExcel(fd).subscribe((e) => {
              if (e) {
                this.currentPage = 1;
              }
            })
          );
        }
      }
    };
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
