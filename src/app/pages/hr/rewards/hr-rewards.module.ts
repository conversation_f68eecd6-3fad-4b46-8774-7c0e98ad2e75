import { NgModule } from '@angular/core';
import { BenefitDeductionApplicationComponent } from './benefit-deduction-application/benefit-deduction-application.component';
import { SalaryAdditionsComponent } from './salary-additions/salary-additions.component';
import { SalaryDeductionComponent } from './salary-deduction/salary-deduction.component';
import { HrRewardsRoutingModule } from './hr-rewards-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { BenefitDeductionApplicationViewComponent } from './benefit-deduction-application/benefit-deduction-application-view/benefit-deduction-application-view.component';
import { SalaryAdditionsViewComponent } from './salary-additions/salary-additions-view/salary-additions-view.component';
import { SalaryDeductionViewComponent } from './salary-deduction/salary-deduction-view/salary-deduction-view.component';

@NgModule({
  declarations: [
    BenefitDeductionApplicationComponent,
    SalaryAdditionsComponent,
    SalaryDeductionComponent,
    BenefitDeductionApplicationViewComponent,
    SalaryAdditionsViewComponent,
    SalaryDeductionViewComponent,
  ],
  imports: [HrRewardsRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrRewardsModule {}
