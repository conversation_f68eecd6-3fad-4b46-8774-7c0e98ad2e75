<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.LatePermissions" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر التاريخ واسم الموظف -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>

          <!-- سطر الأوقات -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.FromHour" | translate
              }}</label>
              <input
                id="fromTime"
                type="time"
                formControlName="fromTimeValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.ToHour" | translate
              }}</label>
              <input
                id="toTime"
                type="time"
                formControlName="toTimeValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر المدة الزمنية ونوع التأخير -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="timeId" class="form-label">
                {{ "COMMON.Time" | translate }}
              </label>
              <input
                id="timeId"
                type="text"
                formControlName="timeId"
                class="form-control"
                style="background-color: inherit"
                readonly
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.DelayType" | translate }}</label>
              <div class="d-flex flex-wrap gap-3 mt-2">
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="delayOptionControl"
                    value="permitted"
                    class="me-2"
                  />
                  {{ "COMMON.DelayPermitted" | translate }}
                </label>
                <label class="d-flex align-items-center">
                  <input
                    type="radio"
                    formControlName="delayOptionControl"
                    value="permission"
                    class="me-2"
                  />
                  {{ "COMMON.DelayPermission" | translate }}
                </label>
              </div>
            </div>
          </div>

          <!-- سطر الحد الأقصى والأرصدة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="limitId" class="form-label">
                {{ "COMMON.Limit" | translate }}
              </label>
              <input
                id="limitId"
                type="text"
                formControlName="limitId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="balanceId" class="form-label">
                {{ "COMMON.Balance" | translate }}
              </label>
              <input
                id="balanceId"
                type="text"
                formControlName="balanceId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الأذونات السابقة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="previousId" class="form-label">
                {{ "COMMON.PreviousPermissions" | translate }}
              </label>
              <input
                id="previousId"
                type="text"
                formControlName="previousId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
