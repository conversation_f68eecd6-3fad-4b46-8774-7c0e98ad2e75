import { NgModule } from '@angular/core';
import { AbsencePermissionsComponent } from './absence-permissions/absence-permissions.component';
import { DownloadAttendanceComponent } from './download-attendance/download-attendance.component';
import { LatePermissionsComponent } from './late-permissions/late-permissions.component';
import { ManualAttendanceComponent } from './manual-attendance/manual-attendance.component';
import { MissionsComponent } from './missions/missions.component';
import { OverTimeComponent } from './over-time/over-time.component';
import { VacationComponent } from './vacation/vacation.component';
import { HrAttendanceRoutingModule } from './hr-attendance-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { Absence_permissions_viewComponent } from './absence-permissions/absence_permissions_view/absence_permissions_view.component';
import { MissionsViewComponent } from './missions/missions-view/missions-view.component';
import { OverTimeViewComponent } from './over-time/over-time-view/over-time-view.component';
import { ManualAttendanceViewComponent } from './manual-attendance/manual-attendance-view/manual-attendance-view.component';
import { VacationViewComponent } from './vacation/vacation-view/vacation-view.component';
import { LatePermissionsViewComponent } from './late-permissions/late-permissions-view/late-permissions-view.component';

@NgModule({
  declarations: [
    MissionsComponent,
    AbsencePermissionsComponent,
    DownloadAttendanceComponent,
    OverTimeComponent,
    ManualAttendanceComponent,
    VacationComponent,
    LatePermissionsComponent,
    Absence_permissions_viewComponent,
    MissionsViewComponent,
    OverTimeViewComponent,
    ManualAttendanceViewComponent,
    VacationViewComponent,
    LatePermissionsViewComponent
  ],
  imports: [HrAttendanceRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrAttendanceModule {}
