import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AbsencePermissionsComponent } from './absence-permissions/absence-permissions.component';
import { DownloadAttendanceComponent } from './download-attendance/download-attendance.component';
import { LatePermissionsComponent } from './late-permissions/late-permissions.component';
import { ManualAttendanceComponent } from './manual-attendance/manual-attendance.component';
import { MissionsComponent } from './missions/missions.component';
import { OverTimeComponent } from './over-time/over-time.component';
import { VacationComponent } from './vacation/vacation.component';
import { Absence_permissions_viewComponent } from './absence-permissions/absence_permissions_view/absence_permissions_view.component';
import { MissionsViewComponent } from './missions/missions-view/missions-view.component';
import { OverTimeViewComponent } from './over-time/over-time-view/over-time-view.component';
import { ManualAttendanceViewComponent } from './manual-attendance/manual-attendance-view/manual-attendance-view.component';
import { VacationViewComponent } from './vacation/vacation-view/vacation-view.component';
import { LatePermissionsViewComponent } from './late-permissions/late-permissions-view/late-permissions-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'missions',
    pathMatch: 'full',
  },
  {
    path: 'missions_view',
    component: MissionsViewComponent,
  },
  {
    path: 'missions_view/:id',
    component: MissionsViewComponent,
  },
  {
    path: 'missions',
    component: MissionsComponent,
  },
  {
    path: 'absence_permissions',
    component: AbsencePermissionsComponent,
  },
  {
    path: 'download_attendance',
    component: DownloadAttendanceComponent,
  },
  {
    path: 'over_time_view/:id',
    component: OverTimeViewComponent,
  },
  {
    path: 'over_time_view',
    component: OverTimeViewComponent,
  },
  {
    path: 'over_time',
    component: OverTimeComponent,
  },
  {
    path: 'manual_attendance_view/:id',
    component: ManualAttendanceViewComponent,
  },
  {
    path: 'manual_attendance_view',
    component: ManualAttendanceViewComponent,
  },
  {
    path: 'manual_attendance',
    component: ManualAttendanceComponent,
  },
  {
    path: 'absence_permissions_view',
    component: Absence_permissions_viewComponent,
  },
  {
    path: 'absence_permissions_view/:id',
    component: Absence_permissions_viewComponent,
  },
  {
    path: 'vacation_view/:id',
    component: VacationViewComponent,
  },
  {
    path: 'vacation_view',
    component: VacationViewComponent,
  },
  {
    path: 'vacation',
    component: VacationComponent,
  },
  {
    path: 'late_permissions_view/:id',
    component: LatePermissionsViewComponent,
  },
  {
    path: 'late_permissions_view',
    component: LatePermissionsViewComponent,
  },
  {
    path: 'late_permissions',
    component: LatePermissionsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrAttendanceRoutingModule {}
