<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.Vacation" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر التواريخ -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.FromDate" | translate
              }}</label>
              <input
                id="fromDate"
                type="date"
                formControlName="fromDateValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.ToDate" | translate
              }}</label>
              <input
                id="toDate"
                type="date"
                formControlName="toDateValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الموظف ونوع الإجازة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="vacationId" class="form-label">
                {{ "COMMON.Vacation" | translate }}
              </label>
              <ng-select
                id="vacationId"
                formControlName="vacationId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="vacations"
              ></ng-select>
            </div>
          </div>

          <!-- سطر السنة والشهر -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="yearId" class="form-label">
                {{ "COMMON.Year" | translate }}
              </label>
              <input
                id="yearId"
                type="text"
                formControlName="yearId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="monthId" class="form-label">
                {{ "COMMON.Month" | translate }}
              </label>
              <input
                id="monthId"
                type="text"
                formControlName="monthId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر اليوم والحد الأقصى -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="dayId" class="form-label">
                {{ "COMMON.Day" | translate }}
              </label>
              <input
                id="dayId"
                type="text"
                formControlName="dayId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="limitId" class="form-label">
                {{ "COMMON.Limit" | translate }}
              </label>
              <input
                id="limitId"
                type="text"
                formControlName="limitId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الرصيد السابق والرصيد الحالي -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="previousId" class="form-label">
                {{ "COMMON.Previous" | translate }}
              </label>
              <input
                id="previousId"
                type="text"
                formControlName="previousId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="balanceId" class="form-label">
                {{ "COMMON.Balance" | translate }}
              </label>
              <input
                id="balanceId"
                type="text"
                formControlName="balanceId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الملاحظات والتأثير على حالة الموظف -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="notesId" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                id="notesId"
                type="text"
                formControlName="notesId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">
                {{ "COMMON.EffectEmployeeStatus" | translate }}
              </label>
              <dx-check-box
                [formControl]="effectEmployeeStatusControl"
                [value]="effectEmployeeStatusControl.value"
                valueExpr="value"
              ></dx-check-box>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
