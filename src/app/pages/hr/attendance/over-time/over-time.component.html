<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="main-inputs">
      <div class="card-header border-0 cursor-pointer w-100">
        <div
          class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
        >
          <h3 class="fw-bolder m-0">{{ "COMMON.OverTime" | translate }}</h3>
          <div [style.position]="'relative'">
            <div class="btn-group">
              <button
                (click)="filter()"
                class="btn btn-sm btn-active-light-primary"
              >
                {{ "COMMON.Filter" | translate }}
                <i class="fa fa-filter"></i>
              </button>
              <button
                routerLink="/hr/attendance/over_time_view"
                class="btn btn-sm btn-active-light-primary"
                *hasPermission="{
                  module: 'HR.OverTime',
                  action: 'createAction'
                }"
              >
                {{ "COMMON.Create" | translate }}
                <i class="fa fa-plus"></i>
              </button>
              <button
                [routerLink]="[
                  '/hr/attendance/over_time_view',
                  selectedItemKeys[0]
                ]"
                [hidden]="
                  selectedItemKeys.length > 1 || selectedItemKeys.length == 0
                "
                class="btn btn-sm btn-active-light-primary"
                *hasPermission="{
                  module: 'HR.OverTime',
                  action: 'updateAction'
                }"
              >
                {{ "COMMON.EDIT" | translate }}
                <i class="fa fa-edit"></i>
              </button>
              <button
                (click)="deleteRecords()"
                [hidden]="!selectedItemKeys.length"
                class="btn btn-sm btn-active-light-primary"
                *hasPermission="{
                  module: 'HR.OverTime',
                  action: 'deleteAction'
                }"
              >
                {{ "COMMON.DELETE" | translate }}
                <i class="fa fa-trash"></i>
              </button>
            </div>
            <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                        (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
            <button
              class="btn btn-icon btn-active-light-primary mx-2"
              (click)="toggleMenu()"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              data-bs-trigger="hover"
              title="Settings"
            >
              <i class="fa fa-gear"></i>
            </button>
            <div
              class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
              [class.show]="menuOpen"
              [style.position]="'absolute'"
              [style.top]="'100%'"
              [style.zIndex]="'1050'"
              [style.left]="isRtl ? '0' : 'auto'"
              [style.right]="!isRtl ? '0' : 'auto'"
            >
              <div class="menu-item px-3">
                <a
                  class="menu-link px-3"
                  (click)="exportToExcel()"
                  data-kt-company-table-filter="delete_row"
                >
                  {{ "COMMON.ExportToExcel" | translate }}
                </a>
              </div>
              <div class="menu-item px-3">
                <a
                  class="menu-link px-3"
                  data-kt-company-table-filter="delete_row"
                  (click)="openSmsModal()"
                >
                  {{ "COMMON.SendViaSMS" | translate }}
                </a>
              </div>

              <div class="menu-item px-3">
                <a
                  class="menu-link px-3"
                  data-kt-company-table-filter="delete_row"
                  (click)="openEmailModal()"
                >
                  {{ "COMMON.SendViaEmail" | translate }}
                </a>
              </div>

              <div class="menu-item px-3">
                <a
                  class="menu-link px-3"
                  data-kt-company-table-filter="delete_row"
                  (click)="openWhatsappModal()"
                >
                  {{ "COMMON.SendViaWhatsapp" | translate }}
                </a>
              </div>

              <div
                class="menu-item px-3"
                *hasPermission="{
                  action: 'printAction',
                  module: 'HR.OverTime'
                }"
              >
                <a
                  class="menu-link px-3"
                  target="_blank"
                  href="/reports/warehouses"
                  data-kt-company-table-filter="delete_row"
                  >{{ "COMMON.Print" | translate }}</a
                >
              </div>
              <!-- <div
                class="menu-item px-3"
                *hasPermission="{
                  action: 'printAction',
                  module: 'HR.OverTime'
                }"
              >
                <a
                  class="menu-link px-3"
                  target="_blank"
                  href="/reports/warehouses?withLogo=true"
                  data-kt-company-table-filter="delete_row"
                  >Print With Logo</a
                >
              </div> -->
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.Date" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="employeeId" class="form-label">
            {{ "COMMON.EmployeeName" | translate }}
          </label>
          <ng-select
            id="employeeId"
            formControlName="employeeId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="employees"
          ></ng-select>
        </div>
      </div>
    </div>
  </div>
</form>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="id"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
  (onSelectionChanged)="selectionChanged($event)"
>
  <dxo-selection mode="multiple"></dxo-selection>

  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="id"
    caption="{{ 'COMMON.id' | translate }}"
  ></dxi-column>
  <dxi-column dataField="nameAr" caption="{{ 'COMMON.NameAr' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="enName" caption="{{ 'COMMON.NameEn' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="job" caption="{{ 'COMMON.Job' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column
    dataField="depart"
    caption="{{ 'COMMON.Department' | translate }}"
  >
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
    <!--<dxi-total-item column="InvoiceDate"
                    summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>-->
    <!--<dxi-total-item column="Total"
                    summaryType="sum"
                    valueFormat="currency">
    </dxi-total-item>-->
  </dxo-summary>
</dx-data-grid>
