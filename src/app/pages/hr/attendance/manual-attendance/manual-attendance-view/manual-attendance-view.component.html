<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.ManualAttendance" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر خطة العمل والوردية -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="workCategoryId" class="form-label">
                {{ "Salary.WorkPlan" | translate }}
              </label>
              <ng-select
                id="workCategoryId"
                formControlName="workCategoryId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="workCategories"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="shiftId" class="form-label">
                {{ "COMMON.Shift" | translate }}
              </label>
              <ng-select
                id="shiftId"
                formControlName="shiftId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Shifts"
              ></ng-select>
            </div>
          </div>

          <!-- سطر التواريخ -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.FromDate" | translate
              }}</label>
              <input
                id="fromDate"
                type="date"
                formControlName="fromDateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.ToDate" | translate
              }}</label>
              <input
                id="toDate"
                type="date"
                formControlName="toDateValue"
                class="form-control"
              />
            </div>
          </div>

          <!-- سطر الموظف ومركز التكلفة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="costId" class="form-label">
                {{ "COMMON.CostName" | translate }}
              </label>
              <ng-select
                id="costId"
                [(ngModel)]="costId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="costCenter"
              ></ng-select>
            </div>
          </div>

          <!-- سطر الإدارات -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="fromManageId" class="form-label">
                {{ "COMMON.FromManage" | translate }}
              </label>
              <ng-select
                id="fromManageId"
                formControlName="fromManageId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Manages"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="toManageId" class="form-label">
                {{ "COMMON.ToManage" | translate }}
              </label>
              <ng-select
                id="toManageId"
                formControlName="toManageId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Manages"
              ></ng-select>
            </div>
          </div>

          <!-- سطر القطاعات -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="fromSectorId" class="form-label">
                {{ "COMMON.FromSector" | translate }}
              </label>
              <ng-select
                id="fromSectorId"
                formControlName="fromSectorId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Sectors"
              ></ng-select>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="toSectorId" class="form-label">
                {{ "COMMON.ToSector" | translate }}
              </label>
              <ng-select
                id="toSectorId"
                formControlName="toSectorId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Sectors"
              ></ng-select>
            </div>
          </div>

          <!-- سطر أوقات الحضور والانصراف -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.Attendance" | translate
              }}</label>
              <input
                id="attendance"
                type="time"
                formControlName="attendance"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Leave" | translate }}</label>
              <input
                id="leave"
                type="time"
                formControlName="leave"
                class="form-control"
              />
            </div>
          </div>

          <!-- سطر أوقات الاستراحة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.BreakOut" | translate
              }}</label>
              <input
                id="breakOut"
                type="time"
                formControlName="breakOut"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.BreakIn" | translate
              }}</label>
              <input
                id="breakIn"
                type="time"
                formControlName="breakIn"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
