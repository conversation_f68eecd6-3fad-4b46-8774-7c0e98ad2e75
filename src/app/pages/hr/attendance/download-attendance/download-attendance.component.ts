import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { HRService } from '../../hr.service';
import { saveAs } from 'file-saver-es';
import ArrayStore from 'devextreme/data/array_store';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { Workbook } from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import jsPDF from 'jspdf';

@Component({
  selector: 'app-download-attendance',
  templateUrl: './download-attendance.component.html',
  styleUrls: ['./download-attendance.component.scss'],
  standalone: false,
})
export class DownloadAttendanceComponent implements OnInit, OnDestroy {
  moduleName = 'HR.DownloadAttendance';
  roles: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  data: ArrayStore;
  isLoading = false;
  employeeId: number = 0;
  selectedItemKeys: any = [];
  currentFilter: any;
  employees: any[];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private service: HRService,
    private router: Router
  ) {
    this.newdata = this.fb.group({
      ip: [null, Validators.required],
      port: [null, Validators.required],
      phone: [null],
      fax: [null],
      mobile: [null],
      website: [null],
      scope: [null],
      notes: [null],
    });
    this.subscriptions.add(
      service.list().subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscriptions.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    // const id = this.route.snapshot.params.id;
    // if (id) {
    //   this.subscriptions.add(
    //     this.myService.details(id).subscribe(r => {
    //       if (r.success) {
    //         this.fillForm(r.data);
    //         this.roles = r.roles;
    //         this.cdk.detectChanges();
    //       }
    //     }));
    //   }
  }
  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }

  fillForm(item: any) {
    this.newdata.get('ip')?.setValue(item.ip);
    this.newdata.get('port')?.setValue(item.port);
    this.newdata.get('phone')?.setValue(item.phone);
    this.newdata.get('fax')?.setValue(item.fax);
    this.newdata.get('mobile')?.setValue(item.mobile);
    this.newdata.get('website')?.setValue(item.website);
    this.newdata.get('scope')?.setValue(item.scope);
    this.newdata.get('notes')?.setValue(item.notes);
  }
  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'falconData.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      // if (this.newdata.valid) {
      //   let form = this.newdata.value
      //   this.subscriptions.add(this.myService.update(id,form)
      //     .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
      //     .subscribe(r => {
      //       if (r.success) {
      //         this.router.navigate(['/hr/configuration/shipping_companies']);
      //       }
      //     }));
      // } else {
      //   this.newdata.markAllAsTouched();
      // }
    } else {
      if (this.newdata.valid) {
        //   let form = this.newdata.value
        //   this.subscriptions.add(this.myService.create(form)
        //     .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
        //     .subscribe(r => {
        //       if (r.success) {
        //         this.router.navigate(['/hr/configuration/shipping_companies']);
        //       }
        //     }));
        // } else {
        //   this.newdata.markAllAsTouched();
      }
    }
  }
  filter() {
    const DepartId = this.employeeId;
    this.subscriptions.add(
      this.service.FilterEmployee({ DepartId }).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
}
