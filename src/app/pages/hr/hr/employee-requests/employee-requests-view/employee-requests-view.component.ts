import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { HRService } from '../../../hr.service';

@Component({
  selector: 'app-employee-requests-view',
  templateUrl: './employee-requests-view.component.html',
  styleUrls: ['./employee-requests-view.component.css'],
  standalone: false,
})
export class EmployeeRequestsViewComponent implements OnInit, OnDestroy {
  moduleName = 'hr.EmployeeRequests';

  showOverlay = false;
  roles: any[] = [];

  Employees: any[] = [];
  requestTypes: any[] = [];

  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: HRService,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      date: [today, Validators.required],
      fromDateValue: [today, Validators.required],
      toDateValue: [today, Validators.required],
      returnDateFromLastVacation: [today, Validators.required],
      hiringDate: [today, Validators.required],
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
    this.subscriptions.add(
      this.myService.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.Employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  fillForm(item: any) {
    this.newdata.get('nameAr')?.setValue(item.nameAr);
    this.newdata.get('nameEn')?.setValue(item.nameEn);
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/human_resources/employee_requests']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/human_resources/employee_requests']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
