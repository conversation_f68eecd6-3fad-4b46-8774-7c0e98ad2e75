<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.EmployeeRequests" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.EmployeeRequests'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.EmployeeRequests'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.Date" | translate }}
              </label>
              <input
                id="date"
                type="date"
                formControlName="date"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="EmployeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="EmployeeId"
                formControlName="EmployeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Employees"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="requestType" class="form-label">
                {{ "COMMON.OrderType" | translate }}
              </label>
              <ng-select
                id="requestType"
                formControlName="requestType"
                bindLabel="nameAr"
                bindValue="id"
                [items]="requestTypes"
              ></ng-select>
            </div>
            <div class="form-group col-xl-2 col-md-4 col-sm-12">
              <label class="form-label">{{
                "COMMON.FromDate" | translate
              }}</label>
              <input
                id="fromDate"
                type="date"
                formControlName="fromDateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-2 col-md-4 col-sm-12">
              <label class="form-label">{{
                "COMMON.ToDate" | translate
              }}</label>
              <input
                id="toDate"
                type="date"
                formControlName="toDateValue"
                class="form-control"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.ReturnDateFromLastVacation" | translate }}
              </label>
              <input
                id="returnDateFromLastVacation"
                type="date"
                formControlName="returnDateFromLastVacation"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.HiringDate" | translate }}
              </label>
              <input
                id="hiringDate"
                type="date"
                formControlName="hiringDate"
                class="form-control"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-2 col-md-2 col-sm-12">
              <label for="yearId" class="form-label">
                {{ "COMMON.ServiceLength" | translate }}
                {{ "COMMON.Year" | translate }}
              </label>
              <input
                id="yearId"
                type="text"
                formControlName="yearId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-2 col-md-2 col-sm-12">
              <label for="monthId" class="form-label">
                {{ "COMMON.Month" | translate }}
              </label>
              <input
                id="monthId"
                type="text"
                formControlName="monthId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-2 col-md-2 col-sm-12">
              <label for="dayId" class="form-label">
                {{ "COMMON.Day" | translate }}
              </label>
              <input
                id="dayId"
                type="text"
                formControlName="dayId"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="notes" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                type="text"
                id="notes"
                name="notes"
                class="form-control"
                formControlName="notes"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
