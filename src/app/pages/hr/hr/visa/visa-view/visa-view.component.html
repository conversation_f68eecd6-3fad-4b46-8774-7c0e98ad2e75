<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.Visa" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.Visa'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.Visa'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="mainVisaNumber" class="form-label">
                {{ "COMMON.MainVisaNumber" | translate }}
              </label>
              <input
                type="text"
                id="mainVisaNumber"
                name="mainVisaNumber"
                class="form-control"
                formControlName="mainVisaNumber"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="contractNumber" class="form-label">
                {{ "COMMON.ContractNumber" | translate }}
              </label>
              <input
                type="text"
                name="contractNumber"
                id="contractNumber"
                class="form-control"
                formControlName="contractNumber"
              />
            </div>
          </div>
          <div class="row mb-3">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.IssueDate" | translate }}
              </label>
              <input
                id="issueDate"
                type="date"
                formControlName="issueDate"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.EXPDate" | translate }}
              </label>
              <input
                id="eXPDate"
                type="date"
                formControlName="eXPDate"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.Visa" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="kafeel" class="form-label">
                  {{ "Salary.Kafeel" | translate }}
                </label>
                <ng-select
                  id="kafeel"
                  bindLabel="name"
                  bindValue="id"
                  formControlName="kafeel"
                  [items]="kafeels"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="project" class="form-label">
                  {{ "COMMON.Project" | translate }}
                </label>
                <input
                  type="text"
                  name="project"
                  id="project"
                  class="form-control"
                  formControlName="project"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="visasNumber" class="form-label">
                  {{ "COMMON.VisasNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="visasNumber"
                  name="visasNumber"
                  class="form-control"
                  formControlName="visasNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="visasGranted" class="form-label">
                  {{ "COMMON.VisasGranted" | translate }}
                </label>
                <input
                  type="text"
                  name="visasGranted"
                  id="visasGranted"
                  class="form-control"
                  formControlName="visasGranted"
                />
              </div>
            </div>
          </div>

          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="nationality" class="form-label">
                  {{ "COMMON.Nationality" | translate }}
                </label>
                <ng-select
                  id="nationality"
                  formControlName="nationality"
                  bindLabel="name"
                  bindValue="id"
                  [items]="nationalities"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="authorizationsIssued" class="form-label">
                  {{ "COMMON.AuthorizationsIssued" | translate }}
                </label>
                <input
                  type="text"
                  id="authorizationsIssued"
                  name="authorizationsIssued"
                  class="form-control"
                  formControlName="authorizationsIssued"
                />
              </div>
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.Date" | translate }}
                </label>
                <input
                  id="date"
                  type="date"
                  formControlName="date"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="job" class="form-label">
                  {{ "COMMON.Job" | translate }}
                </label>
                <ng-select
                  id="job"
                  formControlName="job"
                  bindLabel="name"
                  bindValue="id"
                  [items]="jobs"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="availableAuthorizations" class="form-label">
                  {{ "COMMON.AvailableAuthorizations" | translate }}
                </label>
                <input
                  type="text"
                  id="availableAuthorizations"
                  name="availableAuthorizations"
                  class="form-control"
                  formControlName="availableAuthorizations"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="number" class="form-label">
                  {{ "COMMON.Number" | translate }}
                </label>
                <input
                  type="text"
                  id="number"
                  name="number"
                  class="form-control"
                  formControlName="number"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="holdsVisa" class="form-label">
                  {{ "COMMON.HoldsVisa" | translate }}
                </label>
                <input
                  type="text"
                  id="holdsVisa"
                  name="holdsVisa"
                  class="form-control"
                  formControlName="holdsVisa"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="office" class="form-label">
                  {{ "COMMON.Office" | translate }}
                </label>
                <input
                  type="text"
                  id="office"
                  name="office"
                  class="form-control"
                  formControlName="office"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="notHoldsVisa" class="form-label">
                  {{ "COMMON.NotHoldsVisa" | translate }}
                </label>
                <input
                  type="text"
                  id="notHoldsVisa"
                  name="notHoldsVisa"
                  class="form-control"
                  formControlName="notHoldsVisa"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="notes" class="form-label">
                  {{ "COMMON.Notes" | translate }}
                </label>
                <input
                  type="text"
                  id="notes"
                  name="notes"
                  class="form-control"
                  formControlName="notes"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
