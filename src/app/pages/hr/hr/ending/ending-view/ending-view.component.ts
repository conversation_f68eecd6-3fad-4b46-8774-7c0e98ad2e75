import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { HRService } from '../../../hr.service';

@Component({
  selector: 'app-ending-view',
  templateUrl: './ending-view.component.html',
  styleUrls: ['./ending-view.component.css'],
  standalone: false,
})
export class EndingViewComponent implements OnInit, OnDestroy {
  moduleName = 'hr.Ending';

  showOverlay = false;
  activeTab: string = 'tab1';
  roles: any[] = [];
  employees: any[] = [];
  currencies: any[] = [];

  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: HRService,
    private router: Router
  ) {
    this.subscriptions.add(
      myService.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.initializeForm();

    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  initializeForm(): void {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      date: [today, Validators.required],
      lastWorkDay: [today, Validators.required],
      returnDateFromLastVacation: [today, Validators.required],
      employeeId: [null, Validators.required],
    });
  }

  fillForm(item: any) {
    this.newdata.patchValue({
      date: item.date,
      employeeId: item.employeeId,
      notes: item.notes,
      basicSalary: item.basicSalary,
      fixedAllowance: item.fixedAllowance,
      changedSalary: item.changedSalary,
      transportationAllowance: item.transportationAllowance,
      mealAllowance: item.mealAllowance,
      telephoneAllowance: item.telephoneAllowance,
      housingAllowance: item.housingAllowance,
      schoolAllowance: item.schoolAllowance,
      natureOfWorkAllowance: item.natureOfWorkAllowance,
      otherAllowances: item.otherAllowances,
      total: item.total,
    });
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/human_resources/ending']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/human_resources/ending']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }

  discard() {
    this.newdata.reset();
  }

  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }

  openWhatsappModal() {
    this.whatsappModal.open();
  }

  openEmailModal() {
    this.emailModal.open();
  }
}
