<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.Ending" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>

        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.Ending'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.Ending'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="date"
                type="date"
                formControlName="date"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="currency" class="form-label">
                {{ "COMMON.Currency" | translate }}
              </label>
              <ng-select
                id="currency"
                formControlName="currency"
                bindLabel="nameAr"
                bindValue="id"
                [items]="currencies"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="basicSalary" class="form-label">
                {{ "COMMON.BasicSalary" | translate }}
              </label>
              <input
                type="text"
                id="basicSalary"
                name="basicSalary"
                class="form-control"
                formControlName="basicSalary"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="job" class="form-label">
                {{ "COMMON.Job" | translate }}
              </label>
              <input
                type="text"
                id="job"
                name="job"
                class="form-control"
                formControlName="job"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="jobNumber" class="form-label">
                {{ "COMMON.JobNumber" | translate }}
              </label>
              <input
                type="text"
                id="jobNumber"
                name="jobNumber"
                class="form-control"
                formControlName="jobNumber"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="allowances" class="form-label">
                {{ "COMMON.Allowances" | translate }}
              </label>
              <input
                type="text"
                id="allowances"
                name="allowances"
                class="form-control"
                formControlName="allowances"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="nationality" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <input
                type="text"
                id="nationality"
                name="nationality"
                class="form-control"
                formControlName="nationality"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.Procedures" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="totalSalary" class="form-label">
                  {{ "Salary.mobile" | translate }}
                </label>
                <input
                  type="text"
                  id="totalSalary"
                  name="totalSalary"
                  class="form-control"
                  formControlName="totalSalary"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="sector" class="form-label">
                  {{ "COMMON.Sector" | translate }}
                </label>
                <input
                  type="text"
                  id="sector"
                  name="sector"
                  class="form-control"
                  formControlName="sector"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="annualTicketsValue" class="form-label">
                  {{ "COMMON.AnnualTicketsValue" | translate }}
                </label>
                <input
                  type="text"
                  id="annualTicketsValue"
                  name="annualTicketsValue"
                  class="form-control"
                  formControlName="annualTicketsValue"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="eligibilityRate" class="form-label">
                  {{ "COMMON.EligibilityRate" | translate }}
                </label>
                <input
                  type="text"
                  id="eligibilityRate"
                  name="eligibilityRate"
                  class="form-control"
                  formControlName="eligibilityRate"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="kafeel" class="form-label">
                  {{ "Salary.Kafeel" | translate }}
                </label>
                <input
                  type="text"
                  id="kafeel"
                  name="kafeel"
                  class="form-control"
                  formControlName="kafeel"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">{{
                  "COMMON.ReturnDateFromLastVacation" | translate
                }}</label>
                <input
                  id="returnDateFromLastVacation"
                  type="date"
                  formControlName="returnDateFromLastVacation"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">{{
                  "COMMON.LastWorkDay" | translate
                }}</label>
                <input
                  id="lastWorkDay"
                  type="date"
                  formControlName="lastWorkDay"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="durationInDays" class="form-label">
                  {{ "COMMON.DurationInDays" | translate }}
                </label>
                <input
                  type="text"
                  id="durationInDays"
                  name="durationInDays"
                  class="form-control"
                  formControlName="durationInDays"
                />
              </div>
            </div>
          </div>

          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="col-xl-4 col-md-6 col-sm-12">
                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="AnnualLeave"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.AnnualLeave" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="Month30Days"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.Month30Days" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="DownloadDueSalaries"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.DownloadDueSalaries" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="EndOfService"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.EndOfService" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="ShowingCovenantAndSalafists"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.ShowingCovenantAndSalafists" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="Resignation"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.Resignation" | translate }}
                  </label>
                </div>

                <div class="form-group col-xl-2 col-md-2 col-sm-6">
                  <dx-check-box
                    formControlName="DownloadSalaryOflastMonth"
                    valueExpr="value"
                  ></dx-check-box>
                  <label class="form-label d-block p-2">
                    {{ "COMMON.DownloadSalaryOflastMonth" | translate }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
