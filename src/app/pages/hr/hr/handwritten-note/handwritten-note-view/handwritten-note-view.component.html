<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.HandwrittenNote" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.HandwrittenNote'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.HandwrittenNote'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.Date" | translate }}
              </label>
              <input
                id="date"
                type="date"
                formControlName="date"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="EmployeeId" class="form-label">
                {{ "COMMON.Employee" | translate }}
              </label>
              <ng-select
                id="EmployeeId"
                formControlName="EmployeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Employees"
              ></ng-select>
            </div>
          </div>

          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="memoType" class="form-label">
                {{ "COMMON.MemoType" | translate }}
              </label>
              <ng-select
                id="memoType"
                formControlName="memoType"
                bindLabel="nameAr"
                bindValue="id"
                [items]="memoTypes"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="memoNumber" class="form-label">
                {{ "COMMON.MemoNumber" | translate }}
              </label>
              <input
                type="text"
                id="memoNumber"
                name="memoNumber"
                class="form-control"
                formControlName="memoNumber"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="job" class="form-label">
                {{ "COMMON.Job" | translate }}
              </label>
              <input
                type="text"
                id="job"
                name="job"
                class="form-control"
                formControlName="job"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="basicSalary" class="form-label">
                {{ "COMMON.BasicSalary" | translate }}
              </label>
              <input
                type="text"
                id="basicSalary"
                name="basicSalary"
                class="form-control"
                formControlName="basicSalary"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="jobNumber" class="form-label">
                {{ "COMMON.JobNumber" | translate }}
              </label>
              <input
                type="text"
                id="jobNumber"
                name="jobNumber"
                class="form-control"
                formControlName="jobNumber"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="otherAllowance" class="form-label">
                {{ "COMMON.OtherAllowance" | translate }}
              </label>
              <input
                type="text"
                id="otherAllowance"
                name="otherAllowance"
                class="form-control"
                formControlName="otherAllowance"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="nationality" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <input
                type="text"
                id="nationality"
                name="nationality"
                class="form-control"
                formControlName="nationality"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="totalSalary" class="form-label">
                {{ "Salary.mobile" | translate }}
              </label>
              <input
                type="text"
                id="totalSalary"
                name="totalSalary"
                class="form-control"
                formControlName="totalSalary"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="sector" class="form-label">
                {{ "COMMON.Sector" | translate }}
              </label>
              <input
                type="text"
                id="sector"
                name="sector"
                class="form-control"
                formControlName="sector"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="contractStatus" class="form-label">
                {{ "COMMON.ContractStatus" | translate }}
              </label>
              <input
                type="text"
                id="contractStatus"
                name="contractStatus"
                class="form-control"
                formControlName="contractStatus"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="onAccount" class="form-label">
                {{ "COMMON.OnAccount" | translate }}
              </label>
              <input
                type="text"
                id="onAccount"
                name="onAccount"
                class="form-control"
                formControlName="onAccount"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
