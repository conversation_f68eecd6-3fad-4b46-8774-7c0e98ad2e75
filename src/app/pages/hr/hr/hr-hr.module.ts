import { NgModule } from '@angular/core';
import { AnnualDuesComponent } from './annual-dues/annual-dues.component';
import { EmployeeRequestsComponent } from './employee-requests/employee-requests.component';
import { EndingComponent } from './ending/ending.component';
import { HandwrittenNoteComponent } from './handwritten-note/handwritten-note.component';
import { HousingAllowancePaymentsComponent } from './housing-allowance-payments/housing-allowance-payments.component';
import { IqamaUpdateComponent } from './iqama-update/iqama-update.component';
import { SalaryUpdateComponent } from './salary-update/salary-update.component';
import { ScheduleAdvancesComponent } from './schedule-advances/schedule-advances.component';
import { StaffCostsComponent } from './staff-costs/staff-costs.component';
import { StaffTransferBetweenProjectsComponent } from './staff-transfer-between-projects/staff-transfer-between-projects.component';
import { VisaComponent } from './visa/visa.component';
import { HrHrRoutingModule } from './hr-hr-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { VisaViewComponent } from './visa/visa-view/visa-view.component';
import { StaffTransferBetweenProjectsViewComponent } from './staff-transfer-between-projects/staff-transfer-between-projects-view/staff-transfer-between-projects-view.component';
import { SalaryUpdateViewComponent } from './salary-update/salary-update-view/salary-update-view.component';
import { IqamaUpdateViewComponent } from './iqama-update/iqama-update-view/iqama-update-view.component';
import { EmployeeRequestsViewComponent } from './employee-requests/employee-requests-view/employee-requests-view.component';
import { HousingAllowancePaymentsViewComponent } from './housing-allowance-payments/housing-allowance-payments-view/housing-allowance-payments-view.component';
import { EndingViewComponent } from './ending/ending-view/ending-view.component';
import { AnnualDuesViewComponent } from './annual-dues/annual-dues-view/annual-dues-view.component';
import { HandwrittenNoteViewComponent } from './handwritten-note/handwritten-note-view/handwritten-note-view.component';
import { StaffCostsViewComponent } from './staff-costs/staff-costs-view/staff-costs-view.component';
import { ScheduleAdvancesViewComponent } from './schedule-advances/schedule-advances-view/schedule-advances-view.component';

@NgModule({
  declarations: [
    VisaComponent,
    StaffTransferBetweenProjectsComponent,
    SalaryUpdateComponent,
    IqamaUpdateComponent,
    EmployeeRequestsComponent,
    HousingAllowancePaymentsComponent,
    EndingComponent,
    AnnualDuesComponent,
    HandwrittenNoteComponent,
    StaffCostsComponent,
    ScheduleAdvancesComponent,
    VisaViewComponent,
    StaffTransferBetweenProjectsViewComponent,
    SalaryUpdateViewComponent,
    IqamaUpdateViewComponent,
    EmployeeRequestsViewComponent,
    HousingAllowancePaymentsViewComponent,
    EndingViewComponent,
    AnnualDuesViewComponent,
    HandwrittenNoteViewComponent,
    StaffCostsViewComponent,
    ScheduleAdvancesViewComponent,
  ],
  imports: [HrHrRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrHrModule {}
