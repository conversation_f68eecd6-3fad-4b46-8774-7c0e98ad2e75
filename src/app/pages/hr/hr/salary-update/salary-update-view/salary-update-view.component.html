<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.SalaryUpdate" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>

        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.SalaryUpdate'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.SalaryUpdate'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="date"
                type="date"
                formControlName="date"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>
          <div class="row mb-3">
            <div class="form-group col-xl-8 col-md-6 col-sm-12">
              <label for="notes" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                type="text"
                id="notes"
                name="notes"
                class="form-control"
                formControlName="notes"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.CurrentSalary" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.SuggestedSalary" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentBasicSalary" class="form-label">
                  {{ "COMMON.BasicSalary" | translate }}
                </label>
                <input
                  type="number"
                  id="currentBasicSalary"
                  name="currentBasicSalary"
                  class="form-control"
                  formControlName="currentBasicSalary"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentFixedAllowance" class="form-label">
                  {{ "COMMON.FixedAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentFixedAllowance"
                  name="currentFixedAllowance"
                  class="form-control"
                  formControlName="currentFixedAllowance"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentChangedSalary" class="form-label">
                  {{ "COMMON.ChangedSalary" | translate }}
                </label>
                <input
                  type="number"
                  id="currentChangedSalary"
                  name="currentChangedSalary"
                  class="form-control"
                  formControlName="currentChangedSalary"
                  readonly
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentTransportationAllowance" class="form-label">
                  {{ "COMMON.TransportationAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentTransportationAllowance"
                  name="currentTransportationAllowance"
                  class="form-control"
                  formControlName="currentTransportationAllowance"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentMealAllowance" class="form-label">
                  {{ "COMMON.MealAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentMealAllowance"
                  name="currentMealAllowance"
                  class="form-control"
                  formControlName="currentMealAllowance"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentTelephoneAllowance" class="form-label">
                  {{ "COMMON.TelephoneAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentTelephoneAllowance"
                  name="currentTelephoneAllowance"
                  class="form-control"
                  formControlName="currentTelephoneAllowance"
                  readonly
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentHousingAllowance" class="form-label">
                  {{ "COMMON.HousingAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentHousingAllowance"
                  name="currentHousingAllowance"
                  class="form-control"
                  formControlName="currentHousingAllowance"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentSchoolAllowance" class="form-label">
                  {{ "COMMON.SchoolAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentSchoolAllowance"
                  name="currentSchoolAllowance"
                  class="form-control"
                  formControlName="currentSchoolAllowance"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentNatureOfWorkAllowance" class="form-label">
                  {{ "COMMON.NatureOfWorkAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="currentNatureOfWorkAllowance"
                  name="currentNatureOfWorkAllowance"
                  class="form-control"
                  formControlName="currentNatureOfWorkAllowance"
                  readonly
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentOtherAllowances" class="form-label">
                  {{ "COMMON.OtherAllowances" | translate }}
                </label>
                <input
                  type="number"
                  id="currentOtherAllowances"
                  name="currentOtherAllowances"
                  class="form-control"
                  formControlName="currentOtherAllowances"
                  readonly
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="currentTotal" class="form-label">
                  {{ "COMMON.Total" | translate }}
                </label>
                <input
                  type="number"
                  id="currentTotal"
                  name="currentTotal"
                  class="form-control"
                  formControlName="total"
                  readonly
                />
              </div>
            </div>
          </div>

          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="basicSalary" class="form-label">
                  {{ "COMMON.BasicSalary" | translate }}
                </label>
                <input
                  type="number"
                  id="basicSalary"
                  name="basicSalary"
                  class="form-control"
                  formControlName="basicSalary"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="fixedAllowance" class="form-label">
                  {{ "COMMON.FixedAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="fixedAllowance"
                  name="fixedAllowance"
                  class="form-control"
                  formControlName="fixedAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="changedSalary" class="form-label">
                  {{ "COMMON.ChangedSalary" | translate }}
                </label>
                <input
                  type="number"
                  id="changedSalary"
                  name="changedSalary"
                  class="form-control"
                  formControlName="changedSalary"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="transportationAllowance" class="form-label">
                  {{ "COMMON.TransportationAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="transportationAllowance"
                  name="transportationAllowance"
                  class="form-control"
                  formControlName="transportationAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="mealAllowance" class="form-label">
                  {{ "COMMON.MealAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="mealAllowance"
                  name="mealAllowance"
                  class="form-control"
                  formControlName="mealAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="telephoneAllowance" class="form-label">
                  {{ "COMMON.TelephoneAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="telephoneAllowance"
                  name="telephoneAllowance"
                  class="form-control"
                  formControlName="telephoneAllowance"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="housingAllowance" class="form-label">
                  {{ "COMMON.HousingAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="housingAllowance"
                  name="housingAllowance"
                  class="form-control"
                  formControlName="housingAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="schoolAllowance" class="form-label">
                  {{ "COMMON.SchoolAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="schoolAllowance"
                  name="schoolAllowance"
                  class="form-control"
                  formControlName="schoolAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="natureOfWorkAllowance" class="form-label">
                  {{ "COMMON.NatureOfWorkAllowance" | translate }}
                </label>
                <input
                  type="number"
                  id="natureOfWorkAllowance"
                  name="natureOfWorkAllowance"
                  class="form-control"
                  formControlName="natureOfWorkAllowance"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="otherAllowances" class="form-label">
                  {{ "COMMON.OtherAllowances" | translate }}
                </label>
                <input
                  type="number"
                  id="otherAllowances"
                  name="otherAllowances"
                  class="form-control"
                  formControlName="otherAllowances"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="total" class="form-label">
                  {{ "COMMON.Total" | translate }}
                </label>
                <input
                  type="number"
                  id="total"
                  name="total"
                  class="form-control"
                  formControlName="total"
                  readonly
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
