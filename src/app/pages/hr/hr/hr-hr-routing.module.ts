import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AnnualDuesComponent } from './annual-dues/annual-dues.component';
import { EmployeeRequestsComponent } from './employee-requests/employee-requests.component';
import { EndingComponent } from './ending/ending.component';
import { HandwrittenNoteComponent } from './handwritten-note/handwritten-note.component';
import { HousingAllowancePaymentsComponent } from './housing-allowance-payments/housing-allowance-payments.component';
import { IqamaUpdateComponent } from './iqama-update/iqama-update.component';
import { SalaryUpdateComponent } from './salary-update/salary-update.component';
import { ScheduleAdvancesComponent } from './schedule-advances/schedule-advances.component';
import { StaffCostsComponent } from './staff-costs/staff-costs.component';
import { StaffTransferBetweenProjectsComponent } from './staff-transfer-between-projects/staff-transfer-between-projects.component';
import { VisaComponent } from './visa/visa.component';
import { VisaViewComponent } from './visa/visa-view/visa-view.component';
import { StaffTransferBetweenProjectsViewComponent } from './staff-transfer-between-projects/staff-transfer-between-projects-view/staff-transfer-between-projects-view.component';
import { SalaryUpdateViewComponent } from './salary-update/salary-update-view/salary-update-view.component';
import { IqamaUpdateViewComponent } from './iqama-update/iqama-update-view/iqama-update-view.component';
import { EmployeeRequestsViewComponent } from './employee-requests/employee-requests-view/employee-requests-view.component';
import { HousingAllowancePaymentsViewComponent } from './housing-allowance-payments/housing-allowance-payments-view/housing-allowance-payments-view.component';
import { EndingViewComponent } from './ending/ending-view/ending-view.component';
import { AnnualDuesViewComponent } from './annual-dues/annual-dues-view/annual-dues-view.component';
import { HandwrittenNoteViewComponent } from './handwritten-note/handwritten-note-view/handwritten-note-view.component';
import { StaffCostsViewComponent } from './staff-costs/staff-costs-view/staff-costs-view.component';
import { ScheduleAdvancesViewComponent } from './schedule-advances/schedule-advances-view/schedule-advances-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'visa',
    pathMatch: 'full',
  },
  {
    path: 'visa_view',
    component: VisaViewComponent,
  },
  {
    path: 'visa_view/:id',
    component: VisaViewComponent,
  },
  {
    path: 'visa',
    component: VisaComponent,
  },
  {
    path: 'staff_transfer_between_projects_view',
    component: StaffTransferBetweenProjectsViewComponent,
  },
  {
    path: 'staff_transfer_between_projects_view/:id',
    component: StaffTransferBetweenProjectsViewComponent,
  },
  {
    path: 'staff_transfer_between_projects',
    component: StaffTransferBetweenProjectsComponent,
  },
  {
    path: 'salary_update_view',
    component: SalaryUpdateViewComponent,
  },
  {
    path: 'salary_update_view/:id',
    component: SalaryUpdateViewComponent,
  },
  {
    path: 'salary_update',
    component: SalaryUpdateComponent,
  },
  {
    path: 'iqama_update_view',
    component: IqamaUpdateViewComponent,
  },
  {
    path: 'iqama_update_view/:id',
    component: IqamaUpdateViewComponent,
  },
  {
    path: 'iqama_update',
    component: IqamaUpdateComponent,
  },
  {
    path: 'employee_requests_view',
    component: EmployeeRequestsViewComponent,
  },
  {
    path: 'employee_requests_view/:id',
    component: EmployeeRequestsViewComponent,
  },
  {
    path: 'employee_requests',
    component: EmployeeRequestsComponent,
  },
  {
    path: 'housing_allowance_payments_view',
    component: HousingAllowancePaymentsViewComponent,
  },
  {
    path: 'housing_allowance_payments_view/:id',
    component: HousingAllowancePaymentsViewComponent,
  },
  {
    path: 'housing_allowance_payments',
    component: HousingAllowancePaymentsComponent,
  },
  {
    path: 'ending_view',
    component: EndingViewComponent,
  },
  {
    path: 'ending_view/:id',
    component: EndingViewComponent,
  },
  {
    path: 'ending',
    component: EndingComponent,
  },
  {
    path: 'Annual_dues_view',
    component: AnnualDuesViewComponent,
  },
  {
    path: 'Annual_dues_view/:id',
    component: AnnualDuesViewComponent,
  },
  {
    path: 'Annual_dues',
    component: AnnualDuesComponent,
  },
  {
    path: 'handwritten_note_view',
    component: HandwrittenNoteViewComponent,
  },
  {
    path: 'handwritten_note_view/:id',
    component: HandwrittenNoteViewComponent,
  },
  {
    path: 'handwritten_note',
    component: HandwrittenNoteComponent,
  },
  {
    path: 'staff_costs_view',
    component: StaffCostsViewComponent,
  },
  {
    path: 'staff_costs_view/:id',
    component: StaffCostsViewComponent,
  },
  {
    path: 'staff_costs',
    component: StaffCostsComponent,
  },
  {
    path: 'schedule_advances_view',
    component: ScheduleAdvancesViewComponent,
  },
  {
    path: 'schedule_advances_view/:id',
    component: ScheduleAdvancesViewComponent,
  },

  {
    path: 'schedule_advances',
    component: ScheduleAdvancesComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrHrRoutingModule {}
