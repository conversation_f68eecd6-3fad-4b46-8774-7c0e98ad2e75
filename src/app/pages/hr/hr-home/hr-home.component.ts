import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { HRService } from '../hr.service';

@Component({
    selector: 'app-hr-home',
    templateUrl: './hr-home.component.html',
    styleUrls: ['./hr-home.component.scss'],
    standalone: false
})
export class hrHomeComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription = new Subscription();

  // Dashboard data
  totalEmployees: number = 0;
  attendanceRate: number = 0;
  pendingVacations: number = 0;
  turnoverRate: number = 0;
  recentActivities: any[] = [];
  upcomingEvents: any[] = [];
  employeeBirthdays: any[] = [];

  constructor(
    private hrService: HRService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Load all dashboard data
   */
  loadDashboardData(): void {
    this.loadTotalEmployees();
    this.loadAttendanceRate();
    this.loadPendingVacations();
    this.loadTurnoverRate();
    this.loadRecentActivities();
    this.loadUpcomingEvents();
    this.loadEmployeeBirthdays();
  }

  /**
   * Load total employees count
   */
  loadTotalEmployees(): void {
    this.subscriptions.add(
      this.hrService.getEmployeesCount().subscribe(
        (response) => {
          if (response.success) {
            this.totalEmployees = response.data;
            this.updateDOMElement('totalEmployees', this.totalEmployees.toString());
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading total employees:', error);
        }
      )
    );
  }

  /**
   * Load attendance rate
   */
  loadAttendanceRate(): void {
    this.subscriptions.add(
      this.hrService.getAttendanceRate().subscribe(
        (response) => {
          if (response.success) {
            this.attendanceRate = response.data;
            this.updateDOMElement('attendanceRate', this.attendanceRate.toFixed(1) + '%');
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading attendance rate:', error);
        }
      )
    );
  }

  /**
   * Load pending vacations count
   */
  loadPendingVacations(): void {
    this.subscriptions.add(
      this.hrService.getPendingVacations().subscribe(
        (response) => {
          if (response.success) {
            this.pendingVacations = response.data.length;
            this.updateDOMElement('pendingVacations', this.pendingVacations.toString());
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading pending vacations:', error);
        }
      )
    );
  }

  /**
   * Load turnover rate
   */
  loadTurnoverRate(): void {
    this.subscriptions.add(
      this.hrService.getTurnoverRate().subscribe(
        (response) => {
          if (response.success) {
            this.turnoverRate = response.data;
            this.updateDOMElement('turnoverRate', this.turnoverRate.toFixed(1) + '%');
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading turnover rate:', error);
        }
      )
    );
  }

  /**
   * Load recent HR activities
   */
  loadRecentActivities(): void {
    this.subscriptions.add(
      this.hrService.getRecentActivities().subscribe(
        (response) => {
          if (response.success) {
            this.recentActivities = response.data;
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading recent activities:', error);
        }
      )
    );
  }

  /**
   * Load upcoming events
   */
  loadUpcomingEvents(): void {
    this.subscriptions.add(
      this.hrService.getUpcomingEvents().subscribe(
        (response) => {
          if (response.success) {
            this.upcomingEvents = response.data;
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading upcoming events:', error);
        }
      )
    );
  }

  /**
   * Load employee birthdays
   */
  loadEmployeeBirthdays(): void {
    this.subscriptions.add(
      this.hrService.getEmployeeBirthdays().subscribe(
        (response) => {
          if (response.success) {
            this.employeeBirthdays = response.data;
            this.cdr.detectChanges();
          }
        },
        (error) => {
          console.error('Error loading employee birthdays:', error);
        }
      )
    );
  }

  /**
   * Update DOM element with value
   */
  private updateDOMElement(elementId: string, value: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value;
    }
  }
}
