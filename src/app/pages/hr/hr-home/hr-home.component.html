
<!-- HR Dashboard -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Quick Stats -->
  <div class="col-xl-12">
    <div class="card card-flush mb-5">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.HROverview" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.HRStats" | translate }}</span>
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Total Employees Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalEmployees">0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TotalEmployees" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-primary">
            <i class="fa fa-users text-primary fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Attendance Rate Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="attendanceRate">0%</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.AttendanceRate" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-success">
            <i class="fa fa-calendar-check text-success fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Pending Vacations Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="pendingVacations">0</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.PendingVacations" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-warning">
            <i class="fa fa-umbrella-beach text-warning fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Turnover Rate Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="turnoverRate">0%</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TurnoverRate" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-danger">
            <i class="fa fa-exchange-alt text-danger fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Charts Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Department Distribution Chart -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.DepartmentDistribution" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.EmployeesByDepartment" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <app-charts-widget3></app-charts-widget3>
      </div>
    </div>
  </div>

  <!-- Attendance Trends Chart -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.AttendanceTrends" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.Last30Days" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <app-charts-widget1></app-charts-widget1>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activities and Quick Access -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Recent Activities -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.RecentActivities" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.LatestHRTransactions" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget4 class="card-xl-stretch mb-xl-10"></app-lists-widget4>
      </div>
    </div>
  </div>

  <!-- Quick Access -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.QuickAccess" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.CommonHROperations" | translate }}</span>
        </h3>
      </div>
      <div class="card-body pt-5">
        <div class="row g-3">
          <!-- Quick Access Buttons -->
          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/employees/employees_view" class="card bg-light-primary hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-user-plus text-primary fs-3x my-2"></i>
                <div class="text-primary fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewEmployee" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/attendance/vacation_view" class="card bg-light-success hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-plane-departure text-success fs-3x my-2"></i>
                <div class="text-success fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewVacation" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/attendance/manual-attendance_view" class="card bg-light-info hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-clock text-info fs-3x my-2"></i>
                <div class="text-info fw-bold fs-5 mb-2 mt-5">{{ "COMMON.ManualAttendance" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/reports/attendance_reports" class="card bg-light-warning hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-chart-bar text-warning fs-3x my-2"></i>
                <div class="text-warning fw-bold fs-5 mb-2 mt-5">{{ "COMMON.AttendanceReports" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/rewards/bonuses_view" class="card bg-light-danger hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-award text-danger fs-3x my-2"></i>
                <div class="text-danger fw-bold fs-5 mb-2 mt-5">{{ "COMMON.NewBonus" | translate }}</div>
              </div>
            </a>
          </div>

          <div class="col-md-6 col-lg-4">
            <a routerLink="/hr/configuration/job-descriptions" class="card bg-light-primary hoverable card-xl-stretch mb-xl-8">
              <div class="card-body">
                <i class="fa fa-briefcase text-primary fs-3x my-2"></i>
                <div class="text-primary fw-bold fs-5 mb-2 mt-5">{{ "COMMON.JobDescriptions" | translate }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Upcoming Events and Birthdays -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Upcoming Events -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.UpcomingEvents" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.NextTwoWeeks" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget2 class="card-xl-stretch mb-xl-10"></app-lists-widget2>
      </div>
    </div>
  </div>

  <!-- Employee Birthdays -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.EmployeeBirthdays" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.ThisMonth" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget6 class="card-xl-stretch mb-xl-10"></app-lists-widget6>
      </div>
    </div>
  </div>
</div>