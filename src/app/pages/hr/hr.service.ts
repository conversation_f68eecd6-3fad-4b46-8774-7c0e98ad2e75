import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export class Status {
  id: number;
  name: string;
}

const statuses: Status[] = [
  {
    id: 1,
    name: 'Not Started',
  },
  {
    id: 2,
    name: 'In Progress',
  },
  {
    id: 3,
    name: 'Deferred',
  },
  {
    id: 4,
    name: 'Need Assistance',
  },
  {
    id: 5,
    name: 'Completed',
  },
];

@Injectable({
  providedIn: 'root',
})
export class HRService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query,
      },
    });
  }

  checkin(form: any): Observable<any> {
    return this.http.put(this.baseUrl + '/CheckIn/', form);
  }

  checkOut(form: any): Observable<any> {
    return this.http.put(this.baseUrl + '/CheckOut/', form);
  }
  breakout(form: any): Observable<any> {
    return this.http.put(this.baseUrl + '/BreakOut/', form);
  }

  breakin(form: any): Observable<any> {
    return this.http.put(this.baseUrl + '/BreakIn/', form);
  }

  getProjectList(): Observable<any> {
    return this.http.get('api/AnalyticAccounts');
  }
  getProjectLocation(code: any): Observable<any> {
    return this.http.get('api/AnalyticAccounts/getProjectLocation/' + code);
  }
  FilterEmployee(id: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl + '/Filter' + id, {
      params: {
        currentPage: page.toString(),
      },
    });
  }
  SalaryBreakdown(form: any): Observable<any> {
    return this.http.post(this.baseUrl, form);
  }
  getDepartDropdown(): Observable<any> {
    return this.http.get<any>('api/Departments');
  }
  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }

  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }
  getSectors(): Observable<any> {
    return this.http.get<any>('api/Sectors/dropdown');
  }
  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }

  getNationality(): Observable<any> {
    return this.http.get<any>('api/nationality');
  }
  getCustDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/dropdown');
  }

  /**
   * Get total employees count for dashboard
   */
  getEmployeesCount(): Observable<any> {
    return this.http.get<any>('api/employees/count');
  }

  /**
   * Get attendance rate for dashboard
   */
  getAttendanceRate(): Observable<any> {
    return this.http.get<any>('api/attendance/rate');
  }

  /**
   * Get pending vacations for dashboard
   */
  getPendingVacations(): Observable<any> {
    return this.http.get<any>('api/vacation/pending');
  }

  /**
   * Get turnover rate for dashboard
   */
  getTurnoverRate(): Observable<any> {
    return this.http.get<any>('api/employees/turnover-rate');
  }

  /**
   * Get recent HR activities for dashboard
   */
  getRecentActivities(): Observable<any> {
    return this.http.get<any>('api/hr/recent-activities');
  }

  /**
   * Get upcoming events for dashboard
   */
  getUpcomingEvents(): Observable<any> {
    return this.http.get<any>('api/hr/upcoming-events');
  }

  /**
   * Get employee birthdays for dashboard
   */
  getEmployeeBirthdays(): Observable<any> {
    return this.http.get<any>('api/employees/birthdays');
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  updatePorjectLocaton(form: any): Observable<any> {
    return this.http
      .put<any>(this.baseUrl + 'UpdateProjectLocation', form)
      .pipe(
        map((result) => {
          if (result.success) {
          }
          return result;
        })
      );
  }

  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  batch(obj: any): Observable<any> {
    return this.http.post(this.baseUrl + 'batch/', obj);
  }

  getStatuses() {
    return statuses;
  }
}
