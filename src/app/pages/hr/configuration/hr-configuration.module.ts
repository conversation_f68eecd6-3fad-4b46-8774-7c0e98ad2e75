import { NgModule } from '@angular/core';
import { AdditionsTypesComponent } from './additions-types/additions-types.component';
import { AttendanceMachinesComponent } from './attendance-machines/attendance-machines.component';
import { DeductionsTypesComponent } from './deductions-types/deductions-types.component';
import { DocumentsTypeComponent } from './documents-type/documents-type.component';
import { EmployeesCategoriesComponent } from './employees-categories/employees-categories.component';
import { InsurancesComponent } from './insurances/insurances.component';
import { JobDescriptionsComponent } from './job-descriptions/job-descriptions.component';
import { QualificationsComponent } from './qualifications/qualifications.component';
import { ShiftComponent } from './shift/shift.component';
import { ShippingCompaniesComponent } from './shipping-companies/shipping-companies.component';
import { SponsorsComponent } from './sponsors/sponsors.component';
import { VacationsComponent } from './vacations/vacations.component';
import { JobDescriptionsViewComponent } from './job-descriptions/job-descriptions-view/job-descriptions-view.component';
import { HrConfigurationRoutingModule } from './hr-configuration-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { ShippingCompaniesViewComponent } from './shipping-companies/shipping-companies-view/shipping-companies-view.component';
import { AnalyticAccountsLocationComponent } from './analytic-accounts-location/analytic-accounts-location.component';

@NgModule({
  declarations: [
    AttendanceMachinesComponent,
    InsurancesComponent,
    ShiftComponent,
    VacationsComponent,
    AdditionsTypesComponent,
    DeductionsTypesComponent,
    JobDescriptionsComponent,
    EmployeesCategoriesComponent,
    ShippingCompaniesComponent,
    DocumentsTypeComponent,
    SponsorsComponent,
    QualificationsComponent,
    AnalyticAccountsLocationComponent,
    JobDescriptionsViewComponent,
    ShippingCompaniesViewComponent,
  ],
  imports: [HrConfigurationRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrConfigurationModule {}
