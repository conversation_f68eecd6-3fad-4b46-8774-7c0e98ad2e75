<div class="card-header border-0 cursor-pointer w-100 mb-4">
  <div
    class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
  >
    <h3 class="fw-bolder m-0">{{ "COMMON.ShippingCompanies" | translate }}</h3>
    <div [style.position]="'relative'">
      <div class="btn-group">
        <button
          routerLink="/hr/configuration/shipping_companies_view"
          class="btn btn-sm btn-active-light-primary"
          *hasPermission="{
            module: 'HR.ShippingCompanies',
            action: 'createAction'
          }"
        >
          {{ "COMMON.Create" | translate }}
          <i class="fa fa-plus"></i>
        </button>
        <button
          [routerLink]="[
            '/hr/configuration/shipping_companies_view',
            selectedItemKeys[0]
          ]"
          [hidden]="selectedItemKeys.length > 1 || selectedItemKeys.length == 0"
          class="btn btn-sm btn-active-light-primary"
          *hasPermission="{
            module: 'HR.ShippingCompanies',
            action: 'updateAction'
          }"
        >
          {{ "COMMON.EDIT" | translate }}
          <i class="fa fa-edit"></i>
        </button>
        <button
          (click)="deleteRecords()"
          [hidden]="!selectedItemKeys.length"
          class="btn btn-sm btn-active-light-primary"
          *hasPermission="{
            module: 'HR.ShippingCompanies',
            action: 'deleteAction'
          }"
        >
          {{ "COMMON.DELETE" | translate }}
          <i class="fa fa-trash"></i>
        </button>
      </div>
      <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
      <button
        class="btn btn-icon btn-active-light-primary mx-2"
        (click)="toggleMenu()"
        data-bs-toggle="tooltip"
        data-bs-placement="top"
        data-bs-trigger="hover"
        title="Settings"
      >
        <i class="fa fa-gear"></i>
      </button>
      <div
        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
        [class.show]="menuOpen"
        [style.position]="'absolute'"
        [style.top]="'100%'"
        [style.zIndex]="'1050'"
        [style.left]="isRtl ? '0' : 'auto'"
        [style.right]="!isRtl ? '0' : 'auto'"
      >
        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            (click)="uploadExcel()"
            data-kt-company-table-filter="delete_row"
          >
            {{ "COMMON.ImportFromExcel" | translate }}
          </a>
        </div>
        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            (click)="exportToExcel()"
            data-kt-company-table-filter="delete_row"
          >
            {{ "COMMON.ExportToExcel" | translate }}
          </a>
        </div>
        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openSmsModal()"
          >
            {{ "COMMON.SendViaSMS" | translate }}
          </a>
        </div>

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openEmailModal()"
          >
            {{ "COMMON.SendViaEmail" | translate }}
          </a>
        </div>

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openWhatsappModal()"
          >
            {{ "COMMON.SendViaWhatsapp" | translate }}
          </a>
        </div>

        <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.ShippingCompanies'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses"
            data-kt-company-table-filter="delete_row"
            >{{ "COMMON.Print" | translate }}</a
          >
        </div>
        <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.ShippingCompanies'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
      </div>
    </div>
  </div>
</div>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="id"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  (onSelectionChanged)="selectionChanged($event)"
  [allowColumnResizing]="true"
>
  <dxo-selection mode="multiple"></dxo-selection>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="id"
    caption="{{ 'COMMON.id' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="name"
    caption="{{ 'COMMON.Name' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="address"
    caption="{{ 'COMMON.Address' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="phone"
    caption="{{ 'COMMON.Phone' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="fax"
    caption="{{ 'COMMON.Fax' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="mobile"
    caption="{{ 'COMMON.Mobile' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="website"
    caption="{{ 'COMMON.Website' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="scope"
    caption="{{ 'COMMON.Scope' | translate }}"
  ></dxi-column>
  <dxi-column
    dataField="notes"
    caption="{{ 'COMMON.Notes' | translate }}"
  ></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
