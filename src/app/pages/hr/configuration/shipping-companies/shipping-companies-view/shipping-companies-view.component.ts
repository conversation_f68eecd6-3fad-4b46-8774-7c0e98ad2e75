import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ShippingCompaniesService } from '../shipping-companiesService';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-shipping-companies-view',
  templateUrl: './shipping-companies-view.component.html',
  styleUrls: ['./shipping-companies-view.component.scss'],
  standalone: false,
})
export class ShippingCompaniesViewComponent implements OnInit, OnDestroy {
  roles: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: ShippingCompaniesService,
    private router: Router
  ) {
    this.newdata = this.fb.group({
      name: [null, Validators.required],
      address: [null, Validators.required],
      phone: [null],
      fax: [null],
      mobile: [null],
      website: [null],
      scope: [null],
      notes: [null],
    });
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    this.newdata.get('name')?.setValue(item.name);
    this.newdata.get('address')?.setValue(item.address);
    this.newdata.get('phone')?.setValue(item.phone);
    this.newdata.get('fax')?.setValue(item.fax);
    this.newdata.get('mobile')?.setValue(item.mobile);
    this.newdata.get('website')?.setValue(item.website);
    this.newdata.get('scope')?.setValue(item.scope);
    this.newdata.get('notes')?.setValue(item.notes);
  }
  discard() {
    this.newdata.reset();
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/configuration/shipping_companies']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/configuration/shipping_companies']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
