import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ConfigurationService } from '../../ConfigurationService';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-job-descriptions-view',
  templateUrl: './job-descriptions-view.component.html',
  styleUrls: ['./job-descriptions-view.component.scss'],
  standalone: false,
})
export class JobDescriptionsViewComponent implements OnInit, OnDestroy {
  roles: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: ConfigurationService,
    private router: Router
  ) {
    this.newdata = this.fb.group({
      nameAr: [null, Validators.required],
      nameEn: [null, Validators.required],
      descriptions: [""],
      responsibilities: [""],
      conditions: [""],
    });
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    Object.keys(item).forEach((key) => {
      if (this.newdata.get(key)) {
        const value =
          item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });
  }
  discard() {
    this.newdata.reset();
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/configuration/job_descriptions']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {

     
      if (this.newdata.valid) {
        let form = this.newdata.value;
        if (form.descriptions == null) {
          form.descriptions = 0;
        }
        
         alert(form.responsibilities)
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/configuration/job_descriptions']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
