<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.JobDescriptions" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label class="required form-label">{{
                "COMMON.NameEn" | translate
              }}</label>
              <input
                type="text"
                name="nameEn"
                class="form-control"
                value=""
                formControlName="nameEn"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label class="required form-label">{{
                "COMMON.NameAr" | translate
              }}</label>
              <input
                type="text"
                class="form-control"
                name="nameAr"
                value=""
                formControlName="nameAr"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-8 col-md-3 col-sm-12">
              <label class="form-label">{{
                "COMMON.Descriptions" | translate
              }}</label>

              <input
                class="form-control"
                name="descriptions"
                value=""
                formControlName="descriptions"
              />
            </div>
          </div>

          <div class="row">
            <div class="form-group col-xl-8 col-md-3 col-sm-12">
              <label class="form-label"
                >{{ "COMMON.JobConditions" | translate }}
              </label>
              <input
                type="text"
                class="form-control"
                name="conditions"
                formControlName="conditions"
              />
            </div>
          </div>

          <div class="row">
            <div class="form-group col-xl-8 col-md-3 col-sm-12">
              <label class="form-label">
                {{ "COMMON.JobResponsibilities" | translate }}
              </label>

              <input
                type="email"
                class="form-control"
                name="responsibilities"
                value=""
                formControlName="responsibilities"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
