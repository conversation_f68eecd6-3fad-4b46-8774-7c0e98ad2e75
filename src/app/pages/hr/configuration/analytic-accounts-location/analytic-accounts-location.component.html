<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="card-header border-0 cursor-pointer w-100 mb-4">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">
          {{ "COMMON.AnalyticAccountsLocation" | translate }}
        </h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              (click)="filter()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.Filter" | translate }}
              <i class="fa fa-filter"></i>
            </button>
            <button
              (click)="update()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>

            <button
              (click)="getLocation()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.CurrentLocation" | translate }}
              <i class="fa fa-map-marker"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="main-inputs">
      <div class="row mb-2">
        <div class="form-group col-xl-6 col-md-4 col-sm-12">
          <label for="analyticaccounts" class="form-label">
            {{ "COMMON.CostName" | translate }}
          </label>
          <ng-select
            id="CostID"
            formControlName="code"
            bindLabel="nameAr"
            value="CostID"
            bindValue="code"
            [items]="AnalyticAccounts"
            [searchable]="true"
          >
          </ng-select>
        </div>
      </div>

      <dx-load-panel
        #loadPanel
        shadingColor="rgba(0,0,0,0.4)"
        [position]="{ of: '#employee' }"
        [(visible)]="loadingVisible"
        [showIndicator]="true"
        [showPane]="true"
        [shading]="true"
        message="{{ 'COMMON.LOADING' | translate }}"
        [hideOnOutsideClick]="false"
      >
      </dx-load-panel>

      <div class="row mb-2">
        <div class="form-group col-xl-8 col-md-4 col-sm-12">
          <label for="CurrentLocation" class="form-label fw-bolder">
            {{ "COMMON.CurrentLocation" | translate }}
          </label>

          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
            <input
              formControlName="latitude"
              class="form-control"
              style="background-color: #f5f8fa"
            />
          </div>

          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
            <input
              formControlName="longitude"
              class="form-control"
              style="background-color: #f5f8fa"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<dx-data-grid id="employees" [rtlEnabled]="true" [dataSource]="data" keyExpr="code" [showRowLines]="true"
  [showBorders]="true" [columnAutoWidth]="true" (onExporting)="onExporting($event)" [allowColumnResizing]="true">
  <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="30"> </dxo-paging>
  <dxo-pager [visible]="true" [allowedPageSizes]="[30, 60, 'all']" [displayMode]="'compact'"
    [showPageSizeSelector]="true" [showInfo]="true" [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="code" [caption]="'COMMON.Code' | translate" width="150"></dxi-column>
  <dxi-column dataField="nameAr" [caption]="'COMMON.CostName' | translate">
    <dxo-header-filter>
      <dxo-search [enabled]="true">
      [searchExpr]="searchExpr"
      [editorOptions]="editorOptions"></dxo-search>
    </dxo-header-filter>
  </dxi-column>
<dxi-column dataField="longitude"width="200"></dxi-column>
<dxi-column dataField="latitude"width="200"></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

</dx-data-grid>
