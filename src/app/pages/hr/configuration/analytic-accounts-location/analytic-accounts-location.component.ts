import { CommonModule } from '@angular/common';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { environment } from 'src/environments/environment';
import { HRService } from '../../hr.service';

@Component({
  selector: 'app-analytic-accounts-location',
  templateUrl: './analytic-accounts-location.component.html',
  styleUrls: ['./analytic-accounts-location.component.css'],
  standalone: false,
})
export class AnalyticAccountsLocationComponent implements OnDestroy {
  viewListForm: FormGroup;
  data: ArrayStore;
  editorOptions: any;
  AnalyticAccounts: any[];
  moduleName = 'HR.AnalyticAccountsList';
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  actionsList: string[] = ['Export'];
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  searchCtrl: FormControl;

  latitude: any;
  longitude: any;
  clientDeviceInfo: any;
  loadingVisible = false;
  CostID: any;
  constructor(
    private fb: FormBuilder,
    private service: HRService,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute
  ) {
    service.baseUrl = `${environment.appUrls.AnalyticAccounts}`;

    this.viewListForm = this.fb.group({
      CostID: [null, Validators.required],
      latitude: [this.latitude || 0, Validators.required],
      longitude: [this.longitude || 0, Validators.required],
    });

    this.subscription.add(
      service.getCostCenters().subscribe((r) => {
        if (r.success) {
          this.AnalyticAccounts = r.data;

          this.cdk.detectChanges();
        }
      })
    );
  }

  ngOnInit(): void {
    // this.getLocation();
    this.viewListForm = new FormGroup({
      code: new FormControl(this.CostID),
      latitude: new FormControl(this.latitude),
      longitude: new FormControl(this.longitude),
    });

    // this.subscriptions.add(
    //   this.route.paramMap.subscribe((p) => {
    //     const id = p.get('id') || '';
    //     //if (id) {
    //     this.subscriptions.add(
    //       this.service.list(id).subscribe((r) => {
    //         if (r.success) {
    //           this.loadData(r);
    //         }
    //       })
    //     );
    //     //  }
    //   })
    // );
    this.searchCtrl.valueChanges.subscribe((v) => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(
          this.service.search(v).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            }
          })
        );
      } else if (id) {
        this.subscriptions.add(
          this.service.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      } else {
        this.subscriptions.add(
          this.service.list('').subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      }
    });
    this.viewListForm.patchValue({
      latitude: this.latitude,
      longitude: this.longitude,
    });
  }

  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }

  @ViewChild(DxDataGridComponent, { static: false })
  dataGrid: DxDataGridComponent;

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'falconData.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  loadData(r: any) {
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    /* this.getLocation();*/
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }

  degreesToRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
  filter() {
    const CostID = this.CostID;
    this.subscription.add(
      this.service.getProjectList().subscribe((r) => {
        console.log(r);
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  update() {
    let form = this.viewListForm.value;
    this.subscription.add(
      this.service.updatePorjectLocaton({ ...form }).subscribe((r) => {
        if (r.success) {
          this.clearData();

          this.cdk.detectChanges();
        }
      })
    );
  }

  getLocation(): void {
    this.loadingVisible = true;
    this.cdk.detectChanges();
    if (navigator.geolocation) {
      this.cdk.detectChanges();
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.latitude = position.coords.latitude;
          this.longitude = position.coords.longitude;
          this.viewListForm.patchValue({
            latitude: this.latitude,
            longitude: this.longitude,
          });
          this.loadingVisible = false;
          this.cdk.detectChanges();
        },
        (error) => {
          console.error('Error getting location: ', error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        } // تفعيل الدقة العالية
      );
    } else {
      console.log('Geolocation is not supported by this browser.');
    }
    this.cdk.detectChanges();
  }

  clearData() {
    this.viewListForm.get('CostID')?.reset();

    this.latitude = undefined;
    this.longitude = undefined;
    this.cdk.detectChanges();
  }
}
