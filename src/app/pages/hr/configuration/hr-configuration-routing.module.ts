import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdditionsTypesComponent } from './additions-types/additions-types.component';
import { AttendanceMachinesComponent } from './attendance-machines/attendance-machines.component';
import { DeductionsTypesComponent } from './deductions-types/deductions-types.component';
import { DocumentsTypeComponent } from './documents-type/documents-type.component';
import { EmployeesCategoriesComponent } from './employees-categories/employees-categories.component';
import { InsurancesComponent } from './insurances/insurances.component';
import { JobDescriptionsComponent } from './job-descriptions/job-descriptions.component';
import { QualificationsComponent } from './qualifications/qualifications.component';
import { ShiftComponent } from './shift/shift.component';
import { ShippingCompaniesComponent } from './shipping-companies/shipping-companies.component';
import { SponsorsComponent } from './sponsors/sponsors.component';
import { VacationsComponent } from './vacations/vacations.component';
import { JobDescriptionsViewComponent } from './job-descriptions/job-descriptions-view/job-descriptions-view.component';
import { ShippingCompaniesViewComponent } from './shipping-companies/shipping-companies-view/shipping-companies-view.component';
import { AnalyticAccountsLocationComponent } from './analytic-accounts-location/analytic-accounts-location.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'attendance_machines',
    pathMatch: 'full',
  },
  {
    path: 'attendance_machines',
    component: AttendanceMachinesComponent,
  },

  {
    path: 'insurances',
    component: InsurancesComponent,
  },
  {
    path: 'shift',
    component: ShiftComponent,
  },
  {
    path: 'vacations',
    component: VacationsComponent,
  },
  {
    path: 'additions_types',
    component: AdditionsTypesComponent,
  },
  {
    path: 'deductions_types',
    component: DeductionsTypesComponent,
  },
  {
    path: 'job_descriptions',
    component: JobDescriptionsComponent,
  },
  {
    path: 'job_descriptions_view',
    component: JobDescriptionsViewComponent,
  },
  {
    path: 'job_descriptions_view/:id',
    component: JobDescriptionsViewComponent,
  },
  {
    path: 'employees_categories',
    component: EmployeesCategoriesComponent,
  },
  {
    path: 'shipping_companies_view/:id',
    component: ShippingCompaniesViewComponent,
  },
  {
    path: 'shipping_companies_view',
    component: ShippingCompaniesViewComponent,
  },
  {
    path: 'shipping_companies',
    component: ShippingCompaniesComponent,
  },
  {
    path: 'documents_type',
    component: DocumentsTypeComponent,
  },
  {
    path: 'sponsors',
    component: SponsorsComponent,
  },
  {
    path: 'qualifications',
    component: QualificationsComponent,
  },
  {
    path: 'analytic_accounts_location',
    component: AnalyticAccountsLocationComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrConfigurationRoutingModule {}
