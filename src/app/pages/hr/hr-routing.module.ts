import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {hrHomeComponent} from './hr-home/hr-home.component';

const routes: Routes = [
    {
        path: '',
        component: hrHomeComponent,
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/hr-configuration.module').then((m) => m.HrConfigurationModule),
    },
    {
        path: 'employees',
        loadChildren: () =>
            import('./employees/hr-employees.module').then((m) => m.HrEmployeesModule),
    },
    {
        path: 'attendance_settings',
        loadChildren: () =>
            import('./attendance-settings/hr-attendance-settings.module').then((m) => m.HrAttendanceSettingsModule),
    },
    {
        path: 'attendance',
        loadChildren: () =>
            import('./attendance/hr-attendance.module').then((m) => m.HrAttendanceModule),
    },
    {
        path: 'rewards',
        loadChildren: () =>
            import('./rewards/hr-rewards.module').then((m) => m.HrRewardsModule),
    },
    {
        path: 'forms',
        loadChildren: () =>
            import('./forms/hr-forms.module').then((m) => m.HrFormsModule),
    },
    {
        path: 'employee_evaluation',
        loadChildren: () =>
            import('./employee-evaluation/hr-employee-evaluation.module').then((m) => m.HrEmployeeEvaluationModule),
    },
    {
        path: 'employee_training',
        loadChildren: () =>
            import('./training/hr-training.module').then((m) => m.HrTrainingModule),
    },
    {
        path: 'security',
        loadChildren: () =>
            import('./security/hr-security.module').then((m) => m.HrSecurityModule),
    },
    {
        path: 'salaries',
        loadChildren: () =>
            import('./salaries/hr-salaries.module').then((m) => m.HrSalariesModule),
    },
    {
        path: 'human_resources',
        loadChildren: () =>
            import('./hr/hr-hr.module').then((m) => m.HrHrModule),
    },
    {
        path: 'reports',
        loadChildren: () =>
            import('./reports/hr-reports.module').then((m) => m.HrReportsModule),
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class HrRoutingModule {
}
