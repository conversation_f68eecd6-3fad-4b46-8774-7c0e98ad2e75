import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AnnualTrainingPlanComponent } from './annual-training-plan/annual-training-plan.component';
import { EmployeeTrainingFormComponent } from './employee-training-form/employee-training-form.component';
import { EvaluateTrainingEffectivenessComponent } from './evaluate-training-effectiveness/evaluate-training-effectiveness.component';
import { TrainingEntitiesComponent } from './training-entities/training-entities.component';
import { TrainingNeedsComponent } from './training-needs/training-needs.component';
import { TrainingNeedsViewComponent } from './training-needs/training-needs-view/training-needs-view.component';
import { AnnualTrainingPlanViewComponent } from './annual-training-plan/annual-training-plan-view/annual-training-plan-view.component';
import { EmployeeTrainingFormViewComponent } from './employee-training-form/employee-training-form-view/employee-training-form-view.component';
import { EvaluateTrainingEffectivenessViewComponent } from './evaluate-training-effectiveness/evaluate-training-effectiveness-view/evaluate-training-effectiveness-view.component';
import { TrainingEntitiesViewComponent } from './training-entities/training-entities-view/training-entities-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'training_needs',
    pathMatch: 'full',
  },
  {
    path: 'training_needs_view',
    component: TrainingNeedsViewComponent,
  },
  {
    path: 'training_needs_view/:id',
    component: TrainingNeedsViewComponent,
  },
  {
    path: 'training_needs',
    component: TrainingNeedsComponent,
  },
  {
    path: 'annual_training_plan_view',
    component: AnnualTrainingPlanViewComponent,
  },
  {
    path: 'annual_training_plan_view/:id',
    component: AnnualTrainingPlanViewComponent,
  },
  {
    path: 'annual_training_plan',
    component: AnnualTrainingPlanComponent,
  },
  {
    path: 'employee_training_form_view',
    component: EmployeeTrainingFormViewComponent,
  },
  {
    path: 'employee_training_form_view/:id',
    component: EmployeeTrainingFormViewComponent,
  },
  {
    path: 'employee_training_form',
    component: EmployeeTrainingFormComponent,
  },
  {
    path: 'evaluate_training_effectiveness_view',
    component: EvaluateTrainingEffectivenessViewComponent,
  },
  {
    path: 'evaluate_training_effectiveness_view/:id',
    component: EvaluateTrainingEffectivenessViewComponent,
  },
  {
    path: 'evaluate_training_effectiveness',
    component: EvaluateTrainingEffectivenessComponent,
  },
  {
    path: 'training_entities_view',
    component: TrainingEntitiesViewComponent,
  },
  {
    path: 'training_entities_view/:id',
    component: TrainingEntitiesViewComponent,
  },
  {
    path: 'training_entities',
    component: TrainingEntitiesComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrTrainingRoutingModule {}
