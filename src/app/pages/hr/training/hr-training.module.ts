import { NgModule } from '@angular/core';

import { AnnualTrainingPlanComponent } from './annual-training-plan/annual-training-plan.component';
import { EmployeeTrainingFormComponent } from './employee-training-form/employee-training-form.component';
import { EvaluateTrainingEffectivenessComponent } from './evaluate-training-effectiveness/evaluate-training-effectiveness.component';
import { TrainingEntitiesComponent } from './training-entities/training-entities.component';
import { TrainingNeedsComponent } from './training-needs/training-needs.component';
import { HrTrainingRoutingModule } from './hr-training-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { TrainingNeedsViewComponent } from './training-needs/training-needs-view/training-needs-view.component';
import { AnnualTrainingPlanViewComponent } from './annual-training-plan/annual-training-plan-view/annual-training-plan-view.component';
import { EmployeeTrainingFormViewComponent } from './employee-training-form/employee-training-form-view/employee-training-form-view.component';
import { EvaluateTrainingEffectivenessViewComponent } from './evaluate-training-effectiveness/evaluate-training-effectiveness-view/evaluate-training-effectiveness-view.component';
import { TrainingEntitiesViewComponent } from './training-entities/training-entities-view/training-entities-view.component';

@NgModule({
  declarations: [
    TrainingNeedsComponent,
    AnnualTrainingPlanComponent,
    EmployeeTrainingFormComponent,
    EvaluateTrainingEffectivenessComponent,
    TrainingEntitiesComponent,
    TrainingNeedsViewComponent,
    AnnualTrainingPlanViewComponent,
    EmployeeTrainingFormViewComponent,
    EvaluateTrainingEffectivenessViewComponent,
    TrainingEntitiesViewComponent,
  ],
  imports: [HrTrainingRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrTrainingModule {}
