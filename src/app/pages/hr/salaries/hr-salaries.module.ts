import { MonthlyIqamaCostComponent } from './monthly-iqama-cost/monthly-iqama-cost.component';
import { PrintSalariesComponent } from './print-salaries/print-salaries.component';
import { SalariesPreparationComponent } from './salaries-preparation/salaries-preparation.component';
import { NgModule } from '@angular/core';
import { HrSalariesRoutingModule } from './hr-salaries-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { IncomeTaxComponent } from './income-tax/income-tax.component';

@NgModule({
  declarations: [
    SalariesPreparationComponent,
    PrintSalariesComponent,
    IncomeTaxComponent,
    MonthlyIqamaCostComponent,
  ],
  imports: [HrSalariesRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrSalariesModule {}
