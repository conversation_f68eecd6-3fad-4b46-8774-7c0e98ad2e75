<div class="card-header border-0 cursor-pointer w-100">
  <div
    class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
  >
    <h3 class="fw-bolder m-0">{{ "COMMON.PrintSalaries" | translate }}</h3>
    <div [style.position]="'relative'">
      <div class="btn-group">
        <button (click)="filter()" class="btn btn-sm btn-active-light-primary">
          {{ "COMMON.Filter" | translate }}
          <i class="fa fa-filter"></i>
        </button>
      </div>
      <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
      <button
        class="btn btn-icon btn-active-light-primary mx-2"
        (click)="toggleMenu()"
        data-bs-toggle="tooltip"
        data-bs-placement="top"
        data-bs-trigger="hover"
        title="Settings"
      >
        <i class="fa fa-gear"></i>
      </button>
      <div
        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
        [class.show]="menuOpen"
        [style.position]="'absolute'"
        [style.top]="'100%'"
        [style.zIndex]="'1050'"
        [style.left]="isRtl ? '0' : 'auto'"
        [style.right]="!isRtl ? '0' : 'auto'"
      >
        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            (click)="exportToExcel()"
            data-kt-company-table-filter="delete_row"
          >
            {{ "COMMON.ExportToExcel" | translate }}
          </a>
        </div>
        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openSmsModal()"
          >
            {{ "COMMON.SendViaSMS" | translate }}
          </a>
        </div>

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openEmailModal()"
          >
            {{ "COMMON.SendViaEmail" | translate }}
          </a>
        </div>

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            data-kt-company-table-filter="delete_row"
            (click)="openWhatsappModal()"
          >
            {{ "COMMON.SendViaWhatsapp" | translate }}
          </a>
        </div>

        <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.PrintSalaries'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses"
            data-kt-company-table-filter="delete_row"
            >{{ "COMMON.Print" | translate }}</a
          >
        </div>
        <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.PrintSalaries'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="form-group col-xl-3 col-md-4 col-sm-12">
    <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
    <input
      id="fromDate"
      type="date"
      [(ngModel)]="fromDateValue"
      class="form-control"
      style="background-color: #f5f8fa"
    />
  </div>
  <div class="form-group col-xl-3 col-md-4 col-sm-12">
    <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
    <input
      id="toDate"
      type="date"
      [(ngModel)]="toDateValue"
      class="form-control"
      style="background-color: #f5f8fa"
    />
  </div>
  <div class="form-group col-5">
    <label class="form-label">{{ "COMMON.Employee" | translate }}</label>
    <dx-drop-down-box
      [(value)]="gridBoxValue"
      style="background-color: #f5f8fa"
      class="form-control"
      [inputAttr]="{ 'aria-label': 'Owner' }"
      valueExpr="ID"
      displayExpr="nameAr"
      placeholder="Select a value..."
      [showClearButton]="true"
      [dataSource]="gridDataSource"
    >
      <div *dxTemplate="let data of 'content'">
        <dx-data-grid
          [dataSource]="gridDataSource"
          [rtlEnabled]="true"
          [columns]="['nameAr', 'job', 'depart']"
          [selection]="{ mode: 'multiple' }"
          [hoverStateEnabled]="true"
          [paging]="{ enabled: true, pageSize: 10 }"
          [filterRow]="{ visible: true }"
          [scrolling]="{ mode: 'virtual' }"
          [height]="345"
          [(selectedRowKeys)]="gridBoxValue"
        >
        </dx-data-grid>
      </div>
    </dx-drop-down-box>
  </div>

  <!-- <div class="form-group col-xl-2 col-md-4 col-sm-12">
      <label for="sectorId" class="form-label">
        {{ "COMMON.Sector" | translate }}
      </label>
      <ng-select
        id="sectorId"
        [(ngModel)]="sectorId"
        bindLabel="name"
        bindValue="id"
        [items]="Sectors"
      ></ng-select>
    </div> -->
</div>

<dx-data-grid
  id="employees"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="code"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="code"
    [caption]="'COMMON.Code' | translate"
  ></dxi-column>
  <dxi-column dataField="name" [caption]="'COMMON.Employee' | translate">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"-->
      <!--[searchExpr]="searchExpr"
        [editorOptions]="editorOptions"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>
  <dxi-column
    dataField="BarCode"
    [caption]="'COMMON.JobNumber' | translate"
  ></dxi-column>

  <dxi-column dataField="jop" [caption]="'COMMON.Job' | translate"></dxi-column>

  <dxi-column dataField="ksm" [caption]="'COMMON.Department' | translate">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>
  <dxi-column
    dataField="ed_sahar"
    [caption]="'Salary.ed_sahar' | translate"
  ></dxi-column>
  <dxi-column
    dataField="oth_sal"
    [caption]="'Salary.oth_sal' | translate"
  ></dxi-column>
  <dxi-column
    dataField="elawa"
    [caption]="'Salary.elawa' | translate"
  ></dxi-column>
  <dxi-column
    dataField="mobile"
    [caption]="'Salary.mobile' | translate"
  ></dxi-column>
  <dxi-column dataField="Day" [caption]="'Salary.Day' | translate"></dxi-column>
  <dxi-column dataField="Tel" [caption]="'Salary.Tel' | translate"></dxi-column>
  <dxi-column
    dataField="b_ent"
    [caption]="'Salary.b_ent' | translate"
  ></dxi-column>
  <dxi-column
    dataField="others3"
    [caption]="'Salary.others3' | translate"
  ></dxi-column>
  <dxi-column
    dataField="b_tab"
    [caption]="'Salary.b_tab' | translate"
  ></dxi-column>
  <dxi-column
    dataField="hawafez"
    [caption]="'Salary.hawafez' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Take_Schole"
    [caption]="'Salary.Take_Schole' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Take_Percent"
    [caption]="'Salary.Take_Percent' | translate"
  ></dxi-column>
  <dxi-column
    dataField="b_malbas"
    [caption]="'Salary.b_malbas' | translate"
  ></dxi-column>
  <dxi-column
    dataField="b_food"
    [caption]="'Salary.b_food' | translate"
  ></dxi-column>
  <dxi-column
    dataField="ed_agaza"
    [caption]="'Salary.ed_agaza' | translate"
  ></dxi-column>
  <dxi-column
    dataField="takher"
    [caption]="'Salary.takher' | translate"
  ></dxi-column>
  <dxi-column
    dataField="totalm"
    [caption]="'Salary.totalm' | translate"
  ></dxi-column>
  <dxi-column
    dataField="gaza"
    [caption]="'Salary.gaza' | translate"
  ></dxi-column>
  <dxi-column
    dataField="moneytakher"
    [caption]="'Salary.moneytakher' | translate"
  ></dxi-column>
  <dxi-column
    dataField="moneygyab"
    [caption]="'Salary.moneygyab' | translate"
  ></dxi-column>
  <dxi-column
    dataField="solfa_cut"
    [caption]="'Salary.solfa_cut' | translate"
  ></dxi-column>
  <dxi-column
    dataField="MedicalInsurance"
    [caption]="'Salary.MedicalInsurance' | translate"
  ></dxi-column>
  <dxi-column
    dataField="sick"
    [caption]="'Salary.sick' | translate"
  ></dxi-column>
  <dxi-column dataField="tax" [caption]="'Salary.tax' | translate"></dxi-column>
  <dxi-column dataField="ins" [caption]="'Salary.ins' | translate"></dxi-column>
  <dxi-column
    dataField="totalkh"
    [caption]="'Salary.totalkh' | translate"
  ></dxi-column>
  <dxi-column
    dataField="safy"
    [caption]="'Salary.safy' | translate"
  ></dxi-column>
  <dxi-column
    dataField="month"
    [caption]="'Salary.month' | translate"
  ></dxi-column>
  <dxi-column
    dataField="year"
    [caption]="'Salary.year' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Nationality"
    [caption]="'Salary.Nationality' | translate"
  ></dxi-column>
  <dxi-column
    dataField="planName"
    [caption]="'Salary.planName' | translate"
  ></dxi-column>
  <dxi-column
    dataField="brnchs"
    [caption]="'Salary.brnchs' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Badl_Agaza"
    [caption]="'Salary.Badl_Agaza' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Reward"
    [caption]="'Salary.Reward' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Delivered"
    [caption]="'COMMON.Delivered' | translate"
  ></dxi-column>
  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>

    <dxi-total-item column="val" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
