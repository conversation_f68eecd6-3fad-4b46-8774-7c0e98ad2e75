import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {MonthlyIqamaCostComponent} from './monthly-iqama-cost/monthly-iqama-cost.component';
import {PrintSalariesComponent} from './print-salaries/print-salaries.component';
import {SalariesPreparationComponent} from './salaries-preparation/salaries-preparation.component';
import { IncomeTaxComponent } from './income-tax/income-tax.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'salaries_preparation',
        pathMatch: 'full'
    },
    {
        path: 'salaries_preparation',
        component: SalariesPreparationComponent,
    },
    {
        path: 'print_salaries',
        component: PrintSalariesComponent,
    },
    {
        path: 'monthly_iqama_cost',
        component: MonthlyIqamaCostComponent,
  },
  {
    path: 'income_tax',
    component: IncomeTaxComponent,
  },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class HrSalariesRoutingModule {
}
