import {NgModule} from '@angular/core';
import {AttendanceReportsComponent} from './attendance_reports/attendance_reports.component';
import {BonusesAndIncentivesReports} from './bonuses_and_incentives_reports/bonuses_and_incentives_reports.component';
import {DeductionsReportsComponent} from './deductions-reports/deductions-reports.component';
import {DocumentsReportsComponent} from './documents_reports/documents_reports.component';
import {Driving_licencesReportsComponent} from './driving_licences_reports/driving_licences_reports.component';
import {Due_SalariesReportsComponent} from './due_salaries_reports/due_salaries_reportss.component';
import {Employee_lifeReportsComponent} from './employee_life_reports/employee_life_reports.component';
import {Insurances_DueReportsComponent} from './insurances_due_reports/insurances_due_reports.component';
import {LoanReportsComponent} from './loans_reports/loans_reports.component';
import {ManagersReportsComponent} from './managers_reports/managers_reports.component';
import {MissionsReportsComponent} from './missions_reports/missions_reports.component';
import {Monthly_DeductionsReportsComponent} from './monthly_deductions_reports/monthly_deductions_reports.component';
import {
    Movements_DuringReportsComponent
} from './movements_during_period_reports/movements_during_period_reports.component';
import {Nationality_JobsReportsComponent} from './nationality_jobs_reports/nationality_jobs_reports.component';
import {OverTimeReportsComponent} from './over_time_reports/over_time_reports.component';
import {PermissionsReportsComponent} from './permissions_reports/permissions_reports.component';
import {PlayoffsReportsComponent} from './playoffs_reports/playoffs_reports.component';
import {RecruitedReportsComponent} from './recruited_during_period_reports/recruited_during_period_reports.component';
import {TravelsReportsComponent} from './travels_reports/travels_reports.component';
import {VacationsReportsComponent} from './vacations_reports/vacations_reports.component';
import {HrReportsRoutingModule} from "./hr-reports-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {RouterModule} from "@angular/router";

@NgModule({
    declarations: [
        DeductionsReportsComponent,
        BonusesAndIncentivesReports,
        LoanReportsComponent,
        VacationsReportsComponent,
        DocumentsReportsComponent,
        Driving_licencesReportsComponent,
        Employee_lifeReportsComponent,
        Insurances_DueReportsComponent,
        ManagersReportsComponent,
        TravelsReportsComponent,
        RecruitedReportsComponent,
        MissionsReportsComponent,
        Monthly_DeductionsReportsComponent,
        Movements_DuringReportsComponent,
        Nationality_JobsReportsComponent,
        OverTimeReportsComponent,
        PermissionsReportsComponent,
        PlayoffsReportsComponent,
        AttendanceReportsComponent,
        Due_SalariesReportsComponent,
    ],
    imports: [HrReportsRoutingModule, SharedModule],
    exports: [RouterModule],
})
export class HrReportsModule {
}
