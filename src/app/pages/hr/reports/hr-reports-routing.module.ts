import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AttendanceReportsComponent} from './attendance_reports/attendance_reports.component';
import {BonusesAndIncentivesReports} from './bonuses_and_incentives_reports/bonuses_and_incentives_reports.component';
import {DeductionsReportsComponent} from './deductions-reports/deductions-reports.component';
import {DocumentsReportsComponent} from './documents_reports/documents_reports.component';
import {Driving_licencesReportsComponent} from './driving_licences_reports/driving_licences_reports.component';
import {Due_SalariesReportsComponent} from './due_salaries_reports/due_salaries_reportss.component';
import {Employee_lifeReportsComponent} from './employee_life_reports/employee_life_reports.component';
import {Insurances_DueReportsComponent} from './insurances_due_reports/insurances_due_reports.component';
import {LoanReportsComponent} from './loans_reports/loans_reports.component';
import {ManagersReportsComponent} from './managers_reports/managers_reports.component';
import {MissionsReportsComponent} from './missions_reports/missions_reports.component';
import {Monthly_DeductionsReportsComponent} from './monthly_deductions_reports/monthly_deductions_reports.component';
import {
    Movements_DuringReportsComponent
} from './movements_during_period_reports/movements_during_period_reports.component';
import {Nationality_JobsReportsComponent} from './nationality_jobs_reports/nationality_jobs_reports.component';
import {OverTimeReportsComponent} from './over_time_reports/over_time_reports.component';
import {PermissionsReportsComponent} from './permissions_reports/permissions_reports.component';
import {PlayoffsReportsComponent} from './playoffs_reports/playoffs_reports.component';
import {RecruitedReportsComponent} from './recruited_during_period_reports/recruited_during_period_reports.component';
import {TravelsReportsComponent} from './travels_reports/travels_reports.component';
import {VacationsReportsComponent} from './vacations_reports/vacations_reports.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'deductions_reports',
        pathMatch: 'full'
    },
    {
        path: 'deductions_reports',
        component: DeductionsReportsComponent,
    },
    {
        path: 'bonuses_and_incentives_reports',
        component: BonusesAndIncentivesReports,
    },
    {
        path: 'loans_reports',
        component: LoanReportsComponent,
    },
    {
        path: 'vacations_reports',
        component: VacationsReportsComponent,
    },
    {
        path: 'over_time_reports',
        component: OverTimeReportsComponent,
    },
    {
        path: 'permissions_reports',
        component: PermissionsReportsComponent,
    },
    {
        path: 'attendance_reports',
        component: AttendanceReportsComponent,
    },
    {
        path: 'missions_reports',
        component: MissionsReportsComponent,
    },
    {
        path: 'insurances_due_reports',
        component: Insurances_DueReportsComponent,
    },
    {
        path: 'managers_reports',
        component: ManagersReportsComponent,
    },
    {
        path: 'employee_life_reports',
        component: Employee_lifeReportsComponent,
    },
    {
        path: 'documents_reports',
        component: DocumentsReportsComponent,
    },
    {
        path: 'travels_reports',
        component: TravelsReportsComponent,
    },
    {
        path: 'driving_licences_reports',
        component: Driving_licencesReportsComponent,
    },
    {
        path: 'nationality_jobs_reports',
        component: Nationality_JobsReportsComponent,
    },
    {
        path: 'movements_during_period_reports',
        component: Movements_DuringReportsComponent,
    },
    {
        path: 'recruited_during_period_reports',
        component: RecruitedReportsComponent,
    },
    {
        path: 'due_salaries_reports',
        component: Due_SalariesReportsComponent,
    },
    {
        path: 'playoffs_reports',
        component: PlayoffsReportsComponent,
    },
    {
        path: 'monthly_deductions_reports',
        component: Monthly_DeductionsReportsComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class HrReportsRoutingModule {
}
