import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { HRService } from '../../hr.service';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';

import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { DxDataGridComponent } from 'devextreme-angular';
import { formatDate } from '@angular/common';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-monthly_deductions_reports',
  templateUrl: './monthly_deductions_reports.component.html',
  styleUrls: ['./monthly_deductions_reports.component.scss'],
  standalone: false,
})
export class Monthly_DeductionsReportsComponent implements OnDestroy {
  moduleName = 'HR.MonthlyDeductionsReports';

  data: any[];
  gridDataSource: any[];
  tempdata: 123456;
  tempstring: '123456';
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;

  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(private service: HRService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.hrReports}Monthly_Deductions`;
    // this.subscription.add(
    //   service.list().subscribe((r) => {
    //     if (r.success) {
    //       this.data = r.data;
    //       this.cdk.detectChanges();
    //     }
    //   })
    // );
    this.subscription.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.gridDataSource = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    const currentYear = new Date().getFullYear();
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const employees =
      this.gridBoxValue.length > 0
        ? this.gridBoxValue.map((e: any) => e.id)
        : [];
    this.subscription.add(
      this.service.list({ FromDate, ToDate, employees }).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
