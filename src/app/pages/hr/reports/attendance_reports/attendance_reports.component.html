<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.AttendanceReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.AttendanceReports'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.AttendanceReports'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="main-inputs">
    <div class="row">
      <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
        <input
          id="fromDate"
          type="date"
          [(ngModel)]="fromDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
      <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
      <div class="form-group col-5">
        <label class="form-label">{{ "COMMON.Employee" | translate }}</label>
        <dx-drop-down-box
          [(value)]="gridBoxValue"
          style="background-color: #f5f8fa"
          class="form-control"
          [inputAttr]="{ 'aria-label': 'Owner' }"
          valueExpr="ID"
          displayExpr="nameAr"
          placeholder="Select a value..."
          [showClearButton]="true"
          [dataSource]="gridDataSource"
        >
          <div *dxTemplate="let data of 'content'">
            <dx-data-grid
              [dataSource]="gridDataSource"
              [rtlEnabled]="true"
              [columns]="['nameAr', 'job', 'depart']"
              [selection]="{ mode: 'multiple' }"
              [hoverStateEnabled]="true"
              [paging]="{ enabled: true, pageSize: 10 }"
              [filterRow]="{ visible: true }"
              [scrolling]="{ mode: 'virtual' }"
              [height]="345"
              [(selectedRowKeys)]="gridBoxValue"
            >
            </dx-data-grid>
          </div>
        </dx-drop-down-box>
      </div>
    </div>
  </div>
</div>
<dx-data-grid
  id="employees"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="code"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="30"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[30, 60, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="code"
    [caption]="'COMMON.Code' | translate"
  ></dxi-column>
  <dxi-column dataField="name" [caption]="'COMMON.Employee' | translate">
    <dxo-header-filter>
      <!--<dxo-search [enabled]="true"-->
      <!--[searchExpr]="searchExpr"
      [editorOptions]="editorOptions"></dxo-search>-->
    </dxo-header-filter>
  </dxi-column>
  <dxi-column
    dataField="jobname"
    [caption]="'COMMON.jobname' | translate"
  ></dxi-column>
  <dxi-column
    dataField="BranchName"
    [caption]="'COMMON.Sector' | translate"
  ></dxi-column>
  <dxi-column dataField="depname" [caption]="'COMMON.Department' | translate">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>
  <dxi-column
    dataField="planName"
    [caption]="'Salary.WorkPlan' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Explain"
    [caption]="'COMMON.Notes' | translate"
  ></dxi-column>
  <dxi-column
    dataField="date"
    [caption]="'COMMON.Date' | translate"
  ></dxi-column>

  <dxi-column
    dataField="timeIn"
    [caption]="'COMMON.timeIn' | translate"
  ></dxi-column>

  <dxi-column
    dataField="Brakeout"
    [caption]="'COMMON.BreakOut' | translate"
  ></dxi-column>
  <dxi-column
    dataField="brakein"
    [caption]="'COMMON.BreakIn' | translate"
  ></dxi-column>

  <dxi-column
    dataField="timeOut"
    [caption]="'COMMON.TimeOut' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Late"
    [caption]="'COMMON.Late' | translate"
  ></dxi-column>
  <dxi-column
    dataField="LateOuttime"
    [caption]="'COMMON.LateOutTime' | translate"
  ></dxi-column>
  <dxi-column
    dataField="EmpWorkTime"
    [caption]="'COMMON.EmpWorkTime' | translate"
  ></dxi-column>
  <dxi-column
    dataField="AccCode"
    [caption]="'COMMON.AccCode' | translate"
  ></dxi-column>
  <dxi-column
    dataField="AccName"
    [caption]="'COMMON.CostName' | translate"
  ></dxi-column>
  <dxi-column
    dataField="DayCode"
    [caption]="'COMMON.Day' | translate"
  ></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
