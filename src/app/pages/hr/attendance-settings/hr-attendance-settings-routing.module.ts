import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DaysandhoursComponent } from './daysandhours/daysandhours.component';
import { EditShiftPlansComponent } from './edit-shift-plans/edit-shift-plans.component';
import { OfficialHolidaysComponent } from './official-holidays/official-holidays.component';
import { ShiftPlansComponent } from './shift-plans/shift-plans.component';
import { WorkingHoursAndPenaltiesComponent } from './working-hours-and-penalties/working-hours-and-penalties.component';
import { DaysandhoursViewComponent } from './daysandhours/daysandhours-view/daysandhours-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'Shift_Plans',
    pathMatch: 'full',
  },
  {
    path: 'Shift_Plans',
    component: ShiftPlansComponent,
  },
  {
    path: 'Official_Holidays',
    component: OfficialHolidaysComponent,
  },
  {
    path: 'daysandhours_view',
    component: DaysandhoursViewComponent,
  },
  {
    path: 'daysandhours_view/:id',
    component: DaysandhoursViewComponent,
  },
  {
    path: 'daysandhours',
    component: DaysandhoursComponent,
  },
  {
    path: 'Edit_Shift_Plans',
    component: EditShiftPlansComponent,
  },
  {
    path: 'working_hours_and_penalties',
    component: WorkingHoursAndPenaltiesComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrAttendanceSettingsRoutingModule {}
