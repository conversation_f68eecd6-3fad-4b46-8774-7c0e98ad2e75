<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="main-inputs">
      <div class="row align-items-center">
        <h6 class="col-12 mb-3">
          {{ "COMMON.OfficialHolidayForAllEmployeesDuringPeriod" | translate }}
        </h6>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-12 d-flex justify-content-start gap-2">
          <a (click)="filter()" class="btn btn-success btn-sm">{{
            "COMMON.SaveChanges" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.Cancel" | translate
          }}</a>
        </div>
      </div>

      <hr class="dotted-line" />

      <div class="row align-items-center">
        <h6 class="col-12 mb-3">
          {{ "COMMON.OfficialVacationPeriodForParticularCategory" | translate }}
        </h6>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="customer" class="form-label">
            {{ "COMMON.Department" | translate }}
          </label>
          <ng-select
            id="department"
            formControlName="department"
            bindLabel="nameAr"
            bindValue="id"
            [items]="departments"
          ></ng-select>
        </div>
        <div class="form-group col-12 d-flex justify-content-start gap-2">
          <a (click)="filter()" class="btn btn-success btn-sm">{{
            "COMMON.SaveChanges" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.Cancel" | translate
          }}</a>
        </div>
      </div>

      <hr class="dotted-line" />

      <div class="row align-items-center">
        <h6 class="col-12 mb-3">
          {{ "COMMON.OfficialHolidayForParticularCategory" | translate }}
        </h6>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="daysId" class="form-label">
            {{ "COMMON.DailyVacation" | translate }}
          </label>
          <ng-select
            id="daysId"
            formControlName="daysId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="days"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="customer" class="form-label">
            {{ "COMMON.Department" | translate }}
          </label>
          <ng-select
            id="department"
            formControlName="department"
            bindLabel="nameAr"
            bindValue="id"
            [items]="departments"
          ></ng-select>
        </div>
        <div class="form-group col-12 d-flex justify-content-start gap-2">
          <a (click)="filter()" class="btn btn-success btn-sm">{{
            "COMMON.SaveChanges" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.Cancel" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.CancelDelayOnThisDay" | translate
          }}</a>
        </div>
      </div>

      <hr class="dotted-line" />

      <div class="row align-items-center">
        <h6 class="col-12 mb-3">
          {{
            "COMMON.OfficialHolidayForSpecificCategoryDuringPeriodOfTime"
              | translate
          }}
        </h6>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>

        <div class="form-group col-xl-3 col-lg-3 col-md-4 col-sm-12 mb-3">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDateValue"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="periodInDaysId" class="form-label">
            {{ "COMMON.PeriodInDays" | translate }}
          </label>
          <input
            id="periodInDaysId"
            type="number"
            formControlName="periodInDaysId"
            class="form-control"
            style="background-color: inherit"
            min="1"
          />
        </div>
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="daysId" class="form-label">
            {{ "COMMON.DailyVacation" | translate }}
          </label>
          <ng-select
            id="daysId"
            formControlName="daysId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="days"
          ></ng-select>
        </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="customer" class="form-label">
            {{ "COMMON.Department" | translate }}
          </label>
          <ng-select
            id="department"
            formControlName="department"
            bindLabel="nameAr"
            bindValue="id"
            [items]="departments"
          ></ng-select>
        </div>
        <div class="form-group col-12 d-flex justify-content-start gap-2">
          <a (click)="filter()" class="btn btn-success btn-sm">{{
            "COMMON.SaveChanges" | translate
          }}</a>
        </div>
      </div>

      <hr class="dotted-line" />

      <div class="row align-items-center">
        <h6 class="col-12 mb-3">
          {{ "COMMON.OfficialHolidayForAllEmployees" | translate }}
        </h6>

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="daysId" class="form-label">
            {{ "COMMON.DailyVacation" | translate }}
          </label>
          <ng-select
            id="daysId"
            formControlName="daysId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="days"
          ></ng-select>
        </div>

        <div class="form-group col-12 d-flex justify-content-start gap-2">
          <a (click)="filter()" class="btn btn-success btn-sm">{{
            "COMMON.SaveChanges" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.Cancel" | translate
          }}</a>
          <a (click)="filter()" class="btn btn-danger btn-sm">{{
            "COMMON.CancelDelayOnThisDay" | translate
          }}</a>
        </div>
      </div>
    </div>
  </div>
</form>
