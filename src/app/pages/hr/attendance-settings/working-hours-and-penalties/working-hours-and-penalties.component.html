<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">
          {{ "COMMON.WorkingHoursAndPenalties" | translate }}
        </h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              (click)="filter()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.Filter" | translate }}
              <i class="fa fa-filter"></i>
            </button>
            <button
              (click)="update()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
            <button
              [routerLink]="[
                '/hr/human_resources/visa_view',
                selectedItemKeys[0]
              ]"
              [hidden]="
                selectedItemKeys.length > 1 || selectedItemKeys.length == 0
              "
              class="btn btn-sm btn-active-light-primary"
              *hasPermission="{
                module: 'HR.WorkingHoursAndPenalties',
                action: 'updateAction'
              }"
            >
              {{ "COMMON.EDIT" | translate }}
              <i class="fa fa-edit"></i>
            </button>
            <button
              (click)="deleteRecords()"
              [hidden]="!selectedItemKeys.length"
              class="btn btn-sm btn-active-light-primary"
              *hasPermission="{
                module: 'HR.WorkingHoursAndPenalties',
                action: 'deleteAction'
              }"
            >
              {{ "COMMON.DELETE" | translate }}
              <i class="fa fa-trash"></i>
            </button>
          </div>
          <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                      (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'HR.WorkingHoursAndPenalties'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
            <!-- <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'HR.WorkingHoursAndPenalties'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses?withLogo=true"
                data-kt-company-table-filter="delete_row"
                >Print With Logo</a
              >
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="main-inputs">
      <div class="row">
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="workCategoryId" class="form-label">
            {{ "Salary.WorkPlan" | translate }}
          </label>
          <ng-select
            id="workCategoryId"
            formControlName="workCategoryId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="workCategories"
          ></ng-select>
        </div>
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <label for="shiftId" class="form-label">
            {{ "COMMON.Shift" | translate }}
          </label>
          <ng-select
            id="shiftId"
            formControlName="shiftId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="Shifts"
          ></ng-select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label">
            {{ "COMMON.TimeAttendance" | translate }}
          </label>
          <input
            id="timeAttendance"
            type="time"
            formControlName="timeAttendance"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label">
            {{ "COMMON.DepartureTime" | translate }}
          </label>
          <input
            id="departureTime"
            type="time"
            formControlName="departureTime"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label">
            {{ "COMMON.ExitRest" | translate }}
          </label>
          <input
            id="exitRest"
            type="time"
            formControlName="exitRest"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label">
            {{ "COMMON.ReturnFromRest" | translate }}
          </label>
          <input
            id="returnFromRest"
            type="time"
            formControlName="returnFromRest"
            class="form-control"
            style="background-color: inherit"
          />
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="penaltyForAbsenceWithoutPermission" class="form-label">
            {{ "COMMON.PenaltyForAbsenceWithoutPermission" | translate }}
          </label>
          <input
            type="number"
            id="penaltyForAbsenceWithoutPermission"
            name="penaltyForAbsenceWithoutPermission"
            class="form-control"
            style="background-color: inherit"
            formControlName="penaltyForAbsenceWithoutPermission"
          />
          <span>{{ "COMMON.Day" | translate }}</span>
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="penaltyForAbsenceWithPermission" class="form-label">
            {{ "COMMON.PenaltyForAbsenceWithPermission" | translate }}
          </label>
          <input
            type="number"
            id="penaltyForAbsenceWithPermission"
            name="penaltyForAbsenceWithPermission"
            class="form-control"
            style="background-color: inherit"
            formControlName="penaltyForAbsenceWithPermission"
          />
          <span>{{ "COMMON.Day" | translate }}</span>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="maximumDelayAllowed" class="form-label">
            {{ "COMMON.MaximumDelayAllowed" | translate }}
          </label>
          <input
            type="number"
            id="maximumDelayAllowed"
            name="maximumDelayAllowed"
            class="form-control"
            style="background-color: inherit"
            formControlName="maximumDelayAllowed"
          />
          <span>{{ "COMMON.Minute" | translate }}</span>
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="maximumDelayWithPermission" class="form-label">
            {{ "COMMON.MaximumDelayWithPermission" | translate }}
          </label>
          <input
            type="number"
            id="maximumDelayWithPermission"
            name="maximumDelayWithPermission"
            class="form-control"
            style="background-color: inherit"
            formControlName="maximumDelayWithPermission"
          />
          <span>{{ "COMMON.Minute" | translate }}</span>
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="penaltyForDelayWithPermission" class="form-label">
            {{ "COMMON.PenaltyForDelayWithPermission" | translate }}
          </label>
          <input
            type="number"
            id="penaltyForDelayWithPermission"
            name="penaltyForDelayWithPermission"
            class="form-control"
            style="background-color: inherit"
            formControlName="penaltyForDelayWithPermission"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label for="delayBypass" class="form-label">
            {{ "COMMON.DelayBypass" | translate }}
          </label>
          <input
            type="number"
            id="delayBypass"
            name="delayBypass"
            class="form-control"
            style="background-color: inherit"
            formControlName="delayBypass"
          />
          <span>{{ "COMMON.Minute" | translate }}</span>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <dx-check-box
            formControlName="penaltyCheckbox"
            [value]="viewListForm.get('penaltyCheckbox')?.value"
            (onValueChanged)="toggleInputs($event.value)"
          ></dx-check-box>
          <label class="form-label d-block p-2">
            {{ "COMMON.PenaltyDelayingHourWithoutPermission" | translate }}
          </label>
          <input
            type="number"
            id="penaltyDelayingHourWithoutPermission"
            class="form-control"
            style="background-color: inherit"
            formControlName="penaltyDelayingHourWithoutPermission"
            [disabled]="!viewListForm.get('penaltyCheckbox')?.value"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label p-2">{{
            "COMMON.QuarterDayDelay" | translate
          }}</label>
          <span class="fs-2">{{ "COMMON.GreaterThan" | translate }}</span>
          <input
            type="number"
            class="form-control"
            style="background-color: inherit"
            formControlName="quarterDayDelay"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label p-2">{{
            "COMMON.HalfDayDelay" | translate
          }}</label>
          <span class="fs-2">{{ "COMMON.GreaterThan" | translate }}</span>
          <input
            type="number"
            class="form-control"
            style="background-color: inherit"
            formControlName="halfDayDelay"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label p-2">{{
            "COMMON.ThreeQuartersDayDelay" | translate
          }}</label>
          <span class="fs-2">{{ "COMMON.GreaterThan" | translate }}</span>
          <input
            type="number"
            class="form-control"
            style="background-color: inherit"
            formControlName="threeQuartersDayDelay"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-xl-3 col-md-6 col-sm-12">
          <label class="form-label p-2">{{
            "COMMON.OneDayDelay" | translate
          }}</label>
          <span class="fs-2 px-7">{{ "COMMON.GreaterThan" | translate }}</span>
          <input
            type="number"
            class="form-control"
            style="background-color: inherit"
            formControlName="oneDayDelay"
          />
          <span>{{ "COMMON.hour" | translate }}</span>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="maximumAddition" class="form-label">
            {{ "COMMON.MaximumAddition" | translate }}
          </label>
          <input
            type="number"
            id="maximumAddition"
            name="maximumAddition"
            class="form-control"
            style="background-color: inherit"
            formControlName="maximumAddition"
          />
          <span
            ><span class="fs-2">%</span>
            {{ "COMMON.FromSalary" | translate }}</span
          >
        </div>

        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label for="workSystemId" class="form-label">
            {{ "COMMON.WorkSystem" | translate }}
          </label>
          <ng-select
            id="workSystemId"
            formControlName="workSystemId"
            bindLabel="nameAr"
            bindValue="id"
            [items]="workSystems"
          ></ng-select>
        </div>
      </div>
    </div>
  </div>
</form>
