import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import { HRService } from '../../hr.service';
import { environment } from '../../../../../environments/environment';
import { DxDataGridTypes } from 'devextreme-angular/ui/data-grid';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';

@Component({
  selector: 'app-working-hours-and-penalties',
  templateUrl: './working-hours-and-penalties.component.html',
  styleUrls: ['./working-hours-and-penalties.component.scss'],
  standalone: false,
})
export class WorkingHoursAndPenaltiesComponent implements OnDestroy {
  moduleName = 'HR.WorkingHoursAndPenalties';

  viewListForm: FormGroup;
  data: ArrayStore;
  workCategories: any[];
  workSystems: any[];
  Shifts: { id: number; nameAr: string }[] = [
    { id: 1, nameAr: 'الاولي' },
    { id: 2, nameAr: 'الثانيه' },
    { id: 3, nameAr: 'الثالثة' },
  ];
  workCategoryId: number = 0;
  workSystemId: number = 0;
  shiftId: number = 0;
  year: any;
  employeeId: number = 0;
  employees: any[];

  editorOptions: any;
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  searchCtrl: FormControl;
  fromDateValue: any;
  toDateValue: any;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: HRService,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute,
    private fb: FormBuilder
  ) {
    this.viewListForm = this.fb.group({
      penaltyDelayingHourWithoutPermission: [{ value: '', disabled: true }],
      penaltyCheckbox: [false],
      quarterDayDelay: [{ value: '', disabled: false }],
      halfDayDelay: [{ value: '', disabled: false }],
      threeQuartersDayDelay: [{ value: '', disabled: false }],
      oneDayDelay: [{ value: '', disabled: false }],
    });

    service.baseUrl = `${environment.appUrls.WorkingHoursAndPenalties}`;

    this.subscription.add(
      service.list().subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getDepartDropdown().subscribe((r) => {
        if (r.success) {
          this.workCategories = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['ID', 'nameAr'];
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }
  toggleInputs(isChecked: boolean) {
    this.viewListForm.get('penaltyCheckbox')?.setValue(isChecked);
    console.log(isChecked);

    if (isChecked) {
      this.viewListForm.get('penaltyDelayingHourWithoutPermission')?.enable();
      this.viewListForm.get('quarterDayDelay')?.disable();
      this.viewListForm.get('halfDayDelay')?.disable();
      this.viewListForm.get('threeQuartersDayDelay')?.disable();
      this.viewListForm.get('oneDayDelay')?.disable();
    } else {
      this.viewListForm.get('penaltyDelayingHourWithoutPermission')?.disable();
      this.viewListForm.get('quarterDayDelay')?.enable();
      this.viewListForm.get('halfDayDelay')?.enable();
      this.viewListForm.get('threeQuartersDayDelay')?.enable();
      this.viewListForm.get('oneDayDelay')?.enable();
    }

    this.cdk.detectChanges();
  }

  ngOnInit(): void {
    const currentTime = new Date().toLocaleTimeString();
    this.viewListForm = new FormGroup({
      department: new FormControl(-1),
      fromDateValue: new FormControl('2024-01-01'),
      toDateValue: new FormControl('2024-12-31'),
      departureTime: new FormControl(currentTime),
      timeAttendance: new FormControl(currentTime),
      exitRest: new FormControl(currentTime),
      returnFromRest: new FormControl(currentTime),
    });

    this.subscriptions.add(
      this.route.paramMap.subscribe((p) => {
        const id = p.get('id') || '';
        //if (id) {
        this.subscriptions.add(
          this.service.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
        //  }
      })
    );
    this.searchCtrl.valueChanges.subscribe((v) => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(
          this.service.search(v).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            }
          })
        );
      } else if (id) {
        this.subscriptions.add(
          this.service.list(id).subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      } else {
        this.subscriptions.add(
          this.service.list('').subscribe((r) => {
            if (r.success) {
              this.loadData(r);
            }
          })
        );
      }
    });
  }

  onItemClick(e: any) {}
  calculateSelectedRow(
    options: DxDataGridTypes.CustomSummaryInfo<any, any>
  ): void {
    if (options.name === 'SelectedRowsSummary') {
      if (options.summaryProcess === 'start') {
        options.totalValue = 5;
      }
    }
    const currentYear = new Date().getFullYear();

    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }

  deleteRecords() {
    this.remove();
  }

  @ViewChild(DxDataGridComponent, { static: false })
  dataGrid: DxDataGridComponent;

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      icon: 'warning',
    }).then((r) => {
      if (r.isConfirmed) {
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(
            this.service.delete(key).subscribe((r) => {
              if (r.success) {
                this.data.remove(key);
                this.dataGrid.instance.refresh();
                this.cdk.detectChanges();
              }
            })
          );
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'falconData.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  newbtn() {}

  loadData(r: any) {
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }

  update() {
    // let form = this.viewListForm.value;
    // this.subscription.add(
    //   this.service.update(this.employeeId, form).subscribe(
    //     (r) => {
    //       if (r.success) {
    //         this.clearData();
    //         this.cdk.detectChanges();
    //       }
    //     },
    //     (error) => {
    //       console.error('Error during update:', error);
    //       // Optionally, show an error message to the user
    //     }
    //   )
    // );
  }
  clearData() {
    this.viewListForm.reset();

    this.cdk.detectChanges();
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const WorkCategoryId = this.viewListForm.value.workCategoryId;
    this.subscription.add(
      this.service.FilterEmployee({ WorkCategoryId }).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
