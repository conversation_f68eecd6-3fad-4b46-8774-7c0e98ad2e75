import { NgModule } from '@angular/core';

import { DaysandhoursComponent } from './daysandhours/daysandhours.component';
import { EditShiftPlansComponent } from './edit-shift-plans/edit-shift-plans.component';
import { OfficialHolidaysComponent } from './official-holidays/official-holidays.component';
import { ShiftPlansComponent } from './shift-plans/shift-plans.component';
import { WorkingHoursAndPenaltiesComponent } from './working-hours-and-penalties/working-hours-and-penalties.component';
import { HrAttendanceSettingsRoutingModule } from './hr-attendance-settings-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { DaysandhoursViewComponent } from './daysandhours/daysandhours-view/daysandhours-view.component';

@NgModule({
  declarations: [
    ShiftPlansComponent,
    OfficialHolidaysComponent,
    DaysandhoursComponent,
    EditShiftPlansComponent,
    WorkingHoursAndPenaltiesComponent,
    DaysandhoursViewComponent,
  ],
  imports: [HrAttendanceSettingsRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrAttendanceSettingsModule {}
