import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class EmployeesService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.apiUrl}/Employees/`;
  }

  details(id: string): Observable<any> {
    console.log(`Fetching employee details for ID: ${id}`);
    return this.http.get<any>(`${this.baseUrl}${id}`);
  }
  
  list(id: string, page: number = 1): Observable<any> {
    console.log(`Fetching employees list. ID: ${id}, Page: ${page}`);
    // تجنب استخدام الشرطة المائلة المزدوجة
    const url = id ? `${this.baseUrl}${id}` : this.baseUrl;
    return this.http.get<any>(url, {
      params: {
        currentPage: page.toString(),
      },
    });
  }

  getKeyValue(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}names`);
  }
  
  getDepartDropdown(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/Departments`);
  }

  getUsers(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}users/list`);
  }

  getQualifications(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/Qualifications`);
  }

  create(form: any): Observable<any> {
    console.log('Creating new employee with data:', form);
    return this.http.post<any>(this.baseUrl, form).pipe(
      map((result) => {
        if (result.success) {
          console.log('Employee created successfully:', result);
        } else {
          console.error('Error creating employee:', result);
        }
        return result;
      })
    );
  }

  update(id: string, form: any): Observable<any> {
    // إضافة / قبل المعرف للتأكد من أن عنوان URL صحيح
    return this.http.put<any>(`${this.baseUrl}${id}`, form).pipe(
      map((result) => {
        if (result.success) {
          console.log('Employee updated successfully:', result);
        } else {
          console.error('Error updating employee:', result);
        }
        return result;
      })
    );
  }
  
  delete(id: string): Observable<any> {
    console.log(`Deleting employee with ID: ${id}`);
    return this.http.delete<any>(`${this.baseUrl}${id}`).pipe(
      map((result) => {
        if (result.success) {
          console.log('Employee deleted successfully:', result);
        } else {
          console.error('Error deleting employee:', result);
        }
        return result;
      })
    );
  }

  activate(id: string): Observable<any> {
    return this.http.post(`${this.baseUrl}activate/${id}`, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(`${this.baseUrl}search`, form);
  }
  
  filter(form: any): Observable<any> {
    return this.http.post(`${this.baseUrl}filter`, form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(`${this.baseUrl}excel`, { responseType: 'blob' });
  }
  
  importExcel(form: FormData): Observable<any> {
    return this.http.post(`${this.baseUrl}excel`, form);
  }
}
