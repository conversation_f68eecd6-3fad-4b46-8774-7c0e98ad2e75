import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { EmployeesService } from '../../Employees.service';

@Component({
  selector: 'app-salary_information_view',
  templateUrl: './salary_information_view.component.html',
  styleUrls: ['./salary_information_view.component.css'],
  standalone: false,
})
export class Salary_information_viewComponent implements OnInit, OnDestroy {
  moduleName = 'HR.SalaryInformation';
  roles: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: EmployeesService,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      basicSalary: [null, Validators.required],
      changedSalary: [null, Validators.required],
      mealAllowance: [null, Validators.required],
      transportationAllowance: [null, Validators.required],
      housingAllowance: [null, Validators.required],
      otherAllowances: [null, Validators.required],
      fixedAllowance: [null, Validators.required],
      natureOfWorkAllowance: [null, Validators.required],
      schoolAllowance: [null, Validators.required],
      telephoneAllowance: [null, Validators.required],
      insuranceSalary: [null, Validators.required],
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    this.newdata.get('basicSalary')?.setValue(item.basicSalary);
    this.newdata.get('changedSalary')?.setValue(item.changedSalary);
    this.newdata.get('mealAllowance')?.setValue(item.mealAllowance);
    this.newdata
      .get('transportationAllowance')
      ?.setValue(item.transportationAllowance);
    this.newdata.get('housingAllowance')?.setValue(item.housingAllowance);
    this.newdata.get('otherAllowances')?.setValue(item.otherAllowances);
    this.newdata.get('fixedAllowance')?.setValue(item.fixedAllowance);
    this.newdata
      .get('natureOfWorkAllowance')
      ?.setValue(item.natureOfWorkAllowance);
    this.newdata.get('schoolAllowance')?.setValue(item.schoolAllowance);
    this.newdata.get('telephoneAllowance')?.setValue(item.telephoneAllowance);
    this.newdata.get('insuranceSalary')?.setValue(item.insuranceSalary);
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/employees/salary_information']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/employees/salary_information']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
