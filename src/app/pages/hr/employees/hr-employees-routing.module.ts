import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DependentsComponent } from './dependents/dependents.component';
import { EmployeesComponent } from './employees/employees.component';
import { SalaryInformationComponent } from './salary-information/salary-information.component';
import { UpdateEmployeeDataComponent } from './update-employee-data/update-employee-data.component';
import { EmployeesViewComponent } from './employees/employees-view/employees-view.component';
import { Salary_information_viewComponent } from './salary-information/salary_information_view/salary_information_view.component';
import { DependentsViewComponent } from './dependents/dependents-view/dependents-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'employees',
    pathMatch: 'full',
  },
  {
    path: 'employees',
    component: EmployeesComponent,
  },
  {
    path: 'employees_view',
    component: EmployeesViewComponent,
  },
  {
    path: 'employees_view/:id',
    component: EmployeesViewComponent,
  },
  {
    path: 'salary_information_view',
    component: Salary_information_viewComponent,
  },
  {
    path: 'salary_information_view/:id',
    component: Salary_information_viewComponent,
  },

  {
    path: 'salary_information',
    component: SalaryInformationComponent,
  },
  {
    path: 'dependents_view',
    component: DependentsViewComponent,
  },
  {
    path: 'dependents_view/:id',
    component: DependentsViewComponent,
  },
  {
    path: 'dependents',
    component: DependentsComponent,
  },
  {
    path: 'update_employee_data',
    component: UpdateEmployeeDataComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrEmployeesRoutingModule {}
