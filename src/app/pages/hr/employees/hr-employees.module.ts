import { NgModule } from '@angular/core';
import { DependentsComponent } from './dependents/dependents.component';
import { EmployeesComponent } from './employees/employees.component';
import { SalaryInformationComponent } from './salary-information/salary-information.component';
import { UpdateEmployeeDataComponent } from './update-employee-data/update-employee-data.component';
import { HrEmployeesRoutingModule } from './hr-employees-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { EmployeesViewComponent } from './employees/employees-view/employees-view.component';
import { DependentsViewComponent } from './dependents/dependents-view/dependents-view.component';
import { TranslateModule } from '@ngx-translate/core';
import { Salary_information_viewComponent } from './salary-information/salary_information_view/salary_information_view.component';
import { NgSelectModule } from '@ng-select/ng-select';

@NgModule({
  declarations: [
    EmployeesComponent,
    SalaryInformationComponent,
    DependentsComponent,
    UpdateEmployeeDataComponent,
    EmployeesViewComponent,
    DependentsViewComponent,
    Salary_information_viewComponent,
  ],
  imports: [
    HrEmployeesRoutingModule,
    SharedModule,
    TranslateModule,
    NgSelectModule,
  ],
  exports: [RouterModule],
})
export class HrEmployeesModule {}
