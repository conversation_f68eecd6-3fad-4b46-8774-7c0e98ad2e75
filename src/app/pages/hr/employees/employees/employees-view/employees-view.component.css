:host {
  display: block;
}
.photo-container {
  width: 200px;
  height: 250px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.photo-container img.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-container .overlay {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: end;
  flex-direction: column;
  padding: 10px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.photo-container:hover .overlay {
  opacity: 1;
}

.button-group {
  display: flex;
  gap: 5px;
}
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.switch input:checked + .slider {
  background-color: #4caf50;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.switch input:checked + .slider:before {
  transform: translateX(26px);
}

/* توحيد عرض الليبل فقط */
.form-label {
  display: inline-block;
  width: 150px;
  min-width: 150px;
}
