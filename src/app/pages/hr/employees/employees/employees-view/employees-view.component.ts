import {
  ChangeDetectorR<PERSON>,
  Component,
  ElementRef,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { EmployeesService } from '../../Employees.service';

@Component({
  selector: 'app-employees-view',
  templateUrl: './employees-view.component.html',
  styleUrls: ['./employees-view.component.css'],
  standalone: false,
})
export class EmployeesViewComponent implements OnInit, OnDestroy {
  moduleName = 'HR.Employees';
  employeePhoto: string | null = 'assets/media/avatars/blank.png';
  signaturePhoto: string | null = 'assets/media/avatars/FalconSignature.png';
  showOverlay = false;
  activeTab: string = 'tab1';
  roles: any[] = [];
  qualifications: any[] = [];
  maritalStatuses = [
    { id: '1', name: 'Single' },
    { id: '2', name: 'Married' },
    { id: '3', name: 'Widowed' },
    { id: '4', name: 'Divorced' }
  ];
  religions= [
    { id: '1', name: 'مسلم' },
    { id: '2', name: 'غير مسلم' }
  ];
  types: any[] = [];
  sectors: any[] = [];
  visaNumbers: any[] = [];
  nationalities: any[] = [];
  managements: any[] = [];
  workPlans: any[] = [];
  jobsInTheCompany: any[] = [];
  careersInIdentity: any[] = [];
  careersInVisa: any[] = [];
  sponsors: any[] = [];
  insurancePositions: any[] = [];
  facilityNames: any[] = [];
  jobPositions: any[] = [];
  municipalities: any[] = [];
  employeeRanks: any[] = [];
  creditAccounts: any[] = [];
  covenantAccounts: any[] = [];
  medicalInsuranceValues: any[] = [];
  salaryPackages: any[] = [];
  salariesAccounts: any[] = [];
  bankNames: any[] = [];
  virtualCostCenters: any[] = [];
  militarySituations: any[] = [];
  lineManagers: any[] = [];
  documentTypes: any[] = [];
  statuses: any[] = [];

  qualification: number = 0;
  maritalStatus: number = 0;
  religion: number = 0;
  type: number = 0;
  visaNumber: number = 0;
  sector: number = 0;
  nationality: number = 0;
  management: number = 0;
  workPlan: number = 0;
  jobInTheCompany: number = 0;
  careerInIdentity: number = 0;
  sponsor: number = 0;
  facilityName: number = 0;
  insurancePosition: number = 0;
  jobPosition: number = 0;
  municipality: number = 0;
  employeeRank: number = 0;
  medicalInsuranceValue: number = 0;
  creditAccount: number = 0;
  covenantAccount: number = 0;
  salariesAccount: number = 0;
  salaryPackage: number = 0;
  bankName: number = 0;
  virtualCostCenter: number = 0;
  militarySituation: number = 0;
  lineManager: number = 0;
  documentType: number = 0;
  status: number = 0;

  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  @ViewChild('fileInput1') fileInput1!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput2') fileInput2!: ElementRef<HTMLInputElement>;

  triggerFileInput1(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput1.nativeElement.click();
  }
  triggerFileInput2(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput2.nativeElement.click();
  }

  onEmployeePhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.employeePhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }
  onSignaturePhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.signaturePhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }

  clearEmployeePhoto(event: Event): void {
    event.stopPropagation();
    this.employeePhoto = 'assets/media/avatars/blank.png';
  }

  clearSignaturePhoto(event: Event): void {
    event.stopPropagation();
    this.signaturePhoto = 'assets/media/avatars/FalconSignature.png';
  }
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: EmployeesService,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      nameAr: [null, Validators.required],
      nameEn: [null, Validators.required],
      maritalStatus: [null, Validators.required],
      qualification: [null, Validators.required],
      religion: [null, Validators.required],
      type: [null, Validators.required],
      DateOfBirth: [today, Validators.required],
      EmploymentStartDate: [today, Validators.required],
      visaNumber: [null, Validators.required],
      sector: [null, Validators.required],
      nationality: [null, Validators.required],
      management: [null, Validators.required],
      workPlan: [null, Validators.required],
      jobInTheCompany: [null, Validators.required],
      careerInIdentity: [null, Validators.required],
      careersInVisa: [null, Validators.required],
      sponsor: [null, Validators.required],
      IDExpiryDate: [today, Validators.required],
      passportExpDate: [today, Validators.required],
      EntryDateToCountry: [today, Validators.required],
      MedicalInsuranceEndDate: [today, Validators.required],
      healthCertificateEXPDate: [today, Validators.required],
      SocialInsuranceStartDate: [today, Validators.required],
      MedicalInsuranceStartDate: [today, Validators.required],
      medicalInsuranceEXPDate: [today, Validators.required],
      DateLeavePreviousJob: [today, Validators.required],
      GraduationDate: [today, Validators.required],
      issueDate: [today, Validators.required],
      endIssueDate: [today, Validators.required],
      Eff_Badal_Ta3am: [true],
      Eff_Badal_Tabe3a: [true],
      Eff_Badal_Sakan: [true],
      Eff_Badal_Other: [true],
      Eff_Badal_Entekal: [true],
      Eff_Badal_tel: [true],
      Eff_Take_Schole: [true],
      VEff_Badal_Ta3am: [true],
      VEff_Badal_Tabe3a: [true],
      VEff_Badal_Sakan: [true],
      VEff_Badal_Other: [true],
      VEff_Badal_Entekal: [true],
      VEff_Badal_tel: [true],
      VEff_Take_Schole: [true],
      ES_Eff_Badal_Ta3am: [true],
      ES_Eff_Badal_Tabe3a: [true],
      ES_Eff_Badal_Sakan: [true],
      ES_Eff_Badal_Other: [true],
      ES_Eff_Badal_Entekal: [true],
      ES_Eff_Badal_tel: [true],
      ES_Eff_Take_Schole: [true],
      OptionControl: ['IssuingResidence'],
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    console.log('Employee details component initialized');
    const id = this.route.snapshot.params.id;
    console.log('Employee ID from route:', id);
    
    if (id) {
      console.log('Fetching employee details for ID:', id);
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          console.log('Employee details response:', r);
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            console.log('Employee roles loaded:', this.roles);
            this.cdk.detectChanges();
          } else {
            console.error('Failed to load employee details:', r);
          }
        })
      );
    } else {
      console.log('Creating new employee - no ID provided');
    }
  }

  fillForm(item: any) {
    // تسجيل البيانات المستلمة في وحدة التحكم للتصحيح
    console.log('Employee data received:', item);

    if (!item) {
      console.error('No data received for employee');
      return;
    }

    // معالجة صور الموظف والتوقيع إذا كانت متوفرة
    if (item.photo && item.photo !== '') {
      this.employeePhoto = item.photo;
    }
    
    if (item.signature && item.signature !== '') {
      this.signaturePhoto = item.signature;
    }
    
    // إنشاء خريطة لتعيين الحقول من البيانات الواردة إلى حقول النموذج
    // هذا يساعد في التعامل مع اختلاف أسماء الحقول
    const fieldMapping: { [key: string]: string } = {
      // أسماء الحقول من API إلى أسماء حقول النموذج
      'nameAr': 'nameAr',
      'nameEn': 'nameEn',
      'religionId': 'religion',
      'qualificationId': 'qualification',
      'nationalityId': 'nationality',
      'sectorId': 'sector',
      'departmentId': 'management',
      'jobId': 'jobInTheCompany',
      'kafeelId': 'sponsor',
      // أضف المزيد من الحقول حسب الحاجة
    };
    
    // التاريخ الحالي للقيم الافتراضية
    const today = new Date();
    
    // ملء النموذج بالقيم بناءً على خريطة الحقول
    Object.keys(fieldMapping).forEach(apiField => {
      if (item[apiField] !== undefined && item[apiField] !== null) {
        const formField = fieldMapping[apiField];
        if (this.newdata.get(formField)) {
          this.newdata.get(formField)?.setValue(item[apiField]);
          console.log(`Set ${formField} to ${item[apiField]}`);
        }
      }
    });
    
    // ملء حقول التاريخ وغيرها من الحقول التي قد تحتاج إلى معالجة خاصة
    // قائمة حقول التاريخ
    const dateFields = [
      'IDExpiryDate', 'passportExpDate', 'EntryDateToCountry', 'MedicalInsuranceEndDate',
      'healthCertificateEXPDate', 'SocialInsuranceStartDate', 'MedicalInsuranceStartDate',
      'medicalInsuranceEXPDate', 'DateLeavePreviousJob', 'GraduationDate', 'issueDate', 'endIssueDate',
      'DateOfBirth', 'EmploymentStartDate'
    ];
    
    // تعيين تواريخ افتراضية للحقول غير المتوفرة
    dateFields.forEach(field => {
      if (this.newdata.get(field)) {
        let dateValue = today;
        
        // محاولة استخراج التاريخ من البيانات
        if (item[field] && item[field] !== '') {
          try {
            dateValue = new Date(item[field]);
          } catch (e) {
            console.error(`Error converting date for ${field}:`, e);
          }
        }
        
        this.newdata.get(field)?.setValue(dateValue);
      }
    });
    
    // تعيين قيم افتراضية للحقول المنطقية إذا لم تكن متوفرة
    const booleanFields = [
      'Eff_Badal_Ta3am', 'Eff_Badal_Tabe3a', 'Eff_Badal_Sakan', 'Eff_Badal_Other',
      'Eff_Badal_Entekal', 'Eff_Badal_tel', 'Eff_Take_Schole',
      'VEff_Badal_Ta3am', 'VEff_Badal_Tabe3a', 'VEff_Badal_Sakan', 'VEff_Badal_Other',
      'VEff_Badal_Entekal', 'VEff_Badal_tel', 'VEff_Take_Schole',
      'ES_Eff_Badal_Ta3am', 'ES_Eff_Badal_Tabe3a', 'ES_Eff_Badal_Sakan', 'ES_Eff_Badal_Other',
      'ES_Eff_Badal_Entekal', 'ES_Eff_Badal_tel', 'ES_Eff_Take_Schole'
    ];
    
    booleanFields.forEach(field => {
      if (this.newdata.get(field)) {
        let value = true; // قيمة افتراضية
        
        if (item[field] !== undefined) {
          if (typeof item[field] === 'string') {
            value = item[field].toLowerCase() === 'true';
          } else if (typeof item[field] === 'boolean') {
            value = item[field];
          }
        }
        
        this.newdata.get(field)?.setValue(value);
      }
    });
    
    // تعيين أي حقول أخرى متبقية
    Object.keys(item).forEach(key => {
      // تخطي الحقول التي تم معالجتها بالفعل
      if (Object.keys(fieldMapping).includes(key) || dateFields.includes(key) || booleanFields.includes(key)) {
        return;
      }
      
      if (this.newdata.get(key)) {
        this.newdata.get(key)?.setValue(item[key]);
        console.log(`Set ${key} to ${item[key]}`);
      }
    });

    // هذا للتحقق من صحة وجود جميع البيانات المطلوبة
    console.log('Form after filling:', this.newdata.value);
    
    // تعيين القيم في الحقول المنسدلة
    if (item.qualificationId) this.qualification = item.qualificationId;
    if (item.maritalStatusId) this.maritalStatus = item.maritalStatusId;
    if (item.religionId) this.religion = item.religionId;
    if (item.typeId) this.type = item.typeId;
    if (item.visaNumberId) this.visaNumber = item.visaNumberId;
    if (item.sectorId) this.sector = item.sectorId;
    if (item.nationalityId) this.nationality = item.nationalityId;
    if (item.departmentId) this.management = item.departmentId;
    if (item.workPlanId) this.workPlan = item.workPlanId;
    if (item.jobId) this.jobInTheCompany = item.jobId;
    if (item.careerInIdentityId) this.careerInIdentity = item.careerInIdentityId;
    if (item.sponsorId || item.kafeelId) this.sponsor = item.sponsorId || item.kafeelId;
    
    this.cdk.detectChanges();
  }

  save() {
    const id = this.route.snapshot.params.id;
    console.log('Saving employee form. ID present:', !!id);
    console.log('Form valid:', this.newdata.valid);
    
    if (this.newdata.valid) {
      console.log('Form is valid, proceeding with save');
      let form = this.newdata.value;
      console.log('Form data to save:', form);
      
      if (id) {
        // Updating existing employee
        console.log('Updating existing employee with ID:', id);
        this.isLoading = true;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                console.log('Update operation completed');
                this.cdk.detectChanges();
              })
            )
            .subscribe({
              next: (r) => {
                console.log('Update response:', r);
                if (r.success) {
                  console.log('Employee updated successfully');
                  this.router.navigate(['/hr/employees/employees']);
                } else {
                  console.error('Failed to update employee:', r);
                }
              },
              error: (err) => {
                console.error('Error during employee update:', err);
              }
            })
        );
      } else {
        // Creating new employee
        console.log('Creating new employee');
        this.isLoading = true;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                console.log('Create operation completed');
                this.cdk.detectChanges();
              })
            )
            .subscribe({
              next: (r) => {
                console.log('Create response:', r);
                if (r.success) {
                  console.log('Employee created successfully');
                  this.router.navigate(['/hr/employees/employees']);
                } else {
                  console.error('Failed to create employee:', r);
                }
              },
              error: (err) => {
                console.error('Error during employee creation:', err);
              }
            })
        );
      }
    } else {
      console.warn('Form is invalid, marking all fields as touched');
      this.newdata.markAllAsTouched();
      // طباعة الأخطاء في النموذج للتصحيح
      Object.keys(this.newdata.controls).forEach(key => {
        const control = this.newdata.get(key);
        if (control?.invalid) {
          console.error(`Field ${key} validation errors:`, control.errors);
        }
      });
    }
  }
  
  discard() {
    console.log('Discarding form changes');
    this.newdata.reset();
  }
  
  exportToExcel() {
    console.log('Exporting employee data to Excel');
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          console.log('Excel file received, creating download link');
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'employees.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
          console.log('Excel download initiated');
        } else {
          console.error('Failed to receive Excel file from server');
        }
      })
    );
  }

  openSmsModal() {
    console.log('Opening SMS modal');
    this.smsModal.open();
  }
  
  openWhatsappModal() {
    console.log('Opening WhatsApp modal');
    this.whatsappModal.open();
  }
  
  openEmailModal() {
    console.log('Opening Email modal');
    this.emailModal.open();
  }
  
  // أضف وظيفة طباعة البيانات
  printEmployeeData() {
    console.log('Printing employee data');
    const employeeData = this.newdata.value;
    console.log('Employee data to print:', employeeData);
    
    // إنشاء محتوى HTML للطباعة
    let printContent = `
      <html>
        <head>
          <title>بيانات الموظف</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            h1, h2 { text-align: center; }
            h2 { margin-top: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            table, th, td { border: 1px solid #ddd; }
            th, td { padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; width: 30%; }
            .logo { text-align: center; margin-bottom: 20px; }
            .logo img { max-height: 60px; }
            .date { text-align: left; margin-bottom: 10px; }
            .employee-photo { text-align: center; margin: 10px 0; }
            .employee-photo img { max-height: 150px; border: 1px solid #ddd; padding: 5px; }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="logo">
            <img src="assets/media/logos/falcon.png" alt="شعار فالكون">
          </div>
          
          <div class="date">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</div>
          
          <h1>بيانات الموظف</h1>
          
          <div class="employee-photo">
            <img src="${this.employeePhoto || 'assets/media/avatars/blank.png'}" alt="صورة الموظف">
          </div>
          
          <h2>البيانات الأساسية</h2>
          <table>
            <tbody>
              <tr><th>الاسم (عربي)</th><td>${employeeData.nameAr || ''}</td></tr>
              <tr><th>الاسم (إنجليزي)</th><td>${employeeData.nameEn || ''}</td></tr>
              <tr><th>رقم الهوية</th><td>${employeeData.idNumber || ''}</td></tr>
              <tr><th>رقم الجوال</th><td>${employeeData.mobile || ''}</td></tr>
              <tr><th>البريد الإلكتروني</th><td>${employeeData.email || ''}</td></tr>
              <tr><th>تاريخ الميلاد</th><td>${employeeData.birthDate ? new Date(employeeData.birthDate).toLocaleDateString('ar-SA') : ''}</td></tr>
              <tr><th>الجنسية</th><td>${employeeData.nationalityName || ''}</td></tr>
            </tbody>
          </table>
          
          <h2>بيانات العمل</h2>
          <table>
            <tbody>
              <tr><th>القسم</th><td>${employeeData.managementName || ''}</td></tr>
              <tr><th>المسمى الوظيفي</th><td>${employeeData.jobInTheCompanyName || ''}</td></tr>
              <tr><th>تاريخ التعيين</th><td>${employeeData.joiningDate ? new Date(employeeData.joiningDate).toLocaleDateString('ar-SA') : ''}</td></tr>
              <tr><th>الدرجة الوظيفية</th><td>${employeeData.employeeRankName || ''}</td></tr>
              <tr><th>الحالة</th><td>${employeeData.statusName || ''}</td></tr>
            </tbody>
          </table>
          
          <div style="margin-top: 20px; text-align: center; font-size: 12px;">
            نظام فالكون ERP - جميع الحقوق محفوظة
          </div>
        </body>
      </html>
    `;
    
    // إنشاء إطار مخفي للطباعة
    const printFrame = document.createElement('iframe');
    printFrame.style.position = 'absolute';
    printFrame.style.top = '-9999px';
    printFrame.style.left = '-9999px';
    document.body.appendChild(printFrame);
    
    // كتابة المحتوى في الإطار
    const frameDocument = printFrame.contentDocument || printFrame.contentWindow?.document;
    if (frameDocument) {
      frameDocument.open();
      frameDocument.write(printContent);
      frameDocument.close();
      
      // انتظر تحميل الصور ثم اطبع
      setTimeout(() => {
        printFrame.contentWindow?.focus();
        printFrame.contentWindow?.print();
        
        // إزالة الإطار بعد الطباعة
        setTimeout(() => {
          document.body.removeChild(printFrame);
        }, 1000);
      }, 500);
    } else {
      console.error('Failed to create print frame document');
    }
  }
}
