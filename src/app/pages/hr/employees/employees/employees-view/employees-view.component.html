<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.AddEmployee" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              (click)="printEmployeeData()"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameAr" class="form-label">
                {{ "COMMON.EmployeeNameAr" | translate }}
              </label>
              <input
                type="text"
                id="nameAr"
                name="nameAr"
                class="form-control"
                formControlName="nameAr"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameEn" class="form-label">
                {{ "COMMON.EmployeeNameEn" | translate }}
              </label>
              <input
                type="text"
                name="nameEn"
                id="nameEn"
                class="form-control"
                formControlName="nameEn"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="MaritalStatus" class="form-label">
                {{ "COMMON.MaritalStatus" | translate }}
              </label>
              <ng-select
                id="MaritalStatus"
                bindLabel="name"
                bindValue="id"
                formControlName="maritalStatus"
                [items]="maritalStatuses"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="QualificationName" class="form-label">
                {{ "COMMON.Qualifications" | translate }}
              </label>
              <ng-select
                id="QualificationName"
                bindLabel="nameAr"
                bindValue="id"
                formControlName="qualification"
                [items]="qualifications"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="ReligionName" class="form-label">
                {{ "COMMON.Religion" | translate }}
              </label>
              <ng-select
                id="ReligionName"
                formControlName="religion"
                bindLabel="name"
                bindValue="id"
                [items]="religions"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="Gender" class="form-label">
                {{ "COMMON.Type" | translate }}
              </label>
              <ng-select
                id="Gender"
                formControlName="type"
                bindLabel="name"
                bindValue="id"
                [items]="types"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.BirthDate" | translate }}
              </label>
              <input
                id="DateOfBirth"
                type="date"
                formControlName="DateOfBirth"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.HiringDate" | translate }}
              </label>
              <input
                id="EmploymentStartDate"
                type="date"
                formControlName="EmploymentStartDate"
                class="form-control"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="NationalityID" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <ng-select
                id="NationalityID"
                formControlName="nationality"
                bindLabel="name"
                bindValue="id"
                [items]="nationalities"
              ></ng-select>
            </div>
          </div>
        </div>
        <!-- photo -->
        <div
          class="photo-container position-relative d-flex flex-column align-items-center"
          (mouseenter)="showOverlay = true"
          (mouseleave)="showOverlay = false"
        >
          <img
            [src]="employeePhoto || 'assets/media/avatars/blank.png'"
            alt="New Employee"
            class="photo img-fluid rounded mb-2"
          />
          <div
            class="overlay d-flex flex-column justify-content-end align-items-center"
            *ngIf="showOverlay"
          >
            <div class="button-group d-flex justify-content-between gap-2">
              <button
                class="btn btn-primary btn-sm"
                (click)="triggerFileInput1($event)"
              >
                <i class="fa fa-edit fs-2"></i>
              </button>
              <button
                class="btn btn-danger btn-sm"
                (click)="clearEmployeePhoto($event)"
              >
                <i class="fa fa-trash-o fs-2"></i>
              </button>
            </div>
          </div>
          <input
            type="file"
            accept="image/*"
            class="d-none"
            #fileInput1
            (change)="onEmployeePhotoSelected($event)"
          />
        </div>
        <!-- End Photo  -->
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.OfficialDocuments" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab3'"
              (click)="setActiveTab('tab3')"
            >
              {{ "COMMON.WorkInformation" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab4'"
              (click)="setActiveTab('tab4')"
            >
              {{ "COMMON.Insurances" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab5'"
              (click)="setActiveTab('tab5')"
            >
              {{ "COMMON.MoneyData" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab6'"
              (click)="setActiveTab('tab6')"
            >
              {{ "COMMON.Contracts" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab7'"
              (click)="setActiveTab('tab7')"
            >
              {{ "COMMON.AdditionalInformation" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab8'"
              (click)="setActiveTab('tab8')"
            >
              {{ "COMMON.OtherInformation" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab9'"
              (click)="setActiveTab('tab9')"
            >
              {{ "COMMON.Allowances" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab10'"
              (click)="setActiveTab('tab10')"
            >
              {{ "COMMON.AttachmentsAndDocuments" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab11'"
              (click)="setActiveTab('tab11')"
            >
              {{ "COMMON.StatusAndSignature" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="IdNumber" class="form-label">
                  {{ "CUSTOMER.ID_NUMBER" | translate }}
                </label>
                <input
                  type="text"
                  id="IdNumber"
                  name="IdNumber"
                  class="form-control"
                  formControlName="IdNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="IDExpiryDate"
                  type="date"
                  formControlName="IDExpiryDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="jobid" class="form-label">
                  {{ "COMMON.JobNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="jobid"
                  name="jobid"
                  class="form-control"
                  formControlName="jobid"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="password" class="form-label">
                  {{ "COMMON.Password" | translate }}
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  class="form-control"
                  formControlName="password"
                />
              </div>
              <div class="form-group col-xl-2 col-md-6 col-sm-12">
                <button class="btn btn-outline-primary btn-sm">
                  {{ "COMMON.Reset" | translate }}
                </button>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="PassportID" class="form-label">
                  {{ "COMMON.PassportNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="PassportID"
                  name="PassportID"
                  class="form-control"
                  formControlName="PassportID"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="passportExpDate"
                  type="date"
                  formControlName="passportExpDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.DateOfEntry" | translate }}
                </label>
                <input
                  id="EntryDateToCountry"
                  type="date"
                  formControlName="EntryDateToCountry"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="OptionControl"
                    value="IssuingResidence"
                  />
                  {{ "COMMON.IssuingResidence" | translate }}
                </label>
                <label class="mx-6">
                  <input
                    type="radio"
                    formControlName="OptionControl"
                    value="TransferringSponsorship"
                  />
                  {{ "COMMON.TransferringSponsorship" | translate }}
                </label>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <span class="mx-2">{{
                  "COMMON.SaveShiftPlan" | translate
                }}</span>
                <label class="switch">
                  <input type="checkbox" formControlName="SaveShiftPlan" />
                  <span class="slider"></span>
                </label>
              </div>
            </div>
          </div>

          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12 mb-3">
                <label for="VisaNumber" class="form-label">
                  {{ "COMMON.VisaNumber" | translate }}
                </label>
                <ng-select
                  id="VisaNumber"
                  formControlName="VisaNumber"
                  bindLabel="name"
                  bindValue="id"
                  [items]="visaNumbers"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12 mb-3">
                <label for="SectorName" class="form-label">
                  {{ "COMMON.Sector" | translate }}
                </label>
                <ng-select
                  id="SectorName"
                  formControlName="SectorName"
                  bindLabel="name"
                  bindValue="id"
                  [items]="sectors"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="notes" class="form-label">
                  {{ "COMMON.Notes" | translate }}
                </label>
                <input
                  type="text"
                  id="notes"
                  name="notes"
                  class="form-control"
                  formControlName="notes"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="workPlan" class="form-label">
                  {{ "Salary.WorkPlan" | translate }}
                </label>
                <ng-select
                  id="workPlan"
                  formControlName="workPlan"
                  bindLabel="name"
                  bindValue="id"
                  [items]="workPlans"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="jobInTheCompany" class="form-label">
                  {{ "COMMON.JobInTheCompany" | translate }}
                </label>
                <ng-select
                  id="jobInTheCompany"
                  formControlName="jobInTheCompany"
                  bindLabel="name"
                  bindValue="id"
                  [items]="jobsInTheCompany"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="careerInIdentity" class="form-label">
                  {{ "COMMON.CareerInIdentity" | translate }}
                </label>
                <ng-select
                  id="careerInIdentity"
                  formControlName="careerInIdentity"
                  bindLabel="name"
                  bindValue="id"
                  [items]="careersInIdentity"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="VisaJobTitleId" class="form-label">
                  {{ "COMMON.CareerInVisa" | translate }}
                </label>
                <ng-select
                  id="VisaJobTitleId"
                  formControlName="VisaJobTitleId"
                  bindLabel="name"
                  bindValue="id"
                  [items]="careersInVisa"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="KafeelID" class="form-label">
                  {{ "Salary.Kafeel" | translate }}
                </label>
                <ng-select
                  id="KafeelID"
                  formControlName="KafeelID"
                  bindLabel="name"
                  bindValue="id"
                  [items]="sponsors"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="management" class="form-label">
                  {{ "COMMON.Management" | translate }}
                </label>
                <ng-select
                  id="management"
                  formControlName="management"
                  bindLabel="name"
                  bindValue="id"
                  [items]="managements"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="BorderNumber" class="form-label">
                  {{ "COMMON.BorderNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="BorderNumber"
                  name="BorderNumber "
                  class="form-control"
                  formControlName="BorderNumber"
                />
              </div>
            </div>
          </div>
          <!-- Tab 3 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="facilityName" class="form-label">
                  {{ "COMMON.FacilityName" | translate }}
                </label>
                <ng-select
                  id="facilityName"
                  formControlName="facilityName"
                  bindLabel="name"
                  bindValue="id"
                  [items]="facilityNames"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="insurancePosition" class="form-label">
                  {{ "COMMON.InsurancePosition" | translate }}
                </label>
                <ng-select
                  id="insurancePosition"
                  formControlName="insurancePosition"
                  bindLabel="name"
                  bindValue="id"
                  [items]="insurancePositions"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="facilityNumber" class="form-label">
                  {{ "COMMON.FacilityNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="facilityNumber"
                  name="facilityNumber"
                  class="form-control"
                  formControlName="facilityNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="EmployerName" class="form-label">
                  {{ "COMMON.Owner" | translate }}
                </label>
                <input
                  type="text"
                  id="EmployerName"
                  name="EmployerName"
                  class="form-control"
                  formControlName="EmployerName"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="jobPosition" class="form-label">
                  {{ "COMMON.JobPosition" | translate }}
                </label>
                <ng-select
                  id="jobPosition"
                  formControlName="jobPosition"
                  bindLabel="name"
                  bindValue="id"
                  [items]="jobPositions"
                ></ng-select>
              </div>
            </div>
          </div>
          <!-- Tab 4 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab4'"
            [class.active]="activeTab === 'tab4'"
            *ngIf="activeTab === 'tab4'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="HealthCardNumber" class="form-label">
                  {{ "COMMON.HealthCertificateNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="HealthCardNumber"
                  name="HealthCardNumber"
                  class="form-control"
                  formControlName="HealthCardNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="healthCertificateEXPDate"
                  type="date"
                  formControlName="healthCertificateEXPDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="municipality" class="form-label">
                  {{ "COMMON.Municipality" | translate }}
                </label>
                <ng-select
                  id="municipality"
                  formControlName="municipality"
                  bindLabel="name"
                  bindValue="id"
                  [items]="municipalities"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="InsuranceNumber" class="form-label">
                  {{ "COMMON.SocialSecurityNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="InsuranceNumber"
                  name="InsuranceNumber"
                  class="form-control"
                  value="0"
                  formControlName="InsuranceNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <span class="mx-2">{{
                  "COMMON.InsurancePercentageDiscount" | translate
                }}</span>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="InsurancePercentageDiscount"
                  />
                  <span class="slider"></span>
                </label>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.JoiningInsuranceDate" | translate }}
                </label>
                <input
                  id="SocialInsuranceStartDate"
                  type="date"
                  formControlName="SocialInsuranceStartDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="medicalInsuranceNumber" class="form-label">
                  {{ "COMMON.MedicalInsuranceNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="medicalInsuranceNumber"
                  name="medicalInsuranceNumber"
                  class="form-control"
                  value="0"
                  formControlName="medicalInsuranceNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="employeeRank" class="form-label">
                  {{ "COMMON.EmployeeRank" | translate }}
                </label>
                <ng-select
                  id="employeeRank"
                  formControlName="employeeRank"
                  bindLabel="name"
                  bindValue="id"
                  [items]="employeeRanks"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.JoiningMedicalDate" | translate }}
                </label>
                <input
                  id="MedicalInsuranceStartDate"
                  type="date"
                  formControlName="MedicalInsuranceStartDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="InsuranceValue" class="form-label">
                  {{ "COMMON.MedicalInsuranceValue" | translate }}
                </label>
                <div class="row g-2">
                  <div class="col-4">
                    <ng-select
                      id="InsuranceValue"
                      formControlName="InsuranceValue"
                      bindLabel="name"
                      bindValue="id"
                      [items]="medicalInsuranceValues"
                    ></ng-select>
                  </div>
                  <div class="col-8">
                    <input
                      type="text"
                      id="MedicalInsuranceValue"
                      name="MedicalInsuranceValue"
                      class="form-control"
                      value="0"
                      formControlName="MedicalInsuranceValue"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="HealthRecordNumber" class="form-label">
                  {{ "COMMON.RecordNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="HealthRecordNumber"
                  name="HealthRecordNumber"
                  class="form-control"
                  value="0"
                  formControlName="HealthRecordNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="MedicalInsuranceEndDate"
                  type="date"
                  formControlName="MedicalInsuranceEndDate"
                  class="form-control"
                />
              </div>
            </div>
          </div>
          <!-- Tab 5 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab5'"
            [class.active]="activeTab === 'tab5'"
            *ngIf="activeTab === 'tab5'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="creditAccount" class="form-label">
                  {{ "COMMON.CreditAccount" | translate }}
                </label>
                <ng-select
                  id="creditAccount"
                  formControlName="creditAccount"
                  bindLabel="name"
                  bindValue="id"
                  [items]="creditAccounts"
                ></ng-select>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="creditAccountSwitch"
                  />
                  <span class="slider"></span>
                </label>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="covenantAccount" class="form-label">
                  {{ "COMMON.CovenantAccount" | translate }}
                </label>
                <ng-select
                  id="covenantAccount"
                  formControlName="covenantAccount"
                  bindLabel="name"
                  bindValue="id"
                  [items]="covenantAccounts"
                ></ng-select>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="covenantAccountSwitch"
                  />
                  <span class="slider"></span>
                </label>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <span class="mx-2">{{
                  "COMMON.AdvanceHousingAllowance" | translate
                }}</span>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="AllowHousingAllowance"
                  />
                  <span class="slider"></span>
                </label>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="salariesAccount" class="form-label">
                  {{ "COMMON.SalariesAccount" | translate }}
                </label>
                <ng-select
                  id="salariesAccount"
                  formControlName="salariesAccount"
                  bindLabel="name"
                  bindValue="id"
                  [items]="salariesAccounts"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="SalaryGroup" class="form-label">
                  {{ "COMMON.SalaryPackage" | translate }}
                </label>
                <ng-select
                  id="SalaryGroup"
                  formControlName="SalaryGroup"
                  bindLabel="name"
                  bindValue="id"
                  [items]="salaryPackages"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="virtualCostCenter" class="form-label">
                  {{ "COMMON.VirtualCostCenter" | translate }}
                </label>
                <ng-select
                  id="virtualCostCenter"
                  formControlName="virtualCostCenter"
                  bindLabel="name"
                  bindValue="id"
                  [items]="virtualCostCenters"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="BankName" class="form-label">
                  {{ "COMMON.BankName" | translate }}
                </label>
                <ng-select
                  id="BankName"
                  formControlName="BankName"
                  bindLabel="name"
                  bindValue="id"
                  [items]="bankNames"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="EmployeeBankAccountNumber" class="form-label">
                  {{ "COMMON.EmployeeBankAccountNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="employeeBankAccountNumber"
                  name="employeeBankAccountNumber"
                  class="form-control"
                  formControlName="employeeBankAccountNumber"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label for="HealthCareDiscount" class="form-label">
                  {{ "COMMON.HealthCareDiscountRate" | translate }}
                </label>
                <input
                  type="text"
                  id="HealthCareDiscount"
                  name="HealthCareDiscount"
                  class="form-control"
                  formControlName="HealthCareDiscount"
                />
                <span class="fs-2">%</span>
              </div>
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label for="InsuranceSalary" class="form-label">
                  {{ "COMMON.SalaryInInsurance" | translate }}
                </label>
                <input
                  type="text"
                  id="InsuranceSalary"
                  name="InsuranceSalary"
                  class="form-control"
                  formControlName="InsuranceSalary"
                />
              </div>
              <div class="form-group col-xl-3 col-md-4 col-sm-12">
                <label for="EmployeeFundDeduction" class="form-label">
                  {{ "COMMON.WorkersFundDiscountRatio" | translate }}
                </label>
                <input
                  type="text"
                  id="EmployeeFundDeduction"
                  name="EmployeeFundDeduction"
                  class="form-control"
                  formControlName="EmployeeFundDeduction"
                />
                <span class="fs-2">%</span>
              </div>
            </div>
          </div>
          <!-- Tab 6 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab6'"
            [class.active]="activeTab === 'tab6'"
            *ngIf="activeTab === 'tab6'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="ContractPeriod" class="form-label">
                  {{ "COMMON.ContractPeriod" | translate }}
                </label>
                <input
                  type="text"
                  id="ContractPeriod"
                  name="ContractPeriod"
                  class="form-control"
                  value="0"
                  formControlName="ContractPeriod"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="carryoverLeaveBalance" class="form-label">
                  {{ "COMMON.CarryoverLeaveBalance" | translate }}
                </label>
                <input
                  type="text"
                  id="carryoverLeaveBalance"
                  name="carryoverLeaveBalance"
                  class="form-control"
                  value="0"
                  formControlName="carryoverLeaveBalance"
                />
                <span>{{ "COMMON.Day" | translate }}</span>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <span class="mx-2">{{
                  "COMMON.ThirtyDaysVacationSystem" | translate
                }}</span>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="Vacation30"
                  />
                  <span class="slider"></span>
                </label>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="YearlyTicket" class="form-label">
                  {{ "COMMON.AnnualTicketsValue" | translate }}
                </label>
                <input
                  type="text"
                  id="YearlyTicket"
                  name="YearlyTicket"
                  class="form-control"
                  value="0"
                  formControlName="YearlyTicket"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="TicketCount" class="form-label">
                  {{ "COMMON.AnnualTicketsNumbers" | translate }}
                </label>
                <input
                  type="text"
                  id="TicketCount"
                  name="TicketCount"
                  class="form-control"
                  value="0"
                  formControlName="TicketCount"
                />
              </div>
            </div>

            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="BasicSalary" class="form-label">
                  {{ "COMMON.BasicSalary" | translate }}
                </label>
                <input
                  type="text"
                  id="BasicSalary"
                  name="BasicSalary"
                  class="form-control"
                  value="0"
                  formControlName="BasicSalary"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="FixedAllowance" class="form-label">
                  {{ "COMMON.FixedAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="FixedAllowance"
                  name="FixedAllowance"
                  class="form-control"
                  value="0"
                  formControlName="FixedAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="changedSalary" class="form-label">
                  {{ "COMMON.ChangedSalary" | translate }}
                </label>
                <input
                  type="text"
                  id="changedSalary"
                  name="changedSalary"
                  class="form-control"
                  value="0"
                  formControlName="changedSalary"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="WorkNatureAllowance" class="form-label">
                  {{ "COMMON.NatureOfWorkAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="WorkNatureAllowance"
                  name="WorkNatureAllowance"
                  class="form-control"
                  value="0"
                  formControlName="WorkNatureAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="CommunicationAllowance" class="form-label">
                  {{ "COMMON.TelephoneAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="CommunicationAllowance"
                  name="CommunicationAllowance"
                  class="form-control"
                  value="0"
                  formControlName="CommunicationAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="FoodAllowance" class="form-label">
                  {{ "COMMON.MealAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="FoodAllowance"
                  name="FoodAllowance"
                  class="form-control"
                  value="0"
                  formControlName="FoodAllowance"
                />
              </div>
            </div>

            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="TransportationAllowance" class="form-label">
                  {{ "COMMON.TransportationAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="TransportationAllowance"
                  name="TransportationAllowance"
                  class="form-control"
                  value="0"
                  formControlName="TransportationAllowance"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="OtherAllowances" class="form-label">
                  {{ "COMMON.OtherAllowances" | translate }}
                </label>
                <input
                  type="text"
                  id="OtherAllowances"
                  name="OtherAllowances"
                  class="form-control"
                  value="0"
                  formControlName="OtherAllowances"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="HousingAllowance" class="form-label">
                  {{ "COMMON.HousingAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="HousingAllowance"
                  name="HousingAllowance"
                  class="form-control"
                  value="0"
                  formControlName="HousingAllowance"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="SchoolAllowance" class="form-label">
                  {{ "COMMON.SchoolAllowance" | translate }}
                </label>
                <input
                  type="text"
                  id="SchoolAllowance"
                  name="SchoolAllowance"
                  class="form-control"
                  value="0"
                  formControlName="SchoolAllowance"
                />
              </div>
            </div>
          </div>
          <!-- Tab 7 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab7'"
            [class.active]="activeTab === 'tab7'"
            *ngIf="activeTab === 'tab7'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="ShortName" class="form-label">
                  {{ "COMMON.AbbreviatedName" | translate }}
                </label>
                <input
                  type="text"
                  id="ShortName"
                  name="ShortName"
                  class="form-control"
                  formControlName="ShortName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="PreviousJobPlace" class="form-label">
                  {{ "COMMON.PreviousJobAddress" | translate }}
                </label>
                <input
                  type="text"
                  id="PreviousJobPlace"
                  name="PreviousJobPlace"
                  class="form-control"
                  formControlName="PreviousJobPlace"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="WhyLeavePreviousJob" class="form-label">
                  {{ "COMMON.ReasonForLeavingJob" | translate }}
                </label>
                <input
                  type="text"
                  id="WhyLeavePreviousJob"
                  name="WhyLeavePreviousJob"
                  class="form-control"
                  formControlName="WhyLeavePreviousJob"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.JobLeavingDate" | translate }}
                </label>
                <input
                  id="DateLeavePreviousJob"
                  type="date"
                  formControlName="DateLeavePreviousJob"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="SpouseName" class="form-label">
                  {{ "COMMON.SpouseName" | translate }}
                </label>
                <input
                  type="text"
                  id="SpouseName"
                  name="SpouseName"
                  class="form-control"
                  formControlName="SpouseName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="SpousePhoneNumber" class="form-label">
                  {{ "COMMON.SpousePhone" | translate }}
                </label>
                <input
                  type="text"
                  id="SpousePhoneNumber"
                  name="SpousePhoneNumber"
                  class="form-control"
                  formControlName="SpousePhoneNumber"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="PhoneNumber" class="form-label">
                  {{ "COMMON.Phone" | translate }}
                </label>
                <input
                  type="text"
                  id="PhoneNumber"
                  name="PhoneNumber"
                  class="form-control"
                  formControlName="PhoneNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="RelativePhone" class="form-label">
                  {{ "COMMON.RelativesPhone" | translate }}
                </label>
                <input
                  type="text"
                  id="RelativePhone"
                  name="RelativePhone"
                  class="form-control"
                  formControlName="RelativePhone"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="RelativeName" class="form-label">
                  {{ "COMMON.RelativesNames" | translate }}
                </label>
                <input
                  type="text"
                  id="RelativeName"
                  name="RelativeName"
                  class="form-control"
                  formControlName="RelativeName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="SonCont" class="form-label">
                  {{ "COMMON.NumberOfChildren" | translate }}
                </label>
                <input
                  type="text"
                  id="SonCont"
                  name="SonCont"
                  class="form-control"
                  formControlName="SonCont"
                />
              </div>
            </div>
          </div>
          <!-- Tab 8 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab8'"
            [class.active]="activeTab === 'tab8'"
            *ngIf="activeTab === 'tab8'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="MilitaryStatus" class="form-label">
                  {{ "COMMON.MilitarySituation" | translate }}
                </label>
                <ng-select
                  id="MilitaryStatus"
                  formControlName="MilitaryStatus"
                  bindLabel="name"
                  bindValue="id"
                  [items]="militarySituations"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.GraduationYearDate" | translate }}
                </label>
                <input
                  id="GraduationDate"
                  type="date"
                  formControlName="GraduationDate"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="Address" class="form-label">
                  {{ "COMMON.Address" | translate }}
                </label>
                <input
                  type="text"
                  id="Address"
                  name="Address"
                  class="form-control"
                  formControlName="Address"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="Email" class="form-label">
                  {{ "COMMON.Mail" | translate }}
                </label>
                <input
                  type="text"
                  id="Email"
                  name="Email"
                  class="form-control"
                  formControlName="Email"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="experienceYears" class="form-label">
                  {{ "COMMON.ExperienceYears" | translate }}
                </label>
                <input
                  type="text"
                  id="experienceYears"
                  name="experienceYears"
                  class="form-control"
                  formControlName="experienceYears"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="MobileNumber" class="form-label">
                  {{ "COMMON.Mobile" | translate }}
                </label>
                <input
                  type="text"
                  id="MobileNumber"
                  name="MobileNumber"
                  class="form-control"
                  formControlName="MobileNumber"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="LaborOfficeNumber" class="form-label">
                  {{ "COMMON.LaborOfficeNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="LaborOfficeNumber"
                  name="LaborOfficeNumber"
                  value="0"
                  class="form-control"
                  formControlName="LaborOfficeNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="lineManager" class="form-label">
                  {{ "COMMON.LineManager" | translate }}
                </label>
                <ng-select
                  id="lineManager"
                  formControlName="lineManager"
                  bindLabel="name"
                  bindValue="id"
                  [items]="lineManagers"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <span class="mx-2">{{
                  "COMMON.DeliveryRepresentative" | translate
                }}</span>
                <label class="switch">
                  <input
                    type="checkbox"
                    formControlName="DeliveryRepresentativeSwitch"
                  />
                  <span class="slider"></span>
                </label>
              </div>
            </div>
          </div>
          <!-- Tab 9 -->
          <div
            class="tab-pane fade mt-2"
            [class.show]="activeTab === 'tab9'"
            [class.active]="activeTab === 'tab9'"
            *ngIf="activeTab === 'tab9'"
          >
            <div class="container">
              <div class="row">
                <div class="col-xl-4 col-md-6 col-sm-12">
                  <h4 class="fs-6">
                    {{ "COMMON.AllowancesAffectedByAbsence" | translate }}
                  </h4>

                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_Ta3am"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.MealAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_Tabe3a"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.NatureOfWorkAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_Sakan"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.HousingAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_Other"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.OtherAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_Entekal"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TransportationAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Badal_tel"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TelephoneAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="Eff_Take_Schole"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.SchoolAllowance" | translate }}
                    </label>
                  </div>
                </div>

                <div class="col-xl-4 col-md-6 col-sm-12">
                  <h4 class="fs-6">
                    {{
                      "COMMON.AllowancesAffectingVacationExtract" | translate
                    }}
                  </h4>

                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_Ta3am"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.MealAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_Tabe3a"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.NatureOfWorkAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_Sakan"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.HousingAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_Other"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.OtherAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_Entekal"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TransportationAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Badal_tel"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TelephoneAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="VEff_Take_Schole"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.SchoolAllowance" | translate }}
                    </label>
                  </div>
                </div>

                <div class="col-xl-4 col-md-6 col-sm-12">
                  <h4 class="fs-6">
                    {{ "COMMON.EndOfServiceAllowances" | translate }}
                  </h4>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_Ta3am"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.MealAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_Tabe3a"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.NatureOfWorkAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_Sakan"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.HousingAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_Other"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.OtherAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_Entekal"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TransportationAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Badal_tel"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.TelephoneAllowance" | translate }}
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-2 col-sm-6">
                    <dx-check-box
                      formControlName="ES_Eff_Take_Schole"
                      valueExpr="value"
                    ></dx-check-box>
                    <label class="form-label d-block p-2">
                      {{ "COMMON.SchoolAllowance" | translate }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Tab 10 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab10'"
            [class.active]="activeTab === 'tab10'"
            *ngIf="activeTab === 'tab10'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="documentType" class="form-label">
                  {{ "COMMON.DocumentType" | translate }}
                </label>
                <ng-select
                  id="documentType"
                  formControlName="documentType"
                  bindLabel="name"
                  bindValue="id"
                  [items]="documentTypes"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="issuer" class="form-label">
                  {{ "COMMON.Issuer" | translate }}
                </label>
                <input
                  type="text"
                  id="issuer"
                  name="issuer"
                  class="form-control"
                  formControlName="issuer"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.IssueDate" | translate }}
                </label>
                <input
                  id="issueDate"
                  type="date"
                  formControlName="issueDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.EXPDate" | translate }}
                </label>
                <input
                  id="endIssueDate"
                  type="date"
                  formControlName="endIssueDate"
                  class="form-control"
                />
              </div>
            </div>
          </div>
          <!-- Tab 11 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab11'"
            [class.active]="activeTab === 'tab11'"
            *ngIf="activeTab === 'tab11'"
          >
            <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
              <div class="main-inputs flex-grow-1">
                <div class="row">
                  <div class="form-group col-xl-4 col-md-6 col-sm-12">
                    <label for="status" class="form-label">
                      {{ "COMMON.Status" | translate }}
                    </label>
                    <ng-select
                      id="status"
                      formControlName="status"
                      bindLabel="name"
                      bindValue="id"
                      [items]="statuses"
                    ></ng-select>
                  </div>
                  <div class="form-group col-xl-4 col-md-6 col-sm-12">
                    <label for="Notes" class="form-label">
                      {{ "COMMON.Notes" | translate }}
                    </label>
                    <input
                      type="text"
                      id="Notes"
                      name="Notes"
                      class="form-control"
                      formControlName="Notes"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="form-group col-xl-2 col-md-4 col-sm-12">
                    <span class="mx-2">{{
                      "COMMON.SalarySuspension" | translate
                    }}</span>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="SalarySuspensionSwitch"
                      />
                      <span class="slider"></span>
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-4 col-sm-12">
                    <span class="mx-2">{{
                      "COMMON.SuspensionOfResidenceRenewal" | translate
                    }}</span>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="SuspensionOfResidenceRenewalSwitch"
                      />
                      <span class="slider"></span>
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-4 col-sm-12">
                    <span class="mx-2">{{
                      "COMMON.SuspensionOfVacations" | translate
                    }}</span>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="SuspensionOfVacationsSwitch"
                      />
                      <span class="slider"></span>
                    </label>
                  </div>
                  <div class="form-group col-xl-2 col-md-4 col-sm-12">
                    <span class="mx-2">{{
                      "COMMON.StopExportingBank" | translate
                    }}</span>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="StopExportingBankSwitch"
                      />
                      <span class="slider"></span>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Signature photo  -->
              <div
                class="photo-container position-relative d-flex flex-column align-items-center"
                (mouseenter)="showOverlay = true"
                (mouseleave)="showOverlay = false"
              >
                <img
                  [src]="
                    signaturePhoto || 'assets/media/avatars/FalconSignature.png'
                  "
                  alt="New Signature Image"
                  class="photo img-fluid rounded mb-2"
                />
                <div
                  class="overlay d-flex flex-column justify-content-end align-items-center"
                  *ngIf="showOverlay"
                >
                  <div
                    class="button-group d-flex justify-content-between gap-2"
                  >
                    <button
                      class="btn btn-primary btn-sm"
                      (click)="triggerFileInput2($event)"
                    >
                      <i class="fa fa-edit fs-2"></i>
                    </button>
                    <button
                      class="btn btn-danger btn-sm"
                      (click)="clearSignaturePhoto($event)"
                    >
                      <i class="fa fa-trash-o fs-2"></i>
                    </button>
                  </div>
                </div>
                <input
                  type="file"
                  accept="image/*"
                  class="d-none"
                  #fileInput2
                  (change)="onSignaturePhotoSelected($event)"
                />
              </div>
              <!-- End Signature photo  -->
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
