import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { formatDate } from '@angular/common';
import Swal from 'sweetalert2';
import ArrayStore from 'devextreme/data/array_store';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { ActivatedRoute } from '@angular/router';
import { environment } from '../../../../../environments/environment';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { EmployeesService } from '../Employees.service';

@Component({
  selector: 'app-employees',
  templateUrl: './employees.component.html',
  styleUrls: ['./employees.component.scss'],
  standalone: false,
})
export class EmployeesComponent implements OnDestroy {
  moduleName = 'HR.Employees';

  data: ArrayStore;
  departments: any[];
  editorOptions: any;
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  currentFilter: any;
  selectedItemKeys: any = [];
  subscriptions = new Subscription();
  itemsCount = 0;
  pagesCount = 0;
  departId = 0;
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  companies: [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: EmployeesService,
    private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute
  ) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required],
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
    this.subscription.add(
      service.getDepartDropdown().subscribe((r) => {
        if (r.success) {
          this.departments = r.data;
          this.cdk.detectChanges();
        }
      })
    );



    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['ID', 'nameAr'];
  }

  ngOnInit(): void {
    console.log('Employees list component initialized');
    this.subscriptions.add(
      this.route.paramMap.subscribe((p) => {
        const id = p.get('id') || '';
        console.log('Route parameter ID:', id);
        //if (id) {
        this.subscriptions.add(
          this.service.list(id).subscribe((r) => {
            console.log('Employees list response:', r);
            if (r.success) {
              this.loadData(r);
            } else {
              console.error('Failed to load employees list:', r);
            }
          })
        );
        //  }
      })
    );
    this.searchCtrl.valueChanges.subscribe((v) => {
      console.log('Search value changed:', v);
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(
          this.service.search(v).subscribe((r) => {
            console.log('Search results:', r);
            if (r.success) {
              this.loadData(r);
              this.itemsCount = r.data.itemsCount;
            } else {
              console.error('Search failed:', r);
            }
          })
        );
      } else if (id) {
        this.subscriptions.add(
          this.service.list(id).subscribe((r) => {
            console.log('Loading employees with ID after search cleared:', r);
            if (r.success) {
              this.loadData(r);
            } else {
              console.error('Failed to load employees after search cleared:', r);
            }
          })
        );
      } else {
        this.subscriptions.add(
          this.service.list('').subscribe((r) => {
            console.log('Loading all employees after search cleared:', r);
            if (r.success) {
              this.loadData(r);
            } else {
              console.error('Failed to load all employees after search cleared:', r);
            }
          })
        );
      }
    });
  }

  onItemClick(e: any) {}

  selectionChanged(data: any) {
    // تخزين المفاتيح المحددة
    if (data && data.selectedRowKeys && data.selectedRowKeys.length >= 0) {
      this.selectedItemKeys = [...data.selectedRowKeys];
      console.log('Selected rows:', this.selectedItemKeys);
    }
    // تطبيق التغييرات
    setTimeout(() => {
      this.cdk.detectChanges();
    }, 0);
  }

  deleteRecords() {
    this.remove();
  }

  @ViewChild(DxDataGridComponent, { static: false })
  dataGrid: DxDataGridComponent;

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      icon: 'warning',
    }).then((r) => {
      if (r.isConfirmed) {
        console.log('User confirmed delete for:', this.selectedItemKeys);
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(
            this.service.delete(key).subscribe((r) => {
              console.log('Delete response for ID ' + key + ':', r);
              if (r.success) {
                this.data.remove(key);
                this.dataGrid.instance.refresh();
                console.log('Item removed from grid:', key);
                this.cdk.detectChanges();
              } else {
                console.error('Failed to delete item:', key, r);
              }
            })
          );
        });
      } else {
        console.log('User canceled delete operation');
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('falconWorkbook');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'falconData.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('falconList.pdf');
      });
    }
    e.cancel = true;
  }

  newbtn() {}

  loadData(r: any) {
    console.log('Loading data into grid:', r);
    this.data = new ArrayStore({
      key: 'id',
      data: r.data,
    });
    // this.dataSource = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
      console.log('Items count updated:', this.itemsCount);
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
      console.log('Pages count updated:', this.pagesCount);
    }
    this.cdk.detectChanges();
    MenuComponent.reinitialization();
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  // دالة لطباعة قائمة الموظفين
  printEmployeesList() {
    console.log('Printing employees list');

    // الحصول على بيانات الموظفين من الجدول
    let employeesData: any[] = [];

    // أخذ البيانات من DataGrid مباشرة
    if (this.dataGrid && this.dataGrid.instance) {
      this.dataGrid.instance.getVisibleRows().forEach((row: any) => {
        employeesData.push(row.data);
      });
    }

    console.log('Employees data to print:', employeesData);

    // إنشاء محتوى HTML للطباعة
    let printContent = `
      <html>
        <head>
          <title>قائمة الموظفين</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            h1 { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            table, th, td { border: 1px solid #ddd; }
            th, td { padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            .logo { text-align: center; margin-bottom: 20px; }
            .logo img { max-height: 60px; }
            .date { text-align: left; margin-bottom: 10px; }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="logo">
            <img src="assets/media/logos/falcon.png" alt="شعار فالكون">
          </div>

          <div class="date">تاريخ الطباعة: ${formatDate(new Date(), 'yyyy-MM-dd HH:mm', 'en-US')}</div>

          <h1>قائمة الموظفين</h1>

          <table>
            <thead>
              <tr>
                <th>الرقم</th>
                <th>الاسم</th>
                <th>رقم الهوية</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الجوال</th>
                <th>القسم</th>
              </tr>
            </thead>
            <tbody>
    `;

    if (employeesData.length === 0) {
      printContent += '<tr><td colspan="6" style="text-align: center;">لا توجد بيانات</td></tr>';
    } else {
      employeesData.forEach((employee: any, index: number) => {
        printContent += `
          <tr>
            <td>${index + 1}</td>
            <td>${employee.nameAr || ''}</td>
            <td>${employee.idNumber || ''}</td>
            <td>${employee.email || ''}</td>
            <td>${employee.mobile || ''}</td>
            <td>${employee.departmentName || ''}</td>
          </tr>
        `;
      });
    }

    printContent += `
            </tbody>
          </table>

          <div style="margin-top: 20px; text-align: center; font-size: 12px;">
            نظام فالكون ERP - جميع الحقوق محفوظة
          </div>
        </body>
      </html>
    `;

    // إنشاء إطار مخفي للطباعة
    const printFrame = document.createElement('iframe');
    printFrame.style.position = 'absolute';
    printFrame.style.top = '-9999px';
    printFrame.style.left = '-9999px';
    document.body.appendChild(printFrame);

    // كتابة المحتوى في الإطار
    const frameDocument = printFrame.contentDocument || printFrame.contentWindow?.document;
    if (frameDocument) {
      frameDocument.open();
      frameDocument.write(printContent);
      frameDocument.close();

      // انتظر تحميل الصور ثم اطبع
      setTimeout(() => {
        printFrame.contentWindow?.focus();
        printFrame.contentWindow?.print();

        // إزالة الإطار بعد الطباعة
        setTimeout(() => {
          document.body.removeChild(printFrame);
        }, 1000);
      }, 500);
    } else {
      console.error('Failed to create print frame document');
    }
  }

  filter() {
    const DepartId = this.departId;
    this.subscription.add(
      this.service.filter({ DepartId }).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
