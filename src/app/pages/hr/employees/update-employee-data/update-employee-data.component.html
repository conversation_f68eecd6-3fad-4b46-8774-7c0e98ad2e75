<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="main-inputs">
      <div class="row">
        <div class="form-group col-xl-3 col-md-3 col-sm-12">
          <label for="fieldId_1" class="form-label">
            {{ "COMMON.field_1" | translate }}
          </label>
          <ng-select
            id="fieldId_1"
            formControlName="fieldId_1"
            bindLabel="nameAr"
            bindValue="id"
            [items]="fields"
          ></ng-select>
        </div>
        <div class="form-group col-xl-3 col-md-3 col-sm-12">
          <label for="fieldId_2" class="form-label">
            {{ "COMMON.field_2" | translate }}
          </label>
          <ng-select
            id="fieldId_2"
            formControlName="fieldId_2"
            bindLabel="nameAr"
            bindValue="id"
            [items]="fields"
          ></ng-select>
        </div>
        <div class="form-group col-xl-3 col-md-3 col-sm-12">
          <label for="fieldId_3" class="form-label">
            {{ "COMMON.field_3" | translate }}
          </label>
          <ng-select
            id="fieldId_3"
            formControlName="fieldId_3"
            bindLabel="nameAr"
            bindValue="id"
            [items]="fields"
          ></ng-select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-xl-3 col-md-3 col-sm-12">
          <label for="fieldId_4" class="form-label">
            {{ "COMMON.field_4" | translate }}
          </label>
          <ng-select
            id="fieldId_4"
            formControlName="fieldId_4"
            bindLabel="nameAr"
            bindValue="id"
            [items]="fields"
          ></ng-select>
        </div>
        <div class="form-group col-xl-3 col-md-3 col-sm-12">
          <label for="fieldId_5" class="form-label">
            {{ "COMMON.field_5" | translate }}
          </label>
          <ng-select
            id="fieldId_5"
            formControlName="fieldId_5"
            bindLabel="nameAr"
            bindValue="id"
            [items]="fields"
          ></ng-select>
        </div>
      </div>
    </div>
  </div>
  <a (click)="filter()" class="btn btn-info m-1 btn-sm">{{
    "COMMON.Update" | translate
  }}</a>
</form>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="id"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
  (onSelectionChanged)="selectionChanged($event)"
>
  <dxo-selection mode="multiple"></dxo-selection>

  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="id"
    caption="{{ 'COMMON.id' | translate }}"
  ></dxi-column>
  <dxi-column dataField="nameAr" caption="{{ 'COMMON.NameAr' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="enName" caption="{{ 'COMMON.NameEn' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="job" caption="{{ 'COMMON.Job' | translate }}">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column
    dataField="depart"
    caption="{{ 'COMMON.Department' | translate }}"
  >
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
    <!--<dxi-total-item column="InvoiceDate"
                    summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>-->
    <!--<dxi-total-item column="Total"
                    summaryType="sum"
                    valueFormat="currency">
    </dxi-total-item>-->
  </dxo-summary>
</dx-data-grid>
