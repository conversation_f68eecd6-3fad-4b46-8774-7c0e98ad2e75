import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EmployeeEvaluationComponent } from './employee-evaluation/employee-evaluation.component';
import { MonthlyEvaluationComponent } from './monthly-evaluation/monthly-evaluation.component';
import { NewEmployeeEvaluationComponent } from './new-employee-evaluation/new-employee-evaluation.component';
import { NewEmployeeEvaluationViewComponent } from './new-employee-evaluation/new-employee-evaluation-view/new-employee-evaluation-view.component';
import { EmployeeEvaluationViewComponent } from './employee-evaluation/employee-evaluation-view/employee-evaluation-view.component';
import { MonthlyEvaluationViewComponent } from './monthly-evaluation/monthly-evaluation-view/monthly-evaluation-view.component';
import { NewEmployeeEvaluationAssessmentItemsComponent } from './new-employee-evaluation/new-employee-evaluation-assessment-items/new-employee-evaluation-assessment-items.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'new_employee_evaluation',
    pathMatch: 'full',
  },
  {
    path: 'new_employee_evaluation_view',
    component: NewEmployeeEvaluationViewComponent,
  },
  {
    path: 'new_employee_evaluation_view/:id',
    component: NewEmployeeEvaluationViewComponent,
  },
  {
    path: 'new_employee_evaluation',
    component: NewEmployeeEvaluationComponent,
  },
  {
    path: 'new_employee_evaluation_assessment_items',
    component: NewEmployeeEvaluationAssessmentItemsComponent,
  },
  {
    path: 'employee_evaluation_view',
    component: EmployeeEvaluationViewComponent,
  },
  {
    path: 'employee_evaluation_view/:id',
    component: EmployeeEvaluationViewComponent,
  },
  {
    path: 'employee_evaluation',
    component: EmployeeEvaluationComponent,
  },
  {
    path: 'monthly_evaluation_view',
    component: MonthlyEvaluationViewComponent,
  },
  {
    path: 'monthly_evaluation_view/:id',
    component: MonthlyEvaluationViewComponent,
  },
  {
    path: 'monthly_evaluation',
    component: MonthlyEvaluationComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrEmployeeEvaluationRoutingModule {}
