<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.NewEmployeeEvaluation" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              [routerLink]="[
                '/hr/employee_evaluation/new_employee_evaluation_assessment_items'
              ]"
              class="menu-link px-3"
              *hasPermission="{
                module: 'hr.NewEmployeeEvaluation',
                action: 'updateAction'
              }"
            >
              {{ "COMMON.AddAssessmentItems" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.NewEmployeeEvaluation'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.NewEmployeeEvaluation'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row mb-3">
            <div class="form-group col-xl-3 col-md-4 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>
        </div>
      </div>
    </form>

    <dx-data-grid
      id="gridcontrole"
      [rtlEnabled]="true"
      [dataSource]="data"
      keyExpr="id"
      [showRowLines]="true"
      [showBorders]="true"
      [columnAutoWidth]="true"
      (onExporting)="onExporting($event)"
      [allowColumnResizing]="true"
      (onSelectionChanged)="selectionChanged($event)"
    >
      <dxo-selection mode="multiple"></dxo-selection>

      <dxo-filter-row
        [visible]="true"
        [applyFilter]="currentFilter"
      ></dxo-filter-row>

      <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
      <dxo-paging [pageSize]="10"> </dxo-paging>
      <dxo-pager
        [visible]="true"
        [allowedPageSizes]="[5, 10, 'all']"
        [displayMode]="'compact'"
        [showPageSizeSelector]="true"
        [showInfo]="true"
        [showNavigationButtons]="true"
      >
      </dxo-pager>
      <dxo-header-filter [visible]="true"></dxo-header-filter>

      <dxo-search-panel
        [visible]="true"
        [highlightCaseSensitive]="true"
      ></dxo-search-panel>

      <dxi-column
        dataField="id"
        caption="{{ 'COMMON.id' | translate }}"
      ></dxi-column>
      <dxi-column
        dataField="nameAr"
        caption="{{ 'COMMON.EmployeeName' | translate }}"
      >
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>

      <dxi-column
        dataField="assessment"
        caption="{{ 'COMMON.Assessment' | translate }}"
      >
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>

      <dxi-column
        dataField="excellent"
        caption="{{ 'COMMON.Excellent' | translate }}"
      >
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>
      <dxi-column
        dataField="veryGood"
        caption="{{ 'COMMON.VeryGood' | translate }}"
      >
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>
      <dxi-column dataField="good" caption="{{ 'COMMON.Good' | translate }}">
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>

      <dxi-column
        dataField="acceptable"
        caption="{{ 'COMMON.Acceptable' | translate }}"
      >
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>

      <dxi-column dataField="weak" caption="{{ 'COMMON.Weak' | translate }}">
        <dxo-header-filter>
          <dxo-search [enabled]="true"></dxo-search>
        </dxo-header-filter>
      </dxi-column>
      <dxi-column
        dataField="ratingPercentage"
        caption="{{ 'COMMON.RatingPercentage' | translate }}"
      ></dxi-column>
      <dxi-column
        dataField="notes"
        caption="{{ 'COMMON.Notes' | translate }}"
      ></dxi-column>

      <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

      <dxo-summary>
        <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
      </dxo-summary>
    </dx-data-grid>
  </div>
</div>
