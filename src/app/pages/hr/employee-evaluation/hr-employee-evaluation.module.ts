import { NgModule } from '@angular/core';
import { EmployeeEvaluationComponent } from './employee-evaluation/employee-evaluation.component';
import { MonthlyEvaluationComponent } from './monthly-evaluation/monthly-evaluation.component';
import { NewEmployeeEvaluationComponent } from './new-employee-evaluation/new-employee-evaluation.component';
import { HrEmployeeEvaluationRoutingModule } from './hr-employee-evaluation-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { NewEmployeeEvaluationViewComponent } from './new-employee-evaluation/new-employee-evaluation-view/new-employee-evaluation-view.component';
import { EmployeeEvaluationViewComponent } from './employee-evaluation/employee-evaluation-view/employee-evaluation-view.component';
import { MonthlyEvaluationViewComponent } from './monthly-evaluation/monthly-evaluation-view/monthly-evaluation-view.component';
import { NewEmployeeEvaluationAssessmentItemsComponent } from './new-employee-evaluation/new-employee-evaluation-assessment-items/new-employee-evaluation-assessment-items.component';

@NgModule({
  declarations: [
    NewEmployeeEvaluationComponent,
    EmployeeEvaluationComponent,
    MonthlyEvaluationComponent,
    NewEmployeeEvaluationViewComponent,
    EmployeeEvaluationViewComponent,
    MonthlyEvaluationViewComponent,
    NewEmployeeEvaluationAssessmentItemsComponent,
  ],
  imports: [HrEmployeeEvaluationRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrEmployeeEvaluationModule {}
