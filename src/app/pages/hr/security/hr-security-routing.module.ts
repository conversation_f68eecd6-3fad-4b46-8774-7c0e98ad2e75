import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CarTrafficComponent } from './car-traffic/car-traffic.component';
import { TruckingComponent } from './trucking/trucking.component';
import { VisitorsRecordComponent } from './visitors-record/visitors-record.component';
import { TruckingViewComponent } from './trucking/trucking-view/trucking-view.component';
import { CarTrafficViewComponent } from './car-traffic/car-traffic-view/car-traffic-view.component';
import { VisitorsRecordViewComponent } from './visitors-record/visitors-record-view/visitors-record-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'trucking',
    pathMatch: 'full',
  },
  {
    path: 'trucking_view',
    component: TruckingViewComponent,
  },
  {
    path: 'trucking_view/:id',
    component: TruckingViewComponent,
  },
  {
    path: 'trucking',
    component: TruckingComponent,
  },
  {
    path: 'car_traffic_view',
    component: CarTrafficViewComponent,
  },
  {
    path: 'car_traffic_view/:id',
    component: CarTrafficViewComponent,
  },
  {
    path: 'car_traffic',
    component: CarTrafficComponent,
  },
  {
    path: 'visitors_record_view',
    component: VisitorsRecordViewComponent,
  },
  {
    path: 'visitors_record_view/:id',
    component: VisitorsRecordViewComponent,
  },
  {
    path: 'visitors_record',
    component: VisitorsRecordComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrSecurityRoutingModule {}
