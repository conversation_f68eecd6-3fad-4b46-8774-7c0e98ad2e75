<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.VisitorsRecord" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'hr.VisitorsRecord'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'hr.VisitorsRecord'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر رقم الزيارة وتاريخ الزيارة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="visitNumber" class="form-label">
                {{ "COMMON.VisitNumber" | translate }}
              </label>
              <input
                id="visitNumber"
                type="text"
                formControlName="visitNumber"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.VisitDate" | translate
              }}</label>
              <input
                id="visitDate"
                type="date"
                formControlName="visitDate"
                class="form-control"
              />
            </div>
          </div>

          <!-- سطر أسماء الزوار والغرض من الزيارة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="visitorsName" class="form-label">
                {{ "COMMON.VisitorsName" | translate }}
              </label>
              <textarea
                id="visitorsName"
                formControlName="visitorsName"
                class="form-control"
                style="background-color: inherit"
                rows="3"
              ></textarea>
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="visitPurpose" class="form-label">
                {{ "COMMON.VisitPurpose" | translate }}
              </label>
              <textarea
                id="visitPurpose"
                formControlName="visitPurpose"
                class="form-control"
                style="background-color: inherit"
                rows="3"
              ></textarea>
            </div>
          </div>

          <!-- سطر وقت الدخول ووقت الخروج -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.InTime" | translate
              }}</label>
              <input
                id="inTime"
                type="datetime-local"
                formControlName="inTime"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.OutTime" | translate
              }}</label>
              <input
                id="outTime"
                type="datetime-local"
                formControlName="outTime"
                class="form-control"
              />
            </div>
          </div>

          <!-- سطر رقم السيارة والملاحظات -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="carNumber" class="form-label">
                {{ "COMMON.CarNumber" | translate }}
              </label>
              <input
                id="carNumber"
                type="text"
                formControlName="carNumber"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="notes" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                id="notes"
                type="text"
                formControlName="notes"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
