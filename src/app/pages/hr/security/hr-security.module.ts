import { NgModule } from '@angular/core';
import { CarTrafficComponent } from './car-traffic/car-traffic.component';
import { TruckingComponent } from './trucking/trucking.component';
import { VisitorsRecordComponent } from './visitors-record/visitors-record.component';
import { HrSecurityRoutingModule } from './hr-security-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { TruckingViewComponent } from './trucking/trucking-view/trucking-view.component';
import { CarTrafficViewComponent } from './car-traffic/car-traffic-view/car-traffic-view.component';
import { VisitorsRecordViewComponent } from './visitors-record/visitors-record-view/visitors-record-view.component';

@NgModule({
  declarations: [
    TruckingComponent,
    CarTrafficComponent,
    VisitorsRecordComponent,
    TruckingViewComponent,
    CarTrafficViewComponent,
    VisitorsRecordViewComponent,
  ],
  imports: [HrSecurityRoutingModule, SharedModule],
  exports: [RouterModule],
})
export class HrSecurityModule {}
