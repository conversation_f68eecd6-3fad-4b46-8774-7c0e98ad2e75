import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Security } from '../../Security.service';
@Component({
  selector: 'app-trucking-view',
  templateUrl: './trucking-view.component.html',
  styleUrls: ['./trucking-view.component.css'],
  standalone: false,
})
export class TruckingViewComponent implements OnInit, OnDestroy {
  moduleName = 'hr.Trucking';
  roles: any[] = [];
  customers: any[];
  commissioningDepartments: any[];
  names: any[] = [];
  shippingCompanies: any[] = [];
  employeeId: number = 0;
  commissioningDepartment: number = 0;
  name: number = 0;
  selectedOption = 'developer';


  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];
  timeId: number = 0;
  _currentPage = 1;
  data: any[] = [];
  editorOptions: any;
  searchExpr: any;
  fromnextDateButton: any;
  subscription = new Subscription();
  printList: string[] = ['print', 'Print Custome'];
  currentFilter: any;
  itemsCount = 0;
  pagesCount = 0;

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: Security,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    const currentTime = new Date().toLocaleTimeString();

    this.newdata = this.fb.group({
      employeeId: [null, Validators.required],
      dateValue: [today, Validators.required],
      shipmentDate: [today, Validators.required],
      invoiceDate: [today, Validators.required],
      OptionControl: [this.selectedOption],

    });
    this.subscription.add(
      this.myService.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.customers = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    this.newdata.get('basicSalary')?.setValue(item.basicSalary);
    this.newdata.get('changedSalary')?.setValue(item.changedSalary);
  }
  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(
      this.myService.list('', value).subscribe((r) => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          MenuComponent.reinitialization();
        }
      })
    );
  }

  get currentPage() {
    return this._currentPage;
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/security/trucking']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/security/trucking']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }

  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }
  uploadExcel() {
    var input = document.createElement('input');
    input.type = 'file';
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(
            this.myService.importExcel(fd).subscribe((e) => {
              if (e) {
                this.currentPage = 1;
              }
            })
          );
        }
      }
    };
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
