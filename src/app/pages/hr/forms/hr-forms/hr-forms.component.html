<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.HRForms" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-8 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="Employees"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameAr" class="form-label">
                {{ "COMMON.EmployeeNameAr" | translate }}
              </label>
              <input
                type="text"
                id="nameAr"
                name="nameAr"
                class="form-control"
                formControlName="nameAr"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameEn" class="form-label">
                {{ "COMMON.EmployeeNameEn" | translate }}
              </label>
              <input
                type="text"
                name="nameEn"
                id="nameEn"
                class="form-control"
                formControlName="nameEn"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="employeeCode" class="form-label">
                {{ "COMMON.EmployeeId" | translate }}
              </label>
              <input
                type="text"
                name="employeeCode"
                id="employeeCode"
                class="form-control"
                formControlName="employeeCode"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="brief" class="form-label">
                {{ "COMMON.Brief" | translate }}
              </label>
              <input
                type="text"
                name="brief"
                id="brief"
                class="form-control"
                formControlName="brief"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="form" class="form-label">
                {{ "COMMON.Form" | translate }}
              </label>
              <ng-select
                id="form"
                formControlName="form"
                bindLabel="nameAr"
                bindValue="id"
                [items]="forms"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="maritalStatus" class="form-label">
                {{ "COMMON.MaritalStatus" | translate }}
              </label>
              <input
                type="text"
                id="maritalStatus"
                name="maritalStatus"
                class="form-control"
                formControlName="maritalStatus"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="qualification" class="form-label">
                {{ "COMMON.Qualifications" | translate }}
              </label>
              <input
                type="text"
                id="qualification"
                name="qualification"
                class="form-control"
                formControlName="qualification"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label class="form-label">
                {{ "COMMON.HiringDate" | translate }}
              </label>
              <input
                id="hiringDate"
                type="date"
                formControlName="hiringDate"
                class="form-control"
              />
            </div>
          </div>
        </div>
        <!-- photo -->
        <div
          class="photo-container position-relative d-flex flex-column align-items-center"
          (mouseenter)="showOverlay = true"
          (mouseleave)="showOverlay = false"
        >
          <img
            [src]="employeePhoto || 'assets/media/avatars/blank.png'"
            alt="New Employee"
            class="photo img-fluid rounded mb-2"
          />
          <div
            class="overlay d-flex flex-column justify-content-end align-items-center"
            *ngIf="showOverlay"
          >
            <div class="button-group d-flex justify-content-between gap-2">
              <button
                class="btn btn-primary btn-sm"
                (click)="triggerFileInput1($event)"
              >
                <i class="fa fa-edit fs-2"></i>
              </button>
              <button
                class="btn btn-danger btn-sm"
                (click)="clearEmployeePhoto($event)"
              >
                <i class="fa fa-trash-o fs-2"></i>
              </button>
            </div>
          </div>
          <input
            type="file"
            accept="image/*"
            class="d-none"
            #fileInput1
            (change)="onEmployeePhotoSelected($event)"
          />
        </div>
        <!-- End Photo  -->
      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>

          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.WorkInformation" | translate }}
            </button>
          </li>

          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab3'"
              (click)="setActiveTab('tab3')"
            >
              {{ "COMMON.PrintData" | translate }}
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="religion" class="form-label">
                  {{ "COMMON.Religion" | translate }}
                </label>
                <input
                  type="text"
                  name="religion"
                  id="religion"
                  class="form-control"
                  formControlName="religion"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="religion" class="form-label">
                  {{ "COMMON.Type" | translate }}
                </label>
                <input
                  type="text"
                  name="type"
                  id="type"
                  class="form-control"
                  formControlName="type"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="phone" class="form-label">
                  {{ "COMMON.Phone" | translate }}
                </label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  class="form-control"
                  formControlName="phone"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="religion" class="form-label">
                  {{ "COMMON.MilitarySituation" | translate }}
                </label>
                <input
                  type="text"
                  name="militarySituation"
                  id="militarySituation"
                  class="form-control"
                  formControlName="militarySituation"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="nationality" class="form-label">
                  {{ "COMMON.Nationality" | translate }}
                </label>
                <input
                  type="text"
                  name="nationality"
                  id="nationality"
                  class="form-control"
                  formControlName="nationality"
                />
              </div>
              <div class="form-group col-xl-4 col-md-6 col-sm-12">
                <label for="mobile" class="form-label">
                  {{ "COMMON.Mobile" | translate }}
                </label>
                <input
                  type="text"
                  id="mobile"
                  name="mobile"
                  class="form-control"
                  formControlName="mobile"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="mail" class="form-label">
                  {{ "COMMON.Mail" | translate }}
                </label>
                <input
                  type="text"
                  name="mail"
                  id="mail"
                  class="form-control"
                  formControlName="mail"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="qualification" class="form-label">
                  {{ "COMMON.Qualification" | translate }}
                </label>
                <input
                  type="text"
                  name="qualification"
                  id="qualification"
                  class="form-control"
                  formControlName="qualification"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="address" class="form-label">
                  {{ "COMMON.Address" | translate }}
                </label>
                <input
                  type="text"
                  name="address"
                  id="address"
                  class="form-control"
                  formControlName="address"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="sector" class="form-label">
                  {{ "COMMON.Sector" | translate }}
                </label>
                <input
                  type="text"
                  name="sector"
                  id="sector"
                  class="form-control"
                  formControlName="sector"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="management" class="form-label">
                  {{ "COMMON.Management" | translate }}
                </label>
                <input
                  type="text"
                  name="management"
                  id="management"
                  class="form-control"
                  formControlName="management"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="job" class="form-label">
                  {{ "COMMON.Job" | translate }}
                </label>
                <input
                  type="text"
                  name="job"
                  id="job"
                  class="form-control"
                  formControlName="job"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="workPlan" class="form-label">
                  {{ "Salary.WorkPlan" | translate }}
                </label>
                <input
                  type="text"
                  name="workPlan"
                  id="workPlan"
                  class="form-control"
                  formControlName="workPlan"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="residenceNumber" class="form-label">
                  {{ "COMMON.ResidenceNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="residenceNumber"
                  id="residenceNumber"
                  class="form-control"
                  formControlName="residenceNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="borderNumber" class="form-label">
                  {{ "COMMON.BorderNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="borderNumber"
                  id="borderNumber"
                  class="form-control"
                  formControlName="borderNumber"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-8 col-md-6 col-sm-12">
                <label for="notes" class="form-label">
                  {{ "COMMON.Notes" | translate }}
                </label>
                <input
                  type="text"
                  name="notes"
                  id="notes"
                  class="form-control"
                  formControlName="notes"
                />
              </div>
            </div>
          </div>
          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="salaryPackage" class="form-label">
                  {{ "COMMON.SalaryPackage" | translate }}
                </label>
                <ng-select
                  id="salaryPackage"
                  formControlName="salaryPackage"
                  bindLabel="name"
                  bindValue="id"
                  [items]="salaryPackages"
                ></ng-select>
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.BirthDate" | translate }}
                </label>
                <input
                  id="birthDate"
                  type="date"
                  formControlName="birthDate"
                  class="form-control"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.DateOfEntry" | translate }}
                </label>
                <input
                  id="dateOfEntry"
                  type="date"
                  formControlName="dateOfEntry"
                  class="form-control"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="insuranceSalary" class="form-label">
                  {{ "COMMON.InsuranceSalary" | translate }}
                </label>
                <input
                  type="text"
                  name="insuranceSalary"
                  id="insuranceSalary"
                  class="form-control"
                  formControlName="insuranceSalary"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="changedSalary" class="form-label">
                  {{ "COMMON.ChangedSalary" | translate }}
                </label>
                <input
                  type="text"
                  name="changedSalary"
                  id="changedSalary"
                  class="form-control"
                  formControlName="changedSalary"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="healthCareDiscountRate" class="form-label">
                  {{ "COMMON.HealthCareDiscountRate" | translate }}
                </label>
                <input
                  type="text"
                  name="healthCareDiscountRate"
                  id="healthCareDiscountRate"
                  class="form-control"
                  formControlName="healthCareDiscountRate"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="contractPeriod" class="form-label">
                  {{ "COMMON.ContractPeriod" | translate }}
                </label>
                <input
                  type="text"
                  name="contractPeriod"
                  id="contractPeriod"
                  class="form-control"
                  formControlName="contractPeriod"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="facilityName" class="form-label">
                  {{ "COMMON.FacilityName" | translate }}
                </label>
                <input
                  type="text"
                  name="facilityName"
                  id="facilityName"
                  class="form-control"
                  formControlName="facilityName"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="extraDiscountValue" class="form-label">
                  {{ "COMMON.ExtraDiscountValue" | translate }}
                </label>
                <input
                  type="text"
                  name="extraDiscountValue"
                  id="extraDiscountValue"
                  class="form-control"
                  formControlName="extraDiscountValue"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="annualLeave" class="form-label">
                  {{ "COMMON.AnnualLeave" | translate }}
                </label>
                <input
                  type="text"
                  name="annualLeave"
                  id="annualLeave"
                  class="form-control"
                  formControlName="annualLeave"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="facilityNumber" class="form-label">
                  {{ "COMMON.FacilityNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="facilityNumber"
                  id="facilityNumber"
                  class="form-control"
                  formControlName="facilityNumber"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="workersFundDiscountRatio" class="form-label">
                  {{ "COMMON.WorkersFundDiscountRatio" | translate }}
                </label>
                <input
                  type="text"
                  name="workersFundDiscountRatio"
                  id="workersFundDiscountRatio"
                  class="form-control"
                  formControlName="workersFundDiscountRatio"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="annualTicketsValue" class="form-label">
                  {{ "COMMON.AnnualTicketsValue" | translate }}
                </label>
                <input
                  type="text"
                  name="annualTicketsValue"
                  id="annualTicketsValue"
                  class="form-control"
                  formControlName="annualTicketsValue"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="barcode" class="form-label">
                  {{ "COMMON.Barcode" | translate }}
                </label>
                <input
                  type="text"
                  name="barcode"
                  id="barcode"
                  class="form-control"
                  formControlName="barcode"
                />
              </div>
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="kafeel" class="form-label">
                  {{ "Salary.Kafeel" | translate }}
                </label>
                <input
                  type="text"
                  name="kafeel"
                  id="kafeel"
                  class="form-control"
                  formControlName="kafeel"
                />
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label for="insuranceNumber" class="form-label">
                  {{ "COMMON.InsuranceNumber" | translate }}
                </label>
                <input
                  type="text"
                  name="insuranceNumber"
                  id="insuranceNumber"
                  class="form-control"
                  formControlName="insuranceNumber"
                />
              </div>
            </div>
          </div>
          <!-- Tab 3 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >
            <div class="row">
              <div class="form-group col-xl-4 col-md-4 col-sm-12">
                <label class="form-label">
                  {{ "COMMON.PrintDate" | translate }}
                </label>
                <input
                  id="printDate"
                  type="date"
                  formControlName="printDate"
                  class="form-control"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
