import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { HRService } from '../../hr.service';

@Component({
  selector: 'app-hr-forms',
  templateUrl: './hr-forms.component.html',
  styleUrls: ['./hr-forms.component.scss'],
  standalone: false,
})
export class HrFormsComponent implements OnInit, OnDestroy {
  moduleName = 'HR.HRForms';
  employeePhoto: string | null = 'assets/media/avatars/blank.png';
  signaturePhoto: string | null = 'assets/media/avatars/FalconSignature.png';
  showOverlay = false;
  activeTab: string = 'tab1';
  roles: any[] = [];
  qualifications: any[] = [];
  maritalStatuses: any[] = [];
  religions: any[] = [];
  types: any[] = [];
  sectors: any[] = [];
  visaNumbers: any[] = [];
  nationalities: any[] = [];
  managements: any[] = [];
  workPlans: any[] = [];
  jobsInTheCompany: any[] = [];
  careersInIdentity: any[] = [];
  careersInVisa: any[] = [];
  sponsors: any[] = [];
  insurancePositions: any[] = [];
  facilityNames: any[] = [];
  jobPositions: any[] = [];
  municipalities: any[] = [];
  employeeRanks: any[] = [];
  creditAccounts: any[] = [];
  covenantAccounts: any[] = [];
  medicalInsuranceValues: any[] = [];
  salaryPackages: any[] = [];
  salariesAccounts: any[] = [];
  bankNames: any[] = [];
  virtualCostCenters: any[] = [];
  militarySituations: any[] = [];
  lineManagers: any[] = [];
  documentTypes: any[] = [];
  statuses: any[] = [];
  Employees: any[] = [];
  forms: any[] = [];

  qualification: number = 0;
  maritalStatus: number = 0;
  religion: number = 0;
  type: number = 0;
  visaNumber: number = 0;
  sector: number = 0;
  nationality: number = 0;
  management: number = 0;
  workPlan: number = 0;
  jobInTheCompany: number = 0;
  careerInIdentity: number = 0;
  sponsor: number = 0;
  facilityName: number = 0;
  insurancePosition: number = 0;
  jobPosition: number = 0;
  municipality: number = 0;
  employeeRank: number = 0;
  medicalInsuranceValue: number = 0;
  creditAccount: number = 0;
  covenantAccount: number = 0;
  salariesAccount: number = 0;
  salaryPackage: number = 0;
  bankName: number = 0;
  virtualCostCenter: number = 0;
  militarySituation: number = 0;
  lineManager: number = 0;
  documentType: number = 0;
  status: number = 0;
  form: number = 0;

  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];

  isRtl: boolean = document.documentElement.dir === 'rtl';
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
    'print',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };

  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  @ViewChild('fileInput1') fileInput1!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInput2') fileInput2!: ElementRef<HTMLInputElement>;

  triggerFileInput1(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput1.nativeElement.click();
  }
  triggerFileInput2(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.fileInput2.nativeElement.click();
  }

  onEmployeePhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.employeePhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }
  onSignaturePhotoSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        this.signaturePhoto = reader.result as string;
      };
      reader.readAsDataURL(input.files[0]);
    }
  }

  clearEmployeePhoto(event: Event): void {
    event.stopPropagation();
    this.employeePhoto = 'assets/media/avatars/blank.png';
  }

  clearSignaturePhoto(event: Event): void {
    event.stopPropagation();
    this.signaturePhoto = 'assets/media/avatars/FalconSignature.png';
  }
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private cdk: ChangeDetectorRef,
    private myService: HRService,
    private router: Router
  ) {
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      nameAr: [null, Validators.required],
      nameEn: [null, Validators.required],
      maritalStatus: [null, Validators.required],
      qualification: [null, Validators.required],
      religion: [null, Validators.required],
      type: [null, Validators.required],
      birthDate: [today, Validators.required],
      hiringDate: [today, Validators.required],
      visaNumber: [null, Validators.required],
      sector: [null, Validators.required],
      nationality: [null, Validators.required],
      management: [null, Validators.required],
      workPlan: [null, Validators.required],
      jobInTheCompany: [null, Validators.required],
      careerInIdentity: [null, Validators.required],
      careersInVisa: [null, Validators.required],
      sponsor: [null, Validators.required],
      expDate: [today, Validators.required],
      passportExpDate: [today, Validators.required],
      dateOfEntry: [today, Validators.required],
      healthCertificateEXPDate: [today, Validators.required],
      joiningInsuranceDate: [today, Validators.required],
      joiningMedicalDate: [today, Validators.required],
      printDate: [today, Validators.required],
      medicalInsuranceEXPDate: [today, Validators.required],
      jobLeavingDate: [today, Validators.required],
      graduationYearDate: [today, Validators.required],
      issueDate: [today, Validators.required],
      endIssueDate: [today, Validators.required],
      MealAllowanceAbsenceStatus: [true],
      NatureOfWorkAllowanceAbsenceStatus: [true],
      HousingAllowanceAbsenceStatus: [true],
      OtherAllowanceAbsenceStatus: [true],
      TransportationAllowanceAbsenceStatus: [true],
      TelephoneAllowanceAbsenceStatus: [true],
      SchoolAllowanceAbsenceStatus: [true],
      MealAllowanceVacationStatus: [true],
      NatureOfWorkAllowanceVacationStatus: [true],
      HousingAllowanceVacationStatus: [true],
      OtherAllowanceVacationStatus: [true],
      TransportationAllowanceVacationStatus: [true],
      TelephoneAllowanceVacationStatus: [true],
      SchoolAllowanceVacationStatus: [true],
      MealAllowanceServiceStatus: [true],
      NatureOfWorkAllowanceServiceStatus: [true],
      HousingAllowanceServiceStatus: [true],
      OtherAllowanceServiceStatus: [true],
      TransportationAllowanceServiceStatus: [true],
      TelephoneAllowanceServiceStatus: [true],
      SchoolAllowanceServiceStatus: [true],
      OptionControl: ['IssuingResidence'],
    });
    this.subscriptions.add(
      myService.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.Employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }

  fillForm(item: any) {
    this.newdata.get('nameAr')?.setValue(item.nameAr);
    this.newdata.get('nameEn')?.setValue(item.nameEn);
  }

  save() {
    const id = this.route.snapshot.params.id;
    if (id) {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/forms/hr_forms']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    } else {
      if (this.newdata.valid) {
        let form = this.newdata.value;
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe((r) => {
              if (r.success) {
                this.router.navigate(['/hr/forms/hr_forms']);
              }
            })
        );
      } else {
        this.newdata.markAllAsTouched();
      }
    }
  }
  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
