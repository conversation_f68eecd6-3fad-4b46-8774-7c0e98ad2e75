import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdministrativeDecisionComponent } from './administrative-decision/administrative-decision.component';
import { AppointmentLetterComponent } from './appointment-letter/appointment-letter.component';
import { AssignmentComponent } from './assignment/assignment.component';
import { AttentionComponent } from './attention/attention.component';
import { CertificateComponent } from './certificate/certificate.component';
import { CustodyReceiptComponent } from './custody-receipt/custody-receipt.component';
import { EntitlementsComponent } from './entitlements/entitlements.component';
import { HealthInsuranceComponent } from './health-insurance/health-insurance.component';
import { HrFormsComponent } from './hr-forms/hr-forms.component';
import { LegalIssuesComponent } from './legal-issues/legal-issues.component';
import { MedicalInspectionLetterComponent } from './medical-inspection-letter/medical-inspection-letter.component';
import { ReceiptTransactionsComponent } from './receipt-transactions/receipt-transactions.component';
import { ResignationComponent } from './resignation/resignation.component';
import { SecondmentContractComponent } from './secondment-contract/secondment-contract.component';
import { TicketBookingComponent } from './ticket-booking/ticket-booking.component';
import { WarningComponent } from './warning/warning.component';
import { ContractsComponent } from './contracts/contracts.component';
import { ReceiptTransactionsViewComponent } from './receipt-transactions/receipt-transactions-view/receipt-transactions-view.component';
import { SecondmentContractViewComponent } from './secondment-contract/secondment-contract-view/secondment-contract-view.component';
import { AssignmentViewComponent } from './assignment/assignment-view/assignment-view.component';
import { LegalIssuesViewComponent } from './legal-issues/legal-issues-view/legal-issues-view.component';
import { TicketBookingViewComponent } from './ticket-booking/ticket-booking-view/ticket-booking-view.component';
import { AdministrativeDecisionViewComponent } from './administrative-decision/administrative-decision-view/administrative-decision-view.component';
import { AttentionViewComponent } from './attention/attention-view/attention-view.component';
import { AppointmentLetterViewComponent } from './appointment-letter/appointment-letter-view/appointment-letter-view.component';
import { CustodyReceiptViewComponent } from './custody-receipt/custody-receipt-view/custody-receipt-view.component';
import { HealthInsuranceViewComponent } from './health-insurance/health-insurance-view/health-insurance-view.component';
import { EntitlementsViewComponent } from './entitlements/entitlements-view/entitlements-view.component';
import { CertificateViewComponent } from './certificate/certificate-view/certificate-view.component';
import { ResignationViewComponent } from './resignation/resignation-view/resignation-view.component';
import { WarningViewComponent } from './warning/warning-view/warning-view.component';
import { MedicalInspectionLetterViewComponent } from './medical-inspection-letter/medical-inspection-letter-view/medical-inspection-letter-view.component';
import { ContractsViewComponent } from './contracts/contracts-view/contracts-view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'receipt_transactions',
    pathMatch: 'full',
  },
  {
    path: 'receipt_transactions_view',
    component: ReceiptTransactionsViewComponent,
  },
  {
    path: 'receipt_transactions_view/:id',
    component: ReceiptTransactionsViewComponent,
  },
  {
    path: 'receipt_transactions',
    component: ReceiptTransactionsComponent,
  },
  {
    path: 'secondment_contract_view',
    component: SecondmentContractViewComponent,
  },
  {
    path: 'secondment_contract_view/:id',
    component: SecondmentContractViewComponent,
  },
  {
    path: 'secondment_contract',
    component: SecondmentContractComponent,
  },
  {
    path: 'assignment_view',
    component: AssignmentViewComponent,
  },
  {
    path: 'assignment_view/:id',
    component: AssignmentViewComponent,
  },
  {
    path: 'assignment',
    component: AssignmentComponent,
  },
  {
    path: 'legal_issues_view',
    component: LegalIssuesViewComponent,
  },
  {
    path: 'legal_issues_view/:id',
    component: LegalIssuesViewComponent,
  },
  {
    path: 'legal_issues',
    component: LegalIssuesComponent,
  },
  {
    path: 'ticket_booking_view',
    component: TicketBookingViewComponent,
  },
  {
    path: 'ticket_booking_view/:id',
    component: TicketBookingViewComponent,
  },
  {
    path: 'ticket_booking',
    component: TicketBookingComponent,
  },
  {
    path: 'administrative_decision_view',
    component: AdministrativeDecisionViewComponent,
  },
  {
    path: 'administrative_decision_view/:id',
    component: AdministrativeDecisionViewComponent,
  },
  {
    path: 'administrative_decision',
    component: AdministrativeDecisionComponent,
  },
  {
    path: 'attention_view',
    component: AttentionViewComponent,
  },
  {
    path: 'attention_view/:id',
    component: AttentionViewComponent,
  },
  {
    path: 'attention',
    component: AttentionComponent,
  },
  {
    path: 'appointment_letter_view',
    component: AppointmentLetterViewComponent,
  },
  {
    path: 'appointment_letter_view/:id',
    component: AppointmentLetterViewComponent,
  },
  {
    path: 'appointment_letter',
    component: AppointmentLetterComponent,
  },
  {
    path: 'custody_receipt_view',
    component: CustodyReceiptViewComponent,
  },
  {
    path: 'custody_receipt_view/:id',
    component: CustodyReceiptViewComponent,
  },
  {
    path: 'custody_receipt',
    component: CustodyReceiptComponent,
  },
  {
    path: 'health_insurance_view',
    component: HealthInsuranceViewComponent,
  },
  {
    path: 'health_insurance_view/:id',
    component: HealthInsuranceViewComponent,
  },
  {
    path: 'health_insurance',
    component: HealthInsuranceComponent,
  },
  {
    path: 'entitlements_view',
    component: EntitlementsViewComponent,
  },
  {
    path: 'entitlements_view/:id',
    component: EntitlementsViewComponent,
  },
  {
    path: 'entitlements',
    component: EntitlementsComponent,
  },
  {
    path: 'certificate_view',
    component: CertificateViewComponent,
  },
  {
    path: 'certificate_view/:id',
    component: CertificateViewComponent,
  },
  {
    path: 'certificate',
    component: CertificateComponent,
  },
  {
    path: 'resignation_view',
    component: ResignationViewComponent,
  },
  {
    path: 'resignation_view/:id',
    component: ResignationViewComponent,
  },
  {
    path: 'resignation',
    component: ResignationComponent,
  },
  {
    path: 'warning_view',
    component: WarningViewComponent,
  },
  {
    path: 'warning_view/:id',
    component: WarningViewComponent,
  },
  {
    path: 'warning',
    component: WarningComponent,
  },
  {
    path: 'medical_inspection_letter_view',
    component: MedicalInspectionLetterViewComponent,
  },
  {
    path: 'medical_inspection_letter_view/:id',
    component: MedicalInspectionLetterViewComponent,
  },
  {
    path: 'medical_inspection_letter',
    component: MedicalInspectionLetterComponent,
  },
  {
    path: 'hr_forms',
    component: HrFormsComponent,
  },
  {
    path: 'contracts_view',
    component: ContractsViewComponent,
  },
  {
    path: 'contracts_view/:id',
    component: ContractsViewComponent,
  },
  {
    path: 'contracts',
    component: ContractsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrFormsRoutingModule {}
