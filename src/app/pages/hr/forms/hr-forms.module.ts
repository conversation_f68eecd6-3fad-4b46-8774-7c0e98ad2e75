import { NgModule } from '@angular/core';

import { AdministrativeDecisionComponent } from './administrative-decision/administrative-decision.component';
import { AppointmentLetterComponent } from './appointment-letter/appointment-letter.component';
import { AssignmentComponent } from './assignment/assignment.component';
import { AttentionComponent } from './attention/attention.component';
import { CertificateComponent } from './certificate/certificate.component';
import { CustodyReceiptComponent } from './custody-receipt/custody-receipt.component';
import { EntitlementsComponent } from './entitlements/entitlements.component';
import { HealthInsuranceComponent } from './health-insurance/health-insurance.component';
import { HrFormsComponent } from './hr-forms/hr-forms.component';
import { LegalIssuesComponent } from './legal-issues/legal-issues.component';
import { MedicalInspectionLetterComponent } from './medical-inspection-letter/medical-inspection-letter.component';
import { ReceiptTransactionsComponent } from './receipt-transactions/receipt-transactions.component';
import { ResignationComponent } from './resignation/resignation.component';
import { SecondmentContractComponent } from './secondment-contract/secondment-contract.component';
import { TicketBookingComponent } from './ticket-booking/ticket-booking.component';
import { WarningComponent } from './warning/warning.component';
import { HrFormsRoutingModule } from './hr-forms-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { ReceiptTransactionsViewComponent } from './receipt-transactions/receipt-transactions-view/receipt-transactions-view.component';
import { SecondmentContractViewComponent } from './secondment-contract/secondment-contract-view/secondment-contract-view.component';
import { AssignmentViewComponent } from './assignment/assignment-view/assignment-view.component';
import { LegalIssuesViewComponent } from './legal-issues/legal-issues-view/legal-issues-view.component';
import { TicketBookingViewComponent } from './ticket-booking/ticket-booking-view/ticket-booking-view.component';
import { AdministrativeDecisionViewComponent } from './administrative-decision/administrative-decision-view/administrative-decision-view.component';
import { AttentionViewComponent } from './attention/attention-view/attention-view.component';
import { AppointmentLetterViewComponent } from './appointment-letter/appointment-letter-view/appointment-letter-view.component';
import { CustodyReceiptViewComponent } from './custody-receipt/custody-receipt-view/custody-receipt-view.component';
import { HealthInsuranceViewComponent } from './health-insurance/health-insurance-view/health-insurance-view.component';
import { EntitlementsViewComponent } from './entitlements/entitlements-view/entitlements-view.component';
import { CertificateViewComponent } from './certificate/certificate-view/certificate-view.component';
import { ResignationViewComponent } from './resignation/resignation-view/resignation-view.component';
import { WarningViewComponent } from './warning/warning-view/warning-view.component';
import { MedicalInspectionLetterViewComponent } from './medical-inspection-letter/medical-inspection-letter-view/medical-inspection-letter-view.component';
import { ContractsViewComponent } from './contracts/contracts-view/contracts-view.component';
import { ContractsComponent } from './contracts/contracts.component';
import { TranslateModule } from '@ngx-translate/core';
import { NgSelectModule } from '@ng-select/ng-select';

@NgModule({
  declarations: [
    ReceiptTransactionsComponent,
    SecondmentContractComponent,
    AssignmentComponent,
    LegalIssuesComponent,
    TicketBookingComponent,
    AdministrativeDecisionComponent,
    AttentionComponent,
    AppointmentLetterComponent,
    CustodyReceiptComponent,
    HealthInsuranceComponent,
    EntitlementsComponent,
    CertificateComponent,
    ResignationComponent,
    WarningComponent,
    ContractsComponent,
    MedicalInspectionLetterComponent,
    HrFormsComponent,
    ReceiptTransactionsViewComponent,
    SecondmentContractViewComponent,
    AssignmentViewComponent,
    LegalIssuesViewComponent,
    TicketBookingViewComponent,
    AdministrativeDecisionViewComponent,
    AttentionViewComponent,
    AppointmentLetterViewComponent,
    CustodyReceiptViewComponent,
    HealthInsuranceViewComponent,
    EntitlementsViewComponent,
    CertificateViewComponent,
    ResignationViewComponent,
    WarningViewComponent,
    MedicalInspectionLetterViewComponent,
    ContractsViewComponent,
  ],
  imports: [
    HrFormsRoutingModule,
    SharedModule,
    TranslateModule,
    NgSelectModule,
  ],
  exports: [RouterModule],
})
export class HrFormsModule {}
