<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.AppointmentLetter" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-3 col-md-4 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="job" class="form-label">
                {{ "COMMON.Job" | translate }}
              </label>
              <input
                id="job"
                type="text"
                formControlName="job"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="department" class="form-label">
                {{ "COMMON.Department" | translate }}
              </label>
              <input
                id="department"
                type="text"
                formControlName="department"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="branch" class="form-label">
                {{ "COMMON.Branch" | translate }}
              </label>
              <input
                id="branch"
                type="text"
                formControlName="branch"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="nationality" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <input
                id="nationality"
                type="text"
                formControlName="nationality"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="qualification" class="form-label">
                {{ "COMMON.Qualification" | translate }}
              </label>
              <input
                id="qualification"
                type="text"
                formControlName="qualification"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="specialization" class="form-label">
                {{ "COMMON.Specialization" | translate }}
              </label>
              <input
                id="specialization"
                type="text"
                formControlName="specialization"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="graduationYear" class="form-label">
                {{ "COMMON.GraduationYear" | translate }}
              </label>
              <input
                id="graduationYear"
                type="text"
                formControlName="graduationYear"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="grade" class="form-label">
                {{ "COMMON.Grade" | translate }}
              </label>
              <input
                id="grade"
                type="text"
                formControlName="grade"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="graduationDestination" class="form-label">
                {{ "COMMON.GraduationDestination" | translate }}
              </label>
              <input
                id="graduationDestination"
                type="text"
                formControlName="graduationDestination"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="jobPreviously" class="form-label">
                {{ "COMMON.JobPreviously" | translate }}
              </label>
              <input
                id="jobPreviously"
                type="text"
                formControlName="jobPreviously"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="experienceYears" class="form-label">
                {{ "COMMON.ExperienceYears" | translate }}
              </label>
              <input
                id="experienceYears"
                type="number"
                formControlName="experienceYears"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="reasonForLeavingWork" class="form-label">
                {{ "COMMON.ReasonForLeavingWork" | translate }}
              </label>
              <input
                id="reasonForLeavingWork"
                type="text"
                formControlName="reasonForLeavingWork"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="temporaryForPeriod" class="form-label">
                {{ "COMMON.TemporaryForPeriod" | translate }}
              </label>
              <input
                id="temporaryForPeriod"
                type="text"
                formControlName="temporaryForPeriod"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="initialKnowledgeForPeriod" class="form-label">
                {{ "COMMON.InitialKnowledgeForPeriod" | translate }}
              </label>
              <input
                id="initialKnowledgeForPeriod"
                type="text"
                formControlName="initialKnowledgeForPeriod"
                class="form-control"
                style="background-color: inherit"
              />
            </div>

            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="trainingForPeriod" class="form-label">
                {{ "COMMON.TrainingForPeriod" | translate }}
              </label>
              <input
                id="trainingForPeriod"
                type="text"
                formControlName="trainingForPeriod"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="notes" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                id="notes"
                type="text"
                formControlName="notes"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
