<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.NewContract" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-3 col-md-4 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="employeeId" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <ng-select
                id="employeeId"
                formControlName="employeeId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="employees"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="job" class="form-label">
                {{ "COMMON.Job" | translate }}
              </label>
              <input
                id="job"
                type="text"
                formControlName="job"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="nationality" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <input
                id="nationality"
                type="text"
                formControlName="nationality"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="idIqama" class="form-label">
                {{ "COMMON.IdIqama" | translate }}
              </label>
              <input
                id="idIqama"
                type="text"
                formControlName="idIqama"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="issuedBy" class="form-label">
                {{ "COMMON.IssuedBy" | translate }}
              </label>
              <input
                id="issuedBy"
                type="text"
                formControlName="issuedBy"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label class="form-label">{{
                "COMMON.EXPDate" | translate
              }}</label>
              <input
                id="expDate"
                type="date"
                formControlName="expDate"
                class="form-control"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="salary" class="form-label">
                {{ "COMMON.Salary" | translate }}
              </label>
              <input
                id="salary"
                type="text"
                formControlName="salary"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="housingAllowance" class="form-label">
                {{ "COMMON.HousingAllowance" | translate }}
              </label>
              <input
                id="housingAllowance"
                type="text"
                formControlName="housingAllowance"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="transportationAllowance" class="form-label">
                {{ "COMMON.TransportationAllowance" | translate }}
              </label>
              <input
                id="transportationAllowance"
                type="text"
                formControlName="transportationAllowance"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="empWorkTime" class="form-label">
                {{ "COMMON.EmpWorkTime" | translate }}
              </label>
              <input
                id="empWorkTime"
                type="text"
                formControlName="empWorkTime"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="ClosePerson1" class="form-label">
                {{ "COMMON.ClosePerson" | translate }}
              </label>
              <input
                id="ClosePerson1"
                type="text"
                formControlName="ClosePerson1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="relationship1" class="form-label">
                {{ "COMMON.Relationship" | translate }}
              </label>
              <input
                id="relationship1"
                type="text"
                formControlName="relationship1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="phone1" class="form-label">
                {{ "COMMON.Phone" | translate }}
              </label>
              <input
                id="phone1"
                type="text"
                formControlName="phone1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="ClosePerson2" class="form-label">
                {{ "COMMON.ClosePerson" | translate }}
              </label>
              <input
                id="ClosePerson2"
                type="text"
                formControlName="ClosePerson2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="relationship2" class="form-label">
                {{ "COMMON.Relationship" | translate }}
              </label>
              <input
                id="relationship2"
                type="text"
                formControlName="relationship2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="phone2" class="form-label">
                {{ "COMMON.Phone" | translate }}
              </label>
              <input
                id="phone2"
                type="text"
                formControlName="phone2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="sex" class="form-label">
                {{ "COMMON.Sex" | translate }}
              </label>
              <ng-select
                id="sex"
                formControlName="sex"
                bindLabel="nameAr"
                bindValue="id"
                [items]="sexes"
              ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-3 col-md-6 col-sm-12">
              <label for="contractDuration" class="form-label">
                {{ "COMMON.ContractDuration" | translate }}
              </label>
              <input
                id="contractDuration"
                type="text"
                formControlName="contractDuration"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-2 col-md-4 col-sm-12">
              <label class="form-label">{{
                "COMMON.FromDate" | translate
              }}</label>
              <input
                id="fromDate"
                type="date"
                formControlName="fromDateValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-2 col-md-4 col-sm-12">
              <label class="form-label">{{
                "COMMON.ToDate" | translate
              }}</label>
              <input
                id="toDate"
                type="date"
                formControlName="toDateValue"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="workplace" class="form-label">
                {{ "COMMON.Workplace" | translate }}
              </label>
              <input
                id="workplace"
                type="text"
                formControlName="workplace"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
