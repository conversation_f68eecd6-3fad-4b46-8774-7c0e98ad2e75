<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">
        {{ "COMMON.SecondmentContract" | translate }}
      </h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="uploadExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ImportFromExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'HR.Employees'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <!-- سطر التاريخ والطرف الأول -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label class="form-label">{{ "COMMON.Date" | translate }}</label>
              <input
                id="dateValue"
                type="date"
                formControlName="dateValue"
                class="form-control"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="firstParty" class="form-label">
                {{ "COMMON.FirstParty" | translate }}
              </label>
              <input
                id="firstParty"
                type="text"
                formControlName="firstParty"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر بيانات الطرف الأول -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="commercialRegistration1" class="form-label">
                {{ "COMMON.CommercialRegistration" | translate }} ({{ "COMMON.FirstParty" | translate }})
              </label>
              <input
                id="commercialRegistration1"
                type="text"
                formControlName="commercialRegistration1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="issuedBy1" class="form-label">
                {{ "COMMON.IssuedBy" | translate }} ({{ "COMMON.FirstParty" | translate }})
              </label>
              <input
                id="issuedBy1"
                type="text"
                formControlName="issuedBy1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر ممثل الطرف الأول والطرف الثاني -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="representedBy1" class="form-label">
                {{ "COMMON.RepresentedBy" | translate }} ({{ "COMMON.FirstParty" | translate }})
              </label>
              <input
                id="representedBy1"
                type="text"
                formControlName="representedBy1"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="secondParty" class="form-label">
                {{ "COMMON.SecondParty" | translate }}
              </label>
              <input
                id="secondParty"
                type="text"
                formControlName="secondParty"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر بيانات الطرف الثاني -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="commercialRegistration2" class="form-label">
                {{ "COMMON.CommercialRegistration" | translate }} ({{ "COMMON.SecondParty" | translate }})
              </label>
              <input
                id="commercialRegistration2"
                type="text"
                formControlName="commercialRegistration2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="issuedBy2" class="form-label">
                {{ "COMMON.IssuedBy" | translate }} ({{ "COMMON.SecondParty" | translate }})
              </label>
              <input
                id="issuedBy2"
                type="text"
                formControlName="issuedBy2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر ممثل الطرف الثاني واسم الموظف -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="representedBy2" class="form-label">
                {{ "COMMON.RepresentedBy" | translate }} ({{ "COMMON.SecondParty" | translate }})
              </label>
              <input
                id="representedBy2"
                type="text"
                formControlName="representedBy2"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="employeeName" class="form-label">
                {{ "COMMON.EmployeeName" | translate }}
              </label>
              <input
                id="employeeName"
                type="text"
                formControlName="employeeName"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الجنسية ومحل الإقامة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="nationality" class="form-label">
                {{ "COMMON.Nationality" | translate }}
              </label>
              <input
                id="nationality"
                type="text"
                formControlName="nationality"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="residenceText" class="form-label">
                {{ "COMMON.ResidenceText" | translate }}
              </label>
              <input
                id="residenceText"
                type="text"
                formControlName="residenceText"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>

          <!-- سطر الوظيفة والمدة -->
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="job" class="form-label">
                {{ "COMMON.Job" | translate }}
              </label>
              <input
                id="job"
                type="text"
                formControlName="job"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="period" class="form-label">
                {{ "COMMON.Period" | translate }}
              </label>
              <input
                id="period"
                type="text"
                formControlName="period"
                class="form-control"
                style="background-color: inherit"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
