<!-- System Overview -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Quick Stats -->
  <div class="col-xl-12">
    <div class="card card-flush mb-5">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.SystemOverview" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.SystemStats" | translate }}</span>
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Active Modules Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="activeModules">13</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.ActiveModules" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-primary">
            <i class="fa fa-cubes text-primary fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Users Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalUsers">{{ data?.totalUsers || 0 }}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TotalUsers" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-success">
            <i class="fa fa-users text-success fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Transactions Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalTransactions">{{ data?.transactionsCount || 0 }}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.TotalTransactions" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-warning">
            <i class="fa fa-exchange-alt text-warning fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- System Performance Card -->
  <div class="col-md-6 col-lg-6 col-xl-3">
    <div class="card card-flush h-md-50 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="systemPerformance">98%</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ "COMMON.SystemPerformance" | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <div class="symbol symbol-50px me-2">
          <span class="symbol-label bg-light-info">
            <i class="fa fa-tachometer-alt text-info fs-2x"></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Charts Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Module Usage Chart -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.ModuleUsage" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.Last30Days" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <app-new-charts-widget8 chartHeight="275px" [chartHeightNumber]="275"></app-new-charts-widget8>
      </div>
    </div>
  </div>

  <!-- System Alerts -->
  <div class="col-xl-6">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.SystemAlerts" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.Last30Days" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body">
        <app-cards-widget18 image="./assets/media/stock/600x600/img-65.jpg"></app-cards-widget18>
      </div>
    </div>
  </div>
</div>

<!-- Recent Transactions and Quick Links -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Recent Transactions -->
  <div class="col-xl-8">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.RecentTransactions" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.Last30Days" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body">
        <app-tables-widget10 class="card-xl-stretch"></app-tables-widget10>
      </div>
    </div>
  </div>

  <!-- Quick Links -->
  <div class="col-xl-4">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.QuickLinks" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.CommonOperations" | translate }}</span>
        </h3>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget2 class="card-xl-stretch"></app-lists-widget2>
      </div>
    </div>
  </div>
</div>
<!-- Module Performance -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <div class="col-xl-12">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.ModulePerformance" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.SystemModules" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewDetails" | translate }}</button>
        </div>
      </div>
      <div class="card-body">
        <div class="row g-5 g-xl-8">
          <div class="col-xl-4">
            <app-mixed-widget8 chartColor="success" chartHeight="150px"></app-mixed-widget8>
          </div>
          <div class="col-xl-4">
            <app-lists-widget6 class="card-xl-stretch"></app-lists-widget6>
          </div>
          <div class="col-xl-4">
            <app-lists-widget4 class="card-xl-stretch"></app-lists-widget4>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- System Activity -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <div class="col-xl-12">
    <div class="card card-flush h-md-100 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ "COMMON.UserActivity" | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ "COMMON.TopActiveUsers" | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ "COMMON.ViewAll" | translate }}</button>
        </div>
      </div>
      <div class="card-body">
        <app-tables-widget5 class="card-xl-stretch"></app-tables-widget5>
      </div>
    </div>
  </div>
</div>

