import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class DashboardService {
  baseUrl: string;

  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.statistics}`;
  }

  /**
   * Get dashboard data from API
   */
  getDashboardData(): Observable<any> {
    return this.http.get<any>(this.baseUrl);
  }

  /**
   * Get system performance metrics
   */
  getSystemPerformance(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/performance`);
  }

  /**
   * Get module usage statistics
   */
  getModuleUsage(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/module-usage`);
  }

  /**
   * Get recent system transactions
   */
  getRecentTransactions(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/recent-transactions`);
  }

  /**
   * Get system alerts
   */
  getSystemAlerts(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/alerts`);
  }

  /**
   * Get user activity data
   */
  getUserActivity(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/user-activity`);
  }
}
