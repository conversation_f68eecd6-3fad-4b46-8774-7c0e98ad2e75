import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DashboardService } from './dashboard.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: false
})
export class DashboardComponent implements OnInit, OnDestroy {
  data: any = {};
  private subscriptions: Subscription = new Subscription();

  constructor(
    private dashboardService: DashboardService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
    this.updateStaticData();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Load dashboard data from service
   */
  loadDashboardData(): void {
    this.subscriptions.add(
      this.dashboardService.getDashboardData().subscribe(
        (res) => {
          if (res.success) {
            this.data = res.data;
            this.updateDynamicData();
            this.cdr.detectChanges();
          }
        }
      )
    );
  }

  /**
   * Update static dashboard data
   */
  updateStaticData(): void {
    this.updateDOMElement('activeModules', '13');
    this.updateDOMElement('systemPerformance', '98%');
  }

  /**
   * Update dynamic dashboard data from API response
   */
  updateDynamicData(): void {
    if (this.data) {
      this.updateDOMElement('totalUsers', this.data.totalUsers?.toString() || '0');
      this.updateDOMElement('totalTransactions', this.data.transactionsCount?.toString() || '0');
    }
  }

  /**
   * Update DOM element with value
   */
  private updateDOMElement(elementId: string, value: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * Format number as percentage
   */
  formatNumber(number: any): string {
    if (Number.isInteger(number)) {
      return number + '%';
    } else {
      return parseFloat(number).toFixed(2) + '%';
    }
  }
}
