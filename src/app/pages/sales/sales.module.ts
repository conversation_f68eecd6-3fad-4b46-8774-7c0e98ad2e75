import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {salesRoutingModule} from './sales-routing.module';
import {salesHomeComponent} from './sales-home/sales-home.component';
import {SharedModule} from '../shared/shared.module';
import {ProductsGridControleModule} from '../general/products-grid-controle/ProductsGridControle.module';

@NgModule({
    declarations: [
        salesHomeComponent,
    ],
    imports: [
        salesRoutingModule,
        SharedModule,
        ProductsGridControleModule
    ],
    exports: [RouterModule],
})

export class SalesModule {
}
