import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {salesHomeComponent} from './sales-home/sales-home.component';


const routes: Routes = [

    {
        path: '',
        component: salesHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () =>
            import('./configuration/sales-configuration.module').then((m) => m.SalesConfigurationModule),
    },
    {
        path: 'cars_showrooms',
        loadChildren: () =>
            import('./cars-showrooms/sales-cars-showrooms.module').then((m) => m.SalesCarsShowroomsModule),
    },
    {
        path: 'transportation',
        loadChildren: () =>
            import('./transportation/sales-transportation.module').then((m) => m.SalesTransportationModule),
    },
    {
        path: 'sales',
        loadChildren: () =>
            import('./sales/sales-sales.module').then((m) => m.SalesSalesModule),
    },
    {
        path: 'invoice',
        loadChildren: () =>
            import('./sales-invoice/sales-invoice.module').then((m) => m.SalesInvoiceModule),
    },
    {
        path: 'reports',
        loadChildren: () =>
            import('./reports/sales-reports.module').then((m) => m.SalesReportsModule),
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class salesRoutingModule {
}
