
<form [formGroup]="newdata"> 

<div class="card mb-5 mb-xl-10">

  <div class="card-header border-0 cursor-pointer w-100">

    <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
      <h3 class="fw-bolder m-0">{{ "COMMON.NewServiceSalesInvoiceReturns" | translate }}</h3>
 
      <div [style.position]="'relative'">
     <div class="btn-group">


     <button
       type="submit"
       (click)="discard()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.Cancel" | translate }}
       <i class="fa fa-close"></i>
     </button>
     <button
       type="submit"
       (click)="save()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.SaveChanges" | translate }}
       <i class="fa fa-save"></i>
     </button>
   </div>
   <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
             (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
   <button
     class="btn btn-icon btn-active-light-primary mx-2"
     (click)="toggleMenu()"
     data-bs-toggle="tooltip"
     data-bs-placement="top"
     data-bs-trigger="hover"
     title="Settings"
   >
     <i class="fa fa-gear"></i>
   </button>
   <div
     class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
     [class.show]="menuOpen"
     [style.position]="'absolute'"
     [style.top]="'100%'"
     [style.zIndex]="'1050'"
     [style.left]="isRtl ? '0' : 'auto'"
     [style.right]="!isRtl ? '0' : 'auto'"
   >
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         (click)="exportToExcel()"
         data-kt-company-table-filter="delete_row"
       >
         {{ "COMMON.ExportToExcel" | translate }}
       </a>
     </div>
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openSmsModal()"
       >
         {{ "COMMON.SendViaSMS" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openEmailModal()"
       >
         {{ "COMMON.SendViaEmail" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openWhatsappModal()"
       >
         {{ "COMMON.SendViaWhatsapp" | translate }}
       </a>
     </div>

     <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'Salse.ServiceSalesInvoiceReturns'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses"
         data-kt-company-table-filter="delete_row"
         >{{ "COMMON.Print" | translate }}</a
       >
     </div>
     <!-- <div
     class="menu-item px-3"
     *hasPermission="{
       action: 'printAction',
       module: 'HR.Employees'
     }"
   >
     <a
       class="menu-link px-3"
       target="_blank"
       href="/reports/warehouses?withLogo=true"
       data-kt-company-table-filter="delete_row"
       >Print With Logo</a
     >
   </div> -->
   </div>
 </div>

</div>

  </div>

  <div class="card-body border-top p-9">


    <div class="main-inputs ">

        <div class="row ">


            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label class="form-label">{{ "COMMON.InvoiceDate" | translate }}</label>
              <input
                id="invoiceDate"
                type="date"
                formControlName="invoiceDate"
                class="form-control"
              />
            </div>

            <div class="form-group col-xl-4 col-md-4 col-sm-12  ">
              <label for="customer" class="form-label">
                {{ "COMMON.Customer" | translate }}
              </label>
              <ng-select
                id="customer"
                formControlName="customerId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="customers"
              ></ng-select>
            </div>

            
        
        </div>

        <div class="row ">


          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="SalesPersonid" class="form-label">
              {{ "COMMON.Salesperson" | translate }}
            </label>
            <ng-select
              id="SalesPersonid"
              formControlName="SalesPersonid"
              bindLabel="nameAr"
              bindValue="id"
              [items]="SalesPersons"
            ></ng-select>
          </div>



          <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label for="branch" class="form-label">
                {{ "COMMON.Branch" | translate }}
              </label>
              <ng-select
                      id="branch"
                      formControlName="branch"
                      bindLabel="name"
                      bindValue="id"
                      [items]="branches"></ng-select>
          </div>
          
      
      </div>

      <div class="row ">

        <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-3">
          <label for="journal" class="form-label">
            {{ "COMMON.JournalName" | translate }}
          </label>
          <ng-select
                  id="journal"
                  formControlName="journal"
                  bindLabel="name"
                  bindValue="id"
                  [items]="Journals"></ng-select>
      </div>

        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
            <label for="salesType" class="form-label">
              {{ "COMMON.SalesType" | translate }}
            </label>
            <ng-select
                    id="salesType"
                    formControlName="salesType"
                    bindLabel="nameAr"
                    bindValue="id"
                    [items]="Salestypes"></ng-select>
        </div>
        
    
    </div>

    <div class="row ">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="currencyid" class="form-label">
          {{ "COMMON.Currency" | translate }}
        </label>
        <ng-select
          id="currencyid"
          formControlName="currencyId"
          bindLabel="nameAr"
          bindValue="id"
          [items]="currency"
        ></ng-select>
      </div>
  
  </div>

    </div>



<div class="row">

  <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
    <li class="nav-item" role="presentation">
      <button
        class="nav-link"
        [class.active]="activeTab === 'tab1'"
        (click)="setActiveTab('tab1')"
      >
        {{ "COMMON.InvoiceLines" | translate }}
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button
        class="nav-link"
        [class.active]="activeTab === 'tab2'"
        (click)="setActiveTab('tab2')"
      >
        {{ "COMMON.JournalItems" | translate }}
      </button>
    </li>

    <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [class.active]="activeTab === 'tab3'"
          (click)="setActiveTab('tab3')"
        >
          {{ "COMMON.OtherInfo" | translate }}
        </button>
      </li>

  </ul>



  <div class="tab-content" id="myTabContent">


<!-- Tab 1 -->
<div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab1'"
              [class.active]="activeTab === 'tab1'"
              *ngIf="activeTab === 'tab1'"
            >
                                    <!--grid controle-->
                                    <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
            <div class="row">
              <div class="row">
  
                  <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Total</div>
                      <input type="number" [value]="total" class="form-control" disabled/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount %</div>
                      <input type="number" [(ngModel)]="discountPerc" (change)="discountPercChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount</div>
                      <input type="number" [(ngModel)]="discount" (change)="discountChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Vat</div>
                      <input type="number" [(ngModel)]="vat" (change)="onVatChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax %</div>
                      <input type="number" [(ngModel)]="taxPerc" (change)="onTaxPercChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax</div>
                      <input type="number" [(ngModel)]="tax" (change)="onTaxChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">shaping</div>
                      <input type="number" [(ngModel)]="shaping" (change)="onShapingChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
              </div>
                  <div class="row">
  
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                      <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Net</div>
                      <input type="number" [(ngModel)]="net" class="form-control" disabled [ngModelOptions]="{standalone: true}"/>
                  </div>
  
              </div>
    
    
    
            </div>
    
            
              
 </div>
 
<!-- Tab 2 -->
<div
  class="tab-pane fade"
  [class.show]="activeTab === 'tab2'"
 [class.active]="activeTab === 'tab2'"
*ngIf="activeTab === 'tab2'"
 >

<div class="row">
  
  <div class="form-group col-xl-4 col-md-3 col-sm-12 ">
    <label class="label">   {{ "COMMON.InvoiceNo" | translate }}</label>
    <input
    type="text"
    id="invoiceno"
    name="invoiceno"
    class="form-control"
    formControlName="invoiceno"
  />
</div>
 
  </div>

  

</div>

<!-- Tab 3 -->
<div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >

          <div class="row">

            <div class="form-group col-xl-12 col-md-3 col-sm-12">
              <label class="form-label" style=" font-size: 18px;font-weight: bold; ">{{ "COMMON.Notes" | translate }}</label>
              <input
              type="text"
              id="notes"
              name="notes"
              class="form-control"
              formControlName="notes"
            />
              
            </div>

          </div>


</div>


  </div>

</div>



  </div>




</div>

</form>