import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SalesInvoiceComponent } from './sales-invoice/sales-invoice.component';
import { SalesReturnsComponent } from './sales-returns/sales-returns.component';
import { SalesTaxReturnsComponent } from './sales-tax-returns/sales-tax-returns.component';
import { ServiceSalesInvoiceComponent } from './service-sales-invoice/service-sales-invoice.component';
import { ServiceSalesTaxInvoiceReturnsComponent } from './service-sales-tax-invoice-returns/service-sales-tax-invoice-returns.component';
import { ServiceSalesTaxInvoiceComponent } from './service-sales-tax-invoice/service-sales-tax-invoice.component';
import { TaxInvoiceListComponent } from './taxinvoice/taxInvoice-list/taxInvoice-list.component';
import { TaxInvoiceComponent } from './taxinvoice/taxinvoice/taxinvoice.component';
import { Sales_tax_returns_viewComponent } from './sales-tax-returns/sales_tax_returns_view/sales_tax_returns_view.component';
import { Service_sales_tax_invoice_viewComponent } from './service-sales-tax-invoice/service_sales_tax_invoice_view/service_sales_tax_invoice_view.component';
import { Service_sales_tax_invoice_returns_viewComponent } from './service-sales-tax-invoice-returns/service_sales_tax_invoice_returns_view/service_sales_tax_invoice_returns_view.component';
import { Sales_invoice_viewComponent } from './sales-invoice/sales_invoice_view/sales_invoice_view.component';
import { Sales_returns_viewComponent } from './sales-returns/sales_returns_view/sales_returns_view.component';
import { Service_sales_invoice_viewComponent } from './service-sales-invoice/service_sales_invoice_view/service_sales_invoice_view.component';
import { Service_sales_invoice_returnsComponent } from './service_sales_invoice_returns/service_sales_invoice_returns.component';
import { Service_sales_invoice_returns_viewComponent } from './service_sales_invoice_returns/service_sales_invoice_returns_view/service_sales_invoice_returns_view.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'taxinvoice-list',
    pathMatch: 'full',
  },
  {
    path: 'taxinvoice-list',
    component: TaxInvoiceListComponent,
  },
  {
    path: 'taxInvoice-list',
    component: TaxInvoiceListComponent,
  },
  {
    path: 'taxInvoice',
    component: TaxInvoiceComponent,
  },
  {
    path: 'taxInvoice/:id',
    component: TaxInvoiceComponent,
  },
  {
    path: 'sales_tax_returns_view',
    component: Sales_tax_returns_viewComponent,
  },
  {
    path: 'sales_tax_returns_view/:id',
    component: Sales_tax_returns_viewComponent,
  },

  {
    path: 'service_sales_tax_invoice_view',
    component: Service_sales_tax_invoice_viewComponent,
  },
  {
    path: 'service_sales_tax_invoice_view/:id',
    component: Service_sales_tax_invoice_viewComponent,
  },
  {
    path: 'service_sales_tax_invoice_returns_view',
    component: Service_sales_tax_invoice_returns_viewComponent,
  },
  {
    path: 'service_sales_tax_invoice_returns_view/:id',
    component: Service_sales_tax_invoice_returns_viewComponent,
  },
  {
    path: 'sales_invoice_view',
    component: Sales_invoice_viewComponent,
  },
  {
    path: 'sales_invoice_view/:id',
    component: Sales_invoice_viewComponent,
  },

  {
    path: 'sales_returns_view',
    component: Sales_returns_viewComponent,
  },
  {
    path: 'sales_returns_view/:id',
    component: Sales_returns_viewComponent,
  },
  {
    path: 'service_sales_invoice_view',
    component: Service_sales_invoice_viewComponent,
  },
  {
    path: 'service_sales_invoice_view/:id',
    component: Service_sales_invoice_viewComponent,
  },

  {
    path: 'service_sales_invoice_returns_view',
    component: Service_sales_invoice_returns_viewComponent,
  },
  {
    path: 'service_sales_invoice_returns_view/:id',
    component: Service_sales_invoice_returns_viewComponent,
  },

  {
    path: 'taxinvoice',
    component: TaxInvoiceComponent,
  },
  {
    path: 'sales_tax_returns',
    component: SalesTaxReturnsComponent,
  },
  {
    path: 'service_sales_tax_invoice',
    component: ServiceSalesTaxInvoiceComponent,
  },
  {
    path: 'service_sales_tax_invoice_returns',
    component: ServiceSalesTaxInvoiceReturnsComponent,
  },
  {
    path: 'sales_invoice',
    component: SalesInvoiceComponent,
  },
  {
    path: 'sales_returns',
    component: SalesReturnsComponent,
  },
  {
    path: 'service_sales_invoice',
    component: ServiceSalesInvoiceComponent,
  },
  {
    path: 'service_sales_invoice_returns',
    component: Service_sales_invoice_returnsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SalesInvoiceRoutingModule {}
