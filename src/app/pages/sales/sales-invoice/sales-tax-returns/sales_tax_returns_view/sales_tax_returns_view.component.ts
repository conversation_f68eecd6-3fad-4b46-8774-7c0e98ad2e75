import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { SalesService } from '../../../sales.service';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import jsPDF from 'jspdf';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';



@Component({
  selector: 'app-sales_tax_returns_view',
  templateUrl: './sales_tax_returns_view.component.html',
  styleUrls: ['./sales_tax_returns_view.component.css'],
  standalone:false
})
export class Sales_tax_returns_viewComponent implements OnInit {
  moduleName = 'Salse.SalesTaxReturns';
 
  SalesTaxReturns: FormGroup;
  [x: string]: any;
  // moduleName = ' Salse.SalesTaxReturns';
 Product: any[];
  AnalyticAccounts: any[];
  Units: any[];
  data: any[];
  customers: any[];
  warehouses: any[];
  salesmen: any[];
  Journals: any[];
  drivers: any[];
  shapingCompanys: any[];
  invoiceDate: any;
  newdata:FormGroup;
  dueDate: any;
  Notes: any;
  einvoicestatues: any;
  docno: any;
  deliveryDate: any;
  Salestypes: any[];
  actionType: any[];
  Cashs: any[];
  CashBoxId: number = 0;
  CashId: number = 0;
  bankAccountId: number = 0;
  projectId: number = 0;
  InvoiceTypeId: number = 0;
  Banks: [] = [];
  Invoices: [] = [];
  currencyId: number = 1;
  error: any;
  response: any;
  currency: any[];
  bankid: any;
  isGridBoxOpened: boolean;
  editorOptions: any;
  gridBoxValue: number[] = [1];
  subscription = new Subscription();
  currentFilter: any;
  branches: [] = [];
  total = 0;
  discount = 0;
  discountPerc = 0;
  withoutbreakage: boolean = false;
  vat = 0;
  passedBy=0;
  tax = 0;
  taxPerc = 0;
  shaping = 0;
  net: number | string = 0;
  activeTab: string = 'tab1';


      selectedItemKeys: any = [];
      isRtl: boolean = document.documentElement.dir === 'rtl';
         menuOpen = false;
         toggleMenu() {
           this.menuOpen = !this.menuOpen;
         }
         actionsList: string[] = [
           'Export',
           'Send via SMS',
           'Send Via Email',
           'Send Via Whatsapp',
           'print',
         ];
         modalConfig: ModalConfig = {
           modalTitle: 'Send Sms',
           modalSize: 'lg',
           hideCloseButton(): boolean {
             return true;
           },
           dismissButtonLabel: 'Cancel',
         };
       
         smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
         emailModalConfig: ModalConfig = {
           ...this.modalConfig,
           modalTitle: 'Send Email',
         };
         whatsappModalConfig: ModalConfig = {
           ...this.modalConfig,
           modalTitle: 'Send Whatsapp',
         };
         @ViewChild('smsModal') private smsModal: ModalComponent;
         @ViewChild('emailModal') private emailModal: ModalComponent;
         @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(private service: SalesService,    private fb: FormBuilder,
    private cdk: ChangeDetectorRef,  private router: Router,    private route: ActivatedRoute,) { 

          const today=new Date().toISOString().slice(0, 10);
      
          this.newdata = this.fb.group({
            customer: [null, Validators.required],
            warehouse: [null, Validators.required],
            branch: [null, Validators.required],
            journal: [null, Validators.required],
            salesType: [null, Validators.required],
            total: [null, Validators.required],
            discountPerc: [null, Validators.required],
            discount: [null, Validators.required],
            vat: [null, Validators.required],
            taxPerc: [null, Validators.required],
            tax: [null, Validators.required],
            shaping: [null, Validators.required],
            net: [null, Validators.required],
            currencyid: [null, Validators.required],
            einvoicestatues: [null, Validators.required],
            InvoiceTypeId: [null, Validators.required],
            uuid: [null, Validators.required],
            CashBoxId: [null, Validators.required],
            bankAccountId: [null, Validators.required],
            salesinvoicenumber: [null, Validators.required],
            salesinvoicenumber1: [null, Validators.required],
            salesinvoicenumber2: [null, Validators.required],
            invoiceDate: [today, Validators.required],
            passedBy: [{ value: '', disabled: true }]

            
          });
          




          this.subscription.add(
            service.getCustDropdown().subscribe((r) => {
              if (r.success) {
                this.customers = r.data;
                this.cdk.detectChanges();
              }
            })
          );
      this.subscription.add(service.getwarehouses().subscribe(r => {
        if (r.success) {
          this.warehouses = r.data;
          this.cdk.detectChanges();
        }
      }));
  
      this.subscription.add(
        service.getbranches().subscribe((r) => {
          if (r.success) {
            this.branches = r.data;
  
            this.cdk.detectChanges();
          }
        })
      );
  
      this.subscription.add(
        service.getJournals().subscribe((r) => {
          if (r.success) {
            this.Journals = r.data;
  
            this.cdk.detectChanges();
          }
        })
      );
  
      this.subscription.add(
        service.getSalestypes().subscribe((r) => {
          if (r.success) {
            this.Salestypes = r.data;
  
            this.cdk.detectChanges();
          }
        })
      );

      this.subscription.add(
        service.getCashBoxActionType().subscribe((r) => {
          if (r.success) {
            this.actionType = r.data;
            this.cdk.detectChanges();
          }
        })
      );
  
      this.subscription.add(
        service.getCashBox().subscribe((r) => {
          if (r.success) {
            this.Cashs = r.data;
            this.cdk.detectChanges();
          }
        })
      );

      this.subscription.add(service.getBankAccount().subscribe(r => {
        if (r.success) {
          this.Banks = r.data;
          this.cdk.detectChanges();
  
        }
      }));
      this.subscription.add(service.getBankActionType().subscribe(r => {
        if (r.success) {
          this.actionType = r.data;
          this.cdk.detectChanges();
        }
      }));
      this.subscription.add(service.getCurrency().subscribe(r => {
        if (r.success) {
          this.currency = r.data;
          this.cdk.detectChanges();
        }
      }));


     }

  ngOnInit() {

      this.SalesTaxReturns = new FormGroup({
              invoiceDate: new FormControl(''),
              dueDate: new FormControl(''),
              customer: new FormControl(''),
              deliveryDate: new FormControl(''),
              warehouse: new FormControl(''),
              branch: new FormControl(''),
              journal: new FormControl(''),
              salesType: new FormControl(''),
            });

  }
  sendTaxInvoice() {
    if (!confirm('هل أنت متأكد من إرسال الفاتورة الإلكترونية؟')) {
      return;
    }
  
    this.invoiceService.sendElectronicInvoice(this.invoiceId).subscribe({
      next: (response: any) => {  
        console.log('%cتم إرسال الفاتورة بنجاح:', 'color: green; font-weight: bold;', response);
        alert('تم إرسال الفاتورة الضريبية بنجاح!');
      },
      error: (error: any) => {  
        console.error('%cفشل في إرسال الفاتورة:', 'color: red; font-weight: bold;', error);
        alert('حدث خطأ أثناء إرسال الفاتورة!');
      }
    });
  }

   discard() {
    }
    
  
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          const form = this.newdata.value;
          this.subscriptions.add(
            this.myService
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                if (r.success) {
                  this.router.navigate(['/sales/invoice/sales_tax_returns']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          const form = this.newdata.value;
          this.subscriptions.add(
            this.myService
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                if (r.success) {
                  this.router.navigate(['/sales/invoice/sales_tax_returns']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }
    

  onDateChange() {
    const invoiceDate = new Date(this.SalesTaxReturns.get('invoiceDate')?.value);
    const today = new Date();

    if (invoiceDate) {
      const diffTime = Math.abs(invoiceDate.getTime() - today.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // تحويل الفرق إلى أيام
      this.SalesTaxReturns.get('passedBy')?.setValue(diffDays);
    }
  }

    setActiveTab(tab: string): void {
      this.activeTab = tab;
    }

    onDataUpdate(data: any) {
      this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
      this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    discountChanged(event: any) {
      const value = +event.target.value;
      if(value) {
        this.discountPerc = value * 100/ this.total;
        this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
      }
      this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    discountPercChanged(event: any) {
      const value = +event.target.value;
      if(value) {
        this.discount = this.total * value / 100;
        this.discount = parseFloat(this.discount.toFixed(2));
      }
      this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    onTaxChange(event: any) {
      const value = +event.target.value;
      if(value) {
        this.taxPerc = value * 100 / this.total;
        this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
      }
      this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    onVatChange(event: any) {
      const value = +event.target.value;
      this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    onTaxPercChange(event: any) {
      const value = +event.target.value;
      if(value) {
        this.tax = this.total * value / 100;
        this.tax = parseFloat(this.tax.toFixed(2));
      }
      this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
      this.net = this.net.toFixed(2);
    }
  
    onShapingChange(event: any) {
      const value = +event.target.value;
      this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
      this.net = this.net.toFixed(2);
    }

    exportToExcel() {
      this.subscription.add(this.service.exportExcel()
        .subscribe(e => {
          if (e) {
            const href = URL.createObjectURL(e);
            const link = document.createElement('a');
            link.setAttribute('download', 'SalesTaxReturns.xlsx');
            link.href = href;
            link.click();
            URL.revokeObjectURL(href);
          }
        }));
    }
     onExporting(e: any) {
          console.log(e);
          const workbook = new Workbook();
          const worksheet = workbook.addWorksheet('SalesTaxReturns');
          if (e.format == 'excel') {
            exportDataGrid({
              component: e.component,
              worksheet,
              autoFilterEnabled: true,
            }).then(() => {
              workbook.xlsx.writeBuffer().then((buffer: any) => {
                saveAs(
                  new Blob([buffer], { type: 'application/octet-stream' }),
                  'SalesTaxReturns.xlsx'
                );
              });
            });
          } else if (e.format == 'pdf') {
            const doc = new jsPDF();
            pdfGrid({
              jsPDFDocument: doc,
              component: e.component,
              indent: 5,
            }).then(() => {
              doc.save('SalesTaxReturns.pdf');
            });
          }
          e.cancel = true;
        }
  
  
    openSmsModal() {
      this.smsModal.open();
    }
    openWhatsappModal() {
      this.whatsappModal.open();
    }
    openEmailModal() {
      this.emailModal.open();
    } 

}
