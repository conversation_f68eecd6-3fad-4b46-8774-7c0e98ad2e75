import {NgModule} from '@angular/core';
import {SalesInvoiceComponent} from './sales-invoice/sales-invoice.component';
import {SalesReturnsComponent} from './sales-returns/sales-returns.component';
import {SalesTaxReturnsComponent} from './sales-tax-returns/sales-tax-returns.component';
import {ServiceSalesInvoiceComponent} from './service-sales-invoice/service-sales-invoice.component';
import {
    ServiceSalesTaxInvoiceReturnsComponent
} from './service-sales-tax-invoice-returns/service-sales-tax-invoice-returns.component';
import {ServiceSalesTaxInvoiceComponent} from './service-sales-tax-invoice/service-sales-tax-invoice.component';
import {TaxInvoiceListComponent} from './taxinvoice/taxInvoice-list/taxInvoice-list.component';
import {TaxInvoiceComponent} from './taxinvoice/taxinvoice/taxinvoice.component';
import {SalesInvoiceRoutingModule} from "./sales-invoice-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { Sales_tax_returns_viewComponent } from './sales-tax-returns/sales_tax_returns_view/sales_tax_returns_view.component';
import { Service_sales_tax_invoice_viewComponent } from './service-sales-tax-invoice/service_sales_tax_invoice_view/service_sales_tax_invoice_view.component';
import { Service_sales_tax_invoice_returns_viewComponent } from './service-sales-tax-invoice-returns/service_sales_tax_invoice_returns_view/service_sales_tax_invoice_returns_view.component';
import { Sales_invoice_viewComponent } from './sales-invoice/sales_invoice_view/sales_invoice_view.component';
import { Sales_returns_viewComponent } from './sales-returns/sales_returns_view/sales_returns_view.component';
import { Service_sales_invoice_viewComponent } from './service-sales-invoice/service_sales_invoice_view/service_sales_invoice_view.component';
import { Service_sales_invoice_returnsComponent } from './service_sales_invoice_returns/service_sales_invoice_returns.component';
import { Service_sales_invoice_returns_viewComponent } from './service_sales_invoice_returns/service_sales_invoice_returns_view/service_sales_invoice_returns_view.component';

@NgModule({
    declarations: [
        TaxInvoiceListComponent,
        TaxInvoiceComponent,
        SalesTaxReturnsComponent,
        ServiceSalesTaxInvoiceComponent,
        ServiceSalesTaxInvoiceReturnsComponent,
        SalesInvoiceComponent,
        SalesReturnsComponent,
        ServiceSalesInvoiceComponent,
        Sales_tax_returns_viewComponent,
        Service_sales_tax_invoice_viewComponent,
        Service_sales_tax_invoice_returns_viewComponent,
        Sales_invoice_viewComponent,
        Sales_returns_viewComponent,
        Service_sales_invoice_viewComponent,
        Service_sales_invoice_returnsComponent,
        Service_sales_invoice_returns_viewComponent
        
    ],
    imports: [
        SalesInvoiceRoutingModule,
        SharedModule,
        ProductsGridControleModule
    ],
    exports: [RouterModule],
})

export class SalesInvoiceModule {
}
