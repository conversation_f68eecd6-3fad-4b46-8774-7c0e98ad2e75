import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { SalesService } from '../../../sales.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-service_sales_invoice_view',
  templateUrl: './service_sales_invoice_view.component.html',
  styleUrls: ['./service_sales_invoice_view.component.css'],
  standalone:false
})
export class Service_sales_invoice_viewComponent implements OnInit {
    ServiceSalesInvoice: FormGroup;
    [x: string]: any;
    Product: any[];
      AnalyticAccounts: any[];
      Units: any[];
      data: any[];
      customers: any[];
      warehouses: any[];
      salesmen: any[];
      Journals: any[];
      drivers: any[];
      shapingCompanys: any[];
      invoiceDate: any;
      quotationsEXPDate: any;
      newdata:FormGroup;
      dueDate: any;
      Notes: any;
      paymentterms: any;
      deliveryterms: any;
      deliverylocation: any;
      shippingmethod: any;
      einvoicestatues: any;
      docno: any;
      deliveryDate: any;
      Salestypes: any[];
      actionType: any[];
      Cashs: any[];
      CashBoxId: number = 0;
      SalesPersonid: number = 0;
      productcodeId: number = 0;
      CostID: number = 0;
      projectId: number = 0;
      projectid: number = 0;
      CashId: number = 0;
      bankAccountId: number = 0;
      InvoiceTypeId: number = 0;
      Banks: [] = [];
      projects: [] = [];
      projectsid: [] = [];
      productscode: [] = [];
      costCenter: [] = [];
      SalesPersons: [] = [];
      Invoices: [] = [];
      currencyId: number = 1;
      currency: any[];
      bankid: any;
      isGridBoxOpened: boolean;
      editorOptions: any;
      gridBoxValue: number[] = [1];
      subscription = new Subscription();
      currentFilter: any;
      branches: [] = [];
      total = 0;
      discount = 0;
      discountPerc = 0;
      withoutbreakage: boolean = false;
      vat = 0;
      tax = 0;
      taxPerc = 0;
      shaping = 0;
      net: number | string = 0;
      activeTab: string = 'tab1';
    
    
          selectedItemKeys: any = [];
          isRtl: boolean = document.documentElement.dir === 'rtl';
             menuOpen = false;
             toggleMenu() {
               this.menuOpen = !this.menuOpen;
             }
             actionsList: string[] = [
               'Export',
               'Send via SMS',
               'Send Via Email',
               'Send Via Whatsapp',
               'print',
             ];
             modalConfig: ModalConfig = {
               modalTitle: 'Send Sms',
               modalSize: 'lg',
               hideCloseButton(): boolean {
                 return true;
               },
               dismissButtonLabel: 'Cancel',
             };
           
             smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
             emailModalConfig: ModalConfig = {
               ...this.modalConfig,
               modalTitle: 'Send Email',
             };
             whatsappModalConfig: ModalConfig = {
               ...this.modalConfig,
               modalTitle: 'Send Whatsapp',
             };
             @ViewChild('smsModal') private smsModal: ModalComponent;
             @ViewChild('emailModal') private emailModal: ModalComponent;
             @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(private service: SalesService,    private fb: FormBuilder,
        private cdk: ChangeDetectorRef,  private router: Router,    private route: ActivatedRoute,) {


            const today=new Date().toISOString().slice(0, 10);
                                  
                                      this.newdata = this.fb.group({
                                        customer: [null, Validators.required],
                                        branch: [null, Validators.required],
                                        journal: [null, Validators.required],
                                        salesType: [null, Validators.required],
                                        total: [null, Validators.required],
                                        discountPerc: [null, Validators.required],
                                        discount: [null, Validators.required],
                                        vat: [null, Validators.required],
                                        taxPerc: [null, Validators.required],
                                        tax: [null, Validators.required],
                                        shaping: [null, Validators.required],
                                        net: [null, Validators.required],
                                        currencyid: [null, Validators.required],
                                        InvoiceTypeId: [null, Validators.required],
                                        CashBoxId: [null, Validators.required],
                                        paymentterms: [null, Validators.required],
                                        deliveryterms: [null, Validators.required],
                                        deliverylocation: [null, Validators.required],
                                        shippingmethod: [null, Validators.required],
                                        notes: [null, Validators.required],
                                        bankAccountId: [null, Validators.required],
                                        salesinvoicenumber: [null, Validators.required],
                                        SalesPersonid: [null, Validators.required],
                                        productcodeId: [null, Validators.required],
                                        projectid: [null, Validators.required],
                                        CostID: [null, Validators.required],
                                        projectId: [null, Validators.required],
                                        invoiceDate: [today, Validators.required],   
                                        quotationsEXPDate: [today, Validators.required],             
                                      });
          
          
          
          
          
              this.subscription.add(service.getSalesPersons().subscribe(r => {
                if (r.success) {
                  this.SalesPersons = r.data;
                  this.cdk.detectChanges();
                }
              }));
              
              this.subscription.add(
                service.getCustDropdown().subscribe((r) => {
                  if (r.success) {
                    this.customers = r.data;
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(
                service.getbranches().subscribe((r) => {
                  if (r.success) {
                    this.branches = r.data;
          
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(
                service.getJournals().subscribe((r) => {
                  if (r.success) {
                    this.Journals = r.data;
          
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(
                service.getSalestypes().subscribe((r) => {
                  if (r.success) {
                    this.Salestypes = r.data;
          
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(
                service.getCashBoxActionType().subscribe((r) => {
                  if (r.success) {
                    this.actionType = r.data;
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(
                service.getCashBox().subscribe((r) => {
                  if (r.success) {
                    this.Cashs = r.data;
                    this.cdk.detectChanges();
                  }
                })
              );
          
              this.subscription.add(service.getBankAccount().subscribe(r => {
                if (r.success) {
                  this.Banks = r.data;
                  this.cdk.detectChanges();
          
                }
              }));
              this.subscription.add(service.getBankActionType().subscribe(r => {
                if (r.success) {
                  this.actionType = r.data;
                  this.cdk.detectChanges();
                }
              }));
              this.subscription.add(service.getCurrency().subscribe(r => {
                if (r.success) {
                  this.currency = r.data;
                  this.cdk.detectChanges();
                }
              }));
          
              this.subscription.add(
                service.getCostCenters().subscribe((r) => {
                  if (r.success) {
                    this.costCenter = r.data;
                    this.cdk.detectChanges();
                  }
                })
              ); 

         }

  ngOnInit() {
    this.ServiceSalesInvoice = new FormGroup({
              invoiceDate: new FormControl(''),
              quotationsEXPDate: new FormControl('')   ,        
              dueDate: new FormControl(''),
              customer: new FormControl(''),
              deliveryDate: new FormControl(''),
              warehouse: new FormControl(''),
              branch: new FormControl(''),
              journal: new FormControl(''),
              salesType: new FormControl(''),
            });


            
  }


  discard() {
    }
    
  
    save() {
      const id = this.route.snapshot.params.id;
      if (id) {
        if (this.newdata.valid) {
          const form = this.newdata.value;
          this.subscriptions.add(
            this.myService
              .update(id, form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                if (r.success) {
                  this.router.navigate(['/sales/invoice/service_sales_invoice']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      } else {
        if (this.newdata.valid) {
          const form = this.newdata.value;
          this.subscriptions.add(
            this.myService
              .create(form)
              .pipe(
                finalize(() => {
                  this.isLoading = false;
                  this.cdk.detectChanges();
                })
              )
              .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                if (r.success) {
                  this.router.navigate(['/sales/invoice/service_sales_invoice']);
                }
              })
          );
        } else {
          this.newdata.markAllAsTouched();
        }
      }
    }
  
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  
  onDataUpdate(data: any) {
    this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  discountChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discountPerc = value * 100/ this.total;
      this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
    }
    this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  discountPercChanged(event: any) {
    const value = +event.target.value;
    if(value) {
      this.discount = this.total * value / 100;
      this.discount = parseFloat(this.discount.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  onTaxChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.taxPerc = value * 100 / this.total;
      this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  onVatChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  onTaxPercChange(event: any) {
    const value = +event.target.value;
    if(value) {
      this.tax = this.total * value / 100;
      this.tax = parseFloat(this.tax.toFixed(2));
    }
    this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
    this.net = this.net.toFixed(2);
  }
  
  onShapingChange(event: any) {
    const value = +event.target.value;
    this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
    this.net = this.net.toFixed(2);
  }
  
  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'ServiceSalesInvoice.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }
  
  
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  } 


}
