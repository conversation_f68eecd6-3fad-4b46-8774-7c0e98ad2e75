<form [formGroup]="newdata">

  <div class="card mb-5 mb-xl-10">

    <div class="card-header border-0 cursor-pointer w-100">
  
      <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
        <h3 class="fw-bolder m-0">{{ "COMMON.NewServicesQuotations" | translate }}</h3>
   
        <div [style.position]="'relative'">
       <div class="btn-group">
       <button
         type="submit"
         (click)="discard()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.Cancel" | translate }}
         <i class="fa fa-close"></i>
       </button>
       <button
         type="submit"
         (click)="save()"
         class="btn btn-sm btn-active-light-primary"
       >
         {{ "COMMON.SaveChanges" | translate }}
         <i class="fa fa-save"></i>
       </button>
     </div>
     <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
               (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
     <button
       class="btn btn-icon btn-active-light-primary mx-2"
       (click)="toggleMenu()"
       data-bs-toggle="tooltip"
       data-bs-placement="top"
       data-bs-trigger="hover"
       title="Settings"
     >
       <i class="fa fa-gear"></i>
     </button>
     <div
       class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
       [class.show]="menuOpen"
       [style.position]="'absolute'"
       [style.top]="'100%'"
       [style.zIndex]="'1050'"
       [style.left]="isRtl ? '0' : 'auto'"
       [style.right]="!isRtl ? '0' : 'auto'"
     >
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           (click)="exportToExcel()"
           data-kt-company-table-filter="delete_row"
         >
           {{ "COMMON.ExportToExcel" | translate }}
         </a>
       </div>
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openSmsModal()"
         >
           {{ "COMMON.SendViaSMS" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openEmailModal()"
         >
           {{ "COMMON.SendViaEmail" | translate }}
         </a>
       </div>
  
       <div class="menu-item px-3">
         <a
           class="menu-link px-3"
           data-kt-company-table-filter="delete_row"
           (click)="openWhatsappModal()"
         >
           {{ "COMMON.SendViaWhatsapp" | translate }}
         </a>
       </div>
  
       <div
         class="menu-item px-3"
         *hasPermission="{
           action: 'printAction',
           module: 'Salse.ServicesQuotations'
         }"
       >
         <a
           class="menu-link px-3"
           target="_blank"
           href="/reports/warehouses"
           data-kt-company-table-filter="delete_row"
           >{{ "COMMON.Print" | translate }}</a
         >
       </div>
       <!-- <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'HR.Employees'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses?withLogo=true"
         data-kt-company-table-filter="delete_row"
         >Print With Logo</a
       >
     </div> -->
     </div>
   </div>
  
  </div>
  
    </div>
  
    <div class="card-body border-top p-9">

      <div class="main-inputs mb-5">

          <div class="row ">
  
  
              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label class="form-label">{{ "COMMON.InvoiceDate" | translate }}</label>
                <input
                  id="invoiceDate"
                  type="date"
                  formControlName="invoiceDate"
                  class="form-control"
                />
              </div>
  
              <div class="form-group col-xl-4 col-md-4 col-sm-12  ">
                <label for="customer" class="form-label">
                  {{ "COMMON.Customer" | translate }}
                </label>
                <ng-select
                  id="customer"
                  formControlName="customerId"
                  bindLabel="nameAr"
                  bindValue="id"
                  [items]="customers"
                ></ng-select>
              </div>
  
          </div>

          <div class="row">

            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label for="branch" class="form-label">
                  {{ "COMMON.Branch" | translate }}
              </label>
              <ng-select
                      id="branch"
                      formControlName="branch"
                      bindLabel="name"
                      bindValue="id"
                      [items]="branches"></ng-select>
          </div>


          <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="currencyid" class="form-label">
              {{ "COMMON.Currency" | translate }}
            </label>
            <ng-select
              id="currencyid"
              formControlName="currencyId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="currency"
            ></ng-select>
          </div>

          </div>
        
            
      </div>

  
  
    <div class="row">
  
    <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [class.active]="activeTab === 'tab1'"
          (click)="setActiveTab('tab1')"
        >
          {{ "COMMON.InvoiceLines" | translate }}
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [class.active]="activeTab === 'tab2'"
          (click)="setActiveTab('tab2')"
        >
          {{ "COMMON.JournalItems" | translate }}
        </button>
      </li>
  
      <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            [class.active]="activeTab === 'tab3'"
            (click)="setActiveTab('tab3')"
          >
            {{ "COMMON.OtherInfo" | translate }}
          </button>
        </li>
  
    </ul>
  
  
  
    <div class="tab-content" id="myTabContent">
  
  
  <!-- Tab 1 -->
  <div
                class="tab-pane fade"
                [class.show]="activeTab === 'tab1'"
                [class.active]="activeTab === 'tab1'"
                *ngIf="activeTab === 'tab1'"
              >
                                      <!--grid controle-->
                                      <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
    
              <div class="row">
                <div class="row">
    
                    <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Total</div>
                        <input type="number" [value]="total" class="form-control" disabled/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount %</div>
                        <input type="number" [(ngModel)]="discountPerc" (change)="discountPercChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount</div>
                        <input type="number" [(ngModel)]="discount" (change)="discountChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Vat</div>
                        <input type="number" [(ngModel)]="vat" (change)="onVatChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax %</div>
                        <input type="number" [(ngModel)]="taxPerc" (change)="onTaxPercChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax</div>
                        <input type="number" [(ngModel)]="tax" (change)="onTaxChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">shaping</div>
                        <input type="number" [(ngModel)]="shaping" (change)="onShapingChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                    </div>
                </div>
                    <div class="row">
    
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                        <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Net</div>
                        <input type="number" [(ngModel)]="net" class="form-control" disabled [ngModelOptions]="{standalone: true}"/>
                    </div>
    
                </div>
      
      
      
              </div>
      
              
                
   </div>
   
  <!-- Tab 2 -->
  <div
    class="tab-pane fade"
    [class.show]="activeTab === 'tab2'"
   [class.active]="activeTab === 'tab2'"
  *ngIf="activeTab === 'tab2'"
   >
  
  <div class="row ">

   <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
    <label class="label">{{ "COMMON.Attention" | translate }}</label>
    <input
    type="text"
    id="attention"
    name="attention"
    class="form-control"
    formControlName="attention"
  />
</div>
  <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
    <label class="label">{{ "COMMON.CustomerDepartmen" | translate }}</label>
    <input
    type="text"
    id="customerdepartmen"
    name="customerdepartmen"
    class="form-control"
    formControlName="customerdepartmen"
  />
</div>

    </div>


    <div class="row">

      <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-4 ">
        <label class="label">{{ "COMMON.DocumentNumber" | translate }}</label>
        <input
        type="text"
        id="documentnumber"
        name="documentnumber"
        class="form-control"
        formControlName="documentnumber"
      />
    </div>
    
      <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-4 ">
        <label class="label">{{ "COMMON.PurchaseOrderNumber" | translate }}</label>
        <input
        type="text"
        id="purchaseordernumber"
        name="purchaseordernumber"
        class="form-control"
        formControlName="purchaseordernumber"
      />
    </div>
      
      
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <span class="mx-2">{{
            "COMMON.PriceIncludingTax" | translate
          }}</span>
          <label class="switch">
            <input
              type="checkbox"
              formControlName="priceincludingtax"
            />
            <span class="slider"></span>
          </label>
        </div>

    </div>

    <div class="row">


      <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
        <label class="label">{{ "COMMON.ShippingMethod" | translate }}</label>
        <input
        type="text"
        id="shippingmethod"
        name="shippingmethod"
        class="form-control"
        formControlName="shippingmethod"
      />
    </div>

       <div class="form-group col-xl-8 col-md-3 col-sm-12 mb-3  ">
        <label class="label">{{ "COMMON.PaymentTerms" | translate }}</label>
        <input
        type="text"
        id="paymentterms"
        name="paymentterms"
        class="form-control"
        formControlName="paymentterms"
      />
    </div>  

    </div>


  
    
  
  </div>
  
  <!-- Tab 3 -->
  <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab3'"
              [class.active]="activeTab === 'tab3'"
              *ngIf="activeTab === 'tab3'"
            >
  

              <div class="row">

                <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
                  <label class="label">{{ "COMMON.PresentedTo" | translate }}</label>
                  <input
                  type="text"
                  id="presentedto"
                  name="presentedto"
                  class="form-control"
                  formControlName="presentedto"
                />
              </div>


  


              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label class="form-label">{{ "COMMON.EXPDate" | translate }}</label>
                <input
                  id="quotationsEXPDate"
                  type="date"
                  formControlName="quotationsEXPDate"
                  class="form-control"
                />
              </div>

              </div>



              <div class="row">

                <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
                  <label class="label">{{ "COMMON.DeliveryTerms" | translate }}</label>
                  <input
                  type="text"
                  id="deliveryterms"
                  name="deliveryterms"
                  class="form-control"
                  formControlName="deliveryterms"
                />
              </div>
          
          
              <div class="form-group col-xl-4 col-md-3 col-sm-12 mb-3  ">
                <label class="label">{{ "COMMON.DeliveryLocation" | translate }}</label>
                <input
                type="text"
                id="deliverylocation"
                name="deliverylocation"
                class="form-control"
                formControlName="deliverylocation"
              />
            </div>
          
          
          
          
              </div>



                <div class="row">

                  <div class="form-group col-xl-12 col-md-3 col-sm-12">
                    <label class="form-label" style=" font-size: 18px;font-weight: bold; ">{{ "COMMON.QuotationTerms" | translate }}</label>
                    <input
                    type="text"
                    id="quotationterms"
                    name="quotationterms"
                    class="form-control"
                    formControlName="quotationterms"
                  />
                    <textarea class="form-control" rows="2"></textarea>
                  </div>
      
                </div>


                
  
    
    

  
  
  </div>
  
  
    </div>
  
  </div>
  
  

    </div>
  
  
  
  
  </div>

</form>
