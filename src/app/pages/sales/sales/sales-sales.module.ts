import {NgModule} from '@angular/core';
import {ProductsQuotationsComponent} from './products-quotations/products-quotations.component';
import {ServicesQuotationsComponent} from './services-quotations/services-quotations.component';
import {WarrantiesComponent} from './warranties/warranties.component';
import {SalesSalesRoutingModule} from "./sales-sales-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { Warranties_viewComponent } from './warranties/warranties_view/warranties_view.component';
import { Products_quotations_viewComponent } from './products-quotations/products_quotations_view/products_quotations_view.component';
import { Services_quotations_viewComponent } from './services-quotations/services_quotations_view/services_quotations_view.component';

@NgModule({
    declarations: [
        WarrantiesComponent,
        ServicesQuotationsComponent,
        ProductsQuotationsComponent,
        Warranties_viewComponent,
        Products_quotations_viewComponent,
        Services_quotations_viewComponent,
        
    ],
    imports: [
        SalesSalesRoutingModule,
        SharedModule,
        ProductsGridControleModule
    ],
    exports: [RouterModule],
})

export class SalesSalesModule {
}
