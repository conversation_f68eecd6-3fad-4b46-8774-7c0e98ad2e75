import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { SalesService } from '../../../sales.service';

@Component({
  selector: 'app-warranties_view',
  templateUrl: './warranties_view.component.html',
  styleUrls: ['./warranties_view.component.css'],
  standalone:false
})
export class Warranties_viewComponent implements OnInit {
  Warranties: FormGroup;

   [x: string]: any;
          Product: any[];
            AnalyticAccounts: any[];
            Units: any[];
            data: any[];
            customers: any[];
            warehouses: any[];
            salesmen: any[];
            Journals: any[];
            drivers: any[];
            shapingCompanys: any[];
            invoiceDate: any;
            newdata:FormGroup;
            dueDate: any;
            Notes: any;
            einvoicestatues: any;
            docno: any;
            deliveryDate: any;
            Salestypes: any[];
            actionType: any[];
            Cashs: any[];
            CashBoxId: number = 0;
            SalesPersonid: number = 0;
            productcodeId: number = 0;
            CostID: number = 0;
            projectId: number = 0;
            projectid: number = 0;
            CashId: number = 0;
            bankAccountId: number = 0;
            InvoiceTypeId: number = 0;
            Banks: [] = [];
            projects: [] = [];
            projectsid: [] = [];
            productscode: [] = [];
            costCenter: [] = [];
            SalesPersons: [] = [];
            Invoices: [] = [];
            currencyId: number = 1;
            currency: any[];
            bankid: any;
            isGridBoxOpened: boolean;
            editorOptions: any;
            gridBoxValue: number[] = [1];
            subscription = new Subscription();
            currentFilter: any;
            branches: [] = [];
            total = 0;
            discount = 0;
            discountPerc = 0;
            withoutbreakage: boolean = false;
            vat = 0;
            tax = 0;
            taxPerc = 0;
            shaping = 0;
            net: number | string = 0;
            activeTab: string = 'tab1';
          
          
                selectedItemKeys: any = [];
                isRtl: boolean = document.documentElement.dir === 'rtl';
                   menuOpen = false;
                   toggleMenu() {
                     this.menuOpen = !this.menuOpen;
                   }
                   actionsList: string[] = [
                     'Export',
                     'Send via SMS',
                     'Send Via Email',
                     'Send Via Whatsapp',
                     'print',
                   ];
                   modalConfig: ModalConfig = {
                     modalTitle: 'Send Sms',
                     modalSize: 'lg',
                     hideCloseButton(): boolean {
                       return true;
                     },
                     dismissButtonLabel: 'Cancel',
                   };
                 
                   smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
                   emailModalConfig: ModalConfig = {
                     ...this.modalConfig,
                     modalTitle: 'Send Email',
                   };
                   whatsappModalConfig: ModalConfig = {
                     ...this.modalConfig,
                     modalTitle: 'Send Whatsapp',
                   };
                   @ViewChild('smsModal') private smsModal: ModalComponent;
                   @ViewChild('emailModal') private emailModal: ModalComponent;
                   @ViewChild('whatsappModal') private whatsappModal: ModalComponent;



  constructor(private service: SalesService,    private fb: FormBuilder,
              private cdk: ChangeDetectorRef,  private router: Router,    private route: ActivatedRoute,) { }

  ngOnInit() {
  }


  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }


         discard() {
           }
           
         
           save() {
             const id = this.route.snapshot.params.id;
             if (id) {
               if (this.newdata.valid) {
                 const form = this.newdata.value;
                 this.subscriptions.add(
                   this.myService
                     .update(id, form)
                     .pipe(
                       finalize(() => {
                         this.isLoading = false;
                         this.cdk.detectChanges();
                       })
                     )
                     .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                       if (r.success) {
                         this.router.navigate(['/sales/sales/warranties']);
                       }
                     })
                 );
               } else {
                 this.newdata.markAllAsTouched();
               }
             } else {
               if (this.newdata.valid) {
                 const form = this.newdata.value;
                 this.subscriptions.add(
                   this.myService
                     .create(form)
                     .pipe(
                       finalize(() => {
                         this.isLoading = false;
                         this.cdk.detectChanges();
                       })
                     )
                     .subscribe((r: { success: boolean }) => { // Define the type of 'r' here
                       if (r.success) {
                         this.router.navigate(['/sales/sales/warranties']);
                       }
                     })
                 );
               } else {
                 this.newdata.markAllAsTouched();
               }
             }
           }

           exportToExcel() {
            this.subscription.add(this.service.exportExcel()
              .subscribe(e => {
                if (e) {
                  const href = URL.createObjectURL(e);
                  const link = document.createElement('a');
                  link.setAttribute('download', 'warranties.xlsx');
                  link.href = href;
                  link.click();
                  URL.revokeObjectURL(href);
                }
              }));
          }
          
          
          openSmsModal() {
            this.smsModal.open();
          }
          openWhatsappModal() {
            this.whatsappModal.open();
          }
          openEmailModal() {
            this.emailModal.open();
          } 


}
