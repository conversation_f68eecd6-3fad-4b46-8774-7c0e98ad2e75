<div class="card mb-5 mb-xl-10">

  <div class="card-header border-0 cursor-pointer w-100">

    <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
      <h3 class="fw-bolder m-0">{{ "COMMON.NewWarranties" | translate }}</h3>
 
      <div [style.position]="'relative'">
     <div class="btn-group">
     <button
       type="submit"
       (click)="discard()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.Cancel" | translate }}
       <i class="fa fa-close"></i>
     </button>
     <button
       type="submit"
       (click)="save()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.SaveChanges" | translate }}
       <i class="fa fa-save"></i>
     </button>
   </div>
   <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
             (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
   <button
     class="btn btn-icon btn-active-light-primary mx-2"
     (click)="toggleMenu()"
     data-bs-toggle="tooltip"
     data-bs-placement="top"
     data-bs-trigger="hover"
     title="Settings"
   >
     <i class="fa fa-gear"></i>
   </button>
   <div
     class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
     [class.show]="menuOpen"
     [style.position]="'absolute'"
     [style.top]="'100%'"
     [style.zIndex]="'1050'"
     [style.left]="isRtl ? '0' : 'auto'"
     [style.right]="!isRtl ? '0' : 'auto'"
   >
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         (click)="exportToExcel()"
         data-kt-company-table-filter="delete_row"
       >
         {{ "COMMON.ExportToExcel" | translate }}
       </a>
     </div>
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openSmsModal()"
       >
         {{ "COMMON.SendViaSMS" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openEmailModal()"
       >
         {{ "COMMON.SendViaEmail" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openWhatsappModal()"
       >
         {{ "COMMON.SendViaWhatsapp" | translate }}
       </a>
     </div>

     <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'Salse.Warranties'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses"
         data-kt-company-table-filter="delete_row"
         >{{ "COMMON.Print" | translate }}</a
       >
     </div>
     <!-- <div
     class="menu-item px-3"
     *hasPermission="{
       action: 'printAction',
       module: 'HR.Employees'
     }"
   >
     <a
       class="menu-link px-3"
       target="_blank"
       href="/reports/warehouses?withLogo=true"
       data-kt-company-table-filter="delete_row"
       >Print With Logo</a
     >
   </div> -->
   </div>
 </div>

</div>

  </div>

  <!-- <div class="card-body border-top p-9">
    <form
    [formGroup]="newdata"
    class="form d-flex flex-wrap align-items-start gap-3"
    action="#"
    id="kt_modal_add_company_form"
    data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
  >
  <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
    <div class="main-inputs mb-5">
        <div class="row mb-3">

      
          <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-3 ">
            <label for="transactiontype" class="form-label">
              {{ "COMMON.TransactionType " | translate }}
            </label>
            <ng-select
              id="transactiontype"
              formControlName="transactiontypeId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="transactiontypes"
            ></ng-select>
          </div>


        <div class="form-group col-xl-4 col-md-2 col-sm-12">
          <label for="transactionnumber" class="form-label col-xl-4 col-md-4 col-sm-12">
            {{ "COMMON.TransactionNumber" | translate }}
          </label>

          <input
        id="transactionnumberid"
        type="text"
        formControlName="transactionnumberid"
        class="form-control"
        style="background-color: #f5f8fa"
      />

        </div>


        
        </div>
    </div>
</div>


</form>
  </div> -->


</div>