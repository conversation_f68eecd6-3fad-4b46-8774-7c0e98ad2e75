import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ProductsQuotationsComponent} from './products-quotations/products-quotations.component';
import {ServicesQuotationsComponent} from './services-quotations/services-quotations.component';
import {WarrantiesComponent} from './warranties/warranties.component';
import { Warranties_viewComponent } from './warranties/warranties_view/warranties_view.component';
import { Products_quotations_viewComponent } from './products-quotations/products_quotations_view/products_quotations_view.component';
import { Services_quotations_viewComponent } from './services-quotations/services_quotations_view/services_quotations_view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'warranties',
        pathMatch: 'full'
    },
    {
        path: 'warranties',
        component: WarrantiesComponent
    },

    {
        path: 'warranties_view',
        component: Warranties_viewComponent
    },
    {
        path: 'warranties_view/:id',
        component: Warranties_viewComponent
        
    },

    {
        path: 'products_quotations_view',
        component: Products_quotations_viewComponent
    },
    {
        path: 'products_quotations_view/:id',
        component: Products_quotations_viewComponent
        
    },
{
        path: 'services_quotations_view',
        component: Services_quotations_viewComponent
    },
    {
        path: 'services_quotations_view/:id',
        component: Services_quotations_viewComponent
        
    },


    {
        path: 'services_quotations',
        component: ServicesQuotationsComponent
    },
    {
        path: 'products_quotations',
        component: ProductsQuotationsComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SalesSalesRoutingModule {
}
