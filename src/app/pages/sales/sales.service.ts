import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export class Status {
  id: number;
  name: string;
}

const statuses: Status[] = [
  {
    id: 1,
    name: 'Not Started',
  },
  {
    id: 2,
    name: 'In Progress',
  },
  {
    id: 3,
    name: 'Deferred',
  },
  {
    id: 4,
    name: 'Need Assistance',
  },
  {
    id: 5,
    name: 'Completed',
  },
];

@Injectable({
  providedIn: 'root',
})
export class SalesService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query,
      },
    });
  }
  getThirdPartyAccountId(typId: any): Observable<any> {
    if (typId === 0) {
      return this.http.get<any>('api/ChartofAccounts/ChartofAccountsdropdown');
    } else if (typId === 2) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (typId === 3) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (typId === 4) {
      return this.http.get<any>('api/employees/EmployeesDropdown');
    } else if (typId === 5) {
      return this.http.get<any>('api/BankAccount');
    } else if (typId === 6) {
      return this.http.get<any>('api/CashBox');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }

  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }
  getMainCustDropdown(): Observable<any> {
    return this.http.get<any>('api/MainCustomer/dropdown');
  }
  getCustTypeDropdown(): Observable<any> {
    return this.http.get<any>('api/CustomersCategory');
  }
  getContactsDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/ContactsDropdown');
  }
  getAccountThirdParty(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty');
  }
  getCustactionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/Customer');
  }
  getwarehouses(): Observable<any> {
    return this.http.get<any>('api/Warehouses');
  }
  getSuppliersDropdown(): Observable<any> {
    return this.http.get<any>('api/Suppliers/dropdown');
  }
  getUsers(): Observable<any> {
    return this.http.get<any>('api/FalconUsers');
  }
  getbranches(): Observable<any> {
    return this.http.get<any>('api/Branch');
  }
  getProductCategories(): Observable<any> {
    return this.http.get<any>('api/ProductCategories');
  }
  getJournals(): Observable<any> {
    return this.http.get<any>('api/Journal');
  }
  getSalestypes(): Observable<any> {
    return this.http.get<any>('api/SalesType');
  }
  getSalesPersons(): Observable<any> {
    return this.http.get<any>('api/Salesperson/SalespersonDropdown');
  }
  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }

  getCashBoxActionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/CashBox');
  }

  getCashBox(): Observable<any> {
    return this.http.get<any>('api/CashBox');
  }

  getBankActionType(): Observable<any> {
    return this.http.get<any>('api/AccountThirdParty/Bank');
  }
  getBankAccount(): Observable<any> {
    return this.http.get<any>('api/BankAccount');
  }

  getCurrency(): Observable<any> {
    return this.http.get<any>('api/Currency');
  }

  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }

  getAnalyticAccounts(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts');
  }
  getCustDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/dropdown');
  }
  getGuarantor(): Observable<any> {
    return this.http.get<any>('api/Guarantor');
  }

  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  getStatuses() {
    return statuses;
  }

  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id).pipe(
      map((result) => {
        if (result.success) {
        }
        return result;
      })
    );
  }

  batch(obj: any): Observable<any> {
    return this.http.post(this.baseUrl + 'batch/', obj);
  }
  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  // Métodos para el dashboard

  // Método para obtener estadísticas del dashboard
  getDashboardStats(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    // Por ahora, devolvemos datos simulados
    return of({
      totalSales: 1875,
      totalRevenue: 356250,
      totalCustomers: 243,
      pendingOrders: 28
    });
  }

  // Método para obtener tendencias de ventas
  getSalesTrends(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { month: 'Jan', value: 42000 },
      { month: 'Feb', value: 53000 },
      { month: 'Mar', value: 57000 },
      { month: 'Apr', value: 69000 },
      { month: 'May', value: 75000 },
      { month: 'Jun', value: 60250 }
    ]);
  }

  // Método para obtener los principales productos
  getTopProducts(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { name: 'Premium Laptop', value: 68500, percentage: 19.2 },
      { name: 'Wireless Headphones', value: 52300, percentage: 14.7 },
      { name: 'Smart Watch', value: 45200, percentage: 12.7 },
      { name: 'Gaming Console', value: 38700, percentage: 10.9 },
      { name: 'Smartphone', value: 32100, percentage: 9.0 }
    ]);
  }

  // Método para obtener los principales clientes
  getTopCustomers(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { name: 'Tech Solutions Inc.', value: 42500, percentage: 11.9 },
      { name: 'Global Retail Co.', value: 38700, percentage: 10.9 },
      { name: 'Modern Electronics', value: 35200, percentage: 9.9 },
      { name: 'Smart Devices Ltd.', value: 28700, percentage: 8.1 },
      { name: 'Digital World', value: 25100, percentage: 7.0 }
    ]);
  }

  // Método para obtener ventas por región
  getSalesByRegion(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { region: 'North', value: 125000, percentage: 35.1 },
      { region: 'South', value: 98000, percentage: 27.5 },
      { region: 'East', value: 72000, percentage: 20.2 },
      { region: 'West', value: 61250, percentage: 17.2 }
    ]);
  }

  // Método para obtener ventas recientes
  getRecentSales(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { id: 'SO-2023-0587', customer: 'Tech Solutions Inc.', date: '2023-06-15', amount: 12450, status: 'Completed' },
      { id: 'SO-2023-0586', customer: 'Global Retail Co.', date: '2023-06-14', amount: 8750, status: 'Completed' },
      { id: 'SO-2023-0585', customer: 'Modern Electronics', date: '2023-06-12', amount: 15200, status: 'Pending' },
      { id: 'SO-2023-0584', customer: 'Smart Devices Ltd.', date: '2023-06-10', amount: 5680, status: 'Completed' },
      { id: 'SO-2023-0583', customer: 'Digital World', date: '2023-06-08', amount: 9340, status: 'Completed' }
    ]);
  }
}
