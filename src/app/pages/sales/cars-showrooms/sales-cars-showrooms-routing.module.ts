import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CarRemovalComponent} from './car-removal/car-removal.component';
import {CarSalesReportComponent} from './car-sales-report/car-sales-report.component';
import {CarSalesComponent} from './car-sales/car-sales.component';
import {CarsComponent} from './cars/cars.component';
import {InstallmentsPaymentComponent} from './installments-payment/installments-payment.component';
import {PersonsAccountsComponent} from './persons-accounts/persons-accounts.component';
import {PersonsComponent} from './persons/persons.component';
import { Cars_viewComponent } from './cars/cars_view/cars_view.component';
import { Persons_viewComponent } from './persons/persons_view/persons_view.component';
import { Car_removal_viewComponent } from './car-removal/car_removal_view/car_removal_view.component';
import { Installments_payment_viewComponent } from './installments-payment/installments_payment_view/installments_payment_view.component';
import { Persons_accounts_viewComponent } from './persons-accounts/persons_accounts_view/persons_accounts_view.component';
import { Car_sales_viewComponent } from './car-sales/car_sales_view/car_sales_view.component';
import { Car_sales_report_viewComponent } from './car-sales-report/car_sales_report_view/car_sales_report_view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'cars',
        pathMatch: 'full'
    },
    {
        path: 'cars',
        component: CarsComponent
    },
    {
        path: 'persons',
        component: PersonsComponent
    },
    {
        path: 'car_removal',
        component: CarRemovalComponent
    },
    {
        path: 'installments_payment',
        component: InstallmentsPaymentComponent
    },
    {
        path: 'persons_accounts',
        component: PersonsAccountsComponent
    },
    {
        path: 'cars_view',
        component: Cars_viewComponent
    },
    {
        path: 'cars_view/:id',
        component: Cars_viewComponent
    },
     {
        path: 'persons_view',
        component: Persons_viewComponent
    },
    {
        path: 'persons_view/:id',
        component: Persons_viewComponent
    },
 {
        path: 'car_removal_view',
        component: Car_removal_viewComponent
    },
    {
        path: 'car_removal/:id',
        component: Car_removal_viewComponent
    },
     {
        path: 'installments_payment_view',
        component: Installments_payment_viewComponent
    },
    {
        path: 'installments_payment_view/:id',
        component: Installments_payment_viewComponent
    },

    {
        path: 'persons_accounts_view',
        component: Persons_accounts_viewComponent
    },
    {
        path: 'persons_accounts_view/:id',
        component: Persons_accounts_viewComponent
    },
    {
        path: 'car_sales_view',
        component: Car_sales_viewComponent
    },
    {
        path: 'car_sales_view/:id',
        component: Car_sales_viewComponent
    },
{
        path: 'car_sales_report_view',
        component: Car_sales_report_viewComponent
    },
    {
        path: 'car_sales_report_view/:id',
        component: Car_sales_report_viewComponent
    },




    
    {
        path: 'car_sales',
        component: CarSalesComponent
    },
    {
        path: 'car_sales_report',
        component: CarSalesReportComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SalesCarsShowroomsRoutingModule {
}
