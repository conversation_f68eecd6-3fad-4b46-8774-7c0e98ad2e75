import { NgModule } from '@angular/core';
import {CarRemovalComponent} from './car-removal/car-removal.component';
import {CarSalesReportComponent} from './car-sales-report/car-sales-report.component';
import {CarSalesComponent} from './car-sales/car-sales.component';
import {CarsComponent} from './cars/cars.component';
import {InstallmentsPaymentComponent} from './installments-payment/installments-payment.component';
import {PersonsAccountsComponent} from './persons-accounts/persons-accounts.component';
import {PersonsComponent} from './persons/persons.component';
import {SalesCarsShowroomsRoutingModule} from "./sales-cars-showrooms-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { Cars_viewComponent } from './cars/cars_view/cars_view.component';
import { Persons_viewComponent } from './persons/persons_view/persons_view.component';
import { Car_removal_viewComponent } from './car-removal/car_removal_view/car_removal_view.component';
import { Installments_payment_viewComponent } from './installments-payment/installments_payment_view/installments_payment_view.component';
import { Persons_accounts_viewComponent } from './persons-accounts/persons_accounts_view/persons_accounts_view.component';
import { Car_sales_viewComponent } from './car-sales/car_sales_view/car_sales_view.component';
import { Car_sales_report_viewComponent } from './car-sales-report/car_sales_report_view/car_sales_report_view.component';

@NgModule({
    declarations: [
    CarsComponent,
    PersonsComponent,
    CarRemovalComponent,
    InstallmentsPaymentComponent,
    PersonsAccountsComponent,
    CarSalesComponent,
    CarSalesReportComponent,
    Cars_viewComponent,
    Persons_viewComponent,
    Car_removal_viewComponent,
    Installments_payment_viewComponent,
    Persons_accounts_viewComponent,
    Car_sales_viewComponent,
    Car_sales_report_viewComponent,
    
  ],
  imports: [
    SalesCarsShowroomsRoutingModule,
    SharedModule,
    ProductsGridControleModule,
    
  ],
  exports: [RouterModule],
})

export class SalesCarsShowroomsModule { }
