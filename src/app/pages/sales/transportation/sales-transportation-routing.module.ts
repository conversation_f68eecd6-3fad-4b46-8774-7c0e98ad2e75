import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BillOfLadingComponent} from './bill-of-lading/bill-of-lading.component';
import {BillOfSeaLadingComponent} from './bill-of-sea-lading/bill-of-sea-lading.component';
import {CarRevenueComponent} from './car-revenue/car-revenue.component';
import {CarsDataComponent} from './cars-data/cars-data.component';
import {CarsDriversComponent} from './cars-drivers/cars-drivers.component';
import {PortsComponent} from './ports/ports.component';
import {TransferOrderComponent} from './transfer-order/transfer-order.component';
import {TransportationCompaniesComponent} from './transportation-companies/transportation-companies.component';
import {TransportationComponent} from './transportation/transportation.component';
import { Bill_of_lading_viewComponent } from './bill-of-lading/bill_of_lading_view/bill_of_lading_view.component';
import { Bill_of_sea_lading_viewComponent } from './bill-of-sea-lading/bill_of_sea_lading_view/bill_of_sea_lading_view.component';
import { Transfer_order_viewComponent } from './transfer-order/transfer_order_view/transfer_order_view.component';
import { Cars_data_viewComponent } from './cars-data/cars_data_view/cars_data_view.component';
import { Cars_drivers_viewComponent } from './cars-drivers/cars_drivers_view/cars_drivers_view.component';
import { Ports_viewComponent } from './ports/ports_view/ports_view.component';
import { Transportation_companies_viewComponent } from './transportation-companies/transportation_companies_view/transportation_companies_view.component';
import { Transportation_viewComponent } from './transportation/transportation_view/transportation_view.component';
import { Car_revenue_viewComponent } from './car-revenue/car_revenue_view/car_revenue_view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'bill_of_lading',
        pathMatch: 'full'
    },
    {
        path: 'bill_of_lading',
        component: BillOfLadingComponent
    },
    {
        path: 'bill_of_sea_lading',
        component: BillOfSeaLadingComponent
    },
    {
        path: 'transfer_order',
        component: TransferOrderComponent
    },
    {
        path: 'cars_data',
        component: CarsDataComponent
    },

    {
        path: 'bill_of_lading_view',
        component: Bill_of_lading_viewComponent
    },
    {
        path: 'bill_of_lading_view/:id',
        component: Bill_of_lading_viewComponent
    },
    
{
        path: 'bill_of_sea_lading_view',
        component: Bill_of_sea_lading_viewComponent
    },
    {
        path: 'bill_of_sea_lading_view/:id',
        component: Bill_of_sea_lading_viewComponent
    },
    {
        path: 'cars_data_view',
        component: Cars_data_viewComponent
    },
    {
        path: 'cars_data_view/:id',
        component: Cars_data_viewComponent
    },
    
    {
        path: 'transfer_order_view',
        component: Transfer_order_viewComponent
    },
    {
        path: 'transfer_order_view/:id',
        component: Transfer_order_viewComponent
    },
    {
        path: 'cars_drivers_view',
        component: Cars_drivers_viewComponent
    },
    {
        path: 'cars_drivers_view/:id',
        component: Cars_drivers_viewComponent
    },
    {
        path: 'ports_view',
        component: Ports_viewComponent
    },
    {
        path: 'ports_view/:id',
        component: Ports_viewComponent
    },
    {
        path: 'transportation_view',
        component: Transportation_viewComponent
    },
    {
        path: 'transportation_view/:id',
        component: Transportation_viewComponent
    },
    {
        path: 'car_revenue_view',
        component: Car_revenue_viewComponent
    },
    {
        path: 'car_revenue_view/:id',
        component: Car_revenue_viewComponent
    },






    
    {
        path: 'transportation_companies_view',
        component: Transportation_companies_viewComponent
    },
    {
        path: 'transportation_companies_view/:id',
        component: Transportation_companies_viewComponent
    },





    {
        path: 'cars_drivers',
        component: CarsDriversComponent
    },
    {
        path: 'ports',
        component: PortsComponent
    },
    {
        path: 'transportation_companies',
        component: TransportationCompaniesComponent
    },
    {
        path: 'transportation',
        component: TransportationComponent
    },
    {
        path: 'car_revenue',
        component: CarRevenueComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SalesTransportationRoutingModule {
}
