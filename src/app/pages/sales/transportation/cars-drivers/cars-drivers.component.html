<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-body border-top">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">Salesperson</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              (click)="filter()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.Filter" | translate }}
              <i class="fa fa-filter"></i>
            </button>
            <button
              routerLink="/sales/transportation/cars_drivers_view"
              class="btn btn-sm btn-active-light-primary"
              *hasPermission="{
                module: 'Salse.BillOfLading',
                action: 'createAction'
              }"
            >
              {{ "COMMON.Create" | translate }}
              <i class="fa fa-plus"></i>
            </button>
            <button
              [routerLink]="[
                '/sales/transportation/cars_drivers_view',
                selectedItemKeys[0]
              ]"
              [hidden]="
                selectedItemKeys.length > 1 || selectedItemKeys.length == 0
              "
              class="btn btn-sm btn-active-light-primary"
              *hasPermission="{
                module: 'Salse.BillOfLading',
                action: 'updateAction'
              }"
            >
              {{ "COMMON.EDIT" | translate }}
              <i class="fa fa-edit"></i>
            </button>

            <button
              (click)="deleteRecords()"
              [hidden]="!selectedItemKeys.length"
              class="btn btn-sm btn-active-light-primary"
              *hasPermission="{
                module: 'Salse.BillOfLading',
                action: 'deleteAction'
              }"
            >
              {{ "COMMON.DELETE" | translate }}
              <i class="fa fa-trash"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>

          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>

            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="uploadExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ImportToExcel" | translate }}
              </a>
            </div>

            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Salse.BillOfLading'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-inputs">
      <div class="row">
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-2 col-md-4 col-sm-12">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-4 col-md-4 col-sm-12">
          <label for="customer" class="form-label">
            {{ "COMMON.Customer" | translate }}
          </label>
          <ng-select
            id="customer"
            formControlName="customer"
            bindLabel="Name"
            bindValue="ID"
            [items]="customers"
          ></ng-select>
        </div>
      </div>
    </div>
  </div>
</form>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="InvoiceNo"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
  (onSelectionChanged)="selectionChanged($event)"
>
  <dxo-selection mode="multiple"></dxo-selection>

  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column dataField="InvoiceNo" caption="Number"></dxi-column>

  <dxi-column dataField="Customer" caption="Customer">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="InvoiceDate" caption="Invoice Date"></dxi-column>
  <dxi-column dataField="DueDate" caption="Due Date"></dxi-column>
  <dxi-column dataField="Activities" caption="Activities"></dxi-column>
  <dxi-column dataField="TaxExcluded" caption="Tax Excluded"></dxi-column>
  <dxi-column dataField="Tax" caption="Tax"></dxi-column>
  <dxi-column dataField="Total" caption="Total"></dxi-column>
  <dxi-column dataField="Payment" caption="Payment"></dxi-column>
  <dxi-column dataField="Status" caption="Status"></dxi-column>
  <dxi-column dataField="SourceDocument" caption="Source Document"></dxi-column>
  <dxi-column dataField="Reference" caption="Reference"></dxi-column>
  <dxi-column dataField="Salesperson" caption="Salesperson"></dxi-column>
  <dxi-column dataField="Company" caption="Company"></dxi-column>
  <dxi-column dataField="Currency" caption="Currency"></dxi-column>
  <dxi-column dataField="EInvoicing" caption="E Invoicing"></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="Customer" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="InvoiceDate" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="Total" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
