import {NgModule} from '@angular/core';
import {BillOfLadingComponent} from './bill-of-lading/bill-of-lading.component';
import {BillOfSeaLadingComponent} from './bill-of-sea-lading/bill-of-sea-lading.component';
import {CarRevenueComponent} from './car-revenue/car-revenue.component';
import {CarsDataComponent} from './cars-data/cars-data.component';
import {CarsDriversComponent} from './cars-drivers/cars-drivers.component';
import {PortsComponent} from './ports/ports.component';
import {TransferOrderComponent} from './transfer-order/transfer-order.component';
import {TransportationCompaniesComponent} from './transportation-companies/transportation-companies.component';
import {TransportationComponent} from './transportation/transportation.component';
import {SalesTransportationRoutingModule} from "./sales-transportation-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { Bill_of_lading_viewComponent } from './bill-of-lading/bill_of_lading_view/bill_of_lading_view.component';
import { Bill_of_sea_lading_viewComponent } from './bill-of-sea-lading/bill_of_sea_lading_view/bill_of_sea_lading_view.component';
import { Transfer_order_viewComponent } from './transfer-order/transfer_order_view/transfer_order_view.component';
import { Cars_data_viewComponent } from './cars-data/cars_data_view/cars_data_view.component';
import { Cars_drivers_viewComponent } from './cars-drivers/cars_drivers_view/cars_drivers_view.component';
import { Ports_viewComponent } from './ports/ports_view/ports_view.component';
import { Transportation_companies_viewComponent } from './transportation-companies/transportation_companies_view/transportation_companies_view.component';
import { Transportation_viewComponent } from './transportation/transportation_view/transportation_view.component';
import { Car_revenue_viewComponent } from './car-revenue/car_revenue_view/car_revenue_view.component';

@NgModule({
    declarations: [
        BillOfLadingComponent,
        BillOfSeaLadingComponent,
        TransferOrderComponent,
        CarsDataComponent,
        CarsDriversComponent,
        PortsComponent,
        TransportationCompaniesComponent,
        TransportationComponent,
        CarRevenueComponent,
        Bill_of_lading_viewComponent,
        Bill_of_sea_lading_viewComponent,
        Transfer_order_viewComponent,
        Cars_data_viewComponent,
        Cars_drivers_viewComponent,
        Ports_viewComponent,
        Transportation_companies_viewComponent,
        Transportation_viewComponent,
        Car_revenue_viewComponent,


    ],
    imports: [
        SalesTransportationRoutingModule,
        SharedModule,
        ProductsGridControleModule
    ],
    exports: [RouterModule],
})

export class SalesTransportationModule {
}
