import {
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { SalesService } from '../../sales.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import ArrayStore from 'devextreme/data/array_store';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-contracts-report',
  templateUrl: './contracts-report.component.html',
  styleUrls: ['./contracts-report.component.scss'],
  standalone: false,
})
export class ContractsReportComponent implements OnDestroy {
  moduleName = 'Salse.ContractsReport';
  data: any[];
  gridDataSource: any[];
  customers: any[];
  customersId: any = 0;
  toDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  suarantorId: any = 0;
  suarantors: any[];

  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  dataSource: ArrayStore;
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(private service: SalesService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.SalesReports}ContractsReport`;

    this.subscription.add(
      service.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.customers = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getGuarantor().subscribe((r) => {
        if (r.success) {
          this.suarantors = r.data;
          this.cdk.detectChanges();
        }
      })
    );



    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );

  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('contracts_report');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('contracts_report.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'contracts_report.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const Supplierid = this.customersId ?? 0;
    const GuarantorId = this.suarantorId ?? 0;

    this.subscription.add(
      this.service
        .list({
          ToDate,
          Supplierid,
          GuarantorId,
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
