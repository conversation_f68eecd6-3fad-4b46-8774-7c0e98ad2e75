
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { SalesService } from '../../sales.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';
import ArrayStore from 'devextreme/data/array_store';


@Component({
    selector: 'app-salesperson-commissions-percentage',
    templateUrl: './salesperson-commissions-percentage.component.html',
    styleUrls: ['./salesperson-commissions-percentage.component.scss'],
    standalone: false
})
export class SalespersonCommissionsPercentageComponent implements OnDestroy {
  moduleName = 'SalespersonCommissionsPercentage';
  SalesPersons: any[];
  SalesPersonid: number = 0;
  data: any[];
  gridDataSource: any[];
  Indirectcostid: any[];
  fromDateValue: any;
  toDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  fromprevDateButton: any;
  todayButton: any;
  fromtodayButton: any;
  nextDateButton: any;
  fromnextDateButton: any;
  millisecondsInDay = 24 * 60 * 60 * 1000;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  isDetails: boolean = false ;
  showPreviousBalance: boolean = false ;
dataSource: ArrayStore;
  isRtl: boolean = document.documentElement.dir === 'rtl';
     menuOpen = false;
     toggleMenu() {
      this.menuOpen = !this.menuOpen;
      }
      actionsList: string[] = [
         'Export',
         'Send via SMS',
         'Send Via Email',
         'Send Via Whatsapp',
           'print',
            ];
         modalConfig: ModalConfig = {
         modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
        return true;
         },
       dismissButtonLabel: 'Cancel',
        };
          
         smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
         emailModalConfig: ModalConfig = {
           ...this.modalConfig,
           modalTitle: 'Send Email',
            };
        whatsappModalConfig: ModalConfig = {
          ...this.modalConfig,
          modalTitle: 'Send Whatsapp',
            };
     @ViewChild('smsModal') private smsModal: ModalComponent;
     @ViewChild('emailModal') private emailModal: ModalComponent;
     @ViewChild('whatsappModal') private whatsappModal: ModalComponent;


  constructor(private service: SalesService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.SalesReports}SalespersonCommissionsPercentage`;

    this.subscription.add(service.getContactsDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getSalesPersons().subscribe(r => {
      if (r.success) {
        this.SalesPersons = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();

    this.todayButton = {
      text: 'Today',
      onClick: () => {
        this.toDateValue = new Date().getTime();
        this.cdk.detectChanges();
      },
    };
    this.fromtodayButton = {
      text: 'Today',
      onClick: () => {
        this.fromDateValue = new Date().getTime();
        this.cdk.detectChanges();
      },
    };
    this.prevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue -= this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };

    this.nextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue += this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };
    this.fromprevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.fromDateValue -= this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };

    this.fromnextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.fromDateValue += this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('salesperson_commissions_percentage');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('salesperson_commissions_percentage.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'salesperson_commissions_percentage.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  filter() {

    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const SalespersonId = this.SalesPersonid?? 0;
    const IndirectCost = this.Indirectcostid?? 0;

    this.subscription.add(this.service.list({ 
      
      FromDate,
      ToDate,  
      IndirectCost,
      SalespersonId,

    }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open()
  }
  openWhatsappModal() {
    this.whatsappModal.open()
  }
  openEmailModal() {
    this.emailModal.open()
  }
}