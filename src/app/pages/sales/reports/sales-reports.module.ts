import {NgModule} from '@angular/core';

import {AnnualSalesReportComponent} from './annual-sales-report/annual-sales-report.component';
import {ContractsReportComponent} from './contracts-report/contracts-report.component';
import {CustomerProductsReportComponent} from './customer-products-report/customer-products-report.component';
import {DeliveryOrderReportComponent} from './delivery-order-report/delivery-order-report.component';
import {DeliveryReportComponent} from './delivery-report/delivery-report.component';
import {InvoiceEarningsReportComponent} from './invoice-earnings-report/invoice-earnings-report.component';
import {InvoicesReportComponent} from './invoices-report/invoices-report.component';
import {NetSalesComponent} from './net-sales/net-sales.component';
import {ProductOrdersReportComponent} from './product-orders-report/product-orders-report.component';
import {ProductSaleReportComponent} from './product-sale-report/product-sale-report.component';
import {RentReportComponent} from './rent-report/rent-report.component';
import {SalesReportComponent} from './sales-report/sales-report.component';
import {SalesReturnReportComponent} from './sales-return-report/sales-return-report.component';
import {
    SalespersonCommissionsPercentageComponent
} from './salesperson-commissions-percentage/salesperson-commissions-percentage.component';
import {
    SalespersonCommissionsValueComponent
} from './salesperson-commissions-value/salesperson-commissions-value.component';
import {SalespersonCustomerReportComponent} from './salesperson-customer-report/salesperson-customer-report.component';
import {TotalSalesReportComponent} from './total-sales-report/total-sales-report.component';
import {SalesReportsRoutingModule} from "./sales-reports-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { SalesTaxComponent } from './sales-tax/sales-tax.component';

@NgModule({
    declarations: [
        AnnualSalesReportComponent,
        ContractsReportComponent,
        CustomerProductsReportComponent,
        DeliveryOrderReportComponent,
        DeliveryReportComponent,
        InvoiceEarningsReportComponent,
        InvoicesReportComponent,
        NetSalesComponent,
        ProductOrdersReportComponent,
        ProductSaleReportComponent,
        RentReportComponent,
        SalesReportComponent,
        SalesReturnReportComponent,
        SalespersonCommissionsPercentageComponent,
        SalespersonCommissionsValueComponent,
        SalespersonCustomerReportComponent,
        TotalSalesReportComponent,
        SalesTaxComponent
    ],
    imports: [
        SalesReportsRoutingModule,
        SharedModule,
        ProductsGridControleModule
    ],
    exports: [RouterModule],
})

export class SalesReportsModule {
}
