<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.DeliveryOrderReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'DeliveryOrderReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <div class="main-inputs">
    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
        <input
          id="fromDate"
          type="date"
          [(ngModel)]="fromDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
    </div>
    <div class="row">

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="customer" class="form-label">
          {{ "COMMON.Customer" | translate }}
        </label>
        <ng-select
          id="customer"
          [(ngModel)]="customerId"
          bindLabel="nameAr"
          bindValue="id"
          [items]="customers"
        ></ng-select>
      </div>

      <div class="form-group col-xl-4 col-md-2 col-sm-12">
        <label for="invoiceNoId" class="form-label">
          {{ "COMMON.InvoiceNo" | translate }}
        </label>
        <input
          id="invoiceNoId"
          type="text"
          [(ngModel)]="invoiceNoId"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>

    </div>



    <div class="row">

      <div class="form-group col-xl-4 col-md-2 col-sm-12">
        <label for="orderNOId" class="form-label">
          {{ "COMMON.OrderNO" | translate }}
        </label>
        <input
          id="orderNOId"
          type="text"
          [(ngModel)]="orderNOId"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
    </div>


    <div class="row">
      
      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.SalesOrder" | translate }}</div>

        <dx-check-box
          [value]="salesOrder"
          [(ngModel)]="salesOrder"
          valueExpr="salesOrder"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.NotCompleted" | translate }}</div>

        <dx-check-box
          [value]="NotCompleted"
          [(ngModel)]="NotCompleted"
          valueExpr="NotCompleted"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Draft" | translate }}</div>

        <dx-check-box
          [value]="Draft"
          [(ngModel)]="Draft"
          valueExpr="Draft"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">
          {{ "COMMON.Done" | translate }}
        </div>

        <dx-check-box
          [value]="Done"
          [(ngModel)]="Done"
          valueExpr="Done"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.AllSalesOrder" | translate }}</div>

        <dx-check-box
          [value]="allSalesOrder"
          [(ngModel)]="allSalesOrder"
          valueExpr="allSalesOrder"
        ></dx-check-box>
      </div>
      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.AllOrders" | translate }}</div>

        <dx-check-box
          [value]="AllOrders"
          [(ngModel)]="AllOrders"
          valueExpr="AllOrders"
        ></dx-check-box>
      </div>
    </div>
  </div>
</div>

<dx-data-grid
  id="girdcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="ID"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column dataField="ID" [caption]="'COMMON.ID' | translate"></dxi-column>
  <dxi-column
    dataField="Item_Id"
    [caption]="'COMMON.Item_Id' | translate"
  ></dxi-column>
  <dxi-column
    dataField="item"
    [caption]="'COMMON.item' | translate"
  ></dxi-column>
  <dxi-column
    dataField="quantity"
    [caption]="'COMMON.quantity' | translate"
  ></dxi-column>
  <dxi-column
    dataField="unit"
    [caption]="'COMMON.unit' | translate"
  ></dxi-column>
  <dxi-column
    dataField="customer_name"
    [caption]="'COMMON.customer_name' | translate"
  ></dxi-column>
  <dxi-column
    dataField="action_date"
    [caption]="'COMMON.action_date' | translate"
  ></dxi-column>
  <dxi-column
    dataField="total"
    [caption]="'COMMON.total' | translate"
  ></dxi-column>
  <dxi-column
    dataField="performa_date"
    [caption]="'COMMON.performa_date' | translate"
  ></dxi-column>
  <dxi-column
    dataField="orderdate"
    [caption]="'COMMON.orderdate' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Stateus"
    [caption]="'COMMON.Stateus' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Signed"
    [caption]="'COMMON.Signed' | translate"
  ></dxi-column>
  <dxi-column
    dataField="notes"
    [caption]="'COMMON.notes' | translate"
  ></dxi-column>
  <dxi-column
    dataField="SaleUnitID"
    [caption]="'COMMON.SaleUnitID' | translate"
  ></dxi-column>
  <dxi-column
    dataField="QStill"
    [caption]="'COMMON.QStill' | translate"
  ></dxi-column>
  <dxi-column
    dataField="QRuning"
    [caption]="'COMMON.QRuning' | translate"
  ></dxi-column>
  <dxi-column
    dataField="QDone"
    [caption]="'COMMON.QDone' | translate"
  ></dxi-column>
  <dxi-column
    dataField="OperatingNotes"
    [caption]="'COMMON.OperatingNotes' | translate"
  ></dxi-column>
  <dxi-column
    dataField="performa_id"
    [caption]="'COMMON.performa_id' | translate"
  ></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
