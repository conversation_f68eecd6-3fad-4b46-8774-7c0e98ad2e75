import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AnnualSalesReportComponent} from './annual-sales-report/annual-sales-report.component';
import {ContractsReportComponent} from './contracts-report/contracts-report.component';
import {CustomerProductsReportComponent} from './customer-products-report/customer-products-report.component';
import {DeliveryOrderReportComponent} from './delivery-order-report/delivery-order-report.component';
import {DeliveryReportComponent} from './delivery-report/delivery-report.component';
import {InvoiceEarningsReportComponent} from './invoice-earnings-report/invoice-earnings-report.component';
import {InvoicesReportComponent} from './invoices-report/invoices-report.component';
import {NetSalesComponent} from './net-sales/net-sales.component';
import {ProductOrdersReportComponent} from './product-orders-report/product-orders-report.component';
import {ProductSaleReportComponent} from './product-sale-report/product-sale-report.component';
import {RentReportComponent} from './rent-report/rent-report.component';
import {SalesReportComponent} from './sales-report/sales-report.component';
import {SalesReturnReportComponent} from './sales-return-report/sales-return-report.component';
import {
    SalespersonCommissionsPercentageComponent
} from './salesperson-commissions-percentage/salesperson-commissions-percentage.component';
import {
    SalespersonCommissionsValueComponent
} from './salesperson-commissions-value/salesperson-commissions-value.component';
import {SalespersonCustomerReportComponent} from './salesperson-customer-report/salesperson-customer-report.component';
import {TotalSalesReportComponent} from './total-sales-report/total-sales-report.component';
import { SalesTaxComponent } from './sales-tax/sales-tax.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'sales_report',
        pathMatch: 'full'
    },
    {
        path: 'sales_report',
        component: SalesReportComponent
    },
   {
        path: 'sales_tax',
        component: SalesTaxComponent
    },
    {
        path: 'sales_return_report',
        component: SalesReturnReportComponent
    },
    {
        path: 'invoices_report',
        component: InvoicesReportComponent
    },
    {
        path: 'customer_products_report',
        component: CustomerProductsReportComponent
    },
    {
        path: 'annual_sales_report',
        component: AnnualSalesReportComponent
    },
    {
        path: 'salesperson_customer_report',
        component: SalespersonCustomerReportComponent
    },
    {
        path: 'invoice_earnings_report',
        component: InvoiceEarningsReportComponent
    },
    {
        path: 'salesperson_commissions_percentage',
        component: SalespersonCommissionsPercentageComponent
    },
    {
        path: 'salesperson_commissions_value',
        component: SalespersonCommissionsValueComponent
    },
    {
        path: 'total_sales_report',
        component: TotalSalesReportComponent
    },
    {
        path: 'delivery_order_report',
        component: DeliveryOrderReportComponent
    },
    {
        path: 'rent_report',
        component: RentReportComponent
    },
    {
        path: 'product_orders_report',
        component: ProductOrdersReportComponent
    },
    {
        path: 'net_sales',
        component: NetSalesComponent
    },
    {
        path: 'contracts_report',
        component: ContractsReportComponent
    },
    {
        path: 'delivery_report',
        component: DeliveryReportComponent
    },
    {
        path: 'product_sale_report',
        component: ProductSaleReportComponent
    },


];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SalesReportsRoutingModule {
}
