<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.SalesReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Salse.SalesReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <div class="main-inputs">
    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label col-xl-2 col-md-4 col-sm-12">{{
          "COMMON.FromDate" | translate
        }}</label>
        <input
          id="fromDate"
          type="date"
          [(ngModel)]="fromDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label col-xl-2 col-md-4 col-sm-12">{{
          "COMMON.ToDate" | translate
        }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>

      <!-- <dx-load-panel #loadPanel shadingColor="rgba(0,0,0,0.4)" [position]="{ of: '#employee' }"
      [(visible)]="loadingVisible" [showIndicator]="true" [showPane]="true" [shading]="true"
      message="{{ 'COMMON.LOADING' | translate }}" [hideOnOutsideClick]="false">
    </dx-load-panel> -->
    </div>

    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="customer" class="form-label col-xl-2 col-md-4 col-sm-12">
          {{ "COMMON.Customer" | translate }}
        </label>
        <ng-select
          id="customer"
          [(ngModel)]="customerId"
          bindLabel="nameAr"
          bindValue="id"
          [items]="customers"
        ></ng-select>
      </div>

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="warehouses" class="form-label col-xl-2 col-md-4 col-sm-12">
          {{ "COMMON.Warehouse" | translate }}
        </label>
        <ng-select
          id="customer"
          [(ngModel)]="warehouseId"
          bindLabel="name"
          bindValue="id"
          [items]="warehouses"
        ></ng-select>
      </div>
    </div>

    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="product" class="form-label col-xl-2 col-md-4 col-sm-12">
          {{ "COMMON.TheProduct" | translate }}
        </label>
        <ng-select
          id="product"
          [(ngModel)]="productId"
          bindLabel="productName"
          bindValue="productId"
          [items]="products"
        ></ng-select>
      </div>

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label
          for="SalesPersonid"
          class="form-label col-xl-2 col-md-4 col-sm-12"
        >
          {{ "COMMON.Salesperson" | translate }}
        </label>
        <ng-select
          id="SalesPersonid"
          [(ngModel)]="SalesPersonid"
          bindLabel="nameAr"
          bindValue="id"
          [items]="SalesPersons"
        ></ng-select>
      </div>
    </div>

    <div class="row">
      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Services" | translate }}</div>

        <dx-check-box
          [value]="services"
          [(ngModel)]="services"
          valueExpr="services"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <div class="label p-2">
          {{ "COMMON.Product" | translate }}
        </div>

        <dx-check-box
          [value]="product"
          [(ngModel)]="product"
          valueExpr="product"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Detaild" | translate }}</div>

        <dx-check-box
          [value]="isDetails"
          [(ngModel)]="isDetails"
          valueExpr="isDetails"
        ></dx-check-box>
      </div>

      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Taxable" | translate }}</div>

        <dx-check-box
          [value]="isTaxable"
          [(ngModel)]="isTaxable"
          valueExpr="isTaxable"
        ></dx-check-box>
      </div>
      <div class="form-group col-xl-1 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.NonTaxable" | translate }}</div>

        <dx-check-box
          [value]="isNonTaxable"
          [(ngModel)]="isNonTaxable"
          valueExpr="isNonTaxable"
        ></dx-check-box>
      </div>
    </div>
  </div>
</div>


<dx-data-grid
  id="girdcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="InooiceNOVender"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="InooiceNOVender"
    [caption]="'COMMON.InooiceNOVender' | translate"
  ></dxi-column>
  <dxi-column
    dataField="BranchName"
    [caption]="'COMMON.Branch' | translate"
  ></dxi-column>
  <dxi-column
    dataField="store_name"
    [caption]="'COMMON.Warehouse' | translate"
  ></dxi-column>
  <dxi-column
    dataField="itemid"
    [caption]="'COMMON.ProductCode' | translate"
  ></dxi-column>
  <dxi-column
    dataField="ITEMS_ITEM_NAME"
    [caption]="'COMMON.ProductName' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Barcode"
    [caption]="'COMMON.Barcode' | translate"
  ></dxi-column>
  <dxi-column
    dataField="BankAmount"
    [caption]="'COMMON.BankAmount' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Journal"
    [caption]="'COMMON.Journal' | translate"
  ></dxi-column>
  <dxi-column
    dataField="unit"
    [caption]="'COMMON.UnitName' | translate"
  ></dxi-column>
  <!-- <dxi-column
    dataField="aTotal"
    [caption]="'COMMON.Total' | translate"
  ></dxi-column> -->
  <dxi-column
    dataField="TotalPrice"
    [caption]="'COMMON.Total' | translate"
  ></dxi-column>
  <dxi-column
    dataField="customer_name"
    [caption]="'COMMON.Customer' | translate"
  ></dxi-column>
  <dxi-column
    dataField="MandopName"
    [caption]="'COMMON.Salesperson' | translate"
  ></dxi-column>
    <!-- <dxi-column
      dataField="monycome"
      [caption]="'COMMON.monycome' | translate"
    ></dxi-column> -->
  <dxi-column
    dataField="Descount"
    [caption]="'COMMON.Descount' | translate"
  ></dxi-column>
  <dxi-column
    dataField="DiscountCalculated"
    [caption]="'COMMON.DiscountCalculated' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Discount_Rate"
    [caption]="'CUSTOMER.DISCOUNT_PERCENTAGE' | translate"
  ></dxi-column>
  <dxi-column dataField="net" [caption]="'COMMON.Net' | translate"></dxi-column>
  <dxi-column
    dataField="invtype"
    [caption]="'COMMON.InvoiceType' | translate"
  ></dxi-column>
  <dxi-column
    dataField="servicetype"
    [caption]="'COMMON.SalesType' | translate"
  ></dxi-column>
  <dxi-column
    dataField="DriverName"
    [caption]="'COMMON.DriverName' | translate"
  ></dxi-column>
  <dxi-column
    dataField="ExtraMoney_Value"
    [caption]="'COMMON.ExtraMoney_Value' | translate"
  ></dxi-column>

  <!-- <dxi-column dataField="depname" caption="Depart Name">
      <dxo-header-filter>
        <dxo-search [enabled]="true"></dxo-search>
      </dxo-header-filter>
    </dxi-column>


    <dxi-column dataField="name">
      <dxo-header-filter>
        <!-<dxo-search [enabled]="true"-->
  <!--[searchExpr]="searchExpr"
        [editorOptions]="editorOptions"></dxo-search>-->
  <!-- </dxo-header-filter>
    </dxi-column> -->

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
