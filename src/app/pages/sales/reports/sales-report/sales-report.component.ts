import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { formatDate } from '@angular/common';
import { SalesService } from '../../sales.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { FormControl } from '@angular/forms';
import ArrayStore from 'devextreme/data/array_store';

@Component({
    selector: 'app-sales-report',
    templateUrl: './sales-report.component.html',
    styleUrls: ['./sales-report.component.scss'],
    standalone: false
})
export class SalesReportComponent implements OnDestroy {

  moduleName = 'Salse.SalesReport';
  customers: any[];
  warehouses: any[];
  warehouse: any[];
  products: any[];
  SalesPersons: any[];
  customerId: any = 0;
  warehouseId:any=0;
  productId:any=0;
  SalesPersonid: number = 0;
  data: any[];
  gridDataSource: any[];
  fromDateValue: any;
  toDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  services: boolean = false;
  product: boolean = false;
  isDetails: boolean = false;
  isTaxable: boolean = false;
  isNonTaxable: boolean = false;
  isAdmin: boolean = true;
 subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  _currentPage = 1;
dataSource: ArrayStore;
  isRtl: boolean = document.documentElement.dir === 'rtl';
     menuOpen = false;
     toggleMenu() {
      this.menuOpen = !this.menuOpen;
      }
      actionsList: string[] = [
         'Export',
         'Send via SMS',
         'Send Via Email',
         'Send Via Whatsapp',
           'print',
            ];
         modalConfig: ModalConfig = {
         modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
        return true;
         },
       dismissButtonLabel: 'Cancel',
        };

         smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
         emailModalConfig: ModalConfig = {
           ...this.modalConfig,
           modalTitle: 'Send Email',
            };
        whatsappModalConfig: ModalConfig = {
          ...this.modalConfig,
          modalTitle: 'Send Whatsapp',
            };
     @ViewChild('smsModal') private smsModal: ModalComponent;
     @ViewChild('emailModal') private emailModal: ModalComponent;
     @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(private service: SalesService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.SalesReports}SalesReport`;


    this.subscription.add(service.getwarehouses().subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cdk.detectChanges();
      }
    }));

   this.subscription.add(service.getCustDropdown().subscribe((r) => {
  if (r.success) {
    this.customers = r.data;
    this.cdk.detectChanges();
  }
}));
this.subscription.add(
  service.getProductList().subscribe((r) => {
    if (r.success) {
      this.products = r.data;
      this.cdk.detectChanges();
    }
  })
);
this.subscription.add(service.getSalesPersons().subscribe(r => {
  if (r.success) {
    this.SalesPersons = r.data;
    this.cdk.detectChanges();
  }
}));



    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );

  }


  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Sales_Report');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Sales_Report.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'Sales_Report.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const ShowServiceInvoces = this.services;
    const ShowGoodInvoices = this.product;
    const IsDetails = this.isDetails;
    const CustomerId = this.customerId?? 0;
    const WarehousesId = this.warehouse?? 0
    const ProdcutId = this.productId?? 0;
    const SalespesronId = this.SalesPersonid?? 0;
    const ShowNonTaxInvoices = this.isNonTaxable;
    const ShowTaxInvoices = this.isTaxable;
    const IsAdmin= this.isAdmin;
    this.subscription.add(this.service.list({
      FromDate,
      ToDate,
      ShowServiceInvoces,
      ShowGoodInvoices,
      IsDetails,
      CustomerId,
      WarehousesId,
      ProdcutId,
      SalespesronId,
      ShowNonTaxInvoices,
      ShowTaxInvoices, 
      IsAdmin,

    }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }


  openSmsModal() {
    this.smsModal.open()
  }
  openWhatsappModal() {
    this.whatsappModal.open()
  }
  openEmailModal() {
    this.emailModal.open()
  }

}


