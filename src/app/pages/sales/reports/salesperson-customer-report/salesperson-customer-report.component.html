<div class="card-body border-top">

  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0"> {{ "COMMON.SalespersonCustomerReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
         
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'SalespersonCustomer'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>



  <div class="main-inputs">


   <div class="row">

    <div class="form-group col-xl-4 col-md-4 col-sm-12">
      <label for="SalesPersonid" class="form-label">
        {{ "COMMON.Salesperson" | translate }}
      </label>
      <ng-select
        id="SalesPersonid"
        [(ngModel)]="SalesPersonid"
        bindLabel="nameAr"
        bindValue="id"
        [items]="SalesPersons"
      ></ng-select>
    </div>

  </div>


    <!-- <div class="row">
  
      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Detaild" | translate }}</div>

        <dx-check-box
          [value]="isDetails"
          [(ngModel)]="isDetails"
          valueExpr="isDetails"
        ></dx-check-box>
      </div> 

       <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <div class="label p-2">
          {{ "COMMON.DontShowBreviousBalance" | translate }}
        </div>

        <dx-check-box
          [value]="showPreviousBalance"
          [(ngModel)]="showPreviousBalance"
          valueExpr="showPreviousBalance"
        ></dx-check-box>
      </div>
      
    </div> -->


   
  </div>
</div>



 <dx-data-grid id="girdcontrole"
[rtlEnabled]="true"  
                [dataSource]="data"
                keyExpr="ActivityCode"
                [showRowLines]="true"
                [showBorders]="true"
                [columnAutoWidth]="true"
                (onExporting)="onExporting($event)"
                [allowColumnResizing]="true">
  
    <dxo-filter-row [visible]="true"
                    [applyFilter]="currentFilter"></dxo-filter-row>
  
    <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
    <dxo-paging [pageSize]="10"> </dxo-paging>
    <dxo-pager [visible]="true"
               [allowedPageSizes]="[5, 10, 'all']"
               [displayMode]="'compact'"
               [showPageSizeSelector]="true"
               [showInfo]="true"
               [showNavigationButtons]="true">
    </dxo-pager>
    <dxo-header-filter [visible]="true"></dxo-header-filter>
  
    <dxo-search-panel [visible]="true"
                      [highlightCaseSensitive]="true"></dxo-search-panel>
  
     

    <dxi-column dataField="ActivityCode" [caption]="'COMMON.ActivityCode' | translate"></dxi-column>
    <dxi-column dataField="Customer_Name_EN" [caption]="'COMMON.Customer' | translate"></dxi-column>
    <dxi-column dataField="MandopName" [caption]="'COMMON.Salesperson' | translate"></dxi-column>
<dxi-column dataField="Short_Name_Ar" [caption]="'COMMON.Short_Name_Ar' | translate"></dxi-column>
<dxi-column dataField="Customer_id" [caption]="'COMMON.CustomerId' | translate"></dxi-column>
<dxi-column dataField="customer_name" [caption]="'COMMON.Customer' | translate"></dxi-column>
<dxi-column dataField="Nationality" [caption]="'COMMON.Nationality' | translate"></dxi-column>
<dxi-column dataField="ProunchID" [caption]="'COMMON.Branch' | translate"></dxi-column>
<dxi-column dataField="Qualification" [caption]="'COMMON.Qualification' | translate"></dxi-column>
<dxi-column dataField="governate" [caption]="'COMMON.governate' | translate"></dxi-column>
<dxi-column dataField="mobile" [caption]="'COMMON.Mobile' | translate"></dxi-column>
<!-- <dxi-column dataField="user_id" [caption]="'COMMON.user_id' | translate"></dxi-column> -->
<!-- <dxi-column dataField="Mo7tagazat_Name" [caption]="'COMMON.Mo7tagazat_Name' | translate"></dxi-column>
<dxi-column dataField="Mokadama_Name" [caption]="'COMMON.Mokadama_Name' | translate"></dxi-column> -->
<!-- <dxi-column dataField="IncomAccountName" [caption]="'COMMON.IncomAccountName' | translate"></dxi-column> -->
<!-- <dxi-column dataField="ID_Number" [caption]="'COMMON.ID_Number' | translate"></dxi-column> -->
<dxi-column dataField="HomePhone" [caption]="'COMMON.Phone' | translate"></dxi-column>
<!-- <dxi-column dataField="Dman_Name" [caption]="'COMMON.Dman_Name' | translate"></dxi-column> -->
<dxi-column dataField="Discount_percent" [caption]="'COMMON.Discount_percent' | translate"></dxi-column>
<dxi-column dataField="DiscountAccountName" [caption]="'COMMON.DiscountAccountName' | translate"></dxi-column>
<dxi-column dataField="DiscountAccount" [caption]="'COMMON.DiscountAccount' | translate"></dxi-column>
<dxi-column dataField="DeductionsAccountName" [caption]="'COMMON.DeductionsAccountName' | translate"></dxi-column>




    <dxo-export [enabled]="true"
                [formats]="['pdf','excel']">
    </dxo-export>
  
  
    <dxo-summary>
      <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
      <dxi-total-item column="date"
                      summaryType="min">
        [customizeText]="customizeDate"
      </dxi-total-item>
  
      <dxi-total-item column="val"
                      summaryType="sum"
                      valueFormat="currency">
      </dxi-total-item>
  
    </dxo-summary>
  
  </dx-data-grid>
  