<div class="card-body border-top">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.InvoiceEarningsReport" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Salse.InvoiceEarningsReport'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Openingbalance'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <div class="main-inputs">
    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label col-xl-4 col-md-4 col-sm-12">{{ "COMMON.FromDate" | translate }}</label>
        <input
          id="fromDate"
          type="date"
          [(ngModel)]="fromDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label class="form-label col-xl-4 col-md-4 col-sm-12">{{ "COMMON.ToDate" | translate }}</label>
        <input
          id="toDate"
          type="date"
          [(ngModel)]="toDateValue"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>

      <!-- <dx-load-panel #loadPanel shadingColor="rgba(0,0,0,0.4)" [position]="{ of: '#employee' }"
      [(visible)]="loadingVisible" [showIndicator]="true" [showPane]="true" [shading]="true"
      message="{{ 'COMMON.LOADING' | translate }}" [hideOnOutsideClick]="false">
    </dx-load-panel> -->
    </div>

    <div class="row">
      <div class="form-group col-xl-4 col-md-4 col-sm-12">
        <label for="SalesPersonid" class="form-label col-xl-4 col-md-4 col-sm-12">
          {{ "COMMON.Salesperson" | translate }}
        </label>
        <ng-select
          id="SalesPersonid"
          [(ngModel)]="SalesPersonid"
          bindLabel="nameAr"
          bindValue="id"
          [items]="SalesPersons"
        ></ng-select>
      </div>
      <div class="form-group col-xl-4 col-md-2 col-sm-12">
        <label
          for="Indirectcostid"
          class="form-label col-xl-4 col-md-4 col-sm-12"
        >
          {{ "COMMON.Indirectcost" | translate }}
        </label>
        <input
          id="Indirectcostid"
          type="text"
          [(ngModel)]="Indirectcostid"
          class="form-control"
          style="background-color: #f5f8fa"
        />
      </div>
    </div>

    <!-- <div class="row">

      <div class="form-group col-xl-2 col-md-4 col-sm-12">
        <div class="label p-2">{{ "COMMON.Detaild" | translate }}</div>

        <dx-check-box
          [value]="isDetails"
          [(ngModel)]="isDetails"
          valueExpr="isDetails"
        ></dx-check-box>
      </div>

       <div class="form-group col-xl-3 col-md-4 col-sm-12">
        <div class="label p-2">
          {{ "COMMON.DontShowBreviousBalance" | translate }}
        </div>

        <dx-check-box
          [value]="showPreviousBalance"
          [(ngModel)]="showPreviousBalance"
          valueExpr="showPreviousBalance"
        ></dx-check-box>
      </div>

    </div> -->
  </div>
</div>

<dx-data-grid
  id="girdcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="InvoiceNo"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
>
  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column
    dataField="InvoiceNo"
    [caption]="'COMMON.InvoiceNo' | translate"
  ></dxi-column>
  <dxi-column
    dataField="idate"
    [caption]="'COMMON.InvoiceDate' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Tahseel"
    [caption]="'COMMON.Tahseel' | translate"
  ></dxi-column>
  <dxi-column
    dataField="servicetype"
    [caption]="'COMMON.servicetype' | translate"
  ></dxi-column>
  <dxi-column
    dataField="customer_name"
    [caption]="'COMMON.Customer' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Mandop_Name"
    [caption]="'COMMON.Salesperson' | translate"
  ></dxi-column>
  <dxi-column
    dataField="store_id"
    [caption]="'COMMON.Warehouse' | translate"
  ></dxi-column>
  <dxi-column
    dataField="TotalPrice"
    [caption]="'COMMON.Total' | translate"
  ></dxi-column>
  <dxi-column
    dataField="tahseldate"
    [caption]="'COMMON.CollectedDate' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Discount"
    [caption]="'COMMON.Descount' | translate"
  ></dxi-column>
  <dxi-column
    dataField="ItemMaksab"
    [caption]="'COMMON.Profit' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Vmaksap_Perce"
    [caption]="'COMMON.ProfitPercentage' | translate"
  ></dxi-column>
  <dxi-column
    dataField="Indirect_Cost"
    [caption]="'COMMON.Indirect_Cost' | translate"
  ></dxi-column>
  <dxi-column
    dataField="NetCost"
    [caption]="'COMMON.Net' | translate"
  ></dxi-column>
  <dxi-column
    dataField="PriceOne"
    [caption]="'COMMON.PriceOne' | translate"
  ></dxi-column>
  <!-- <dxi-column
    dataField="TotalCost"
    [caption]="'COMMON.TotalCost' | translate"
  ></dxi-column> -->
  <!-- <dxi-column
    dataField="ValPonas"
    [caption]="'COMMON.ValPonas' | translate"
  ></dxi-column> -->
  <!-- <dxi-column
    dataField="itemid"
    [caption]="'COMMON.itemid' | translate"
  ></dxi-column> -->

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="name" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="date" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="val" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
