import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { SalesService } from '../../sales.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';
import ArrayStore from 'devextreme/data/array_store';

@Component({
    selector: 'app-customer-products-report',
    templateUrl: './customer-products-report.component.html',
    styleUrls: ['./customer-products-report.component.scss'],
    standalone: false
})
export class CustomerProductsReportComponent implements OnDestroy {
  moduleName = 'CustomerProducts';
  customers: any[];
  warehouses: any[];
  warehouse: any[];
  products: any[];
  maingroups: any[];
  Supgroups: any[];
  SalesPersons: any[];
  customerId: any = 0;
  warehouseId:any=0;
  productId:any=0;
  mingroupid: number = 0;
  supgroupid: number=0;
  data: any[];
  gridDataSource: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  prevDateButton: any;
  fromprevDateButton: any;
  todayButton: any;
  fromtodayButton: any;
  nextDateButton: any;
  fromnextDateButton: any;
  millisecondsInDay = 24 * 60 * 60 * 1000;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  isTotaly: boolean = true;


dataSource: ArrayStore;
  isRtl: boolean = document.documentElement.dir === 'rtl';
     menuOpen = false;
     toggleMenu() {
      this.menuOpen = !this.menuOpen;
      }
      actionsList: string[] = [
         'Export',
         'Send via SMS',
         'Send Via Email',
         'Send Via Whatsapp',
           'print',
            ];
         modalConfig: ModalConfig = {
         modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
        return true;
         },
       dismissButtonLabel: 'Cancel',
        };
          
         smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
         emailModalConfig: ModalConfig = {
           ...this.modalConfig,
           modalTitle: 'Send Email',
            };
        whatsappModalConfig: ModalConfig = {
          ...this.modalConfig,
          modalTitle: 'Send Whatsapp',
            };
     @ViewChild('smsModal') private smsModal: ModalComponent;
     @ViewChild('emailModal') private emailModal: ModalComponent;
     @ViewChild('whatsappModal') private whatsappModal: ModalComponent;


  constructor(private service: SalesService, private cdk: ChangeDetectorRef) {
    service.baseUrl = `${environment.appUrls.SalesReports}CustomerProductsReport`;
    this.subscription.add(service.getProductCategories().subscribe(r => {
      if (r.success) {
        this.maingroups = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getContactsDropdown().subscribe(r => {
      if (r.success) {
        this.gridDataSource = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(service.getwarehouses().subscribe(r => {
      if (r.success) {
        this.warehouses = r.data;
        this.cdk.detectChanges();
      }
    }));
    this.subscription.add(
      service.getProductList().subscribe((r) => {
        if (r.success) {
          this.products = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.customers = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();

    this.todayButton = {
      text: 'Today',
      onClick: () => {
        this.toDateValue = new Date().getTime();
        this.cdk.detectChanges();
      },
    };
    this.fromtodayButton = {
      text: 'Today',
      onClick: () => {
        this.fromDateValue = new Date().getTime();
        this.cdk.detectChanges();
      },
    };
    this.prevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue -= this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };

    this.nextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.toDateValue += this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };
    this.fromprevDateButton = {
      icon: 'spinprev',
      stylingMode: 'text',
      onClick: () => {
        this.fromDateValue -= this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };

    this.fromnextDateButton = {
      icon: 'spinnext',
      stylingMode: 'text',
      onClick: () => {
        this.fromDateValue += this.millisecondsInDay;
        this.cdk.detectChanges();
      },
    };
    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];
    const currentYear = new Date().getFullYear();
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }
  onAccountSelect(selectedItem: any) {
    if(!selectedItem) {
      return;
    }
 
    this.Supgroups = this.maingroups
    .filter(r => r.groupid === selectedItem.groupid)
    .flatMap(r => r.productSubCategory);



    this.cdk.detectChanges();

    console.log(this.Supgroups);
    // this.subscription.add(this.service.getProductSubCategoriesById( this.groupId).subscribe(r => {
    //   if (r.success) {
    //     this.Supgroups = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
    
  }

  onItemClick(e: any) {

  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }

  exportToExcel() {
    this.subscription.add(this.service.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'Contracts Report.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const CustomerId = this.customerId?? 0;
    const WarehousesId = this.warehouse?? 0
    const ProdcutId = this.productId?? 0;
    const GroupId = this.mingroupid??0;
    const SubGroupId = this.supgroupid??0;
     const IsTotaly = this.isTotaly??0;
    this.subscription.add(this.service.list({ 
      FromDate,
      ToDate,
      CustomerId,
      WarehousesId,
      ProdcutId,
      GroupId ,
      SubGroupId,
      IsTotaly,

    
    }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cdk.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open()
  }
  openWhatsappModal() {
    this.whatsappModal.open()
  }
  openEmailModal() {
    this.emailModal.open()
  }
}



