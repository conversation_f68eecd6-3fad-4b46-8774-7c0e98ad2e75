<div class="card-header border-0 cursor-pointer w-100">
  <div
    class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
  >
    <h3 class="fw-bolder m-0">Sales Types</h3>
    <div [style.position]="'relative'">
      <div class="btn-group">
      

        <button
          [routerLink]="['/sales/configuration/salesperson_view',selectedItemKeys[0] ]"
          [hidden]="
            selectedItemKeys.length > 1 || selectedItemKeys.length == 0
          "
          class="btn btn-sm btn-active-light-primary"
          *hasPermission="{
            module: 'sales.SalesType',
            action: 'updateAction'
          }"
        >
          {{ "COMMON.EDIT" | translate }}
          <i class="fa fa-edit"></i>
        </button>
      </div>

      <button
        class="btn btn-icon btn-active-light-primary mx-2"
        (click)="toggleMenu()"
        data-bs-toggle="tooltip"
        data-bs-placement="top"
        data-bs-trigger="hover"
        title="Settings"
      >
        <i class="fa fa-gear"></i>
      </button>

      <div
        class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
        [class.show]="menuOpen"
        [style.position]="'absolute'"
        [style.top]="'100%'"
        [style.zIndex]="'1050'"
        [style.left]="isRtl ? '0' : 'auto'"
        [style.right]="!isRtl ? '0' : 'auto'"
      >

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            (click)="exportToExcel()"
            data-kt-company-table-filter="delete_row"
          >{{ "COMMON.ExportToExcel"| translate }} </a>
        </div>

        <div class="menu-item px-3">
          <a
            class="menu-link px-3"
            (click)="uploadExcel()"
            data-kt-company-table-filter="delete_row"
          >
            {{ "COMMON.ImportToExcel" | translate }}
          </a>
        </div>

        <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'Salse.Salesperson'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses"
            data-kt-company-table-filter="delete_row"
            >{{ "COMMON.Print" | translate }}</a
          >
        </div>


      </div>
    </div>
  </div>
</div>




<dx-data-grid id="gridcontrole"  [rtlEnabled]="true"  [dataSource]="data" keyExpr="id" [showRowLines]="true" [showBorders]="true"
              [columnAutoWidth]="true" (onExporting)="onExporting($event)" [allowColumnResizing]="true" [remoteOperations]="true"
              [repaintChangesOnly]="true" (onSaving)="onSaving($event)">

  <dxo-editing mode="batch" [allowAdding]="true" [allowDeleting]="true" [allowUpdating]="true">

  </dxo-editing>

  <dxo-selection mode="single"></dxo-selection>
  <dxo-filter-row [visible]="true" [applyFilter]="currentFilter"></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager [visible]="true" [allowedPageSizes]="[5, 10, 'all']" [displayMode]="'compact'"
             [showPageSizeSelector]="true" [showInfo]="true" [showNavigationButtons]="true">
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>

  <dxi-column dataField="id" caption="id" [allowEditing]="false"></dxi-column>

  <dxi-column dataField="nameAr" caption="Name Ar"></dxi-column>
  <dxi-column dataField="nameEn" caption="Name En"></dxi-column>



  <dxo-export [enabled]="true" [formats]="['pdf','excel']">
  </dxo-export>

  <dxo-summary>
    <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
