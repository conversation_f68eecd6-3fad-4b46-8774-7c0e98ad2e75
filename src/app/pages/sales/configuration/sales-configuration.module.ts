import { NgModule } from '@angular/core';
import {CustomersCategoryComponent} from './customers-category/customers-category.component';
import {CustomersComponent} from './customers/customers.component';
import {GuarantorComponent} from './guarantor/guarantor.component';
import {MainCustomerComponent} from './main-customer/main-customer.component';
import {PricelistsComponent} from './pricelists/pricelists.component';
import {SalesAndCommissionTermsComponent} from './sales-and-commission-terms/sales-and-commission-terms.component';
import {SalesTypesComponent} from './sales-types/sales-types.component';
import {SalespersonCommissionComponent} from './salesperson-commission/salesperson-commission.component';
import {SalespersonComponent} from './salesperson/salesperson.component';
import {SalesTeamsComponent} from './sales-teams/sales-teams.component';
import {SalespersonCategoriesComponent} from './salesperson-categories/salesperson-categories.component';
import {SalespersonViewComponent} from './salesperson/salesperson-view/salesperson-view.component';
import {GuarantorViewComponent} from './guarantor/guarantor-view/guarantor-view.component';
import {CustomerViewComponent} from './customers/customer-view/customer-view.component';
import {SalesPaymentTermsComponent} from './sales-payment-terms/sales-payment-terms.component';
import {SalesConfigurationRoutingModule} from "./sales-configuration-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {ProductsGridControleModule} from "../../general/products-grid-controle/ProductsGridControle.module";
import {RouterModule} from "@angular/router";
import { CustomerSalesPersonComponent } from './customers/customer-view/customer-sales-person/customer-sales-person.component';
import { CustomerRelativesComponent } from './customers/customer-view/customer-relatives/customer-relatives.component';
import { CustomerOfficialsComponent } from './customers/customer-view/customer-officials/customer-officials.component';
import { Sales_and_commission_terms_viewComponent } from './sales-and-commission-terms/sales_and_commission_terms_view/sales_and_commission_terms_view.component';
import { Sales_payment_terms_viewComponent } from './sales-payment-terms/sales_payment_terms_view/sales_payment_terms_view.component';

@NgModule({
    declarations: [
    SalespersonComponent,
    SalesTypesComponent,
    SalespersonCommissionComponent,
    CustomersCategoryComponent,
    SalesAndCommissionTermsComponent,
    GuarantorComponent,
    MainCustomerComponent,
    CustomersComponent,
    PricelistsComponent,
    SalesTeamsComponent,
    SalespersonCategoriesComponent,
    SalespersonViewComponent,
    GuarantorViewComponent,
    CustomerViewComponent,
    SalesPaymentTermsComponent,
    CustomerSalesPersonComponent,
    CustomerRelativesComponent,
    CustomerOfficialsComponent,
    Sales_and_commission_terms_viewComponent,
    Sales_payment_terms_viewComponent
  ],
  imports: [
    SalesConfigurationRoutingModule,
    SharedModule,
    ProductsGridControleModule
  ],
  exports: [RouterModule],
})

export class SalesConfigurationModule { }
