import { ChangeDetectorR<PERSON>, Component, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { lastValueFrom, Subscription } from 'rxjs';
import DxDataGrid from 'devextreme/ui/data_grid';
import { DxDataGridComponent } from 'devextreme-angular';
import { environment } from '../../../../../environments/environment';
import { customersService } from './customersService';
import { <PERSON>dal<PERSON>omponent, ModalConfig } from 'src/app/_metronic/partials';
import ArrayStore from 'devextreme/data/array_store';

@Component({
    selector: 'app-customers',
    templateUrl: './customers.component.html',
    styleUrls: ['./customers.component.scss'],
    standalone: false
})
export class CustomersComponent implements OnInit, OnDestroy {
  moduleName = 'Salse.Customers';
dataSource: ArrayStore;
  data: any[] = [];
  subscriptions = new Subscription();
  newInquiryFrm: FormGroup;
  searchCtrl: FormControl;
  statusFilterCtrl: FormControl;
  companyFilterCtrl: FormControl;
  selectedItemKeys: any = [];
  companies: [];
  _currentPage = 1;
  itemsCount = 0;
  statuses: any[] = [{ name: 'تمت', value: true }, { name: 'لم تتم', value: false }];
  editMode: boolean = false;
  pagesCount = 0;
  currentFilter: any;

   isRtl: boolean = document.documentElement.dir === 'rtl';

    menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
     actionsList: string[] = [
        'Export',
        'Send via SMS',
        'Send Via Email',
        'Send Via Whatsapp',
        'print',
      ];
      modalConfig: ModalConfig = {
        modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
          return true;
        },
        dismissButtonLabel: 'Cancel',
      };

      smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
      emailModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Email',
      };
      whatsappModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Whatsapp',
      };
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;
@ViewChild(DxDataGridComponent, { static: false })
dataGrid: DxDataGridComponent;


  constructor(private fb: FormBuilder,
    private myService: customersService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef) {
    this.newInquiryFrm = this.fb.group({
      id: null,
      notes: [null, Validators.required]
    });
    this.searchCtrl = this.fb.control(null);
    this.statusFilterCtrl = this.fb.control(null);
    this.companyFilterCtrl = this.fb.control(null);
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  selectionChanged(data: any) {
    // تخزين المفاتيح المحددة
    if (data && data.selectedRowKeys && data.selectedRowKeys.length >= 0) {
      this.selectedItemKeys = [...data.selectedRowKeys];
      console.log('Selected rows:', this.selectedItemKeys);
    }
    // تطبيق التغييرات
    setTimeout(() => {
      this.cd.detectChanges();
    }, 0);
  }

  ngOnInit(): void {
    this.subscriptions.add(this.route.paramMap.subscribe(p => {
      const id = p.get('id') || '';
      //if (id) {
      this.subscriptions.add(this.myService.list(id).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
      //  }
    }));
    this.searchCtrl.valueChanges.subscribe(v => {
      const id = this.route.snapshot.params.id;
      if (v) {
        this.subscriptions.add(this.myService.search(v).subscribe(r => {
          if (r.success) {
            this.loadData(r);
            this.itemsCount = r.data.itemsCount;
          }
        }));
      } else if (id) {
        this.subscriptions.add(this.myService.list(id).subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      } else {
        this.subscriptions.add(this.myService.list('').subscribe(r => {
          if (r.success) {
            this.loadData(r);
          }
        }));
      }
    });
  }

  deleteRecords() {
    this.remove;
  }

  remove() {
    Swal.fire({
      title: 'هل حقاً تريد الحذف',
      showConfirmButton: true,
      showCancelButton: true,
      confirmButtonText: 'نعم',
      cancelButtonText: 'إلغاء',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      icon: 'warning',
    }).then((r) => {
      if (r.isConfirmed) {
        this.selectedItemKeys.forEach((key: any) => {
          this.subscriptions.add(
            this.myService.delete(key).subscribe((r) => {
              if (r.success) {
                this.dataSource.remove(key);
                this.dataGrid.instance.refresh();
                this.cd.detectChanges();
              }
            })
          );
        });
      }
    });
  }








  set currentPage(value: any) {
    this._currentPage = value;
    this.subscriptions.add(this.myService.list('', value).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cd.detectChanges();
        MenuComponent.reinitialization();
      }
    }));
  }

  get currentPage() {
    return this._currentPage;
  }

  loadData(r: any) {
    this.data = r.data;
    if (r.itemsCount) {
      this.itemsCount = r.itemsCount;
    }
    if (r.pagesCount) {
      this.pagesCount = r.pagesCount;
    }
    this.cd.detectChanges();
    MenuComponent.reinitialization();
  }


  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('customers');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });

      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('customers.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscriptions.add(this.myService.exportExcel()
      .subscribe(e => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'customers.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      }));
  }




  uploadExcel() {
    var input = document.createElement('input');
    input.type = "file";
    input.click();
    input.onchange = (e) => {
      if (input.files) {
        if (input.files[0]) {
          const fd = new FormData();
          fd.append('file', input.files[0]);
          this.subscriptions.add(this.myService.importExcel(fd)
            .subscribe(e => {
              if (e) {
                this.currentPage = 1;
              }
            }));
        }
      }
    }
  }

  filter() {

    this.subscriptions.add(this.myService.list({  }).subscribe(r => {
      if (r.success) {
        this.data = r.data;
        this.cd.detectChanges();
      }
    }));

  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }

  onPrint() {
    // استخدام نهج أكثر أماناً لا يتدخل في DOM الأصلي
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لاستخدام وظيفة الطباعة');
      return;
    }

    // إعداد محتوى صفحة الطباعة
    const printContent = `
      <html dir="rtl">
      <head>
        <title>قائمة العملاء</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
          }
          h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
          }
          p.date {
            text-align: left;
            margin-bottom: 10px;
            font-size: 12px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
          }
          th {
            background-color: #f2f2f2;
          }
          @media print {
            @page {
              size: A4 landscape;
              margin: 1cm;
            }
          }
        </style>
      </head>
      <body>
        <h1>قائمة العملاء</h1>
        <p class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>

        <table>
          <tr>
            <th>ID</th>
            <th>الاسم بالعربية</th>
            <th>الاسم بالإنجليزية</th>
            <th>العنوان</th>
          </tr>
          ${this.data.map(customer => `
            <tr>
              <td>${customer.id || ''}</td>
              <td>${customer.nameAr || ''}</td>
              <td>${customer.nameEn || ''}</td>
              <td>${customer.address || ''}</td>
            </tr>
          `).join('')}
        </table>

        <footer style="text-align: center; margin-top: 30px; font-size: 10px; color: #777;">
          ${new Date().getFullYear()} Falcon ERP - جميع الحقوق محفوظة
        </footer>

        <script>
          // الطباعة تلقائياً عند تحميل الصفحة
          window.onload = function() {
            window.print();
            setTimeout(function() {
              window.close();
            }, 500);
          };
        </script>
      </body>
      </html>
    `;

    // كتابة المحتوى إلى النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();
  }
}
