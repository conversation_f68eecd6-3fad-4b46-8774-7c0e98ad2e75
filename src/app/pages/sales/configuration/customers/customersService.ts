 import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class customersService {

  baseUrl: string;
  companyListUrl: string;
  countryListUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.Customers}`;
    this.companyListUrl = `${environment.appUrls.companylist}`;
    this.countryListUrl = `${environment.appUrls.countries}`;
  }




  details(id: string): Observable<any> {
    // Make sure there's a slash between baseUrl and id
    const url = this.baseUrl.endsWith('/') ? this.baseUrl + id : this.baseUrl + '/' + id;
    console.log('Details URL:', url); // Log the URL for debugging
    
    return this.http.get<any>(url);
  }
  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query
      }
    });
  }






  Accounts(): Observable<any> {
    return this.http.get<any>(this.baseUrl + '/ChartofAccounts');
  }



  getbranches(): Observable<any> {
    return this.http.get<any>('api/Branch');
  }



  getCompanies(): Observable<any> {
    return this.http.get<any>(this.companyListUrl);
  }

  getcountries(): Observable<any> {
    return this.http.get<any>(this.countryListUrl);
  }



  getInfo(): Observable<any> {
    return this.http.get<any>(this.baseUrl);
  }
  getSalestypes(): Observable<any> {
    return this.http.get<any>('api/SalesType');
  }

  getNationality(): Observable<any> {
    return this.http.get<any>('api/Nationality');
  }

  
  getSalesPersons(): Observable<any> {
    return this.http.get<any>('api/Salesperson/SalespersonDropdown');
  }
  
  getcompanies(): Observable<any> {
    return this.http.get<any>('/api/CompanyList');
  }

getcountry(): Observable<any> {
    return this.http.get<any>('/api/Country');
  }

  getGovernorates(countryId: number): Observable<any> {
    return this.http.get<any>(`/api/Governorates/bycountry/${countryId}`);
  }

  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + "Guarantor" + 'names');
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    // Make sure there's a slash between baseUrl and id
    const url = this.baseUrl.endsWith('/') ? this.baseUrl + id : this.baseUrl + '/' + id;
    console.log('Update URL:', url, 'Form data:', form); // Log the URL and data for debugging
    
    return this.http.put<any>(url, form)
      .pipe(map(result => {
        console.log('Update result:', result); // Log the result for debugging
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }
}
