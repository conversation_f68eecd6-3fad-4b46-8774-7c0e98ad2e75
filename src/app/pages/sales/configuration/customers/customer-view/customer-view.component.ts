import {ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {CustomerView} from "./CustomerView";
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { salespersonService } from '../../salesperson/salespersonService';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, lastValueFrom, Subscription } from 'rxjs';
import { customersService } from '../customersService';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { Workbook } from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import jsPDF from 'jspdf';
import { DxDataGridComponent } from 'devextreme-angular';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import dxDataGrid from 'devextreme/ui/data_grid';
import { TranslationService } from 'src/app/modules/i18n/translation.service';

@Component({
    selector: 'app-customer-view',
    templateUrl: './customer-view.component.html',
    styleUrls: ['./customer-view.component.scss'],
    standalone: false
})
export class CustomerViewComponent implements OnInit, OnDestroy{
customerForm: FormGroup;
subscriptions = new Subscription();
activeTab: string = 'tab1';
data: any[] = [];
newdata: FormGroup;
countries: any[];
Governoraties: any[];
AccountOperatories: any[];
companies: any[];
SalesPersons: any[];
_currentPage = 1;
roles: any[] = [];
selectedItemKeys: any = [];
itemsCount = 0;
editMode: boolean = false;
pagesCount = 0;
currentFilter: any;
classifications: any[] = [];
SalesPersonid: number = 0;
Salestypes: any[];
Nationalities: any[];
isLoading = false;
isRtl: boolean = document.documentElement.dir === 'rtl';
menuOpen = false;
toggleMenu() {
this.menuOpen = !this.menuOpen;
}
actionsList: string[] = [
'Export',
'Send via SMS',
'Send Via Email',
'Send Via Whatsapp',
'print',
];
modalConfig: ModalConfig = {
modalTitle: 'Send Sms',
modalSize: 'lg',
hideCloseButton(): boolean {
return true;
},
dismissButtonLabel: 'Cancel',
};
smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
emailModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Email',
};
whatsappModalConfig: ModalConfig = {
...this.modalConfig,
modalTitle: 'Send Whatsapp',
};
@ViewChild('smsModal') private smsModal: ModalComponent;
@ViewChild('emailModal') private emailModal: ModalComponent;
@ViewChild('whatsappModal') private whatsappModal: ModalComponent;
constructor(
private fb: FormBuilder,
private cdk: ChangeDetectorRef,
private myService: customersService,
private route: ActivatedRoute,
private router: Router,
private translationService: TranslationService,
) {


  function formatDateLocal(date: Date): string {
    const year = date.getFullYear();
    const month = (`0${date.getMonth() + 1}`).slice(-2); 
    const day = (`0${date.getDate()}`).slice(-2);        
    return `${year}-${month}-${day}`;
  }


  const today = new Date().toISOString().substring(0, 10);
  const currentYear = new Date().getFullYear();
  const firstDayOfYear = formatDateLocal(new Date(currentYear, 0, 1));   
  const lastDayOfYear = formatDateLocal(new Date(currentYear, 11, 31)); 
  const action_date = new Date().toISOString(); 

  this.newdata = this.fb.group({
            nameAr: [null, Validators.required],
            nameEn: [null, Validators.required],
            address: [''],
            site: [''],
            phone: [''],
            mobile: [''],
            fax: [''],
             id:[1],
            cityID: [0],
            mainCustomerId: [0],
            customerGroupId: [0],
            eMail: [''],
            short_Name_Ar: [''],
            short_Name_EN: [''],
            iD_Number: [1],
            iD_Date_start: [firstDayOfYear],
            birthDate: [firstDayOfYear],
            iD_Date_End: [lastDayOfYear],
            PaymentMethod: [''],
            custtime: [''],
            custVacaiotn: [''],
            OperatorID: [1],
            taxStatus: [''],
            iD_From: [''],
            bankName: [''],
            iBAN_NO: [''],
            increase_Rate: [0],
            tax_registration_Number: [0],
            commercialRegisterNumber: [''],
            collection: [''],
            collectionPeriod: [0],
            limet_money: [0],
            companyId: [null],
            nationality: [''],
            identityType: [''],
            identityNumber: [''],
            registrynumber: [null],
            business_personal: [''],
            activityCode: [null],
            customerActivity: [''],
            priceList: [1],
            discount_percent: [''],
            notes: [''],
            custcountryID: [12345],
            governorate: [''],
            city: [''],
            postalCode: [''],
            branchId: [null],
            buildingNumber: [''],
            custfloor: [''],
            workPhone: [''],
            landmark: [''],
            district: [''],
            street: [''],
            additionalInformation: [''],
            qualification: [''],
            PersonalMail: [''],
            homePhone: [''],
            relativeName_1: [''],
            relativeType_1: [''],
            relativePhone_1: [''],
            relativeName_2: [''],
            relativeType_2: [''],
            relativePhone_2: [''],
            relativeName_3: [''],
            relativeType_3: [''],
            relativePhone_3: [''],
            emergencyAddress: [''],
            user_id: [1],   
            isRealEstateCustomer: [false],
            supervisorId: [0],
            flag: [false],
            action_date: [action_date],

});


this.subscriptions.add(
  this.myService.getNationality().subscribe((r) => {
    if (r.success) {
      this.Nationalities = r.data;
    }
  })
);


this.subscriptions.add(
          myService.getSalestypes().subscribe((r) => {
            if (r.success) {
              this.Salestypes = r.data;
    
              this.cdk.detectChanges();
            }
          })
        );

this.subscriptions.add(myService.getSalesPersons().subscribe(r => {
          if (r.success) {
            this.SalesPersons = r.data;
            this.cdk.detectChanges();
          }
        }));

this.subscriptions.add(myService.getcompanies().subscribe(r => {
          if (r.success) {
            this.companies = r.data;
            this.cdk.detectChanges();
          }
        }));

this.subscriptions.add(myService.getcountry().subscribe(r => {
          if (r.success) {
            this.countries = r.data;
            this.cdk.detectChanges();
          }
        }));

    }

ngOnDestroy(): void {
      this.subscriptions.unsubscribe();
    }

ngOnInit(): void {
      const id = this.route.snapshot.params.id;
      if (id) {

        this.subscriptions.add(
          this.myService.details(id).subscribe((r) => {
            if (r.success) {
              this.fillForm(r.data);
              this.roles = r.roles;
              this.cdk.detectChanges();
            }
          })
        );
      }
    }

fillForm(item: any) {
  // Set primary form values from item
  Object.keys(item).forEach((key) => {
    if (this.newdata.get(key)) {
      const value =
        item[key] !== undefined && item[key] !== null ? item[key] : '';
      this.newdata.get(key)?.setValue(value);
    }
  });

  // Handle related fields and dependent dropdowns
  if (item.countryID && this.countries?.length > 0) {
    // If we have a country, update governorates dropdown
    this.subscriptions.add(
      this.myService.getGovernorates(item.countryID).subscribe((r: any) => {
        if (r.success) {
          this.Governoraties = r.data;
          this.cdk.detectChanges();
        }
      })
    );
  }

  // If we have a company selected, ensure it's properly selected in the dropdown
  if (item.companyId && this.companies?.length > 0) {
    const company = this.companies.find(c => c.id === item.companyId);
    if (company) {
      this.newdata.get('companyId')?.setValue(company.id);
      this.cdk.detectChanges();
    }
  }

  // If we have a sales person selected, ensure it's properly selected in the dropdown
  if (item.salespersonId && this.SalesPersons?.length > 0) {
    const salesPerson = this.SalesPersons.find(s => s.id === item.salespersonId);
    if (salesPerson) {
      this.newdata.get('salespersonId')?.setValue(salesPerson.id);
      this.cdk.detectChanges();
    }
  }

  // If we have a sales type selected, ensure it's properly selected in the dropdown
  if (item.salesTypeId && this.Salestypes?.length > 0) {
    const salesType = this.Salestypes.find(s => s.id === item.salesTypeId);
    if (salesType) {
      this.newdata.get('salesTypeId')?.setValue(salesType.id);
      this.cdk.detectChanges();
    }
  }

  // If we have a nationality selected, ensure it's properly selected in the dropdown
  if (item.nationalityId && this.Nationalities?.length > 0) {
    const nationality = this.Nationalities.find(n => n.id === item.nationalityId);
    if (nationality) {
      this.newdata.get('nationalityId')?.setValue(nationality.id);
      this.cdk.detectChanges();
    }
  }

  // Force change detection to update UI
  this.cdk.detectChanges();
}

save() {
  const id = this.route.snapshot.params.id;
  
  if (this.newdata.valid) {
    let form = this.newdata.value;
    
    // تحديد نوع النموذج بشكل صريح لضمان توافقه مع ما يتوقعه الخادم
    // Type يحل مشكلة AutoMapper
    form.Type = "Customer";
    
    this.isLoading = true;
    
    if (id) {
      // Update existing customer
      this.subscriptions.add(
        this.myService
          .update(id, form)
          .pipe(
            finalize(() => {
              this.isLoading = false;
              this.cdk.detectChanges();
            })
          )
          .subscribe(
            (r) => {
              if (r.success) {
                this.router.navigate(['/sales/configuration/customers']);
              } else {
                // Handle error response with more details
                const errorMessage = r.message || r.error || this.getLocalizedErrorMessage('update_error');
                alert(`${errorMessage}`);
                console.error('Update error details:', r);
              }
            },
            (error) => {
              // Handle HTTP error with more details
              let errorMessage = this.getLocalizedErrorMessage('server_error');
              if (error.error && error.error.message) {
                errorMessage = error.error.message;
              } else if (error.message) {
                errorMessage = error.message;
              }
              alert(`${errorMessage}`);
              console.error('HTTP error details:', error);
              this.isLoading = false;
            }
          )
      );
    } else {
      // Create new customer
      this.subscriptions.add(
        this.myService
          .create(form)
          .pipe(
            finalize(() => {
              this.isLoading = false;
              this.cdk.detectChanges();
            })
          )
          .subscribe(
            (r) => {
              if (r.success) {
                this.router.navigate(['/sales/configuration/customers']);
              } else {
                // Handle error response with more details
                const errorMessage = r.message || r.error || this.getLocalizedErrorMessage('create_error');
                alert(`${errorMessage}`);
                console.error('Create error details:', r);
              }
            },
            (error) => {
              // Handle HTTP error with more details
              let errorMessage = this.getLocalizedErrorMessage('server_error');
              if (error.error && error.error.message) {
                errorMessage = error.error.message;
              } else if (error.message) {
                errorMessage = error.message;
              }
              alert(`${errorMessage}`);
              console.error('HTTP error details:', error);
              this.isLoading = false;
            }
          )
      );
    }
  } else {
    // Form is invalid, mark fields as touched to show validation errors
    this.newdata.markAllAsTouched();
    
    // Log validation errors to help debugging
    console.log('Form validation errors:', this.getFormValidationErrors());
    alert(this.getLocalizedErrorMessage('validation_error'));
  }
}

// Helper function to get localized error messages
private getLocalizedErrorMessage(errorKey: string): string {
    const currentLang = this.translationService.getSelectedLanguage();
    
    // Error messages in both languages
    const errorMessages: { [key: string]: { en: string; ar: string } } = {
      'update_error': {
        en: 'Error updating customer record',
        ar: 'خطأ في تحديث بيانات العميل'
      },
      'create_error': {
        en: 'Error creating customer record',
        ar: 'خطأ في إنشاء بيانات العميل'
      },
      'server_error': {
        en: 'Error communicating with server',
        ar: 'خطأ في الاتصال بالخادم'
      },
      'validation_error': {
        en: 'Form has validation errors. Please check the form and try again.',
        ar: 'يوجد أخطاء في البيانات المدخلة. الرجاء التحقق من النموذج والمحاولة مرة أخرى.'
      }
    };
    
    // Return the message in the current language, or English as fallback
    return currentLang === 'ar' ? 
      errorMessages[errorKey]?.ar || errorMessages['server_error'].ar : 
      errorMessages[errorKey]?.en || errorMessages['server_error'].en;
  }

// Helper method to display form validation errors
getFormValidationErrors(): { control: string; error: string; value: any }[] {
  const result: { control: string; error: string; value: any }[] = [];
  Object.keys(this.newdata.controls).forEach(key => {
    const controlErrors = this.newdata.get(key)?.errors;
    if (controlErrors) {
      Object.keys(controlErrors).forEach(keyError => {
        result.push({
          control: key,
          error: keyError,
          value: controlErrors[keyError]
        });
      });
    }
  });
  return result;
}

discard() {
this.newdata.reset();
}

toggleBranch(): void {
const branchControl = this.customerForm.get('branch');
const branchEnabledControl = this.customerForm.get('branchEnabled');

if (branchEnabledControl?.value) {
branchControl?.enable();
} else {
branchControl?.disable();
}
}

// handleSubmit($event: SubmitEvent) {
// console.log(this.customerForm.value)
// console.log($event)
// }

get currentPage() {
return this._currentPage;
}

protected readonly branches = CustomerView.branches;
protected readonly customerClassifiedList = CustomerView.customerClassifiedList;
protected readonly mainCustomers = CustomerView.mainCustomers;
protected readonly priceList = CustomerView.priceList;
nationalities: any = [];
discountPercentageList: any = [];
regions: any = [];

setActiveTab(tab: string): void {
this.activeTab = tab;
}

exportToExcel() {
this.subscriptions.add(
this.myService.exportExcel().subscribe((e) => {
if (e) {
const href = URL.createObjectURL(e);
const link = document.createElement('a');
link.setAttribute('download', 'customer_view.xlsx');
link.href = href;
link.click();
URL.revokeObjectURL(href);
}
})
);
}

openSmsModal() {
this.smsModal.open();
}
openWhatsappModal() {
this.whatsappModal.open();
}
openEmailModal() {
this.emailModal.open();
} 

onPrint() {
  // إنشاء عنصر div للطباعة
  const printContent = document.createElement('div');
  printContent.style.direction = 'rtl';
  printContent.style.fontFamily = 'Arial, sans-serif';
  
  // إنشاء وتنسيق رأس الصفحة
  const header = document.createElement('div');
  header.style.textAlign = 'center';
  header.style.marginBottom = '20px';
  
  // إضافة شعار (اختياري)
  const logoDiv = document.createElement('div');
  logoDiv.style.textAlign = 'center';
  logoDiv.style.marginBottom = '10px';
  logoDiv.innerHTML = '<h2>Falcon ERP</h2>';
  header.appendChild(logoDiv);
  
  // إضافة العنوان
  const title = document.createElement('h1');
  title.textContent = 'بيانات العميل';
  title.style.fontSize = '22px';
  title.style.color = '#333';
  title.style.marginBottom = '5px';
  header.appendChild(title);
  
  // إضافة التاريخ
  const dateInfo = document.createElement('p');
  dateInfo.textContent = 'تاريخ الطباعة: ' + new Date().toLocaleDateString('ar-SA');
  dateInfo.style.fontSize = '12px';
  dateInfo.style.marginBottom = '10px';
  header.appendChild(dateInfo);
  
  // إضافة خط أفقي
  const hr = document.createElement('hr');
  hr.style.border = '1px solid #ddd';
  hr.style.margin = '10px 0 20px 0';
  
  // إنشاء جسم البيانات
  const customerData = document.createElement('div');
  customerData.style.marginBottom = '30px';
  
  // مصفوفة بالحقول المهمة وعناوينها
  const mainFields = [
    { key: 'nameAr', label: 'الاسم بالعربية' },
    { key: 'nameEn', label: 'الاسم بالإنجليزية' },
    { key: 'address', label: 'العنوان' },
    { key: 'phone', label: 'رقم الهاتف' },
    { key: 'mobile', label: 'الجوال' },
    { key: 'fax', label: 'الفاكس' },
    { key: 'eMail', label: 'البريد الإلكتروني' },
    { key: 'site', label: 'الموقع الإلكتروني' }
  ];
  
  // إنشاء جدول للبيانات الرئيسية
  const mainTable = document.createElement('table');
  mainTable.style.width = '100%';
  mainTable.style.borderCollapse = 'collapse';
  mainTable.style.marginBottom = '20px';
  
  // إضافة صفوف البيانات الرئيسية
  mainFields.forEach(field => {
    if (this.newdata.value[field.key]) {
      const row = document.createElement('tr');
      
      const labelCell = document.createElement('td');
      labelCell.textContent = field.label;
      labelCell.style.padding = '8px';
      labelCell.style.fontWeight = 'bold';
      labelCell.style.width = '30%';
      labelCell.style.backgroundColor = '#f8f8f8';
      labelCell.style.border = '1px solid #ddd';
      
      const valueCell = document.createElement('td');
      valueCell.textContent = this.newdata.value[field.key] || '';
      valueCell.style.padding = '8px';
      valueCell.style.border = '1px solid #ddd';
      
      row.appendChild(labelCell);
      row.appendChild(valueCell);
      mainTable.appendChild(row);
    }
  });
  
  customerData.appendChild(mainTable);
  
  // إنشاء أقسام مختلفة بناءً على علامات التبويب
  const sections = document.createElement('div');
  
  // قسم معلومات الهوية
  if (this.newdata.value.iD_Number) {
    const idSection = document.createElement('div');
    idSection.style.marginBottom = '20px';
    
    const idTitle = document.createElement('h2');
    idTitle.textContent = 'معلومات الهوية';
    idTitle.style.fontSize = '18px';
    idTitle.style.marginBottom = '10px';
    idTitle.style.borderBottom = '1px solid #ddd';
    idTitle.style.paddingBottom = '5px';
    
    idSection.appendChild(idTitle);
    
    const idTable = document.createElement('table');
    idTable.style.width = '100%';
    idTable.style.borderCollapse = 'collapse';
    
    const idFields = [
      { key: 'iD_Number', label: 'رقم الهوية' },
      { key: 'iD_Date_start', label: 'تاريخ الإصدار' },
      { key: 'iD_Date_End', label: 'تاريخ الانتهاء' },
      { key: 'iD_From', label: 'جهة الإصدار' },
      { key: 'birthDate', label: 'تاريخ الميلاد' }
    ];
    
    idFields.forEach(field => {
      if (this.newdata.value[field.key]) {
        const row = document.createElement('tr');
        
        const labelCell = document.createElement('td');
        labelCell.textContent = field.label;
        labelCell.style.padding = '8px';
        labelCell.style.fontWeight = 'bold';
        labelCell.style.width = '30%';
        labelCell.style.backgroundColor = '#f8f8f8';
        labelCell.style.border = '1px solid #ddd';
        
        const valueCell = document.createElement('td');
        let value = this.newdata.value[field.key] || '';
        // تنسيق التواريخ
        if (field.key.includes('Date') && value) {
          try {
            const dateObj = new Date(value);
            if (!isNaN(dateObj.getTime())) {
              value = dateObj.toLocaleDateString('ar-SA');
            }
          } catch (e) {
            console.error('Error formatting date', e);
          }
        }
        valueCell.textContent = value;
        valueCell.style.padding = '8px';
        valueCell.style.border = '1px solid #ddd';
        
        row.appendChild(labelCell);
        row.appendChild(valueCell);
        idTable.appendChild(row);
      }
    });
    
    idSection.appendChild(idTable);
    sections.appendChild(idSection);
  }
  
  // قسم معلومات الأقارب
  if (this.newdata.value.relativeName_1 || this.newdata.value.relativeName_2 || this.newdata.value.relativeName_3) {
    const relativesSection = document.createElement('div');
    relativesSection.style.marginBottom = '20px';
    
    const relativesTitle = document.createElement('h2');
    relativesTitle.textContent = 'معلومات الأقارب';
    relativesTitle.style.fontSize = '18px';
    relativesTitle.style.marginBottom = '10px';
    relativesTitle.style.borderBottom = '1px solid #ddd';
    relativesTitle.style.paddingBottom = '5px';
    
    relativesSection.appendChild(relativesTitle);
    
    const relativesTable = document.createElement('table');
    relativesTable.style.width = '100%';
    relativesTable.style.borderCollapse = 'collapse';
    
    // إضافة رأس الجدول
    const relTableHeader = document.createElement('tr');
    ['الاسم', 'صلة القرابة', 'رقم الهاتف'].forEach(headerText => {
      const th = document.createElement('th');
      th.textContent = headerText;
      th.style.padding = '8px';
      th.style.backgroundColor = '#f2f2f2';
      th.style.border = '1px solid #ddd';
      th.style.textAlign = 'right';
      relTableHeader.appendChild(th);
    });
    relativesTable.appendChild(relTableHeader);
    
    // إضافة البيانات لكل قريب
    for (let i = 1; i <= 3; i++) {
      const nameKey = `relativeName_${i}`;
      const typeKey = `relativeType_${i}`;
      const phoneKey = `relativePhone_${i}`;
      
      if (this.newdata.value[nameKey]) {
        const row = document.createElement('tr');
        
        const nameCell = document.createElement('td');
        nameCell.textContent = this.newdata.value[nameKey] || '';
        nameCell.style.padding = '8px';
        nameCell.style.border = '1px solid #ddd';
        
        const typeCell = document.createElement('td');
        typeCell.textContent = this.newdata.value[typeKey] || '';
        typeCell.style.padding = '8px';
        typeCell.style.border = '1px solid #ddd';
        
        const phoneCell = document.createElement('td');
        phoneCell.textContent = this.newdata.value[phoneKey] || '';
        phoneCell.style.padding = '8px';
        phoneCell.style.border = '1px solid #ddd';
        
        row.appendChild(nameCell);
        row.appendChild(typeCell);
        row.appendChild(phoneCell);
        relativesTable.appendChild(row);
      }
    }
    
    relativesSection.appendChild(relativesTable);
    sections.appendChild(relativesSection);
  }
  
  // إضافة معلومات إضافية
  if (this.newdata.value.emergencyAddress) {
    const additionalSection = document.createElement('div');
    
    const additionalTitle = document.createElement('h2');
    additionalTitle.textContent = 'معلومات إضافية';
    additionalTitle.style.fontSize = '18px';
    additionalTitle.style.marginBottom = '10px';
    additionalTitle.style.borderBottom = '1px solid #ddd';
    additionalTitle.style.paddingBottom = '5px';
    
    additionalSection.appendChild(additionalTitle);
    
    const additionalTable = document.createElement('table');
    additionalTable.style.width = '100%';
    additionalTable.style.borderCollapse = 'collapse';
    
    const additionalFields = [
      { key: 'emergencyAddress', label: 'عنوان الطوارئ' },
      { key: 'customerGroupId', label: 'تصنيف العميل' },
      { key: 'PaymentMethod', label: 'طريقة الدفع' }
    ];
    
    additionalFields.forEach(field => {
      if (this.newdata.value[field.key]) {
        const row = document.createElement('tr');
        
        const labelCell = document.createElement('td');
        labelCell.textContent = field.label;
        labelCell.style.padding = '8px';
        labelCell.style.fontWeight = 'bold';
        labelCell.style.width = '30%';
        labelCell.style.backgroundColor = '#f8f8f8';
        labelCell.style.border = '1px solid #ddd';
        
        const valueCell = document.createElement('td');
        valueCell.textContent = this.newdata.value[field.key] || '';
        valueCell.style.padding = '8px';
        valueCell.style.border = '1px solid #ddd';
        
        row.appendChild(labelCell);
        row.appendChild(valueCell);
        additionalTable.appendChild(row);
      }
    });
    
    additionalSection.appendChild(additionalTable);
    sections.appendChild(additionalSection);
  }
  
  // تجميع كل الأقسام في محتوى الطباعة
  printContent.appendChild(header);
  printContent.appendChild(hr);
  printContent.appendChild(customerData);
  printContent.appendChild(sections);
  
  // إضافة تذييل
  const footer = document.createElement('div');
  footer.style.marginTop = '30px';
  footer.style.textAlign = 'center';
  footer.style.fontSize = '10px';
  footer.style.color = '#777';
  footer.textContent = '© ' + new Date().getFullYear() + ' Falcon ERP - جميع الحقوق محفوظة';
  printContent.appendChild(footer);
  
  // حفظ المحتوى الأصلي
  const originalContent = document.body.innerHTML;
  
  // أنماط CSS للطباعة
  const printStyles = `
    <style>
      @media print {
        body { 
          font-family: Arial, sans-serif; 
          margin: 0;
          padding: 20px;
          direction: rtl;
        }
        button, input, .dx-button, .menu { 
          display: none !important; 
        }
        table { 
          page-break-inside: avoid;
        }
        h1, h2 { 
          page-break-after: avoid;
        }
        @page {
          size: A4;
          margin: 1cm;
        }
      }
    </style>
  `;
  
  // تعيين محتوى الطباعة
  document.body.innerHTML = printStyles + printContent.outerHTML;
  
  // تنفيذ الطباعة
  window.print();
  
  // إعادة المحتوى الأصلي
  document.body.innerHTML = originalContent;
  
  // إعادة تهيئة المكونات التفاعلية
  this.cdk.detectChanges();
  MenuComponent.reinitialization();
}
}
