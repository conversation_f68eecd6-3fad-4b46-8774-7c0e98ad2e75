export interface CustomerModel {
    arabicName?: string;
    customerClassified?: number;
    englishName?: string;
    mainCustomer?: number;
    shortAr?: string;
    activity?: string;
    shortEN?: string;
    phone?: string;
    title?: string;
    fax?: string;
    theTaxStation?: string;
    tel?: string;
    timeOfCollection?: string;
    creditLimit?: string;
    website?: string;
    paymentMethod?: string;
    priceList?: number;
    workingTime?: string;
    nationality?: string;
    durationOfCollection?: string;
    officialHoliday?: string;
    birthday?: string;
    discountPercentage?: string;
    notes?: string;
    accountOperator?: string;
    idNumber?: number;
    issuer?: string;
    bankName?: string;
    expirationDate?: Date;
    accountNumber?: string;
    email?: string;
    country?: number;
    region?: number;
    city?: number;
    postalCode?: string;
    branchEnabled?: boolean;
    branch?: number;
    increaseRate?: string;
    vatNo?: string;
    commercialRegister?: string;
    buildingNumber?: string;
    floorNumber?: string;
    officeNumber?: string;
    typeCode?: string;
    activityCode?: string;
    landmark?: string;
    district?: string;
    additionalInformation?: string;
    street?: string;
}
