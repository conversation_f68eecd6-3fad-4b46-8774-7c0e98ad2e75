<!-- <form [formGroup]="newdata"> -->
<div class="card mb-5 mb-xl-10">

<div class="card-header border-0 cursor-pointer w-100">

            <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
                <h3 class="fw-bolder m-0">{{ "COMMON.NewCustomer" | translate }}</h3>
           
                <div [style.position]="'relative'">
             <div class="btn-group">
               <button
                 type="submit"
                 (click)="discard()"
                 class="btn btn-sm btn-active-light-primary"
               >
                 {{ "COMMON.Cancel" | translate }}
                 <i class="fa fa-close"></i>
               </button>
               <button
                 type="submit"
                 (click)="save()"
                 class="btn btn-sm btn-active-light-primary"
               >
                 {{ "COMMON.SaveChanges" | translate }}
                 <i class="fa fa-save"></i>
               </button>
             </div>
             <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                       (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
             <button
               class="btn btn-icon btn-active-light-primary mx-2"
               (click)="toggleMenu()"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               data-bs-trigger="hover"
               title="Settings"
             >
               <i class="fa fa-gear"></i>
             </button>
             <div
               class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
               [class.show]="menuOpen"
               [style.position]="'absolute'"
               [style.top]="'100%'"
               [style.zIndex]="'1050'"
               [style.left]="isRtl ? '0' : 'auto'"
               [style.right]="!isRtl ? '0' : 'auto'"
             >
               <div class="menu-item px-3">
                 <a
                   class="menu-link px-3"
                   (click)="exportToExcel()"
                   data-kt-company-table-filter="delete_row"
                 >
                   {{ "COMMON.ExportToExcel" | translate }}
                 </a>
               </div>
               <div class="menu-item px-3">
                 <a
                   class="menu-link px-3"
                   data-kt-company-table-filter="delete_row"
                   (click)="openSmsModal()"
                 >
                   {{ "COMMON.SendViaSMS" | translate }}
                 </a>
               </div>
     
               <div class="menu-item px-3">
                 <a
                   class="menu-link px-3"
                   data-kt-company-table-filter="delete_row"
                   (click)="openEmailModal()"
                 >
                   {{ "COMMON.SendViaEmail" | translate }}
                 </a>
               </div>
     
               <div class="menu-item px-3">
                 <a
                   class="menu-link px-3"
                   data-kt-company-table-filter="delete_row"
                   (click)="openWhatsappModal()"
                 >
                   {{ "COMMON.SendViaWhatsapp" | translate }}
                 </a>
               </div>
     
               <div
                 class="menu-item px-3"
                 *hasPermission="{
                   action: 'printAction',
                   module: 'Salse.Customers'
                 }"
               >
                 <a
                   class="menu-link px-3"
                   (click)="onPrint()"
                   data-kt-company-table-filter="delete_row"
                   >{{ "COMMON.Print" | translate }}</a
                 >
               </div>
               <!-- <div
               class="menu-item px-3"
               *hasPermission="{
                 action: 'printAction',
                 module: 'HR.Employees'
               }"
             >
               <a
                 class="menu-link px-3"
                 target="_blank"
                 href="/reports/warehouses?withLogo=true"
                 data-kt-company-table-filter="delete_row"
                 >Print With Logo</a
               >
             </div> -->
             </div>
           </div>

         </div>
</div>



<div class="card-body border-top p-9">


  <form
  [formGroup]="newdata"
  action="#"
  id="kt_modal_add_company_form"
  data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
>

<div class="main-inputs mb-5">


<div class="row">


<div class="form-group col-xl-4 col-md-6 col-sm-12">
  <label class="required form-label">{{
    "CUSTOMER.ARABIC_NAME" | translate
  }}</label>
  <input
    type="text"
    name="nameAr"
    class="form-control"
    value=""
    formControlName="nameAr"
  />
</div>

<div class="form-group col-xl-4 col-md-6 col-sm-12">
  <label class="required form-label">{{
    "CUSTOMER.ENGLISH_NAME" | translate
  }}</label>
  <input
    type="text"
    name="nameAr"
    class="form-control"
    value=""
    formControlName="nameEn"
  />
</div>


</div>

<div class="row"> 

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="customerGroupId" class="form-label">
                        {{'CUSTOMER.CUSTOMER_CLASSIFIED' | translate}}
                    </label>
                    <ng-select
                            id="customerGroupId"
                            formControlName="customerGroupId"
                            bindLabel="name"
                            bindValue="id"
                            [items]="customerClassifiedList"></ng-select>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="mainCustomer" class="form-label">
                        {{'CUSTOMER.MAIN_CUSTOMER' | translate}}
                    </label>
                    <ng-select
                            id="mainCustomer"
                            formControlName="mainCustomerId"
                            bindLabel="name"
                            bindValue="id"
                            [items]="mainCustomers"></ng-select>

</div>

</div>


<div class="row">

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="email" class="form-label">
                            {{'CUSTOMER.EMAIL' | translate}}
                        </label>
                        <input id="email"

                               formControlName="eMail"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                      <label for="phone" class="form-label">
                          {{'CUSTOMER.PHONE' | translate}}
                      </label>
                      <input id="phone"
  
                             formControlName="phone"
                             class="form-control"/>
</div>



</div>

<div class="row">

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="shortAr" class="form-label">
                        {{'CUSTOMER.SHORT_AR' | translate}}
                    </label>
                    <input id="shortAr"

                           formControlName="short_Name_Ar"
                           class="form-control"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="shortEn" class="form-label">
                        {{'CUSTOMER.SHORT_EN' | translate}}
                    </label>
                    <input id="shortEn"

                           formControlName="short_Name_EN"
                           class="form-control"/>
</div>




</div>

<div class="row">

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="tel" class="form-label">
                        {{'CUSTOMER.TEL' | translate}}
                    </label>
                    <input id="tel"
    
                           formControlName="mobile"
                           class="form-control"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                    <label for="fax" class="form-label">
                        {{'CUSTOMER.FAX' | translate}}
                    </label>
                    <input id="fax"

                           formControlName="fax"
                           class="form-control"/>
</div>

</div>


<div class="row">

  <div class="form-group col-xl-8 col-md-4 col-sm-12 ">
    <label for="address" class="form-label">
        {{'COMMON.Address' | translate}}
    </label>
    <input id="address"

           formControlName="address"
           class="form-control"/>
</div>

</div>



</div>


<!-- <pre>{{ newdata.value | json }}</pre> -->

<div class="row">

<ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
  
                    <li class="nav-item" role="presentation">
                      <button
                        class="nav-link"
                        [class.active]="activeTab === 'tab1'"
                        (click)="setActiveTab('tab1')"
                      >
                        {{ "COMMON.BasicData" | translate }}
                      </button>
                    </li>

                    <li class="nav-item" role="presentation">
                      <button
                        class="nav-link"
                        [class.active]="activeTab === 'tab6'"
                        (click)="setActiveTab('tab6')"
                      >
                        {{ "COMMON.TaxInformation" | translate }}
                      </button>
                    </li>

                     <li class="nav-item" role="presentation">
                      <button
                        class="nav-link"
                        [class.active]="activeTab === 'tab7'"
                        (click)="setActiveTab('tab7')"
                      >
                        {{ "COMMON.LicensesInformation" | translate }}
                      </button>
                    </li>

                    <li class="nav-item" role="presentation">
                      <button
                        class="nav-link"
                        [class.active]="activeTab === 'tab2'"
                        (click)="setActiveTab('tab2')"
                      >
                        {{ "CUSTOMER.TABS.ADDRESS" | translate }}
                      </button>
                    </li>

                    <li class="nav-item" role="presentation">
                        <button
                          class="nav-link"
                          [class.active]="activeTab === 'tab3'"
                          (click)="setActiveTab('tab3')"
                        >
                          {{ "CUSTOMER.TABS.OFFICIALS" | translate }}
                        </button>
                      </li>

                      <li class="nav-item" role="presentation">
                        <button
                          class="nav-link"
                          [class.active]="activeTab === 'tab4'"
                          (click)="setActiveTab('tab4')"
                        >
                          {{ "CUSTOMER.TABS.SALES_PERSON" | translate }}
                        </button>
                      </li>

                      <li class="nav-item" role="presentation">
                        <button
                          class="nav-link"
                          [class.active]="activeTab === 'tab5'"
                          (click)="setActiveTab('tab5')"
                        >
                          {{ "CUSTOMER.TABS.RELATIVES" | translate }}
                        </button>
                      </li>

           
</ul>

<div class="tab-content" id="myTabContent">

<!-- Tab 1 -->
<div
class="tab-pane fade"
[class.show]="activeTab === 'tab1'"
[class.active]="activeTab === 'tab1'"
*ngIf="activeTab === 'tab1'"
>

<div class="row">
 


  <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
    <label for="ID_Number" class="form-label">
    {{'CUSTOMER.ID_NUMBER' | translate}}
    </label>
    <input id="ID_Number"
    formControlName="iD_Number"
    class="form-control"/>
    </div>


    <div class="form-group col-xl-4 col-md-2 col-sm-12">
      <label class="form-label">{{ "COMMON.IDIssueDate" | translate }}</label>
      <input
        id="ID_Date_start"
        type="date"
        formControlName="iD_Date_start"
        class="form-control"
      />
    </div>


<!-- expirationDate -->
<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                  <label for="ID_Date_End"
                         class="form-label">{{ 'CUSTOMER.EXPIRATION_DATE' | translate }}</label>
                  <input id="ID_Date_End"
                         type="date"
                         formControlName="iD_Date_End" class="form-control"/>
</div>


<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
  <label for="birthDate"
         class="form-label">{{ 'CUSTOMER.BIRTHDAY' | translate }}</label>
  <input id="birthDate"
         type="date"
         formControlName="birthDate" class="form-control"/>
</div>





<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
<label for="website" class="form-label">{{'CUSTOMER.WEBSITE' | translate}} </label>
<input id="website"
formControlName="site"
class="form-control"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
<label for="paymentMethod" class="form-label">{{'CUSTOMER.PAYMENT_METHOD' | translate}}</label>
<input id="paymentMethod"
formControlName="paymentMethod"
class="form-control"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
  <label for="custtime" class="form-label">
      {{'CUSTOMER.WORKING_TIME' | translate}}
  </label>
  <input id="custtime"

         formControlName="custtime"
         class="form-control"/>
</div>




<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
  <label for="custVacaiotn" class="form-label">
      {{'CUSTOMER.OFFICIAL_HOLIDAY' | translate}}
  </label>
  <input id="custVacaiotn"

         formControlName="custVacaiotn"
         class="form-control"/>
</div>











<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
  <label for="OperatorID" class="form-label">
      {{'COMMON.AccountOperator' | translate}}
  </label>
  <ng-select
          id="OperatorID"
          formControlName="operatorID"
          bindLabel="name"
          bindValue="id"
          [items]="AccountOperatories"></ng-select>
</div>




</div>

</div>

<!-- Tab 6 -->
<div
class="tab-pane fade"
[class.show]="activeTab === 'tab6'"
[class.active]="activeTab === 'tab6'"
*ngIf="activeTab === 'tab6'"
>
<div class="row">


  <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
    <label for="taxStation" class="form-label">
    {{'CUSTOMER.TAX_STATION' | translate}}
    </label>
    <input id="taxStation"
    
    formControlName="taxStatus"
    class="form-control"/>
    </div>






<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="ID_From" class="form-label">{{ 'CUSTOMER.ISSUER' | translate }}</label>
                <input id="ID_From"
                       formControlName="iD_From" class="form-control"/>
</div>

            <!-- bankName -->
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="bankName" class="form-label">{{ 'CUSTOMER.BANK_NAME' | translate }}</label>
                <input id="bankName"
                       formControlName="bankName" class="form-control"/>
            </div>



            <!-- accountNumber -->
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="IBAN_NO"
                       class="form-label">{{ 'CUSTOMER.ACCOUNT_NUMBER' | translate }}</label>
                <input id="IBAN_NO"
                       formControlName="iBAN_NO" class="form-control"/>
            </div>
            <!-- increaseRate -->
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="Increase_Rate"
                       class="form-label">{{ 'CUSTOMER.INCREASE_RATE' | translate }}</label>
                <input id="Increase_Rate"
                       formControlName="increase_Rate" class="form-control"/>
            </div>

            <!-- vatNo Tax_registration_Number -->
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="Tax_registration_Number" class="form-label">{{ 'CUSTOMER.VAT_NUMBER' | translate }}</label>
                <input id="Tax_registration_Number"
                       formControlName="tax_registration_Number" class="form-control"/>
            </div>

            <!-- commercialRegister -->
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="commercialRegister"
                       class="form-label">{{ 'CUSTOMER.COMMERCIAL_REGISTER' | translate }}</label>
                <input id="commercialRegister"

                       formControlName="commercialRegisterNumber" class="form-control"/>
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label for=" Collection" class="form-label">
              {{'CUSTOMER.COLLECTION' | translate}}
              </label>
              <input id=" Collection"
              formControlName=" collection"
              class="form-control"/>
              </div>
              
              
              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                <label for="CollectionPeriod" class="form-label">
                    {{'CUSTOMER.DURATION_OF_COLLECTION' | translate}}
                </label>
                <input id="CollectionPeriod"
              
                       formControlName="collectionPeriod"
                       class="form-control"/>
              </div>
              
              
              <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label for="creditLimit" class="form-label">
              {{'CUSTOMER.CREDIT_LIMIT' | translate}}
              </label>
              <input id="creditLimit"
              formControlName="limet_money"
              class="form-control"/>
              </div>


            <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
              <label for="companyId" class="form-label">
                  {{'COMMON.Company' | translate}}
              </label>
              <ng-select
                      id="companyId"
                      formControlName="companyId"
                      bindLabel="name"
                      bindValue="id"
                      [items]="companies"></ng-select>
            </div>

            <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0 ">
              <label for="nationality" class="form-label">
                  {{'CUSTOMER.NATIONALITY' | translate}}
              </label>
              <ng-select
                      id="nationality"
                      formControlName="nationality"
                      bindLabel="nameAr"
                      bindValue="id"
                      [items]="Nationalities"></ng-select>
            </div>


          </div>



  
          
            
</div>

<!-- Tab 7 -->
<div
class="tab-pane fade"
[class.show]="activeTab === 'tab7'"
[class.active]="activeTab === 'tab7'"
*ngIf="activeTab === 'tab7'"
>
<div class="row mb-0">


  <div class="form-group col-xl-4 col-md-4 col-sm-12">
    <label for="IdentityType" class="form-label">
    {{'COMMON.LicenseType' | translate}}
    </label>
    <input id="IdentityType"
    
    formControlName="identityType"
    class="form-control"/>
    </div>

  <div class="form-group col-xl-4 col-md-4 col-sm-12">
    <label for="IdentityNumber" class="form-label">
    {{'COMMON.LicenseNumber' | translate}}
    </label>
    <input id="IdentityNumber"
    
    formControlName="identityNumber"
    class="form-control"/>
    </div>

      <div class="form-group col-xl-4 col-md-4 col-sm-12">
    <label for="registrynumber" class="form-label">
    {{'COMMON.RegistryNumber' | translate}}
    </label>
    <input id="registrynumber"
    
    formControlName="registrynumber"
    class="form-control"/>
    </div>

</div>

<div class="row mb-0">
  <!-- typeCode -->
  <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0">
    <label for="Business_personal" class="form-label">{{ 'CUSTOMER.TYPE_CODE' | translate }}</label>
    <input id="Business_personal"
           formControlName="business_personal" class="form-control"/>
</div>

<!-- activityCode -->
<div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0">
    <label for="activityCode"
           class="form-label">{{ 'CUSTOMER.ACTIVITY_CODE' | translate }}</label>
    <input id="activityCode"
           formControlName="activityCode" class="form-control"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0 ">
    <label for="CustomerActivity" class="form-label">
        {{'CUSTOMER.ACTIVITY' | translate}}
    </label>
    <textarea id="CustomerActivity"

              formControlName="customerActivity"
              class="form-control"></textarea>
</div>



</div>

<div class="row mb-0">


  
  <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0 ">
    <label for="priceList" class="form-label">
        {{'CUSTOMER.PRICE_LIST' | translate}}
    </label>
    <ng-select
            id="priceList"
            formControlName="priceList"
            bindLabel="name"
            bindValue="id"
            [items]="priceList"></ng-select>
  </div>
  
  <div class="form-group col-xl-4 col-md-4 col-sm-12 mb-0 ">
    <label for="discountPercentage" class="form-label">
        {{'CUSTOMER.DISCOUNT_PERCENTAGE' | translate}}
    </label>
    <ng-select
            id="discountPercentage"
            formControlName="discount_percent"
            bindLabel="name"
            bindValue="id"
            [items]="discountPercentageList"></ng-select>
  </div>


  <div class="form-group col-xl-4 col-md-3 col-sm-12">
    <label class="form-label"> {{ "COMMON.Notes" | translate }}</label>
    <input
    type="text"
    id="notes"
    name="notes"
    class="form-control"
    formControlName="notes"
  />
    
  </div>


</div>





</div>

<!-- Tab 2 -->
<div
          class="tab-pane fade"
          [class.show]="activeTab === 'tab2'"
          [class.active]="activeTab === 'tab2'"
          *ngIf="activeTab === 'tab2'"
        >

        <div class="row">


                        <!-- country -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="CustcountryID" class="form-label">{{ 'CUSTOMER.COUNTRY' | translate }}</label>
                            <ng-select
                                    id="CustcountryID"
                                    formControlName="custcountryID"
                                    bindLabel="contryNameEN"
                                    bindValue="id"
                                    [items]="countries"></ng-select>
                        </div>

                        <!-- region --> 
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="Governorate" class="form-label">{{ 'COMMON.Governorate' | translate }}</label>
                            <ng-select
                                    id="Governorate"
                                    formControlName="governorate"
                                    bindLabel="name"
                                    bindValue="id"
                                    [items]="Governoraties"></ng-select>
                        </div>

                        <!-- city -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="city" class="form-label">{{ 'CUSTOMER.CITY' | translate }}</label>
                            <input id="city" formControlName="city"
                                   class="form-control"/>
                        </div>

                        <!-- postalCode -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="postalCode" class="form-label">{{ 'CUSTOMER.POSTAL_CODE' | translate }}</label>
                            <input id="postalCode"
                                   formControlName="postalCode" class="form-control"/>
                        </div>

                        <!-- branch -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="branch" class="form-label">{{ 'CUSTOMER.BRANCH' | translate }}</label>
                            <div class="d-flex w-100">
                                <ng-select class="select-border-bottom w-100"
                                           id="branch"
                                           formControlName="branchId"
                                           bindLabel="name"
                                           bindValue="id"
                                           [items]="branches"></ng-select>
                                <!-- <input id="branchEnabled" class="form-check-input m-3" type="checkbox"
                                       (change)="toggleBranch()"
                                       formControlName="branchEnabled"/> -->
                            </div>
                        </div>

                        <!-- buildingNumber -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="buildingNumber"
                                   class="form-label">{{ 'CUSTOMER.BUILDING_NUMBER' | translate }}</label>
                            <input id="buildingNumber"
                                   formControlName="buildingNumber" class="form-control"/>
                        </div>

                        <!-- floorNumber -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="floorNumber"
                                   class="form-label">{{ 'CUSTOMER.FLOOR_NUMBER' | translate }}</label>
                            <input id="floorNumber"
                                   formControlName="custfloor" class="form-control"/>
                        </div>

                        <!-- officeNumber -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="WorkPhone"
                                   class="form-label">{{ 'CUSTOMER.WorkPhone' | translate }}</label>
                            <input id="WorkPhone"
                                   formControlName="workPhone" class="form-control"/>
                        </div>

                        <!-- landmark -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="landmark" class="form-label">{{ 'CUSTOMER.LANDMARK' | translate }}</label>
                            <input id="landmark"
                                   formControlName="landmark" class="form-control"/>
                        </div>

                        
                          <!-- district -->
                          <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="district" class="form-label">{{ 'CUSTOMER.DISTRICT' | translate }}</label>
                            <input id="district"
                                   formControlName="district" class="form-control"/>
                        </div>

                        <!-- street -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="street" class="form-label">{{ 'CUSTOMER.STREET' | translate }}</label>
                            <input id="street"
                                   formControlName="street" class="form-control"/>
                        </div>

                        <!-- additionalInformation -->
                        <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                            <label for="additionalInformation"
                                   class="form-label">{{ 'CUSTOMER.ADDITIONAL_INFORMATION' | translate }}</label>
                            <textarea id="additionalInformation"

                                      formControlName="additionalInformation"
                                      class="form-control"></textarea>
                        </div>


        </div>

 

</div>

<!-- Tab 3 -->
<div
class="tab-pane fade"
[class.show]="activeTab === 'tab3'"
[class.active]="activeTab === 'tab3'"
*ngIf="activeTab === 'tab3'"
>

<div class="row">

<div class="form-group col-xl-3 col-md-2 col-sm-12 ">
                        <label for="branchname" class="form-label">
                          {{ "COMMON.Branch" | translate }}
                        </label>
                        <input
                          type="text"
                          id="branchname"
                          name="branchname"
                          class="form-control"
                          formControlName="branchname"
                        />
</div>

<div class="form-group col-xl-3 col-md-2 col-sm-12 ">
                        <label for="responsiblepersonname" class="form-label">
                          {{ "COMMON.ResponsiblePerson" | translate }}
                        </label>
                        <input
                          type="text"
                          id="responsiblepersonname"
                          name="responsiblepersonname"
                          class="form-control"
                          formControlName="responsiblepersonname"
                        />
</div>

<div class="form-group col-xl-3 col-md-2 col-sm-12 ">
                        <label for="jobtitlename" class="form-label">
                          {{ "COMMON.JobTitle" | translate }}
                        </label>
                        <input
                          type="text"
                          id="jobtitlename"
                          name="jobtitlename"
                          class="form-control"
                          formControlName="jobtitlename"
                        />
</div>

<div class="form-group col-xl-3 col-md-2 col-sm-12 ">
                        <label for="Departmentname" class="form-label">
                          {{ "COMMON.Department" | translate }}
                        </label>
                        <input
                          type="text"
                          id="Departmentname"
                          name="Departmentname"
                          class="form-control"
                          formControlName="Departmentname"
                        />
</div>


        
</div>

<div class="row">
                    <div class="form-group col-xl-3 col-md-4 col-sm-12 ">
                        <label for="email" class="form-label">
                            {{'CUSTOMER.EMAIL' | translate}}
                        </label>
                        <input id="email"

                               formControlName="`"
                               class="form-control required"/>
                    </div>

                    <div class="form-group col-xl-3 col-md-4 col-sm-12 ">
                        <label for="phone" class="form-label">
                            {{'CUSTOMER.PHONE' | translate}}
                        </label>
                        <input id="phone"

                               formControlName="phone"
                               class="form-control"/>
                    </div>
                    <div class="form-group col-xl-3 col-md-4 col-sm-12 ">
                        <label for="tel" class="form-label">
                            {{'CUSTOMER.TEL' | translate}}
                        </label>
                        <input id="tel"

                               formControlName="tel"
                               class="form-control"/>
                    </div>




                      <div class="form-group col-3 d-flex justify-content-end">
                        <div class="btn-group">
                          <button
                            type="submit"
                            (click)="discard()"
                            class="btn btn-sm btn-active-light-primary"
                          >
                            {{ "COMMON.Cancel" | translate }}
                            <i class="fa fa-close"></i>
                          </button>
                          <button
                            type="submit"
                            (click)="save()"
                            class="btn btn-sm btn-active-light-primary"
                          >
                            {{ "COMMON.SaveChanges" | translate }}
                            <i class="fa fa-save"></i>
                          </button>
                        </div>
       

                      </div>

        
</div>

</div>

<!-- Tab 4 -->
<div
    class="tab-pane fade"
    [class.show]="activeTab === 'tab4'"
    [class.active]="activeTab === 'tab4'"
    *ngIf="activeTab === 'tab4'">

    <div class="row">

        <div class="form-group col-xl-4 col-md-4 col-sm-12">
            <label for="SalesPersonid" class="form-label">
              {{ "COMMON.Salesperson" | translate }}
            </label>
            <ng-select
              id="SalesPersonid"
              formControlName="salesPersonId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="SalesPersons"
            ></ng-select>
          </div>

          <div class="form-group col-xl-4 col-md-4 col-sm-12 ">
            <label for="salesType" class="form-label">
              {{ "COMMON.SalesType" | translate }}
            </label>
            <ng-select
                    id="salesType"
                    formControlName="salesType"
                    bindLabel="nameAr"
                    bindValue="id"
                    [items]="Salestypes"></ng-select>
        </div>


          
          <div class="form-group col-4 d-flex justify-content-end">
            <div class="btn-group">
              <button
                type="submit"
                (click)="discard()"
                class="btn btn-sm btn-active-light-primary"
              >
                {{ "COMMON.Cancel" | translate }}
                <i class="fa fa-close"></i>
              </button>
              <button
                type="submit"
                (click)="save()"
                class="btn btn-sm btn-active-light-primary"
              >
                {{ "COMMON.SaveChanges" | translate }}
                <i class="fa fa-save"></i>
              </button>
            </div>


          </div>

</div>




</div>

<!-- Tab 5 -->
<div
class="tab-pane fade"
[class.show]="activeTab === 'tab5'"
[class.active]="activeTab === 'tab5'"
*ngIf="activeTab === 'tab5'"
>

<div class="row">

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="qualification" class="form-label">
                            {{'COMMON.Qualification' | translate}}
                        </label>
                        <input id="qualification"
                        id="qualification"
                        name="qualification"
                               formControlName="qualification"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="PersonalMail" class="form-label">
                            {{'COMMON.Email' | translate}}
                        </label>
                        <input id="PersonalMail"
                        id="PersonalMail"
                        name="PersonalMail"
                               formControlName="personalMail"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
  <label for="HomePhone" class="form-label">
      {{'COMMON.HomePhone' | translate}}
  </label>
  <input id="HomePhone"
  id="HomePhone"
  name="HomePhone"
  formControlName="homePhone"
  class="form-control"/>
</div>


</div>

<div class="row">

<div class="form-group col-xl-4 col-md-2 col-sm-12 ">
<label for="relativename1" class="form-label">
                          {{ "COMMON.RelativeName" | translate }}
                        </label>
                        <input
                          type="text"
                          id="relativename1"
                          name="relativename1"
                          class="form-control"
                          formControlName="relativeName_1"
                        />
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="kinship1" class="form-label">
                            {{'COMMON.Kinship' | translate}}
                        </label>
                        <input id="kinship1"
                        id="kinship1"
                        name="kinship1"
                               formControlName="relativeType_1"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="relativephone1" class="form-label">
                            {{'COMMON.RelativePhone' | translate}}
                        </label>
                        <input id="relativephone1"
                        id="relativephone1"
                        name="relativephone1"
                               formControlName="relativePhone_1"
                               class="form-control"/>
</div>

</div>

<div class="row">

<div class="form-group col-xl-4 col-md-2 col-sm-12 ">
                        <label for="RelativeName_2  " class="form-label">
                          {{ "COMMON.RelativeName" | translate }}
                        </label>
                        <input
                          type="text"
                          id="RelativeName_2 "
                          name="RelativeName_2 "
                          class="form-control"
                          formControlName="relativeName_2"
                        />
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="kinship2" class="form-label">
                            {{'COMMON.Kinship' | translate}}
                        </label>
                        <input id="kinship2"
                        id="kinship2"
                        name="kinship2"
                               formControlName="relativeType_2"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="RelativePhone_2" class="form-label">
                            {{'COMMON.RelativePhone' | translate}}
                        </label>
                        <input id="RelativePhone_2"
                        id="RelativePhone_2"
                        name="RelativePhone_2"
                               formControlName="relativePhone_2"
                               class="form-control"/>
</div>

</div>

<div class="row">

<div class="form-group col-xl-4 col-md-2 col-sm-12 ">
                        <label for="RelativeName_3  " class="form-label">
                          {{ "COMMON.RelativeName" | translate }}
                        </label>
                        <input
                          type="text"
                          id="RelativeName_3 "
                          name="RelativeName_3 "
                          class="form-control"
                          formControlName="relativeName_3"
                        />
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="RelativeType_3" class="form-label">
                            {{'COMMON.Kinship' | translate}}
                        </label>
                        <input id="kinship2"
                        id="RelativeType_3"
                        name="RelativeType_3"
                               formControlName="relativeType_3"
                               class="form-control required"/>
</div>

<div class="form-group col-xl-4 col-md-4 col-sm-12 ">
                        <label for="RelativePhone_3" class="form-label">
                            {{'COMMON.RelativePhone' | translate}}
                        </label>
                        <input id="RelativePhone_3"
                        id="RelativePhone_3"
                        name="RelativePhone_3"
                               formControlName="relativePhone_3"
                               class="form-control"/>
</div>

</div>


<div class="row">

<div class="form-group col-xl-8 col-md-2 col-sm-12 ">
                        <label for="EmergencyAddress" class="form-label">
                          {{ "COMMON.SpecialAddress" | translate }}
                        </label>
                        <input
                          type="text"
                          id="EmergencyAddress"
                          name="EmergencyAddress"
                          class="form-control"
                          formControlName="emergencyAddress"
                        />
</div>




        
</div>


</div>

</div>

</div>
</form>

</div>


</div>

<!-- </form> -->
