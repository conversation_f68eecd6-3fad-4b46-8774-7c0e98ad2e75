<form [formGroup]="newInquiryFrm" class="mb-3">
<div class="card-body border-top">

  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
    <h3 class="fw-bolder m-0">{{ "COMMON.Customer" | translate }}</h3>
    <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            routerLink="/sales/configuration/customer_view"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Salse.Customers',
              action: 'createAction'
            }"
          >
            {{ "COMMON.Create" | translate }}
            <i class="fa fa-plus"></i>
          </button>
          <!-- زر التعديل تم إزالته وتم استبداله برابط في عمود رقم الحركة -->
          <button
            (click)="deleteRecords()"
            [hidden]="!selectedItemKeys.length"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Salse.Customers',
              action: 'deleteAction'
            }"
          >
            {{ "COMMON.DELETE" | translate }}
            <i class="fa fa-trash"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Purchases.Suppliers'
            }"
          >
            <a
              class="menu-link px-3"
              (click)="onPrint()"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Purchases.Suppliers'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>

</div>



</form>

<dx-data-grid
  [dataSource]="data"
  [columnAutoWidth]="true"
  [wordWrapEnabled]="true"
  (onSelectionChanged)="selectionChanged($event)"
  [focusedRowEnabled]="false"
  [showBorders]="true"
  [rtlEnabled]="isRtl"
  (onExporting)="onExporting($event)"
>
<dxo-selection
  mode="multiple"
  [allowSelectAll]="true"
  [selectAllMode]="'page'"
  [showCheckBoxesMode]="'always'"
  [selectByClick]="false"
  [deferred]="false">
</dxo-selection>
<dxo-filter-row
  [visible]="true"
  [applyFilter]="currentFilter"
></dxo-filter-row>

<dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
<dxo-paging [pageSize]="10"> </dxo-paging>
<dxo-pager
  [visible]="true"
  [allowedPageSizes]="[5, 10, 20, 50]"
  [displayMode]="'full'"
  [showPageSizeSelector]="true"
  [showInfo]="true"
  [showNavigationButtons]="true"
>
</dxo-pager>
<dxo-header-filter [visible]="true"></dxo-header-filter>

<dxo-search-panel
  [visible]="true"
  [highlightCaseSensitive]="true"
></dxo-search-panel>

<dxi-column dataField="id" caption="id" cellTemplate="idCellTemplate"></dxi-column>

<div *dxTemplate="let data of 'idCellTemplate'">
  <a [routerLink]="['/sales/configuration/customer_view', data.value]"
     class="text-primary"
     (click)="$event.stopPropagation()"
     (mousedown)="$event.stopPropagation()">
    {{ data.value }}
  </a>
</div>
<dxi-column dataField="nameAr" caption="{{ 'COMMON.NameAr' | translate }}"></dxi-column>
<dxi-column dataField="nameEn" caption="{{ 'COMMON.NameEn' | translate }}"></dxi-column>
<dxi-column dataField="address" caption="{{ 'COMMON.Address' | translate }}"></dxi-column>

<dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

<dxo-summary>
  <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
</dxo-summary>
</dx-data-grid>
