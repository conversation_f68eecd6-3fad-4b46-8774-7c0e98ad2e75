
<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">

    <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
      <h3 class="fw-bolder m-0">{{ "COMMON.Sales And Commission Terms" | translate }}</h3>
 
      <div [style.position]="'relative'">
     <div class="btn-group">
     <button
       type="submit"
       (click)="discard()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.Cancel" | translate }}
       <i class="fa fa-close"></i>
     </button>
     <button
       type="submit"
       (click)="save()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.SaveChanges" | translate }}
       <i class="fa fa-save"></i>
     </button>
   </div>
   <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
             (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
   <button
     class="btn btn-icon btn-active-light-primary mx-2"
     (click)="toggleMenu()"
     data-bs-toggle="tooltip"
     data-bs-placement="top"
     data-bs-trigger="hover"
     title="Settings"
   >
     <i class="fa fa-gear"></i>
   </button>
   <div
     class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
     [class.show]="menuOpen"
     [style.position]="'absolute'"
     [style.top]="'100%'"
     [style.zIndex]="'1050'"
     [style.left]="isRtl ? '0' : 'auto'"
     [style.right]="!isRtl ? '0' : 'auto'"
   >
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         (click)="exportToExcel()"
         data-kt-company-table-filter="delete_row"
       >
         {{ "COMMON.ExportToExcel" | translate }}
       </a>
     </div>
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openSmsModal()"
       >
         {{ "COMMON.SendViaSMS" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openEmailModal()"
       >
         {{ "COMMON.SendViaEmail" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openWhatsappModal()"
       >
         {{ "COMMON.SendViaWhatsapp" | translate }}
       </a>
     </div>

     <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'Salse.SalesAndCommissionTerms'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses"
         data-kt-company-table-filter="delete_row"
         >{{ "COMMON.Print" | translate }}</a
       >
     </div>
     <!-- <div
     class="menu-item px-3"
     *hasPermission="{
       action: 'printAction',
       module: 'HR.Employees'
     }"
   >
     <a
       class="menu-link px-3"
       target="_blank"
       href="/reports/warehouses?withLogo=true"
       data-kt-company-table-filter="delete_row"
       >Print With Logo</a
     >
   </div> -->
   </div>
 </div>

</div>

  </div>


  <div class="card-body border-top p-9">

    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
      <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
 
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="SalesPersonid" class="form-label">
                {{ "COMMON.Salesperson" | translate }}
              </label>
              <ng-select
                id="SalesPersonid"
                [(ngModel)]="SalesPersonid"
                bindLabel="nameAr"
                bindValue="id"
                [items]="SalesPersons"
              ></ng-select>
            </div>
        
        
        
        
        
          </div>

        </div>
 
      </div>


      <div class="row">

        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.Rate" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.Value" | translate }}
            </button>
          </li>
 
        </ul>






        <div class="tab-content" id="myTabContent">
          
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
          <div class="row">
            <div class="form-group col-xl-3 col-md-2 col-sm-12 mb-3">
              <label for="TargetNumber" class="form-label">
                {{ "COMMON.Target" | translate }}
              </label>
              <input
                type="text"
                id="TargetNumber"
                name="TargetNumber"
                class="form-control"
                formControlName="TargetNumber"
              />
            </div>
  
            <div class="form-group col-xl-3 col-md-2 col-sm-12 mb-3">
              <label for="AchievedPercentageNumber" class="form-label">
                {{ "COMMON.AchievedPercentage" | translate }}
              </label>
              <input
                type="text"
                id="AchievedPercentageNumber"
                name="AchievedPercentageNumber"
                class="form-control"
                formControlName="AchievedPercentageNumber"
              />
            </div>
  
            <div class="form-group col-xl-4 col-md-2 col-sm-12 mb-3">
              <label for="CommissionPercentageDueNumber" class="form-label">
                {{ "COMMON.CommissionPercentageDue" | translate }}
              </label>
              <input
                type="text"
                id="CommissionPercentageDueNumber"
                name="CommissionPercentageDueNumber"
                class="form-control"
                formControlName="CommissionPercentageDueNumber"
              />
            </div>
  
  
  
          </div>
  
          
            
          </div>


          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >

          <div class="row">
            <div class="form-group col-xl-4 col-md-2 col-sm-12 mb-3">
              <label for="FromNumber" class="form-label">
                {{ "COMMON.From" | translate }}
              </label>
              <input
                type="text"
                id="FromNumber"
                name="FromNumber"
                class="form-control"
                formControlName="FromNumber"
              />
            </div>
  
            <div class="form-group col-xl-3 col-md-2 col-sm-12 mb-3">
              <label for="ToNumber" class="form-label">
                {{ "COMMON.To" | translate }}
              </label>
              <input
                type="text"
                id="ToNumber"
                name="ToNumber"
                class="form-control"
                formControlName="ToNumber"
              />
            </div>

            <div class="form-group col-xl-4 col-md-2 col-sm-12 mb-3">
              <label for="CommissionNumber" class="form-label">
                {{ "COMMON.Commission" | translate }}
              </label>
              <input
                type="text"
                id="CommissionNumber"
                name="CommissionNumber"
                class="form-control"
                formControlName="CommissionNumber"
              />
            </div>
  
  
          </div>

   

          </div>


        </div>


      </div>
    </form>
 
  </div>
</div>


  






