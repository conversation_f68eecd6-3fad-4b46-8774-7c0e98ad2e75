import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { SalesService } from '../../../sales.service';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Service } from 'src/app/pages/general/products-grid-controle/products-grid-controle.service';
import { ConfigurationService } from '../../ConfigurationService';

@Component({
  selector: 'app-sales_and_commission_terms_view',
  templateUrl: './sales_and_commission_terms_view.component.html',
  styleUrls: ['./sales_and_commission_terms_view.component.css'],
  standalone:false
})
export class Sales_and_commission_terms_viewComponent implements OnInit {
   viewListForm: FormGroup;
  subscription = new Subscription();
  newdata: FormGroup;
  SalesPersons: any[];
  activeTab: string = 'tab1';
  SalesPersonid: number = 0;



    selectedItemKeys: any = [];
    isRtl: boolean = document.documentElement.dir === 'rtl';
       menuOpen = false;
       toggleMenu() {
         this.menuOpen = !this.menuOpen;
       }
       actionsList: string[] = [
         'Export',
         'Send via SMS',
         'Send Via Email',
         'Send Via Whatsapp',
         'print',
       ];
       modalConfig: ModalConfig = {
         modalTitle: 'Send Sms',
         modalSize: 'lg',
         hideCloseButton(): boolean {
           return true;
         },
         dismissButtonLabel: 'Cancel',
       };
     
       smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
       emailModalConfig: ModalConfig = {
         ...this.modalConfig,
         modalTitle: 'Send Email',
       };
       whatsappModalConfig: ModalConfig = {
         ...this.modalConfig,
         modalTitle: 'Send Whatsapp',
       };
       @ViewChild('smsModal') private smsModal: ModalComponent;
       @ViewChild('emailModal') private emailModal: ModalComponent;
       @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
private service: ConfigurationService, private cdk: ChangeDetectorRef, private route: ActivatedRoute,
  ) {


    this.subscription.add(service.getSalesPersons().subscribe(r => {
      if (r.success) {
        this.SalesPersons = r.data;
        this.cdk.detectChanges();
      }
    }));
   }

  ngOnInit() {
  }


  discard() {}

    save() {}

    setActiveTab(tab: string): void {
      this.activeTab = tab;
    }


    exportToExcel() {
      this.subscription.add(
        this.service.exportExcel().subscribe((e) => {
          if (e) {
            const href = URL.createObjectURL(e);
            const link = document.createElement('a');
            link.setAttribute('download', 'sales_and_commission_terms_view.xlsx');
            link.href = href;
            link.click();
            URL.revokeObjectURL(href);
          }
        })
      );
    }
    openSmsModal() {
      this.smsModal.open();
    }
    openWhatsappModal() {
      this.whatsappModal.open();
    }
    openEmailModal() {
      this.emailModal.open();
    } 

}
