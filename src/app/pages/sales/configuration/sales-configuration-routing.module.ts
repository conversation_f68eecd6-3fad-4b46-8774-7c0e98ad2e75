import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CustomersCategoryComponent} from './customers-category/customers-category.component';
import {CustomersComponent} from './customers/customers.component';
import {GuarantorComponent} from './guarantor/guarantor.component';
import {MainCustomerComponent} from './main-customer/main-customer.component';
import {PricelistsComponent} from './pricelists/pricelists.component';
import {SalesAndCommissionTermsComponent} from './sales-and-commission-terms/sales-and-commission-terms.component';
import {SalesTypesComponent} from './sales-types/sales-types.component';
import {SalespersonCommissionComponent} from './salesperson-commission/salesperson-commission.component';
import {SalespersonComponent} from './salesperson/salesperson.component';
import {SalesTeamsComponent} from './sales-teams/sales-teams.component';
import {SalespersonCategoriesComponent} from './salesperson-categories/salesperson-categories.component';
import {SalespersonViewComponent} from './salesperson/salesperson-view/salesperson-view.component';
import {GuarantorViewComponent} from './guarantor/guarantor-view/guarantor-view.component';
import {CustomerViewComponent} from './customers/customer-view/customer-view.component';
import {SalesPaymentTermsComponent} from './sales-payment-terms/sales-payment-terms.component';
import { Sales_and_commission_terms_viewComponent } from './sales-and-commission-terms/sales_and_commission_terms_view/sales_and_commission_terms_view.component';
import { Sales_payment_terms_viewComponent } from './sales-payment-terms/sales_payment_terms_view/sales_payment_terms_view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'salesperson_categories',
        pathMatch: 'full'
    },
    {
        path: 'salesperson_categories',
        component: SalespersonCategoriesComponent
    },
    {
        path: 'sales_payment_terms',
        component: SalesPaymentTermsComponent
    },
    {
        path: 'salesperson',
        component: SalespersonComponent
    },
    {
        path: 'salesperson_view',
        component: SalespersonViewComponent
    },
    {
        path: 'salesperson_view/:id',
        component: SalespersonViewComponent
    },
    {
        path: 'sales_payment_terms_view',
        component: Sales_payment_terms_viewComponent
    },
    {
        path: 'sales_payment_terms_view/:id',
        component: Sales_payment_terms_viewComponent
    },

    
    {
        path: 'sales_and_commission_terms_view',
        component: Sales_and_commission_terms_viewComponent
    },
    {
        path: 'sales_and_commission_terms_view/:id',
        component: Sales_and_commission_terms_viewComponent
    },




    
    {
        path: 'sales_types',
        component: SalesTypesComponent
    },
    {
        path: 'sales_teams',
        component: SalesTeamsComponent
    },
    {
        path: 'salesperson_commission',
        component: SalespersonCommissionComponent
    },
    {
        path: 'customers_category',
        component: CustomersCategoryComponent
    },
    {
        path: 'sales_and_commission_terms',
        component: SalesAndCommissionTermsComponent
    },
    {
        path: 'guarantor',
        component: GuarantorComponent
    },
    {
        path: 'guarantor_view',
        component: GuarantorViewComponent
    },
    {
        path: 'main_customer',
        component: MainCustomerComponent
    },
    {
        path: 'customers',
        component: CustomersComponent
    },
    {
        path: 'customer_view',
        component: CustomerViewComponent
    },
    {
        path: 'customer_view/:id',
        component: CustomerViewComponent
    },

    {
        path: 'pricelists',
        component: PricelistsComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SalesConfigurationRoutingModule {
}
