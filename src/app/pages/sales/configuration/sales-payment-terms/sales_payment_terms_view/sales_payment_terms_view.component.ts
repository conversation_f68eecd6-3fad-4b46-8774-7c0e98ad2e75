import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { ConfigurationService } from '../../ConfigurationService';
import { SalesService } from '../../../sales.service';

@Component({
  selector: 'app-sales_payment_terms_view',
  templateUrl: './sales_payment_terms_view.component.html',
  styleUrls: ['./sales_payment_terms_view.component.css'],
  standalone:false
})
export class Sales_payment_terms_viewComponent implements OnInit {
subscriptions = new Subscription();
activeTab: string = 'tab1';
newdata: FormGroup;
      isLoading = false;
        selectedItemKeys: any = [];
isRtl: boolean = document.documentElement.dir === 'rtl';
          menuOpen = false;
          toggleMenu() {
            this.menuOpen = !this.menuOpen;
          }
          actionsList: string[] = [
            'Export',
            'Send via SMS',
            'Send Via Email',
            'Send Via Whatsapp',
            'print',
          ];
          modalConfig: ModalConfig = {
            modalTitle: 'Send Sms',
            modalSize: 'lg',
            hideCloseButton(): boolean {
              return true;
            },
            dismissButtonLabel: 'Cancel',
          };
        
          smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
          emailModalConfig: ModalConfig = {
            ...this.modalConfig,
            modalTitle: 'Send Email',
          };
          whatsappModalConfig: ModalConfig = {
            ...this.modalConfig,
            modalTitle: 'Send Whatsapp',
          };
          @ViewChild('smsModal') private smsModal: ModalComponent;
          @ViewChild('emailModal') private emailModal: ModalComponent;
          @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
private service: SalesService, private cdk: ChangeDetectorRef, private route: ActivatedRoute,
  ) { }

  ngOnInit() {
  }







  discard() {}
    
    
    
    
  save() { }

  exportToExcel() {
    this.subscriptions.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'customer_view.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  } 

}
