<form [formGroup]="salespersonform" (submit)="handleSubmit($event)">

  <div class="card mb-5 mb-xl-10">



<div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
           <h3 class="fw-bolder m-0">{{ "COMMON.Salesperson" | translate }}</h3>
      
           <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Salesperson'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>



    <div class="card-body border-top p-9">

        <div class="row">

            <form [formGroup]="newdata" class="form" action="#" id="kt_modal_add_company_form"
                data-kt-redirect="../../demo1/dist/apps/Companies/list.html">
              <div class="me-n7 pe-7" id="kt_modal_add_company">
                <div class="row">
                  <!--begin::Col-->


                  <div class="form-group col-xl-4 col-md-4 col-sm-12">
                    <label class="required form-label">Ar Name</label>
                    <input type="text" class="form-control" placeholder="Ar Name" name="nameAr"
                           value="" id="arabicName"  formControlName="nameAr" />
                  </div>
                  <div class="form-group col-xl-4 col-md-4 col-sm-12">
                    <label class="required form-label">En Name</label>

                    <input
                    
                    id="englishName"
                    formControlName="nameEn"
                    class="form-control required"/>

                    <!-- <input type="text" class="form-control" placeholder="En Name"
                           name="nameEn" value="" id="englishName" formControlName="nameEn" /> -->
                 
                          </div>
 

                </div>



                <div class="row">

                  <div class="form-group col-xl-4 col-md-4 col-sm-12">
                    <label class="form-label">
                      <span class="required">Category</span>
                    </label>
                    <ng-select  formControlName="salesManTypeId" bindLabel="nameAr" bindValue="id" [items]="SalespersonCategories"></ng-select>
                  </div>


                  <div class="form-group col-xl-4 col-md-4 col-sm-12">
                    <label class="form-label">
                      <span class="required">user</span>
                    </label>
                    <ng-select  formControlName="UserID" bindLabel="fullName" bindValue="falconUserId" [items]="users"></ng-select>
                    <!--{{ users | json }}-->
                  </div>


                </div>







                <div class="row">


                      
                      <div class="form-group col-xl-4 col-md-4 col-sm-12" >

                        <!-- <h4 class="fw-bold text-dark" translate="SALES.PSP"></h4> -->
                        <label class="required form-label">Product  Percentage </label> 

                        <input type="text" class="form-control" placeholder="Percent Value"
                               name="percentVaue" value="" formControlName="percentVaue" />

                      </div>
                   

               
                      
                      <div class="form-group col-xl-4 col-md-4 col-sm-12" >
                        <label class="required form-label">Service Percentage </label>
                        <input type="text" class="form-control" placeholder="Service Percent"
                               name="servicePercent" value="" formControlName="servicePercent" />
                      </div>
                


                </div>


                <div class="row">

                  <div class="d-flex flex-column mb-5  col-md-4">
                    <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                      <input class="form-check-input" name="group1" type="radio" [value]="0" formControlName="isFrom_Mksab" />
                      <div class="form-check-label">
                        {{ 'SALES.OfProfit' |translate}}
                      </div>
                    </div>
                    <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                      <input class="form-check-input" name="group1" type="radio" [value]="1" formControlName="isFrom_Egmaly" />
                      <div class="form-check-label">
                        {{ 'SALES.OfTotalBill'  |translate}}
                      </div>
                    </div>
                  </div>

                  <div class="d-flex flex-column mb-5  col-md-4">
                    <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                      <input class="form-check-input" name="group2" type="radio" [value]="0" formControlName="srv_IsFrom_Mksab" />
                      <div class="form-check-label">
                        {{ 'SALES.OfProfit' |translate}}
                      </div>
                    </div>
                    <div class="form-check form-check-custom form-check-solid mb-2 p-5">
                      <input class="form-check-input" name="group2" type="radio" [value]="1" formControlName="srv_IsFrom_Egmaly" />
                      <div class="form-check-label">
                        {{ 'SALES.OfTotalBill'|translate}}
                      </div>
                    </div>
                  </div>



                </div>
                

                
                <div class="row">


                  <div class="form-group col-xl-4 col-md-4 col-sm-12">
                    <label class="required form-label">Target </label> 
                    <input type="text" class="form-control" placeholder="Target" name="targetValue" value=" " formControlName="targetValue" />
                  </div>

                </div>
              


              </div>



</form>
        </div>

    </div>

</div>

</form>


