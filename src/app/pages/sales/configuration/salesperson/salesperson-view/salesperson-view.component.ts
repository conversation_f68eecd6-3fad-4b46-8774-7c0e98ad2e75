import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { salespersonService } from '../salespersonService';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';


@Component({
    selector: 'app-salesperson-view',
    templateUrl: './salesperson-view.component.html',
    styleUrls: ['./salesperson-view.component.scss'],
    standalone: false
})
export class SalespersonViewComponent implements OnInit, OnDestroy {
  salespersonform : FormGroup;
  users: any[];
  SalespersonCategories: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  selectedItemKeys: any = [];
 isRtl: boolean = document.documentElement.dir === 'rtl';
    menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
      'print',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };
  
    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: salespersonService,
    private route: ActivatedRoute,
    private router: Router) {
    this.createForm();

    this.route.paramMap.subscribe(p => { 
      const id = p.get('id');
      if (id) {
        this.subscriptions.add(myService.details(id).subscribe(r => {
          if (r.success) {
            this.createForm(r.data);
            this.cdk.detectChanges();
          }
        }));
      } else {
        this.createForm()
      }
    });
    this.subscriptions.add(myService.getUsers().subscribe(r => {
      if (r.success) {
        this.users = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscriptions.add(myService.getSalespersonCategories().subscribe(r => {
      if (r.success) {
        this.SalespersonCategories = r.data;
        this.cdk.detectChanges();
      }
    }));

this.salespersonform = new FormGroup({

            arabicName: new FormControl(''),
            englishName: new FormControl(''),
 
        });





  }
  private createForm(model :any  = null ) {
    this.newdata = this.fb.group({
      salesManTypeId: [model?.salesManTypeId, Validators.required],
      nameAr: [model?.nameAr, Validators.required],
      nameEn: [model?.nameEn],
      percentVaue: [model?.percentVaue],
      isFrom_Mksab: [model?.isFrom_Mksab],
      isFrom_Egmaly: [model?.isFrom_Egmaly],
      servicePercent: [model?.servicePercent],
      srv_IsFrom_Mksab: [model?.srv_IsFrom_Mksab],
      srv_IsFrom_Egmaly: [model?.srv_IsFrom_Egmaly],
      targetValue: [model?.targetValue],
      userID: [model?.userID],
      mandopImag: [model?.mandopImag],
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {


  }


  handleSubmit($event: SubmitEvent) {
    console.log(this.salespersonform.value)
    console.log($event)
}

discard() {
    this.salespersonform.reset({englishName: ''});
}




  save() {
    if (this.newdata.valid) {
      let form = this.newdata.value
      if (form.id) {
        this.subscriptions.add(this.myService.update(form.id, form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/sales/configuration/salesperson']);
            }
          }));
      } else {
        this.subscriptions.add(this.myService.create(form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/sales/configuration/salesperson']);
            }
          }));
      }
    }
    else {
      this.newdata.markAllAsTouched();
    }
  }

  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  } 
}

