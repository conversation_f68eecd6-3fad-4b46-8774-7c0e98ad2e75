<div class="card mb-5 mb-xl-10">

    <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
      <h3 class="fw-bolder m-0">{{ "COMMON.Guarantors" | translate }}</h3>
 
      <div [style.position]="'relative'">
   <div class="btn-group">
     <button
       type="submit"
       (click)="discard()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.Cancel" | translate }}
       <i class="fa fa-close"></i>
     </button>
     <button
       type="submit"
       (click)="save()"
       class="btn btn-sm btn-active-light-primary"
     >
       {{ "COMMON.SaveChanges" | translate }}
       <i class="fa fa-save"></i>
     </button>
   </div>
   <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
             (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
   <button
     class="btn btn-icon btn-active-light-primary mx-2"
     (click)="toggleMenu()"
     data-bs-toggle="tooltip"
     data-bs-placement="top"
     data-bs-trigger="hover"
     title="Settings"
   >
     <i class="fa fa-gear"></i>
   </button>
   <div
     class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
     [class.show]="menuOpen"
     [style.position]="'absolute'"
     [style.top]="'100%'"
     [style.zIndex]="'1050'"
     [style.left]="isRtl ? '0' : 'auto'"
     [style.right]="!isRtl ? '0' : 'auto'"
   >
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         (click)="exportToExcel()"
         data-kt-company-table-filter="delete_row"
       >
         {{ "COMMON.ExportToExcel" | translate }}
       </a>
     </div>
     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openSmsModal()"
       >
         {{ "COMMON.SendViaSMS" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openEmailModal()"
       >
         {{ "COMMON.SendViaEmail" | translate }}
       </a>
     </div>

     <div class="menu-item px-3">
       <a
         class="menu-link px-3"
         data-kt-company-table-filter="delete_row"
         (click)="openWhatsappModal()"
       >
         {{ "COMMON.SendViaWhatsapp" | translate }}
       </a>
     </div>

     <div
       class="menu-item px-3"
       *hasPermission="{
         action: 'printAction',
         module: 'Salse.Guarantor'
       }"
     >
       <a
         class="menu-link px-3"
         target="_blank"
         href="/reports/warehouses"
         data-kt-company-table-filter="delete_row"
         >{{ "COMMON.Print" | translate }}</a
       >
     </div>

   </div>
 </div>
</div>


    <div class="card-body border-top p-9">
        <div class="row">
          <form [formGroup]="newdata" class="form" action="#" id="kt_modal_add_company_form"
                data-kt-redirect="../../demo1/dist/apps/Companies/list.html">
            <div class="me-n7 pe-7" id="kt_modal_add_company">
              <div class="row g-9 mb-7">
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                  <label class="required fs-6 fw-semibold mb-2">En Name</label>
                  <input type="text" class="form-control form-control-solid" placeholder="Name"
                         name="name" value="" formControlName="name" />
                </div>
                <div class="col-md-6 fv-row">
                  <label class="required fs-6 fw-semibold mb-2">Job Name</label>
                  <input type="text" class="form-control form-control-solid" placeholder="Job Name" name="jobName"
                         value="" formControlName="jobName" />
                </div>
              </div>

              <div class="row">
                <div class="fv-row col-md-6">
                  <label class="fs-6 fw-semibold mb-2">
                    <span class="required">Nationality</span>
                  </label>
                  <ng-select [multiple]="true" formControlName="nationality" bindLabel="Nationality" bindValue="id" [items]="Nationality"></ng-select>
                </div>

                <div class="fv-row col-md-6">
                  <label class="fs-6 fw-semibold mb-2">
                    <span class="required">AccID</span>
                  </label>
                  <ng-select [multiple]="true" formControlName="accID" bindLabel="name" bindValue="id" [items]="Accounts"></ng-select>
                </div>
              </div>



              <div class="row g-9 mb-7">
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                  <label class="required fs-6 fw-semibold mb-2">ID NO</label>
                  <input type="text" class="form-control form-control-solid" placeholder="id no"
                         name="id" value="" formControlName="id" />
                </div>
                <div class="col-md-3 fv-row">
                  <label class="required fs-6 fw-semibold mb-2">id source</label>
                  <input type="text" class="form-control form-control-solid" placeholder="Job Name" name="idsource"
                         value="" formControlName="idsource" />
                </div>


                <div class="col-md-3 fv-row">
                  <label class="required fs-6 fw-semibold mb-2">id date</label>
                  <input type="text" class="form-control form-control-solid" placeholder="Job Name" name="iddate"
                         value="" formControlName="iddate" />
                </div>

              </div>
            </div>

            <div class="row g-9 mb-7">
              <!--begin::Col-->
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Phone</label>
                <input type="text" class="form-control form-control-solid" placeholder="phone"
                       name="phone" value="" formControlName="phone" />
              </div>
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Mobile</label>
                <input type="text" class="form-control form-control-solid" placeholder="mobile" name="mobile"
                       value="" formControlName="mobile" />
              </div>
            </div>


            <div class="row g-9 mb-7">
              <!--begin::Col-->
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Address</label>
                <input type="text" class="form-control form-control-solid" placeholder="address"
                       name="address" value="" formControlName="address" />
              </div>
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Work Address</label>
                <input type="text" class="form-control form-control-solid" placeholder="Work address" name="Workaddress"
                       value="" formControlName="workaddress" />
              </div>
            </div>



            <div class="row g-9 mb-7">
              <!--begin::Col-->
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Bank Name</label>
                <input type="text" class="form-control form-control-solid" placeholder="BankName"
                       name="BankName" value="" formControlName="BankName" />
              </div>
              <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Bank Account Number</label>
                <input type="text" class="form-control form-control-solid" placeholder="Bank Account Number" name="iBAN_NO"
                       value="" formControlName="iBAN_NO" />
              </div>
            </div>



          </form>
        </div>

    </div>

    
</div>
