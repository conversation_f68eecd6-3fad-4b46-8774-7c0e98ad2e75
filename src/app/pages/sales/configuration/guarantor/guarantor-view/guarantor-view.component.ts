import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { guarantorService } from '../guarantorService';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
@Component({
    selector: 'app-guarantor-view',
    templateUrl: './guarantor-view.component.html',
    styleUrls: ['./guarantor-view.component.scss'],
    standalone: false
})
export class GuarantorViewComponent implements OnInit, OnDestroy {
  
  Nationality: any[] = [];
  Accounts: any[] = [];
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
    selectedItemKeys: any = [];
   isRtl: boolean = document.documentElement.dir === 'rtl';
      menuOpen = false;
      toggleMenu() {
        this.menuOpen = !this.menuOpen;
      }
      actionsList: string[] = [
        'Export',
        'Send via SMS',
        'Send Via Email',
        'Send Via Whatsapp',
        'print',
      ];
      modalConfig: ModalConfig = {
        modalTitle: 'Send Sms',
        modalSize: 'lg',
        hideCloseButton(): boolean {
          return true;
        },
        dismissButtonLabel: 'Cancel',
      };
    
      smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
      emailModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Email',
      };
      whatsappModalConfig: ModalConfig = {
        ...this.modalConfig,
        modalTitle: 'Send Whatsapp',
      };
      @ViewChild('smsModal') private smsModal: ModalComponent;
      @ViewChild('emailModal') private emailModal: ModalComponent;
      @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: guarantorService,
    private router: Router) {
    this.newdata = this.fb.group({
      id: [null, Validators.required],
      name: [null, Validators.required],
      idsource: [null],
      iddate: [null],
      address: [null],
      workaddress: [null],
      phone: [null],
      mobile: [null],
      nationality: [null],
      jobName: [null],
      accID: [null],
      bankName: [null],
      iBAN_NO: [null],

    });
 
    this.subscriptions.add(myService.Accounts().subscribe(r => {
      if (r.success) {
        this.Accounts = r.data;
        this.cdk.detectChanges();
      }
    }));

    this.subscriptions.add(myService.getNationality().subscribe(r => {
      if (r.success) {
        this.Nationality = r.data;
        this.cdk.detectChanges();
      }
    }));
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
     

  }


  discard() {}
  
  save() {
    if (this.newdata.valid) {
      let form = this.newdata.value
      this.subscriptions.add(this.myService.create(form)
        .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
        .subscribe(r => {
          if (r.success) {
            this.router.navigate(['/sales/configuration/guarantor']);
          }
        }));
    } else {
      this.newdata.markAllAsTouched();
    }
  }

  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'guarantor.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  } 


}

