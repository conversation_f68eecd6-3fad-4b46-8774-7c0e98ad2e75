
<!-- <PERSON>cabe<PERSON><PERSON> del Dashboard -->
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-9 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="me-7 mb-4">
        <div class="symbol symbol-60px symbol-lg-80px symbol-fixed position-relative">
          <img src="./assets/media/svg/brand-logos/sales.svg" alt="Sales" style="max-width: 100%; max-height: 100%;" />
        </div>
      </div>
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex flex-column">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">{{ 'COMMON.SalesOverview' | translate }}</span>
            </div>
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
              <span class="d-flex align-items-center text-gray-400 text-hover-primary me-5 mb-2">
                <i class="ki-outline ki-calendar fs-4 me-1"></i>{{ 'COMMON.Last30Days' | translate }}
              </span>
            </div>
          </div>
          <div class="d-flex my-4">
            <a href="#" class="btn btn-sm btn-primary me-3">{{ 'COMMON.ViewAll' | translate }}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Estadísticas de Ventas -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Total de Ventas -->
  <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{totalSales | number}}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TotalSales' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <span class="fs-6 fw-bolder text-gray-800 d-block mb-2">{{ 'COMMON.SalesTrends' | translate }}</span>
        <div class="symbol-group symbol-hover flex-nowrap">
          <div class="symbol symbol-35px symbol-circle">
            <span class="symbol-label bg-success text-inverse-success">+18%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Órdenes Pendientes -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{pendingOrders | number}}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.PendingOrders' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <span class="fs-6 fw-bolder text-gray-800 d-block mb-2">{{ 'COMMON.RequiresAttention' | translate }}</span>
        <div class="symbol-group symbol-hover flex-nowrap">
          <div class="symbol symbol-35px symbol-circle">
            <span class="symbol-label bg-warning text-inverse-warning">!</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Total de Ingresos y Clientes -->
  <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
    <!-- Total de Ingresos -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">${{totalRevenue | number}}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TotalRevenue' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <span class="fs-6 fw-bolder text-gray-800 d-block mb-2">{{ 'COMMON.RevenueGrowth' | translate }}</span>
        <div class="symbol-group symbol-hover flex-nowrap">
          <div class="symbol symbol-35px symbol-circle">
            <span class="symbol-label bg-primary text-inverse-primary">+15%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Total de Clientes -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{totalCustomers | number}}</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TotalCustomers' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-end pe-0">
        <span class="fs-6 fw-bolder text-gray-800 d-block mb-2">{{ 'COMMON.ActiveCustomers' | translate }}</span>
        <div class="symbol-group symbol-hover flex-nowrap">
          <div class="symbol symbol-35px symbol-circle">
            <span class="symbol-label bg-info text-inverse-info">+8%</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Gráficos y Tendencias -->
  <div class="col-xxl-6">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.SalesTrends' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.Last30Days' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.Export' | translate }}</button>
        </div>
      </div>
      <div class="card-body d-flex flex-column justify-content-between pb-5 px-0">
        <!-- Aquí iría un gráfico de tendencias -->
        <div class="min-h-auto ps-4 pe-6" style="height: 300px">
          <!-- Placeholder para el gráfico -->
          <div class="d-flex flex-column justify-content-center align-items-center h-100">
            <i class="ki-outline ki-chart fs-5x text-gray-300"></i>
            <span class="text-gray-500 mt-5">{{ 'COMMON.SalesTrendsChart' | translate }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Principales Productos y Ventas Recientes -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Principales Productos -->
  <div class="col-xxl-6 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.TopProducts' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.BySalesVolume' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <div class="d-flex flex-stack">
          <div class="text-gray-700 fw-semibold fs-6 me-2">{{ 'COMMON.Product' | translate }}</div>
          <div class="text-gray-700 fw-semibold fs-6">{{ 'COMMON.Amount' | translate }}</div>
        </div>
        <div class="separator separator-dashed my-3"></div>

        <!-- Lista de Productos -->
        <div *ngFor="let product of topProducts" class="d-flex flex-stack py-3">
          <div class="d-flex align-items-center me-2">
            <div class="symbol symbol-35px me-4">
              <span class="symbol-label bg-light-primary">
                <i class="ki-outline ki-abstract-24 fs-2 text-primary"></i>
              </span>
            </div>
            <div class="d-flex flex-column">
              <a href="#" class="text-gray-800 text-hover-primary fs-5 fw-bold">{{product.name}}</a>
              <div class="text-gray-500 fw-semibold fs-7">{{product.percentage}}% of total</div>
            </div>
          </div>
          <div class="text-end">
            <div class="text-gray-800 fs-5 fw-bold">${{product.value | number}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Ventas Recientes -->
  <div class="col-xxl-6 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.RecentSales' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.Last30Days' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <div class="d-flex flex-stack">
          <div class="text-gray-700 fw-semibold fs-6 me-2">{{ 'COMMON.OrderID' | translate }}</div>
          <div class="text-gray-700 fw-semibold fs-6">{{ 'COMMON.Status' | translate }}</div>
        </div>
        <div class="separator separator-dashed my-3"></div>

        <!-- Lista de Ventas Recientes -->
        <div *ngFor="let sale of recentSales" class="d-flex flex-stack py-3">
          <div class="d-flex align-items-center me-2">
            <div class="symbol symbol-35px me-4">
              <span class="symbol-label" [ngClass]="sale.status === 'Completed' ? 'bg-light-success' : 'bg-light-warning'">
                <i class="ki-outline ki-document fs-2" [ngClass]="sale.status === 'Completed' ? 'text-success' : 'text-warning'"></i>
              </span>
            </div>
            <div class="d-flex flex-column">
              <a href="#" class="text-gray-800 text-hover-primary fs-5 fw-bold">{{sale.id}}</a>
              <div class="d-flex">
                <div class="text-gray-500 fw-semibold fs-7">{{sale.customer}}</div>
                <div class="text-gray-500 fw-semibold fs-7 ms-2">${{sale.amount | number}}</div>
              </div>
            </div>
          </div>
          <div class="text-end">
            <span class="badge" [ngClass]="sale.status === 'Completed' ? 'badge-light-success' : 'badge-light-warning'">{{sale.status}}</span>
            <div class="text-gray-500 fw-semibold fs-7">{{sale.date}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Principales Clientes y Ventas por Región -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Principales Clientes -->
  <div class="col-xxl-6 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.TopCustomers' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.BySalesVolume' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <div class="d-flex flex-stack">
          <div class="text-gray-700 fw-semibold fs-6 me-2">{{ 'COMMON.Customer' | translate }}</div>
          <div class="text-gray-700 fw-semibold fs-6">{{ 'COMMON.Amount' | translate }}</div>
        </div>
        <div class="separator separator-dashed my-3"></div>

        <!-- Lista de Clientes -->
        <div *ngFor="let customer of topCustomers" class="d-flex flex-stack py-3">
          <div class="d-flex align-items-center me-2">
            <div class="symbol symbol-35px me-4">
              <span class="symbol-label bg-light-info">
                <i class="ki-outline ki-profile-user fs-2 text-info"></i>
              </span>
            </div>
            <div class="d-flex flex-column">
              <a href="#" class="text-gray-800 text-hover-primary fs-5 fw-bold">{{customer.name}}</a>
              <div class="text-gray-500 fw-semibold fs-7">{{customer.percentage}}% of total</div>
            </div>
          </div>
          <div class="text-end">
            <div class="text-gray-800 fs-5 fw-bold">${{customer.value | number}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Ventas por Región -->
  <div class="col-xxl-6 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.SalesByRegion' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.CurrentYear' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.Export' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <div class="d-flex flex-stack">
          <div class="text-gray-700 fw-semibold fs-6 me-2">{{ 'COMMON.Region' | translate }}</div>
          <div class="text-gray-700 fw-semibold fs-6">{{ 'COMMON.Amount' | translate }}</div>
        </div>
        <div class="separator separator-dashed my-3"></div>

        <!-- Lista de Regiones -->
        <div *ngFor="let region of salesByRegion" class="d-flex flex-stack py-3">
          <div class="d-flex align-items-center me-2">
            <div class="symbol symbol-35px me-4">
              <span class="symbol-label bg-light-warning">
                <i class="ki-outline ki-geolocation fs-2 text-warning"></i>
              </span>
            </div>
            <div class="d-flex flex-column">
              <a href="#" class="text-gray-800 text-hover-primary fs-5 fw-bold">{{region.region}}</a>
              <div class="text-gray-500 fw-semibold fs-7">{{region.percentage}}% of total</div>
            </div>
          </div>
          <div class="text-end">
            <div class="text-gray-800 fs-5 fw-bold">${{region.value | number}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
