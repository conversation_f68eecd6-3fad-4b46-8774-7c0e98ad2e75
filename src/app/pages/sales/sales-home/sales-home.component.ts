import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { SalesService } from '../sales.service';
import { getCSSVariableValue } from '../../../_metronic/kt/_utils';

@Component({
    selector: 'app-sales-home',
    templateUrl: './sales-home.component.html',
    styleUrls: ['./sales-home.component.scss'],
    standalone: false
})
export class salesHomeComponent implements OnInit, OnDestroy {
  // Estadísticas del dashboard
  totalSales: number = 0;
  totalRevenue: number = 0;
  totalCustomers: number = 0;
  pendingOrders: number = 0;

  // Datos para gráficos
  salesTrends: any[] = [];
  topProducts: any[] = [];
  topCustomers: any[] = [];
  recentSales: any[] = [];
  salesByRegion: any[] = [];

  // Colores para gráficos
  chartColors = {
    primary: getCSSVariableValue('--kt-primary'),
    success: getCSSVariableValue('--kt-success'),
    info: getCSSVariableValue('--kt-info'),
    warning: getCSSVariableValue('--kt-warning'),
    danger: getCSSVariableValue('--kt-danger')
  };

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private salesService: SalesService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // Cargar datos del dashboard
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones para evitar memory leaks
    this.subscriptions.forEach(sb => sb.unsubscribe());
  }

  loadDashboardData(): void {
    // Simular datos para el dashboard
    // En un entorno real, estos datos vendrían de la API

    // Estadísticas
    this.totalSales = 1875;
    this.totalRevenue = 356250;
    this.totalCustomers = 243;
    this.pendingOrders = 28;

    // Datos para gráficos
    this.salesTrends = [
      { month: 'Jan', value: 42000 },
      { month: 'Feb', value: 53000 },
      { month: 'Mar', value: 57000 },
      { month: 'Apr', value: 69000 },
      { month: 'May', value: 75000 },
      { month: 'Jun', value: 60250 }
    ];

    this.topProducts = [
      { name: 'Premium Laptop', value: 68500, percentage: 19.2 },
      { name: 'Wireless Headphones', value: 52300, percentage: 14.7 },
      { name: 'Smart Watch', value: 45200, percentage: 12.7 },
      { name: 'Gaming Console', value: 38700, percentage: 10.9 },
      { name: 'Smartphone', value: 32100, percentage: 9.0 }
    ];

    this.topCustomers = [
      { name: 'Tech Solutions Inc.', value: 42500, percentage: 11.9 },
      { name: 'Global Retail Co.', value: 38700, percentage: 10.9 },
      { name: 'Modern Electronics', value: 35200, percentage: 9.9 },
      { name: 'Smart Devices Ltd.', value: 28700, percentage: 8.1 },
      { name: 'Digital World', value: 25100, percentage: 7.0 }
    ];

    this.salesByRegion = [
      { region: 'North', value: 125000, percentage: 35.1 },
      { region: 'South', value: 98000, percentage: 27.5 },
      { region: 'East', value: 72000, percentage: 20.2 },
      { region: 'West', value: 61250, percentage: 17.2 }
    ];

    this.recentSales = [
      { id: 'SO-2023-0587', customer: 'Tech Solutions Inc.', date: '2023-06-15', amount: 12450, status: 'Completed' },
      { id: 'SO-2023-0586', customer: 'Global Retail Co.', date: '2023-06-14', amount: 8750, status: 'Completed' },
      { id: 'SO-2023-0585', customer: 'Modern Electronics', date: '2023-06-12', amount: 15200, status: 'Pending' },
      { id: 'SO-2023-0584', customer: 'Smart Devices Ltd.', date: '2023-06-10', amount: 5680, status: 'Completed' },
      { id: 'SO-2023-0583', customer: 'Digital World', date: '2023-06-08', amount: 9340, status: 'Completed' }
    ];

    // Actualizar la vista
    this.cdr.detectChanges();
  }
}
