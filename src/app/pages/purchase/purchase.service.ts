import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export class Status {
  id: number;
  name: string;
}

const statuses: Status[] = [{
  id: 1, name: 'Not Started',
}, {
  id: 2, name: 'In Progress',
}, {
  id: 3, name: 'Deferred',
}, {
  id: 4, name: 'Need Assistance',
}, {
  id: 5, name: 'Completed',
},
];

@Injectable({
  providedIn: 'root'
})
export class PurchaseService {

  baseUrl: string;
  constructor(private http: HttpClient) {
    /*this.baseUrl = `${environment.appUrls.hr}`;*/
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }

  list(query: any = {}, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
      params: {
        currentPage: page.toString(),
        ...query
      }
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }
  getWarehouses(): Observable<any> {
    return this.http.get<any>('api/Warehouses');
  }
  getbranches(): Observable<any> {
    return this.http.get<any>('api/Branch');
  }
  getSupplierTypeDropdown(): Observable<any> {
    return this.http.get<any>('api/SupplierClassifications');
  }

  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }

  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }
  getSuppliersDropdown(): Observable<any> {
    return this.http.get<any>('api/Suppliers/dropdown');
  }
  getProductList(): Observable<any> {
    return this.http.get<any>('api/Products/GetProductList');
  }
  getpurchaseTypes(): Observable<any> {
    return this.http.get<any>('api/purchaseTypes');
  }
  getUsers(): Observable<any> {
    return this.http.get<any>('api/FalconUsers');
  }


  getCurrency(): Observable<any> {
    return this.http.get<any>('api/Currency');
  }
  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }
  getDepartDropdown(): Observable<any> {
    return this.http.get<any>('api/Departments');
  }
  // getUsers(): Observable<any> {
  //   return this.http.get<any>(this.baseUrl + 'users/list');
  // }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }

  getStatuses() {
    return statuses;
  }


  batch(obj: any): Observable<any> {
    return this.http.post(this.baseUrl + 'batch/', obj);
  }

  // Métodos para el dashboard

  // Método para obtener estadísticas del dashboard
  getDashboardStats(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    // Por ahora, devolvemos datos simulados
    return of({
      totalPurchases: 1248,
      pendingOrders: 42,
      totalSuppliers: 156,
      purchaseValue: 284750
    });
  }

  // Método para obtener tendencias de compras
  getPurchasesTrends(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { month: 'Jan', value: 65000 },
      { month: 'Feb', value: 78000 },
      { month: 'Mar', value: 72000 },
      { month: 'Apr', value: 84000 },
      { month: 'May', value: 93000 },
      { month: 'Jun', value: 86000 }
    ]);
  }

  // Método para obtener los principales proveedores
  getTopSuppliers(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { name: 'Tech Solutions Inc.', value: 78500, percentage: 27.6 },
      { name: 'Global Supplies Co.', value: 52300, percentage: 18.4 },
      { name: 'Office Essentials', value: 45200, percentage: 15.9 },
      { name: 'Industrial Parts Ltd.', value: 38700, percentage: 13.6 },
      { name: 'Quality Materials', value: 32100, percentage: 11.3 }
    ]);
  }

  // Método para obtener compras por categoría
  getPurchasesByCategory(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { category: 'Raw Materials', value: 125000, percentage: 43.9 },
      { category: 'Office Supplies', value: 68000, percentage: 23.9 },
      { category: 'Equipment', value: 52000, percentage: 18.3 },
      { category: 'Services', value: 39750, percentage: 13.9 }
    ]);
  }

  // Método para obtener compras recientes
  getRecentPurchases(): Observable<any> {
    // En un entorno real, esto haría una llamada a la API
    return of([
      { id: 'PO-2023-0587', supplier: 'Tech Solutions Inc.', date: '2023-06-15', amount: 12450, status: 'Completed' },
      { id: 'PO-2023-0586', supplier: 'Global Supplies Co.', date: '2023-06-14', amount: 8750, status: 'Completed' },
      { id: 'PO-2023-0585', supplier: 'Industrial Parts Ltd.', date: '2023-06-12', amount: 15200, status: 'Pending' },
      { id: 'PO-2023-0584', supplier: 'Office Essentials', date: '2023-06-10', amount: 5680, status: 'Completed' },
      { id: 'PO-2023-0583', supplier: 'Quality Materials', date: '2023-06-08', amount: 9340, status: 'Completed' }
    ]);
  }
}
