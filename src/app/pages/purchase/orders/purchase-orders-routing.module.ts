import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {FollowUpRequisitionComponent} from './follow-up-requisition/follow-up-requisition.component';
import {PurchaseOrdersComponent} from './purchase-orders/purchase-orders.component';
import {PurchaseRequisitionComponent} from './purchase-requisition/purchase-requisition.component';
import {RfqComparisonsComponent} from './rfq-comparisons/rfq-comparisons.component';
import {RfqComponent} from './rfq/rfq.component';
import { Purchase_requisition_viewComponent } from './purchase-requisition/purchase_requisition_view/purchase_requisition_view.component';
import { Purchase_orders_viewComponent } from './purchase-orders/purchase_orders_view/purchase_orders_view.component';
import { Follow_up_requisition_viewComponent } from './follow-up-requisition/follow_up_requisition_view/follow_up_requisition_view.component';
import { Rfq_viewComponent } from './rfq/rfq_view/rfq_view.component';
import { Rfq_comparisons_viewComponent } from './rfq-comparisons/rfq_comparisons_view/rfq_comparisons_view.component';
import { Bills_viewComponent } from '../vendor-bills/bills/bills_view/bills_view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'purchase_requisition',
        pathMatch: 'full'
    },
    {
        path: 'purchase_requisition',
        component: PurchaseRequisitionComponent
    },
    {
        path: 'purchase_orders',
        component: PurchaseOrdersComponent
    },
    {
        path: 'follow_up_requisition',
        component: FollowUpRequisitionComponent
    },
    {
        path: 'rfq',
        component: RfqComponent
    },
    {
        path: 'rfq_comparisons',
        component: RfqComparisonsComponent
    },
    {
        path: 'purchase_requisition_view/:id',
        component: Purchase_requisition_viewComponent
    },
    {
        path: 'purchase_requisition_view',
        component: Purchase_requisition_viewComponent
    },

    {
        path: 'purchase_orders_view/:id',
        component: Purchase_orders_viewComponent
    },
    {
        path: 'purchase_orders_view',
        component: Purchase_orders_viewComponent
    },


    {
        path: 'follow_up_requisition_view/:id',
        component: Follow_up_requisition_viewComponent
    },
    {
        path: 'follow_up_requisition_view',
        component: Follow_up_requisition_viewComponent
    },

    {
        path: 'rfq_view/:id',
        component: Rfq_viewComponent

    },
    {
        path: 'rfq_view',
        component: Rfq_viewComponent

    },
    {
        path: 'rfq_comparisons_view/:id',
        component: Rfq_comparisons_viewComponent

    },
    {
        path: 'rfq_comparisons_view',
        component: Rfq_comparisons_viewComponent

    },
 






];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PurchaseOrdersRoutingModule {
}
