/* تحسين تخطيط الليبل والحقول - منع التداخل */
.form-label {
  display: inline-block;
  width: 180px;
  min-width: 180px;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #3f4254;
  font-size: 0.95rem;
  line-height: 1.4;
  vertical-align: top;
}

/* تحسين المسافات بشكل طبيعي */
.main-inputs .row {
  margin-bottom: 0.75rem;
}

.main-inputs .row:last-child {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 0.5rem;
}

/* تحسين بسيط للحقول */
.form-control {
  width: 100%;
  max-width: 400px;
}

/* تحسين ng-select بشكل بسيط */
::ng-deep ng-select {
  width: 100%;
  max-width: 400px;
}

::ng-deep .ng-select.ng-select-single .ng-select-container {
  height: 38px;
}

/* تحسين textarea */
textarea.form-control {
  max-width: 500px;
  resize: vertical;
}

/* تحسين Bootstrap rows */
.row.mb-4 {
  margin-bottom: 0.75rem !important;
}

/* تحسين form-select-container */
.form-select-container {
  width: 100%;
  max-width: 400px;
}

/* تحسين المحاذاة للحقول */
.form-group {
  margin-bottom: 0.5rem;
}