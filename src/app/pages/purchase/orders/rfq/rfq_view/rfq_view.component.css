/* تحسين تخطيط الليبل والحقول - منع التداخل */
.form-label {
  display: block;
  width: 100%;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #3f4254;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* تحسين المسافات بشكل طبيعي */
.main-inputs .row {
  margin-bottom: 0.75rem;
}

.main-inputs .row:last-child {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 0.5rem;
}

/* تحسين بسيط للحقول */
.form-control {
  width: 100%;
}

/* تحسين ng-select بشكل بسيط */
::ng-deep ng-select {
  width: 100%;
}

::ng-deep .ng-select.ng-select-single .ng-select-container {
  height: 38px;
}

/* تحسين textarea */
textarea.form-control {
  resize: vertical;
}

/* تحسين Bootstrap rows */
.row.mb-4 {
  margin-bottom: 0.75rem !important;
}

/* تحسين form-select-container */
.form-select-container {
  width: 100%;
}

/* تحسين المحاذاة للحقول */
.form-group {
  margin-bottom: 0.5rem;
}

.col-12.col-md-6 .form-group {
  width: 100%;
}

/* ضبط المحاذاة للحقول في Bootstrap */
.pe-md-2, .ps-md-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}