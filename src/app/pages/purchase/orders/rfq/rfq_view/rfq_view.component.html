<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.RFQ" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.RFQ',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs">
        <!-- First Row: Request Date and Care Of -->
        <div class="row mb-4">
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group">
              <label class="form-label mb-2">{{ "COMMON.RequestDate" | translate }}</label>
              <input
                id="date"
                type="date"
                formControlName="date"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-12 col-md-6 ps-md-2">
            <div class="form-group">
              <label for="careof" class="form-label mb-2">
                {{ "COMMON.Careof" | translate }}
              </label>
              <ng-select
                id="careof"
                formControlName="careof"
                bindLabel="name"
                bindValue="id"
                [items]="careofs"
                class="form-select-container"
              ></ng-select>
            </div>
          </div>
        </div>
        <!-- Second Row: Supplier and Project -->
        <div class="row mb-4">
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group">
              <label for="supplier" class="form-label mb-2">
                {{ "COMMON.Supplier" | translate }}
              </label>
              <ng-select
                id="supplier"
                formControlName="supplierId"
                bindLabel="nameAr"
                bindValue="id"
                [items]="suppliers"
                class="form-select-container"
              ></ng-select>
            </div>
          </div>
          <div class="col-12 col-md-6 ps-md-2">
            <div class="form-group">
              <label for="projectid" class="form-label mb-2">
                {{ "COMMON.Project" | translate }}
              </label>
             <ng-select
                id="projectid"
                formControlName="projectid"
                bindLabel="nameAr"
                bindValue="id"
                [items]="projects"
                class="form-select-container"
              ></ng-select>
            </div>
          </div>
        </div>

        <!-- Third Row: Employee Name and Requesting Department -->
        <div class="row mb-4">
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group">
              <label for="employeeName" class="form-label mb-2">
                {{ "COMMON.EmployeeNameAr" | translate }}
              </label>
              <input
                type="text"
                id="employeeName"
                name="employeeName"
                class="form-control"
                formControlName="employeeName"
              />
            </div>
          </div>
          <div class="col-12 col-md-6 ps-md-2">
            <div class="form-group">
              <label for="requestingDepartment" class="form-label mb-2">
                {{ "COMMON.RequestingDepartment" | translate }}
              </label>
              <input
                type="text"
                id="requestingDepartment"
                name="requestingDepartment"
                class="form-control"
                formControlName="requestingDepartment"
              />
            </div>
          </div>
        </div>

        <!-- Fourth Row: Subject -->
        <div class="row mb-4">
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group">
              <label for="subject" class="form-label mb-2">
                {{ "COMMON.Subject" | translate }}
              </label>
              <textarea
                id="subject"
                name="subject"
                class="form-control"
                formControlName="subject"
                rows="2"
              ></textarea>
            </div>
          </div>
          <div class="col-12 col-md-6 ps-md-2">
            <!-- مساحة للعنصر الثاني إذا لزم الأمر مستقبلاً -->
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-12">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  [class.active]="activeTab === 'tab1'"
                  (click)="setActiveTab('tab1')"
                >
                  <span class="fw-bold">{{ "COMMON.ProductLabel" | translate }}</span>
                </button>
              </li>
              <li class="nav-item" role="presentation"> 
                <button
                  class="nav-link"
                  [class.active]="activeTab === 'tab2'"
                  (click)="setActiveTab('tab2')"
                >
                  <span class="fw-bold">{{ "COMMON.AdditionalInformation" | translate }}</span>
                </button>
              </li>
            </ul>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <div class="tab-content mt-3" id="myTabContent">
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab1'"
              [class.active]="activeTab === 'tab1'"
              *ngIf="activeTab === 'tab1'"
            >
              <!-- شبكة المنتجات -->
              <div class="products-grid-container mt-3 mb-4">
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
              </div>
              
              <div class="row mb-4">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label class="form-label mb-2">Total</label>
                    <input type="number" [value]="total" class="form-control" disabled/>
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label class="form-label mb-2">{{ "COMMON.NumbeRofRows" | translate }}</label>
                    <input type="text" formControlName="NumbeRofRows" class="form-control"/>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="tab-pane fade"
              [class.show]="activeTab === 'tab2'"
              [class.active]="activeTab === 'tab2'"
              *ngIf="activeTab === 'tab2'"
            >
              <!-- مدة التوريد -->
              <div class="row mb-4 mt-3">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label class="form-label mb-2">{{ "COMMON.SupplyDuration" | translate }}</label>
                    <div class="input-group">
                      <input type="text" id="supplyDuration" name="supplyDuration" class="form-control" formControlName="supplyDuration" />
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label class="form-label mb-2">{{ "COMMON.SupplyLocation" | translate }}</label>
                    <input type="text" id="supplyLocation" name="supplyLocation" class="form-control" formControlName="supplyLocation" />
                  </div>
                </div>
              </div>
              <!-- Document Number and Order Request Number -->
              <div class="row mb-4">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label for="docno" class="form-label mb-2">
                      {{ "COMMON.DocumentNumber" | translate }}
                    </label>
                    <input
                      type="text"
                      id="docno"
                      name="docno"
                      class="form-control"
                      formControlName="docno"
                    />
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label for="orderRequestNo" class="form-label mb-2">
                      {{ "COMMON.OrderRequestNo" | translate }}
                    </label>
                    <input
                      type="text"
                      id="orderRequestNo"
                      name="orderRequestNo"
                      class="form-control"
                      formControlName="orderRequestNo"
                    />
                  </div>
                </div>
              </div>
              
              <!-- Taxes and Commitment Duration -->
              <div class="row mb-4">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label for="tax" class="form-label mb-2">
                      {{ "COMMON.Taxes" | translate }}
                    </label>
                    <ng-select
                      id="tax"
                      formControlName="tax"
                      bindLabel="nameAr"
                      bindValue="id"
                      [items]="taxes"
                      class="form-select-container"
                    ></ng-select>
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label for="commitmentDuration" class="form-label mb-2">
                      {{ "COMMON.CommitmentDuration" | translate }}
                    </label>
                    <input
                      type="text"
                      id="commitmentDuration"
                      name="commitmentDuration"
                      class="form-control"
                      formControlName="commitmentDuration"
                    />
                  </div>
                </div>
              </div>
              
              <!-- Delivery Place and Payment Terms -->
              <div class="row mb-4">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label for="deliveryPlace" class="form-label mb-2">
                      {{ "COMMON.DeliveryPlace" | translate }}
                    </label>
                    <ng-select
                      id="deliveryPlace"
                      formControlName="deliveryPlace"
                      bindLabel="nameAr"
                      bindValue="id"
                      [items]="deliveryPlaces"
                      class="form-select-container"
                    ></ng-select>
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label for="paymentTerms" class="form-label mb-2">
                      {{ "COMMON.PaymentTerms" | translate }}
                    </label>
                    <ng-select
                      id="paymentTerms"
                      formControlName="paymentTerms"
                      bindLabel="nameAr"
                      bindValue="id"
                      [items]="paymentTermsList"
                      class="form-select-container"
                    ></ng-select>
                  </div>
                </div>
              </div>
              
              <!-- Payment Method and Additional Fields -->
              <div class="row mb-4">
                <div class="col-12 col-md-6 pe-md-2">
                  <div class="form-group">
                    <label for="paymentMethod" class="form-label mb-2">
                      {{ "CUSTOMER.PAYMENT_METHOD" | translate }}
                    </label>
                    <ng-select
                      id="paymentMethod"
                      formControlName="paymentMethod"
                      bindLabel="nameAr"
                      bindValue="id"
                      [items]="paymentMethods"
                      class="form-select-container"
                    ></ng-select>
                  </div>
                </div>
                <div class="col-12 col-md-6 ps-md-2">
                  <div class="form-group">
                    <label for="additionalInfo" class="form-label mb-2">
                      {{ "COMMON.AdditionalInfo" | translate }}
                    </label>
                    <textarea
                      id="additionalInfo"
                      name="additionalInfo"
                      class="form-control"
                      formControlName="additionalInfo"
                      rows="2"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
