<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.RFQ" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.RFQ',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
         
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.RequestDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="careof" class="form-label">
              {{ "COMMON.Careof" | translate }}
            </label>
            <ng-select
              id="careof"
              formControlName="careof"
              bindLabel="name"
              bindValue="id"
              [items]="careofs"
            ></ng-select>
          </div>

        </div>
        <div class="row">


          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="supplier" class="form-label">
              {{ "COMMON.Supplier" | translate }}
            </label>
            <ng-select
              id="supplier"
              formControlName="supplierId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="suppliers"
            ></ng-select>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="projectid" class="form-label">
              {{ "COMMON.Project" | translate }}
            </label>
           <ng-select
              id="projectid"
              formControlName="projectid"
              bindLabel="nameAr"
              bindValue="id"
              [items]="projects"
            ></ng-select>
          </div>
    
        </div>

         <div class="row">

          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="employeeName" class="form-label">
              {{ "COMMON.EmployeeNameAr" | translate }}
            </label>
            <input
              type="text"
              id="employeeName"
              name="employeeName"
              class="form-control"
              formControlName="employeeName"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="requestingDepartment" class="form-label">
              {{ "COMMON.RequestingDepartment" | translate }}
            </label>
            <input
              type="text"
              id="requestingDepartment"
              name="requestingDepartment"
              class="form-control"
              formControlName="requestingDepartment"
            />
          </div>
         </div>

        <div class="row">
          <div class="form-group col-xl-8 col-md-6 col-sm-12">
            <label for="subject" class="form-label">
              {{ "COMMON.Subject" | translate }}
            </label>
            <textarea
              id="subject"
              name="subject"
              class="form-control"
              formControlName="subject"
              rows="3" 
            ></textarea>
          </div>
          
        </div>



        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.ProductLabel" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.AdditionalInformation" | translate }}
            </button>
              
            </li>
   
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
  
              
                    <div class="row">
  
                        <div class="form-group col-xl-3 col-md-4 col-sm-12 ">
                            <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Total</div>
                            <input type="number" [value]="total" class="form-control" disabled/>
                        </div>
                    </div>
                        <div class="row">
  
                            <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                              <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">{{ "COMMON.NumbeRofRows" | translate }} </div>
                              <input type="text" formControlName="NumbeRofRows" class="form-control"/>
                        </div>
                    </div>
  
            </div>
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
                  <div class="row">
                    <div class="form-group col-xl-4 col-md-6 col-sm-12">
                      <label for="docno" class="form-label">
                        {{ "COMMON.DocumentNumber" | translate }}
                      </label>
                      <input
                      type="text"
                      id="docno"
                      name="docno"
                      class="form-control"
                      formControlName="docno"
                    />
                    </div>
                    <div class="form-group col-xl-4 col-md-6 col-sm-12">
                      <label for="orderRequestNo" class="form-label">
                        {{ "COMMON.OrderRequestNo" | translate }}
                      </label>
                      <input
                      type="text"
                      id="orderRequestNo"
                      name="orderRequestNo"
                      class="form-control"
                      formControlName="orderRequestNo"
                    />
                    </div>
                  </div>
                      <div class="row">
                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="tax" class="form-label">
                          {{ "COMMON.Taxes" | translate }}
                        </label>
                        <ng-select
                          id="tax"
                          formControlName="tax"
                          bindLabel="name"
                          bindValue="id"
                          [items]="taxes"
                        ></ng-select>
                      </div>

                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="commitmentDuration" class="form-label">
                          {{ "COMMON.CommitmentDuration" | translate }}
                        </label>
                        <input
                        type="text"
                        id="commitmentDuration"
                        name="commitmentDuration"
                        class="form-control"
                        formControlName="commitmentDuration"
                      />
                      </div>
                    </div>
              
                    <div class="row">
                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="deliveryPlace" class="form-label">
                          {{ "COMMON.DeliveryPlace" | translate }}
                        </label>
                        <ng-select
                          id="deliveryPlace"
                          formControlName="deliveryPlace"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="deliveryPlaces"
                        ></ng-select>
                      </div>

                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="paymentTerms" class="form-label">
                          {{ "COMMON.PaymentTerms" | translate }}
                        </label>
                        <input
                        type="text"
                        id="paymentTerms"
                        name="paymentTerms"
                        class="form-control"
                        formControlName="paymentTerms"
                      />
                      </div>
                    </div>
                        <div class="row">
   

                           <div class="form-group col-xl-4 col-md-6 col-sm-12">
                            <label for="paymentMethod" class="form-label">
                              {{ "CUSTOMER.PAYMENT_METHOD" | translate }}
                            </label>
                            <ng-select
                              id="paymentMethod"
                              formControlName="paymentMethod"
                              bindLabel="name"
                              bindValue="id"
                              [items]="paymentMethods"
                            ></ng-select>
                          </div>
                          <div class="form-group col-xl-4 col-md-6 col-sm-12">
                            <label for="supplyDuration" class="form-label">
                              {{ "COMMON.SupplyDuration" | translate }}
                            </label>
                            <input
                            type="text"
                            id="supplyDuration"
                            name="supplyDuration"
                            class="form-control"
                            formControlName="supplyDuration"
                          />
                          <label for="supplyDuration" class="form-label">
                            {{ "COMMON.Days" | translate }}
                          </label>
                          </div>
                      
                    </div>

        
  
            </div>
            </div>
       
  

      </div>

    </div>
  </div>
  </div>

</form>
