<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.PurchaseOrder" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
          <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.PurchaseOrders',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <!-- سطر التاريخ والقسم -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="department" class="form-label">
              {{ "COMMON.Department" | translate }}
            </label>
            <ng-select
              id="department"
              formControlName="department"
              bindLabel="nameAr"
              bindValue="id"
              [items]="departments"
            ></ng-select>
          </div>
        </div>

        <!-- سطر المشروع والمورد -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="projectid" class="form-label">
              {{ "COMMON.Project" | translate }}
            </label>
            <ng-select
              id="projectid"
              formControlName="projectid"
              bindLabel="nameAr"
              bindValue="id"
              [items]="projects"
            ></ng-select>
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="supplier" class="form-label">
              {{ "COMMON.Supplier" | translate }}
            </label>
            <ng-select
              id="supplier"
              formControlName="supplierId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="suppliers"
            ></ng-select>
          </div>
        </div>

        <!-- سطر رقم طلب الشراء والمباشر -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="envelopeOpeningCommitteePurchaseRequestNo" class="form-label">
              {{ "COMMON.FetchFromEnvelopeOpeningCommitteePurchaseRequestNo" | translate }}
            </label>
            <input
              type="text"
              id="envelopeOpeningCommitteePurchaseRequestNo"
              name="envelopeOpeningCommitteePurchaseRequestNo"
              class="form-control"
              formControlName="envelopeOpeningCommitteePurchaseRequestNo"
            />
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="direct" class="form-label">
              {{ "COMMON.Direct" | translate }}
            </label>
            <ng-select
              id="direct"
              formControlName="direct"
              bindLabel="nameAr"
              bindValue="id"
              [items]="directs"
            ></ng-select>
          </div>
        </div>

        <!-- سطر الجلب من طلب الشراء والنص -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label class="form-label">{{ "COMMON.FetchFromPurchaseRequest" | translate }}</label>
            <div class="mt-2">
              <dx-check-box
                [value]="FetchFromPurchaseRequest"
                formControlName="FetchFromPurchaseRequest"
                valueExpr="FetchFromPurchaseRequest"
                text="{{ 'COMMON.EnableFetch' | translate }}"
              ></dx-check-box>
            </div>
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="text" class="form-label">{{ "COMMON.Notes" | translate }}</label>
            <textarea
              id="text"
              name="text"
              class="form-control"
              formControlName="text"
              rows="3"
            ></textarea>
          </div>
        </div>



        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.ProductLabel" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.AdditionalInformation" | translate }}
            </button>
              
            </li>
   
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
              <div class="row">
                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.Total" | translate }}</div>
                    <input type="number" [value]="total" class="form-control no-shadow-input rows-field" readonly/>
                </div>

                <div class="form-group col-xl-3 col-md-4 col-sm-12">
                    <div class="form-label mb-1">{{ "COMMON.NumbeRofRows" | translate }}</div>
                    <input type="text" [value]="newdata.get('NumbeRofRows')?.value" class="form-control no-shadow-input rows-field" readonly/>
                 </div>
               </div>
  
            </div>
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
    
  
              
                    <!-- سطر مكان التسليم والشحن -->
                    <div class="row">
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="deliveryPlace" class="form-label">
                          {{ "COMMON.DeliveryPlace" | translate }}
                        </label>
                        <ng-select
                          id="deliveryPlace"
                          formControlName="deliveryPlace"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="deliveryPlaces"
                        ></ng-select>
                      </div>
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="shipping" class="form-label">
                          {{ "COMMON.Shipping" | translate }}
                        </label>
                        <ng-select
                          id="shipping"
                          formControlName="shipping"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="shipments"
                        ></ng-select>
                      </div>
                    </div>

                    <!-- سطر شروط الدفع وتاريخ التسليم -->
                    <div class="row">
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="paymentTerms" class="form-label">
                          {{ "COMMON.PaymentTerms" | translate }}
                        </label>
                        <ng-select
                          id="paymentTerms"
                          formControlName="paymentTerms"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="paymentTermses"
                        ></ng-select>
                      </div>
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="deliveryDate" class="form-label">
                          {{ "COMMON.DeliveryDate" | translate }}
                        </label>
                        <ng-select
                          id="deliveryDate"
                          formControlName="deliveryDate"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="deliveryDates"
                        ></ng-select>
                      </div>
                    </div>

                    <!-- سطر العملة والإجمالي -->
                    <div class="row">
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="currencyid" class="form-label">
                          {{ "COMMON.Currency" | translate }}
                        </label>
                        <ng-select
                          id="currencyid"
                          formControlName="currencyid"
                          bindLabel="nameAr"
                          bindValue="id"
                          [items]="currency"
                        ></ng-select>
                      </div>
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="total" class="form-label">
                          {{ "COMMON.Total" | translate }}
                        </label>
                        <input
                          type="text"
                          id="total"
                          name="total"
                          class="form-control"
                          formControlName="total"
                        />
                      </div>
                    </div>

                    <!-- سطر ضريبة القيمة المضافة -->
                    <div class="row">
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="VAT%" class="form-label">
                          VAT%
                        </label>
                        <input
                          type="text"
                          id="VAT%"
                          name="VAT%"
                          class="form-control"
                          formControlName="VAT%"
                        />
                      </div>
                      <div class="form-group col-xl-6 col-md-6 col-sm-12">
                        <label for="VAT" class="form-label">
                          VAT
                        </label>
                        <input
                          type="text"
                          id="VAT"
                          name="VAT"
                          class="form-control"
                          formControlName="VAT"
                        />
                      </div>
                    </div>
                    <div class="row">
                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="net" class="form-label">
                          {{ "COMMON.Net" | translate }}
                        </label>
                        <input
                          type="text"
                          id="net"
                          name="net"
                          class="form-control"
                          formControlName="net"
                        />
                      </div>
                      <div class="form-group col-xl-4 col-md-6 col-sm-12">
                        <label for="necessity" class="form-label">
                          {{ "COMMON.Necessity" | translate }}
                        </label>
                        <input
                          type="text"
                          id="necessity"
                          name="total"
                          class="form-control"
                          formControlName="necessity"
                        />
                      </div>
                    </div>
                    <div class="row">
                      <div class="form-group col-xl-8 col-md-6 col-sm-12">
                        <textarea
                          id="text"
                          name="text"
                          class="form-control"
                          formControlName="text"
                          rows="3" 
                        ></textarea>
                      </div>
                      
                    </div>
                    </div>
  
            </div>
            </div>
       
  

      </div>

    </div>
  </div>
  </div>

</form>
