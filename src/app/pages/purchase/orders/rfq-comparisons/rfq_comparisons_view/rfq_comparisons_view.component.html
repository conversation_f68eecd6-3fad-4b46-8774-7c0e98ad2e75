<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.RFQComparisons" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.RFQComparisons',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
         
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.SearchInOffersFromDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label class="form-label">{{"COMMON.ToDate" | translate}}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          
          </div>
        </div>
        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="searchByPurchaseOrderNumber" class="form-label">
              {{ "COMMON.SearchByPurchaseOrderNumber" | translate }}
            </label>
            <input
            type="text"
            id="searchByPurchaseOrderNumber"
            name="searchByPurchaseOrderNumber"
            class="form-control"
            formControlName="searchByPurchaseOrderNumber"
          />
          </div>

          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="searchByOfferNumber" class="form-label">
              {{ "COMMON.SearchByOfferNumber" | translate }}
            </label>
            <input
            type="text"
            id="searchByOfferNumber"
            name="searchByOfferNumber"
            class="form-control"
            formControlName="searchByOfferNumber"
          />
    
        </div>
        </div>

        <div class="row">
   
     <div class="form-group col-xl-8 col-md-6 col-sm-12 ">
      <label for="selectionReasonForManager" class="form-label"> 
         {{ "COMMON.SelectionReasonForManager" | translate }} </label>
      <input
      type="text"
      id="selectionReasonForManager"
      name="selectionReasonForManager"
      class="form-control"
      formControlName="selectionReasonForManager"
    />
    </div>
    <div class="form-group col-xl-2 col-md-6 col-sm-12">
      <div class="label p-2"> {{ "COMMON.All" | translate }}</div>

      <dx-check-box
        [value]="all"
        formControlName="all"
        valueExpr="all"
      ></dx-check-box>
    </div>
         </div>



    </div>
  </div>
  </div>

</form>
