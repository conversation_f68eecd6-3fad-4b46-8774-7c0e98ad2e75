<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <!-- بداية رأس البطاقة -->
    <div class="card-header border-0 cursor-pointer w-100">
      <div class="card-title m-0 d-flex justify-content-between w-100 align-items-center">
        <h3 class="fw-bolder m-0">{{ "COMMON.RFQComparisons" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.RFQComparisons'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.Print" | translate }}
              </a>
            </div>

            <!-- <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Inventory.Receipts'
              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses?withLogo=true"
                data-kt-company-table-filter="delete_row"
              >
                Print With Logo
              </a>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <!-- نهاية رأس البطاقة -->

    <!-- بداية محتوى البطاقة -->
    <div class="card-body border-top p-9">
      <div class="main-inputs">
        <!-- صف التاريخ -->
        <div class="row mb-4">
          <!-- تاريخ البداية -->
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group mb-2">
              <label class="form-label mb-2">{{ "COMMON.SearchInOffersFromDate" | translate }}</label>
              <input
                id="fromDate"
                type="date"
                formControlName="date"
                class="form-control w-100"
              />
            </div>
          </div>
          <!-- تاريخ النهاية -->
          <div class="col-12 col-md-6 ps-md-2">
            <div class="form-group mb-2">
              <label class="form-label mb-2">{{ "COMMON.ToDate" | translate }}</label>
              <input
                id="toDate"
                type="date"
                formControlName="date"
                class="form-control w-100"
              />
            </div>
          </div>
        </div>
        
        <!-- صف أرقام الطلبات -->
        <div class="row mb-4">
          <!-- رقم طلب الشراء -->
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group mb-2">
              <label for="searchByPurchaseOrderNumber" class="form-label mb-2">
                {{ "COMMON.SearchByPurchaseOrderNumber" | translate }}
              </label>
              <input
                type="text"
                id="searchByPurchaseOrderNumber"
                name="searchByPurchaseOrderNumber"
                class="form-control w-100"
                formControlName="searchByPurchaseOrderNumber"
              />
            </div>
          </div>
          <!-- رقم العرض -->
          <div class="col-12 col-md-6 ps-md-2">
            <div class="form-group mb-2">
              <label for="searchByOfferNumber" class="form-label mb-2">
                {{ "COMMON.SearchByOfferNumber" | translate }}
              </label>
              <input
                type="text"
                id="searchByOfferNumber"
                name="searchByOfferNumber"
                class="form-control w-100"
                formControlName="searchByOfferNumber"
              />
            </div>
          </div>
        </div>
        
        <!-- صف سبب الاختيار -->
        <div class="row g-3 mb-4">
          <!-- سبب الاختيار للمدير -->
          <div class="col-12 col-md-6 pe-md-2">
            <div class="form-group mb-2">
              <label for="selectionReasonForManager" class="form-label mb-2">
                {{ "COMMON.SelectionReasonForManager" | translate }}
              </label>
              <input
                type="text"
                id="selectionReasonForManager"
                name="selectionReasonForManager"
                class="form-control w-100"
                formControlName="selectionReasonForManager"
              />
            </div>
          </div>
          <!-- الكل -->
          <div class="col-12 col-md-6 ps-md-2 d-flex align-items-center">
            <div class="form-group mb-2 w-100">
              <label class="form-label mb-2 d-block">{{ "COMMON.All" | translate }}</label>
              <dx-check-box
                [value]="all"
                formControlName="all"
                valueExpr="all"
              ></dx-check-box>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- نهاية محتوى البطاقة -->
  </div>
</form>
