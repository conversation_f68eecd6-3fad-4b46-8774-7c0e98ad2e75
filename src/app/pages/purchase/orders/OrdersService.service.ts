import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class OrdersServiceService {

baseUrl: string;
  constructor(private http: HttpClient) {
    // this.baseUrl = `${environment.appUrls.Adjustments}`;
  }

  details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + id);
  }
  list(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl + `${id}`, {
      params: {
        currentPage: page.toString()
      }
    });
  }
  getKeyValue(): Observable<any> {
    return this.http.get<any>(this.baseUrl + 'names');
  }


  getUsers(): Observable<any> {
    return this.http.get<any>('api/FalconUsers');
  }

  create(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  update(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  delete(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  activate(id: string): Observable<any> {
    return this.http.post(this.baseUrl + 'activate/' + id, null);
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }
  getWarehouses(): Observable<any> {
    return this.http.get<any>('api/Warehouses');
  }
  getbranches(): Observable<any> {
    return this.http.get<any>('api/Branch');
  }
  getfinancialYear(): Observable<any> {
    return this.http.get<any>('api/financialYear');
  }
  getSuppliersDropdown(): Observable<any> {
    return this.http.get<any>('api/Suppliers/dropdown');
  }
  getCustDropdown(): Observable<any> {
    return this.http.get<any>('api/Customer/dropdown');
  }
  getFinancialEntities(entityId : any): Observable<any> {
    if (entityId === 1) {
      return this.http.get<any>('api/Tenders/TendersDropdown');
    } else if (entityId === 2) {
      return this.http.get<any>('api/Machine/dropdown');
    } else if (entityId === 3) {
      return this.http.get<any>('api/Assets/dropdown');
    } else if (entityId === 4) {
      return this.http.get<any>('api/Departments');
    } else if (entityId === 5) {
      return this.http.get<any>('api/Suppliers/dropdown');
    } else if (entityId === 6) {
      return this.http.get<any>('api/Customer/dropdown');
    } else if (entityId === 7) {
      return this.http.get<any>('api/Cars/dropdown');
    } else {
      // Handle cases where entityId doesn't match
      throw new Error('Invalid entityId');
    }
  }
  getCostCenters(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccounts/AnalyticAccounDropdown');
  }

  getCostGroups(): Observable<any> {
    return this.http.get<any>('api/AnalyticAccountsGroups/AnalyticAccountsGroupsDropdown');
  }
  
  getEmployeesDropdown(): Observable<any> {
    return this.http.get<any>('api/employees/EmployeesDropdown');
  }
  getDepartDropdown(): Observable<any> {
    return this.http.get<any>('api/Departments');
  }
  getCurrency(): Observable<any> {
    return this.http.get<any>('api/Currency');
  }

}
