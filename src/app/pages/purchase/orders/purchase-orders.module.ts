import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {FollowUpRequisitionComponent} from './follow-up-requisition/follow-up-requisition.component';
import {PurchaseOrdersComponent} from './purchase-orders/purchase-orders.component';
import {PurchaseRequisitionComponent} from './purchase-requisition/purchase-requisition.component';
import {RfqComparisonsComponent} from './rfq-comparisons/rfq-comparisons.component';
import {RfqComponent} from './rfq/rfq.component';
import {PurchaseOrdersRoutingModule} from "./purchase-orders-routing.module";
import {SharedModule} from "../../shared/shared.module";
import { Purchase_requisition_viewComponent } from './purchase-requisition/purchase_requisition_view/purchase_requisition_view.component';
import { Purchase_orders_viewComponent } from './purchase-orders/purchase_orders_view/purchase_orders_view.component';
import { Follow_up_requisition_viewComponent } from './follow-up-requisition/follow_up_requisition_view/follow_up_requisition_view.component';
import { Rfq_viewComponent } from './rfq/rfq_view/rfq_view.component';
import { Rfq_comparisons_viewComponent } from './rfq-comparisons/rfq_comparisons_view/rfq_comparisons_view.component';
import { Bills_viewComponent } from '../vendor-bills/bills/bills_view/bills_view.component';
import { ProductsGridControleModule } from '../../general/products-grid-controle/ProductsGridControle.module';


@NgModule({
    declarations: [
        PurchaseRequisitionComponent,
        PurchaseOrdersComponent,
        FollowUpRequisitionComponent,
        RfqComponent,
        RfqComparisonsComponent,
        Purchase_requisition_viewComponent,
        Purchase_orders_viewComponent,
        Follow_up_requisition_viewComponent,
        Rfq_viewComponent,
        Rfq_comparisons_viewComponent,
        
    ],
    imports: [
        PurchaseOrdersRoutingModule,
        SharedModule,
        ProductsGridControleModule,

    ],
    exports: [RouterModule],
})
export class PurchaseOrdersModule {
}
