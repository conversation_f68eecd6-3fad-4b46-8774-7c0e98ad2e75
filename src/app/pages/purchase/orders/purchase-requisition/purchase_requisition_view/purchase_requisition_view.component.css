/* تحسين تخطيط الليبل والحقول - منع التداخل */
.form-label {
  display: inline-block;
  width: 180px;
  min-width: 180px;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #3f4254;
  font-size: 0.95rem;
  line-height: 1.4;
  vertical-align: top;
}

/* تحسين المسافات بشكل مدمج */
.main-inputs .row {
  margin-bottom: 0.25rem;
}

.main-inputs .row:last-child {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 0.1rem;
}

/* تحسين بسيط للحقول */
.form-control {
  width: 100%;
  max-width: 400px;
}

/* تحسين ng-select بشكل بسيط */
::ng-deep ng-select {
  width: 100%;
  max-width: 400px;
}

/* تحسين textarea */
textarea.form-control {
  max-width: 500px;
  resize: vertical;
}