import {
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { finalize } from 'rxjs';
import { formatDate } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { OrdersServiceService } from '../../OrdersService.service';
@Component({
  selector: 'app-purchase_requisition_view',
  templateUrl: './purchase_requisition_view.component.html',
  styleUrls: ['./purchase_requisition_view.component.css'],
  standalone:false
})
export class Purchase_requisition_viewComponent implements OnInit {
   newdata: FormGroup;
   moduleName = 'Purchases.Requisition';
   activeTab: string = 'tab1';
   [x: string]: any;
   subscriptions = new Subscription();
   Product: any[];
   AnalyticAccounts: any[];
   Units: any[];
   data: any[];
   isGridBoxOpened: boolean;
   editorOptions: any;
   gridBoxValue: number[] = [1];
   subscription = new Subscription();
   departments: any[];
   employees: any[];
   requestTypes: any[] = [
    { id: 1, name: 'عادي' },
    { id: 2, name: 'مستعجل' },

   ];
   projects: any[];
   customers: any[];
   responsiblePersons: any[];
   users: any[];
   total = 0;

   // optionControl=null;
    menuOpen = false;
     toggleMenu() {
       this.menuOpen = !this.menuOpen;
     }
     actionsList: string[] = [
       'Export',
       'Send via SMS',
       'Send Via Email',
       'Send Via Whatsapp',
     ];
     modalConfig: ModalConfig = {
       modalTitle: 'Send Sms',
       modalSize: 'lg',
       hideCloseButton(): boolean {
         return true;
       },
       dismissButtonLabel: 'Cancel',
     };
     financial_entity_Types: { id: number; name: string }[] = [
       { id: 1, name: 'Project' },
       { id: 2, name: 'Machine' },
       { id: 3, name: 'Assets' },
       { id: 4, name: 'Department' },
       { id: 5, name: 'Supplier' },
       { id: 6, name: 'Customer' },
       { id: 7, name: 'Cars' },
     ];
     smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
     emailModalConfig: ModalConfig = {
       ...this.modalConfig,
       modalTitle: 'Send Email',
     };
     whatsappModalConfig: ModalConfig = {
       ...this.modalConfig,
       modalTitle: 'Send Whatsapp',
     };
     @ViewChild('smsModal') private smsModal: ModalComponent;
     @ViewChild('emailModal') private emailModal: ModalComponent;
     @ViewChild('whatsappModal') private whatsappModal: ModalComponent;
     setActiveTab(tab: string): void {
       this.activeTab=tab;
   }
   constructor(
     private service: OrdersServiceService,
     private cdk: ChangeDetectorRef,
     private fb: FormBuilder,
     private route: ActivatedRoute,
     private router: Router
   ) {
     const today = new Date().toISOString().slice(0, 10);
     const currentYear = new Date().getFullYear();
     const currentTime = new Intl.DateTimeFormat('EN-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(new Date());

  
     this.newdata = this.fb.group({
       date: [today],
       orderTime: [currentTime, Validators.required],
       departId:[null, Validators.required],
       employeeId:[null, Validators.required],
       requestType: [null, Validators.required],
       projectid:[null, Validators.required],
       customerId: [null, Validators.required],
       responsiblePerson: [null, Validators.required],
       tenderNo: [null, Validators.required],

     });
 
     //service.baseUrl = `${environment.appUrls.sales}TaxInvoice/InuputProducts`;
 
     this.subscription.add(
      service.getDepartDropdown().subscribe((r) => {
        if (r.success) {
          this.departments = r.data;
          this.cdk.detectChanges();
        }
      })
    );
 
    this.subscription.add(
      service.getEmployeesDropdown().subscribe((r) => {
        if (r.success) {
          this.employees = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getCustDropdown().subscribe((r) => {
        if (r.success) {
          this.customers = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(service.getUsers().subscribe(r => {
      if (r.success) {
        this.users = r.data;
        this.cdk.detectChanges();
      }
    }));

   }
 
 
   ngOnInit() {
     const id = this.route.snapshot.params.id;
     if (id) {
       this.subscriptions.add(
         this.service.details(id).subscribe((r) => {
           if (r.success) {
             this.fillForm(r.data);
             this.roles = r.roles;
             this.cdk.detectChanges();
           }
         })
       );
     }
   }
   onFinaTypeSelect(selectedItem: any) {
     this.FinancialEntities = [];
 
     if (!selectedItem) {
       this.cdk.detectChanges();
       return;
     }
     // this.financial_entity_TypeId= selectedItem.id;
 
     this.subscription.add(
       this.service
         .getFinancialEntities(this.financial_entity_TypeId)
         .subscribe((r) => {
           if (r.success) {
             this.FinancialEntities = r.data;
             this.cdk.detectChanges();
           }
         })
     );
   }
 
   onItemClick(e: any) {}
 
   ngOnDestroy(): void {
     this.subscription.unsubscribe();
   }
 
   onExporting(e: any) {
     console.log(e);
     const workbook = new Workbook();
     const worksheet = workbook.addWorksheet('Employees');
     if (e.format == 'excel') {
       exportDataGrid({
         component: e.component,
         worksheet,
         autoFilterEnabled: true,
       }).then(() => {
         workbook.xlsx.writeBuffer().then((buffer: any) => {
           saveAs(
             new Blob([buffer], { type: 'application/octet-stream' }),
             'taxinvoice.xlsx'
           );
         });
       });
     } else if (e.format == 'pdf') {
       const doc = new jsPDF();
       pdfGrid({
         jsPDFDocument: doc,
         component: e.component,
         indent: 5,
       }).then(() => {
         doc.save('taxinvoice.pdf');
       });
     }
     e.cancel = true;
   }
 
   handleSubmit($event: SubmitEvent) {
     console.log(this.customerForm.value);
     console.log($event);
   }
     save() {
       const id = this.route.snapshot.params.id;
       if (id) {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.service
               .update(id, form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cdk.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                   this.router.navigate(['/inventory/operations/delivery']);
                 }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       } else {
         if (this.newdata.valid) {
           let form = this.newdata.value;
           this.subscriptions.add(
             this.service
               .create(form)
               .pipe(
                 finalize(() => {
                   this.isLoading = false;
                   this.cdk.detectChanges();
                 })
               )
               .subscribe((r) => {
                 if (r.success) {
                   this.router.navigate(['/inventory/operations/delivery']);
                 }
               })
           );
         } else {
           this.newdata.markAllAsTouched();
         }
       }
     }
 
     discard() {
       this.newdata.reset();
     }
   
   onDataUpdate(data: any) {
     this.total = data.reduce((sum: any, item: any) => sum + (item.Subtotal || 0), 0);
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
   
   discountChanged(event: any) {
     const value = +event.target.value;
     if(value) {
       this.discountPerc = value * 100/ this.total;
       this.discountPerc = parseFloat(this.discountPerc.toFixed(2));
     }
     this.net = this.total - (value || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   discountPercChanged(event: any) {
     const value = +event.target.value;
     if(value) {
       this.discount = this.total * value / 100;
       this.discount = parseFloat(this.discount.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onTaxChange(event: any) {
     const value = +event.target.value;
     if(value) {
       this.taxPerc = value * 100 / this.total;
       this.taxPerc = parseFloat(this.taxPerc.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (value || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onVatChange(event: any) {
     const value = +event.target.value;
     this.net = this.total - (this.discount || 0) + (value || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onTaxPercChange(event: any) {
     const value = +event.target.value;
     if(value) {
       this.tax = this.total * value / 100;
       this.tax = parseFloat(this.tax.toFixed(2));
     }
     this.net = this.total - (this.discount || 0) + (+this.vat || 0) - (this.tax || 0)  + (this.shaping || 0);
     this.net = this.net.toFixed(2);
   }
 
   onShapingChange(event: any) {
     const value = +event.target.value;
     this.net = this.total - (this.discount || 0) + (this.vat || 0) - (this.tax || 0)  + (value || 0);
     this.net = this.net.toFixed(2);
   }
   exportToExcel() {
     this.subscription.add(
       this.service.exportExcel().subscribe((e) => {
         if (e) {
           const href = URL.createObjectURL(e);
           const link = document.createElement('a');
           link.setAttribute('download', 'payables.xlsx');
           link.href = href;
           link.click();
           URL.revokeObjectURL(href);
         }
       })
     );
   }
 
   openSmsModal() {
     this.smsModal.open();
   }
   openWhatsappModal() {
     this.whatsappModal.open();
   }
   openEmailModal() {
     this.emailModal.open();
   }
 

}
