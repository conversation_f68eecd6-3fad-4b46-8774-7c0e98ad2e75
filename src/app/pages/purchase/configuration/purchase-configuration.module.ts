import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {PurchasesTermsComponent} from './purchases-terms/purchases-terms.component';
import {SupplierOpeningBalaceComponent} from './supplier-opening-balace/supplier-opening-balace.component';
import {SuppliersComponent} from './suppliers/suppliers.component';
import {SupplierEvaluationComponent} from './supplier-evaluation/supplier-evaluation.component';
import {SuppliersViewComponent} from './suppliers/suppliers-view/suppliers-view.component';
import {SuppliersClassificationComponent} from "./suppliers-classification/suppliers-classification.component";
import {PurchaseTypeComponent} from "./purchases-types/purchase-types.component";
import {PurchaseConfigurationRoutingModule} from "./purchase-configuration-routing.module";
import {SharedModule} from "../../shared/shared.module";
import { Supplier_evaluation_viewComponent } from './supplier-evaluation/supplier_evaluation_view/supplier_evaluation_view.component';
import { Supplier_evaluation_assessment_itemsComponent } from './supplier-evaluation/supplier_evaluation_assessment_items/supplier_evaluation_assessment_items.component';


@NgModule({
    declarations: [
        SuppliersClassificationComponent,
        PurchasesTermsComponent,
        SuppliersComponent,
        SupplierOpeningBalaceComponent,
        SupplierEvaluationComponent,
        SuppliersViewComponent,
        PurchaseTypeComponent,
        Supplier_evaluation_viewComponent,
        Supplier_evaluation_assessment_itemsComponent,

    ],
    imports: [
        PurchaseConfigurationRoutingModule,
        SharedModule
    ],
    exports: [RouterModule],
})
export class PurchaseConfigurationModule {
}
