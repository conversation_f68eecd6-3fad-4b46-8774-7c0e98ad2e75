import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {PurchasesTermsComponent} from './purchases-terms/purchases-terms.component';
import {SupplierOpeningBalaceComponent} from './supplier-opening-balace/supplier-opening-balace.component';
import {SuppliersComponent} from './suppliers/suppliers.component';
import {SupplierEvaluationComponent} from './supplier-evaluation/supplier-evaluation.component';
import {SuppliersViewComponent} from './suppliers/suppliers-view/suppliers-view.component';
import {SuppliersClassificationComponent} from "./suppliers-classification/suppliers-classification.component";
import {PurchaseTypeComponent} from "./purchases-types/purchase-types.component";
import { Supplier_evaluation_viewComponent } from './supplier-evaluation/supplier_evaluation_view/supplier_evaluation_view.component';
import { Supplier_evaluation_assessment_itemsComponent } from './supplier-evaluation/supplier_evaluation_assessment_items/supplier_evaluation_assessment_items.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'suppliers_classification',
        pathMatch: 'full'
    },
    {
        path: 'suppliers_classification',
        component: SuppliersClassificationComponent
    },
    {
        path: 'purchases_types',
        component: PurchaseTypeComponent
    },
    {
        path: 'purchases_terms',
        component: PurchasesTermsComponent
    },

    {
        path: 'supplier_evaluation',
        component: SupplierEvaluationComponent
    },
    {
        path: 'supplier_evaluation_view/:id',
        component: Supplier_evaluation_viewComponent
    },
    {
        path: 'supplier_evaluation_view',
        component: Supplier_evaluation_viewComponent
    },


    {
        path: 'supplier_evaluation_assessment_items/:id',
        component: Supplier_evaluation_assessment_itemsComponent
    },
    {
        path: 'supplier_evaluation_assessment_items',
        component: Supplier_evaluation_assessment_itemsComponent
    },
    
    {
        path: 'suppliers',
        component: SuppliersComponent
    },

    {
        path: 'suppliers_view',
        component: SuppliersViewComponent
    },
    {
            path: 'suppliers_view/:id',
            component: SuppliersViewComponent,
     },
    {
        path: 'supplier_opening_balace',
        component: SupplierOpeningBalaceComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PurchaseConfigurationRoutingModule {
}
