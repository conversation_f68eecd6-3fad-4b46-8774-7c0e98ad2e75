import { ChangeDetectorRef, Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import { SuppliersService } from '../suppliersService';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { ActivatedRoute, Router } from '@angular/router';
import jsPDF from 'jspdf';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import {  lastValueFrom } from 'rxjs';
import DxDataGrid from 'devextreme/ui/data_grid';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { saveAs } from 'file-saver-es';
@Component({
    selector: 'app-suppliers-view',
    templateUrl: './suppliers-view.component.html',
    styleUrls: ['./suppliers-view.component.scss'],
    standalone: false
})
export class SuppliersViewComponent implements OnInit, OnDestroy {
  moduleName = 'Purchases.Suppliers';
  newdata: FormGroup;
  company: any;
  subscription = new Subscription();
  isLoading = false;
  branches: any[];
  countries: any[];
    activeTab: string = 'tab1';
    roles: any[] = [];
    subscriptions = new Subscription();
    data: any[] = [];
    _currentPage = 1;
    itemsCount = 0;
    editMode: boolean = false;
    pagesCount = 0;
    currentFilter: any;
    activeitytypes: any[] = [];
    licenseTypes: any[] = [];
    governorates: any[] = [];
    
    isRtl: boolean = document.documentElement.dir === 'rtl';
    menuOpen = false;
    toggleMenu() {
      this.menuOpen = !this.menuOpen;
    }
    actionsList: string[] = [
      'Export',
      'Send via SMS',
      'Send Via Email',
      'Send Via Whatsapp',
      'print',
    ];
    modalConfig: ModalConfig = {
      modalTitle: 'Send Sms',
      modalSize: 'lg',
      hideCloseButton(): boolean {
        return true;
      },
      dismissButtonLabel: 'Cancel',
    };
  
    smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
    emailModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Email',
    };
    whatsappModalConfig: ModalConfig = {
      ...this.modalConfig,
      modalTitle: 'Send Whatsapp',
    };
    @ViewChild('smsModal') private smsModal: ModalComponent;
    @ViewChild('emailModal') private emailModal: ModalComponent;
    @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

    setActiveTab(tab: string): void {
      this.activeTab = tab;
    }
  constructor(
    private myService: SuppliersService,
    private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private route: ActivatedRoute,
    private router: Router

  ) {
    this.subscription.add(myService.getbranches().subscribe(r => {
      if (r.success) {
        this.branches = r.data;

        this.cdk.detectChanges();

      }
    }));



    this.subscription.add(myService.getcountries().subscribe(r => {
      if (r.success) {
        this.countries = r.data;

        this.cdk.detectChanges();

      }
    }));
    const today = new Date().toISOString().slice(0, 10);
    this.newdata = this.fb.group({
      nameAr: [''],
      address: [''],
      phone: [''],
      fax: [''],
      mobile: [''],
      site: [''],
      email: [''],
      supplierActivity: [''],
      commercialRegisterNumber: [''],
      vATNumber: [''],
      taxCardNumber: [''],
      taxAuthorityOffice: [''],
      taxFileNumber: [''],
      notes: [''],
      user_id: [''],
      action_date: [new Date()],
      flag: [false],
      branchId: [null],
      companyId: [null],
      collectionDuration: [null],
      short_Name_Ar: [''],
      nameEn: [''],
      short_Name_EN: [''],
      supplierGroupID: [null],
      suppliercountryID: [null],
      governorate: [''],
      city: [''],
      buildingNumber: [''],
      postalCode: [''],
      custfloor: [''],
      room: [''],
      landmark: [''],
      additionalInformation: [''],
      activityCode: ['']

    });

    
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    const id = this.route.snapshot.params.id;
    if (id) {
      this.subscriptions.add(
        this.myService.details(id).subscribe((r) => {
          if (r.success) {
            this.fillForm(r.data);
            this.roles = r.roles;
            this.cdk.detectChanges();
          }
        })
      );
    }
  }
  

  fillForm(item: any) {
    Object.keys(item).forEach((key) => {
      if (this.newdata.get(key)) {
        const value =
          item[key] !== undefined && item[key] !== null ? item[key] : '';
        this.newdata.get(key)?.setValue(value);
      }
    });
  }

  save() {
    console.log('تم استدعاء دالة الحفظ');
    
    const id = this.route.snapshot.params.id;
    console.log('معرف المورد:', id);
    
    if (id) {
      console.log('وضع التعديل');
      
      if (this.newdata.valid) {
        console.log('النموذج صحيح');
        let form = this.newdata.value;
        console.log('بيانات النموذج بعد التعديل:', form);
        
        this.isLoading = true; // تعيين حالة التحميل
        
        this.subscription.add(
          this.myService
            .update(id, form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe(
              (r) => {
                console.log('استجابة التحديث:', r);
                if (r.success) {
                  console.log('تم التحديث بنجاح، جاري التوجيه للقائمة الرئيسية');
                  this.router.navigate(['/purchases/configuration/suppliers']);
                } else {
                  console.error('فشل التحديث:', r);
                  // إضافة رسالة خطأ للمستخدم
                  alert('حدث خطأ أثناء حفظ البيانات');
                }
              },
              (error) => {
                console.error('خطأ في طلب التحديث:', error);
                // إضافة رسالة خطأ للمستخدم
                alert('حدث خطأ أثناء الاتصال بالخادم');
                this.isLoading = false;
              }
            )
        );
      } else {
        console.log('النموذج غير صحيح');
        console.log('أخطاء النموذج:', this.getFormValidationErrors());
        this.newdata.markAllAsTouched();
      }
    } else {
      console.log('وضع الإضافة');
      
      if (this.newdata.valid) {
        console.log('النموذج صحيح');
        let form = this.newdata.value;
        console.log('بيانات النموذج بعد التعديل:', form);
        
        this.isLoading = true; // تعيين حالة التحميل
        
        this.subscriptions.add(
          this.myService
            .create(form)
            .pipe(
              finalize(() => {
                this.isLoading = false;
                this.cdk.detectChanges();
              })
            )
            .subscribe(
              (r) => {
                console.log('استجابة الإنشاء:', r);
                if (r.success) {
                  console.log('تم الإنشاء بنجاح، جاري التوجيه للقائمة الرئيسية');
                  this.router.navigate(['/purchases/configuration/suppliers']);
                } else {
                  console.error('فشل الإنشاء:', r);
                  // إضافة رسالة خطأ للمستخدم
                  alert('حدث خطأ أثناء حفظ البيانات');
                }
              },
              (error) => {
                console.error('خطأ في طلب الإنشاء:', error);
                // إضافة رسالة خطأ للمستخدم
                alert('حدث خطأ أثناء الاتصال بالخادم');
                this.isLoading = false;
              }
            )
        );
      } else {
        console.log('النموذج غير صحيح');
        console.log('أخطاء النموذج:', this.getFormValidationErrors());
        this.newdata.markAllAsTouched();
      }
    }
  }

  // دالة مساعدة للحصول على أخطاء التحقق من صحة النموذج
  getFormValidationErrors() {
    const result: any = {};
    Object.keys(this.newdata.controls).forEach(key => {
      const controlErrors = this.newdata.get(key)?.errors;
      if (controlErrors) {
        result[key] = controlErrors;
      }
    });
    return result;
  }

  discard() {
    this.newdata.reset();
  }
  exportToExcel() {
    this.subscriptions.add(
      this.myService.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
  onSaving(e: any) {
    e.cancel = true;
    if (e.changes.length) {
      e.changes.forEach((c: any) => {
        if (c.type == 'update') {
          let selected = this.data.find(d => d.id == c.key);
          if (selected) {
            for (const key in selected) {
              if (!Object.prototype.hasOwnProperty.call(c.data, key)) {
                c.data[key] = selected[key];
              }
            }
          }
        }
      });
      e.promise = this.processBatchRequest(e.changes, e.component);
    }
  }
    async processBatchRequest(changes: Array<{}>, component: DxDataGrid): Promise<any> {
      await lastValueFrom(this.myService.batch(changes));
      component.cancelEditData();
      await component.refresh(true);
      this.cdk.detectChanges();
      this.subscriptions.add(this.myService.list('', this.currentPage).subscribe(r => {
        if (r.success) {
          this.loadData(r);
        }
      }));
    }
    get currentPage() {
      return this._currentPage;
    }
    set currentPage(value: any) {
      this._currentPage = value;
      this.subscriptions.add(this.myService.list('', value).subscribe(r => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          MenuComponent.reinitialization();
        }
      }));
    }
    loadData(r: any) {
      this.data = r.data;
      if (r.itemsCount) {
        this.itemsCount = r.itemsCount;
      }
      if (r.pagesCount) {
        this.pagesCount = r.pagesCount;
      }
      this.cdk.detectChanges();
      MenuComponent.reinitialization();
    }
      onExporting(e: any) {
        console.log(e);
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('warehouses');
        if (e.format == 'excel') {
          exportDataGrid({
            component: e.component,
            worksheet,
            autoFilterEnabled: true,
          }).then(() => {
            workbook.xlsx.writeBuffer().then((buffer: any) => {
              saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
            });
    
          });
        } else if (e.format == 'pdf') {
          const doc = new jsPDF();
          pdfGrid({
            jsPDFDocument: doc,
            component: e.component,
            indent: 5,
          }).then(() => {
            doc.save('warehouses.pdf');
          });
        }
        e.cancel = true;
      }

      onPrint() {
        // استخدام نهج أكثر أماناً لا يتدخل في DOM الأصلي
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          alert('يرجى السماح بالنوافذ المنبثقة لاستخدام وظيفة الطباعة');
          return;
        }
        
        // تحضير البيانات من النموذج
        const supplierData = this.newdata.value;
        
        // إعداد محتوى صفحة الطباعة
        const printContent = `
          <html dir="rtl">
          <head>
            <title>بيانات المورد</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                direction: rtl;
                margin: 0;
                padding: 20px;
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
              }
              h1 {
                color: #333;
                margin: 0;
                font-size: 24px;
              }
              .date {
                text-align: left;
                margin: 5px 0 15px;
                font-size: 12px;
                color: #666;
              }
              .section {
                margin-bottom: 20px;
              }
              .section-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                background-color: #f0f0f0;
                padding: 5px;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
              }
              th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: right;
              }
              th {
                background-color: #f2f2f2;
                font-weight: bold;
              }
              .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #777;
                border-top: 1px solid #ddd;
                padding-top: 10px;
              }
              @media print {
                @page {
                  size: A4;
                  margin: 1cm;
                }
                body {
                  margin: 0;
                  padding: 10px;
                }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>بيانات المورد</h1>
              <p class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            <div class="section">
              <div class="section-title">المعلومات الأساسية</div>
              <table>
                <tr>
                  <th>الاسم بالعربية</th>
                  <td>${supplierData.nameAr || '-'}</td>
                  <th>الاسم بالإنجليزية</th>
                  <td>${supplierData.nameEn || '-'}</td>
                </tr>
                <tr>
                  <th>الاسم المختصر (عربي)</th>
                  <td>${supplierData.short_Name_Ar || '-'}</td>
                  <th>الاسم المختصر (إنجليزي)</th>
                  <td>${supplierData.short_Name_EN || '-'}</td>
                </tr>
                <tr>
                  <th>نشاط المورد</th>
                  <td>${supplierData.supplierActivity || '-'}</td>
                  <th>مدة التحصيل</th>
                  <td>${supplierData.collectionDuration || '-'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">معلومات الاتصال</div>
              <table>
                <tr>
                  <th>العنوان</th>
                  <td>${supplierData.address || '-'}</td>
                  <th>البريد الإلكتروني</th>
                  <td>${supplierData.email || '-'}</td>
                </tr>
                <tr>
                  <th>الهاتف</th>
                  <td>${supplierData.phone || '-'}</td>
                  <th>الجوال</th>
                  <td>${supplierData.mobile || '-'}</td>
                </tr>
                <tr>
                  <th>الفاكس</th>
                  <td>${supplierData.fax || '-'}</td>
                  <th>الموقع الإلكتروني</th>
                  <td>${supplierData.site || '-'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">العنوان التفصيلي</div>
              <table>
                <tr>
                  <th>الدولة</th>
                  <td>${this.getCountryName(supplierData.suppliercountryID) || '-'}</td>
                  <th>المحافظة</th>
                  <td>${supplierData.governorate || '-'}</td>
                </tr>
                <tr>
                  <th>المدينة</th>
                  <td>${supplierData.city || '-'}</td>
                  <th>الرمز البريدي</th>
                  <td>${supplierData.postalCode || '-'}</td>
                </tr>
                <tr>
                  <th>رقم المبنى</th>
                  <td>${supplierData.buildingNumber || '-'}</td>
                  <th>الطابق</th>
                  <td>${supplierData.custfloor || '-'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">المعلومات القانونية</div>
              <table>
                <tr>
                  <th>رقم السجل التجاري</th>
                  <td>${supplierData.commercialRegisterNumber || '-'}</td>
                  <th>رقم ضريبة القيمة المضافة</th>
                  <td>${supplierData.vATNumber || '-'}</td>
                </tr>
                <tr>
                  <th>رقم البطاقة الضريبية</th>
                  <td>${supplierData.taxCardNumber || '-'}</td>
                  <th>مكتب الضرائب</th>
                  <td>${supplierData.taxAuthorityOffice || '-'}</td>
                </tr>
                <tr>
                  <th>رقم الملف الضريبي</th>
                  <td colspan="3">${supplierData.taxFileNumber || '-'}</td>
                </tr>
              </table>
            </div>
            
            <div class="section">
              <div class="section-title">ملاحظات</div>
              <p>${supplierData.notes || 'لا توجد ملاحظات'}</p>
            </div>
            
            <div class="footer">
              ${new Date().getFullYear()} Falcon ERP - جميع الحقوق محفوظة
            </div>
            
            <script>
              // الطباعة تلقائياً عند تحميل الصفحة
              window.onload = function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              };
            </script>
          </body>
          </html>
        `;
        
        // كتابة المحتوى إلى النافذة الجديدة
        printWindow.document.write(printContent);
        printWindow.document.close();
      }
      
      // دالة مساعدة للحصول على اسم الدولة من معرفها
      getCountryName(countryId: number): string {
        if (!countryId || !this.countries) return '';
        const country = this.countries.find((c: any) => c.id === countryId);
        return country ? country.name : '';
      }
}
