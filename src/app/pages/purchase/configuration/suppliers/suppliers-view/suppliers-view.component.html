<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
      <h3 class="fw-bolder m-0">{{ "COMMON.AddSupplier" | translate }}</h3>
      <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            type="submit"
            (click)="discard()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Cancel" | translate }}
            <i class="fa fa-close"></i>
          </button>
          <button
            type="submit"
            (click)="save()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.SaveChanges" | translate }}
            <i class="fa fa-save"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                  (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Purchases.Suppliers'
            }"
          >
            <a
              class="menu-link px-3"
              (click)="onPrint()"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
          class="menu-item px-3"
          *hasPermission="{
            action: 'printAction',
            module: 'HR.Employees'
          }"
        >
          <a
            class="menu-link px-3"
            target="_blank"
            href="/reports/warehouses?withLogo=true"
            data-kt-company-table-filter="delete_row"
            >Print With Logo</a
          >
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <form
      [formGroup]="newdata"
      class="form d-flex flex-wrap align-items-start gap-3"
      action="#"
      id="kt_modal_add_company_form"
      data-kt-redirect="../../demo1/dist/apps/Companies/list.html"
    >
       <div class="d-flex flex-wrap flex-xl-nowrap w-100 gap-4">
        <div class="main-inputs flex-grow-1">
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameAr" class="form-label">
                {{ "COMPANY.NAMEAR" | translate }}
              </label>
              <input
                type="text"
                id="nameAr"
                name="NameAr"
                class="form-control"
                formControlName="nameAr"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="nameEn" class="form-label">
                {{ "COMPANY.NAMEEN" | translate }}
              </label>
              <input
                type="text"
                name="nameEn"
                id="nameEn"
                class="form-control"
                formControlName="nameEn"
              />
            </div>
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="short_Name_Ar" class="form-label">
                {{ "INVENTORY.WAREHOUSES.SHORT" | translate }}
              </label>
              <input
                type="text"
                id="short_Name_Ar"
                name="short_Name_Ar"
                class="form-control"
                formControlName="short_Name_Ar"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="short_Name_EN" class="form-label">
                {{ "COMMON.ShortName2" | translate }}
              </label>
              <input
                type="text"
                id="short_Name_EN"
                name="short_Name_EN"
                class="form-control"
                formControlName="short_Name_EN"
              />
            </div>


          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="branchId" class="form-label">
                {{ "COMMON.Branch" | translate }}
              </label>
              <ng-select
                id="branchId"
                formControlName="branchId"
                bindLabel="name"
                bindValue="id"
                [items]="branches"
              ></ng-select>
            </div>
        
        
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="supplierActivity" class="form-label">
                {{ "CUSTOMER.ACTIVITY" | translate }}
              </label>
              <input
                type="text"
                id="supplierActivity"
                name="supplierActivity"
                class="form-control"
                formControlName="supplierActivity"
              />
            </div>
      
           
           
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="email" class="form-label">
                {{ "COMPANY.MAIL" | translate }}
              </label>
              <input
                type="text"
                id="email"
                name="email"
                class="form-control"
                formControlName="email"
              />
            </div>
            <div class="form-group col-xl-4 col-md-4 col-sm-12">
              <label for="site" class="form-label">
                {{ "CUSTOMER.WEBSITE" | translate }}
              </label>
              <input
                type="text"
                id="site"
                name="site"
                class="form-control"
                formControlName="site"
              />
            </div>
          </div>
        </div>


      </div>

      <div class="row">
        <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
              {{ "COMMON.BasicData" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.TaxInfo" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab3'"
              (click)="setActiveTab('tab3')"
            >
              {{ "COMMON.AddressInfo" | translate }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              [class.active]="activeTab === 'tab4'"
              (click)="setActiveTab('tab4')"
            >
              {{ "COMMON.ResponsiblePersons" | translate }}
            </button>
          </li>

        </ul>

        <div class="tab-content" id="myTabContent">
          <!-- Tab 1 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="telephon" class="form-label">
                {{ "CUSTOMER.TEL" | translate }}
              </label>
              <input
                type="text"
                id="telephon"
                name="telephon"
                class="form-control"
                formControlName="telephon"
              />
            </div>
            
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="fax" class="form-label">
                {{ "CUSTOMER.FAX" | translate }}
              </label>
              <input
                type="text"
                id="fax"
                name="fax"
                class="form-control"
                formControlName="fax"
              />
            </div>

          </div>

          
          <div class="row">
            
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="mobile" class="form-label">
                {{ "CUSTOMER.PHONE" | translate }}
              </label>
              <input
                type="text"
                id="mobile"
                name="mobile"
                class="form-control"
                formControlName="mobile"
              />
            </div>
          
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="notes" class="form-label">
                {{ "COMMON.Notes" | translate }}
              </label>
              <input
                type="text"
                id="notes"
                name="notes"
                class="form-control"
                formControlName="notes"
              />
            </div>

          </div>
          
            <div class="row">
              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="activityCode" class="form-label">
                  {{ "CUSTOMER.ACTIVITY_CODE" | translate }}
                </label>
                <input
                  type="text"
                  id="activityCode"
                  name="activityCode"
                  class="form-control"
                  formControlName="activityCode"
                />
              </div>

              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="typeId" class="form-label">
                  {{ "COMMON.Type" | translate }}
                </label>
                <ng-select
                  id="typeId"
                  formControlName="typeId"
                  bindLabel="name"
                  bindValue="id"
                  [items]="activeitytypes"
                ></ng-select>
              </div>
            </div>
            <div class="row">
              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="licenseTypeId" class="form-label">
                  {{ "COMMON.LicenseType" | translate }}
                </label>
                <ng-select
                  id="licenseTypeId"
                  formControlName="licenseTypeId"
                  bindLabel="name"
                  bindValue="id"
                  [items]="licenseTypes"
                ></ng-select>
              </div>
            
              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="licenseNumber" class="form-label">
                  {{ "COMMON.LicenseNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="licenseNumber"
                  name="licenseNumber"
                  class="form-control"
                  formControlName="licenseNumber"

                />
              </div>
            </div>

          </div>

          <!-- Tab 2 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
            <div class="row">
    
              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="taxAuthorityOffice" class="form-label">
                  {{ "COMMON.TaxOffice" | translate }}
                </label>
                <input
                  type="text"
                  id="taxAuthorityOffice"
                  name="taxAuthorityOffice"
                  class="form-control"
                  formControlName="taxAuthorityOffice"
                />
              </div>
              
              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="taxFileNumber" class="form-label">
                  {{ "COMMON.TaxFileNumber" | translate }}
                </label>
                <input
                  type="text"
                  id="taxFileNumber"
                  name="taxFileNumber"
                  class="form-control"
                  formControlName="taxFileNumber"
                />
              </div>

  
            </div>
            <div class="row">

              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="vATNumber" class="form-label">
                  {{ "COMMON.VatNo" | translate }}
                </label>
                <input
                  type="text"
                  id="vATNumber"
                  name="vATNumber"
                  class="form-control"
                  formControlName="vATNumber"
                />
              </div>

              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="commercialRegisterNumber" class="form-label">
                  {{ "COMMON.CommercialRegistration" | translate }}
                </label>
                <input
                  type="text"
                  id="commercialRegisterNumber"
                  name="commercialRegisterNumber"
                  class="form-control"
                  formControlName="commercialRegisterNumber"
                />
              </div>
              
            </div>
            <div class="row">

              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="taxCardNumber" class="form-label">
                  {{ "COMMON.TaxCard" | translate }}
                </label>
                <input
                  type="text"
                  id="taxCardNumber"
                  name="taxCardNumber"
                  class="form-control"
                  formControlName="taxCardNumber"
                />
              </div>

              <div class="form-group col-xl-6 col-md-6 col-sm-12">
                <label for="collectionDuration" class="form-label">
                  {{ "CUSTOMER.DURATION_OF_COLLECTION" | translate }}
                </label>
                <input
                  type="text"
                  id="collectionDuration"
                  name="collectionDuration"
                  class="form-control"
                  formControlName="collectionDuration"
                />
              </div>
              
            </div>

          </div>
          <!-- Tab 3 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab3'"
            [class.active]="activeTab === 'tab3'"
            *ngIf="activeTab === 'tab3'"
          >

          <div class="row">
    
            <div class="form-group col-xl-12 col-md-12 col-sm-12">
              <label for="address" class="form-label">
                {{ "COMMON.Address" | translate }}
              </label>
              <input
                type="text"
                id="address"
                name="address"
                class="form-control"
                formControlName="address"
              />
            </div>


          </div>
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="country" class="form-label">
                {{ "CUSTOMER.COUNTRY" | translate }}
              </label>
              <ng-select
                id="countryid"
                formControlName="countryid"
                bindLabel="countryName"
                bindValue="id"
                [items]="countries"
              ></ng-select>
            </div>


            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="governorate" class="form-label">
                {{ "COMMON.Governorate" | translate }}
              </label>
              <ng-select
                id="governorate"
                formControlName="governorate"
                bindLabel="name"
                bindValue="id"
                [items]="governorates"
              ></ng-select>
            </div>
   

     
            
          </div>
          <div class="row">

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="city" class="form-label">
                {{ "CUSTOMER.CITY" | translate }}
              </label>
              <input
                type="text"
                id="city"
                name="city"
                class="form-control"
                formControlName="city"
              />
            </div>

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="district" class="form-label">
                {{ "CUSTOMER.DISTRICT" | translate }}
              </label>
              <input
                type="text"
                id="district"
                name="district"
                class="form-control"
                formControlName="district"
              />
            </div>
            
          </div>

          <div class="row">

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="street" class="form-label">
                {{ "CUSTOMER.STREET" | translate }}
              </label>
              <input
                type="text"
                id="street"
                name="street"
                class="form-control"
                formControlName="street"
              />
            </div>

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="landmark" class="form-label">
                {{ "COMMON.DefinedAttribute" | translate }}
              </label>
              <input
                type="text"
                id="landmark"
                name="landmark"
                class="form-control"
                formControlName="landmark"
              />
            </div>
            
          </div>

          <div class="row">

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="additionalInformation" class="form-label">
                {{ "COMMON.AdditionalInformation" | translate }}
              </label>
              <input
                type="text"
                id="additionalInformation"
                name="additionalInformation"
                class="form-control"
                formControlName="additionalInformation"
              />
            </div>

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="postalCode" class="form-label">
                {{ "CUSTOMER.POSTAL_CODE" | translate }}
              </label>
              <input
                type="text"
                id="postalCode"
                name="postalCode"
                class="form-control"
                formControlName="postalCode"
              />
            </div>
            
          </div>

          <div class="row">

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="buildingNumber" class="form-label">
                {{ "CUSTOMER.BUILDING_NUMBER" | translate }}
              </label>
              <input
                type="text"
                id="buildingNumber"
                name="buildingNumber"
                class="form-control"
                formControlName="buildingNumber"
              />
            </div>

            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="custfloor" class="form-label">
                {{ "CUSTOMER.FLOOR_NUMBER" | translate }}
              </label>
              <input
                type="text"
                id="custfloor"
                name="custfloor"
                class="form-control"
                formControlName="custfloor"
              />
            </div>
            
          </div>
          <div class="row">
            <div class="form-group col-xl-6 col-md-6 col-sm-12">
              <label for="officeNo" class="form-label">
                {{ "CUSTOMER.OFFICE_NUMBER" | translate }}
              </label>
              <input
                type="text"
                id="officeNo"
                name="officeNo"
                class="form-control"
                formControlName="officeNo"
              />
            </div>
          </div>
            </div>

          <!-- Tab 4 -->
          <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab4'"
            [class.active]="activeTab === 'tab4'"
            *ngIf="activeTab === 'tab4'"
          >
          <dx-data-grid
          id="gridcontrole"
          [rtlEnabled]="true"
          [dataSource]="data"
          keyExpr="price"
          [showRowLines]="true"
          [showBorders]="true"
          [columnAutoWidth]="true"
          (onExporting)="onExporting($event)"
          [allowColumnResizing]="true"
          [remoteOperations]="true"
          [repaintChangesOnly]="true"
          (onSaving)="onSaving($event)"
        >
          <dxo-editing
            mode="batch"
            [allowAdding]="true"
            [allowDeleting]="true"
            [allowUpdating]="true"
          ></dxo-editing>
        
          <dxo-selection mode="single"></dxo-selection>
        
          <dxo-filter-row
            [visible]="true"
            [applyFilter]="currentFilter"
          ></dxo-filter-row>
        
          <dxo-scrolling rowRenderingMode="virtual"></dxo-scrolling>
        
          <dxo-paging [pageSize]="10"></dxo-paging>
        
          <dxo-pager
            [visible]="true"
            [allowedPageSizes]="[5, 10, 'all']"
            [displayMode]="'compact'"
            [showPageSizeSelector]="true"
            [showInfo]="true"
            [showNavigationButtons]="true"
          ></dxo-pager>
        
          <dxo-header-filter [visible]="true"></dxo-header-filter>
        
          <dxo-search-panel [visible]="true" [highlightCaseSensitive]="true"></dxo-search-panel>
        

          <dxi-column dataField="responsiblePerson" caption="{{ 'COMMON.ResponsiblePerson' | translate }}" ></dxi-column>
          <dxi-column dataField="job" caption="{{ 'COMMON.Job' | translate }}" ></dxi-column>
          <dxi-column dataField="mobile" caption="{{ 'COMPANY.MOBILE' | translate }}" ></dxi-column>
          <dxi-column dataField="mail" caption="{{ 'COMPANY.MAIL' | translate }}" ></dxi-column>
            <dxo-header-filter>
              <dxo-search [enabled]="true"></dxo-search>
            </dxo-header-filter>

        

        
          <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"></dxo-export>

        </dx-data-grid>
          </div>

        </div>
      </div>
    </form>
  </div>
</div>
