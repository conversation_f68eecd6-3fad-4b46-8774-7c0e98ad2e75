<form [formGroup]="viewListForm" class="mb-3">
  <div class="card-header border-0 cursor-pointer w-100">
    <div
      class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
    >
    <h3 class="fw-bolder m-0">{{ "COMMON.PurchaseInvoice" | translate }}</h3>
    <div [style.position]="'relative'">
        <div class="btn-group">
          <button
            (click)="filter()"
            class="btn btn-sm btn-active-light-primary"
          >
            {{ "COMMON.Filter" | translate }}
            <i class="fa fa-filter"></i>
          </button>
          <button
            routerLink="/purchases/vendor_bills/bills_view"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Purchases.Bills',
              action: 'createAction'
            }"
          >
            {{ "COMMON.Create" | translate }}
            <i class="fa fa-plus"></i>
          </button>
          <!-- زر التعديل تم إزالته وتم استبداله برابط في عمود رقم الحركة -->
          <button
            (click)="deleteRecords()"
            [hidden]="!selectedItemKeys.length"
            class="btn btn-sm btn-active-light-primary"
            *hasPermission="{
              module: 'Purchases.Bills',
              action: 'deleteAction'
            }"
          >
            {{ "COMMON.DELETE" | translate }}
            <i class="fa fa-trash"></i>
          </button>
        </div>
        <!-- <button type="button" class="btn btn-sm btn-danger mx-2"
                    (click)="discard()"> {{'COMMON.DISCARD' | translate}}</button> -->
        <button
          class="btn btn-icon btn-active-light-primary mx-2"
          (click)="toggleMenu()"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          data-bs-trigger="hover"
          title="Settings"
        >
          <i class="fa fa-gear"></i>
        </button>
        <div
          class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
          [class.show]="menuOpen"
          [style.position]="'absolute'"
          [style.top]="'100%'"
          [style.zIndex]="'1050'"
          [style.left]="isRtl ? '0' : 'auto'"
          [style.right]="!isRtl ? '0' : 'auto'"
        >
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              (click)="exportToExcel()"
              data-kt-company-table-filter="delete_row"
            >
              {{ "COMMON.ExportToExcel" | translate }}
            </a>
          </div>
          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openSmsModal()"
            >
              {{ "COMMON.SendViaSMS" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openEmailModal()"
            >
              {{ "COMMON.SendViaEmail" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              class="menu-link px-3"
              data-kt-company-table-filter="delete_row"
              (click)="openWhatsappModal()"
            >
              {{ "COMMON.SendViaWhatsapp" | translate }}
            </a>
          </div>

          <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Purchases.Bills'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses"
              data-kt-company-table-filter="delete_row"
              >{{ "COMMON.Print" | translate }}</a
            >
          </div>
          <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Purchases.Bills'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="card-body border-top">
    <div class="main-inputs">
      <div class="row">
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label class="form-label">{{ "COMMON.FromDate" | translate }}</label>
          <input
            id="fromDate"
            type="date"
            formControlName="fromDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
        <div class="form-group col-xl-4 col-md-6 col-sm-12">
          <label class="form-label">{{ "COMMON.ToDate" | translate }}</label>
          <input
            id="toDate"
            type="date"
            formControlName="toDate"
            class="form-control"
            style="background-color: #f5f8fa"
          />
        </div>
      </div>
    </div>
  </div>
</form>

<dx-data-grid
  id="gridcontrole"
  [rtlEnabled]="true"
  [dataSource]="data"
  keyExpr="id"
  [showRowLines]="true"
  [showBorders]="true"
  [columnAutoWidth]="true"
  (onExporting)="onExporting($event)"
  [allowColumnResizing]="true"
  (onSelectionChanged)="selectionChanged($event)"
  [focusedRowEnabled]="false"
>
  <dxo-selection
    mode="multiple"
    [allowSelectAll]="true"
    [selectAllMode]="'page'"
    [showCheckBoxesMode]="'always'"
    [selectByClick]="false"
    [deferred]="false">
  </dxo-selection>

  <dxo-filter-row
    [visible]="true"
    [applyFilter]="currentFilter"
  ></dxo-filter-row>

  <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
  <dxo-paging [pageSize]="10"> </dxo-paging>
  <dxo-pager
    [visible]="true"
    [allowedPageSizes]="[5, 10, 'all']"
    [displayMode]="'compact'"
    [showPageSizeSelector]="true"
    [showInfo]="true"
    [showNavigationButtons]="true"
  >
  </dxo-pager>
  <dxo-header-filter [visible]="true"></dxo-header-filter>

  <dxo-search-panel
    [visible]="true"
    [highlightCaseSensitive]="true"
  ></dxo-search-panel>

  <dxi-column dataField="id" caption="Number" cellTemplate="idCellTemplate"></dxi-column>

  <div *dxTemplate="let data of 'idCellTemplate'">
    <a [routerLink]="['/purchases/vendor_bills/bills_view', data.value]"
       class="text-primary"
       (click)="$event.stopPropagation()"
       (mousedown)="$event.stopPropagation()">
      {{ data.value }}
    </a>
  </div>

  <dxi-column dataField="Warehouse" caption="Warehouse">
    <dxo-header-filter>
      <dxo-search [enabled]="true"></dxo-search>
    </dxo-header-filter>
  </dxi-column>

  <dxi-column dataField="effectiveDate" caption="Effective Date"></dxi-column>
  <dxi-column dataField="year" caption="Year"></dxi-column>
  <dxi-column dataField="notes" caption="Notes"></dxi-column>
  <dxi-column dataField="actionDate" caption="Action Date"></dxi-column>
  <dxi-column dataField="userName" caption="User Name"></dxi-column>
  <dxi-column dataField="branch" caption="Branch"></dxi-column>

  <dxo-export [enabled]="true" [formats]="['pdf', 'excel']"> </dxo-export>

  <dxo-summary>
    <dxi-total-item column="Customer" summaryType="count"> </dxi-total-item>
    <dxi-total-item column="InvoiceDate" summaryType="min">
      [customizeText]="customizeDate"
    </dxi-total-item>

    <dxi-total-item column="Total" summaryType="sum" valueFormat="currency">
    </dxi-total-item>
  </dxo-summary>
</dx-data-grid>
