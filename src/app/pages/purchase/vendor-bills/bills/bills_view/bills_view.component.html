<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.PurchaseInvoice" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.Bills',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <div class="row ">
         
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
            <label class="form-label">{{ "COMMON.EntitlementDate" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>

        </div>


         <div class="row">

            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="supplier" class="form-label" style="color: green;"> {{ "COMMON.Supplier" | translate }}  </label>
              <ng-select
                id="supplier"
                formControlName="supplier"
                bindLabel="nameAr"
                bindValue="id"
                [items]="suppliers"
              ></ng-select>
            </div>
  
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="warehouse" class="form-label" style="color: red;"> 
                {{ "COMMON.Warehouse" | translate }} 
              </label>
                          <ng-select
                id="warehouse"
                formControlName="warehouse"
                bindLabel="name"
                bindValue="id"
                [items]="warehouses"
              ></ng-select>
            </div>
  
         </div>
         <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="purchaseTypeId" class="form-label">
              {{ "COMMON.PurchaseType" | translate }}
            </label>
            <ng-select
              id="purchaseTypeId"
              formControlName="purchaseTypeId"
              bindLabel="name"
              bindValue="id"
              [items]="purchaseTypes"
            ></ng-select>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="costId" class="form-label">
              {{ "COMMON.CostName" | translate }}
            </label>
            <ng-select
              id="costId"
              formControlName="costId"
              bindLabel="nameAr"
              bindValue="code"
              [items]="costCenter"
            ></ng-select>
          </div>
         </div>

        <div class="row">
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="financial_entity_TypeId" class="form-label">
              {{ "COMMON.financial_entity_Type" | translate }}
            </label>
            <ng-select
              id="financial_entity_TypeId"
              formControlName="financial_entity_TypeId"
              bindLabel="name"
              bindValue="id"
              [items]="financial_entity_Types"
              [searchable]="true"
              (change)="onFinaTypeSelect($event)"
              
            >
            </ng-select>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="financial_entity_Id" class="form-label">
              {{ "COMMON.financial_entity" | translate }}
            </label>
            <ng-select
              id="financial_entity_Id"
              formControlName="financial_entity_Id"
              bindLabel="nameAr"
              bindValue="id"
              [items]="FinancialEntities"
            ></ng-select>
          </div>

        </div>


        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.ProductLabel" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.AdditionalInformation" | translate }}
            </button>
              
            </li>
   
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
  
              
                <div class="row">
                  <div class="row">
      
                      <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Total</div>
                          <input type="number" [value]="total" class="form-control" disabled/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount %</div>
                          <input type="number" [(ngModel)]="discountPerc" (change)="discountPercChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Discount</div>
                          <input type="number" [(ngModel)]="discount" (change)="discountChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Vat</div>
                          <input type="number" [(ngModel)]="vat" (change)="onVatChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax %</div>
                          <input type="number" [(ngModel)]="taxPerc" (change)="onTaxPercChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">tax</div>
                          <input type="number" [(ngModel)]="tax" (change)="onTaxChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">shaping</div>
                          <input type="number" [(ngModel)]="shaping" (change)="onShapingChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                      </div>
                  </div>
                      <div class="row">
      
                          <div class="form-group col-xl-3 col-md-4 col-sm-12 mb-3">
                          <div class="form-label col-xl-3 col-md-4 col-sm-12 mb-3">Net</div>
                          <input type="number" [(ngModel)]="net" class="form-control" disabled [ngModelOptions]="{standalone: true}"/>
                      </div>
      
                  </div>
        
        
        
                </div>
  
            </div>
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
    
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="currencyid" class="form-label">
                {{ "COMMON.Currency" | translate }}
              </label>
              <ng-select
                id="currencyid"
                formControlName="currencyid"
                bindLabel="nameAr"
                bindValue="id"
                [items]="currency"
              ></ng-select>
            </div>
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="invcomno" class="form-label">
                {{ "COMMON.Invcomno" | translate }}
              </label>
              <input
                type="text"
                id="invcomno"
                name="invcomno"
                class="form-control"
                formControlName="invcomno"
              />
            </div>

      
          </div>
          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12 mb-3">
              <label class="form-label">{{ "COMMON.Driver" | translate }}</label>
              <ng-select
                      id="driver"
                      formControlName="driver"
                      bindLabel="name"
                      bindValue="id"
                      [items]="drivers"></ng-select>
          </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="InvoiceTypeId" class="form-label">
              {{ "COMMON.InvoiceType" | translate }}
            </label>
            <ng-select
              id="InvoiceTypeId"
              formControlName="InvoiceTypeId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="InvoiceTypes"
            ></ng-select>
          </div>
            
          </div>


    


                    <div class="row">
                      <div class="form-group col-xl-4 col-md-6 col-sm-12 ">
                        <label for="journal" class="form-label">  {{ "COMMON.JournalName" | translate }} </label>
                        <ng-select
                          id="journal"
                          formControlName="journal"
                          bindLabel="name"
                          bindValue="id"
                          [items]="journals"
                        ></ng-select>
                      </div> 
                      <div class="form-group col-xl-4 col-md-12 col-sm-12">
                     <label for="notes" class="form-label">  {{ "COMMON.Notes" | translate }} </label>

                        <textarea
                          id="text"
                          name="notes"
                          class="form-control"
                          formControlName="notes"
                          rows="2" 
                        ></textarea>
                      </div>
                      
                    </div>
  
            </div>
            </div>
       
  

      </div>

    </div>
  </div>
  </div>
 

</form>
