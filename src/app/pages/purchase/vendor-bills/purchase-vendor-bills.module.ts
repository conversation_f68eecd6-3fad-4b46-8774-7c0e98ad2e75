import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {BillsReturnsComponent} from './bills-returns/bills-returns.component';
import {BillsComponent} from './bills/bills.component';
import {ReturnsServiceBillsComponent} from './returns-service-bills/returns-service-bills.component';
import {ServiceBillsComponent} from './service-bills/service-bills.component';
import {PurchaseVendorBillsRoutingModule} from "./purchase-vendor-bills-routing.module";
import {SharedModule} from "../../shared/shared.module";
import { Bills_viewComponent } from './bills/bills_view/bills_view.component';
import { Service_bills_viewComponent } from './service-bills/service_bills_view/service_bills_view.component';
import { Bills_returns_viewComponent } from './bills-returns/bills_returns_view/bills_returns_view.component';
import { Returns_service_bills_viewComponent } from './returns-service-bills/returns_service_bills_view/returns_service_bills_view.component';
import { ProductsGridControleModule } from '../../general/products-grid-controle/ProductsGridControle.module';



@NgModule({
    declarations: [
        BillsComponent,
        ServiceBillsComponent,
        BillsReturnsComponent,
        ReturnsServiceBillsComponent,
        Bills_viewComponent,
        Service_bills_viewComponent,
        Bills_returns_viewComponent,
        Returns_service_bills_viewComponent
        
        
        
    ],
    imports: [
        PurchaseVendorBillsRoutingModule,
        SharedModule,
        ProductsGridControleModule,

    ],
    exports: [RouterModule],
})
export class PurchaseVendorBillsModule {
}
