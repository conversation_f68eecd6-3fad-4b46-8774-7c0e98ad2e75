<form [formGroup]="newdata">
  <div class="card mb-5 mb-xl-10">
    <div class="card-header border-0 cursor-pointer w-100">
      <div
        class="card-title m-0 d-flex justify-content-between w-100 align-items-center"
      >
        <h3 class="fw-bolder m-0">{{ "COMMON.ReturnsServiceBills" | translate }}</h3>
        <div [style.position]="'relative'">
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-sm btn-active-light-primary"
              (click)="discard()"
            >
              {{ "COMMON.Cancel" | translate }}
              <i class="fa fa-times"></i>
            </button>
            <button
              type="submit"
              (click)="save()"
              class="btn btn-sm btn-active-light-primary"
            >
              {{ "COMMON.SaveChanges" | translate }}
              <i class="fa fa-save"></i>
            </button>
          </div>

          <button
            class="btn btn-icon btn-active-light-primary mx-2"
            (click)="toggleMenu()"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            data-bs-trigger="hover"
            title="Settings"
          >
            <i class="fa fa-gear"></i>
          </button>
          <div
            class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px"
            [class.show]="menuOpen"
            [style.position]="'absolute'"
            [style.top]="'100%'"
            [style.zIndex]="'1050'"
            [style.left]="isRtl ? '0' : 'auto'"
            [style.right]="!isRtl ? '0' : 'auto'"
          >
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                (click)="exportToExcel()"
                data-kt-company-table-filter="delete_row"
              >
                {{ "COMMON.ExportToExcel" | translate }}
              </a>
            </div>
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openSmsModal()"
              >
                {{ "COMMON.SendViaSMS" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openEmailModal()"
              >
                {{ "COMMON.SendViaEmail" | translate }}
              </a>
            </div>
  
            <div class="menu-item px-3">
              <a
                class="menu-link px-3"
                data-kt-company-table-filter="delete_row"
                (click)="openWhatsappModal()"
              >
                {{ "COMMON.SendViaWhatsapp" | translate }}
              </a>
            </div>
  
            <div
              class="menu-item px-3"
              *hasPermission="{
                action: 'printAction',
                module: 'Purchases.ReturnsServiceBills',

              }"
            >
              <a
                class="menu-link px-3"
                target="_blank"
                href="/reports/warehouses"
                data-kt-company-table-filter="delete_row"
                >{{ "COMMON.Print" | translate }}</a
              >
            </div>


            <!-- <div
            class="menu-item px-3"
            *hasPermission="{
              action: 'printAction',
              module: 'Inventory.Receipts'
            }"
          >
            <a
              class="menu-link px-3"
              target="_blank"
              href="/reports/warehouses?withLogo=true"
              data-kt-company-table-filter="delete_row"
              >Print With Logo</a
            >
          </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body border-top p-9">
      <div class="main-inputs ">
        <!-- سطر التاريخ والمورد -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label class="form-label">{{ "COMMON.Date" | translate }}</label>
            <input
              id="date"
              type="date"
              formControlName="date"
              class="form-control"
            />
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="supplier" class="form-label">{{ "COMMON.Supplier" | translate }}</label>
            <ng-select
              id="supplier"
              formControlName="supplier"
              bindLabel="nameAr"
              bindValue="id"
              [items]="suppliers"
            ></ng-select>
          </div>
        </div>

        <!-- سطر نوع الشراء والصندوق النقدي -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="purchaseTypeId" class="form-label">
              {{ "COMMON.PurchaseType" | translate }}
            </label>
            <ng-select
              id="purchaseTypeId"
              formControlName="purchaseTypeId"
              bindLabel="name"
              bindValue="id"
              [items]="purchaseTypes"
            ></ng-select>
          </div>
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="CashBoxId" class="form-label">
              {{ "COMMON.CashBox" | translate }}
            </label>
            <ng-select
              id="CashBoxId"
              [(ngModel)]="CashBoxId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="Cashs"
            ></ng-select>
          </div>
        </div>

        <!-- سطر رقم الفاتورة -->
        <div class="row">
          <div class="form-group col-xl-6 col-md-6 col-sm-12">
            <label for="invcomno" class="form-label">
              {{ "COMMON.Invcomno" | translate }}
            </label>
            <input
              type="text"
              id="invcomno"
              name="invcomno"
              class="form-control"
              formControlName="invcomno"
            />
          </div>
        </div>

  


        <div class="row">
          <ul class="nav nav-tabs mx-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab1'"
              (click)="setActiveTab('tab1')"
            >
            {{ "COMMON.ProductLabel" | translate }}
            </button>
            </li>
            <li class="nav-item" role="presentation"> 
              <button
              class="nav-link"
              [class.active]="activeTab === 'tab2'"
              (click)="setActiveTab('tab2')"
            >
              {{ "COMMON.AdditionalInformation" | translate }}
            </button>
              
            </li>

   
          </ul>
  
          <div class="tab-content" id="myTabContent">
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab1'"
            [class.active]="activeTab === 'tab1'"
            *ngIf="activeTab === 'tab1'"
          >
  
                <!--grid controle-->
                <app-products-grid-controle (onDataUpdate)="onDataUpdate($event)"></app-products-grid-controle>
  
  
              
                <!-- الحقول المالية -->
                <div class="row">
                  <!-- سطر الإجمالي ونسبة الخصم -->
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Total</label>
                    <input type="number" [value]="total" class="form-control" disabled/>
                  </div>
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Discount %</label>
                    <input type="number" [(ngModel)]="discountPerc" (change)="discountPercChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                </div>

                <div class="row">
                  <!-- سطر الخصم وضريبة القيمة المضافة -->
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Discount</label>
                    <input type="number" [(ngModel)]="discount" (change)="discountChanged($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">VAT</label>
                    <input type="number" [(ngModel)]="vat" (change)="onVatChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                </div>

                <div class="row">
                  <!-- سطر نسبة الضريبة والضريبة -->
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Tax %</label>
                    <input type="number" [(ngModel)]="taxPerc" (change)="onTaxPercChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Tax</label>
                    <input type="number" [(ngModel)]="tax" (change)="onTaxChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                </div>

                <div class="row">
                  <!-- سطر التشكيل والصافي -->
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Shaping</label>
                    <input type="number" [(ngModel)]="shaping" (change)="onShapingChange($event)" class="form-control" [ngModelOptions]="{standalone: true}"/>
                  </div>
                  <div class="form-group col-xl-6 col-md-6 col-sm-12">
                    <label class="form-label">Net</label>
                    <input type="number" [(ngModel)]="net" class="form-control" disabled [ngModelOptions]="{standalone: true}"/>
                  </div>
                </div>
        
        
        
                </div>
  
            </div>
            <div
            class="tab-pane fade"
            [class.show]="activeTab === 'tab2'"
            [class.active]="activeTab === 'tab2'"
            *ngIf="activeTab === 'tab2'"
          >
    

          <div class="row">
            <div class="form-group col-xl-4 col-md-6 col-sm-12">
              <label for="currencyid" class="form-label">
                {{ "COMMON.Currency" | translate }}
              </label>
              <ng-select
                id="currencyid"
                formControlName="currencyid"
                bindLabel="nameAr"
                bindValue="id"
                [items]="currency"
              ></ng-select>
            </div>
          <div class="form-group col-xl-4 col-md-6 col-sm-12">
            <label for="InvoiceTypeId" class="form-label">
              {{ "COMMON.InvoiceType" | translate }}
            </label>
            <ng-select
              id="InvoiceTypeId"
              formControlName="InvoiceTypeId"
              bindLabel="nameAr"
              bindValue="id"
              [items]="InvoiceTypes"
            ></ng-select>
          </div>
            
          </div>


    


                    <div class="row">

                      <div class="form-group col-xl-8 col-md-12 col-sm-12">
                     <label for="notes" class="form-label">  {{ "COMMON.Notes" | translate }} </label>

                        <textarea
                          id="text"
                          name="notes"
                          class="form-control"
                          formControlName="notes"
                          rows="2" 
                        ></textarea>
                      </div>
                      
                    </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
