import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BillsReturnsComponent} from './bills-returns/bills-returns.component';
import {BillsComponent} from './bills/bills.component';
import {ReturnsServiceBillsComponent} from './returns-service-bills/returns-service-bills.component';
import {ServiceBillsComponent} from './service-bills/service-bills.component';
import { Bills_viewComponent } from './bills/bills_view/bills_view.component';
import { Service_bills_viewComponent } from './service-bills/service_bills_view/service_bills_view.component';
import { Bills_returns_viewComponent } from './bills-returns/bills_returns_view/bills_returns_view.component';
import { Returns_service_bills_viewComponent } from './returns-service-bills/returns_service_bills_view/returns_service_bills_view.component';



const routes: Routes = [

    {
        path: '',
        redirectTo: 'bills',
        pathMatch: 'full'
    },
    {
        path: 'bills',
        component: BillsComponent
    },
    {
        path: 'service_bills',
        component: ServiceBillsComponent
    },
    {
        path: 'bills_returns',
        component: BillsReturnsComponent
    },
    {
        path: 'returns_service_bills',
        component: ReturnsServiceBillsComponent
    },
    {
        path: 'bills_view/:id',
        component: Bills_viewComponent

    },
    {
        path: 'bills_view',
        component: Bills_viewComponent

    },
    {
        path: 'service_bills_view/:id',
        component: Service_bills_viewComponent

    },
    {
        path: 'service_bills_view',
        component: Service_bills_viewComponent

    },

    {
        path: 'bills_returns_view/:id',
        component: Bills_returns_viewComponent

    },
    {
        path: 'bills_returns_view',
        component: Bills_returns_viewComponent

    },

    {
        path: 'returns_service_bills_view/:id',
        component: Returns_service_bills_viewComponent

    },
    {
        path: 'returns_service_bills_view',
        component: Returns_service_bills_viewComponent

    },

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PurchaseVendorBillsRoutingModule {
}
