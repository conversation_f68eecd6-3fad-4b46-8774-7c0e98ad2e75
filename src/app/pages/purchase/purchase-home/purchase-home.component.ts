import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { PurchaseService } from '../purchase.service';
import { getCSSVariableValue } from '../../../_metronic/kt/_utils';

@Component({
    selector: 'app-purchase-home',
    templateUrl: './purchase-home.component.html',
    styleUrls: ['./purchase-home.component.scss'],
    standalone: false
})
export class purchaseHomeComponent implements OnInit, OnDestroy {
  // Estadísticas del dashboard
  totalPurchases: number = 0;
  pendingOrders: number = 0;
  totalSuppliers: number = 0;
  purchaseValue: number = 0;

  // Datos para gráficos
  purchasesTrends: any[] = [];
  topSuppliers: any[] = [];
  purchasesByCategory: any[] = [];
  recentPurchases: any[] = [];

  // Colores para gráficos
  chartColors = {
    primary: getCSSVariableValue('--kt-primary'),
    success: getCSSVariableValue('--kt-success'),
    info: getCSSVariableValue('--kt-info'),
    warning: getCSSVariableValue('--kt-warning'),
    danger: getCSSVariableValue('--kt-danger')
  };

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private purchaseService: PurchaseService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // Cargar datos del dashboard
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones para evitar memory leaks
    this.subscriptions.forEach(sb => sb.unsubscribe());
  }

  loadDashboardData(): void {
    // Simular datos para el dashboard
    // En un entorno real, estos datos vendrían de la API

    // Estadísticas
    this.totalPurchases = 1248;
    this.pendingOrders = 42;
    this.totalSuppliers = 156;
    this.purchaseValue = 284750;

    // Datos para gráficos
    this.purchasesTrends = [
      { month: 'Jan', value: 65000 },
      { month: 'Feb', value: 78000 },
      { month: 'Mar', value: 72000 },
      { month: 'Apr', value: 84000 },
      { month: 'May', value: 93000 },
      { month: 'Jun', value: 86000 }
    ];

    this.topSuppliers = [
      { name: 'Tech Solutions Inc.', value: 78500, percentage: 27.6 },
      { name: 'Global Supplies Co.', value: 52300, percentage: 18.4 },
      { name: 'Office Essentials', value: 45200, percentage: 15.9 },
      { name: 'Industrial Parts Ltd.', value: 38700, percentage: 13.6 },
      { name: 'Quality Materials', value: 32100, percentage: 11.3 }
    ];

    this.purchasesByCategory = [
      { category: 'Raw Materials', value: 125000, percentage: 43.9 },
      { category: 'Office Supplies', value: 68000, percentage: 23.9 },
      { category: 'Equipment', value: 52000, percentage: 18.3 },
      { category: 'Services', value: 39750, percentage: 13.9 }
    ];

    this.recentPurchases = [
      { id: 'PO-2023-0587', supplier: 'Tech Solutions Inc.', date: '2023-06-15', amount: 12450, status: 'Completed' },
      { id: 'PO-2023-0586', supplier: 'Global Supplies Co.', date: '2023-06-14', amount: 8750, status: 'Completed' },
      { id: 'PO-2023-0585', supplier: 'Industrial Parts Ltd.', date: '2023-06-12', amount: 15200, status: 'Pending' },
      { id: 'PO-2023-0584', supplier: 'Office Essentials', date: '2023-06-10', amount: 5680, status: 'Completed' },
      { id: 'PO-2023-0583', supplier: 'Quality Materials', date: '2023-06-08', amount: 9340, status: 'Completed' }
    ];

    // Actualizar la vista
    this.cdr.detectChanges();
  }
}
