import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {FollowUpBillsReportComponent} from './follow-up-bills-report/follow-up-bills-report.component';
import {LandedCostReportComponent} from './landed-cost-report/landed-cost-report.component';
import {PurchasOrderReportComponent} from './purchas-order-report/purchas-order-report.component';
import {
    PurchaseRequisitionsReportComponent
} from './purchase-requisitions-report/purchase-requisitions-report.component';
import {PurchasedProductsReportComponent} from './purchased-products-report/purchased-products-report.component';
import {
    PurchasesPeriodicallyReportComponent
} from './purchases-periodically-report/purchases-periodically-report.component';
import {PurchasesReportComponent} from './purchases-report/purchases-report.component';
import {PurchasesReturnsReportComponent} from './purchases-returns-report/purchases-returns-report.component';
import {PurchasesTaxReportComponent} from './purchases-tax-report/purchases-tax-report.component';
import {
    SuppliersAccountInMothesReportComponent
} from './suppliers-account-in-mothes-report/suppliers-account-in-mothes-report.component';
import {SuppliersProductsReportComponent} from './suppliers-products-report/suppliers-products-report.component';
import {
    PurchaseOrdersPaymentsReportComponent
} from './purchase-orders-payments-report/purchase-orders-payments-report.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'purchase_orders_payments_report',
        pathMatch: 'full'
    },
    {
        path: 'purchase_orders_payments_report',
        component: PurchaseOrdersPaymentsReportComponent
    },
    {
        path: 'purchases_report',
        component: PurchasesReportComponent
    },
    {
        path: 'purchases_returns_report',
        component: PurchasesReturnsReportComponent
    },
    {
        path: 'purchases_tax_report',
        component: PurchasesTaxReportComponent
    },
    {
        path: 'suppliers_account_in_mothes_report',
        component: SuppliersAccountInMothesReportComponent
    },
    {
        path: 'purchases_periodically_report',
        component: PurchasesPeriodicallyReportComponent
    },
    {
        path: 'purchased_products_report',
        component: PurchasedProductsReportComponent
    },
    {
        path: 'suppliers_products_report',
        component: SuppliersProductsReportComponent
    },
    {
        path: 'purchase_requisitions_report',
        component: PurchaseRequisitionsReportComponent
    },
    {
        path: 'purchas_order_report',
        component: PurchasOrderReportComponent
    },
    {
        path: 'landed_cost_report',
        component: LandedCostReportComponent
    },
    {
        path: 'follow_up_bills_report',
        component: FollowUpBillsReportComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PurchaseReportsRoutingModule {
}
