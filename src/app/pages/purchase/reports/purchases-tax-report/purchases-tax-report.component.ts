import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import { exportDataGrid as pdfGrid } from 'devextreme/pdf_exporter';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { jsPDF } from 'jspdf';
import { Subscription } from 'rxjs';
import { PurchaseService } from '../../purchase.service';
import { ModalComponent, ModalConfig } from 'src/app/_metronic/partials';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-purchases-tax-report',
  templateUrl: './purchases-tax-report.component.html',
  styleUrls: ['./purchases-tax-report.component.scss'],
  standalone: false,
})
export class PurchasesTaxReportComponent implements OnDestroy {
  moduleName = 'Purchases.PurchasesTaxReport';
  data: any[];
  gridDataSource: any[];
  toDateValue: any;
  fromDateValue: any;
  editorOptions: any;
  gridBoxValue: number[] = [];
  searchExpr: any;
  subscription = new Subscription();
  PrintList: any;
  currentFilter: any;
  isDetails: boolean = true;
  showPreviousBalance: boolean = true;
  branches: [] = [];
  branchid: any = 1;
  warehouses: any[];
  purchaseTypes: any[];
  warehouse: number = 0;
  purchaseTypeId: number = 0;
  year: any;
  isProduct: boolean = false;
  isRtl: boolean = document.documentElement.dir === 'rtl';
  printList: string[] = ['print', 'Print Custome'];
  menuOpen = false;
  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }
  actionsList: string[] = [
    'Export',
    'Send via SMS',
    'Send Via Email',
    'Send Via Whatsapp',
  ];
  modalConfig: ModalConfig = {
    modalTitle: 'Send Sms',
    modalSize: 'lg',
    hideCloseButton(): boolean {
      return true;
    },
    dismissButtonLabel: 'Cancel',
  };
  smsModalConfig: ModalConfig = { ...this.modalConfig, modalTitle: 'Send Sms' };
  emailModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Email',
  };
  whatsappModalConfig: ModalConfig = {
    ...this.modalConfig,
    modalTitle: 'Send Whatsapp',
  };
  @ViewChild('smsModal') private smsModal: ModalComponent;
  @ViewChild('emailModal') private emailModal: ModalComponent;
  @ViewChild('whatsappModal') private whatsappModal: ModalComponent;

  constructor(
    private service: PurchaseService,
    private cdk: ChangeDetectorRef
  ) {
    service.baseUrl = `${environment.appUrls.PurchaseReports}PurchasesTaxReport`;

    // this.subscription.add(service.getEmployeesDropdown().subscribe(r => {
    //   if (r.success) {
    //     this.gridDataSource = r.data;
    //     this.cdk.detectChanges();
    //   }
    // }));
    this.subscription.add(
      service.getWarehouses().subscribe((r) => {
        if (r.success) {
          this.warehouses = r.data;
          this.cdk.detectChanges();
        }
      })
    );
    this.subscription.add(
      service.getpurchaseTypes().subscribe((r) => {
        if (r.success) {
          this.purchaseTypes = r.data;
          this.cdk.detectChanges();
        }
      })
    );

    this.subscription.add(
      service.getbranches().subscribe((r) => {
        if (r.success) {
          this.branches = r.data;

          this.cdk.detectChanges();
        }
      })
    );

    this.editorOptions = { placeholder: 'Search name or code' };
    this.searchExpr = ['name', 'code'];

    this.toDateValue = new Date().getTime();
    this.fromDateValue = new Date().getTime();
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.fromDateValue = formatDate(
      new Date(currentYear, 0, 1),
      'yyyy-MM-dd',
      'en'
    );
    this.toDateValue = formatDate(
      new Date(currentYear, 11, 31),
      'yyyy-MM-dd',
      'en'
    );
  }

  onItemClick(e: any) {}

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onExporting(e: any) {
    console.log(e);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');
    if (e.format == 'excel') {
      exportDataGrid({
        component: e.component,
        worksheet,
        autoFilterEnabled: true,
      }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer: any) => {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'DataGrid.xlsx'
          );
        });
      });
    } else if (e.format == 'pdf') {
      const doc = new jsPDF();
      pdfGrid({
        jsPDFDocument: doc,
        component: e.component,
        indent: 5,
      }).then(() => {
        doc.save('Employees.pdf');
      });
    }
    e.cancel = true;
  }
  exportToExcel() {
    this.subscription.add(
      this.service.exportExcel().subscribe((e) => {
        if (e) {
          const href = URL.createObjectURL(e);
          const link = document.createElement('a');
          link.setAttribute('download', 'payables.xlsx');
          link.href = href;
          link.click();
          URL.revokeObjectURL(href);
        }
      })
    );
  }

  filter() {
    const FromDate = formatDate(this.fromDateValue, 'yyyy-MM-dd', 'en');
    const ToDate = formatDate(this.toDateValue, 'yyyy-MM-dd', 'en');
    const WarehousesId = this.warehouse ?? 0;
    const ShowGoodInvoices = this.isProduct;
    const BerchisingTypeId = this.purchaseTypeId ?? 0;
    const BranchId = this.branchid ?? 0;
    this.subscription.add(
      this.service
        .list({
          FromDate,
          ToDate,
          WarehousesId,
          ShowGoodInvoices,
          BranchId,
          BerchisingTypeId,
        })
        .subscribe((r) => {
          if (r.success) {
            this.data = r.data;
            this.cdk.detectChanges();
          }
        })
    );
  }
  openSmsModal() {
    this.smsModal.open();
  }
  openWhatsappModal() {
    this.whatsappModal.open();
  }
  openEmailModal() {
    this.emailModal.open();
  }
}
