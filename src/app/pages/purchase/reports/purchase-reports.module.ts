import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import {FollowUpBillsReportComponent} from './follow-up-bills-report/follow-up-bills-report.component';
import {LandedCostReportComponent} from './landed-cost-report/landed-cost-report.component';
import {PurchasOrderReportComponent} from './purchas-order-report/purchas-order-report.component';
import {
  PurchaseRequisitionsReportComponent
} from './purchase-requisitions-report/purchase-requisitions-report.component';
import {PurchasedProductsReportComponent} from './purchased-products-report/purchased-products-report.component';
import {
  PurchasesPeriodicallyReportComponent
} from './purchases-periodically-report/purchases-periodically-report.component';
import {PurchasesReportComponent} from './purchases-report/purchases-report.component';
import {PurchasesReturnsReportComponent} from './purchases-returns-report/purchases-returns-report.component';
import {PurchasesTaxReportComponent} from './purchases-tax-report/purchases-tax-report.component';
import {
  SuppliersAccountInMothesReportComponent
} from './suppliers-account-in-mothes-report/suppliers-account-in-mothes-report.component';
import {SuppliersProductsReportComponent} from './suppliers-products-report/suppliers-products-report.component';
import {
  PurchaseOrdersPaymentsReportComponent
} from './purchase-orders-payments-report/purchase-orders-payments-report.component';
import {SharedModule} from "../../shared/shared.module";
import {PurchaseReportsRoutingModule} from "./purchase-reports-routing.module";


@NgModule({
  declarations: [
    PurchasesReportComponent,
    PurchasesReturnsReportComponent,
    PurchasesTaxReportComponent,
    SuppliersAccountInMothesReportComponent,
    PurchasesPeriodicallyReportComponent,
    PurchasedProductsReportComponent,
    SuppliersProductsReportComponent,
    PurchaseRequisitionsReportComponent,
    PurchasOrderReportComponent,
    LandedCostReportComponent,
    FollowUpBillsReportComponent,
    PurchaseOrdersPaymentsReportComponent,
  ],
  imports: [
    PurchaseReportsRoutingModule,
    SharedModule
  ],
  exports: [RouterModule],
})
export class PurchaseReportsModule { }
