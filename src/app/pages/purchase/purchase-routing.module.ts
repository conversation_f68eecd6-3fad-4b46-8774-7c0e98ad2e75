import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {purchaseHomeComponent} from './purchase-home/purchase-home.component';


const routes: Routes = [

    {
        path: '',
        component: purchaseHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () => import('./configuration/purchase-configuration.module').then(m => m.PurchaseConfigurationModule)
    },
    {
        path: 'orders',
      loadChildren: () => import('./orders/purchase-orders.module').then(m => m.PurchaseOrdersModule)
    },
    {
        path: 'vendor_bills',
        loadChildren: () => import('./vendor-bills/purchase-vendor-bills.module').then(m => m.PurchaseVendorBillsModule)
    },
    {
        path: 'reports',
        loadChildren: () => import('./reports/purchase-reports.module').then(m => m.PurchaseReportsModule)
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class purchaseRoutingModule {
}
