import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TemplatesService {
  constructor(private http: HttpClient) {
  }

  initSend(): Observable<any> {
    return this.http.get<any>('/api/wbtemplatemessages');
  }

  findTemplate(id: any): Observable<any> {
    return this.http.get<any>("/api/wbtemplates/" + id);
  }

  upload(file: any, phoneNumberId: any): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>("/api/wbtemplatemessages/upload?phoneNumberId=" + phoneNumberId, fd);
  }
  send(value: any) {
    return this.http.post<any>("/api/wbtemplatemessages", value);
  }

  initCreat(): Observable<any> {
    return this.http.get<any>('/api/wbtemplates');
  }

  create(value: any) {
    return this.http.post<any>("/api/wbtemplates", value);
  }

  getMediaHandle(file: any): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>("/api/wbtemplates/mediaHandle", fd);
  }

  getSallaCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbsalla/customers');
  }
  getZidCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbzid/customers');
  }
  getCustomers(): Observable<any> {
    return this.http.get<any>('/api/customer');
  }
  getSuppliers(): Observable<any> {
    return this.http.get<any>('/api/Suppliers');
  }
  getLeads(): Observable<any> {
    return this.http.get<any>('/api/Leads');
  }
  getStaff(): Observable<any> {
    return this.http.get<any>('/api/Employees');
  }

}
