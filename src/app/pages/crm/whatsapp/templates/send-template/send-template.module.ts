import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SendTemplateRoutingModule } from './send-template-routing.module';
import { SendTemplateComponent } from './send-template.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { DxDataGridModule } from 'devextreme-angular';

@NgModule({
  declarations: [
    SendTemplateComponent
  ],
  imports: [
    CommonModule,
    SendTemplateRoutingModule,
    NgSelectModule,
    DxDataGridModule,
    ReactiveFormsModule
  ]
})
export class SendTemplateModule { }
