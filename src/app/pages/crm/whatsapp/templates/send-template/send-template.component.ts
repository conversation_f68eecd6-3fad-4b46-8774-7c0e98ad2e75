import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { TemplatesService } from '../templates.service';
import { finalize, Subscription } from 'rxjs';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import * as XLSX from 'xlsx';


@Component({
    selector: 'app-send-template',
    templateUrl: './send-template.component.html',
    styleUrls: ['./send-template.component.css'],
    standalone: false
})
export class SendTemplateComponent implements OnInit, OnDestroy {
  templates: any[] = [];
  subscriptions = new Subscription();
  phoneNumbers: any[] = [];
  item: any = null;
  sendForm: FormGroup;
  isLoading = false;
  groups: any[] = [];
/*  columns: any[] = [];*/
  showExcelPreview = false;
/*  excelData: any[] = [];*/
  currentExcelPage: any = 0;
  pageSize: any = 5;
  isPreviewing: boolean = false;
  headerCol = false;
  bodyFromExcel = false;
  isUploading: boolean = false;
  recipientTypes: any[] = [];
  showSallaPreview = false;
  //sallaCustomers: any[] = [];
  //sallaItemsCount: any;
  //sallaPagesCount: any;
  headerSallaCol = false;
  headerZidCol = false;
  headerCustomerCol = false;
  //sallaColumns: any[] = [];
  //zidCustomers: any[] = [];
  //zidColumns: any[] = [];
  //zidPagesCount: any;
  //zidItemsCount: any;
  showZidPreview: boolean = false;
  griddata: any[] = [];
  showCustomersPreview: boolean;
    griddataPagesCount: any;
    griddataItemsCount: any;
    griddataColumns: any [] = [];

  //Suppliers: any = [];
  showSuppliersPreview: boolean;
  //suppliersPagesCount: any;
  //supplierItemsCount: any;
  //suppliersColumns: any = [];

  //Leads: any = [];
  showleadsPreview: boolean;
  //leadsPagesCount: any;
  //leadsItemsCount: any;
  //leadsColumns: any = [];


  //Staff: any = [];
  showstaffPreview: boolean;
  //staffPagesCount: any;
  //staffItemsCount: any;
  //staffColumns: any = [];


  selectedItemKeys: any;
  currentFilter: any;
  constructor(private templateService: TemplatesService,
    private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private toastr: ToastrService) {
    this.sendForm = this.fb.group({
      templateName: [null, Validators.required],
      recipientNumber: null,
      groupId: null,
      contacts: [],
      recipientType: ['individual', Validators.required],
      headerVariableValue: null,
      uploadedMediaId: null,
      bodyVariables: this.fb.array([]),
      senderNumber: [null, Validators.required],
      mobileColumn: null,
      contentType: null,
      fileName: null,
      campaignTitle: null
    });

    this.subscriptions.add(this.sendForm.get('templateName')?.valueChanges.subscribe(v => {
      if (v) {
        this.selectTemplate(v);
      }
    }));

    this.recipientTypes = [
      { text: 'Individual', value: 'individual' },
      { text: 'Group', value: 'group' },
      { text: 'Excel', value: 'excel' },
      { text: 'Customers', value: 'Customers' },
      { text: 'Suppliers', value: 'Suppliers' },
      { text: 'Leads', value: 'Leads' },
      { text: 'Staff', value: 'Staff' },
      { text: 'Salla Customers', value: 'salla' },
      { text: 'Zid Customers', value: 'zid' },


      ];
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }


  selectionChanged(data: any) {
    this.selectedItemKeys = data.selectedRowKeys;
    this.cdk.detectChanges();
  }


  ngOnInit(): void {
    this.subscriptions.add(this.templateService.initSend().subscribe(r => {
      if (r.success) {
        this.templates = r.data.templates;
        this.phoneNumbers = r.data.phoneNumbers;
        this.groups = r.data.groups;
      }
    }));
    this.subscriptions.add(this.sendForm.get('recipientType')?.valueChanges.subscribe(v => {
      if (v != 'excel') {
        this.griddata = [];
        this.bodyFromExcel = false;
        this.headerCol = false;
        this.sendForm.get('contacts')?.setValue([]);
        this.griddataColumns = [];
        this.showExcelPreview = false;
        this.currentExcelPage = 0;
        this.isPreviewing = false;
        this.showExcelPreview = false;
      }
      if (v == 'salla') {
        this.loadSallaCustomers();
      } else {
        this.headerSallaCol = false;
        this.griddataColumns = [];
        this.griddata = [];
        this.griddataItemsCount = 0;
        this.griddataPagesCount = 0;
        this.showSallaPreview = false;
      }
      if (v == 'zid') {
        this.loadZidCustomers();
      } else {
        this.headerZidCol = false;
        this.griddataColumns = [];
        this.griddata = [];
        this.griddataItemsCount = 0;
        this.griddataPagesCount = 0;
        this.showZidPreview = false;
      }
      this.bodyVars.reset();
    }));
  }

  selectTemplate(value: any) {
    this.subscriptions.add(this.templateService.findTemplate(value).subscribe(r => {
      if (r.success) {
        this.bodyVars.clear();
        this.item = r.data;
        this.item.bodyVariables.forEach((b: any, i: any) => {
          const control = this.fb.group({
            index: i,
            value: [null, [Validators.required]],
            excel: false,
            salla: false,
            zid: false,
            customers: false,
            suppliers: false,
            leads: false,
            staff: false
          });
          this.subscriptions.add(control.get('excel')?.valueChanges.subscribe(v => {
            if (v) {
              control.get('value')?.setValue(this.griddataColumns[0]);
            } else {
              control.get('value')?.setValue(null);
            }
          }));
          this.subscriptions.add(control.get('salla')?.valueChanges.subscribe(v => {
            if (v) {
              control.get('value')?.setValue(this.griddataColumns[0]);
            } else {
              control.get('value')?.setValue(null);
            }
          }));
          this.subscriptions.add(control.get('zid')?.valueChanges.subscribe(v => {
            if (v) {
              control.get('value')?.setValue(this.griddataColumns[0]);
            } else {
              control.get('value')?.setValue(null);
            }
          }));
          this.bodyVars.push(control);
        });
        if (this.item.headerHasVariables) {
          this.sendForm.get('headerVariableValue')?.setValidators([Validators.required]);
        } else {
          this.sendForm.get('headerVariableValue')?.clearValidators();
          this.sendForm.get('headerVariableValue')?.updateValueAndValidity();
        }
        this.cdk.detectChanges();
      }
    }));
  }
  get bodyVars() {
    return <FormArray>this.sendForm.get('bodyVariables');
  }

  upload(event: any) {
    if (event.target.files.length) {
      var file = event.target.files[0];
      const phoneNumberId = this.sendForm.get('senderNumber')?.value;
      if (!phoneNumberId) {
        this.toastr.error("Sender is required");
        event.target.files[0].value = '';
        return false;
      }
      this.isUploading = true;
      this.subscriptions.add(this.templateService.upload(file, phoneNumberId)
        .subscribe(v => {
          if (v.success) {
            this.sendForm.get('uploadedMediaId')?.setValue(v.data.uploadedMediaId);
            this.sendForm.get('contentType')?.setValue(file.type);
            this.sendForm.get('file')?.setValue(file.name);
            this.isUploading = false;
          }
        }));
    }
    return true;
  }
  send(value: any) {
    if (this.sendForm.valid) {
      if (!this.isUploading) {
        if (this.item.headerHasMedia && !value.uploadedMediaId) {
          this.toastr.error(`You Should select a header ${this.item.headerType} first!.`);
          return;
        }
        this.isLoading = true;
        const recipientType = value.recipientType;
        if (recipientType == 'excel') {
          const mobileColIndex = this.griddataColumns.findIndex(c => c == value.mobileColumn);
          const indexes = [{ col: 'Mobile', index: mobileColIndex }];
          value.bodyVariables.filter((b: any) => b.excel).map((b: any) => b.value)
            .forEach((b: any) => {
              const i = this.griddataColumns.findIndex(c => c == b);
              if (indexes.findIndex(c => c.col == b) == -1) {
                indexes.push({ col: b, index: i });
              }
            });
          if (this.headerCol) {
            const headerColIndex = this.griddataColumns.findIndex(c => c == value.headerVariableValue);
            if (headerColIndex >= 0) {
              value.headerFromExcel = true;
              if (indexes.findIndex(c => c.col == value.headerVariableValue) == -1) {
                indexes.push({ col: value.headerVariableValue, index: headerColIndex });
              }
            }
          }
          const data = this.griddata.slice(1).map(e => {
            let obj: any = {};
            indexes.forEach(c => {
              obj[c.col] = e[c.index].toString();
            });
            return obj;
          });
          value.contacts = data;
        }

        if (recipientType == 'salla') {
          if (this.headerSallaCol) {
            value.headerFromSalla = true;
          }
        }
        if (recipientType == 'zid') {
          if (this.headerZidCol) {
            value.headerFromZid = true;
          }
        }
        if (recipientType == 'customers') {
          if (this.headerCustomerCol) {
            value.headerFromCustomers = true;
          }
        }
        this.subscriptions.add(this.templateService.send(value)
          .pipe(finalize(() => this.isLoading = false))
          .subscribe(v => {
            this.isLoading = false;
            if (v.success) {
              this.toastr.success(v.message);
            } else {
              this.toastr.error(v.message);
            }
          }));
      }
    } else {
      this.sendForm.markAllAsTouched();
      this.isLoading = false;
      console.log(this.sendForm);
    }
  }

  onFileChange(event: any) {
    const file = event.target.files[0];
    const fileReader = new FileReader();
    fileReader.onload = (e: any) => {
      const arrayBuffer = e.target.result;
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.griddataColumns = XLSX.utils.sheet_to_json<any>(worksheet, { header: 1 })[0];
      const mobileColumn = this.griddataColumns.find(col => col.toLowerCase().indexOf('mobile') >= 0) || this.griddataColumns.find(col => col.toLowerCase().indexOf('phone') >= 0) || this.griddataColumns.find(col => col.toLowerCase().indexOf('whatsapp') >= 0);
      this.sendForm.get('mobileColumn')?.setValue(mobileColumn);
      this.griddata = XLSX.utils.sheet_to_json(worksheet, { header: 0 });
     
      this.cdk.detectChanges();
    };
    fileReader.readAsArrayBuffer(file);
  }

  toggleExcelPreview() {
    this.isPreviewing = !this.isPreviewing;
    this.cdk.detectChanges();
    setTimeout(() => {
      this.showExcelPreview = !this.showExcelPreview;
    }, 100);
  }
  get pagedExcelData(): any[] {
    return this.griddata.slice(1).filter((d, index) => index < (this.currentExcelPage + this.pageSize) && index >= this.currentExcelPage);
  }
  stopPreviewLoading(last: any) {
    if (last) {
      if (this.isPreviewing) {
        this.isPreviewing = false;
        this.currentExcelPage = 0;
        this.cdk.detectChanges();
      }
    }
  }
  nextPreview() {
    if (this.currentExcelPage >= this.griddata.length) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage + this.pageSize;
  }
  prevPreview() {
    if (this.currentExcelPage <= 1) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage - this.pageSize;
  }

  loadSallaCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showSallaPreview = preview;
      this.subscriptions.add(this.templateService.getSallaCustomers()
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            this.griddataColumns.push(...[{ text: 'First Name', value: 'first_name' }, { text: 'Last Name', value: 'last_name' }, { text: 'Full Name', value: 'first_name & last_name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'country' }, { text: 'City', value: 'city' }]);
          }
        }));
    } else {
      this.showSallaPreview = !this.showSallaPreview;
    }
  }

  loadZidCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showZidPreview = preview;
      this.subscriptions.add(this.templateService.getZidCustomers()
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            this.griddataColumns.push(...[{ text: 'Name', value: 'name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'city.country_name' }, { text: 'City', value: 'city.name' }]);
          }
        }));
    } else {
      this.showZidPreview = !this.showZidPreview;
    }
  }

  loadStaff(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showstaffPreview = preview;
      this.subscriptions.add(this.templateService.getStaff()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            if (r.data) {
              this.griddataColumns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();

          }
        }));
    } else {
      this.showstaffPreview = !this.showstaffPreview;
    }
  }

  loadLeads(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showleadsPreview = preview;
      this.subscriptions.add(this.templateService.getLeads()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            if (r.data) {
              this.griddataColumns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();

          }
        }));
    } else {
      this.showleadsPreview = !this.showleadsPreview;
    }
  }

  loadSuppliers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showSuppliersPreview = preview;
      this.subscriptions.add(this.templateService.getSuppliers()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            if (r.data) {
              this.griddataColumns = Object.keys(r.data[0]).map(d => {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();
          
          }
        }));
    } else {
      this.showSuppliersPreview = !this.showSuppliersPreview;
    }
  }

  loadCustomers(preview = false) {
    if (this.griddata.length == 0) {
      this.isLoading = true;
      this.showCustomersPreview = preview;
      this.subscriptions.add(this.templateService.getCustomers()
        .pipe(finalize(() => {
          this.isLoading = false;
          this.cdk.detectChanges();
        }))
        .subscribe(r => {
          if (r.success) {
            this.griddata = r.data;
            this.griddataPagesCount = r.pagesCount;
            this.griddataItemsCount = r.itemsCount;
            if(r.data){
              this.griddataColumns = Object.keys(r.data[0]).map(d =>
              {
                return { text: d.toString(), value: d };
              });
            }
            this.cdk.detectChanges();
            //this.customersColumns.push(...[{ text: 'Name', value: 'name' }, { text: 'Email', value: 'email' }, { text: 'Mobile', value: 'mobile' }, { text: 'Country', value: 'city.country_name' }, { text: 'City', value: 'city.name' }]);
          }
        }));
    } else {
      this.showCustomersPreview = !this.showCustomersPreview;
    }
  }
}
