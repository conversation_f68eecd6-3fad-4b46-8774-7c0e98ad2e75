<div class="col-12">

  <div class="card">
    <div class="card-body">
      <div class="card-title">
        <strong>Send Message</strong>
      </div>
      <div class="row">
        <form [formGroup]="sendForm">
          <div class="row">
            <div class="col-xl-6 col-md-6 col-sm-12">
              <div class="mb-2 col-12">
                <label for="templateName" class="form-label">
                  Campaign Title ( Optional)
                </label>
                <input formControlName="campaignTitle" class="form-control h-50" />
              </div>
              <div class="col-12">
                <label for="templateName" class="form-label">
                  Template
                </label>
                <ng-select class="pb-3" formControlName="templateName" [items]="templates" bindLabel="name"
                           bindValue="id"></ng-select>
              </div>
              <div class="col-12 alert alert-dark mb-2" role="alert" *ngIf="item">
                <strong for="templateHeader" class="form-label ">
                  Header <span *ngIf="item.headerHasMedia">(Type : {{ item?.headerType?.toLowerCase()}} ) </span>
                </strong>
                <p *ngIf="!item.header && !item.headerHasMedia"> NONE </p>
                <p *ngIf="!item.headerHasMedia">{{item.header}}</p>
                <input (change)="upload($event)" *ngIf="item?.headerHasMedia && sendForm.get('senderNumber')?.value"
                       type="file" class="form-control" />
                <p *ngIf="item?.headerHasMedia && !sendForm.get('senderNumber')?.value">
                  Select Sender Number to Upload
                  {{item?.headerType?.toLowerCase()}}
                </p>
              </div>
              <div class="col-12 alert alert-dark mb-2" role="alert" *ngIf="item">
                <strong for="templateBody" class="form-label">
                  Body
                </strong>
                <p *ngIf="item">{{item.body}}</p>
              </div>
              <div class="col-12 alert alert-dark mb-2" role="alert" *ngIf="item">
                <strong for="templateFooter" class="form-label ">
                  Footer
                </strong>
                <p *ngIf="item">{{item.footer}}</p>
              </div>
            </div>
            <div class="col-xl-6 col-md-6 col-sm-12" [dir]="item?.isRTL ? 'rtl' : 'ltr'" *ngIf="item">
              <div class="mb-2 col-12">
                <label for="templateName" class="form-label mb-0">
                  Sender
                </label>
                <ng-select formControlName="senderNumber" [items]="phoneNumbers" bindLabel="verified_name"
                           bindValue="id" class="pb-3"></ng-select>
              </div>
              <div class="col-xl-12 col-sm-12  my-2">
                <label for="templateName" class="form-label mb-0">
                  Recipient Type
                </label>
                <ng-select formControlName="recipientType" [items]="recipientTypes" bindLabel="text" bindValue="value"
                           class="pb-3">

                </ng-select>

                <!-- salla -->

                <p *ngIf="sendForm.get('recipientType')?.value == 'salla'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadSallaCustomers(!showSallaPreview)">
                    {{ showSallaPreview ? 'Hide ':'Preview ' }} Salla Customers
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>

                <!-- Zid -->

                <p *ngIf="sendForm.get('recipientType')?.value == 'zid'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadZidCustomers(!showZidPreview)">
                    {{ showZidPreview ? 'Hide ':'Preview ' }} Zid Customers
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>

                <!-- Customer -->

                <p *ngIf="sendForm.get('recipientType')?.value == 'Customers'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadCustomers(!showCustomersPreview)">
                    {{ showCustomersPreview ? 'Hide ':'Preview ' }} Customers
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>

                <!-- Suppliers -->
                <p *ngIf="sendForm.get('recipientType')?.value == 'Suppliers'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadSuppliers(!showCustomersPreview)">
                    {{ showSuppliersPreview ? 'Hide ':'Preview ' }}  Suppliers
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>
                <!-- Leads -->
                <p *ngIf="sendForm.get('recipientType')?.value == 'Leads'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadLeads(!showleadsPreview)">
                    {{ showleadsPreview ? 'Hide ':'Preview ' }}  Leads
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>
                <!-- Staff -->
                <p *ngIf="sendForm.get('recipientType')?.value == 'Staff'" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="loadStaff(!showstaffPreview)">
                    {{ showstaffPreview ? 'Hide ':'Preview ' }}  Staff
                    <span *ngIf="griddataItemsCount">({{griddataItemsCount}} Rows)</span>
                  </a>
                </p>

              </div>

              <!-- individual -->
              <div class="mb-2 col-12" *ngIf="sendForm.get('recipientType')?.value == 'individual' ">
                <label for="recipientNumber" class="form-label">
                  Recipient Number
                </label>
                <input class="form-control" placeholder="Recipient Number" formControlName="recipientNumber">
              </div>

              <!-- group -->

              <div class="col-xl-12 col-sm-12" *ngIf="sendForm.get('recipientType')?.value == 'group'">
                <label class="form-label mb-0">
                  Group Name
                </label>
                <ng-select class="pb-3" formControlName="groupId" [items]="groups" bindLabel="name"
                           bindValue="id"></ng-select>
              </div>

              <!-- excel -->

              <div class="mb-2 col-12" *ngIf="sendForm.get('recipientType')?.value == 'excel'">
                <input type="file" class="form-control" (change)="onFileChange($event)">
                <p *ngIf="griddata.length > 1" class="form-label">
                  <a [routerLink]="[]" class="pointer float-end" (click)="toggleExcelPreview()">
                    {{ showExcelPreview ? 'Hide ':'Preview ' }} Excel Data ({{ (griddata.length - 1 )+ ' Rows'}})
                  </a>
                </p>
                <label class="form-label my-2 mb-0">
                  Recipient Number
                </label>
                <ng-select class="pb-3" formControlName="mobileColumn" [items]="griddataColumns"></ng-select>
              </div>

              <!-- header Variables -->

              <div class="mb-2 col-12" *ngIf="item?.headerHasVariables">
                <label for="recipientNumber" class="form-label w-100">
                  Header Variable
                  <label class="float-end" style="font-weight:normal" *ngIf="griddataColumns.length > 0">
                    <input type="checkbox" class="form-check-input"
                           (change)="headerCol= !headerCol;sendForm.get('headerVariableValue')?.setValue(null);" />
                    Excel
                  </label>
                  <label class="float-end" style="font-weight:normal" *ngIf="griddata.length > 0">
                    <input type="checkbox" class="form-check-input"
                           (change)="headerSallaCol= !headerSallaCol;sendForm.get('headerVariableValue')?.setValue(null);" />

                  </label>
                </label>
                <input *ngIf="!headerCol && !headerSallaCol && !headerZidCol  && !headerCustomerCol" class="form-control"
                       placeholder="Header Variable Value" formControlName="headerVariableValue">
                <select formControlName="headerVariableValue" *ngIf="headerCol" class="form-select">
                  <option [value]="col" *ngFor="let col of griddataColumns">{{col}}</option>
                </select>
                <select formControlName="headerVariableValue" *ngIf="headerSallaCol" class="form-select">
                  <option [value]="col.value" *ngFor="let col of griddataColumns">{{col.text}}</option>
                </select>

              </div>

              <!-- body Variables -->

              <div class="mb-2 col-12" *ngIf="item?.bodyHasVariables">
                <label for="recipientNumber" class="form-label w-100">
                  Body Variables
                </label>
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <td>Variable</td>
                      <td>
                        Value
                      </td>

                      <td *ngIf="griddataColumns.length > 0">Select</td>
                    </tr>
                  </thead>
                  <tbody formArrayName="bodyVariables">
                    <tr *ngFor="let v of bodyVars.controls;let i = index" [formGroupName]="i">

                      <td width="10%">{{item.bodyVariables[i]}}</td>

                      <td width="80%" *ngIf="!v.get('excel')?.value && !v.get('salla')?.value && !v.get('zid')?.value
                      && !v.get('customers')?.value">
                        <input id="{{i}}" class="form-control" placeholder="Variable Value" formControlName="value">
                      </td>

                      <td width="80%" *ngIf="v.get('excel')?.value">
                        <select formControlName="value" id="{{i}}" class="form-select">
                          <option *ngFor="let col of griddataColumns">{{col}}</option>
                        </select>
                      </td>

                      <td width="80%" *ngIf="v.get('customers')?.value">
                        <select formControlName="value" id="{{i}}" class="form-select">
                          <option [value]="col.value" *ngFor="let col of griddataColumns">{{col.text}}</option>
                        </select>
                      </td>
                      <!--<td width="10%" *ngIf="griddataColumns.length > 0">
    <label>
      <input type="checkbox" class="form-check-input" formControlName="excel" />
    </label>
  </td>-->

                      <td width="10%" *ngIf="griddataColumns.length > 0">
                        <label>
                          <input type="checkbox" class="form-check-input" formControlName="customers" />
                        </label>
                      </td>

                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="mb-2 col-12">
                <button [disabled]="isLoading" class="btn btn-light" type="submit" (click)="send(sendForm.value)">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!--Data Preview-->
  <div class="col-xl-12 col-sm-12 mb-2">
    <div class="card">
      <div class="card-body">
        <div class="card-title">
          <strong>Data Preview</strong>
          <a class="float-end pointer mx-2" style="text-decoration:none" (click)="showCustomersPreview = false">Close</a>
          <span class="float-end" *ngIf="griddataItemsCount">Total : {{griddataItemsCount}} Rows</span>
        </div>
        <div class="table-responsive imported-preview">

          <!--grid data-->

          <dx-data-grid id="gridcontrole"
          [rtlEnabled]="true"
                        [dataSource]="griddata"
                        keyExpr="id"
                        [showRowLines]="true"
                        [showBorders]="true"
                        [columnAutoWidth]="true"
                        (onSelectionChanged)="selectionChanged($event)"
                        [allowColumnResizing]="true">
            <dxo-selection mode="multiple"></dxo-selection>
            <dxo-filter-row [visible]="true"
                            [applyFilter]="currentFilter"></dxo-filter-row>

            <dxo-scrolling rowRenderingMode="virtual"> </dxo-scrolling>
            <dxo-paging [pageSize]="10"> </dxo-paging>
            <dxo-pager [visible]="true"
                       [allowedPageSizes]="[5, 10, 'all']"
                       [displayMode]="'compact'"
                       [showPageSizeSelector]="true"
                       [showInfo]="true"
                       [showNavigationButtons]="true">
            </dxo-pager>
            <dxo-header-filter [visible]="true"></dxo-header-filter>

            <dxo-search-panel [visible]="true"
                              [highlightCaseSensitive]="true"></dxo-search-panel>

            <dxi-column dataField="id" caption="id"></dxi-column>

            <dxi-column dataField="nameAr" caption="Name Ar"></dxi-column>
            <dxi-column dataField="nameEn" caption="Name En"></dxi-column>
            <dxi-column dataField="mobile" caption="Mobile"></dxi-column>


            <dxo-export [enabled]="true"
                        [formats]="['pdf','excel']">
            </dxo-export>

            <dxo-summary>
              <dxi-total-item column="id" summaryType="count"> </dxi-total-item>
            </dxo-summary>




          </dx-data-grid>


          <div class="col-12 text-center">
            <div class="spinner-border text-success" role="status" *ngIf="isLoading">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
