import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {SendTemplateComponent} from "../../../shared/send-whatsapp/send-template/send-template.component";

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'send',
        component: SendTemplateComponent
      },
      {
        path: 'create',
        loadChildren: () => import('./create-template/create-template.module').then(t => t.CreateTemplateModule)
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TemplatesRoutingModule { }
