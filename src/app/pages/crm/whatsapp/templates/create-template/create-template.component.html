<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Create Template</strong>
    </div>
    <div class="row">
      <form [formGroup]="createForm">
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-xl-4 col-sm-12">
                <label for="templateName" class="form-label">
                  Template Name
                </label>
                <input class="form-control h-50" placeholder="Template Name" formControlName="templateName"
                  (keypress)="templateNameKeyPress($event)" (input)="templateNameChanged($event.target)" maxlength="60">
              </div>
              <div class="col-xl-4 col-sm-12">
                <label for="language" class="form-label">
                  Category
                </label>
                <ng-select formControlName="category" [items]="categories"></ng-select>
              </div>
              <div class="col-xl-4 col-sm-12">
                <label for="language" class="form-label">
                  Language
                </label>
                <ng-select formControlName="language" [items]="languages" bindLabel="name" bindValue="code"></ng-select>
              </div>
            </div>
            <div class="row">
              <p *ngIf="createForm.get('category')?.value  == 'UTILITY'">Create template to send messages about a
                specific transaction, account, order or customer request.</p>
              <p *ngIf="createForm.get('category')?.value  == 'AUTHENTICATION'">Create template to send One-time
                passwords your customers use to authenticate a transaction or login.</p>
              <p *ngIf="createForm.get('category')?.value  == 'MARKETING'">Create template to send Promotions or
                information about your business, products or services. Or any message that isn’t utility or
                authentication.</p>
            </div>
            <div class="row" *ngIf="createForm.get('category')?.value != 'AUTHENTICATION'">
              <p><strong>Components</strong></p>
              <hr />
              <div class="col-12 row">
                <div class="col-xl-4 col-sm-12">
                  <label for="language" class="form-label">
                    Header Type
                  </label>
                  <ng-select formControlName="headerType" [items]="headerTypes"></ng-select>
                </div>
                <div class="col-xl-8 col-sm-12" *ngIf="createForm.get('headerType')?.value == 'TEXT' ">
                  <label for="language" class="form-label">
                    Header Text
                  </label>
                  <input class="form-control h-50" placeholder="Header Text" formControlName="headerText"
                    (input)="removeExtraVariables($event.target,true)">
                  <span>{{ createForm.get('headerText')?.value?.length ?? 0 }} /60</span>
                  <a (click)="openHeaderModal()" class="float-end" [routerLink]="[]" *ngIf="showHeaderExample"
                    data-bs-toggle="modal" data-bs-target="#headerModal">Header Variable Example </a>
                </div>
                <div class="col-xl-8 col-sm-12"
                  *ngIf="createForm.get('headerType')?.value == 'IMAGE' || createForm.get('headerType')?.value == 'VIDEO' || createForm.get('headerType')?.value == 'DOCUMENT' ">
                  <label for="file" class="form-label">
                    {{ createForm.get('headerType')?.value == 'IMAGE' ? 'Choose JPG or PNG File' : (
                    createForm.get('headerType')?.value == 'VIDEO' ? 'Choose MP4 File' : 'Choose PDF File' )}}
                  </label>
                  <input class="form-control form-control-lg" placeholder="Header Text" type="file"
                    (change)="upload($event)">
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-12">
                  <label for="body" class="form-label">
                    Body
                  </label>
                  <textarea class="form-control" rows="4" placeholder="Body Text" formControlName="bodyText"
                    maxlength="1040"></textarea>
                  <span>{{ createForm.get('bodyText')?.value?.length ?? 0 }} /1040</span>
                  <a (click)="openBodyModal()" class="float-end" [routerLink]="[]" *ngIf="showBodyExamples"
                    data-bs-toggle="modal" data-bs-target="#bodyModal">Body Variable Examples </a>
                </div>
              </div>
              <div class="row">
                <div class="col-12 mb-3">
                  <label for="footer" class="form-label">
                    Footer Text (Optional)
                  </label>
                  <input id="footer" class="form-control h-80" placeholder="Footer Text" formControlName="footerText"
                    maxlength="60" (input)="removeExtraVariables($event.target,false)">
                  <span>{{ createForm.get('footerText')?.value?.length ?? 0 }} /60</span>
                </div>
              </div>
            </div>
            <div class="row mb-3" *ngIf="createForm.get('category')?.value == 'AUTHENTICATION'">
              <div class="col-md-6">
                <!--<h6>
              Message Content (Optional)
            </h6>-->
                <!--<div class="form-group mb-3">
              <label class="form-label" for="flexCheckChecked">
                Button Text
              </label>
              <input class="form-control" formControlName="buttonText" />
            </div>-->
                <div class="form-check  mb-3">
                  <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault"
                    formControlName="addExpirationTime">
                  <label class="form-check-label" for="flexCheckDefault">
                    Add expiration time for the code (Optional)
                  </label>
                </div>
                <div class="alert alert-light" *ngIf="createForm.get('addExpirationTime')?.value">
                  <label class="pb-1">Expires in (Minutes) </label>
                  <input type="number" class="form-control" style="width:200px" min="1" formControlName="expiresIn"
                    step="1" onkeypress="return event.charCode >= 48 && event.charCode <= 57" />
                </div>

              </div>
            </div>
            <div class="row">
              <div class="mb-3 col-12">
                <button [disabled]="isLoading" class="btn btn-light" type="submit"
                  (click)="save(createForm.value)">Create Template</button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal" id="bodyModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <b class="modal-title">Body Variable Exmaples</b>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <td>Variable</td>
                      <td>Value</td>
                    </tr>
                  </thead>
                  <tbody formArrayName="bodyVariablesExamples">
                    <tr *ngFor="let v of bodyVars.controls;let i = index">
                      <td>{{bodyVariables[i]}}</td>
                      <td>
                        <input class="form-control" placeholder="Variable Value" [formControlName]="i">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal" id="headerModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <b class="modal-title">Header Variable Exmaple</b>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <td>Variable</td>
                      <td>Value</td>
                    </tr>
                  </thead>
                  <tbody formArrayName="headerVariablesExamples">
                    <tr *ngFor="let v of headerVars.controls;let i = index">
                      <td>{{headerVariables[i]}}</td>
                      <td>
                        <input id="{{i}}" class="form-control" placeholder="Variable Value" [formControlName]="i">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
