import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { TemplatesService } from '../templates.service';

@Component({
    selector: 'app-create-template',
    templateUrl: './create-template.component.html',
    styleUrls: ['./create-template.component.css'],
    standalone: false
})
export class CreateTemplateComponent implements OnInit, OnDestroy {
  subscriptions = new Subscription();
  createForm: FormGroup;
  languages: any[] = [];
  categories: any[] = [];
  headerTypes: any[] = [];
  showHeaderExample: boolean = false;
  showBodyExamples: boolean = false;
  examples: any[] = [];
  isLoading = false;
  constructor(private templateService: TemplatesService, private fb: FormBuilder, private toastr: ToastrService) {
    this.createForm = this.fb.group({
      templateName: [null, Validators.required],
      headerText: null,
      headerVariablesExamples: this.fb.array([]),
      bodyText: [null, Validators.required],
      bodyVariablesExamples: this.fb.array([]),
      language: [null, Validators.required],
      category: [null, Validators.required],
      headerType: [null, Validators.required],
      latitude: null,
      longitude: null,
      imageVariablesExamples: [],
      footerText: null,
      addExpirationTime: false,
      /*buttonText: null,*/
      expiresIn: null
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.subscriptions.add(this.templateService.initCreat().subscribe(r => {
      if (r.success) {
        this.categories = r.data.categories;
        this.languages = r.data.languages;
        this.headerTypes = r.data.headerTypes;
      }
    }));

    this.createForm.get('headerText')?.valueChanges.subscribe(v => {
      if (v) {
        const regExp = new RegExp(/{{\d}}/g);
        const matchesCount = v.match(regExp)?.length;
        if (matchesCount && matchesCount > 1) {
          this.toastr.error('Header can conatins ONLY one variable!.');
        }
        if (matchesCount == 1) {
          this.showHeaderExample = true;
          this.createForm.get('headerVariablesExamples')?.addValidators([Validators.required]);
        } else {
          this.showHeaderExample = false;
          (<FormArray>this.createForm.get('headerVariablesExamples')).clear();
          this.createForm.get('headerVariablesExamples')?.clearValidators();
          this.createForm.get('headerVariablesExamples')?.updateValueAndValidity();
        }
      }
    });
    this.createForm.get('bodyText')?.valueChanges.subscribe(v => {
      if (v) {
        const regExp = new RegExp(/{{\d}}/g);
        const matchesCount = v.match(regExp)?.length;
        if (matchesCount) {
          this.showBodyExamples = true;
        } else {
          this.showBodyExamples = false;
          (<FormArray>this.createForm.get('bodyVariablesExamples')).clear();
          this.createForm.get('bodyVariablesExamples')?.clearValidators();
          this.createForm.get('bodyVariablesExamples')?.updateValueAndValidity();
        }
      }
    });
    this.createForm.get('headerType')?.valueChanges.subscribe(v => {
      if (v) {
        const matchesCount = v == 'VIDEO' || v == 'IMAGE' || v == 'DOCUMENT';
        if (matchesCount) {
          this.createForm.get('imageVariablesExamples')?.addValidators([Validators.required]);
        } else {
          this.createForm.get('imageVariablesExamples')?.clearValidators();
          this.createForm.get('imageVariablesExamples')?.updateValueAndValidity();
        }
        if (v == 'TEXT') {
          this.createForm.get('headerText')?.addValidators([Validators.required]);
        }
        if (v == 'NONE') {
          this.createForm.get('headerText')?.reset();
          this.createForm.get('headerText')?.clearValidators();
          this.createForm.get('headerText')?.updateValueAndValidity();
          this.headerVars.clear();
          this.createForm.get('headerVariablesExamples')?.clearValidators();
          this.createForm.get('headerVariablesExamples')?.updateValueAndValidity();
        }
      }
    });
    this.createForm.get('footerText')?.valueChanges.subscribe(v => {
      this.validateFooter(v);
    });

    this.createForm.get('category')?.valueChanges.subscribe(v => {
      if (v == "AUTHENTICATION") {
        this.createForm.get('headerType')?.setValue('NONE');
        this.createForm.get('bodyText')?.setValue('NONE');
        this.createForm.get('headerType')?.updateValueAndValidity();
        this.createForm.get('bodyText')?.updateValueAndValidity();
      } else {

        //this.createForm.get('headerType')?.reset();
        //this.createForm.get('bodyText')?.reset();
        //this.createForm.get('headerType')?.updateValueAndValidity();
        //this.createForm.get('bodyText')?.updateValueAndValidity();
      }
    });
  }
  private validateFooter(v: any) {
    if (v) {
      const regExp = new RegExp(/{{\d}}/g);
      const matchesCount = v.match(regExp)?.length;
      if (matchesCount) {
        this.toastr.error('Footer can not conatins a variable!.');
        return;
      }
    }
  }

  upload(event: any) {
    if (event.target.files.length) {
      var file = event.target.files[0];
      this.subscriptions.add(this.templateService.getMediaHandle(file)
        .subscribe(v => {
          if (v.success) {
            this.createForm.get('imageVariablesExamples')?.setValue([v.data.url]);
            this.createForm.get('imageVariablesExamples')?.updateValueAndValidity();
          }
        }));
    }
  }

  save(value: any) {
    const regExp = new RegExp(/{{\d}}/g);
    const matchesCount = this.createForm.get('headerText')?.value?.match(regExp)?.length;
    if (matchesCount && matchesCount > 1) {
      this.toastr.error('Header can conatins Only one variable!.');
      return;
    }

    this.validateFooter(this.createForm.get('footerText')?.value);

    if (this.createForm.valid) {
      this.isLoading = true;
      this.subscriptions.add(this.templateService.create(value).subscribe(v => {
        this.isLoading = false;
        if (v.success) {
          this.toastr.success(v.message);
        } else {
          this.toastr.error(v.message);
        }
      }));
    } else {
      console.log(this.createForm)
      this.createForm.markAllAsTouched();
      this.toastr.error('Please input valid data, and submit again');
      this.isLoading = false;
    }
  }
  templateNameChanged(target: any) {
    if (target?.value) {
      this.createForm.get('templateName')?.patchValue(target.value.replace(' ', '_').toLowerCase());
      this.createForm.get('templateName')?.updateValueAndValidity();
    }
  }
  //template Name restrictions
  templateNameKeyPress(event: KeyboardEvent) {
    const key = event.keyCode;
    if (key == 32 || key == 95) {
      return true;
    }
    if (!this.IsEnglishCharacter(key)) {
      event.preventDefault();
      this.toastr.error("English characters only allowed");
      return false;
    }
    return true;
  }

  IsEnglishCharacter(ch: any): boolean {
    if (ch >= 97 && ch <= 122 || ch >= 65 && ch <= 90) {
      return true;
    }
    return false;
  }

  removeExtraVariables(target: any, isHeader: boolean) {
    if (target?.value) {
      const regExp = new RegExp(/{{\d}}/g);
      const matchesCount = target.value.match(regExp)?.length;
      if (isHeader && matchesCount > 1) {
        return;
      }
      if (!isHeader && matchesCount) {
        return;
      }
    }
    return true;
  }
  get headerVars() {
    return <FormArray>this.createForm.get('headerVariablesExamples');
  }

  get bodyVars() {
    return <FormArray>this.createForm.get('bodyVariablesExamples');
  }

  get headerVariables(): Array<any> {
    const regExp = new RegExp(/{{\d}}/g);
    const matchesCount = this.createForm.get('headerText')?.value?.match(regExp)?.length ?? 0;
    return Array.from({ length: matchesCount }, (v, i) => '{{' + (++i) + '}}');
  }
  get bodyVariables(): Array<any> {
    const regExp = new RegExp(/{{\d}}/g);
    const matchesCount = this.createForm.get('bodyText')?.value?.match(regExp)?.length ?? 0;
    return Array.from({ length: matchesCount }, (v, i) => '{{' + (++i) + '}}');
  }

  openBodyModal() {
    const existedVariables = this.bodyVars.value;
    this.bodyVars.clear();
    this.bodyVariables.forEach((v, i) => {
      (<FormArray>this.createForm.get('bodyVariablesExamples')).push(this.fb.control(existedVariables[i], [Validators.required]));
    });
  }

  openHeaderModal() {
    const existedVariables = this.headerVars.value;
    this.headerVars.clear();
    this.headerVariables.forEach((v, i) => {
      (<FormArray>this.createForm.get('headerVariablesExamples')).push(this.fb.control(existedVariables[i], [Validators.required]));
    })
  }
}
