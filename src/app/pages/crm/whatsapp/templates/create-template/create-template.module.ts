import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CreateTemplateRoutingModule } from './create-template-routing.module';
import { CreateTemplateComponent } from './create-template.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';


@NgModule({
  declarations: [
    CreateTemplateComponent
  ],
  imports: [
    CommonModule,
    CreateTemplateRoutingModule,
    NgSelectModule,
    ReactiveFormsModule
  ]
})
export class CreateTemplateModule { }
