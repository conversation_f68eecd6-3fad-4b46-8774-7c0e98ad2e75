<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>WhatsApp Account Profile</strong>
    </div>
    <div class="row" *ngIf="profileForm">
      <form [formGroup]="profileForm">
        <div class="row">
          <div class="col-xl-8 col-sm-12">
            <div class="col">
              <label class="form-label">
                Sender Number
              </label>
              <ng-select [formControl]="phoneNumberControl" [items]="phoneNumbers" bindLabel="verified_name"
                         bindValue="id"></ng-select>
            </div>
            <div class="col">
              <label class="form-label">
                About
              </label>
              <input class="form-control " formControlName="about" />
            </div>
            <div class="col">
              <label class="form-label">
                Address
              </label>
              <input class="form-control " placeholder="Address " formControlName="address" />
            </div>
            <div class="col">
              <label class="form-label">
                Description
              </label>
              <textarea class="form-control " formControlName="description"></textarea>
            </div>
            <div class="col">
              <label class="form-label">
                E-mail
              </label>
              <input class="form-control " formControlName="email" />
            </div>
            <div class="col">
              <label class="form-label">
                Industry
              </label>
              <ng-select formControlName="vertical" [items]="industryList"></ng-select>
            </div>
            <div class="col">
              <label class="form-label">
                Profile Picture
              </label>
              <input type="file" (change)="upload($event , previewImg)" class="form-control" />
            </div>
            <div class="col">
              <label class="form-label">
                Website (Max 2 WebSite Url)
              </label>
              <div formArrayName="websites" class="row">
                <div *ngFor="let site of websites; let i=index" class="col-xl-6 col-sm-12">
                  <div class="col-12 mb-1">
                    <input class="form-control" [formControlName]="i" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-4 col-sm-12">
            <p>Profile Picture</p>
            <img [src]="profile_picture_url" width="100%"  #previewImg  class="img-thumbnail" style="min-width:350px;min-height:300px"/>
          </div>
        </div>
        <div class="row mt-3">
          <div class="mb-3 col-12">
            <button [disabled]="isLoading" class="btn btn-light" type="submit" (click)="save(profileForm.value)">
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
