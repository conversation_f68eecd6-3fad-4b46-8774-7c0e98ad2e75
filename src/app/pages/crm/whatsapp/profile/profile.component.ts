import { HttpClient } from '@angular/common/http';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';

@Component({
    selector: 'app-profile',
    templateUrl: './profile.component.html',
    styleUrls: ['./profile.component.css'],
    standalone: false
})
export class ProfileComponent implements OnInit, OnDestroy {
  profileForm!: FormGroup;
  isLoading: boolean = false;
  industryList: any[] = [
    "UNDEFINED",
    "OTHER",
    "AUTO",
    "BEAUTY",
    "APPAREL",
    "EDU",
    "ENTERTAIN",
    "EVENT_PLAN",
    "FINANCE",
    "GROCERY",
    "GOVT",
    "HOTEL",
    "HEALTH",
    "NONPROFIT",
    "PROF_SERVICES",
    "RETA<PERSON>",
    "TRAVEL",
    "RESTAURANT",
    "NOT_A_BIZ"
  ];
  subscription = new Subscription();
  profile_picture_url: string | undefined;
  phoneNumbers: any[] = [];
  phoneNumberControl = new FormControl();
  constructor(private fb: FormBuilder,
    private http: HttpClient,
    private toastr: ToastrService) {
    this.loadProfileData();
  }


  loadProfileData() {
    const phoneNumberId = this.phoneNumberControl.getRawValue() ?? '';
    this.subscription.add(
      this.http.get<any>('/api/wbprofile?phoneNumberId=' + phoneNumberId).subscribe(r => {
        if (r.success) {
          this.creaetForm(r.data);
          if (r.phoneNumbers) {
            this.phoneNumbers = r.phoneNumbers;
            this.phoneNumberControl.setValue(this.phoneNumbers[0].id, { emitEvent: false });
          }
          this.profile_picture_url = r.data.profile_picture_url;
        }
      })
    );
  }

  private creaetForm(model: any) {
    this.profileForm = this.fb.group({
      about: [model.about, [Validators.maxLength(139)]],
      address: [model.address, [Validators.maxLength(256)]],
      description: [model.description, [Validators.maxLength(512)]],
      profile_picture_handle: null,
      websites: this.fb.array([
        new FormControl(model.websites?.length > 0 ? model.websites[0] : null),
        new FormControl(model.websites?.length > 1 ? model.websites[1] : null)
      ]),
      vertical: model.vertical,
      email: [model.email, [Validators.email, Validators.maxLength(128)]],
      profile_picture_url: model.profile_picture_url
    });
  }

  upload(event: any, previewImg: HTMLImageElement) {
    if (event.target.files.length) {
      var file = event.target.files[0];
      const fd = new FormData();
      fd.append('file', file);
      this.subscription.add(this.http.post<any>("/api/wbtemplates/mediaHandle", fd)
        .subscribe(v => {
          if (v.success) {
            this.profileForm.get('profile_picture_handle')?.setValue(v.data.url);
            previewImg.src = URL.createObjectURL(file);
          }
        }));
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  ngOnInit(): void {
    this.subscription.add(this.phoneNumberControl.valueChanges.subscribe(r => {
      this.loadProfileData();
    }));
  }

  save(form: any) {
    if (this.profileForm?.valid) {
      const phoneNumberId = this.phoneNumberControl.getRawValue() ?? '';
      this.subscription.add(this.http.post<any>('/api/wbprofile?phoneNumberId=' + phoneNumberId, form)
        .subscribe(r => {
          if (r.success) {
            this.toastr.success('Profile Updated Successfully');
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.profileForm?.markAllAsTouched();
    }
  }

  get websites() {
    return (<FormArray>this.profileForm.get('websites')).controls;
  }

}
