<div class="card">
  <div class="card-body">
    <div class="card-title row">
      <div class="col-xl-8 col-sm-12 mb-1">
        <strong>Campaign List</strong>
        <ng-container *ngIf="pagesCount > 1">
          <a class="float-end pointer mx-2  my-1" style="text-decoration:none" (click)="prev()">Prev</a>
          <a class="float-end pointer mx-2 my-1" style="text-decoration:none" (click)="next()">Next</a>
        </ng-container>
      </div>
      <div class="col-xl-4 col-sm-12">
        <a [routerLink]="['/crm/whatsapp/templates/send']" class="float-end">Start Campaign</a>
      </div>
    </div>
    <div class="table-responsive">
      <table class="table table-hover" #repliesContainer>
        <thead>
          <tr>
            <th>Title</th>
            <th>Template</th>
            <th>Created Date</th>
            <th>Recipients</th>
            <th>Sent</th>
            <th>Delivered</th>
            <th>Read</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of data">
            <td>{{item.title}}</td>
            <td>{{item.templateName}}</td>
            <td>{{item.createdDate | date: 'yyyy-MM-dd hh:mm aa'}}</td>
            <td>{{item.recipientCount}}</td>
            <td>{{item.sentCount}}</td>
            <td>{{item.deliveredCount}}</td>
            <td>{{item.readCount}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
