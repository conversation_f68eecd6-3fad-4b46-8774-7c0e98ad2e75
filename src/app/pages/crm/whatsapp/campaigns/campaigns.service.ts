import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CampaignsService {

  constructor(private http: HttpClient) {
  }

  get(currentPage: number = 1): Observable<any> {
    return this.http.get<any>('/api/wbCampaigns/', {
      params: {
        'currentPage': currentPage
      }
    });
  }
}
