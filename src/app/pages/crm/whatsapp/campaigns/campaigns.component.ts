import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { finalize, Subscription } from 'rxjs';
import { CampaignsService } from './campaigns.service';

@Component({
    selector: 'app-campaigns',
    templateUrl: './campaigns.component.html',
    styleUrls: ['./campaigns.component.css'],
    standalone: false
})
export class CampaignsComponent implements OnInit, OnDestroy {
  data: any[] = [];
  subscriptions = new Subscription();
  currentDataPage = 0;
  itemsCount = 0;
  pagesCount = 0;
  isLoading: boolean = false;
  constructor(private campainService: CampaignsService) {
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.currentDataPage = 0;
    this.loadByPage();
  }
  next() {
    this.currentDataPage++;
    if (this.currentDataPage > this.pagesCount) {
      this.currentDataPage--;
      return;
    }
    this.loadByPage();
  }

  prev() {
    this.currentDataPage--;
    if (this.currentDataPage < 1) {
      this.currentDataPage = 1;
      return;
    }
    this.loadByPage();
  }
  loadByPage() {
    this.isLoading = true;
    this.subscriptions.add(this.campainService.get(this.currentDataPage)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(r => {
        if (r.success) {
          this.data = r.data;
          if (r.itemsCount && r.pagesCount) {
            this.itemsCount = r.itemsCount;
            this.pagesCount = r.pagesCount;
          }
        }
      }));
  }
  //initializeTooltip() {
  //  const tooltipTriggerList = Array.from(
  //    this.repliesContainer.nativeElement.querySelectorAll('[data-bs-toggle="tooltip"]')
  //  );
  //  tooltipTriggerList.forEach((tooltipTriggerEl) => {
  //    new bootstrap.Tooltip(tooltipTriggerEl);
  //  });
  //}
}
