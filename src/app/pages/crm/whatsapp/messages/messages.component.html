<div class="container--fluid header-bar">

</div>
<div class="container app">
  <div class="row app-one">

    <div class="col-xl-4 col-sm-12 side">
      <div class="side-one">
        <!-- Heading -->
        <div class="row heading">
          <div class="col-8 row heading-avatar">
            <div class="heading-avatar-icon col-3 ">
              <div class="user-icon">
                <svg viewBox="0 0 212 212" height="212" width="212" preserveAspectRatio="xMidYMid meet"
                     class="ln8gz9je ppled2lx" version="1.1" x="0px" y="0px" enable-background="new 0 0 212 212"
                     xml:space="preserve">
                <path fill="#DFE5E7" class="background"
                      d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z">
                  </path>
                <g>
                <path fill="#FFFFFF" class="primary"
                      d="M173.561,171.615c-0.601-0.915-1.287-1.907-2.065-2.955c-0.777-1.049-1.645-2.155-2.608-3.299 c-0.964-1.144-2.024-2.326-3.184-3.527c-1.741-1.802-3.71-3.646-5.924-5.47c-2.952-2.431-6.339-4.824-10.204-7.026 c-1.877-1.07-3.873-2.092-5.98-3.055c-0.062-0.028-0.118-0.059-0.18-0.087c-9.792-4.44-22.106-7.529-37.416-7.529 s-27.624,3.089-37.416,7.529c-0.338,0.153-0.653,0.318-0.985,0.474c-1.431,0.674-2.806,1.376-4.128,2.101 c-0.716,0.393-1.417,0.792-2.101,1.197c-3.421,2.027-6.475,4.191-9.15,6.395c-2.213,1.823-4.182,3.668-5.924,5.47 c-1.161,1.201-2.22,2.384-3.184,3.527c-0.964,1.144-1.832,2.25-2.609,3.299c-0.778,1.049-1.464,2.04-2.065,2.955 c-0.557,0.848-1.033,1.622-1.447,2.324c-0.033,0.056-0.073,0.119-0.104,0.174c-0.435,0.744-0.79,1.392-1.07,1.926 c-0.559,1.068-0.818,1.678-0.818,1.678v0.398c18.285,17.927,43.322,28.985,70.945,28.985c27.678,0,52.761-11.103,71.055-29.095 v-0.289c0,0-0.619-1.45-1.992-3.778C174.594,173.238,174.117,172.463,173.561,171.615z">
                    </path>
                <path fill="#FFFFFF" class="primary"
                      d="M106.002,125.5c2.645,0,5.212-0.253,7.68-0.737c1.234-0.242,2.443-0.542,3.624-0.896 c1.772-0.532,3.482-1.188,5.12-1.958c2.184-1.027,4.242-2.258,6.15-3.67c2.863-2.119,5.39-4.646,7.509-7.509 c0.706-0.954,1.367-1.945,1.98-2.971c0.919-1.539,1.729-3.155,2.422-4.84c0.462-1.123,0.872-2.277,1.226-3.458 c0.177-0.591,0.341-1.188,0.49-1.792c0.299-1.208,0.542-2.443,0.725-3.701c0.275-1.887,0.417-3.827,0.417-5.811 c0-1.984-0.142-3.925-0.417-5.811c-0.184-1.258-0.426-2.493-0.725-3.701c-0.15-0.604-0.313-1.202-0.49-1.793 c-0.354-1.181-0.764-2.335-1.226-3.458c-0.693-1.685-1.504-3.301-2.422-4.84c-0.613-1.026-1.274-2.017-1.98-2.971 c-2.119-2.863-4.646-5.39-7.509-7.509c-1.909-1.412-3.966-2.643-6.15-3.67c-1.638-0.77-3.348-1.426-5.12-1.958 c-1.181-0.355-2.39-0.655-3.624-0.896c-2.468-0.484-5.035-0.737-7.68-0.737c-21.162,0-37.345,16.183-37.345,37.345 C68.657,109.317,84.84,125.5,106.002,125.5z">
                    </path>
                  </g>
                </svg>
                <!--<span class="ml-5"> Contact List </span>-->
              </div>
            </div>
            <div class="col-9 p-1">
              <strong>Contact List </strong>
            </div>
          </div>
          <div class="col-2  heading-dot ">
            <!--<i class="fa fa-ellipsis-v fa-2x  pull-right" aria-hidden="true"></i>-->
          </div>
          <div class="col-2 heading-compose">
            <!--<i class="fa fa-comments fa-2x  pull-right" aria-hidden="true"></i>-->
          </div>
        </div>
        <!-- Heading End -->
        <!-- SearchBox -->
        <div class="row searchBox d-none d-xl-block">
          <div class="col-12 searchBox-inner">
            <div class="form-group has-feedback">
              <input id="searchText" type="text" class="form-control" name="searchText" placeholder="Search"
                     [ngClass]="{'message-reply-arabic' : isArabic(search.value) }"
                     (input)="searchList($event)" #search>
              <span class="glyphicon glyphicon-search form-control-feedback"></span>
            </div>
          </div>
        </div>

        <!-- Search Box End -->
        <!-- chat-sideBar -->
        <div class="row chat-sideBar">
          <div class="row chat-sideBar-body" *ngFor="let item of conversations" [ngClass]="{active: item == current }"
               (click)="setCurrent(item)">
            <div class="col-2 chat-sideBar-avatar d-none d-xl-flex">
              <div class="avatar-icon">
                <div class="user-icon">
                  <svg viewBox="0 0 212 212" height="212" width="212"
                       preserveAspectRatio="xMidYMid meet" class="ln8gz9je ppled2lx" version="1.1" x="0px" y="0px"
                       enable-background="new 0 0 212 212" xml:space="preserve">
                  <path fill="#DFE5E7" class="background"
                        d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z">
                    </path>
                  <g>
                  <path fill="#FFFFFF" class="primary"
                        d="M173.561,171.615c-0.601-0.915-1.287-1.907-2.065-2.955c-0.777-1.049-1.645-2.155-2.608-3.299 c-0.964-1.144-2.024-2.326-3.184-3.527c-1.741-1.802-3.71-3.646-5.924-5.47c-2.952-2.431-6.339-4.824-10.204-7.026 c-1.877-1.07-3.873-2.092-5.98-3.055c-0.062-0.028-0.118-0.059-0.18-0.087c-9.792-4.44-22.106-7.529-37.416-7.529 s-27.624,3.089-37.416,7.529c-0.338,0.153-0.653,0.318-0.985,0.474c-1.431,0.674-2.806,1.376-4.128,2.101 c-0.716,0.393-1.417,0.792-2.101,1.197c-3.421,2.027-6.475,4.191-9.15,6.395c-2.213,1.823-4.182,3.668-5.924,5.47 c-1.161,1.201-2.22,2.384-3.184,3.527c-0.964,1.144-1.832,2.25-2.609,3.299c-0.778,1.049-1.464,2.04-2.065,2.955 c-0.557,0.848-1.033,1.622-1.447,2.324c-0.033,0.056-0.073,0.119-0.104,0.174c-0.435,0.744-0.79,1.392-1.07,1.926 c-0.559,1.068-0.818,1.678-0.818,1.678v0.398c18.285,17.927,43.322,28.985,70.945,28.985c27.678,0,52.761-11.103,71.055-29.095 v-0.289c0,0-0.619-1.45-1.992-3.778C174.594,173.238,174.117,172.463,173.561,171.615z">
                      </path>
                  <path fill="#FFFFFF" class="primary"
                        d="M106.002,125.5c2.645,0,5.212-0.253,7.68-0.737c1.234-0.242,2.443-0.542,3.624-0.896 c1.772-0.532,3.482-1.188,5.12-1.958c2.184-1.027,4.242-2.258,6.15-3.67c2.863-2.119,5.39-4.646,7.509-7.509 c0.706-0.954,1.367-1.945,1.98-2.971c0.919-1.539,1.729-3.155,2.422-4.84c0.462-1.123,0.872-2.277,1.226-3.458 c0.177-0.591,0.341-1.188,0.49-1.792c0.299-1.208,0.542-2.443,0.725-3.701c0.275-1.887,0.417-3.827,0.417-5.811 c0-1.984-0.142-3.925-0.417-5.811c-0.184-1.258-0.426-2.493-0.725-3.701c-0.15-0.604-0.313-1.202-0.49-1.793 c-0.354-1.181-0.764-2.335-1.226-3.458c-0.693-1.685-1.504-3.301-2.422-4.84c-0.613-1.026-1.274-2.017-1.98-2.971 c-2.119-2.863-4.646-5.39-7.509-7.509c-1.909-1.412-3.966-2.643-6.15-3.67c-1.638-0.77-3.348-1.426-5.12-1.958 c-1.181-0.355-2.39-0.655-3.624-0.896c-2.468-0.484-5.035-0.737-7.68-0.737c-21.162,0-37.345,16.183-37.345,37.345 C68.657,109.317,84.84,125.5,106.002,125.5z">
                      </path>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
            <div class="col-xl-10 col-sm-12 chat-sideBar-main">
              <div class="row">
                <div class="col-xl-8 col-sm-12  chat-sideBar-name">
                  <span class="name-meta">
                    {{item.name || item.from}}
                    <br />
                    <span class="d-none d-xl-block">
                      {{item.lastMsgText}}
                    </span>
                  </span>
                </div>
                <div class="col-xl-4 col-sm-12 float-end chat-sideBar-time">
                  <span class="time-meta pull-right">
                    {{formatTimeFromNow(item.time)}}
                  </span>
                  <span class="badge float-end rounded-circle unread-counter" *ngIf="item?.unReadCount > 0">
                    {{item?.unReadCount}}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- chat-sideBar End -->
      </div>
    </div>


    <!-- New Message chat-sideBar End -->
    <!-- Conversation Start -->
    <div class="col-xl-8 col-sm-12 conversation">
      <!-- Heading -->
      <div class="row heading">
        <div class="col-1 heading-avatar">
          <div class="heading-avatar-icon">
            <div class="user-icon" *ngIf="current">
              <!-- <svg  height="1em"
                viewBox="0 0 448 512">
                <path
                  d="M304 128a80 80 0 1 0 -160 0 80 80 0 1 0 160 0zM96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM49.3 464H398.7c-8.9-63.3-63.3-112-129-112H178.3c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3z" />
              </svg> -->
              <svg viewBox="0 0 212 212" height="212" width="212" preserveAspectRatio="xMidYMid meet"
                   class="ln8gz9je ppled2lx" version="1.1" x="0px" y="0px" enable-background="new 0 0 212 212"
                   xml:space="preserve">
              <path fill="#DFE5E7" class="background"
                    d="M106.251,0.5C164.653,0.5,212,47.846,212,106.25S164.653,212,106.25,212C47.846,212,0.5,164.654,0.5,106.25 S47.846,0.5,106.251,0.5z">
                </path>
              <g>
              <path fill="#FFFFFF" class="primary"
                    d="M173.561,171.615c-0.601-0.915-1.287-1.907-2.065-2.955c-0.777-1.049-1.645-2.155-2.608-3.299 c-0.964-1.144-2.024-2.326-3.184-3.527c-1.741-1.802-3.71-3.646-5.924-5.47c-2.952-2.431-6.339-4.824-10.204-7.026 c-1.877-1.07-3.873-2.092-5.98-3.055c-0.062-0.028-0.118-0.059-0.18-0.087c-9.792-4.44-22.106-7.529-37.416-7.529 s-27.624,3.089-37.416,7.529c-0.338,0.153-0.653,0.318-0.985,0.474c-1.431,0.674-2.806,1.376-4.128,2.101 c-0.716,0.393-1.417,0.792-2.101,1.197c-3.421,2.027-6.475,4.191-9.15,6.395c-2.213,1.823-4.182,3.668-5.924,5.47 c-1.161,1.201-2.22,2.384-3.184,3.527c-0.964,1.144-1.832,2.25-2.609,3.299c-0.778,1.049-1.464,2.04-2.065,2.955 c-0.557,0.848-1.033,1.622-1.447,2.324c-0.033,0.056-0.073,0.119-0.104,0.174c-0.435,0.744-0.79,1.392-1.07,1.926 c-0.559,1.068-0.818,1.678-0.818,1.678v0.398c18.285,17.927,43.322,28.985,70.945,28.985c27.678,0,52.761-11.103,71.055-29.095 v-0.289c0,0-0.619-1.45-1.992-3.778C174.594,173.238,174.117,172.463,173.561,171.615z">
                  </path>
              <path fill="#FFFFFF" class="primary"
                    d="M106.002,125.5c2.645,0,5.212-0.253,7.68-0.737c1.234-0.242,2.443-0.542,3.624-0.896 c1.772-0.532,3.482-1.188,5.12-1.958c2.184-1.027,4.242-2.258,6.15-3.67c2.863-2.119,5.39-4.646,7.509-7.509 c0.706-0.954,1.367-1.945,1.98-2.971c0.919-1.539,1.729-3.155,2.422-4.84c0.462-1.123,0.872-2.277,1.226-3.458 c0.177-0.591,0.341-1.188,0.49-1.792c0.299-1.208,0.542-2.443,0.725-3.701c0.275-1.887,0.417-3.827,0.417-5.811 c0-1.984-0.142-3.925-0.417-5.811c-0.184-1.258-0.426-2.493-0.725-3.701c-0.15-0.604-0.313-1.202-0.49-1.793 c-0.354-1.181-0.764-2.335-1.226-3.458c-0.693-1.685-1.504-3.301-2.422-4.84c-0.613-1.026-1.274-2.017-1.98-2.971 c-2.119-2.863-4.646-5.39-7.509-7.509c-1.909-1.412-3.966-2.643-6.15-3.67c-1.638-0.77-3.348-1.426-5.12-1.958 c-1.181-0.355-2.39-0.655-3.624-0.896c-2.468-0.484-5.035-0.737-7.68-0.737c-21.162,0-37.345,16.183-37.345,37.345 C68.657,109.317,84.84,125.5,106.002,125.5z">
                  </path>
                </g>
              </svg>
            </div>
          </div>
        </div>
        <div class="col-8 heading-name" *ngIf="current">
          <a class="heading-name-meta">
            {{ current.name }}
          </a>
          <span class="heading-online">Online</span>
        </div>
        <div class="col-3 d-none d-xl-block" *ngIf="current">
          <strong *ngIf="current.showMobile" class="float-end px-2"> {{ current.from }}</strong>
          <i data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="Mobile" data-bs-trigger="hover" (click)="current.showMobile = !current.showMobile" class="fa fa-mobile fa-2x float-end pointer"></i>
        </div>
        <div class="col-2 heading-dot d-xl-none d-sm-block">
          <!--<i class="fa fa-ellipsis-v fa-2x  pull-right" aria-hidden="true"></i>-->
          <span (click)="showContacts()">
            <i class="fa fa-arrow-left float-end" aria-hidden="true"></i>
          </span>

        </div>
      </div>
      <!--<ngx-emoji [emoji]="{ id: 'xyz', skin: 3 }" size="16"></ngx-emoji>-->
      <!-- Heading End -->
      <div class="row empty-conversation" *ngIf="!current">
        <p>
          <svg height="1em" viewBox="0 0 448 512">
            <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z" />
          </svg>
          WhatsApp Business
        </p>
      </div>
      <!-- Message Box -->
      <div class="row message" id="conversation" #conversation (scroll)="markVisibleMessagesAsRead()">
        <div class="page-separator text-success text-center w-100">
          &nbsp;
          <div class="spinner-border text-success" role="status" *ngIf="isLoading">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        <ng-container *ngFor="let msg of current?.messages">
          <div [ngClass]="{'message-body-reaction': msg.reaction}" class="row message-body" *ngIf="!msg.isSender">
            <div class="col-12 message-main-receiver" [id]="msg.messageId" [ngClass]="{read: msg.read}">
              <div class="receiver" [ngClass]="{'image-size': msg.image}">
                <div class="message-text" [ngClass]="{'message-text-arabic' : isArabic(msg.text) , 'emoji-size text-center': containsEmoji(msg.text) , 'image-size': msg.image || msg.video || msg.audio || msg.docId}">
                  <span [innerHtml]="formatText(msg.text)"></span>
                  <img (click)="previewImage(msg.image)" class="img-thumbnail image-fixed-container pointer" [src]="msg.image" #img width="330" *ngIf="msg.type == 'image' || msg.type == 'sticker' " />
                  <video class="img-thumbnail image-fixed-container" #video width="330" *ngIf="msg.type == 'video'" controls>
                    <source [src]="msg.video" [type]="msg.contentType">
                  </video>
                  <audio controls *ngIf="msg.type == 'audio'" style=" padding-top: 13px; height: 30px !important; width: 330px;">
                    <source [src]="msg.audio" [type]="msg.contentType">
                    Your browser does not support the audio element.
                  </audio>
                  <div *ngIf="msg.type == 'document'" class="row" style="min-width:330px;">
                    <div class="col-12 doc-msg">
                      <i class="fa fa-file"></i>
                      <span class="file-name">
                        {{msg.fileName}}
                      </span>
                      <span title="download" class="doc-download-icon">
                        <svg *ngIf="downloadMedia != msg.docId" (click)="download(msg.docId,msg.fileName)" class="pointer" viewBox="0 0 512 512"><path d="M256 464a208 208 0 1 1 0-416 208 208 0 1 1 0 416zM256 0a256 256 0 1 0 0 512A256 256 0 1 0 256 0zM376.9 294.6c4.5-4.2 7.1-10.1 7.1-16.3c0-12.3-10-22.3-22.3-22.3H304V160c0-17.7-14.3-32-32-32l-32 0c-17.7 0-32 14.3-32 32v96H150.3C138 256 128 266 128 278.3c0 6.2 2.6 12.1 7.1 16.3l107.1 99.9c3.8 3.5 8.7 5.5 13.8 5.5s10.1-2 13.8-5.5l107.1-99.9z" /></svg>
                        <span *ngIf="downloadMedia == msg.docId" class="spinner-border text-success mx-3" role="status" style="width:23px;height:23px">
                          <span class="visually-hidden">Loading...</span>
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="px-2" style="max-width:330px" [dir]="isArabic(msg.caption) ? 'rtl': 'ltr'" *ngIf="(msg.type == 'image' || msg.type == 'sticker' || msg.type == 'video' || msg.type == 'document') && msg.caption">
                    {{msg.caption}}
                  </div>
                </div>
                <span class="message-time px-2" dir="ltr" [ngClass]="{'pull-left' : isArabic(msg.text ? msg.text : msg.caption) , 'pull-right' :  !isArabic(msg.text ? msg.text : msg.caption)}">
                  {{formatTime(msg.time)}}
                </span>
                <div class="col-12 message-reaction" *ngIf="msg.reaction">
                  <button class="btn btn-light rounded">{{msg.reaction}}</button>
                </div>
              </div>
            </div>
          </div>
          <div [ngClass]="{'message-body-reaction': msg.reaction}" class="row message-body" *ngIf="msg.isSender">
            <div class="col-12 message-main-sender">
              <div class="sender" [ngClass]="{'image-size': msg.image}">
                <div class="message-text" [ngClass]="{'message-text-arabic' : isArabic(msg.text ? msg.text : msg.caption) ,  'emoji-size text-center': containsEmoji(msg.text) , 'image-size': msg.image || msg.video || msg.audio || msg.docId}">
                  <div class="header-text" *ngIf="msg.headerText"><b>{{msg.headerText}}</b></div>
                  <span [innerHtml]="formatText(msg.text)"></span>
                  <img (click)="previewImage(msg.image)" class="img-thumbnail image-fixed-container pointer" [src]="msg.image" #img width="330" *ngIf="msg.type == 'image' || msg.type == 'sticker' " />
                  <video class="img-thumbnail image-fixed-container" #video width="330" *ngIf="msg.type == 'video'" controls>
                    <source [src]="msg.video" [type]="msg.contentType">
                  </video>
                  <audio width="330" controls *ngIf="msg.type == 'audio'" style=" padding-top: 13px; height: 30px !important; width: 330px;">
                    <source [src]="msg.audio" [type]="msg.contentType">
                    Your browser does not support the audio element.
                  </audio>
                  <div *ngIf="msg.type == 'document'" class="row" style="min-width:330px;">
                    <div class="col-12 doc-msg">
                      <i class="fa fa-file"></i>
                      <span class="file-name">
                        {{msg.fileName}}
                      </span>
                      <span title="download" class="doc-download-icon">
                        <svg *ngIf="downloadMedia != msg.docId" (click)="download(msg.docId,msg.fileName)" class="pointer" viewBox="0 0 512 512"><path d="M256 464a208 208 0 1 1 0-416 208 208 0 1 1 0 416zM256 0a256 256 0 1 0 0 512A256 256 0 1 0 256 0zM376.9 294.6c4.5-4.2 7.1-10.1 7.1-16.3c0-12.3-10-22.3-22.3-22.3H304V160c0-17.7-14.3-32-32-32l-32 0c-17.7 0-32 14.3-32 32v96H150.3C138 256 128 266 128 278.3c0 6.2 2.6 12.1 7.1 16.3l107.1 99.9c3.8 3.5 8.7 5.5 13.8 5.5s10.1-2 13.8-5.5l107.1-99.9z" /></svg>
                        <span *ngIf="downloadMedia == msg.docId" class="spinner-border text-success mx-3" role="status" style="width:23px;height:23px">
                          <span class="visually-hidden">Loading...</span>
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="px-2" style="max-width:330px" [dir]="isArabic(msg.caption) ? 'rtl': 'ltr'" *ngIf=" (msg.type == 'image' || msg.type == 'sticker' || msg.type == 'video' || msg.type == 'document') && msg.caption">
                    {{msg.caption}}
                  </div>
                  <div class="footer-text" *ngIf="msg.footerText">{{msg.footerText}}</div>
                </div>
                <div class="row" [dir]="isArabic(msg.text ? msg.text : msg.caption)? 'ltr': 'rtl'">
                  <div class="col-1" [ngClass]="{'icon-read': msg.read , 'px-3': !containsEmoji(msg.text)}">
                    <svg *ngIf="msg.delivered || msg.read" viewBox="0 0 16 11" height="11" width="16" preserveAspectRatio="xMidYMid meet" fill="none"><path d="M11.0714 0.652832C10.991 0.585124 10.8894 0.55127 10.7667 0.55127C10.6186 0.55127 10.4916 0.610514 10.3858 0.729004L4.19688 8.36523L1.79112 6.09277C1.7488 6.04622 1.69802 6.01025 1.63877 5.98486C1.57953 5.95947 1.51817 5.94678 1.45469 5.94678C1.32351 5.94678 1.20925 5.99544 1.11192 6.09277L0.800883 6.40381C0.707784 6.49268 0.661235 6.60482 0.661235 6.74023C0.661235 6.87565 0.707784 6.98991 0.800883 7.08301L3.79698 10.0791C3.94509 10.2145 4.11224 10.2822 4.29844 10.2822C4.40424 10.2822 4.5058 10.259 4.60313 10.2124C4.70046 10.1659 4.78086 10.1003 4.84434 10.0156L11.4903 1.59863C11.5623 1.5013 11.5982 1.40186 11.5982 1.30029C11.5982 1.14372 11.5348 1.01888 11.4078 0.925781L11.0714 0.652832ZM8.6212 8.32715C8.43077 8.20866 8.2488 8.09017 8.0753 7.97168C7.99489 7.89128 7.8891 7.85107 7.75791 7.85107C7.6098 7.85107 7.4892 7.90397 7.3961 8.00977L7.10411 8.33984C7.01947 8.43717 6.97715 8.54508 6.97715 8.66357C6.97715 8.79476 7.0237 8.90902 7.1168 9.00635L8.1959 10.0791C8.33132 10.2145 8.49636 10.2822 8.69102 10.2822C8.79681 10.2822 8.89838 10.259 8.99571 10.2124C9.09304 10.1659 9.17556 10.1003 9.24327 10.0156L15.8639 1.62402C15.9358 1.53939 15.9718 1.43994 15.9718 1.32568C15.9718 1.1818 15.9125 1.05697 15.794 0.951172L15.4386 0.678223C15.3582 0.610514 15.2587 0.57666 15.1402 0.57666C14.9964 0.57666 14.8715 0.635905 14.7657 0.754395L8.6212 8.32715Z" fill="currentColor"></path></svg>
                    <svg *ngIf="msg.sent && !msg.delivered && !msg.read" viewBox="0 0 12 11" height="11" width="12" preserveAspectRatio="xMidYMid meet" class="" fill="none"><path d="M11.1549 0.652832C11.0745 0.585124 10.9729 0.55127 10.8502 0.55127C10.7021 0.55127 10.5751 0.610514 10.4693 0.729004L4.28038 8.36523L1.87461 6.09277C1.8323 6.04622 1.78151 6.01025 1.72227 5.98486C1.66303 5.95947 1.60166 5.94678 1.53819 5.94678C1.407 5.94678 1.29275 5.99544 1.19541 6.09277L0.884379 6.40381C0.79128 6.49268 0.744731 6.60482 0.744731 6.74023C0.744731 6.87565 0.79128 6.98991 0.884379 7.08301L3.88047 10.0791C4.02859 10.2145 4.19574 10.2822 4.38194 10.2822C4.48773 10.2822 4.58929 10.259 4.68663 10.2124C4.78396 10.1659 4.86436 10.1003 4.92784 10.0156L11.5738 1.59863C11.6458 1.5013 11.6817 1.40186 11.6817 1.30029C11.6817 1.14372 11.6183 1.01888 11.4913 0.925781L11.1549 0.652832Z" fill="currentcolor"></path></svg>
                  </div>
                  <div class="col-8" [ngClass]="{'px-2': !containsEmoji(msg.text)}">
                    <span class="message-time" dir="ltr" [ngClass]="{'pull-left' : isArabic(msg.text ? msg.text : msg.caption) , 'pull-right' :  !isArabic(msg.text ? msg.text : msg.caption)}">
                      {{formatTime(msg.time)}}
                    </span>
                  </div>
                </div>
                <div class="col-12 message-reaction" *ngIf="msg.reaction">
                  <button class="btn btn-light rounded">{{msg.reaction}}</button>
                </div>
              </div>
            </div>
          </div>
          <div class="alert bg-danger text-white" *ngIf="msg.errorMessage">
            {{msg.errorMessage}}
          </div>
        </ng-container>
        <div class="alert bg-secondary text-white" *ngIf="current?.isBlocked">
          This Account is Blocked
        </div>
      </div>
      <div class="row" [ngStyle]="{display: currentImageToUpload ? 'block' : 'none' }" style="position:relative; width:100%; height:580px;">
        <div class="col-12 text-center">
          <img #imgDist width="500" height="500" />
          <div class="spinner-border text-success" role="status" *ngIf="isUploading" style="position: absolute; top: 50%; right: 50%;">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
      <div class="row doc-container" [ngStyle]="{display: currentDocToUpload ? 'flex' : 'none' }">
        <div class="col-12 doc-container-text" #fileDist>
          <i class="fa fa-file fa-3x px-2"></i>
          <p>
            <span>
              {{ currentDocToUpload?.name }}
            </span>
            <br />
            Size :  {{ currentDocToUpload?.size ? (currentDocToUpload?.size / 1024).toFixed(2) + 'Kb' : '' }}
          </p>
          <div class="spinner-border text-success" role="status" *ngIf="isUploading" style="position: absolute; top: 50%; right: 50%;">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
      <!-- Message Box End -->
      <!-- Reply Box -->
      <div class="row reply" *ngIf="current && !current.isBlocked">
        <div class="col-1 reply-attachment text-center" *ngIf="!isRecording">
          <div class="btn-group dropup" *ngIf="!currentImageToUpload && !currentDocToUpload">
            <svg data-bs-toggle="dropdown" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-toggle feather feather-file-plus  pointer"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" /><polyline points="14 2 14 8 20 8" /><line x1="12" y1="18" x2="12" y2="12" /><line x1="9" y1="15" x2="15" y2="15" /></svg>
            <ul class="dropdown-menu">
              <li>
                <a class="dropdown-item" [routerLink]="[]" (click)="uploadImageOrVideo(imageFile,imgDist)">
                  <i class="fa fa-image"></i>
                  Image Or Video
                </a>
                <input type="file" #imageFile style="display:none" />
              </li>
              <li>
                <a class="dropdown-item" [routerLink]="[]" (click)="uploadDocument(docFile,imgDist)">
                  <i class="fa fa-file"></i>
                  Document
                </a>
                <input type="file" #docFile style="display:none" />
              </li>
            </ul>
          </div>
          <div (click)="removeUploadedImage(imgDist)" class="remove-icon">
            <svg *ngIf="currentImageToUpload" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 pointer"><polyline points="3 6 5 6 21 6" /><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" /><line x1="10" y1="11" x2="10" y2="17" /><line x1="14" y1="11" x2="14" y2="17" /></svg>
          </div>
          <div (click)="removeUploadedDoc(fileDist)" class="remove-icon">
            <svg *ngIf="currentDocToUpload" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2 pointer"><polyline points="3 6 5 6 21 6" /><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" /><line x1="10" y1="11" x2="10" y2="17" /><line x1="14" y1="11" x2="14" y2="17" /></svg>
          </div>
        </div>
        <div *ngIf="!isRecording" class="col-1 reply-emojis text-center pointer" (click)="toggleEmojiPicker()" [ngClass]="{'reply-emojis-svg':showEmojiPicker}">
          <i class="fa fa-smile-o fa-2x" *ngIf="!showEmojiPicker"></i>
          <svg *ngIf="showEmojiPicker" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18" /><line x1="6" y1="6" x2="18" y2="18" /></svg>
        </div>
        <div class="col-9 reply-main" *ngIf="!isRecording">
          <emoji-mart (emojiSelect)="addEmoji($event,message)" class="emoji-mart"
                      [showSingleCategory]="true"
                      [showPreview]="false" [isNative]="true" *ngIf="showEmojiPicker" title="Pick your emoji…"></emoji-mart>
          <textarea class="form-control no-validation-border"
                    autocomplete="off" rows="1"
                    #message
                    [formControl]="messageBodyCtrl"
                    (keyup)="sendIfEnter($event,message.value)"
                    [ngClass]="{'message-reply-arabic' : isArabic(message.value) }"
                    id="comment"></textarea>
        </div>
        <div class="col-11" *ngIf="isRecording">
          <div class="row">
            <div class="col-8 pointer py-2" (click)="abortRecording()" style="text-align:right">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6" /><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" /><line x1="10" y1="11" x2="10" y2="17" /><line x1="14" y1="11" x2="14" y2="17" /></svg>
            </div>
            <div class="col-4 py-2 text-center">
              <span>{{ recordedTime }}</span>
            </div>
          </div>
        </div>
        <div class="col-1 reply-recording text-center pointer" *ngIf="!hasValue && !isRecording" (click)="startRecording()">
          <i class="fa fa-microphone fa-2x" aria-hidden="true"></i>
        </div>
        <div class="col-1 reply-send text-center pointer" (click)="sendAudio()" *ngIf="isRecording">
          <i class="fa fa-send fa-2x" aria-hidden="true"></i>
        </div>
        <div class="col-1 reply-send text-center pointer" (click)="send()" *ngIf="hasValue">
          <i class="fa fa-send fa-2x" aria-hidden="true"></i>
        </div>
      </div>
      <!-- Reply Box End -->
    </div>
    <!-- Conversation End -->
  </div>
  <!-- App One End -->
</div>
