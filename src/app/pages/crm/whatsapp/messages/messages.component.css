.fa-2x {
  font-size: 1.5em !important;
}

.app {
  top: -25px !important;
  position: relative;
  overflow: hidden;
  height: calc(100% - 50px);
  height: 688px;
  margin-bottom: 30px !important;
  margin: auto !important;
  padding: 0 !important;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06), 0 2px 5px 0 rgba(0, 0, 0, 0.2);
}

.app-one {
  background-color: #f7f7f7;
  height: 100%;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.06), 0 2px 5px 0 rgba(0, 0, 0, 0.2);
}

.side {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
}

.side-one {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  position: relative;
  display: block;
  top: 0;
}

.side-two {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
  width: 100%;
  z-index: 2;
  position: relative;
  top: -100%;
  left: -100%;
  -webkit-transition: left 0.3s ease;
  transition: left 0.3s ease;
}

.heading {
  padding: 5px 5px;
  margin: 0 !important;
  height: 40px;
  width: 100%;
  background-color: #eee;
  color: #fff !important;
  z-index: 1000;
}

.heading-avatar {
  padding: 0 !important;
  cursor: pointer;
}

.heading-avatar-icon img {
  border-radius: 50%;
  height: 40px;
  width: 40px;
}

.heading-name {
  padding: 0 !important;
  cursor: pointer;
}

.heading-name-meta {
  font-weight: 700;
  font-size: 100%;
  padding: 5px;
  padding-bottom: 0;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  /*color: #000;*/
  display: block;
}

.heading-online {
  display: none;
  padding: 0 5px;
  font-size: 12px;
  color: #93918f;
}

.heading-compose {
  padding: 0;
}

  .heading-compose i {
    text-align: center;
    padding: 5px;
    color: #93918f;
    cursor: pointer;
  }

.heading-dot {
  padding: 0;
  margin-left: 10px;
}

  .heading-dot i {
    text-align: right;
    padding: 5px;
    color: #93918f;
    cursor: pointer;
  }

.searchBox {
  padding: 0 !important;
  margin: 0 !important;
  height: 60px;
  width: 100%;
}

.searchBox-inner {
  height: 100%;
  width: 100%;
  padding: 10px !important;
  background-color: #fbfbfb;
}


  .searchBox-inner input:focus {
    outline: none;
    border: none;
    box-shadow: none;
  }

.chat-sideBar {
  padding: 0 !important;
  margin: 0 !important;
  background-color: #fff;
  border: 1px solid #f7f7f7;
  height: 520px !important;
  max-height: 100%;
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow-y: scroll;
}

.chat-sideBar-body {
  position: relative;
  padding: 10px !important;
  border-bottom: 1px solid #eee; /*#f7f7f7;*/
  height: 65px;
  margin: 0 !important;
  cursor: pointer;
}

  .chat-sideBar-body:hover {
    background-color: #f0f2f5;
  }


  .chat-sideBar-body.active {
    background-color: #f0f2f5;
  }

.chat-sideBar-avatar {
  text-align: center;
  padding: 0 !important;
}

.avatar-icon img {
  border-radius: 50%;
  height: 49px;
  width: 49px;
}

.chat-sideBar-main {
  padding: 0 !important;
}

  .chat-sideBar-main .row {
    padding: 0 !important;
    margin: 0 !important;
  }

.chat-sideBar-name {
  /*padding: 10px !important;*/
}

.name-meta {
  font-size: 100%;
  padding: 1% !important;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #000;
}

.chat-sideBar-time {
  padding: 5px !important;
}

.time-meta {
  text-align: right;
  font-size: 12px;
  padding: 1% !important;
  color: rgba(0, 0, 0, 0.4);
  vertical-align: baseline;
}

/*New Message*/

.newMessage {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
  position: relative;
  left: -100%;
}

.newMessage-heading {
  padding: 10px 16px 10px 15px !important;
  margin: 0 !important;
  height: 100px;
  width: 100%;
  background-color: #00bfa5;
  z-index: 1001;
}

.newMessage-main {
  padding: 10px 16px 0 15px !important;
  margin: 0 !important;
  height: 60px;
  margin-top: 30px !important;
  width: 100%;
  z-index: 1001;
  color: #fff;
}

.newMessage-title {
  font-size: 18px;
  font-weight: 700;
  padding: 10px 5px !important;
}

.newMessage-back {
  text-align: center;
  vertical-align: baseline;
  padding: 12px 5px !important;
  display: block;
  cursor: pointer;
}

  .newMessage-back i {
    margin: auto !important;
  }

.composeBox {
  padding: 0 !important;
  margin: 0 !important;
  height: 60px;
  width: 100%;
}

.composeBox-inner {
  height: 100%;
  width: 100%;
  padding: 10px !important;
  background-color: #fbfbfb;
}

  .composeBox-inner input:focus {
    outline: none;
    border: none;
    box-shadow: none;
  }

.compose-chat-sideBar {
  padding: 0 !important;
  margin: 0 !important;
  background-color: #fff;
  overflow-y: auto;
  border: 1px solid #f7f7f7;
  height: calc(100% - 160px);
}

/*Conversation*/

.conversation {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
  /*width: 100%;*/
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  /*overflow-y: auto;*/
}

.message {
  padding: 0 !important;
  margin: 0 !important;
  /* background: url("assets/img/avatars/avatar-2.jpg") no-repeat fixed center; */
  background-size: cover;
  overflow-y: auto;
  border: 1px solid #f7f7f7;
  /* height: calc(100% - 120px); */
}

.message-previous {
  margin: 0 !important;
  padding: 0 !important;
  height: auto;
  width: 100%;
}

.previous {
  font-size: 15px;
  text-align: center;
  padding: 10px !important;
  cursor: pointer;
}

  .previous a {
    text-decoration: none;
    font-weight: 700;
  }

.message-body {
  margin: 0 !important;
  padding: 0 !important;
  margin-bottom: 3px !important;
}

.message-main-receiver {
  max-width: 60%;
}

.message-main-sender {
  padding: .5px 20px !important;
  margin-left: 40% !important;
  max-width: 60%;
}

.message-text {
  margin: 0 !important;
  padding: 0px !important;
  word-wrap: break-word;
  /*font-weight: 200;*/
  font-size: 14px;
  padding-bottom: 0 !important;
  padding-right: 60px !important;
  padding-top: 2px !important;
}

.message-time {
  margin: 0 !important;
  font-size: .6875rem;
  text-align: right;
  color: #9a9a9a;
  padding-bottom: 5px;
}

.receiver {
  width: auto !important;
  padding: 0.5px 10px !important;
  border-radius: 7.5px;
  background: #ffffff;
  font-size: 12px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  word-wrap: break-word;
  display: inline-block;
  box-shadow: 0 1px 0.5px rgba(11,20,26,.13);
}

.message-text-arabic {
  direction: rtl;
  padding-left: 60px !important;
  padding-right: 5px !important;
}

.sender {
  float: right;
  width: auto !important;
  background: #d9fdd3;
  padding: 0.5px 10px !important;
  font-size: 12px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  display: inline-block;
  word-wrap: break-word;
  box-shadow: 0 1px 0.5px rgba(11,20,26,.13);
  border-radius: 7.5px;
}

/* .sender .message-text {
    padding-left: 60px !important;
  }*/

/*Reply*/
.reply {
  height: 60px;
  width: 100%;
  background-color: #f0f2f5;
  padding: 10px 5px 10px 5px !important;
  margin: 0 !important;
  z-index: 1000;
}

  .reply svg {
    /*color: #54656f;*/
    color: #54656f !important;
  }

.reply-emojis {
  padding: 5px !important;
}

  .reply-emojis i {
    text-align: center;
    padding: 5px 5px 5px 5px !important;
    /*color: #54656f;*/
    color: #54656f !important;
    cursor: pointer;
  }

.reply-recording {
  padding: 5px !important;
}

  .reply-recording i {
    text-align: center;
    padding: 5px !important;
    /*color: #54656f;*/
    /*color: #54656f !important;*/
    color: #54656f !important;
    cursor: pointer;
  }

.reply-send {
  padding: 5px !important;
}

  .reply-send i {
    text-align: center;
    padding: 5px !important;
    /*color: #93918f;*/
    color: #54656f !important;
    cursor: pointer;
  }


.reply-main {
  padding: 2px 5px !important;
}

  .reply-main textarea {
    width: 100%;
    resize: none;
    overflow: hidden;
    padding: 5px !important;
    outline: none;
    border: none;
    text-indent: 5px;
    box-shadow: none;
    height: 100%;
    font-size: 16px;
    overflow-y: auto;
  }

    .reply-main textarea:focus {
      outline: none;
      border: none;
      text-indent: 5px;
      box-shadow: none;
    }

@media screen and (max-width: 700px) {
  .app {
    top: 0;
    height: auto !important;
  }

  .heading {
    height: 50px;
    background-color: #009688;
  }

  .fa-2x {
    font-size: 2.3em !important;
  }

  .heading-avatar {
    padding: 0 !important;
  }

  .heading-avatar-icon img {
    height: 50px;
    width: 50px;
  }

  .heading-compose {
    padding: 5px !important;
  }

    .heading-compose i {
      color: #fff;
      cursor: pointer;
    }

  .heading-dot {
    padding: 5px !important;
    margin-left: 10px !important;
  }

    .heading-dot i {
      color: #fff;
      cursor: pointer;
    }

  .chat-sideBar {
    height: calc(100% - 130px);
  }

  .chat-sideBar-body {
    height: 80px;
  }

  .chat-sideBar-avatar {
    text-align: left;
    padding: 0 8px !important;
  }

  .avatar-icon img {
    height: 55px;
    width: 55px;
  }

  .chat-sideBar-main {
    padding: 0 !important;
  }

    .chat-sideBar-main .row {
      padding: 0 !important;
      margin: 0 !important;
    }

  .chat-sideBar-name {
    padding: 10px 5px !important;
  }

  .name-meta {
    font-size: 16px;
    padding: 5% !important;
  }

  .chat-sideBar-time {
    padding: 5px !important;
  }

  .time-meta {
    text-align: right;
    font-size: 14px;
    padding: 4% !important;
    color: rgba(0, 0, 0, 0.4);
    vertical-align: baseline;
  }

  /*Conversation*/
  .conversation {
    padding: 0 !important;
    margin: 0 !important;
    height: 100%;
    /*width: 100%;*/
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    /*overflow-y: auto;*/
  }

  .message {
    height: calc(100% - 140px);
  }

  .reply {
    height: 70px;
  }

  .reply-emojis {
    padding: 5px 0 !important;
  }

    .reply-emojis i {
      padding: 5px 2px !important;
      font-size: 1.8em !important;
    }

  .reply-main {
    padding: 2px 8px !important;
  }

    .reply-main textarea {
      padding: 8px !important;
      font-size: 18px;
    }

  .reply-recording {
    padding: 5px 0 !important;
  }

    .reply-recording i {
      padding: 5px 0 !important;
      font-size: 1.8em !important;
    }

  .reply-send {
    padding: 5px 0 !important;
  }

    .reply-send i {
      padding: 5px 2px 5px 0 !important;
      font-size: 1.8em !important;
    }

  .message-main-receiver {
    max-width: 100% !important;
  }

  .message-main-sender {
    max-width: 100% !important;
    margin-left: 0px !important
  }

  .reply {
    height: 100% !important;
  }

  .empty-conversation {
    height: 100% !important;
    position: absolute;
    width: 100%;
    margin-bottom: 0px !important;
  }

  .conversation {
    /* display:none;*/
  }

  .chat-sideBar-name, .chat-sideBar-time {
    width: 50%;
  }
}

.user-icon {
  background-color: #eee;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

  .user-icon svg {
    width: 100%;
    height: 100%;
  }

#conversation {
  min-height: 480px;
  padding: 10px !important;
  overflow-y: auto;
  height: 583px;
  display: block;
  background: url(/assets/img/chat-body-bg.png) repeat 0 50%;
}

.heading,
.heading-name-meta {
  background-color: #f0f2f5 !important;
  color: #111b21 !important;
}

.heading-name-meta {
  text-decoration: none;
}

.heading-avatar-icon .user-icon {
  text-align: center;
  margin: 1px;
  width: 30px !important;
  height: 30px !important;
}

  .heading-avatar-icon .user-icon svg {
    width: 100% !important;
    height: 100% !important;
  }


.empty-conversation {
  text-align: center;
  font-size: 24px;
  background-color: #fff;
  height: 100%;
  margin: 0px !important;
}

  .empty-conversation p {
    margin-top: 30%;
  }

  .empty-conversation svg {
    font-size: 75px;
    /*fill: #187312;*/
    fill: #00a884;
  }

.message-reply-arabic {
  direction: rtl;
}

.emoji-mart {
  position: absolute;
  width: auto !important;
  bottom: 10%; /*60px*/
  left: 33%;
}

  .emoji-mart ::ng-deep.emoji-mart {
    width: 100% !important;
  }

.emoji-size {
  font-size: 30px;
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.reply-attachment {
  padding-top: 8px !important;
}

.reply-emojis-svg {
  padding-top: 7px !important;
}

.image-size {
  padding: 0 !important;
}

.message-reaction {
  position: relative;
}

  .message-reaction button {
    position: absolute;
    border-radius: 50% !important;
    background-color: #fff;
    padding: 2px !important;
    font-size: 18px;
  }

.reply-attachment .dropup .dropdown-item i {
  padding: 10px;
  color: #54656f !important;
}

.reply-attachment .dropup .dropdown-menu {
  margin-bottom: 20px !important;
}

.message-body-reaction {
  margin-bottom: 12px !important;
}

.icon-read {
  color: #53bdeb;
}

.feather-trash-2 {
  color: red;
}

.receiver audio::-webkit-media-controls-panel {
  background-color: #fff;
}

audio::-webkit-media-controls-volume-slider {
  background-color: #B1D4E0;
  border-radius: 25px;
  padding-left: 8px;
  padding-right: 8px;
}

.sender audio::-webkit-media-controls-panel {
  background-color: #d9fdd3;
}

.header-bar {
  background-color: #00a783 !important;
  min-height: 15%;
  left: 0px;
  right: 0px;
  position: absolute;
  top: 36px;
}

.doc-msg {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding-top: 12px;
  padding-bottom: 11px;
  padding-right: 5px;
  padding-left: 5px;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}

.receiver .doc-msg {
  background-color: #f5f6f6;
}

.sender .doc-msg {
  background-color: #d1f4cc;
}

.doc-msg .file-name {
  width: 100%;
  overflow: hidden;
  height: 25px;
}

.doc-msg i, .doc-msg svg {
  font-size: 26px;
  color: #93918f;
  fill: #93918f;
  padding-left: 15px;
  padding-right: 15px;
  height: 25px;
}

.doc-container {
  position: relative;
  width: 100%;
  height: 580px;
  align-items: center;
  display: flex;
  justify-content: center;
}

.doc-container-text {
  width: auto;
  align-items: center;
  display: flex;
  min-width: 300px;
  height: 100px;
  background-color: #f0f2f5;
  color: #93918f;
  overflow: hidden;
  max-width: 441px;
}

  .doc-container-text p {
    margin-top: 10px !important;
    overflow: hidden;
  }

.heading .heading-dot i {
  color: #93918f;
}

.name-meta span {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.footer-text {
  font-size: 0.8125rem;
  line-height: 17px;
  color: #8696a0;
  padding: 1px
}

.sender .img-thumbnail {
  background-color: #d9fdd3 !important;
}

.image-fixed-container {
  height: auto;
  max-height: 240px;
  min-height: 240px
}

.doc-download-icon svg:hover {
  fill: #00a783 !important;
}

.unread-counter {
  background-color: #25d366 !important;
}

.page-separator {
  width: 100%;
  height: 20px;
}

::ng-deep.emoji-mart-anchor {
  cursor: pointer;
}

.feather-trash-2 {
  color: #ff3b30 !important;
}

.heading-avatar-icon {
  display: flex;
  justify-content: center;
}
:host {
  width: 100%;
}
