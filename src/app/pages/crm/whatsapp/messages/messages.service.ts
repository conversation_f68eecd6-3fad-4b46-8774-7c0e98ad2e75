import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MessagesService {
  constructor(private http: HttpClient) {
  }

  get(): Observable<any> {
    return this.http.get<any>('/api/wbmessages');
  }

  getMessages(from: any, phoneNumberId: any, currentPage: any = 1): Observable<any> {
    return this.http.get<any>(`/api/wbmessages/${from}/messages`, {
      params: {
        phoneNumberId: phoneNumberId,
        currentPage: currentPage
      }
    });
  }

  upload(file: any, phoneNumberId: any): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>("/api/wbtemplatemessages/upload?phoneNumberId=" + phoneNumberId, fd);
  }
  send(msg: any) {
    return this.http.post<any>("/api/wbmessages", msg);
  }

  markAsRead(msgId: any, phoneNumberId: any) {
    return this.http.get<any>("/api/wbmessages/read", {
      params: {
        messageId: msgId,
        phoneNumberId: phoneNumberId
      }
    });
  }

  download(mediaId: any, phoneNumberId: any) {
    const options = {
      responseType: 'blob' as 'json', // Important: Use 'blob' as the response type
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      params: {
        phoneNumberId: phoneNumberId,
        mediaId: mediaId
      }
    };
    return this.http.get<any>("/api/wbmessages/download", options);
  }

  removeMedia(mediaId: string): Observable<any> {
    return this.http.get<any>("/api/wbmessages/removeMedia?mediaId=" + mediaId);
  }
}
