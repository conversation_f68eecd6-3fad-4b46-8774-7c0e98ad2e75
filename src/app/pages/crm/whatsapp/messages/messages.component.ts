import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ConversationModel, InputMessageModel, MessageModel } from '../AppModels';
import { SignalRConnectionService } from '../signalR-connection.service';
import moment from 'moment';
import { FormControl, Validators } from '@angular/forms';
import { EmojiService } from '@ctrl/ngx-emoji-mart/ngx-emoji'
import { Subscription } from 'rxjs';
import { DomSanitizer } from '@angular/platform-browser';
import { MessagesService } from './messages.service';
import { AudioRecordingService } from './audio-recording.service';
import { Howl } from 'howler';
import ImageViewer from 'awesome-image-viewer';

declare var bootstrap: any;
@Component({
    selector: 'app-messages',
    templateUrl: './messages.component.html',
    styleUrls: ['./messages.component.css'],
    standalone: false
})
export class MessagesComponent implements OnInit, OnDestroy {
  subscriptions = new Subscription();
  conversations: ConversationModel[] = [];
  tempConversations: ConversationModel[] = [];
  messages: any[] = [];
  current: ConversationModel | undefined;
  messageBodyCtrl = new FormControl('', { validators: [Validators.required] });
  showEmojiPicker = false;
  currentImageToUpload: any;
  currentDocToUpload: any;
  @ViewChild('conversation') conversationContainer!: ElementRef;
  _messageToSend!: InputMessageModel;
  isUploading = false;
  downloadMedia = null;
  isRecording = false;
  recordedTime: any;
  blobUrl: any;
  audioSource: any;
  hideConversation = false;
  currentPage = 1;
  pagesCount = 0;
  emojiList: any[] = [];
  isLoading: any;
  constructor(private connectionService: SignalRConnectionService,
    private cdk: ChangeDetectorRef,
    private sanitizer: DomSanitizer,
    private emojiService: EmojiService,
    private messageService: MessagesService,
    private audioRecordingService: AudioRecordingService,
    private toastr: ToastrService) {
    this.emojiList = this.emojiService.emojis.map(e => e.native?.trim());
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.abortRecording();
  }

  ngOnInit(): void {
    this.subscriptions.add(this.audioRecordingService
      .recordingFailed()
      .subscribe(() => {
        this.isRecording = false;
        this.cdk.detectChanges();
      }));
    this.subscriptions.add(this.audioRecordingService
      .getRecordedTime()
      .subscribe(time => {
        this.recordedTime = time;
        this.cdk.detectChanges();
      }));
    this.subscriptions.add(this.audioRecordingService.getRecordedBlob().subscribe(data => {
      this.audioSource = data;
    }));

    this.subscriptions.add(this.connectionService.notificationReached.subscribe((msg: MessageModel) => {
      if (msg) {
        let selectedConversation = this.conversations.find(c => c.phoneNumberId == msg.phoneNumberId && c.from == msg.from);
        if (selectedConversation) {
          if (msg.type != 'reaction' && msg.type != 'status') {
            if (!msg.isSender) {
              selectedConversation.unReadCount++;
              document.title = this.getDocumentTitle(selectedConversation.unReadCount);
            }
            this.conversations = this.conversations.filter(c => c.from != msg.from);
          }
        } else {
          const newConversation = new ConversationModel();
          newConversation.from = msg.from;
          newConversation.name = msg.name;
          newConversation.phoneNumberId == msg.phoneNumberId
          selectedConversation = newConversation;
        }
        this.cdk.detectChanges();
        switch (msg.type) {
          case 'text':
            selectedConversation.lastMsgText = msg.text;
            break;
          case 'image':
          case 'video':
          case 'audio':
          case 'document':
            selectedConversation.lastMsgText = `${msg.type} sent`;
            break;
          case 'reaction':
            selectedConversation.lastMsgText = msg.reaction;
            break;
        }
        if (msg.time) {
          selectedConversation.time = msg.time;
        }
        this.cdk.detectChanges();
        if (msg.type == 'reaction') {
          const selectedMsg = selectedConversation.messages?.find(m => m.messageId == msg.parentId);
          if (selectedMsg) {
            selectedMsg.reaction = msg.reaction;
          }
          this.cdk.detectChanges();
        } else if (msg.type == 'status') {
          const selectedMsg = selectedConversation.messages?.find(m => m.messageId == msg.messageId);
          if (selectedMsg) {
            switch (msg.status) {
              case "read":
                selectedMsg.read = true;
                selectedMsg.readTime = msg.readTime;
                break;
              case "sent":
                selectedMsg.sent = true;
                selectedMsg.sentTime = msg.sentTime;
                break;
              case "delivered":
                selectedMsg.delivered = true;
                selectedMsg.deliveredTime = msg.deliveredTime;
                break;
              case "failed":
                selectedMsg.errorMessage = msg.errorMessage;
                this.toastr.error(msg.errorMessage);
                break;
              default:
                break;
            }
            this.cdk.detectChanges();
          }
        }
        else {
          selectedConversation?.messages?.push(msg);
          this.conversations.splice(0, 0, selectedConversation);
          if (this.current?.from == selectedConversation.from) {
            //this.moveToBottom(this.conversationContainer.nativeElement);
          }
        }
        this.markVisibleMessagesAsRead();
        this.moveToBottom(this.conversationContainer.nativeElement);
        if (msg.type != 'status') {
          var sound = new Howl({
            src: ['/assets/whatsapp.mp3'],
            volume: 0.1,
          });
          sound.play();
        }
        this.cdk.detectChanges();
      }
    }));

    this.subscriptions.add(this.messageService.get().subscribe(r => {
      if (r.success) {
        r.data.forEach((msg: any) => {
          const newConversation = new ConversationModel();
          newConversation.from = msg.from;
          newConversation.name = msg.name;
          newConversation.time = msg.lastMessageTime;
          newConversation.lastMsgText = msg.lastMessageText;
          newConversation.unReadCount = msg.unReadCount;
          newConversation.phoneNumberId = msg.phoneNumberId;
          newConversation.displayPhoneNumber = msg.displayPhoneNumber;
          newConversation.isBlocked = msg.isBlocked;
          this.conversations.push(newConversation);
          document.title = this.getDocumentTitle(newConversation.unReadCount);
        });
        this.cdk.detectChanges();
      }
    }));
  }

  send() {
    this.showEmojiPicker = false
    if (this.isUploading) {
      return;
    }
    if (this.current) {
      //message to display in UI
      const newMessage = new MessageModel();
      newMessage.from = this.current.from;
      newMessage.name = this.current.name;
      newMessage.time = moment().format('yyyy-MM-DD hh:mm:ss a');
      newMessage.isSender = true
      this.current.messages?.push(newMessage);
      this.cdk.detectChanges();
      if (this.messageToSend.mediaId) {
        if (this.messageBodyCtrl.value) {
          newMessage.caption = this.messageBodyCtrl.value;
          this.messageToSend.caption = newMessage.caption;
        }
        if (this.messageToSend.type == 'image') {
          newMessage.image = `/api/wbWebhooks/Stream?mediaId=${this.messageToSend.mediaId}&contentType=${this.currentImageToUpload['type']}&phoneNumberId=${this.current.phoneNumberId}`;
        } else if (this.messageToSend.type == 'video') {
          newMessage.video = this.currentImageToUpload;
        } else if (this.messageToSend.type == 'audio') {
          newMessage.audio = `/api/wbWebhooks/Stream?mediaId=${this.messageToSend.mediaId}&contentType=audio/mp4&phoneNumberId=${this.current.phoneNumberId}`;
          newMessage.contentType = "audio/mp4";
        } else if (this.messageToSend.type == 'video') {
          this.current.lastMsgText = `${newMessage.type} sent`;
        } else if (this.messageToSend.type == 'document') {
          newMessage.docId = this.messageToSend.mediaId;
          newMessage.fileName = this.messageToSend.fileName;
          newMessage.contentType = this.messageToSend.contentType;
        }
        newMessage.type = this.messageToSend.type;
      } else if (this.messageToSend.type == 'reaction') {

      } else {
        if (this.messageBodyCtrl.value) {
          newMessage.text = this.messageBodyCtrl.value;
          newMessage.type = 'text';
          this.current.lastMsgText = newMessage.text;
          this.messageToSend.text = newMessage.text;
        }
      }
      this.messageToSend.phoneNumberId = this.current.phoneNumberId;
      this.messageToSend.type = newMessage.type;
      this.messageToSend.recipientNumber = this.current.from;
      this.messageToSend.recipientName = this.current.name;
      //this.removeUploadedImage(undefined);
      this.conversationContainer.nativeElement.style.display = 'block';
      this.currentImageToUpload = null;
      this.currentDocToUpload = null;
      this.messageBodyCtrl.reset();
      //send message to server
      this.subscriptions.add(this.messageService.send(this.messageToSend)
        .subscribe(r => {
          if (r.success) {
            newMessage.messageId = r.data.message_id;
            this.messageToSend = new InputMessageModel();
            this.cdk.detectChanges();
          }
        }));

      this.messageBodyCtrl.reset();
      this.messageBodyCtrl.updateValueAndValidity();
      this.cdk.detectChanges();
    }
    this.moveToBottom(this.conversationContainer.nativeElement);
  }

  sendIfEnter(event: any, msg: any) {
    if (msg) {
      if (event.keyCode === 13 && !event.shiftKey) {
        event.target.rows = 1;
        this.send();
      }
      if (event.keyCode === 13 && event.shiftKey) {
        if (event.target.rows < 2) {
          event.target.rows = event.target.rows + 1;
        }
      }
      // this.cdk.detectChanges();
    }
    return false;
  }

  searchList(event: any) {
    const value = event.target.value;
    if (this.tempConversations.length == 0) {
      this.tempConversations = [...this.conversations];
    }
    if (value) {
      this.conversations = this.tempConversations.filter(c => (c.name ? c.name.toLowerCase().indexOf(value.toLowerCase()) >= 0 : false) || (c.from ? c.from.indexOf(value) >= 0 : false));
    } else {
      this.conversations = [...this.tempConversations];
      this.tempConversations = [];
    }
    this.cdk.detectChanges();
  }

  isArabic(text: any) {
    const pattern = /[\u0600-\u06FF\u0750-\u077F]/;
    let isArabic = pattern.test(text);
    if (isArabic) {
      return isArabic;
    } else {
      //  isArabic = this.containsEmoji(text);
    }
    return isArabic;
  }

  formatTimeFromNow(time: any) {
    if (time) {
      return moment(new Date(time)).fromNow();
    }
    return null;
  }

  formatTime(time: any) {
    return moment(new Date(time)).format("hh:mm A");
  }

  setCurrent(item: ConversationModel) {
    this.current = item;
    if (this.current?.messages?.length == 0) {
      this.currentPage = 1;
      this.loadMessages();
    } {
      setTimeout(() => {
        this.moveToBottom(this.conversationContainer.nativeElement);
        this.cdk.detectChanges();
      }, 100);
    }
    window.scrollTo(0, window.outerHeight);
    setTimeout(() => {
      this.initializeTooltip();
      this.cdk.detectChanges();
    }, 100);
  }

  loadMessages(currentHeight: any = 0) {
    if (!this.isLoading) {
      this.isLoading = true;
      this.subscriptions.add(this.messageService.getMessages(this.current?.from, this.current?.phoneNumberId, this.currentPage)
        .subscribe((r: any) => {
          this.isLoading = false;
          if (r.success) {
            if (this.current) {
              r.data.forEach((msg: MessageModel) => {
                this.current?.messages?.splice(0, 0, msg);
              });
              if (r.pagesCount) {
                this.pagesCount = r.pagesCount;
              }
              this.cdk.detectChanges();
              window.scrollTo(0, window.innerHeight);
              if (this.currentPage == 1) {
                this.moveToBottom(this.conversationContainer.nativeElement);
              } else {
                const element = this.conversationContainer.nativeElement;
                element.scrollTo(0, currentHeight);
              }
              setTimeout(() => {
                this.markVisibleMessagesAsRead();
              }, 300);
            }
          }
        }));
    }
  }

  toggleEmojiPicker() {
    this.showEmojiPicker = !this.showEmojiPicker;
    this.cdk.detectChanges();
  }

  addEmoji(event: any, element: HTMLTextAreaElement) {
    const message = this.messageBodyCtrl.value ? this.messageBodyCtrl.value : '';
    const text = `${message}${event.emoji.native}`;
    this.messageBodyCtrl.setValue(text);
    element.focus();
    this.cdk.detectChanges();
  }
  get hasValue(): boolean {
    return (this.messageBodyCtrl.value ? this.messageBodyCtrl.value.length > 0 : false) || this.currentImageToUpload || this.currentDocToUpload;
  }

  //containsEmoji2(value: any) {
  //  return value ? this.emojiList.findIndex((e: any) => e.localeCompare(value.trim()) == 0) >= 0 : false;
  //}

  containsEmoji(text: any): boolean {
    const emojis = this.emojiList;
    if (!text) {
      return false;
    }
    const result = this.linearSearch(emojis, text.trim());
    return result > -1;
  }

  linearSearch(arr: any[], target: any): number {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] === target) {
        return i; // Found at index i
      }
    }
    return -1; // Not found
  }


  moveToBottom(element: HTMLDivElement) {
    element.scrollTo(0, element.scrollHeight);
    this.cdk.detectChanges();
  }

  formatText(text: any) {
    if (text) {
      const regex = new RegExp(/(http|ftp|https):\/\/([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:\/~+#-]*[\w@?^=%&\/~+#-])/g);
      const links = text.match(regex);
      if (links) {
        for (var i = 0; i < links.length; i++) {
          const l = links[i];
          const a = `<a target='_blank' href='${l}'>${l}</a>`
          text = text.replace(l, a);
        }
      }
      text = text.replace('\n', '<br/>');
      return this.sanitizer.bypassSecurityTrustHtml(text);
    }
    return null;
  }
  get messageToSend() {
    if (!this._messageToSend) {
      return this._messageToSend = new InputMessageModel();
    }
    return this._messageToSend;
  }
  set messageToSend(value: InputMessageModel) {
    this._messageToSend = value;
  }

  uploadImageOrVideo(image: HTMLInputElement, imgDist: HTMLImageElement) {
    if (image) {
      image.click();
      image.onchange = (event) => {
        if (image.files) {
          imgDist.style.visibility = 'hidden';
          this.currentImageToUpload = image.files[0];
          this.uploadMedia(this.currentImageToUpload, imgDist);
        }
      }
    }
  }

  uploadMedia(file: any, dist: any) {
    this.isUploading = true;
    const phoneNumberId = this.current?.phoneNumberId;
    this.subscriptions.add(this.messageService.upload(file, phoneNumberId)
      .subscribe(r => {
        this.isUploading = false;
        if (r.success) {
          this.messageToSend.mediaId = r.data.uploadedMediaId;
          this.messageToSend.contentType = file.type;
          if (file.type.indexOf('image') >= 0 && this.messageToSend.type != 'document') {
            this.messageToSend.type = "image";
            dist.src = URL.createObjectURL(file);
          } else if (file.type.indexOf('video') >= 0 && this.messageToSend.type != 'document') {
            this.messageToSend.type = "video";
          } else if (file.type.indexOf('audio') >= 0 && this.messageToSend.type != 'document') {
            this.messageToSend.type = "audio";
            this.send();
          } else if (this.messageToSend.type == 'document') {
            if (dist) {
              dist.style.color = 'black !important';
              this.cdk.detectChanges();
            }
          }
          if (dist) {
            dist.style.visibility = 'visible';
          }
        }
        this.cdk.detectChanges();
      }));
    if (file.type.indexOf('audio') < 0) {
      this.conversationContainer.nativeElement.style.display = 'none';
    }
    this.cdk.detectChanges();
  }

  uploadDocument(file: HTMLInputElement, dist: HTMLElement) {
    if (file) {
      file.click();
      file.onchange = (event) => {
        if (file.files) {
          dist.style.visibility = 'hidden';
          this.currentDocToUpload = file.files[0];
          this.cdk.detectChanges();
          this.messageToSend.type = 'document';
          this.messageToSend.contentType = this.currentDocToUpload.type;
          this.messageToSend.fileName = this.currentDocToUpload.name;
          this.uploadMedia(this.currentDocToUpload, dist);
        }
      }
    }
  }

  removeUploadedImage(imgDist: HTMLImageElement | undefined) {
    this.conversationContainer.nativeElement.style.display = 'block';
    this.currentImageToUpload = null;
    this.currentDocToUpload = null;
    this.messageBodyCtrl.reset();
    if (imgDist) {
      imgDist.src = '';
    }
    if (this.messageToSend.mediaId) {
      this.subscriptions.add(this.messageService.removeMedia(this.messageToSend.mediaId).subscribe());
    }
    //this.messageToSend = new InputMessageModel();
    this.cdk.detectChanges();
  }
  removeUploadedDoc(fileDist: any) {
    this.removeUploadedImage(undefined);
  }

  markVisibleMessagesAsRead() {
    const container = this.conversationContainer.nativeElement;
    const messageDivs = Array.from(container.getElementsByClassName('message-main-receiver') as HTMLCollectionOf<HTMLElement>);
    for (var i = 0; i < messageDivs.length; i++) {
      let messageDiv = messageDivs[i];
      if (this.isElementInViewport(messageDiv) && !messageDiv.classList.contains('read')) {
        this.subscriptions.add(this.messageService.markAsRead(messageDiv.id, this.current?.phoneNumberId).subscribe(r => {
          if (r.success) {
            messageDiv.classList.add('read');
            if (this.current) {
              if (this.current.unReadCount > 0) {
                this.current.unReadCount--;
                document.title = this.getDocumentTitle(this.current.unReadCount);
                this.cdk.detectChanges();
              }
            }
          }
        }));
      }
    }

    const page_separator = container.getElementsByClassName('page-separator')[0] as HTMLDivElement;
    if (page_separator) {
      if (this.isElementInViewport(page_separator)) {
        if (this.current && !this.isLoading && this.currentPage < this.pagesCount) {
          this.currentPage++;
          const messageBodies = Array.from(container.getElementsByClassName('message-body') as HTMLCollectionOf<HTMLElement>);
          const currentHeight = messageBodies[0].getBoundingClientRect().top;
          this.loadMessages(currentHeight);
        }
      }
    }
  }

  getDocumentTitle(unReadCount: any): string {
    return unReadCount > 0 ? `(${unReadCount}) ` + 'Whatsapp' : 'Whatsapp';
  }

  isElementInViewport(element: HTMLElement) {
    const rect = element.getBoundingClientRect();
    const containerRect = this.conversationContainer.nativeElement.getBoundingClientRect();
    return (
      rect.top >= containerRect.top &&
      rect.bottom <= containerRect.bottom &&
      rect.left >= containerRect.left &&
      rect.right <= containerRect.right
    );
  }

  startRecording() {
    if (!this.isRecording) {
      this.isRecording = true;
      this.audioRecordingService.startRecording();
      this.messageToSend.type = 'audio';
      this.cdk.detectChanges();
    }
  }

  abortRecording() {
    if (this.isRecording) {
      this.isRecording = false;
      this.audioRecordingService.abortRecording();
      this.cdk.detectChanges();
    }
  }

  stopRecording() {
    if (this.isRecording) {
      this.audioRecordingService.stopRecording();
      this.isRecording = false;
      this.cdk.detectChanges();
    }
  }

  clearRecordedData() {
    this.blobUrl = null;
    this.cdk.detectChanges();
  }
  sendAudio() {
    this.stopRecording();
    this.cdk.detectChanges();
    setTimeout(async () => {
      this.uploadMedia(this.audioSource.blob, null);
    }, 100);
  }

  download(mediaId: any, fileName: any): void {
    this.downloadMedia = mediaId;
    this.subscriptions.add(this.messageService.download(mediaId, this.current?.phoneNumberId).subscribe(r => {
      const url = window.URL.createObjectURL(r);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      this.downloadMedia = null;
      this.cdk.detectChanges();
    }));
  }

  showContacts() {
    this.current = undefined;
    //this.hideConversation = true;
    window.scrollTo(0, 0);
  }

  previewImage(img: any) {
    new ImageViewer({
      images: [{
        mainUrl: img,
        description: '',
        thumbnailUrl: img
      }],
      isZoomable: false,
      stretchImages: true
    });
  }

  initializeTooltip() {
    const tooltipTriggerList = Array.from(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    tooltipTriggerList.forEach((tooltipTriggerEl) => {
      new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
}
