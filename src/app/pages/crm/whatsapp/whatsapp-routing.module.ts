import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import {WhatsappInstanceComponent} from "./whatsapp-instance/whatsapp-instance.component";
import {WebhooksModule} from "./webhooks/webhooks.module";
import {TemplatesModule} from "./templates/templates.module";
import {MessagesModule} from "./messages/messages.module";
import {SettingsModule} from "./settings/settings.module";
import {ProfileModule} from "./profile/profile.module";
import {ContactsModule} from "./contacts/contacts.module";
import {AutoRepliesModule} from "./auto-replies/auto-replies.module";
import {CampaignsModule} from "./campaigns/campaigns.module";
import {QrCodesModule} from "./qr-codes/qr-codes.module";
import {AnalyticsModule} from "./analytics/analytics.module";
import {SallaModule} from "./salla/salla.module";
import {ZidModule} from "./zid/zid.module";

const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: 'home',
    component: HomeComponent,
  },
  {
    path: 'instance',
    component: WhatsappInstanceComponent,
  },
  {
    path: 'webhooks',
    loadChildren: () => WebhooksModule,
  },
  {
    path: 'templates',
    loadChildren: () => TemplatesModule,
  },
  {
    path: 'messages',
    loadChildren: () => MessagesModule,
  },
  {
    path: 'settings',
    loadChildren: () => SettingsModule,
  },
  {
    path: 'profile',
    loadChildren: () => ProfileModule,
  },
  {
    path: 'contacts',
    loadChildren: () => ContactsModule,
  },
  {
    path: 'autoreplies',
    loadChildren: () => AutoRepliesModule
  },
  {
    path: 'campaigns',
    loadChildren: () => CampaignsModule
  },
  {
    path: 'qrcodes',
    loadChildren: () => QrCodesModule,
  },
  {
    path: 'analytics',
    loadChildren: () => AnalyticsModule,
  },
  {
    path: 'salla',
    loadChildren: () => SallaModule,
  },
  {
    path: 'zid',
    loadChildren: () => ZidModule,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class WhatsappRoutingModule {}
