export class MessageModel {
  name: string | undefined | null;
  from!: string;
  type!: string;
  time: string | undefined | null
  messageId: string | undefined | null;
  parentId: string | undefined | null;
  image!: any;
  video!: any;
  audio!: any;
  text?: string;
  reaction: string | undefined | null;
  status: string | undefined | null;
  sticker: string | undefined | null;
  location: string | undefined | null;
  isSender: boolean | undefined | null;
  contentType: string | undefined | null;
  caption: string | undefined | null;
  read!: boolean;
  delivered!: boolean;
  sent!: boolean;
  readTime!: string;
  sentTime!: string;
  deliveredTime!: string;
  fileName!: string;
  docId!: string;
  footerText!: string;
  headerText!: string;
  errorMessage!: string;
  phoneNumberId!: any;
  displayPhoneNumber!: any;
}

export class ConversationModel {
  phoneNumberId: string | undefined | null;
  displayPhoneNumber: string | undefined | null;
  from!: string;
  name: string | undefined | null;
  messages: MessageModel[] | undefined | null = [];
  time: string | undefined | null;
  lastMsgText: string | undefined | null;
  lastMsgRead!: boolean;
  unReadCount!: any;
  showMobile!: boolean;
  isBlocked!: boolean;
}


export class InputMessageModel {
  type!: string;
  mediaId!: string;
  text!: string;
  emoji!: string;
  caption!: string;
  parentId!: string;
  fileName!: string;
  messageId!: string;
  recipientNumber!: string;
  recipientName: string | null | undefined;
  contentType!: string | null | undefined;
  phoneNumberId: any;
}
