<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Zid Store</strong>
    </div>
    <div class="row">
      <div class="col-3">
        <div class="list-group">
          <button type="button" class="list-group-item list-group-item-action"
            [ngClass]="{active: current == 'settings' }" (click)="loadSettings()">
            Integration
            <!--<i class="fa fa-rotate-left float-end" *ngIf="!isLoading && current == 'settings'"></i>-->
            <div style="width:20px;height:20px;" class="spinner-border text-light float-end" role="status"
              *ngIf="isLoading && current == 'settings'">
              <span class="visually-hidden">Loading...</span>
            </div>
          </button>
          <button type="button" class="list-group-item list-group-item-action"
            [ngClass]="{active:current == 'register'}" (click)="showRegister()">
            Register Webhook
            <div style="width:20px;height:20px;" class="spinner-border text-light float-end" role="status"
              *ngIf="isLoading && current == 'register'">
              <span class="visually-hidden">Loading...</span>
            </div>
          </button>

          <button type="button" class="list-group-item list-group-item-action"
            [ngClass]="{active:current == 'webhooks'}" (click)="loadWebhooks()">
            Webhooks
            <i class="fa fa-rotate-left float-end" *ngIf="!isLoading && current == 'webhooks'"></i>
            <div style="width:20px;height:20px;" class="spinner-border text-light float-end" role="status"
              *ngIf="isLoading && current == 'webhooks'">
              <span class="visually-hidden">Loading...</span>
            </div>
          </button>

        </div>
      </div>

      <div class="col-9" *ngIf="current == 'settings'">
        <ng-container *ngIf="!isIntegrated">
          Click this button to login with Zid :
          <a class="btn btn-light" href="/api/wbzid/login">Integrate With Zid</a>
        </ng-container>
        <ng-container *ngIf="isIntegrated">
          <p>
            You are already
            <span class="badge bg-light text-dark">integrated</span>
            with Zid
          </p>
        </ng-container>
      </div>
      <div class="col-9 row" *ngIf="current == 'register'">
        <div class="col-6">
          <form [formGroup]="registerForm">
            <div class="col-12">
              <label class="form-label  py-0 my-0">
                Event
              </label>
              <ng-select [searchFn]="searchEvent" #eventsContainer [items]="events" formControlName="event"
                bindLabel="label" bindValue="event">
                <ng-template ng-option-tmp let-item="item" let-index="index">
                  <span [appBootstrapTooltip]="item.event" [title]="item.event"
                    data-bs-trigger="hover">{{item.label}}</span>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12">
              <label class="form-label  py-0 my-0">
                Order Status
              </label>
              <!--<input class="form-control"  />-->
              <ng-select formControlName="rule"
                [items]="['none','new', 'preparing', 'ready', 'indelivery', 'delivered', 'cancelled']"></ng-select>
            </div>
            <div class="col-12">
              <label class="form-label  py-0 my-0">
                Template Name
              </label>
              <ng-select class="pb-3" formControlName="template" [items]="templates" bindLabel="name"
                bindValue="name"></ng-select>
            </div>
            <div class="mb-2 col-12" *ngIf="item?.headerHasVariables">
              <label for="headerVar" class="form-label w-100  py-0 my-0">
                Header Variable
              </label>
              <ng-select formControlName="headerVariableValue" [items]="columns" bindLabel="name" bindValue="value"
                class="pb-3"></ng-select>
            </div>
            <div class="mb-2 col-12" *ngIf="item?.bodyHasVariables">
              <label for="recipientNumber" class="form-label w-100">
                Body Variables
              </label>
              <table class="table table-hover">
                <thead>
                  <tr>
                    <td>Variable</td>
                    <td>
                      Value
                    </td>
                  </tr>
                </thead>
                <tbody formArrayName="bodyVariables">
                  <tr *ngFor="let v of bodyVars.controls;let i = index" [formGroupName]="i">
                    <td width="10%">{{item.bodyVariables[i]}}</td>
                    <td width="80%">
                      <select formControlName="value" id="{{i}}" class="form-select">
                        <option [value]="col.value" *ngFor="let col of columns">{{col.name}}</option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="mb-2 col-12">
              <label for="templateName" class="form-label mb-0">
                Sender
              </label>
              <ng-select formControlName="senderNumber" [items]="phoneNumbers" bindLabel="verified_name"
                bindValue="display_phone_number" class="pb-3"></ng-select>
            </div>
            <div class="col-12">
              <button type="button" class="btn btn-light" (click)="register(registerForm.value)">Register</button>
            </div>
          </form>
        </div>
        <div class="col-xl-6 col-sm-12">
          <div class="col-12 alert alert-dark mb-2 py-1" role="alert" *ngIf="item">
            <strong for="templateHeader" class="form-label ">
              Header <span *ngIf="item.headerHasMedia">(Type : {{ item?.headerType?.toLowerCase()}} ) </span>
            </strong>
            <p *ngIf="!item.header && !item.headerHasMedia"> NONE </p>
            <p *ngIf="!item.headerHasMedia">{{item.header}}</p>
            <input (change)="upload($event)" *ngIf="item?.headerHasMedia && registerForm.get('senderNumber')?.value"
              type="file" class="form-control" />
            <p *ngIf="item?.headerHasMedia && !registerForm.get('senderNumber')?.value">Select Sender Number to Upload
              {{item?.headerType?.toLowerCase()}}</p>
          </div>
          <div class="col-12 alert alert-dark mb-2 py-1" role="alert" *ngIf="item">
            <strong for="templateBody" class="form-label">
              Body
            </strong>
            <p *ngIf="item">{{item.body}}</p>
          </div>
          <div class="col-12 alert alert-dark mb-2 py-1" role="alert" *ngIf="item">
            <strong for="templateFooter" class="form-label ">
              Footer
            </strong>
            <p *ngIf="item">{{item.footer}}</p>
          </div>
        </div>
        <div class="row mt-3">
          <div class="badge bg-light text-dark col-1">Note :</div>
          <div class="col-11">When The Webhook arrived we will send selected template to customer</div>
        </div>
      </div>
      <div class="col-9 " *ngIf="current == 'webhooks'">
        <div>
          <strong *ngIf="webhooks.length > 0">Zid Integration: <span
              class="badge text-bg-light">Completed</span></strong>
          <hr />
          <h6>List Of Webhooks</h6>
          <div class="row">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Event</th>
                    <th>Status</th>
                    <th>Template</th>
                    <th>Sender</th>
                    <th>Activate</th>
                    <th>Delete</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of webhooks">
                    <td>{{item.name}}</td>
                    <td>{{item.event}}</td>
                    <td>{{item.rule}}</td>
                    <td>{{item.template}}</td>
                    <td>{{item.senderNumber}}</td>
                    <td>
                      <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked"
                          [checked]="item.isActive" [value]="true" (change)="activate(item.id)">
                      </div>
                    </td>
                    <td class="text-center">
                      <i class="fa fa-trash pointer text-danger" (click)="tobeDelete(item)"
                        *ngIf="!item.toBeDelete"></i>
                      <i class="fa fa-times pointer text-dark px-2" (click)="item.toBeDelete = false"
                        *ngIf="item.toBeDelete"></i>
                      <i class="fa fa-check pointer text-success" (click)="remove(item)" *ngIf="item.toBeDelete"></i>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>