import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ZidRoutingModule } from './zid-routing.module';
import { ZidComponent } from './zid.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { BootstrapTooltipDirective } from './BootstrapTooltipDirective';

@NgModule({
  declarations: [
    ZidComponent,
    BootstrapTooltipDirective
  ],
  imports: [
    CommonModule,
    ZidRoutingModule,
    ReactiveFormsModule,
    NgSelectModule
  ]
})
export class ZidModule { }


