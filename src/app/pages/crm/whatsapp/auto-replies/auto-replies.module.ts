import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AutoRepliesRoutingModule } from './auto-replies-routing.module';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { AutoReplyListComponent } from './auto-reply-list/auto-reply-list.component';
import { AutoReplyCreateComponent } from './auto-reply-create/auto-reply-create.component';
import { AutoReplyEditComponent } from './auto-reply-edit/auto-reply-edit.component';


@NgModule({
  declarations: [

    AutoReplyListComponent,
    AutoReplyCreateComponent,
    AutoReplyEditComponent
  ],
  imports: [
    CommonModule,
    AutoRepliesRoutingModule,
    ReactiveFormsModule,
    NgSelectModule
  ]
})
export class AutoRepliesModule { }
