import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AutoRepliesService {

  constructor(private http: HttpClient) {
  }

  get(currentPage: number = 1): Observable<any> {
    return this.http.get<any>('/api/wbautoReplies/', {
      params: {
        'currentPage': currentPage
      }
    });
  }

  find(id: any) {
    return this.http.get<any>('/api/wbautoReplies/' + id);
  }

  create(contact: any): Observable<any> {
    return this.http.post<any>("/api/wbautoReplies", contact);
  }

  update(id: any, contact: any): Observable<any> {
    return this.http.put<any>("/api/wbautoReplies/" + id, contact);
  }

  delete(id: any): Observable<any> {
    return this.http.delete<any>("/api/wbautoReplies/" + id);
  }
}
