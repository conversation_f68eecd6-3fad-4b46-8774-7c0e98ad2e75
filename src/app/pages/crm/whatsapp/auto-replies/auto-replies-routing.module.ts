import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AutoReplyCreateComponent } from './auto-reply-create/auto-reply-create.component';
import { AutoReplyEditComponent } from './auto-reply-edit/auto-reply-edit.component';
import { AutoReplyListComponent } from './auto-reply-list/auto-reply-list.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    component: AutoReplyListComponent
  },
  {
    path: 'create',
    component: AutoReplyCreateComponent
  },
  {
    path: 'edit/:id',
    component: AutoReplyEditComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AutoRepliesRoutingModule { }
