import { ChangeDetectorRef, Component, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { AutoRepliesService } from '../auto-replies.service';

declare var bootstrap: any;
@Component({
    selector: 'app-auto-reply-list',
    templateUrl: './auto-reply-list.component.html',
    styleUrls: ['./auto-reply-list.component.css'],
    standalone: false
})
export class AutoReplyListComponent implements OnInit, OnDestroy {
  data: any[] = [];
  subscriptions = new Subscription();
  @ViewChild('repliesContainer') repliesContainer!: ElementRef;
  constructor(private autoReplyService: AutoRepliesService, private cdk: ChangeDetectorRef) {
  }


  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.autoReplyService.get()
      .subscribe(r => {
        if (r.success) {
          this.data = r.data;
          this.cdk.detectChanges();
          setTimeout(() => {
            this.initializeTooltip();
          }, 100);
        }
      }));
  }
  initializeTooltip() {
    const tooltipTriggerList = Array.from(
      this.repliesContainer.nativeElement.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    tooltipTriggerList.forEach((tooltipTriggerEl) => {
      new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
}
