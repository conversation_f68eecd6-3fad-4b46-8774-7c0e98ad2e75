<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Auto Reply List</strong>
      <a [routerLink]="['/crm/whatsapp/autoreplies/create']" class="float-end">Create New</a>
    </div>
    <div class="table-responsive">
      <table class="table table-hover" #repliesContainer>
        <thead>
          <tr>
            <th>Message</th>
            <th>Keywords</th>
            <th>Always</th>
            <th>From</th>
            <th>To</th>
            <th>Edit</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of data">
            <td>
              <a [routerLink]="[]"
                 data-bs-toggle="tooltip"
                 data-bs-placement="right"
                 [attr.data-bs-title]="item.messageText"
                 data-bs-trigger="hover">Message</a>
            </td>
            <td>
              <span *ngFor="let k of item.keywords" class="badge bg-primary px-2 mx-1">{{k}}</span>
            </td>
            <td>{{item.runAlways ? "Yes" : "No"}}</td>
            <td>{{item.fromTime}}</td>
            <td>{{item.toTime}}</td>
            <td>
              <i [routerLink]="['/crm/whatsapp/autoreplies/edit/', item.id]" class="fa fa-edit pointer"></i>
              <!--|
              <i class="fa fa-trash pointer"></i>-->
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
