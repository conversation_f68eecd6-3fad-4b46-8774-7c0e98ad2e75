import { Component, OnDestroy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AutoRepliesService } from '../auto-replies.service';

@Component({
    selector: 'app-auto-reply-edit',
    templateUrl: './auto-reply-edit.component.html',
    styleUrls: ['./auto-reply-edit.component.css'],
    standalone: false
})
export class AutoReplyEditComponent implements OnInit, OnDestroy {
  updateForm!: FormGroup;
  isLoading: boolean = false;
  subscriptios = new Subscription();
  constructor(private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private autoRepleyService: AutoRepliesService) {

  }
  ngOnDestroy(): void {
    this.subscriptios.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptios.add(this.route.paramMap.subscribe(r => {
      const id = r.get('id');
      this.autoRepleyService.find(id)
        .subscribe(r => {
          if (r.success) {
            this.initForm(r.data);
          }
        });
    }));
  }

  initForm(model: any) {
    this.updateForm = this.fb.group({
      id: model.id,
      messageText: [model.messageText, [Validators.required]],
      keywords: [model.keywords, [Validators.required]],
      runAlways: model.runAlways,
      fromTime: model.fromTime,
      toTime: model.toTime
    });
  }

  save(form: any) {
    if (this.updateForm.valid) {
      //form.keywords = form.keywords.map((k: any) => k.label);
      console.log(form.keywords);
      this.subscriptios.add(this.autoRepleyService.update(form.id, form)
        .subscribe(r => {
          if (r.success) {
            this.router.navigate(['/whatsapp/autoreplies/list']);
          }
        }));
    } else {
      this.updateForm.markAllAsTouched();
    }
  }
}
