import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AutoRepliesService } from '../auto-replies.service';

@Component({
    selector: 'app-auto-reply-create',
    templateUrl: './auto-reply-create.component.html',
    styleUrls: ['./auto-reply-create.component.css'],
    standalone: false
})
export class AutoReplyCreateComponent implements OnInit, OnDestroy {
  createForm!: FormGroup;
  isLoading: boolean = false;
  subscriptios = new Subscription();
  constructor(private fb: FormBuilder,
    private router: Router,
    private autoRepleyService: AutoRepliesService) {
    this.initForm();
  }
  ngOnDestroy(): void {
    this.subscriptios.unsubscribe();
  }
  ngOnInit(): void {

  }

  initForm() {
    this.createForm = this.fb.group({
      messageText: [null, [Validators.required]],
      keywords: [null, [Validators.required]],
      runAlways: true,
      fromTime: "00:00:00",
      toTime: "00:00:00"
    });
  }

  save(form: any) {
    if (this.createForm.valid) {
      form.keywords = form.keywords.map((k: any) => k.label);
      this.subscriptios.add(this.autoRepleyService.create(form)
        .subscribe(r => {
          if (r.success) {
            this.router.navigate(['/whatsapp/autoreplies/list']);
          }
        }));
    } else {
      this.createForm.markAllAsTouched();
    }
  }
}
