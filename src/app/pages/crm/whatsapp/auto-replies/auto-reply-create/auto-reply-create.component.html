<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Create Auto Reply!</strong>
      <a [routerLink]="['/crm/whatsapp/autoreplies/list']" class="float-end">Back to List</a>
    </div>
    <form [formGroup]="createForm">
      <div class="row">
        <div class="col-xl-12  col-sm-12">
          <div class="col">
            <label class="form-label">
              keywords
            </label>
            <ng-select notFoundText="Write keyword and press enter" formControlName="keywords" [searchable]="true" [hideSelected]="true" [addTag]="true" [multiple]="true" addTagText="Write and press enter"></ng-select>
          </div>
          <div class="col">
            <label class="form-label">
              Message Text
            </label>
            <textarea class="form-control" rows="5" formControlName="messageText"></textarea>
          </div>
          <div class="col py-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked" checked [value]="true" formControlName="runAlways">
              <label class="form-check-label" for="flexSwitchCheckChecked">Run Alway </label>
            </div>
          </div>
          <div class="row" *ngIf="!createForm.get('runAlways')?.value">
            <div class="col-6">
              <label class="form-label">
                From Tim
              </label>
              <input type="time" class="form-control h-50" formControlName="fromTime" />
            </div>
            <div class="col-6">
              <label class="form-label">
                To Time
              </label>
              <input type="time" class="form-control h-50" formControlName="toTime" />
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="mb-3 col-12">
          <button [disabled]="isLoading" class="btn btn-light" type="submit" (click)="save(createForm.value)">
            Save Changes
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
