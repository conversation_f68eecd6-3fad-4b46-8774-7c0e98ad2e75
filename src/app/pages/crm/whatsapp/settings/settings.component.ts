import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { finalize, Subscription } from 'rxjs';

@Component({
    selector: 'app-settings',
    templateUrl: './settings.component.html',
    styleUrls: ['./settings.component.css'],
    standalone: false
})
export class SettingsComponent implements OnInit, OnDestroy {
  settingsForm!: FormGroup;
  zidSettingsForm!: FormGroup;
  sallaSettingsForm!: FormGroup;
  isLoading: boolean = false;
  subscriptions = new Subscription();
  constructor(private fb: FormBuilder,
    private http: HttpClient,
    private cdk: ChangeDetectorRef,
    private toastr: ToastrService) {
    this.initForm(null);
    this.initSallaForm(null);
    this.initZidForm(null);
  }
  initForm(model: any) {
    this.settingsForm = this.fb.group({
      apiUrl: [model?.apiUrl, [Validators.required]], //'https://graph.facebook.com/v17.0/'
      appId: [model?.appId, [Validators.required]],
      appSecret: [model?.appSecret, [Validators.required]],
      accessToken: [model?.accessToken, [Validators.required]],
      businessAccountId: [model?.businessAccountId, [Validators.required]]
    });
  }

  initZidForm(model: any) {
    this.zidSettingsForm = this.fb.group({
      clientId: [model?.clientId, [Validators.required]],
      clientSecret: [model?.clientSecret, [Validators.required]]
    });
  }
  initSallaForm(model: any) {
    this.sallaSettingsForm = this.fb.group({
      clientId: [model?.clientId, [Validators.required]],
      clientSecret: [model?.clientSecret, [Validators.required]]
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.subscriptions.add(this.http.get<any>('/api/wbsettings')
      .subscribe(r => {
        if (r.success) {
          this.initForm(r.data);
          this.cdk.detectChanges();
        }
      }));
    this.subscriptions.add(this.http.get<any>('/api/wbsalla/settings')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(r => {
        if (r.success) {
          const d = r.data;
          this.initSallaForm(d.settings);
          this.cdk.detectChanges();
        }
      }));
    this.subscriptions.add(this.http.get<any>('/api/wbzid/settings')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(r => {
        if (r.success) {
          const d = r.data;
          this.initZidForm(d.settings);
          this.cdk.detectChanges();
        }
      }));
  }


  save(form: any) {
    if (this.settingsForm?.valid) {
      this.isLoading = true;
      this.subscriptions.add(this.http.post<any>('/api/wbsettings', form)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.toastr.success(r.message);
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.settingsForm?.markAllAsTouched();
    }
  }


  clearCache() {
    this.isLoading = true;
    this.subscriptions.add(this.http.put<any>('/api/wbsettings', null)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(r => {
        if (r.success) {
          this.toastr.success(r.message);
        } else {
          this.toastr.error(r.message);
        }
      }));
  }
  saveSalla(form: any) {
    if (this.sallaSettingsForm?.valid) {
      this.isLoading = true;
      this.subscriptions.add(this.http.post<any>('/api/wbsalla/settings', form)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            //this.isSaved = true;
            this.toastr.success(r.message);
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.sallaSettingsForm?.markAllAsTouched();
    }
  }
  saveZid(form: any) {
    if (this.zidSettingsForm?.valid) {
      this.isLoading = true;
      this.subscriptions.add(this.http.post<any>('/api/wbzid/settings', form)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            /*this.isSaved = true;*/
            this.toastr.success(r.message);
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.zidSettingsForm?.markAllAsTouched();
    }
  }

}
