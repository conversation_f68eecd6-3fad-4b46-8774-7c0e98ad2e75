<div class="row">
  <div class="col-12 text-center" *ngIf="isPreviewing">
    <div class="spinner-border text-success" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <div class="col-xl-12 col-sm-12 mb-2" *ngIf="showExcelPreview">
    <div class="card">
      <div class="card-body">
        <div class="card-title">
          <strong>Preview Excel</strong>
          <a class="float-end pointer mx-2" style="text-decoration:none" (click)="toggleExcelPreview()">Close</a>
          <ng-container *ngIf="excelData.length > pageSize">
            <a class="float-end pointer mx-2" style="text-decoration:none" (click)="prevPreview()">Prev</a>
            <a class="float-end pointer mx-2" style="text-decoration:none" (click)="nextPreview()">Next</a>
          </ng-container>
        </div>
        <div class="table-responsive imported-preview">
          <table class="table table-hover">
            <thead>
              <tr>
                <th *ngFor="let col of columns">
                  {{col}}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of pagedExcelData; let last = last">
                <td *ngFor="let col of columns; let i = index">{{row[i]}}</td>
                <ng-container *ngIf="last">
                  {{stopPreviewLoading(last)}}
                </ng-container>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-8 col-sm-12 mb-2">
    <div class="card" style="height: 100%">
      <div class="card-body">
        <div class="card-title">
          <strong>Import Contacts</strong>
        </div>
        <form [formGroup]="importForm">
          <div class="row">
            <div class="col-12 text-center">
              <div class="spinner-border text-success" role="status" *ngIf="isReading" style="position: absolute; top: 50%; right: 50%;">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <div class="" [ngClass]="{'col-xl-12 col-sm-12': excelData.length == 0 ,'col-xl-8 col-sm-8' : excelData.length > 1}">
              <label class="form-label">
                Select Excel File
              </label>
              <input type="file" class="form-control h-50" (change)="onFileChange($event)" />
            </div>
            <div class="col-xl-4 col-sm-4 mt-4 mb-1" *ngIf="excelData.length > 1" style="align-items: center; display: flex;">
              <a [routerLink]="[]" class="pointer" (click)="toggleExcelPreview()">
                {{ showExcelPreview ? 'Hide ':'Preview ' }} Excel Data ({{ (excelData.length  - 1 )+ ' Rows'}})
              </a>
            </div>
            <div class="col-xl-6 col-sm-12">
              <label class="form-label">
                Name Column
              </label>
              <!--<select class="form-select h-50" formControlName="nameColumn">
                <option [selected]="col.toLowerCase().indexOf('name') >= 0 ? true : false" [value]="col" *ngFor="let col of columns">
                  {{ col }}
                </option>
              </select>-->
              <ng-select formControlName="nameColumn" [items]="columns"></ng-select>
            </div>
            <div class="col-xl-6 col-sm-12">
              <label class="form-label">
                Mobile Column
              </label>
              <!--<select class="form-select h-50" formControlName="mobileColumn">
                <option [selected]="col.toLowerCase().indexOf('phone') >= 0 || col.toLowerCase().indexOf('mobile') >= 0 " [value]="col" *ngFor="let col of columns">
                  {{ col }}
                </option>
              </select>-->
              <ng-select formControlName="mobileColumn" [items]="columns"></ng-select>
            </div>
            <div class="col-xl-12 col-sm-12">
              <label class="form-label">
                <input type="radio" name="isNewGroup" [value]="true" formControlName="isNewGroup" />
                New Group
              </label>
              <label class="form-label mx-3">
                <input type="radio" name="isNewGroup" [value]="false" formControlName="isNewGroup" />
                Existing Group
              </label>
            </div>
            <div class="col-xl-12 col-sm-12">
              <label class="form-label">
                Group Name
              </label>
              <input class="form-control" placeholder="Group Name" formControlName="groupName" *ngIf="importForm.get('isNewGroup')?.value" />
              <!--<select class="form-select h-50" formControlName="groupId" *ngIf="!importForm.get('isNewGroup')?.value">
                <option [value]="g.id" *ngFor="let g of groups">
                  {{g.name}}
                </option>
              </select>-->
              <ng-select formControlName="groupId" [items]="groups" bindLabel="name"
                         bindValue="id" *ngIf="!importForm.get('isNewGroup')?.value"></ng-select>
            </div>
            <div class="mt-3 col-xl-12 col-sm-12" style="align-items: center; display: flex;">
              <button [disabled]="isLoading" class="btn btn-light" type="submit" (click)="importExcel(importForm.value)">
                Import Data
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div class="col-xl-4  col-sm-12  mb-2">
    <div class="card" style="height: 100%">
      <div class="card-body">
        <div class="card-title">
          <strong>New Contact</strong>
        </div>
        <div class="row">
          <form [formGroup]="contactsForm">
            <div class="row">
              <div class="col-xl-12 col-sm-12 row">
                <div class="col-xl-12 col-sm-12">
                  <label class="form-label">
                    Name
                  </label>
                  <input class="form-control h-50" formControlName="name" />
                </div>
                <div class="col-xl-12 col-sm-12">
                  <label class="form-label">
                    Mobile
                  </label>
                  <input class="form-control h-50" formControlName="mobile" />
                </div>
                <div class="col-xl-12 col-sm-12">
                  <label class="form-label">
                    <input type="radio" name="isNewGroup" [value]="true" formControlName="isNewGroup" />
                    New Group
                  </label>
                  <label class="form-label mx-3">
                    <input type="radio" name="isNewGroup" [value]="false" formControlName="isNewGroup" />
                    Existing Group
                  </label>
                </div>
                <div class="col-xl-12 col-sm-12">
                  <label class="form-label">
                    Group Name
                  </label>
                  <input class="form-control" placeholder="Group Name" formControlName="groupName" *ngIf="contactsForm.get('isNewGroup')?.value" />
                  <!--<select class="form-select h-50" formControlName="groupId" *ngIf="!contactsForm.get('isNewGroup')?.value">
                    <option [value]="g.id" *ngFor="let g of groups">
                      {{g.name}}
                    </option>
                  </select>-->
                  <ng-select formControlName="groupId" [items]="groups" bindLabel="name" *ngIf="!contactsForm.get('isNewGroup')?.value"
                             bindValue="id"></ng-select>
                </div>
                <div class="mt-3 col-xl-12 col-sm-12" style="align-items: center; display: flex;">
                  <button [disabled]="isLoading" class="btn btn-light" type="submit" (click)="save(contactsForm.value)">
                    Save Contact
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="col-12 text-center" *ngIf="isLoading">
    <div class="spinner-border text-success" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <div class="col-xl-12  col-sm-12 mt-2">
    <div class="card">
      <div class="card-body">
        <div class="card-title row">
          <div class="col-xl-8 col-sm-12 mb-1">
            <strong>Contact List</strong>
            <ng-container *ngIf="pagesCount > 1">
              <a class="float-end pointer mx-2  my-1" style="text-decoration:none" (click)="prev()">Prev</a>
              <a class="float-end pointer mx-2 my-1" style="text-decoration:none" (click)="next()">Next</a>
            </ng-container>
          </div>

          <div class="col-xl-4 col-sm-12">
            <select class="form-select" (change)="getByGroup($event)">
              <option>Select Group</option>
              <option [value]="g.id" *ngFor="let g of groups">
                {{g.name}}
              </option>
            </select>
          </div>
          <!--<div class="col-xl-3 col-sm-12 mb-1">
            <input type="text" placeholder="Search Contacts" [formControl]="searchCtrl" class="form-control float-end" />
          </div>-->
        </div>
        <div class="row">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Mobile</th>
                  <th>Group</th>
                  <th>Added Date</th>
                  <!--<th>Blocked</th>-->
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let c of contacts">
                  <td>{{c.name}}</td>
                  <td>{{c.mobile}}</td>
                  <td>{{c.groupName}}</td>
                  <td>{{c.addedDate | date }}</td>
                  <!--<td>
                    <span *ngIf="c.isBlocked" class="badge bg-danger">Blocked</span>
                  </td>-->
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
