import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ContactsService {
  constructor(private http: HttpClient) {
  }

  get(groupId: string = '', currentPage: number = 1): Observable<any> {
    return this.http.get<any>('/api/wbcontacts/' + groupId, {
      params: {
        'currentPage': currentPage
      }
    });
  }

  send(contact: any): Observable<any> {
    return this.http.post<any>("/api/wbcontacts", contact);
  }

  importExcel(contact: any): Observable<any> {
    return this.http.post<any>("/api/wbcontacts/import", contact);
  }
}
