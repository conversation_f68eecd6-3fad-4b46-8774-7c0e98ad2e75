import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { finalize, pipe, Subscription } from 'rxjs';
import * as ExcelJS from 'exceljs';
import { ContactsService } from './contacts.service';

@Component({
    selector: 'app-contacts',
    templateUrl: './contacts.component.html',
    styleUrls: ['./contacts.component.css'],
    standalone: false
})
export class ContactsComponent implements OnInit, OnDestroy {
  contactsForm: FormGroup;
  importForm: FormGroup;
  isLoading: boolean = false;
  columns: string[] = [];
  excelData: (string | number | null | undefined)[][] = [];
  isReading = false;
  showExcelPreview = false;
  isPreviewing = false;
  pageSize = 50;
  currentExcelPage = 0;
  groups: any[] = [];
  subscriptions = new Subscription();
  contacts: any[] = [];
  currentDataPage = 0;
  itemsCount = 0;
  pagesCount = 0;
  selectedGroup = '';
  searchCtrl = new FormControl();
  constructor(private fb: FormBuilder,
    private contactService: ContactsService,
    private toastr: ToastrService,
    private cdk: ChangeDetectorRef) {
    this.contactsForm = this.fb.group({
      name: [null, [Validators.required]],
      mobile: [null, [Validators.required]],
      groupName: null,
      groupId: null,
      isNewGroup: true,
    });
    this.importForm = this.fb.group({
      nameColumn: [null, [Validators.required]],
      mobileColumn: [null, [Validators.required]],
      groupName: null,
      groupId: null,
      isNewGroup: true,
    });
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.isLoading = true;
    this.subscriptions.add(
      this.contactService.get().subscribe(r => {
        if (r.success) {
          this.contacts = r.data;
          this.groups = r.info;
          this.itemsCount = r.itemsCount;
          this.pagesCount = r.pagesCount;
          this.currentDataPage = 1;
        }
        this.isLoading = false;
        this.cdk.detectChanges();
      }));
  }


  save(form: any) {
    if (this.contactsForm?.valid) {
      if (!form.groupName && !form.groupId) {
        this.toastr.error('Group is required!');
      }
      this.isLoading = true;
      this.subscriptions.add(this.contactService.send(form).subscribe(r => {
        if (r.success) {
          if (!form.isNewGroup) {
            r.data.groupName = this.groups.find(g => g.id == form.groupId)?.name;
          }
          this.contacts.push(r.data);
          if (r.groups) {
            this.groups = r.groups;
          }
          this.toastr.success(r.message);
          this.contactsForm.reset();
        }
        this.isLoading = false;
      }));
    } else {
      this.contactsForm?.markAllAsTouched();
    }
  }

  importExcel(form: any) {
    if (this.importForm?.valid) {
      if (!form.groupName && !form.groupId) {
        this.toastr.error('Group is required!');
      }
      const isNewGroup = form.isNewGroup;
      const nameColIndex = this.columns.findIndex(c => c == form.nameColumn);
      const mobileColIndex = this.columns.findIndex(c => c == form.mobileColumn);
      const body = {
        isNewGroup: isNewGroup, groupName: form.groupName,
        contacts: this.excelData.slice(1).map(e => {
          return { name: e[nameColIndex], mobile: e[mobileColIndex] + '', groupId: !isNewGroup ? form.groupId : null };
        })
      };
      this.isLoading = true;
      this.subscriptions.add(this.contactService.importExcel(body).subscribe(r => {
        if (r.success) {
          this.contacts = body.contacts;
          if (r.groups) {
            this.groups = r.groups;
          }
          this.toastr.success(r.message);
          this.importForm.reset();
        }
        this.isLoading = false;
      }));
    } else {
      this.importForm?.markAllAsTouched();
    }
  }

  async onFileChange(event: any) {
    this.isReading = true;
    const file = event.target.files[0];
    const workbook = new ExcelJS.Workbook();
    
    try {
      await workbook.xlsx.load(await file.arrayBuffer());
      const worksheet = workbook.worksheets[0];
      
      // Get columns from the first row
      this.columns = [];
      worksheet.getRow(1).eachCell((cell) => {
        this.columns.push(cell.value?.toString() || '');
      });

      // Set default name and mobile columns
      this.importForm.get('nameColumn')?.setValue(this.columns.find(c => c.toLowerCase().indexOf('name') >= 0));
      const mobileColumn = this.columns.find(col => col.toLowerCase().indexOf('mobile') >= 0) || 
                          this.columns.find(col => col.toLowerCase().indexOf('phone') >= 0);
      this.importForm.get('mobileColumn')?.setValue(mobileColumn);

      // Get all data
      this.excelData = [];
      worksheet.eachRow((row, rowNumber) => {
        const rowData: (string | number | null | undefined)[] = [];
        row.eachCell((cell) => {
          rowData.push(cell.value?.toString() ?? null);
        });
        this.excelData.push(rowData);
      });

      this.isReading = false;
      this.cdk.detectChanges();
    } catch (error) {
      this.isReading = false;
      this.toastr.error('Error reading Excel file');
      console.error('Error reading Excel file:', error);
    }
  }

  toggleExcelPreview() {
    this.isPreviewing = !this.isPreviewing;
    this.cdk.detectChanges();
    setTimeout(() => {
      this.showExcelPreview = !this.showExcelPreview;
    }, 100);
  }

  get pagedExcelData(): any[] {
    return this.excelData.slice(1).filter((d, index) => index < (this.currentExcelPage + this.pageSize) && index >= this.currentExcelPage);
  }
  stopPreviewLoading(last: any) {
    if (last) {
      if (this.isPreviewing) {
        this.isPreviewing = false;
        this.currentExcelPage = 0;
        this.cdk.detectChanges();
      }
    }
  }
  nextPreview() {
    if (this.currentExcelPage >= this.excelData.length) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage + this.pageSize;
  }
  prevPreview() {
    if (this.currentExcelPage <= 1) {
      return;
    }
    this.currentExcelPage = this.currentExcelPage - this.pageSize;
  }

  next() {
    this.currentDataPage++;
    if (this.currentDataPage > this.pagesCount) {
      this.currentDataPage--;
      return;
    }
    this.loadByPage();
  }

  prev() {
    this.currentDataPage--;
    if (this.currentDataPage < 1) {
      this.currentDataPage = 1;
      return;
    }
    this.loadByPage();
  }

  loadByPage() {
    this.isLoading = true;
    this.subscriptions.add(
      this.contactService.get(this.selectedGroup, this.currentDataPage)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.contacts = r.data;
            if (r.itemsCount && r.pagesCount) {
              this.itemsCount = r.itemsCount;
              this.pagesCount = r.pagesCount;
            }
          }
        }));
  }

  getByGroup(event: any) {
    const value = event.target.value;
    this.currentDataPage = 1;
    this.selectedGroup = value;
    this.loadByPage();
  }
}
