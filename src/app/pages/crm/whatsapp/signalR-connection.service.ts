import { Injectable } from '@angular/core';
import * as signalR from "@microsoft/signalr";
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignalRConnectionService {

  connection: signalR.HubConnection | undefined;
  notificationReached: Subject<any>;
  constructor(/*private authService: AuthService*/) {
    this.notificationReached = new Subject();
  }

  startConnection() {
    this.connection = new signalR.HubConnectionBuilder()
      .configureLogging(signalR.LogLevel.Information)
      .withAutomaticReconnect()
      .withUrl("/notifications", {
        transport: signalR.HttpTransportType.ServerSentEvents
      })
      .build();
    // , {
    //   //  accessTokenFactory: () => this.authService.getAccessToken()
    // }
    this.connection.start()
      .then(() => {
        //console.log('Connection started');
      }).catch((err: any) => {
        //  console.log('Error while starting connection: ' + err)
      })
      .finally(() => {
        // console.log('Connection finally');
      });

    this.connection.on('Notify', d => {
      this.notificationReached.next(d);
    });
  }

  stopConnection() {
    this.connection?.stop();
    //  console.log('Connection stopped');
  }
}
