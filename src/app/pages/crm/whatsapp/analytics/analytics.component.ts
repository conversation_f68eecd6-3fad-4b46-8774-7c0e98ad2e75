import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { Subscription, finalize, pipe } from 'rxjs';

@Component({
    selector: 'app-analytics',
    templateUrl: './analytics.component.html',
    styleUrls: ['./analytics.component.css'],
    standalone: false
})
export class AnalyticsComponent implements OnInit, OnDestroy {
  analytics: any[] = [];
  conversation: any[] = [];
  subscriptions = new Subscription();
  granularities: any[] = [];
  phoneNumbers: any[] = [];
  queryform!: FormGroup;
  isLoading = false;
  current = 'analytics';
  constructor(private http: HttpClient,
    private cdk: ChangeDetectorRef,
    private fb: FormBuilder) {
    const currentDate = new Date();
    const previousMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    this.queryform = this.fb.group({
      start: [moment(previousMonth).format('yyyy-MM-DD'), [Validators.required]],
      end: [moment(currentDate).format('yyyy-MM-DD'), [Validators.required]],
      granularity: ['DAY', [Validators.required]],
      phoneNumberId: ['', [Validators.required]]
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.loadAnalytics();
  }

  loadAnalytics() {
    this.current = 'analytics';
    this.isLoading = true;
    if (this.granularities.indexOf('DAY') < 0) {
      this.granularities = ['HALF_HOUR', 'DAY', 'MONTH'];
      this.queryform.get('granularity')?.setValue('MONTH');
    }
    this.subscriptions.add(this.http.get<any>('/api/wbanalytics', {
      params: { ...this.queryform?.value }
    }).pipe(finalize(() => {
      this.isLoading = false;
      this.cdk.detectChanges();
    }))
      .subscribe(r => {
        if (r.success) {
          this.conversation = [];
          const d = r.data;
          this.analytics = d.data;
          if (d.phoneNumbers) {
            this.phoneNumbers = d.phoneNumbers;
            this.queryform.get('phoneNumberId')?.setValue(d.phoneNumbers[0].display_phone_number, { emitEvent: false })
          }
          this.cdk.detectChanges();
        }
      }));
  }

  loadConversations() {
    this.current = 'conversation';
    this.isLoading = true;
    if (this.granularities.indexOf('DAILY') < 0) {
      this.granularities = ['HALF_HOUR', 'DAILY', 'MONTHLY'];
      this.queryform.get('granularity')?.setValue('MONTHLY');
    }
    this.subscriptions.add(this.http.get<any>('/api/wbanalytics/conversation', {
      params: { ...this.queryform?.value }
    })
      .pipe(finalize(() => {
        this.isLoading = false;
        this.cdk.detectChanges();
      }))
      .subscribe(r => {
        if (r.success) {
          this.analytics = [];
          this.conversation = r.data.data;
          this.cdk.detectChanges();
        }
      }));
  }
}
