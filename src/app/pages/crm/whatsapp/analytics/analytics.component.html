<div class="card">
    <div class="card-body">
        <div class="card-title row">
            <div class="col-3">
                <strong>Analytics</strong>
            </div>
            <div class="col-9 float-end">
                <form [formGroup]="queryform">
                    <div class="row">
                        <div class="col">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1">Start</span>
                                <input type="date" class="form-control" placeholder="Start Date"
                                    formControlName="start">
                            </div>
                        </div>
                        <div class="col">
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1">End</span>
                                <input type="date" class="form-control" placeholder="End Date" formControlName="end">
                            </div>
                        </div>
                        <div class="col">
                            <select class="form-select foat-end" formControlName="granularity">
                                <option>Select Granularity</option>
                                <option [value]="g" *ngFor="let g of granularities">
                                    {{g}}
                                </option>
                            </select>
                        </div>
                        <div class="col">
                            <select class="form-select foat-end" formControlName="phoneNumberId">
                                <option>Select Phone Number</option>
                                <option [value]="ph.display_phone_number" *ngFor="let ph of phoneNumbers">
                                    {{ph.verified_name}}
                                </option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="row">
            <div class="col-3">
                <div class="list-group">
                    <button type="button" class="list-group-item list-group-item-action"
                        [ngClass]="{active: current == 'analytics' }" (click)="loadAnalytics()">
                        Statistics
                        <i class="fa fa-rotate-left float-end" *ngIf="!isLoading && current == 'analytics'"></i>
                        <div style="width:20px;height:20px;" class="spinner-border text-light float-end" role="status"
                            *ngIf="isLoading && current == 'analytics'">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                    <button type="button" class="list-group-item list-group-item-action"
                        [ngClass]="{active:current == 'conversation'}" (click)="loadConversations()">
                        Conversation
                        <i class="fa fa-rotate-left float-end" *ngIf="!isLoading && current == 'conversation'"></i>
                        <div style="width:20px;height:20px;" class="spinner-border text-light float-end" role="status"
                            *ngIf="isLoading && current == 'conversation'">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                </div>
            </div>
            <div class="col-9" *ngIf="current == 'analytics'">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Start</th>
                                <th>End</th>
                                <th>Sent</th>
                                <th>Delivered</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of analytics">
                                <td>{{ item.start | date:'dd/MM/yyyy hh:mm aa' }}</td>
                                <td>{{item.end | date: 'dd/MM/yyyy hh:mm aa' }}</td>
                                <td>{{item.sent}}</td>
                                <td>{{item.delivered}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-9" *ngIf="current == 'conversation'">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Start</th>
                                <th>End</th>
                                <th>Conversation</th>
                                <th>Category</th>
                                <th>Type</th>
                                <th>Country</th>
                                <th>Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of conversation">
                                <td>{{ item.start | date:'dd/MM/yyyy hh:mm aa' }}</td>
                                <td>{{item.end | date: 'dd/MM/yyyy hh:mm aa' }}</td>
                                <td>{{item.conversation}}</td>
                                <td>{{item.conversation_category}}</td>
                                <td>{{item.conversation_type}}</td>
                                <td>{{item.country}}</td>
                                <td>{{item.cost}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
