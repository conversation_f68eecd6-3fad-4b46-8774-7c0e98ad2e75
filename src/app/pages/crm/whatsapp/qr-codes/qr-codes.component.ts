import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { finalize, Subscription } from 'rxjs';
declare var bootstrap: any;

@Component({
    selector: 'app-qr-codes',
    templateUrl: './qr-codes.component.html',
    styleUrls: ['./qr-codes.component.css'],
    standalone: false
})
export class QrCodesComponent implements OnInit, OnDestroy {
  createForm: FormGroup | undefined;
  updateForm: FormGroup | undefined;
  subscriptions = new Subscription();
  isLoading = false;
  isCreating = false;
  data: any[] = [];
  phoneNumbers: any[] = [];
  selectedPhoneNumberId: any = '';
  @ViewChild('qrCodeContainer') qrCodeContainer!: ElementRef;
  isDeleting: boolean = false;
  isUpdating: boolean = false;
  toBeUpdate = false;
  itemToUpdate: any;
  constructor(private fb: FormBuilder,
    private toastr: ToastrService,
    private cdk: ChangeDetectorRef,
    private http: HttpClient) {
    this.initCreateForm();
  }

  initCreateForm() {
    this.createForm = this.fb.group({
      prefilled_message: '',
      generate_qr_image: ['SVG', Validators.required]
    });
  }

  initUpdateForm(model: any) {
    this.toBeUpdate = true;
    this.updateForm = this.fb.group({
      prefilled_message: model.prefilled_message,
      code: [model.code, Validators.required]
    });
    this.itemToUpdate = model;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {
    this.loadData();
  }


  loadData() {
    this.isLoading = true;
    this.subscriptions.add(this.http.get<any>('/api/wbqrcodes', {
      params: {
        phoneNumberId: this.selectedPhoneNumberId
      }
    })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(r => {
        if (r.success) {
          const d = r.data;
          d.data.forEach((i: any) => {
            i.toBeDelete = false;
          });
          this.data = d.data;
          if (d.phoneNumbers) {
            this.phoneNumbers = d.phoneNumbers;
            this.selectedPhoneNumberId = d.phoneNumbers[0].id;
          }
          setTimeout(() => {
            this.initializeTooltip();
          }, 100);
          this.cdk.detectChanges();
        }
      }));
  }

  save(form: any) {
    if (this.createForm?.valid) {
      this.isCreating = true;
      this.subscriptions.add(this.http.post<any>('/api/wbqrcodes', form)
        .pipe(finalize(() => this.isCreating = false))
        .subscribe(r => {
          if (r.success) {
            this.data.splice(0, 0, r.data);
            this.toastr.success(r.message);
            this.createForm?.get('prefilled_message')?.setValue('');
            setTimeout(() => {
              this.initializeTooltip();
            }, 50);
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.createForm?.markAllAsTouched();
    }
  }

  update(form: any) {
    if (this.updateForm?.valid) {
      this.isUpdating = true;
      this.subscriptions.add(this.http.put<any>('/api/wbqrcodes', form)
        .pipe(finalize(() => {
          this.isUpdating = false;
          this.toBeUpdate = false;
          this.itemToUpdate = null;
        }))
        .subscribe(r => {
          if (r.success) {
            this.updateForm?.reset();
            this.itemToUpdate.prefilled_message = r.data.prefilled_message;
            setTimeout(() => {
              this.initializeTooltip();
            }, 50);
            this.toastr.success(r.message);
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.updateForm?.markAllAsTouched();
    }
  }


  getByPhone(event: any) {
    const value = event.target.value;
    this.selectedPhoneNumberId = value;
    this.loadData();
  }

  initializeTooltip() {
    const tooltipTriggerList = Array.from(
      this.qrCodeContainer.nativeElement.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    if (tooltipTriggerList.length) {
      tooltipTriggerList.forEach((tooltipTriggerEl) => {
        new bootstrap.Tooltip(tooltipTriggerEl);
      });
      this.cdk.detectChanges();
    }
  }

  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.toastr.success('Copied to Clipboard!.');
  }

  remove(code: any) {
    this.isDeleting = true;
    this.subscriptions.add(this.http.delete<any>('/api/wbqrcodes/' + code)
      .pipe(finalize(() => this.isDeleting = false))
      .subscribe(r => {
        if (r.success) {
          this.data = this.data.filter(i => i.code != code);
          setTimeout(() => {
            this.initializeTooltip();
          }, 50);
        }
      }));
  }
}
