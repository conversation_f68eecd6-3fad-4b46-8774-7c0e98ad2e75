<div class="card mb-2" *ngIf="!toBeUpdate">
  <div class="card-body">
    <div class="card-title">
      <strong>Create New Qr Code</strong>
    </div>
    <div class="row" *ngIf="createForm">
      <form [formGroup]="createForm">
        <div class="row">
          <div class="col">
            <label class="form-label">
              Pre Message
            </label>
            <input class="form-control" formControlName="prefilled_message" />
          </div>
          <!--<div class="col">
            <label class="form-label">
              Image Format
            </label>
            <div class="col">
              <label class="form-label">
                <input type="radio" [value]="'PNG'" formControlName="generate_qr_image" />
                PNG
              </label>
              <label class="form-label mx-3">
                <input type="radio" [value]="'SVG'" formControlName="generate_qr_image" />
                SVG
              </label>
            </div>-->
          <!--</div>-->
          <div class="col pt-4">
            <button [disabled]="isCreating" class="btn btn-light mt-1" type="submit" (click)="save(createForm.value)">
              <div class="spinner-border text-success" style="width:20px;height:20px" role="status" *ngIf="isCreating">
                <span class="visually-hidden">Loading...</span>
              </div>
              Create
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="card mb-2" *ngIf="toBeUpdate">
  <div class="card-body">
    <div class="card-title">
      <strong>Update Qr Code</strong>
    </div>
    <div class="row" *ngIf="updateForm">
      <form [formGroup]="updateForm">
        <div class="row">
          <div class="col">
            <label class="form-label">
              Pre Message
            </label>
            <input class="form-control" formControlName="prefilled_message" />
          </div>
          <!--<div class="col">
            <label class="form-label">
              Image Format
            </label>
            <div class="col">
              <label class="form-label">
                <input type="radio" [value]="'PNG'" formControlName="generate_qr_image" />
                PNG
              </label>
              <label class="form-label mx-3">
                <input type="radio" [value]="'SVG'" formControlName="generate_qr_image" />
                SVG
              </label>
            </div>-->
          <!--</div>-->
          <div class="col pt-4">
            <button [disabled]="isUpdating" class="btn btn-light mt-1" type="submit" (click)="update(updateForm.value)">
              <div class="spinner-border text-success" style="width:20px;height:20px" role="status" *ngIf="isUpdating">
                <span class="visually-hidden">Loading...</span>
              </div>
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="card">
  <div class="card-body">
    <div class="card-title row">
      <div class="col-xl-8 col-sm-6">
        <strong>Qr Code List</strong>
      </div>
      <div class="col-xl-4 col-sm-6">
        <select class="form-select foat-end" (change)="getByPhone($event)">
          <option>Select Phone Number</option>
          <option [selected]="ph.id == selectedPhoneNumberId" [value]="ph.id" *ngFor="let ph of phoneNumbers">
            {{ph.verified_name}}
          </option>
        </select>
      </div>
    </div>
    <div class="row" #qrCodeContainer>
      <div class="col-xl-3 col-sm-12 px-2 text-center qrcode-box" *ngFor="let item of data">
        <img style="width:80%;" src="{{item.qr_image_url}}" />
        <div class="actions-btn bg-secondary">
          <ng-container *ngIf="!item.toBeDelete">
            <a [routerLink]="[]" class="pointer mx-2" (click)="item.toBeDelete = true">
              <i class="fa fa-trash"></i> Delete
            </a>
            <a [routerLink]="[]" class="pointer mx-2" (click)="initUpdateForm(item)">
              <i class="fa fa-edit"></i> Update
            </a>
          </ng-container>
          <ng-container *ngIf="item.toBeDelete ">
            <div class="col-12 text-center" *ngIf="isDeleting">
              <div class="spinner-border text-light" role="status" style="width:20px;height:20px;">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <ng-container *ngIf="!isDeleting">
              <a [routerLink]="[]" class="pointer mx-2" (click)="remove(item.code)">
                <i class="fa fa-check"></i> Yes
              </a>
              <a [routerLink]="[]" class="pointer mx-2" (click)="item.toBeDelete = false">
                <i class="fa fa-times"></i> No
              </a>
            </ng-container>
          </ng-container>
        </div>
        <div class="pt-2">
          <p>
            <a [routerLink]="[]" class="pointer" data-bs-toggle="tooltip" data-bs-placement="bottom" [attr.data-bs-title]="item.prefilled_message" data-bs-trigger="hover"
               (click)="copyMessage(item.prefilled_message)">
              <i class="fa fa-envelope"></i> Message
            </a>
            <a [routerLink]="[]" class="pointer px-2" data-bs-toggle="tooltip" data-bs-placement="bottom" [attr.data-bs-title]="item.deep_link_url" data-bs-trigger="hover"
               (click)="copyMessage(item.deep_link_url)">
              <i class="fa fa-copy"></i> Link
            </a>
            <a [routerLink]="[]" class="pointer" (click)="copyMessage(item.qr_image_url)">
              <i class="fa fa-copy"></i>  QR
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
