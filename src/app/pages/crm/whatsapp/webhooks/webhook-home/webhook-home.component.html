<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Webhooks</strong>
    </div>
    <div *ngIf="showActivate">
      Subscribe to WhatsApp Business and Activate Webhooks click Activate Webhooks Button
      <button  type="button" class="btn btn-light" (click)="activate()">Activate Webhooks</button>
    </div>
    <div *ngIf="WbSubscriptions.length > 0">
      <strong>WhatsApp Webhooks : <span class="badge text-bg-light">Activated</span></strong>
      <hr />
      <h6>List Of Subscriptions</h6>
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <td>Object</td>
              <td>Fields</td>
              <td>Status</td>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of WbSubscriptions">
              <td>{{item.object}}</td>
              <td>
                <span *ngFor="let f of item.fields">
                  {{f.name }}
                  <!-- - {{ f.version}} -->
                </span>
              </td>
              <td>
                <span *ngIf="item.active" class="badge text-bg-light">Active</span>
                <span *ngIf="!item.active" class="badge text-bg-light">inactive</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
