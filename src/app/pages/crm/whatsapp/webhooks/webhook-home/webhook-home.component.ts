import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { WebhooksService } from '../webhooks.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';


@Component({
    selector: 'webhook-home',
    templateUrl: './webhook-home.component.html',
    styleUrls: ['./webhook-home.component.css'],
    standalone: false
})
export class WebhookHomeComponent implements OnInit, OnDestroy {

  WbSubscriptions: any[] = [];
  subscription = new Subscription();
  showActivate = false;
  constructor(private webhooksService: WebhooksService,
    private cdk: ChangeDetectorRef,
    private toastr: ToastrService) {

  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  ngOnInit(): void {
    this.loadSubscriptions();
  }
  loadSubscriptions() {
    this.subscription.add(this.webhooksService.getSubscriptions()
      .subscribe(r => {
        if (r.success) {
          this.WbSubscriptions = r.data;
          if (this.WbSubscriptions.length == 0) {
            this.showActivate = true;
            this.cdk.detectChanges();
          } else {
            this.showActivate = false;
          }
        } else {
          this.showActivate = true;
        }
        this.cdk.detectChanges();
      }));
  }

  activate() {
    this.webhooksService.initialize()
      .subscribe(r => {
        if (r.success) {
          this.loadSubscriptions();
          this.toastr.success('Webhooks is activated successfully!');
        }
      });
  }
}
