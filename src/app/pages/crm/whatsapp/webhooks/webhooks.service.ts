import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class WebhooksService {

  constructor(private http: HttpClient) { }

  initialize(): Observable<any> {
    return this.http.get<any>('api/wbwebhooks/initialize');
  }

  getSubscriptions(): Observable<any> {
    return this.http.get<any>('api/wbwebhooks/subscriptions');
  }
}
