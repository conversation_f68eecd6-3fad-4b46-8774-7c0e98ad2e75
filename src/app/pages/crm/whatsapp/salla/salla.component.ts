import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { finalize, Subscription } from 'rxjs';
import { TemplatesService } from '../templates/templates.service';

@Component({
    selector: 'app-salla',
    templateUrl: './salla.component.html',
    styleUrls: ['./salla.component.css'],
    standalone: false
})
export class SallaComponent implements OnInit, OnDestroy {
  webhooks: any[] = [];
  subscriptions = new Subscription();
  isLoading = false;
  current = 'webhooks';
  isIntegrated = false;
  events: any[] = [];
  templates: any[] = [];
  phoneNumbers: any[] = [];
  registerForm!: FormGroup;
  item: any = null;
  columns: any[] = [];
  isUploading: boolean = false;
  constructor(private http: HttpClient, private fb: FormBuilder,
    private templateService: TemplatesService,
    private cdk: ChangeDetectorRef,
    private toastr: ToastrService) {
    this.initForm(null);
    this.registerForm = this.fb.group({
      event: [null, [Validators.required]],
      template: [null, [Validators.required]],
      senderNumber: [null, [Validators.required]],
      name: null,
      bodyVariables: this.fb.array([]),
      headerVariableValue: null,
      rule: null,
      uploadedMediaId: null,
      contentType: null,
      fileName: null,
    });
  }
  initForm(model: any) {

  }
  get bodyVars() {
    return <FormArray>this.registerForm.get('bodyVariables');
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnInit(): void {
    this.loadSettings();
    this.loadWebhooks();
    this.subscriptions.add(this.registerForm.get('template')?.valueChanges.subscribe(v => {
      if (v) {
        const templateId = this.templates.find(t => t.name == v)?.id;
        this.selectTemplate(templateId);
      }
    }));
  }

  loadSettings() {
    this.current = "settings";
    this.isLoading = true;
    this.subscriptions.add(this.http.get<any>('/api/wbsalla/settings')
      .pipe(finalize(() => {
        this.isLoading = false;
        this.cdk.detectChanges();
      }))
      .subscribe(r => {
        if (r.success) {
          const d = r.data;
          this.initForm(d.settings);
          if (d.settings) {
            if (d.settings.accessToken) {
              this.isIntegrated = true;
            }
          }
          this.events = d.events;
          this.templates = d.templates;
          this.phoneNumbers = d.phoneNumbers;
          this.columns = d.columns;
        }
      }));
  }

  loadWebhooks() {
    this.current = "webhooks";
    this.isLoading = true;
    this.subscriptions.add(this.http.get<any>('/api/wbsalla/webhooks')
      .pipe(finalize(() => {
        this.isLoading = false;
        this.cdk.detectChanges();
      }))
      .subscribe(r => {
        if (r.success) {
          this.webhooks = r.data;
        }
      }));
  }

  register(form: any) {
    if (this.registerForm.valid) {
      this.isLoading = true;
      form.name = this.events.find(e => e.event == form.event)?.label;
      this.subscriptions.add(this.http.post<any>('/api/wbsalla/webhooks', form)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(r => {
          if (r.success) {
            this.toastr.success(r.message);
            this.loadWebhooks();
          } else {
            this.toastr.error(r.message);
          }
        }));
    } else {
      this.registerForm.markAllAsTouched();
    }
  }

  selectTemplate(value: any) {
    this.subscriptions.add(this.templateService.findTemplate(value).subscribe(r => {
      if (r.success) {
        this.bodyVars.clear();
        this.item = r.data;
        this.item.bodyVariables.forEach((b: any, i: any) => {
          const control = this.fb.group({
            index: i,
            value: [null, [Validators.required]],
          });

          this.bodyVars.push(control);
        });
        if (this.item.headerHasVariables) {
          this.registerForm.get('headerVariableValue')?.setValidators([Validators.required]);
        } else {
          this.registerForm.get('headerVariableValue')?.clearValidators();
          this.registerForm.get('headerVariableValue')?.updateValueAndValidity();
        }
        this.cdk.detectChanges();
      }
    }));
  }

  showRegister() {
    this.current = "register";
  }
  searchEvent(term: string, item: any) {
    return item.event.indexOf(term.toLowerCase()) >= 0 || item.label.indexOf(term.toLowerCase()) >= 0;
  }

  upload(event: any) {
    if (event.target.files.length) {
      var file = event.target.files[0];
      const phoneNumberId = this.phoneNumbers.find(p => p.display_phone_number == this.registerForm.get('senderNumber')?.value)?.id;
      if (!phoneNumberId) {
        this.toastr.error("Sender is required");
        event.target.files[0].value = '';
        return false;
      }
      this.isUploading = true;
      this.subscriptions.add(this.templateService.upload(file, phoneNumberId)
        .subscribe(v => {
          if (v.success) {
            this.registerForm.get('uploadedMediaId')?.setValue(v.data.uploadedMediaId);
            this.registerForm.get('contentType')?.setValue(file.type);
            this.registerForm.get('fileName')?.setValue(file.name);
            this.isUploading = false;
          }
        }));
    }
    return true;
  }

  activate(id: any) {
    this.subscriptions.add(this.http.post<any>('/api/wbsalla/activate/' + id, null).subscribe(r => {
      if (r.success) {
        this.toastr.success(r.message);
      } else {
        this.toastr.error(r.message);
      }
    }));
  }

  tobeDelete(item: any) {
    this.webhooks.forEach(w => {
      w.toBeDelete = false;
    });
    item.toBeDelete = true;
  }

  remove(item: any) {
    this.subscriptions.add(this.http.delete<any>('/api/wbsalla/' + item.webhookStoreId).subscribe(r => {
      if (r.success) {
        this.toastr.success(r.message);
        this.webhooks = this.webhooks.filter(w => w.webhookStoreId != item.webhookStoreId);
      } else {
        this.toastr.error(r.message);
      }
    }));
  }
}
