import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SallaRoutingModule } from './salla-routing.module';
import { SallaComponent } from './salla.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { BootstrapTooltipDirective } from './BootstrapTooltipDirective';

@NgModule({
  declarations: [
    SallaComponent,
    BootstrapTooltipDirective
  ],
  imports: [
    CommonModule,
    SallaRoutingModule,
    ReactiveFormsModule,
    NgSelectModule
  ]
})
export class SallaModule { }


