import { Directive, ElementRef, Input, AfterViewInit, Renderer2, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
declare var bootstrap: any;
const tooltipElements: any[] = [];
@Directive({
    selector: '[appBootstrapTooltip]',
    standalone: false
})
export class BootstrapTooltipDirective implements AfterViewInit, OnDestroy {
  @Input('appBootstrapTooltip') tooltipText!: string;
  constructor(private el: ElementRef, private renderer: Renderer2) { }
  ngOnDestroy(): void {
    //tooltipElements.splice(0, tooltipElements.length - 1);
  }

  ngAfterViewInit() {
    const options = {
      title: this.tooltipText,
      trigger: 'hover',
      placement: 'top'
    };
    // Initialize Bootstrap tooltip on the element
    const t = new bootstrap.Tooltip(this.el.nativeElement, options);
    tooltipElements.push(t);
    this.renderer.listen(this.el.nativeElement, 'click', () => {
      t.hide();
      this.hideTooltip();
    });
    this.renderer.listen(this.el.nativeElement, 'focusOut', () => {
      t.hide();
      this.hideTooltip();
    });
    this.renderer.listen(document.body, 'click', () => {
      this.hideTooltip();
      document.body.querySelectorAll('tooltip').forEach(e => {
        e.remove();
        tooltipElements.splice(0);
      });
    });
  }

  private hideTooltip() {
    tooltipElements.forEach((e, i) => {
      e.hide();
    });
  }
}
