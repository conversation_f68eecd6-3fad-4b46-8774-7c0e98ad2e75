import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { WebhooksService } from '../webhooks/webhooks.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';


@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    standalone: false
})
export class HomeComponent implements OnInit, OnDestroy {

  WbSubscriptions: any[] = [];
  subscription = new Subscription();
  showActivate = false;
  constructor(private webhooksService: WebhooksService, private toastr: ToastrService) {

  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  ngOnInit(): void {
    this.subscription.add(this.webhooksService.getSubscriptions()
      .subscribe(r => {
        if (r.success) {
          this.WbSubscriptions = r.data;
          if (this.WbSubscriptions.length == 0) {
            this.showActivate = true;
          }
        } else {
          this.showActivate = true;
        }
      }));
  }

  activate() {
    this.webhooksService.initialize()
      .subscribe(r => {
        if (r.success) {
          this.toastr.success('Webhooks is activated successfully!');
        }
      });
  }
}
