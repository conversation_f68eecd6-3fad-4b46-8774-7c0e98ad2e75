import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {crmHomeComponent} from './crm-home/crm-home.component';


const routes: Routes = [

    {
        path: '',
        component: crmHomeComponent
    },
    {
        path: 'configuration',
        loadChildren: () => import('./configuration/crm-configuration.module').then(m => m.CrmConfigurationModule)
    },
    {
        path: 'crm',
        loadChildren: () => import('./crm/crm-crm.module').then(m => m.CrmCrmModule)
    },
    {
        path: 'email',
        loadChildren: () => import('./email/crm-email.module').then(m => m.CrmEmailModule)
    },
    {
        path: 'sms',
        loadChildren: () => import('./sms/sms.module').then(m => m.SMSModule)
    },
    {
        path: 'whatsapp',
        loadChildren: () => import('./whatsapp/whatsapp.module').then(m => m.WhatsAppModule),
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class crmRoutingModule {
}
