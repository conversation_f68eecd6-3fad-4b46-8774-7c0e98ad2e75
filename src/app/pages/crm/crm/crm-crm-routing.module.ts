import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ComplaintsComponent} from './complaints/complaints.component';
import {CustomerRequestsComponent} from './customer-requests/customer-requests.component';
import {CustomerSurveyComponent} from './customer-survey/customer-survey.component';
import {LadesComponent} from './lades/lades.component';
import {SalecrmComponent} from './salecrm/salecrm.component';
import {LeadsViewComponent} from './lades/leads-view/leads-view.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'customer_survey',
        pathMatch: 'full'
    },
    {
        path: 'customer_survey',
        component: CustomerSurveyComponent
    },
    {
        path: 'customer_requests',
        component: CustomerRequestsComponent
    },
    {
        path: 'complaints',
        component: ComplaintsComponent
    },
    {
        path: 'Leads',
        component: LadesComponent
    },
    {
        path: 'Leads_view',
        component: LeadsViewComponent
    },
    {
        path: 'crm',
        component: SalecrmComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CrmCrmRoutingModule {
}
