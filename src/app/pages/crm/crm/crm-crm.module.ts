import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';

import {ComplaintsComponent} from './complaints/complaints.component';
import {CustomerRequestsComponent} from './customer-requests/customer-requests.component';
import {CustomerSurveyComponent} from './customer-survey/customer-survey.component';
import {LadesComponent} from './lades/lades.component';
import {SalecrmComponent} from './salecrm/salecrm.component';
import {LeadsViewComponent} from './lades/leads-view/leads-view.component';
import {CrmCrmRoutingModule} from "./crm-crm-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {DxScrollViewModule} from "devextreme-angular";

@NgModule({
    declarations: [
        CustomerSurveyComponent,
        CustomerRequestsComponent,
        ComplaintsComponent,
        LadesComponent,
        SalecrmComponent,
        LeadsViewComponent,
    ],
    imports: [CrmCrmRoutingModule, SharedModule, DxScrollViewModule],
    exports: [RouterModule],
})
export class CrmCrmModule {
}
