import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import { LadeService } from '../lades.service';


@Component({
    selector: 'app-leads-view',
    templateUrl: './leads-view.component.html',
    styleUrls: ['./leads-view.component.scss'],
    standalone: false
})
export class LeadsViewComponent implements OnInit, OnDestroy {
  companyForm: FormGroup;
  company: any;
  subscription = new Subscription();
  isLoading = false;
  branches: any[];
  countries: any[];
  companies: any[];
  searchExpr: any;
  registrationtype: any[];
  constructor(private service: LadeService, private fb: FormBuilder,
    private translateService: TranslateService,
    private cdk: ChangeDetectorRef) {
    this.subscription.add(service.getbranches().subscribe(r => {
      if (r.success) {
        this.branches = r.data;

        this.cdk.detectChanges();

      }
    }));

    this.subscription.add(service.getCompanies().subscribe(r => {
      if (r.success) {
        this.companies = r.data;

        this.cdk.detectChanges();

      }
    }));

    this.subscription.add(service.getcountries().subscribe(r => {
      if (r.success) {
        this.countries = r.data;

        this.cdk.detectChanges();

      }
    }));

    this.searchExpr = ['id', 'name'];
    this.registrationtype = [
      {
        "id": "CRN",
        "name": "Commercial Registration Number"
      },
      {
        "id": "MOM",
        "name": "Momra License"
      },
      {
        "id": "MLS",
        "name": "MLSD License"
      },
      {
        "id": "SAG",
        "name": "Sagia License"
      },
      {
        "id": "OTH",
        "name": "Other ID"
      }
    ];
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.companyForm = this.fb.group({
      nameAr: [this.company?.nameAr, Validators.required],
      nameEn: [this.company?.nameEn, Validators.required],
      mobile: [this.company?.mobile, Validators.required],
      crn: [this.company?.crn, Validators.required],
      address: this.company?.address,
      email: [this.company?.email, Validators.email],
      header: this.company?.header,
      logo: this.company?.footer,
      footer: this.company?.footer,
    });
  }

  save(form: any) {
    if (this.companyForm.valid) {
      if (!this.isLoading) {
        this.isLoading = true;
        const fd = new FormData();
        fd.append('nameAr', form.nameAr);
        fd.append('nameEn', form.nameEn);
        fd.append('crn', form.crn);
        fd.append('address', form.address);
        fd.append('mobile', form.mobile);
        fd.append('email', form.email);
        fd.append('logo', form.logo);
        fd.append('header', form.header);
        fd.append('footer', form.footer);

      }
    } else {

    }
  }

  loadInfo() {
    this.subscription.add(this.service.getInfo().subscribe(r => {
      if (r.success) {
        this.company = r.data;
        this.initForm();
        this.cdk.detectChanges();
      }
    }));
  }

  upload(event: any, key: string) {
    this.companyForm.get(key)?.setValue(event.target.files[0]);
  }

  get isArabic() {
    return this.translateService.currentLang == "ar";
  }
}


