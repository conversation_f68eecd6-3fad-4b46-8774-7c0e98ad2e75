#kanban {
  white-space: nowrap;
}

.list {
  border-radius: 8px;
  margin: 5px;
  
  display: inline-block;
  vertical-align: top;
  white-space: normal;
}

.list-title {
  font-size: 16px;
  padding: 10px;
  padding-left: 30px;
  margin-bottom: -10px;
  font-weight: bold;
  cursor: pointer;
}

.scrollable-list {
  height: 800px;
  width: 100%;
}

.sortable-cards {
  min-height: 100%;
}

.card {
  position: relative;
  background-color: white;
  box-sizing: border-box;
  width: 230px;
  padding: 10px 20px;
  margin: 10px;
  cursor: pointer;
}

.card-subject {
  padding-bottom: 10px;
}

.card-assignee {
  opacity: 0.6;
}

.card-priority {
  position: absolute;
  top: 10px;
  bottom: 10px;
  left: 5px;
  width: 5px;
  border-radius: 2px;
  background: #86c285;
}

.priority-1 {
  background: #adadad;
}

.priority-2 {
  background: #86c285;
}

.priority-3 {
  background: #edc578;
}

.priority-4 {
  background: #ef7d59;
}

::ng-deep .dx-sortable {
  display: block;
}
