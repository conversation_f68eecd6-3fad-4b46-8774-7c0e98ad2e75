import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SalesCrmService } from './salecrm.service';
 

@Component({
    selector: 'app-salecrm',
    templateUrl: './salecrm.component.html',
    styleUrls: ['./salecrm.component.scss'],
    standalone: false
})
export class SalecrmComponent implements OnInit {
  lists: any[] = [];
  statuses: string[] = [
    'Not Started',
    'Need Assistance',
    'In Progress',
    'Deferred',
    'Completed',
  ];
  employees: any = {};
  constructor(
    private myService: SalesCrmService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef
  ) {
    const tasks = myService.getTasks();

    myService.getEmployees().forEach((employee) => {
      this.employees[employee.ID] = employee.Name;
    });

    this.statuses.forEach((status) => {
      this.lists.push(tasks.filter((task) => task.Task_Status === status));
    });
  }

  onListReorder(e: any) {
    const list = this.lists.splice(e.fromIndex, 1)[0];
    this.lists.splice(e.toIndex, 0, list);

    const status = this.statuses.splice(e.fromIndex, 1)[0];
    this.statuses.splice(e.toIndex, 0, status);
  }

  onTaskDragStart(e: any) {
    e.itemData = e.fromData[e.fromIndex];
  }

  onTaskDrop(e: any) {
    e.fromData.splice(e.fromIndex, 1);
    e.toData.splice(e.toIndex, 0, e.itemData);
  }

  ngOnInit(): void { }
}

