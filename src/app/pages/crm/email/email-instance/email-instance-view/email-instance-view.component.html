

<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Create Instance</strong>
    </div>
    <div class="row">
      <form [formGroup]="newdata">

        <div class="row mb-3">
          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              Name
            </label>
            <input class="form-control h-50" placeholder="Name" formControlName="name" />

          </div>
           
        </div>


        <div class="row mb-3">


          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              Email Address
            </label>
            <input class="form-control h-50" placeholder="EmailAddress" formControlName="emailAddress" />

          </div>

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              User Name
            </label>
            <input class="form-control h-50" placeholder="User Name" formControlName="userName" />

          </div>

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              PassWord
            </label>
            <input class="form-control h-50" placeholder="PassWord" formControlName="passWord" />

          </div>



        </div>

        <div class="row mb-3">
        

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              Incomming EmailServer
            </label>
            <input class="form-control h-50" placeholder="Incomming Email Server" formControlName="incommingEmailServer" />

          </div>

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label">
              Outgoing EmailServer
            </label>
            <input class="form-control h-50" placeholder="Outgoing Email Server" formControlName="outgoingEmailServer" />

          </div>

        </div>


        <div class="row mb-3">
          <div class="col-xl-2 col-sm-12">
            <label for="templateName" class="form-label m-2">
              POP3
            </label>
            <input class="form-check-input" type="radio" [value]="0" formControlName="pOP3" />

          </div>

          <div class="col-xl-2 col-sm-12">
            <label for="templateName" class="form-label m-2">
              IMAP4
            </label>
            <input class="form-check-input" type="radio" [value]="1" formControlName="iMAP4" />
          </div>



        </div>




        <div class="row mb-3">

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label m-3">
              Incomming Port
            </label>
            <input class="form-control h-50" placeholder="Incomming Port" formControlName="incommingPort" />

          </div>

          <div class="col-xl-4 col-sm-12">
            <label for="templateName" class="form-label m-3">
              Outgoing Port
            </label>
            <input class="form-control h-50" placeholder="Outgoing Port" formControlName="outgoingPort" />

          </div>



        </div>



        <div class="row mb-3">
          <div class="col-xl-3 col-sm-12">
            <label for="templateName" class="form-label m-3">
              Outgoing Requires Authentication
            </label>

            <input class="form-check-input m-3" type="checkbox" [value]="0" formControlName="outgoingRequiresAuthentication" />
          </div>

          <div class="col-xl-3 col-sm-12">
            <label for="templateName" class="form-label m-3">
              UseUser Password For Send
            </label>
            <input class="form-check-input m-3" type="checkbox" [value]="0" formControlName="useUserPasswordForSend" />
          </div>


          <div class="col-xl-3 col-sm-12">
            <label for="templateName" class="form-label m-3">
              Incomming Require SSL
            </label>
            <input class="form-check-input m-3" type="checkbox" [value]="0" formControlName="incommingRequireSSL" />

          </div>

          <div class="col-xl-3 col-sm-12">
            <label for="templateName" class="form-label m-3">
              Outgoing Require SSL
            </label>
            <input class="form-check-input m-3" type="checkbox" [value]="0" formControlName="outgoingRequireSSL" />
          </div>
        </div>




        <div class="row ">
          <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="save()">
              <ng-container *ngIf="!isLoading">Save Changes</ng-container>
              <ng-container *ngIf="isLoading">
                <span clas="indicator-progress" [style.display]="'block'">
                  Please wait...{{ " " }}
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
              </ng-container>
            </button>
          </div>
        </div>

      </form>
    </div>
  </div>
</div>
