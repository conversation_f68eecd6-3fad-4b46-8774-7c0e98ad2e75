import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { EmailService } from '../../email.service';

@Component({
    selector: 'app-email-instance-view',
    templateUrl: './email-instance-view.component.html',
    styleUrls: ['./email-instance-view.component.scss'],
    standalone: false
})
export class EmailInstanceViewComponent implements OnInit, OnDestroy {
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: EmailService,
    private route: ActivatedRoute,
    private router: Router) {
    this.createForm();

    this.route.paramMap.subscribe(p => {
      const id = p.get('id');
      if (id) {
        this.subscriptions.add(myService.instancedetails(id).subscribe(r => {
          if (r.success) {
            this.createForm(r.data);
            this.cdk.detectChanges();
          }
        }));
      } else {
        this.createForm()
      }
    });

  }
  public createForm(model: any = null) {
    this.newdata = this.fb.group({
      id: model?.id,
      name: [model?.name, Validators.required],
      emailAddress: [model?.emailAddress],
      userName: [model?.userName],
      passWord: [model?.passWord],
      incommingEmailServer: [model?.incommingEmailServer],
      outgoingEmailServer: [model?.outgoingEmailServer],
      pOP3: [model?.pOP3],
      iMAP4: [model?.iMAP4],
      incommingPort: [model?.incommingPort],
      outgoingPort: [model?.outgoingPort],
      outgoingRequiresAuthentication: [model?.outgoingRequiresAuthentication],
      useUserPasswordForSend: [model?.useUserPasswordForSend],
      incommingRequireSSL: [model?.incommingRequireSSL],
      outgoingRequireSSL: [model?.outgoingRequireSSL]
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {


  }

  save() {
    if (this.newdata.valid) {
      let form = this.newdata.value
      if (form.id) {
        this.subscriptions.add(this.myService.updateinstance(form.id, form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/emailInstance']);
            }
          }));
      } else {
        this.subscriptions.add(this.myService.createinstance(form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/emailInstance']);
            }
          }));
      }
    }
    else {
      this.newdata.markAllAsTouched();
    }
  }


  
}
