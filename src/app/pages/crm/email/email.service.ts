import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = `${environment.appUrls.Emailurl}`;
  }

  templatedetails(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + "EmailTemplate/" + id);
  }
  instancedetails(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + "EmailInstance/" + id);
  }
  emaillist(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl + "EmailTemplate/" + `${id}`, {
      params: {
        currentPage: page.toString()
      }
    });
  }


  inistance_details(id: string): Observable<any> {
    return this.http.get<any>(this.baseUrl + "EmailInstance/" + id);
  }
  instance_list(id: string, page: number = 1): Observable<any> {
    return this.http.get<any>(this.baseUrl + "EmailInstance/" + `${id}`, {
      params: {
        currentPage: page.toString()
      }
    });
  }

  upload(file: any, phoneNumberId: any): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>("/api/wbtemplatemessages/upload?phoneNumberId=" + phoneNumberId, fd);
  }
  createtemplate(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl + "EmailTemplate", form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  createinstance(form: any): Observable<any> {
    return this.http.post<any>(this.baseUrl + "EmailInstance", form)
      .pipe(map(result => {
        if (result.success) {

        }
        return result;
      }));
  }

  updateinstance(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  updatetemplate(id: string, form: any): Observable<any> {
    return this.http.put<any>(this.baseUrl + "EmailTemplate/" + id, form)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }
  deleteinstance(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + "EmailInstance/" + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  deletetemplate(id: string): Observable<any> {
    return this.http.delete<any>(this.baseUrl + "EmailTemplate/" + id)
      .pipe(map(result => {
        if (result.success) {
        }
        return result;
      }));
  }

  getSallaCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbsalla/customers');
  }
  getZidCustomers(): Observable<any> {
    return this.http.get<any>('/api/wbzid/customers');
  }
  getCustomers(): Observable<any> {
    return this.http.get<any>('/api/customer');
  }
  getSuppliers(): Observable<any> {
    return this.http.get<any>('/api/Suppliers');
  }
  getLeads(): Observable<any> {
    return this.http.get<any>('/api/Leads');
  }
  getStaff(): Observable<any> {
    return this.http.get<any>('/api/Employees');
  }

  search(v: any): Observable<any> {
    const form = { term: v };
    return this.http.post(this.baseUrl + 'search', form);
  }
  filter(form: any): Observable<any> {
    return this.http.post(this.baseUrl + 'filter', form);
  }

  exportExcel(): Observable<any> {
    return this.http.get(this.baseUrl + 'excel', { responseType: 'blob' });
  }
  importExcel(form: FormData): Observable<any> {
    return this.http.post(this.baseUrl + 'excel', form);
  }
}
