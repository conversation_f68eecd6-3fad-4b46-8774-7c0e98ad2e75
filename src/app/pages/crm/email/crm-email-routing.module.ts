import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {EmailTemplateComponent} from './email-template/email-template.component';
import {EmailInstanceComponent} from './email-instance/email-instance.component';

import {EmailTemplateViewComponent} from './email-template/email-template-view/email-template-view.component';
import {EmailInstanceViewComponent} from './email-instance/email-instance-view/email-instance-view.component';
import {SendEmailComponent} from "../../shared/send-email/send-email/send-email.component";


const routes: Routes = [

    {
        path: '',
        redirectTo: 'email_instance_view',
        pathMatch: 'full'
    },
    {
        path: 'email_instance_view',
        component: EmailInstanceViewComponent
    },
    {
        path: 'email_instance',
        component: EmailInstanceComponent
    },
    {
        path: 'email_instance_view/:id',
        component: EmailInstanceViewComponent
    },
    {
        path: 'email_template',
        component: EmailTemplateComponent
    },
    {
        path: 'email_template_view',
        component: EmailTemplateViewComponent
    },
    {
        path: 'email_template_view/:id',
        component: EmailTemplateViewComponent
    },
    {
        path: 'send',
        component: SendEmailComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CrmEmailRoutingModule {
}
