

<div class="card">
  <div class="card-body">
    <div class="card-title">
      <strong>Create Template</strong>
    </div>
    <div class="row">
      <form [formGroup]="newdata">
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="col-xl-4 col-sm-12">
                <label for="templateName" class="form-label">
                  Template Name
                </label>
                <input class="form-control h-50" placeholder="Template Name" formControlName="name">

              </div>

            </div>

            <div class="row mb-3">
              <div class="col-12">
                <label for="body" class="form-label">
                  Template
                </label>
                <textarea class="form-control" rows="4" placeholder="Body Text" formControlName="template"
                          maxlength="1040"></textarea>


              </div>
            </div>

            <div class="row ">
              <div class="col-md-12 text-right">
                <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="save()">
                  <ng-container *ngIf="!isLoading">Save Changes</ng-container>
                  <ng-container *ngIf="isLoading">
                    <span clas="indicator-progress" [style.display]="'block'">
                      Please wait...{{ " " }}
                      <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                  </ng-container>
                </button>
              </div>
            </div>
          </div>
        </div>



      </form>
    </div>
  </div>
</div>
