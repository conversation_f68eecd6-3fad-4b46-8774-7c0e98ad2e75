import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { EmailService } from '../../email.service';
 

@Component({
    selector: 'app-email-template-view',
    templateUrl: './email-template-view.component.html',
    styleUrls: ['./email-template-view.component.scss'],
    standalone: false
})
export class EmailTemplateViewComponent implements OnInit, OnDestroy {
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: EmailService,
    private route: ActivatedRoute,
    private router: Router) {
    this.createForm();

    this.route.paramMap.subscribe(p => {
      const id = p.get('id');
      if (id) {
        this.subscriptions.add(myService.templatedetails(id).subscribe(r => {
          if (r.success) {
            this.createForm(r.data);
            this.cdk.detectChanges();
          }
        }));
      } else {
        this.createForm()
      }
    });

  }
  public createForm(model: any = null) {
    this.newdata = this.fb.group({
      id: model?.id,
      name: [model?.name, Validators.required],
      template: [model?.template]
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {


  }

  save() {
    if (this.newdata.valid) {
      let form = this.newdata.value
      if (form.id) {
        this.subscriptions.add(this.myService.updatetemplate(form.id, form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/email_template']);
            }
          }));
      } else {
        form.id = 0
        this.subscriptions.add(this.myService.createtemplate(form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/email_template']);
            }
          }));
      }
    }
    else {
      this.newdata.markAllAsTouched();
    }
  }

  
}
