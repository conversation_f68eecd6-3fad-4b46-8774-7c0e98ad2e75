import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {EmailTemplateComponent} from './email-template/email-template.component';
import {EmailInstanceComponent} from './email-instance/email-instance.component';

import {EmailTemplateViewComponent} from './email-template/email-template-view/email-template-view.component';
import {EmailInstanceViewComponent} from './email-instance/email-instance-view/email-instance-view.component';
import {CrmEmailRoutingModule} from "./crm-email-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {DxScrollViewModule} from "devextreme-angular";

@NgModule({
    declarations: [
        EmailInstanceComponent,
        EmailTemplateComponent,
        EmailTemplateViewComponent,
        EmailInstanceViewComponent

    ],
    imports: [CrmEmailRoutingModule, SharedModule, DxScrollViewModule],
    exports: [RouterModule],
})
export class CrmEmailModule {
}
