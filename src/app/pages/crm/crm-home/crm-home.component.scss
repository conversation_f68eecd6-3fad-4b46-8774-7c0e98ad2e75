:host {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

// Dashboard specific styles
.card {
  box-shadow: 0px 0px 20px 0px rgba(76, 87, 125, 0.02);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0px 0px 30px 0px rgba(76, 87, 125, 0.05);
  }
}

// Timeline styles
.timeline-label {
  position: relative;
  margin-top: 1.5rem;

  &:before {
    content: '';
    position: absolute;
    left: 8px;
    width: 2px;
    top: 0;
    bottom: 0;
    background-color: #eff2f5;
  }

  .timeline-item {
    display: flex;
    align-items: flex-start;
    position: relative;
    margin-bottom: 1.7rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timeline-label {
    width: 50px;
    flex-shrink: 0;
    position: relative;
    color: #3f4254;
  }

  .timeline-badge {
    flex-shrink: 0;
    position: relative;
    z-index: 1;
    margin-top: 2px;
  }

  .timeline-content {
    flex-grow: 1;
    margin-left: 0.5rem;
  }
}

// Chart placeholder
.chart-placeholder {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(to right, #009ef7, #50cd89);
  position: relative;

  &:before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background-color: #ffffff;
    border-radius: 50%;
  }
}

// Quick actions
.symbol-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #3f4254;
  background-color: #f5f8fa;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  border-radius: 0.475rem;
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .col-xxl-6 {
    margin-top: 1rem;
  }
}
