import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { CRMService } from '../crm.service';

@Component({
    selector: 'app-crm-home',
    templateUrl: './crm-home.component.html',
    styleUrls: ['./crm-home.component.scss'],
    standalone: false
})
export class crmHomeComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription = new Subscription();

  // Dashboard data
  totalCustomers: number = 1358;
  customerGrowthRate: number = 12.4;
  activeLeads: number = 357;
  leadConversionRate: number = 28.5;
  messagesSent: number = 2450;
  customerSatisfaction: number = 92;

  // Communication channels distribution
  whatsappPercentage: number = 65;
  smsPercentage: number = 23;
  emailPercentage: number = 12;

  // Recent activities
  recentActivities: any[] = [];

  // Upcoming tasks
  upcomingTasks: any[] = [];

  constructor(
    private crmService: CRMService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
    this.initCharts();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Load all dashboard data
   */
  loadDashboardData(): void {
    this.loadCustomerStats();
    this.loadLeadStats();
    this.loadCommunicationStats();
    this.loadRecentActivities();
    this.loadUpcomingTasks();
  }

  /**
   * Load customer statistics
   */
  loadCustomerStats(): void {
    // In a real application, this would fetch data from the API
    // For now, we're using static data
    this.totalCustomers = 1358;
    this.customerGrowthRate = 12.4;
    this.updateDOMElement('totalCustomers', this.totalCustomers.toString());
    this.cdr.detectChanges();
  }

  /**
   * Load lead statistics
   */
  loadLeadStats(): void {
    // In a real application, this would fetch data from the API
    // For now, we're using static data
    this.activeLeads = 357;
    this.leadConversionRate = 28.5;
    this.updateDOMElement('activeLeads', this.activeLeads.toString());
    this.cdr.detectChanges();
  }

  /**
   * Load communication statistics
   */
  loadCommunicationStats(): void {
    // In a real application, this would fetch data from the API
    // For now, we're using static data
    this.messagesSent = 2450;
    this.whatsappPercentage = 65;
    this.smsPercentage = 23;
    this.emailPercentage = 12;
    this.updateDOMElement('messagesSent', this.messagesSent.toString());
    this.cdr.detectChanges();
  }

  /**
   * Load recent activities
   */
  loadRecentActivities(): void {
    // In a real application, this would fetch data from the API
    // For now, we're using static data
    this.recentActivities = [
      {
        time: '10:00',
        activity: 'New Lead Created',
        details: 'By User',
        status: 'success'
      },
      {
        time: '12:45',
        activity: 'Customer Meeting',
        details: 'With Client',
        status: 'warning'
      },
      {
        time: '14:30',
        activity: 'Proposal Sent',
        details: 'To Client',
        status: 'primary'
      },
      {
        time: '16:50',
        activity: 'New Enquiry Received',
        details: 'Via Website',
        status: 'info'
      }
    ];
    this.cdr.detectChanges();
  }

  /**
   * Load upcoming tasks
   */
  loadUpcomingTasks(): void {
    // In a real application, this would fetch data from the API
    // For now, we're using static data
    this.upcomingTasks = [
      {
        title: 'Follow up with potential client',
        date: 'Tomorrow',
        status: 'pending'
      },
      {
        title: 'Prepare sales presentation',
        date: 'In 2 days',
        status: 'in-progress'
      },
      {
        title: 'Client meeting',
        date: 'Next week',
        status: 'scheduled'
      }
    ];
    this.cdr.detectChanges();
  }

  /**
   * Initialize charts
   */
  initCharts(): void {
    // In a real application, this would initialize charts
    // For now, we're just setting up a placeholder
    setTimeout(() => {
      this.initMessagesChart();
    }, 300);
  }

  /**
   * Initialize messages chart
   */
  initMessagesChart(): void {
    const element = document.getElementById('messagesChart');
    if (!element) {
      return;
    }

    // In a real application, this would initialize a chart
    // For now, we're just adding a placeholder class
    element.classList.add('chart-placeholder');
  }

  /**
   * Update DOM element with value
   */
  private updateDOMElement(id: string, value: string): void {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  }
}
