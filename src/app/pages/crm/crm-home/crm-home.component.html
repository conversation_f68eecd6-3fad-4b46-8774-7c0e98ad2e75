
<!-- CRM Dashboard Header -->
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-9 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="me-7 mb-4">
        <div class="symbol symbol-60px symbol-lg-80px symbol-fixed position-relative">
          <img src="./assets/media/svg/brand-logos/crm-icon.svg" alt="CRM" style="max-width: 100%; max-height: 100%;" />
        </div>
      </div>
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex flex-column">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">{{ 'COMMON.CRMDashboard' | translate }}</span>
              <span class="badge badge-light-success fw-bold fs-8 px-2 py-1 ms-2">{{ 'COMMON.Active' | translate }}</span>
            </div>
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
              <span class="d-flex align-items-center text-gray-400 me-5 mb-2">
                <i class="fa fa-chart-line me-1 fs-4 text-primary"></i>{{ 'COMMON.CustomerRelationshipManagement' | translate }}
              </span>
            </div>
          </div>
          <div class="d-flex my-4">
            <a href="#" class="btn btn-sm btn-primary me-3" data-bs-toggle="modal" data-bs-target="#kt_modal_create_campaign">{{ 'COMMON.NewCampaign' | translate }}</a>
            <div class="me-0">
              <button class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                <i class="fa fa-ellipsis-h fs-3"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Key Metrics Row -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Total Customers Card -->
  <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="totalCustomers">1,358</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.TotalCustomers' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex align-items-end pt-0">
        <div class="d-flex align-items-center flex-column mt-3 w-100">
          <div class="d-flex justify-content-between w-100 mt-auto mb-2">
            <span class="fw-bold fs-6 text-gray-400">{{ 'COMMON.GrowthRate' | translate }}</span>
            <span class="fw-bolder fs-6 text-primary">+12.4%</span>
          </div>
          <div class="h-8px mx-3 w-100 bg-light-primary rounded">
            <div class="bg-primary rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="72" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Leads Card -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="activeLeads">357</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.ActiveLeads' | translate }}</span>
        </div>
      </div>
      <div class="card-body d-flex align-items-end pt-0">
        <div class="d-flex align-items-center flex-column mt-3 w-100">
          <div class="d-flex justify-content-between w-100 mt-auto mb-2">
            <span class="fw-bold fs-6 text-gray-400">{{ 'COMMON.ConversionRate' | translate }}</span>
            <span class="fw-bolder fs-6 text-success">+28.5%</span>
          </div>
          <div class="h-8px mx-3 w-100 bg-light-success rounded">
            <div class="bg-success rounded h-8px" role="progressbar" style="width: 28.5%;" aria-valuenow="28.5" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Communication Stats -->
  <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
    <!-- Messages Sent Card -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2" id="messagesSent">2,450</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.MessagesSent' | translate }}</span>
        </div>
      </div>
      <div class="card-body pt-2 pb-4 d-flex align-items-center">
        <div class="d-flex flex-center me-5 pt-2">
          <div id="messagesChart" style="min-width: 70px; min-height: 70px" data-kt-size="70" data-kt-line="11"></div>
        </div>
        <div class="d-flex flex-column content-justify-center w-100">
          <div class="d-flex fs-6 fw-semibold align-items-center">
            <div class="bullet w-8px h-6px rounded-2 bg-primary me-3"></div>
            <div class="text-gray-500 flex-grow-1 me-4">{{ 'COMMON.WhatsApp' | translate }}</div>
            <div class="fw-bolder text-gray-700 text-end">65%</div>
          </div>
          <div class="d-flex fs-6 fw-semibold align-items-center my-3">
            <div class="bullet w-8px h-6px rounded-2 bg-success me-3"></div>
            <div class="text-gray-500 flex-grow-1 me-4">{{ 'COMMON.SMS' | translate }}</div>
            <div class="fw-bolder text-gray-700 text-end">23%</div>
          </div>
          <div class="d-flex fs-6 fw-semibold align-items-center">
            <div class="bullet w-8px h-6px rounded-2 bg-info me-3"></div>
            <div class="text-gray-500 flex-grow-1 me-4">{{ 'COMMON.Email' | translate }}</div>
            <div class="fw-bolder text-gray-700 text-end">12%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Satisfaction Card -->
    <div class="card card-flush h-md-50 mb-5 mb-xl-10">
      <div class="card-header pt-5">
        <div class="card-title d-flex flex-column">
          <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">92%</span>
          <span class="text-gray-400 pt-1 fw-semibold fs-6">{{ 'COMMON.CustomerSatisfaction' | translate }}</span>
        </div>
      </div>
      <div class="card-body pt-2 pb-4">
        <div class="d-flex flex-wrap align-items-center mt-5">
          <div class="d-flex flex-column me-7 me-lg-16 pt-1">
            <div class="d-flex align-items-center mb-2">
              <span class="fs-4 fw-semibold text-gray-400 me-1">{{ 'COMMON.Satisfied' | translate }}</span>
              <span class="fs-4 fw-bold text-gray-800">92%</span>
            </div>
            <div class="progress h-7px bg-success bg-opacity-50 mt-2">
              <div class="progress-bar bg-success" role="progressbar" style="width: 92%" aria-valuenow="92" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
          <div class="d-flex flex-column pt-1">
            <div class="d-flex align-items-center mb-2">
              <span class="fs-4 fw-semibold text-gray-400 me-1">{{ 'COMMON.Unsatisfied' | translate }}</span>
              <span class="fs-4 fw-bold text-gray-800">8%</span>
            </div>
            <div class="progress h-7px bg-danger bg-opacity-50 mt-2">
              <div class="progress-bar bg-danger" role="progressbar" style="width: 8%" aria-valuenow="8" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Customer Engagement & Activity -->
  <div class="col-xxl-6 mb-5 mb-xl-10">
    <div class="card card-flush h-md-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">{{ 'COMMON.CustomerEngagement' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.Last30DaysCRM' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <div class="d-flex flex-stack flex-wrap gap-4">
            <div class="d-flex align-items-center fw-bold">
              <div class="text-muted fs-7 me-2">{{ 'COMMON.Created' | translate }}</div>
              <div class="d-flex">
                <select class="form-select form-select-transparent text-dark fs-7 lh-1 fw-bold py-0 ps-3 w-auto" data-control="select2" data-hide-search="true" data-dropdown-css-class="w-150px" data-placeholder="Select an option">
                  <option value="Show All" selected>{{ 'COMMON.ShowAll' | translate }}</option>
                  <option value="Today">{{ 'COMMON.Today' | translate }}</option>
                  <option value="Yesterday">{{ 'COMMON.Yesterday' | translate }}</option>
                  <option value="Last 7 Days">{{ 'COMMON.Last7Days' | translate }}</option>
                  <option value="Last 30 Days">{{ 'COMMON.Last30DaysCRM' | translate }}</option>
                  <option value="Last Month">{{ 'COMMON.LastMonth' | translate }}</option>
                </select>
              </div>
            </div>
            <button type="button" class="btn btn-light-primary btn-sm">{{ 'COMMON.ViewDetails' | translate }}</button>
          </div>
        </div>
      </div>
      <div class="card-body d-flex flex-column pt-5">
        <div class="mb-0">
          <app-new-charts-widget8 chartHeight="275px" [chartHeightNumber]="275"></app-new-charts-widget8>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activities & Quick Actions -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Recent Activities -->
  <div class="col-xl-8">
    <div class="card card-flush h-xl-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.RecentActivitiesCRM' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.LatestCustomerInteractions' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="fa fa-ellipsis-h fs-3"></i>
          </button>
        </div>
      </div>
      <div class="card-body pt-5">
        <div class="timeline-label">
          <!-- Timeline Item 1 -->
          <div class="timeline-item">
            <div class="timeline-label fw-bold text-gray-800 fs-6">10:00</div>
            <div class="timeline-badge">
              <i class="fa fa-genderless text-success fs-1"></i>
            </div>
            <div class="timeline-content d-flex">
              <span class="fw-bold text-gray-800 ps-3">{{ 'COMMON.NewLeadCreated' | translate }}</span>
              <span class="text-muted ms-2">- {{ 'COMMON.ByUser' | translate }}</span>
            </div>
          </div>
          <!-- Timeline Item 2 -->
          <div class="timeline-item">
            <div class="timeline-label fw-bold text-gray-800 fs-6">12:45</div>
            <div class="timeline-badge">
              <i class="fa fa-genderless text-warning fs-1"></i>
            </div>
            <div class="timeline-content d-flex">
              <span class="fw-bold text-gray-800 ps-3">{{ 'COMMON.CustomerMeeting' | translate }}</span>
              <span class="text-muted ms-2">- {{ 'COMMON.WithClient' | translate }}</span>
            </div>
          </div>
          <!-- Timeline Item 3 -->
          <div class="timeline-item">
            <div class="timeline-label fw-bold text-gray-800 fs-6">14:30</div>
            <div class="timeline-badge">
              <i class="fa fa-genderless text-primary fs-1"></i>
            </div>
            <div class="timeline-content d-flex">
              <span class="fw-bold text-gray-800 ps-3">{{ 'COMMON.ProposalSent' | translate }}</span>
              <span class="text-muted ms-2">- {{ 'COMMON.ToClient' | translate }}</span>
            </div>
          </div>
          <!-- Timeline Item 4 -->
          <div class="timeline-item">
            <div class="timeline-label fw-bold text-gray-800 fs-6">16:50</div>
            <div class="timeline-badge">
              <i class="fa fa-genderless text-info fs-1"></i>
            </div>
            <div class="timeline-content d-flex">
              <span class="fw-bold text-gray-800 ps-3">{{ 'COMMON.NewEnquiryReceived' | translate }}</span>
              <span class="text-muted ms-2">- {{ 'COMMON.ViaWebsite' | translate }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer pt-5">
        <a href="#" class="btn btn-primary btn-sm me-2">{{ 'COMMON.ViewAll' | translate }}</a>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="col-xl-4">
    <div class="card card-flush h-xl-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.QuickActions' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.CommonTasks' | translate }}</span>
        </h3>
      </div>
      <div class="card-body">
        <div class="d-flex flex-column gap-5">
          <a href="#" class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-primary">
                <i class="fa fa-user-plus text-primary fs-2x"></i>
              </div>
            </div>
            <div class="d-flex flex-column">
              <div class="fw-bold fs-6">{{ 'COMMON.AddNewLead' | translate }}</div>
              <div class="fs-7 text-muted">{{ 'COMMON.CreateNewLeadRecord' | translate }}</div>
            </div>
          </a>
          <a href="#" class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-warning">
                <i class="fa fa-calendar-check text-warning fs-2x"></i>
              </div>
            </div>
            <div class="d-flex flex-column">
              <div class="fw-bold fs-6">{{ 'COMMON.ScheduleMeeting' | translate }}</div>
              <div class="fs-7 text-muted">{{ 'COMMON.ArrangeCustomerMeeting' | translate }}</div>
            </div>
          </a>
          <a href="#" class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-success">
                <i class="fa fa-paper-plane text-success fs-2x"></i>
              </div>
            </div>
            <div class="d-flex flex-column">
              <div class="fw-bold fs-6">{{ 'COMMON.SendCampaign' | translate }}</div>
              <div class="fs-7 text-muted">{{ 'COMMON.CreateMarketingCampaign' | translate }}</div>
            </div>
          </a>
          <a href="#" class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-info">
                <i class="fa fa-chart-pie text-info fs-2x"></i>
              </div>
            </div>
            <div class="d-flex flex-column">
              <div class="fw-bold fs-6">{{ 'COMMON.GenerateReportsCRM' | translate }}</div>
              <div class="fs-7 text-muted">{{ 'COMMON.CreateCustomReports' | translate }}</div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Upcoming Tasks & Deals -->
<div class="row g-5 g-xl-10 mb-5 mb-xl-10">
  <!-- Upcoming Tasks -->
  <div class="col-xl-6">
    <div class="card card-flush h-xl-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.UpcomingTasks' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.ScheduledActivities' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewAll' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-lists-widget4 class="card-xl-stretch"></app-lists-widget4>
      </div>
    </div>
  </div>

  <!-- Deals Pipeline -->
  <div class="col-xl-6">
    <div class="card card-flush h-xl-100">
      <div class="card-header pt-7">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-gray-800">{{ 'COMMON.DealsPipeline' | translate }}</span>
          <span class="text-gray-400 mt-1 fw-semibold fs-6">{{ 'COMMON.SalesForecast' | translate }}</span>
        </h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-light">{{ 'COMMON.ViewDetails' | translate }}</button>
        </div>
      </div>
      <div class="card-body pt-5">
        <app-mixed-widget8 chartColor="primary" chartHeight="200px"></app-mixed-widget8>
      </div>
    </div>
  </div>
</div>
