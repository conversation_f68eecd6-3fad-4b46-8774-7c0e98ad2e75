import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CommercialActivitiesComponent} from './commercial-activities/commercial-activities.component';
import {CustomerCategoriesComponent} from './customer-categories/customer-categories.component';
import {SectorComponent} from './sector/sector.component';
import {StagesComponent} from './stages/stages.component';


const routes: Routes = [

    {
        path: '',
        redirectTo: 'sales_teams',
        pathMatch: 'full'
    },
    {
        path: 'commercial_activities',
        component: CommercialActivitiesComponent
    },
    {
        path: 'customer_categories',
        component: CustomerCategoriesComponent
    },
    {
        path: 'stages',
        component: StagesComponent
    },
    {
        path: 'sectors',
        component: SectorComponent
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CrmConfigurationRoutingModule {
}
