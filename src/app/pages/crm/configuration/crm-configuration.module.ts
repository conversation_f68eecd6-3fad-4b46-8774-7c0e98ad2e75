import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import {CommercialActivitiesComponent} from './commercial-activities/commercial-activities.component';
import {CustomerCategoriesComponent} from './customer-categories/customer-categories.component';
import {SectorComponent} from './sector/sector.component';
import {StagesComponent} from './stages/stages.component';
import {CrmConfigurationRoutingModule} from "./crm-configuration-routing.module";
import {SharedModule} from "../../shared/shared.module";
import {DxScrollViewModule} from "devextreme-angular";

@NgModule({
  declarations: [
    CommercialActivitiesComponent,
    CustomerCategoriesComponent,
    SectorComponent,
    StagesComponent,
  ],
  imports: [CrmConfigurationRoutingModule, SharedModule,DxScrollViewModule],
  exports: [RouterModule],
})
export class CrmConfigurationModule {}
