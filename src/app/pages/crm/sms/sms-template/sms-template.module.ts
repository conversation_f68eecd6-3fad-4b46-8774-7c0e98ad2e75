import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { DxDataGridModule } from 'devextreme-angular';
import { SmsTemplateComponent } from './sms-template.component';
import { SmsTemplateViewComponent } from './sms-template-view/sms-template-view.component';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

const routes = [
  {
    path: '',
    component: SmsTemplateComponent
  },
  {
    path: 'view',
    component: SmsTemplateViewComponent
  },
  {
    path: 'view/:id',
    component: SmsTemplateViewComponent
  }
];

@NgModule({
  declarations: [
    SmsTemplateComponent,
    SmsTemplateViewComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    TranslateModule,
    NgSelectModule,
    DxDataGridModule,
    ReactiveFormsModule
  ]
})
export class SMSTemplateModule { }
