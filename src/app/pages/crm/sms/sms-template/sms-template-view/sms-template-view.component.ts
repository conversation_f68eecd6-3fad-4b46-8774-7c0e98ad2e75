import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { SMSService } from '../../sms.service';


@Component({
    selector: 'app-sms-template-view',
    templateUrl: './sms-template-view.component.html',
    styleUrls: ['./sms-template-view.component.scss'],
    standalone: false
})
export class SmsTemplateViewComponent implements OnInit, OnDestroy {
  subscriptions = new Subscription();
  newdata: FormGroup;
  isLoading = false;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: SMSService,
    private route: ActivatedRoute,
    private router: Router) {
    this.createForm();

    this.route.paramMap.subscribe(p => {
      const id = p.get('id');
      if (id) {
        this.subscriptions.add(myService.get_template_details(id).subscribe(r => {
          if (r.success) {
            this.createForm(r.data);
            this.cdk.detectChanges();
          }
        }));
      } else {
        this.createForm()
      }
    });

  }
  public createForm(model: any = null) {
    this.newdata = this.fb.group({
      id: model?.id,
      name: [model?.name, Validators.required],
      template: [model?.template]
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {


  }
  save() {
    if (this.newdata.valid) {
      let form = this.newdata.value
      if (form.id) {
        this.subscriptions.add(this.myService.updatetemplate(form.id, form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/sms/template']);
            }
          }));
      } else {
        form.id = 0
        this.subscriptions.add(this.myService.createtemplate(form)
          .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
          .subscribe(r => {
            if (r.success) {
              this.router.navigate(['/crm/sms/template']);
            }
          }));
      }
    }
    else {
      this.newdata.markAllAsTouched();
    }
  }
}

