import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { DxDataGridModule } from 'devextreme-angular';
import { SmsInstanceComponent } from './sms-instance.component';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';

const routes = [
  {
    path: '',
    component: SmsInstanceComponent
  }
]

@NgModule({
  declarations: [
    SmsInstanceComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NgSelectModule,
    DxDataGridModule,
    ReactiveFormsModule,
    TranslateModule,

  ]
})
export class SmsInstanceModule { }
