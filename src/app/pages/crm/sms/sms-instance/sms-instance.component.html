 
<p>
  <!--<PERSON>.DeleteByMultiWHERE_Record("Sms_Settings", $"CaseID = {cmbProvider.SelectedIndex}")

  DB.Save_Record_WithMessage("Sms_Settings", "CaseID,UserName,Password,SenderName,<PERSON>fulatNum<PERSON>,IsMain", $"{cmbProvider.SelectedIndex},'{Smsuser.Text}','{SmsPass.Text}','{MobilySender.Text}','{DeFaultSMSNO.Text}','{CBool(CheckBox1.Checked)}'")-->
</p>
<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
       data-bs-target="#kt_account_connected_accounts" aria-expanded="true"
       aria-controls="kt_account_connected_accounts">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{'COMMON.SMSInstance' | translate }}</h3>
    </div>
  </div>
  <div class="card-body border-top p-9">
    <div class="row">
      <form [formGroup]="myForm" class="form" action="#" id="kt_modal_add_company_form"
            data-kt-redirect="../../demo1/dist/apps/Companies/list.html">
        <div class="me-n7 pe-7" id="kt_modal_add_company">


          <div class="row g-9 mb-7">
            <!--begin::Col-->
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2">{{ 'COMMON.ServiceProvider' |translate}}</label>
              
              <ng-select (change)="getInstance()" formControlName="caseID" bindLabel="value" bindValue="key" [items]="providers"></ng-select>

            </div>
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2"> {{ 'COMMON.Default' |translate}} </label>
              <br/>
              <input class="form-check-input" type="checkbox" [value]="0" formControlName="isMain" />
            </div>
          </div>

          

          <div class="row g-9 mb-7">
            <!--begin::Col-->
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2">{{ 'COMMON.UserName' |translate}}</label>
              <input type="text" class="form-control form-control-solid" placeholder="User Name"
                     name="userName" value="" formControlName="userName" />
            </div>
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2">{{ 'COMMON.Password' |translate}}</label>
              <input type="text" class="form-control form-control-solid" placeholder="Password" name="password"
                     value="" formControlName="password" />
            </div>
          </div>

          <div class="row g-9 mb-7">
            <!--begin::Col-->
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2">{{ 'COMMON.SenderName' |translate}}</label>
              <input type="text" class="form-control form-control-solid" placeholder="Sender Name"
                     name="senderName" value="" formControlName="senderName" />
            </div>
            <div class="col-md-6 fv-row">
              <label class="required fs-6 fw-semibold mb-2">{{ 'COMMON.DefulatNumbers' |translate}}</label>
              <input type="text" class="form-control form-control-solid" placeholder="Defulat Numbers" name="defulatNumbers"
                     value="" formControlName="defulatNumbers" />
            </div>
          </div>

        </div>

        <div class="row ">
          <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary" [disabled]="isLoading" (click)="save()">
              <ng-container *ngIf="!isLoading">{{ 'COMMON.SaveChanges' |translate}}</ng-container>
              <ng-container *ngIf="isLoading">
                <span clas="indicator-progress" [style.display]="'block'">
                  Please wait...{{ " " }}
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
              </ng-container>
            </button>
          </div>
        </div>

      </form>
    </div>

  </div>
</div>
