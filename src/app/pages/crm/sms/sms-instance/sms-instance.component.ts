import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';
import { SMSService } from '../sms.service';


@Component({
    selector: 'app-sms-instance',
    templateUrl: './sms-instance.component.html',
    styleUrls: ['./sms-instance.component.scss'],
    standalone: false
})
export class SmsInstanceComponent implements OnInit, OnDestroy {
  providers: any[] = [{ key: 0, value: "Mobily" }, { key: 1, value: "UltrMsg" }, { key: 2, value: "Jawa<PERSON>" }, { key: 3, value: "Yamamah" }, { key: 4, value: "Hisms" }, { key: 5, value: "Msegat" }, { key: 6, value: "Oursms" }, { key: 7, value: "Unifonic" }, { key: 8, value: "Zain" }];
  SalespersonCategories: any[] = [];
  subscriptions = new Subscription();
  myForm: FormGroup;
  isLoading = false;
  constructor(private fb: FormBuilder,
    private cdk: ChangeDetectorRef,
    private myService: SMSService,
    private route: ActivatedRoute,
    private router: Router) {
    this.createForm();




  }

  getInstance() {
    const id = this.myForm.get('caseID')?.value;
    this.subscriptions.add(this.myService.getinstance(id).subscribe(r => {
      if (r.success) {
        this.createForm(r.data);
        this.cdk.detectChanges();
      } else {
        this.myForm.reset();
        this.myForm.get('caseID')?.setValue(id);
      }
    }));
  }
  private createForm(model: any = null) {
    this.myForm = this.fb.group({
      userName: [model?.userName, Validators.required],
      password: [model?.password, Validators.required],
      senderName: [model?.senderName, Validators.required],
      defulatNumbers: [model?.defulatNumbers],
      isMain: [model?.isMain],
      caseID: [model?.caseID, Validators.required],
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  ngOnInit(): void {


  }
  save() {
    if (this.myForm.valid) {
      let form = this.myForm.value
      this.subscriptions.add(this.myService.createinstance(form)
        .pipe(finalize(() => { this.isLoading = false; this.cdk.detectChanges(); }))
        .subscribe(r => {

        }));
    } else {
      this.myForm.markAllAsTouched();
    }
  }
}
