import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {SendSmsComponent} from "../../shared/send-sms/send-sms/send-sms.component";

const routes: Routes = [

  {
    path: 'instance',
    loadChildren: () => import('./sms-instance/sms-instance.module').then(t => t.SmsInstanceModule)
  },
  {
    path: 'send',
    component: SendSmsComponent
  },
  {
    path: 'template',
    loadChildren: () => import('./sms-template/sms-template.module').then(t => t.SMSTemplateModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SMSRoutingModule { }
