import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule } from '@angular/forms';
import { DxDataGridModule } from 'devextreme-angular';
import { SendSmsComponent } from './send-sms.component';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

const routes = [
  {
    path: '',
    component: SendSmsComponent
  }
]
@NgModule({
  declarations: [
    SendSmsComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NgSelectModule,
    DxDataGridModule,
    ReactiveFormsModule,
    TranslateModule
  ]
})
export class SendSmsModule { }
