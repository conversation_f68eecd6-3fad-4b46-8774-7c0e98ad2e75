import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { TokenKeys } from 'src/app/modules/auth/services';
import { TokenService } from 'src/app/modules/auth/services/token.service';

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: '[hasPermission]',
    standalone: false
})
export class HasPermissionDirective {
  private hasView = false;

  constructor(private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef) { }

  @Input() set hasPermission(obj: any) {
    const action = obj.action;
    const module = obj.module.toLowerCase();
    const storedPermissions = localStorage.getItem(TokenKeys.permissions);
    const permissions = storedPermissions ? JSON.parse(storedPermissions) : [];
    let userPermission;
    if(action == null) {
      userPermission = permissions.find((p: any) => p.module.split('.')[0].toLowerCase() == module) !== undefined;
    } else {
      const role = permissions.find((p: any) => p.module.toLowerCase() == module);
      userPermission = role[action] !== undefined;
    }
    if (userPermission) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!userPermission) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }
}
