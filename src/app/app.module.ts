import { NgModule, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
// import { HttpClientInMemoryWebApiModule } from 'angular-in-memory-web-api';
import { ClipboardModule } from 'ngx-clipboard';
import { TranslateModule } from '@ngx-translate/core';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AuthService } from './modules/auth/services/auth.service';
import { httpInterceptorProviders } from './http-interceptors';
import { ToastrModule } from 'ngx-toastr';
import { DefaultUrlSerializer, UrlSerializer, UrlTree } from '@angular/router';
import { LicenseService } from './license.service'; // استيراد خدمة الترخيص
import { AgGridModule } from 'ag-grid-angular'; // إضافة AG Grid
import { FormsModule } from '@angular/forms'; // إضافة FormsModule

export class LowerCaseUrlSerializer extends DefaultUrlSerializer {
  parse(url: string): UrlTree {
    return super.parse(url.toLowerCase());
  }
}

function appInitializer(authService: AuthService) {
  return () => {
    return new Promise((resolve) => {
      //@ts-ignore
      authService.getUserByToken().subscribe().add(resolve);
    });
  };
}

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    TranslateModule.forRoot(),
    ClipboardModule,
    AppRoutingModule,
    InlineSVGModule.forRoot(),
    NgbModule,
    ToastrModule.forRoot(),
    AgGridModule, // إضافة AG Grid
    FormsModule, // إضافة FormsModule
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializer,
      multi: true,
      deps: [AuthService],
    },
    // إضافة خدمة الترخيص
    {
      provide: APP_INITIALIZER,
      useFactory: (licenseService: LicenseService) => () => {
        // ضمان تحميل خدمة الترخيص عند بدء التطبيق
        return Promise.resolve();
      },
      multi: true,
      deps: [LicenseService],
    },
    httpInterceptorProviders,
    {
      provide: UrlSerializer,
      useClass: LowerCaseUrlSerializer,
    },
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {}
