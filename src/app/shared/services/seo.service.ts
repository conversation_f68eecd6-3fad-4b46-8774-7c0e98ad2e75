import { Injectable } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  author?: string;
  url?: string;
  image?: string;
  type?: string;
  locale?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SeoService {

  constructor(
    private meta: Meta,
    private title: Title
  ) { }

  updateSEOData(data: SEOData): void {
    // Set page title
    this.title.setTitle(data.title);

    // Basic meta tags
    this.meta.updateTag({ name: 'description', content: data.description });
    
    if (data.keywords) {
      this.meta.updateTag({ name: 'keywords', content: data.keywords });
    }
    
    if (data.author) {
      this.meta.updateTag({ name: 'author', content: data.author });
    }

    // Open Graph tags
    this.meta.updateTag({ property: 'og:title', content: data.title });
    this.meta.updateTag({ property: 'og:description', content: data.description });
    
    if (data.url) {
      this.meta.updateTag({ property: 'og:url', content: data.url });
    }
    
    if (data.image) {
      this.meta.updateTag({ property: 'og:image', content: data.image });
    }
    
    if (data.type) {
      this.meta.updateTag({ property: 'og:type', content: data.type });
    }
    
    if (data.locale) {
      this.meta.updateTag({ property: 'og:locale', content: data.locale });
    }

    // Twitter Card tags
    this.meta.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:title', content: data.title });
    this.meta.updateTag({ name: 'twitter:description', content: data.description });
    
    if (data.image) {
      this.meta.updateTag({ name: 'twitter:image', content: data.image });
    }

    // Additional SEO tags
    this.meta.updateTag({ name: 'robots', content: 'index, follow' });
    this.meta.updateTag({ name: 'language', content: 'Arabic' });
    this.meta.updateTag({ name: 'geo.region', content: 'EG' });
    this.meta.updateTag({ name: 'geo.country', content: 'Egypt' });
  }

  // Predefined SEO data for common pages
  getHomePageSEO(): SEOData {
    return {
      title: 'فالكون ERP - نظام إدارة موارد المؤسسات الشامل | الصفحة الرئيسية',
      description: 'نظام فالكون ERP الشامل لإدارة موارد المؤسسات. حلول متكاملة للمحاسبة والمخزون والمبيعات والمشتريات والموارد البشرية. أكثر من 1000 عميل يثقون بنا.',
      keywords: 'فالكون ERP, نظام ERP, إدارة موارد المؤسسات, محاسبة, مخزون, مبيعات, مشتريات, موارد بشرية, نظام محاسبي, برنامج إدارة, حلول الأعمال',
      author: 'Falcon ERP Team',
      url: 'https://falcon-v.com/web/home',
      image: 'https://falcon-v.com/assets/media/logos/falconerp-social.png',
      type: 'website',
      locale: 'ar_EG'
    };
  }

  getAboutPageSEO(): SEOData {
    return {
      title: 'من نحن - فالكون ERP | تعرف على قصتنا ورؤيتنا',
      description: 'تعرف على شركة فالكون ERP، رائدة في مجال حلول إدارة موارد المؤسسات. أكثر من 10 سنوات من الخبرة في تطوير أنظمة ERP المتطورة للشركات والمؤسسات.',
      keywords: 'من نحن فالكون ERP, شركة فالكون, تاريخ الشركة, رؤية فالكون, مهمة الشركة, فريق العمل, خبرة ERP, تطوير الأنظمة',
      author: 'Falcon ERP Team',
      url: 'https://falcon-v.com/web/about',
      image: 'https://falcon-v.com/assets/media/logos/falconerp-social.png',
      type: 'website',
      locale: 'ar_EG'
    };
  }

  getContactPageSEO(): SEOData {
    return {
      title: 'اتصل بنا - فالكون ERP | تواصل معنا الآن',
      description: 'تواصل مع فريق فالكون ERP. نحن هنا لمساعدتك في جميع استفساراتك حول نظام إدارة موارد المؤسسات. اتصل بنا على 201113105777 أو راسلنا على <EMAIL>',
      keywords: 'اتصل بنا فالكون ERP, تواصل معنا, دعم فني, خدمة العملاء, رقم الهاتف, البريد الإلكتروني, العنوان, مدينة السادات',
      author: 'Falcon ERP Team',
      url: 'https://falcon-v.com/web/contact',
      image: 'https://falcon-v.com/assets/media/logos/falconerp-social.png',
      type: 'website',
      locale: 'ar_EG'
    };
  }

  getServicesPageSEO(): SEOData {
    return {
      title: 'خدماتنا - فالكون ERP | حلول شاملة لإدارة الأعمال',
      description: 'اكتشف خدمات فالكون ERP الشاملة: نظام المحاسبة، إدارة المخزون، المبيعات والمشتريات، الموارد البشرية، وإدارة المشاريع. حلول متكاملة لجميع احتياجات مؤسستك.',
      keywords: 'خدمات فالكون ERP, نظام محاسبة, إدارة مخزون, نظام مبيعات, نظام مشتريات, موارد بشرية, إدارة مشاريع, حلول الأعمال',
      author: 'Falcon ERP Team',
      url: 'https://falcon-v.com/web/services',
      image: 'https://falcon-v.com/assets/media/logos/falconerp-social.png',
      type: 'website',
      locale: 'ar_EG'
    };
  }

  getStorePageSEO(): SEOData {
    return {
      title: 'متجر فالكون ERP | احصل على النظام الآن',
      description: 'احصل على نظام فالكون ERP بأفضل الأسعار. باقات متنوعة تناسب جميع أحجام الشركات والمؤسسات. ابدأ تجربتك المجانية الآن.',
      keywords: 'متجر فالكون ERP, شراء نظام ERP, أسعار فالكون ERP, باقات ERP, تجربة مجانية, اشتراك فالكون',
      author: 'Falcon ERP Team',
      url: 'https://falcon-v.com/web/store',
      image: 'https://falcon-v.com/assets/media/logos/falconerp-social.png',
      type: 'website',
      locale: 'ar_EG'
    };
  }
}
