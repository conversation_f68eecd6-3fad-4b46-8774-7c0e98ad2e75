import { Component, OnInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-about',
  standalone: false,
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss'
})
export class AboutComponent implements OnInit {

  constructor(
    private meta: Meta,
    private title: Title
  ) {}

  ngOnInit(): void {
    this.setSEOData();
  }

  private setSEOData(): void {
    // Set page title
    this.title.setTitle('من نحن - فالكون ERP | تعرف على قصتنا ورؤيتنا');

    // Set meta description
    this.meta.updateTag({
      name: 'description',
      content: 'تعرف على شركة فالكون ERP، رائدة في مجال حلول إدارة موارد المؤسسات. أكثر من 10 سنوات من الخبرة في تطوير أنظمة ERP المتطورة للشركات والمؤسسات.'
    });

    // Set keywords
    this.meta.updateTag({
      name: 'keywords',
      content: 'من نحن فالكون ERP, شركة فالكون, تاريخ الشركة, رؤية فالكون, مهمة الشركة, فريق العمل, خبرة ERP, تطوير الأنظمة'
    });

    // Open Graph tags
    this.meta.updateTag({
      property: 'og:title',
      content: 'من نحن - فالكون ERP | تعرف على قصتنا ورؤيتنا'
    });

    this.meta.updateTag({
      property: 'og:description',
      content: 'تعرف على شركة فالكون ERP، رائدة في مجال حلول إدارة موارد المؤسسات. أكثر من 10 سنوات من الخبرة في تطوير أنظمة ERP المتطورة.'
    });

    this.meta.updateTag({
      property: 'og:url',
      content: 'https://falcon-v.com/web/about'
    });

    // Twitter Card tags
    this.meta.updateTag({
      name: 'twitter:title',
      content: 'من نحن - فالكون ERP | تعرف على قصتنا ورؤيتنا'
    });

    this.meta.updateTag({
      name: 'twitter:description',
      content: 'تعرف على شركة فالكون ERP، رائدة في مجال حلول إدارة موارد المؤسسات. أكثر من 10 سنوات من الخبرة في تطوير أنظمة ERP المتطورة.'
    });

    // Additional SEO tags
    this.meta.updateTag({
      name: 'robots',
      content: 'index, follow'
    });
  }
}
