/* About Header */
.about-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 80px 0 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-header-content {
  position: relative;
  z-index: 2;
}

.about-header-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.about-header-content p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

.header-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Section Subtitle */
.section-subtitle {
  display: inline-block;
  background-color: rgba(54, 153, 255, 0.1);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

/* About Story */
.about-story {
  padding: 80px 0;
  background-color: white;
}

.about-story-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-image {
  flex: 1;
  position: relative;
  padding: 20px;
  background-color: #3699FF;
  border-radius: 15px;

  &::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100%;
    height: 100%;
    background-color: white;
    border-radius: 15px;
    z-index: 1;
  }

  img {
    max-width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
  }
}

.about-text {
  flex: 1;
}

.about-text h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 20px;
}

.about-text p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 20px;
  line-height: 1.8;
}

/* Our Mission */
.our-mission {
  padding: 80px 0;
  background-color: var(--light-color);
}

.mission-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.mission-text {
  flex: 1;
}

.mission-text h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 20px;
}

.mission-text p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 30px;
  line-height: 1.8;
}

.mission-points {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mission-point {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.point-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(54, 153, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.point-icon i {
  font-size: 20px;
  color: var(--primary-color);
}

.point-text h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.point-text p {
  font-size: 1rem;
  color: var(--text-muted);
  margin-bottom: 0;
  line-height: 1.6;
}

.mission-image {
  flex: 1;
  position: relative;
  padding: 20px;
  background-color: #3699FF;
  border-radius: 15px;

  &::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100%;
    height: 100%;
    background-color: white;
    border-radius: 15px;
    z-index: 1;
  }

  img {
    max-width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
  }
}

/* Our Team */
.our-team {
  padding: 80px 0;
  background-color: white;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-muted);
  max-width: 700px;
  margin: 0 auto;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.team-member {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.member-image {
  position: relative;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
  transform: scale(1.05);
}

.member-social {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.team-member:hover .member-social {
  opacity: 1;
  transform: translateY(0);
}

.member-social a {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-color);
  transition: all 0.3s ease;
}

.member-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-5px);
}

.member-info {
  padding: 20px;
  text-align: center;
}

.member-info h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.member-info p {
  font-size: 1rem;
  color: var(--text-muted);
}

/* Our Values */
.our-values {
  padding: 80px 0;
  background-color: var(--light-color);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.value-card {
  background-color: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.value-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(54, 153, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.value-card:hover .value-icon {
  background-color: var(--primary-color);
}

.value-icon i {
  font-size: 30px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.value-card:hover .value-icon i {
  color: white;
}

.value-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.value-card p {
  color: var(--text-muted);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-section .btn-primary {
  background-color: white;
  color: var(--primary-color);
}

.cta-section .btn-outline {
  border-color: white;
  color: white;
}

.cta-section .btn-primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.cta-section .btn-outline:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.cta-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,0 C200,100 800,100 1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,1000 C200,900 800,900 1000,1000 L1000,0 L0,0 Z" style="fill: rgba(255, 255, 255, 0.05)"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .about-story-content, .mission-content {
    flex-direction: column;
  }

  .about-image, .mission-image {
    margin-bottom: 40px;
  }

  .mission-image {
    order: -1;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .about-header-content h1 {
    font-size: 2.2rem;
  }

  .about-text h2, .mission-text h2, .section-header h2, .cta-content h2 {
    font-size: 1.8rem;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .values-grid {
    grid-template-columns: 1fr;
  }
}