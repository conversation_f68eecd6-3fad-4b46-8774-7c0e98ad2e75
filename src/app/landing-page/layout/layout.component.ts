import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../modules/auth/services/auth.service';

@Component({
  selector: 'app-layout',
  standalone: false,
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss'
})
export class LayoutComponent implements OnInit, AfterViewInit {
  isLoggedIn: boolean = false;
  showDropdown: boolean = false;
  authLocalStorageToken = 'falcon-erp-authToken';
  // Will be set dynamically based on current hostname

  constructor(
    private router: Router,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // Check if user is logged in
    this.checkAuthStatus();

    // Subscribe to auth state changes
    this.authService.currentUser$.subscribe(() => {
      this.checkAuthStatus();
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.user-dropdown')) {
        this.showDropdown = false;
      }
    });
  }

  toggleDropdown(event: MouseEvent): void {
    this.showDropdown = !this.showDropdown;
    event.stopPropagation();
  }

  ngAfterViewInit(): void {
    // Mobile Menu Toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle') as HTMLElement;
    const mobileMenuClose = document.querySelector('.mobile-menu-close') as HTMLElement;
    const mobileMenu = document.querySelector('.mobile-menu') as HTMLElement;

    if (mobileMenuToggle && mobileMenuClose && mobileMenu) {
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('active');
      });

      mobileMenuClose.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
      });
    }
  }

  checkAuthStatus(): void {
    // Check if user is authenticated using AuthService
    this.isLoggedIn = this.authService.isAuthenticated;

    // Also check for the token in localStorage
    const token = localStorage.getItem('falcon-erp-authToken');
    if (!token) {
      this.isLoggedIn = false;
    }

    // For testing purposes only - uncomment for testing
    // this.isLoggedIn = true;
  }

  logout(): void {
    // Update isLoggedIn state
    this.isLoggedIn = false;

    // Use AuthService to logout
    this.authService.logout();

    // Navigate to home page
    this.router.navigate(['/']);
  }

  goToDashboard(): void {
    console.log('Navigating to dashboard...');

    // Navigate to the root (which will redirect to dashboard)
    window.location.href = '/';
  }
}
