/* Global Styles */
:root {
  --primary-color: #3699FF;
  --secondary-color: #8950FC;
  --dark-color: #1E1E2D;
  --light-color: #F3F6F9;
  --success-color: #1BC5BD;
  --warning-color: #FFA800;
  --danger-color: #F64E60;
  --text-color: #3F4254;
  --text-muted: #B5B5C3;
  --border-color: #E4E6EF;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Poppins', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  direction: rtl;
  text-align: right;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-portal {
  background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(111, 66, 193, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.btn-portal:hover {
  background: linear-gradient(135deg, #5a2d91 0%, #4a1f7a 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(111, 66, 193, 0.4);
  color: white;
  text-decoration: none;
}

.btn-login {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 30px;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(54, 153, 255, 0.3);
}

.btn-login:hover {
  background-color: darken(#3699FF, 10%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(54, 153, 255, 0.4);
}

/* User Dropdown Styles */
.user-dropdown {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.dropdown-menu {
  position: absolute;
  top: 50px;
  left: 0;
  width: 250px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  padding: 10px 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-section {
  padding: 5px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: var(--text-color);
  transition: background-color 0.3s ease;
  text-decoration: none;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--light-color);
}

.dropdown-item i {
  margin-left: 10px;
  font-size: 16px;
  color: var(--text-muted);
  width: 20px;
  text-align: center;
}

.dropdown-item span {
  flex: 1;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 5px 0;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 20px;
  cursor: pointer;
  transition: 0.4s;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  right: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

.toggle-switch input:checked + label {
  background-color: var(--success-color);
}

.toggle-switch input:checked + label:before {
  transform: translateX(-20px);
}

/* Header Styles */
.header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 50px;
  width: auto;
  max-width: 200px;
  margin-left: 10px;
  object-fit: contain;
  object-position: center;
  transition: all 0.3s ease;

  // للشاشات الصغيرة
  @media (max-width: 768px) {
    height: 40px;
    max-width: 150px;
    margin-left: 5px;
  }

  // للشاشات الكبيرة
  @media (min-width: 1200px) {
    height: 60px;
    max-width: 250px;
  }

  // تأثير عند التمرير
  &:hover {
    transform: scale(1.05);
  }
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark-color);
}

.main-nav {
  flex: 1;
  margin: 0 30px;
}

.nav-list {
  display: flex;
  justify-content: center;
}

.nav-item {
  margin: 0 15px;
}

.nav-item a {
  font-weight: 500;
  color: var(--text-color);
  position: relative;
  padding: 5px 0;
}

.nav-item a:hover, .nav-item a.active {
  color: var(--primary-color);
}

.nav-item a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-item a:hover::after, .nav-item a.active::after {
  transform: scaleX(1);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--dark-color);
  cursor: pointer;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 300px;
  height: 100vh;
  background-color: white;
  z-index: 1001;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  padding: 20px;
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.mobile-menu-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--dark-color);
  cursor: pointer;
}

.mobile-nav-list {
  margin-bottom: 30px;
}

.mobile-nav-item {
  margin-bottom: 15px;
}

.mobile-nav-item a {
  font-weight: 500;
  color: var(--text-color);
  display: block;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.mobile-nav-item a:hover, .mobile-nav-item a.active {
  color: var(--primary-color);
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .btn {
    text-align: center;
    margin-bottom: 5px;
  }
}

.mobile-user-menu {
  margin-top: 15px;
}

.mobile-user-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.mobile-user-header .user-avatar {
  margin-left: 15px;
}

.user-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--dark-color);
}

.user-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text-muted);
}

.mobile-menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 10px 0;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  color: var(--text-color);
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-menu-item i {
  margin-left: 15px;
  font-size: 16px;
  color: var(--text-muted);
  width: 20px;
  text-align: center;
}

.mobile-menu-item span {
  font-size: 15px;
  font-weight: 500;
}

/* Main Content Styles */
.main-content {
  min-height: calc(100vh - 70px - 300px); /* Viewport height minus header and footer */
  padding-top: 70px; /* Height of fixed header */
}

/* Footer Styles */
.footer {
  background-color: var(--dark-color);
  color: white;
  padding: 60px 0 20px;
  position: relative;
  overflow: hidden;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-logo-img {
  height: 40px;
  margin-left: 10px;
}

.footer-logo-text {
  font-size: 1.5rem;
  font-weight: 700;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.footer-links-column {
  flex: 1;
  min-width: 200px;
  margin-bottom: 20px;
}

.footer-links-column h4 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
}

.footer-links-column h4:after {
  content: '';
  position: absolute;
  bottom: -10px;
  right: 0;
  width: 50px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links-column ul li {
  margin-bottom: 10px;
}

.footer-links-column ul li a {
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.footer-links-column ul li a:hover {
  color: white;
}

.contact-info li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.contact-info li i {
  margin-left: 10px;
  color: var(--primary-color);
}

.social-icons {
  display: flex;
  margin-top: 20px;
}

.social-icons a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  margin-left: 10px;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.footer-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.02)"></path><path d="M0,0 C200,100 800,100 1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.02)"></path><path d="M0,1000 C200,900 800,900 1000,1000 L1000,0 L0,0 Z" style="fill: rgba(255, 255, 255, 0.02)"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .main-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .footer-links-column {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 10px 0;
  }

  .logo-img {
    height: 35px;
  }

  .logo-text {
    font-size: 1.3rem;
  }

  .footer-content {
    flex-direction: column;
  }

  .footer-links {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .header-content {
    padding: 8px 0;
  }

  .logo-img {
    height: 30px;
  }

  .logo-text {
    font-size: 1.2rem;
  }

  .btn-portal {
    padding: 8px 15px;
    font-size: 0.8rem;
    margin-left: 0.5rem;
  }

  .btn-login {
    padding: 8px 20px;
    font-size: 0.9rem;
  }
}