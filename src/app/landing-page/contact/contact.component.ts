import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-contact',
  standalone: false,
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit, AfterViewInit {

  constructor(
    private meta: Meta,
    private title: Title
  ) { }

  ngOnInit(): void {
    this.setSEOData();
  }

  private setSEOData(): void {
    // Set page title
    this.title.setTitle('اتصل بنا - فالكون ERP | تواصل معنا الآن');

    // Set meta description
    this.meta.updateTag({
      name: 'description',
      content: 'تواصل مع فريق فالكون ERP. نحن هنا لمساعدتك في جميع استفساراتك حول نظام إدارة موارد المؤسسات. اتصل بنا على 201113105777 أو راسلنا على <EMAIL>'
    });

    // Set keywords
    this.meta.updateTag({
      name: 'keywords',
      content: 'اتصل بنا فالكون ERP, تواصل معنا, دعم فني, خدمة العملاء, رقم الهاتف, البريد الإلكتروني, العنوان, مدينة السادات'
    });

    // Open Graph tags
    this.meta.updateTag({
      property: 'og:title',
      content: 'اتصل بنا - فالكون ERP | تواصل معنا الآن'
    });

    this.meta.updateTag({
      property: 'og:description',
      content: 'تواصل مع فريق فالكون ERP. نحن هنا لمساعدتك في جميع استفساراتك حول نظام إدارة موارد المؤسسات.'
    });

    this.meta.updateTag({
      property: 'og:url',
      content: 'https://falcon-v.com/web/contact'
    });

    // Twitter Card tags
    this.meta.updateTag({
      name: 'twitter:title',
      content: 'اتصل بنا - فالكون ERP | تواصل معنا الآن'
    });

    this.meta.updateTag({
      name: 'twitter:description',
      content: 'تواصل مع فريق فالكون ERP. نحن هنا لمساعدتك في جميع استفساراتك حول نظام إدارة موارد المؤسسات.'
    });

    // Additional SEO tags
    this.meta.updateTag({
      name: 'robots',
      content: 'index, follow'
    });
  }

  ngAfterViewInit(): void {
    // FAQ Toggle
    const faqQuestions = document.querySelectorAll('.faq-question');

    if (faqQuestions.length > 0) {
      faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
          const faqItem = question.parentElement;
          if (faqItem) {
            faqItem.classList.toggle('active');
          }
        });
      });
    }
  }
}
