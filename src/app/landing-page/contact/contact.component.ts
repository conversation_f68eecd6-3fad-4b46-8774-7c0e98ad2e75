import { Component, OnInit, AfterViewInit } from '@angular/core';

@Component({
  selector: 'app-contact',
  standalone: false,
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit, AfterViewInit {

  constructor() { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // FAQ Toggle
    const faqQuestions = document.querySelectorAll('.faq-question');

    if (faqQuestions.length > 0) {
      faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
          const faqItem = question.parentElement;
          if (faqItem) {
            faqItem.classList.toggle('active');
          }
        });
      });
    }
  }
}
