/* Contact Header */
.contact-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 80px 0 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.contact-header-content {
  position: relative;
  z-index: 2;
}

.contact-header-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.contact-header-content p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

.header-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Section Subtitle */
.section-subtitle {
  display: inline-block;
  background-color: rgba(54, 153, 255, 0.1);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

/* Contact Info Section */
.contact-info-section {
  padding: 80px 0;
  background-color: white;
}

.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.contact-info-card {
  background-color: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-info-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.info-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(54, 153, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.contact-info-card:hover .info-icon {
  background-color: var(--primary-color);
}

.info-icon i {
  font-size: 30px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.contact-info-card:hover .info-icon i {
  color: white;
}

.contact-info-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.contact-info-card p {
  color: var(--text-muted);
  line-height: 1.6;
}

/* Contact Form Section */
.contact-form-section {
  padding: 80px 0;
  background-color: var(--light-color);
}

.contact-form-container {
  display: flex;
  align-items: center;
  gap: 60px;
}

.contact-form-content {
  flex: 1;
}

.contact-form-content h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.contact-form-content p {
  font-size: 1.1rem;
  color: var(--text-muted);
  margin-bottom: 30px;
}

.contact-form {
  margin-top: 30px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--dark-color);
}

.form-group input, .form-group textarea {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(54, 153, 255, 0.1);
  outline: none;
}

.contact-form .btn-primary {
  margin-top: 20px;
  padding: 12px 30px;
  font-size: 1.1rem;
}

.contact-image {
  flex: 1;
  position: relative;
}

.contact-image img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  object-fit: cover;
  min-height: 300px;
}

.contact-shape {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 20px;
  right: -20px;
  border-radius: 15px;
  background-color: var(--primary-color);
  z-index: 1;
}

/* Map Section */
.map-section {
  padding: 80px 0;
  background-color: white;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-muted);
  max-width: 700px;
  margin: 0 auto;
}

.map-container {
  height: 400px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--light-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.map-placeholder i {
  font-size: 60px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.map-placeholder p {
  font-size: 1.2rem;
  color: var(--text-muted);
}

/* FAQ Section */
.faq-section {
  padding: 80px 0;
  background-color: var(--light-color);
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background-color: white;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.faq-question {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background-color: rgba(54, 153, 255, 0.05);
}

.faq-question h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0;
}

.faq-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(54, 153, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.faq-icon i {
  font-size: 14px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.faq-answer {
  padding: 0 20px 20px;
  display: none;
}

.faq-answer p {
  color: var(--text-muted);
  line-height: 1.6;
  margin: 0;
}

.faq-item.active .faq-question {
  background-color: rgba(54, 153, 255, 0.05);
}

.faq-item.active .faq-icon {
  background-color: var(--primary-color);
}

.faq-item.active .faq-icon i {
  color: white;
  transform: rotate(45deg);
}

.faq-item.active .faq-answer {
  display: block;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-section .btn-primary {
  background-color: white;
  color: var(--primary-color);
}

.cta-section .btn-outline {
  border-color: white;
  color: white;
}

.cta-section .btn-primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.cta-section .btn-outline:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.cta-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,0 C200,100 800,100 1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,1000 C200,900 800,900 1000,1000 L1000,0 L0,0 Z" style="fill: rgba(255, 255, 255, 0.05)"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .contact-form-container {
    flex-direction: column;
  }

  .contact-image {
    order: -1;
    margin-bottom: 40px;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .contact-header-content h1 {
    font-size: 2.2rem;
  }

  .contact-form-content h2, .section-header h2, .cta-content h2 {
    font-size: 1.8rem;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
  }

  .map-container {
    height: 300px;
  }
}