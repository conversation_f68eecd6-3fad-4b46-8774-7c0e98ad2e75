import { Component, OnInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-home',
  standalone: false,
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {

  constructor(
    private meta: Meta,
    private title: Title
  ) {}

  ngOnInit(): void {
    this.setSEOData();
  }

  private setSEOData(): void {
    // Set page title
    this.title.setTitle('فالكون ERP - نظام إدارة موارد المؤسسات الشامل | الصفحة الرئيسية');

    // Set meta description
    this.meta.updateTag({
      name: 'description',
      content: 'نظام فالكون ERP الشامل لإدارة موارد المؤسسات. حلول متكاملة للمحاسبة والمخزون والمبيعات والمشتريات والموارد البشرية. أكثر من 1000 عميل يثقون بنا.'
    });

    // Set keywords
    this.meta.updateTag({
      name: 'keywords',
      content: 'فالكون ERP, نظام ERP, إدارة موارد المؤسسات, محاسبة, مخزون, مبيعات, مشتريات, موارد بشرية, نظام محاسبي, برنامج إدارة, حلول الأعمال'
    });

    // Open Graph tags
    this.meta.updateTag({
      property: 'og:title',
      content: 'فالكون ERP - نظام إدارة موارد المؤسسات الشامل'
    });

    this.meta.updateTag({
      property: 'og:description',
      content: 'نظام فالكون ERP الشامل لإدارة موارد المؤسسات. حلول متكاملة للمحاسبة والمخزون والمبيعات والمشتريات والموارد البشرية.'
    });

    this.meta.updateTag({
      property: 'og:url',
      content: 'https://falcon-v.com/web/home'
    });

    this.meta.updateTag({
      property: 'og:type',
      content: 'website'
    });

    // Twitter Card tags
    this.meta.updateTag({
      name: 'twitter:title',
      content: 'فالكون ERP - نظام إدارة موارد المؤسسات الشامل'
    });

    this.meta.updateTag({
      name: 'twitter:description',
      content: 'نظام فالكون ERP الشامل لإدارة موارد المؤسسات. حلول متكاملة للمحاسبة والمخزون والمبيعات والمشتريات والموارد البشرية.'
    });

    // Additional SEO tags
    this.meta.updateTag({
      name: 'author',
      content: 'Falcon ERP Team'
    });

    this.meta.updateTag({
      name: 'robots',
      content: 'index, follow'
    });

    this.meta.updateTag({
      name: 'language',
      content: 'Arabic'
    });

    this.meta.updateTag({
      name: 'geo.region',
      content: 'EG'
    });

    this.meta.updateTag({
      name: 'geo.country',
      content: 'Egypt'
    });
  }
}
