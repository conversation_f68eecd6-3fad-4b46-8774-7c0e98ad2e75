/* Hero Section */
.hero-section {
  padding: 120px 0 100px;
  background: linear-gradient(135deg, var(--light-color) 0%, #ffffff 100%);
  overflow: hidden;
  position: relative;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.hero-text {
  flex: 1;
  padding-left: 30px;
}

.hero-badge {
  display: inline-block;
  background-color: rgba(54, 153, 255, 0.1);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--dark-color);
  margin-bottom: 10px;
  line-height: 1.2;
}

.hero-text h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.hero-text p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 30px;
  line-height: 1.8;
}

.hero-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 1rem;
  color: var(--text-muted);
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.hero-image {
  flex: 1;
  position: relative;
}

.image-wrapper {
  position: relative;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  border-radius: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
}

.hero-image:hover img {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.floating-card {
  position: absolute;
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  animation: float 3s ease-in-out infinite;
}

.floating-card i {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.floating-card span {
  font-weight: 600;
  font-size: 0.9rem;
}

.card-1 {
  top: 20%;
  left: -5%;
  animation-delay: 0s;
}

.card-2 {
  bottom: 25%;
  left: -10%;
  animation-delay: 0.5s;
}

.card-3 {
  top: 15%;
  right: -5%;
  animation-delay: 1s;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.hero-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Features Section */
.features-section {
  padding: 100px 0;
  background-color: white;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-subtitle {
  display: inline-block;
  background-color: rgba(54, 153, 255, 0.1);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 15px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-muted);
  max-width: 700px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: white;
  border-radius: 15px;
  padding: 40px 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(54, 153, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  background-color: var(--primary-color);
}

.feature-icon i {
  font-size: 32px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon i {
  color: white;
}

.feature-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.feature-card p {
  color: var(--text-muted);
  line-height: 1.6;
}

/* About Section */
.about-section {
  padding: 100px 0;
  background-color: var(--light-color);
  position: relative;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-image {
  flex: 1;
  position: relative;
}

.about-image img {
  max-width: 100%;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.about-shape {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 20px;
  right: -20px;
  border-radius: 15px;
  background-color: var(--primary-color);
  z-index: 1;
}

.about-text {
  flex: 1;
}

.about-text h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 20px;
}

.about-text p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 25px;
  line-height: 1.8;
}

.about-list {
  margin-bottom: 30px;
}

.about-list li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.about-list li i {
  color: var(--success-color);
  margin-left: 15px;
  font-size: 1.2rem;
}

/* Testimonials Section */
.testimonials-section {
  padding: 100px 0;
  background-color: white;
}

.testimonials-slider {
  display: flex;
  gap: 30px;
  overflow-x: auto;
  padding: 20px 0;
  scrollbar-width: none; /* Firefox */
}

.testimonials-slider::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.testimonial-card {
  background-color: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  min-width: 350px;
  flex: 1;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.testimonial-rating {
  margin-bottom: 20px;
}

.testimonial-rating i {
  color: var(--warning-color);
  margin-left: 2px;
}

.testimonial-content {
  margin-bottom: 25px;
}

.testimonial-content p {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-left: 15px;
  border: 3px solid var(--light-color);
}

.author-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.author-info p {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* CTA Section */
.cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-section .btn-primary {
  background-color: white;
  color: var(--primary-color);
}

.cta-section .btn-outline {
  border-color: white;
  color: white;
}

.cta-section .btn-primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.cta-section .btn-outline:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.cta-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,0 C200,100 800,100 1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(255, 255, 255, 0.05)"></path><path d="M0,1000 C200,900 800,900 1000,1000 L1000,0 L0,0 Z" style="fill: rgba(255, 255, 255, 0.05)"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-text {
    padding-left: 0;
    margin-bottom: 50px;
  }

  .hero-stats {
    justify-content: center;
  }

  .hero-buttons {
    justify-content: center;
  }

  .about-content {
    flex-direction: column;
  }

  .about-image {
    margin-bottom: 50px;
  }

  .testimonials-slider {
    flex-direction: column;
    gap: 20px;
  }

  .testimonial-card {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-text h2 {
    font-size: 1.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .floating-card {
    display: none;
  }
}