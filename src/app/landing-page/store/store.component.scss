/* Store Header */
.store-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 80px 0 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.store-header-content {
  position: relative;
  z-index: 2;
}

.store-header-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.store-header-content p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

.header-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Store Categories */
.store-categories {
  background-color: white;
  padding: 20px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 70px;
  z-index: 100;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.category-btn {
  padding: 10px 20px;
  border-radius: 30px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn:hover, .category-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Store Search and Filter */
.store-search {
  padding: 30px 0;
}

.search-filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.search-box {
  display: flex;
  flex: 1;
  max-width: 500px;
}

.search-box input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px 0 0 5px;
  font-size: 1rem;
}

.search-btn {
  padding: 12px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 5px 5px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background-color: darken(#3699FF, 10%);
}

.filter-options {
  display: flex;
  gap: 15px;
}

.filter-select {
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-size: 1rem;
  min-width: 200px;
  cursor: pointer;
}

/* Products Grid */
.products-section {
  padding: 30px 0 60px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.product-card {
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.product-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.product-badge.new {
  background-color: var(--primary-color);
  color: white;
}

.product-badge.sale {
  background-color: var(--danger-color);
  color: white;
}

.product-badge.popular {
  background-color: var(--warning-color);
  color: white;
}

.product-image {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-actions-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  opacity: 0;
  transition: all 0.3s ease;
}

.product-card:hover .product-actions-overlay {
  opacity: 1;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(20px);
}

.product-card:hover .action-btn {
  transform: translateY(0);
}

.action-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.action-btn:nth-child(1) {
  transition-delay: 0.1s;
}

.action-btn:nth-child(2) {
  transition-delay: 0.2s;
}

.action-btn:nth-child(3) {
  transition-delay: 0.3s;
}

.product-info {
  padding: 20px;
}

.product-category {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 10px;
}

.product-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
  height: 50px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.product-rating i {
  color: var(--warning-color);
  margin-left: 2px;
}

.product-rating span {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-right: 5px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.current-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--dark-color);
}

.old-price {
  font-size: 1rem;
  color: var(--text-muted);
  text-decoration: line-through;
}

.product-actions {
  padding: 0 20px 20px;
  display: flex;
  gap: 10px;
}

.btn-add-to-cart, .btn-details {
  flex: 1;
  padding: 10px;
  font-size: 0.9rem;
}

/* Pagination */
.pagination-section {
  padding: 30px 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background-color: white;
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover, .pagination-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-dots {
  margin: 0 5px;
}

/* Newsletter */
.newsletter-section {
  background-color: var(--light-color);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.newsletter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
  position: relative;
  z-index: 2;
}

.newsletter-text {
  flex: 1;
  min-width: 300px;
}

.newsletter-text h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.newsletter-text p {
  font-size: 1.1rem;
  color: var(--text-muted);
}

.newsletter-form {
  display: flex;
  flex: 1;
  max-width: 500px;
}

.newsletter-form input {
  flex: 1;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px 0 0 5px;
  font-size: 1rem;
}

.newsletter-form .btn {
  padding: 15px 25px;
  border-radius: 0 5px 5px 0;
  font-size: 1rem;
}

.newsletter-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none"><path d="M0,0 L1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(54, 153, 255, 0.05)"></path><path d="M0,0 C200,100 800,100 1000,0 L1000,1000 L0,1000 Z" style="fill: rgba(54, 153, 255, 0.05)"></path><path d="M0,1000 C200,900 800,900 1000,1000 L1000,0 L0,0 Z" style="fill: rgba(54, 153, 255, 0.05)"></path></svg>');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

/* Section Subtitle */
.section-subtitle {
  display: inline-block;
  background-color: rgba(54, 153, 255, 0.1);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .search-filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: 100%;
  }

  .newsletter-content {
    flex-direction: column;
    text-align: center;
  }

  .newsletter-form {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .store-header-content h1 {
    font-size: 2.2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .product-actions {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .categories-list {
    flex-direction: column;
    align-items: stretch;
  }

  .category-btn {
    text-align: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }
}