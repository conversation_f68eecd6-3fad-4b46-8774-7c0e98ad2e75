.services-page {
  font-family: var(--bs-body-font-family);
  color: var(--text-color);
}

// Hero Section
.hero-section {
  background-color: var(--light-color);
  padding: 100px 0;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(54, 153, 255, 0.1) 0%, rgba(54, 153, 255, 0) 100%);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 20px;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    max-width: 700px;
    margin: 0 auto;
  }
}

// Services Overview Section
.services-overview {
  padding: 80px 0;
  background-color: white;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 20px;
  }

  .section-description {
    font-size: 1.1rem;
    color: var(--text-color);
    line-height: 1.8;
    margin-bottom: 20px;
  }

  .section-subtitle {
    display: inline-block;
    background-color: rgba(54, 153, 255, 0.1);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 15px;
  }

  .overview-image {
    position: relative;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #3699FF;
    border-radius: 15px;

    &::before {
      content: '';
      position: absolute;
      top: 20px;
      right: 20px;
      width: 100%;
      height: 100%;
      background-color: white;
      border-radius: 15px;
      z-index: 1;
    }

    img {
      max-width: 100%;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 2;
    }
  }

  .overview-content {
    padding: 20px;
  }
}

// Main Services Section
.main-services {
  padding: 80px 0;
  background-color: var(--light-color);

  .service-item {
    margin-bottom: 30px;
  }

  .service-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
  }

  .service-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    i {
      font-size: 30px;
      color: white;
    }
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
  }

  p {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 20px;
    line-height: 1.6;
  }

  .service-features {
    padding-right: 20px;

    li {
      margin-bottom: 10px;
      position: relative;

      &::before {
        content: "\f00c";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        color: var(--success-color);
        position: absolute;
        right: -20px;
      }
    }
  }
}

// Why Choose Us Section
.why-choose-us {
  padding: 80px 0;
  background-color: white;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 50px;
  }

  .feature-card {
    text-align: center;
    padding: 30px;
    margin-bottom: 30px;
    border-radius: 10px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .feature-icon {
    width: 80px;
    height: 80px;
    background-color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;

    i {
      font-size: 35px;
      color: var(--primary-color);
    }
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
  }

  p {
    font-size: 1rem;
    color: var(--text-color);
    line-height: 1.6;
  }
}

// Call to Action Section
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, darken(#3699FF, 15%) 100%);
  color: white;

  h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
  }

  .btn-primary {
    background-color: white;
    color: var(--primary-color);
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--light-color);
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
  }
}

// Responsive Styles
@media (max-width: 991px) {
  .hero-section {
    padding: 80px 0;

    .section-title {
      font-size: 2.5rem;
    }
  }

  .services-overview, .main-services, .why-choose-us, .cta-section {
    padding: 60px 0;
  }

  .services-overview .section-title,
  .why-choose-us .section-title,
  .cta-section h2 {
    font-size: 2rem;
  }

  .services-overview .row {
    flex-direction: column-reverse;
  }

  .services-overview .overview-image {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .hero-section {
    padding: 60px 0;

    .section-title {
      font-size: 2rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }
  }

  .services-overview, .main-services, .why-choose-us, .cta-section {
    padding: 40px 0;
  }

  .services-overview .section-title,
  .why-choose-us .section-title,
  .cta-section h2 {
    font-size: 1.8rem;
  }

  .services-overview .section-description {
    font-size: 1rem;
  }

  .cta-section p {
    font-size: 1rem;
  }
}
