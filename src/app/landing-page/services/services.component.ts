import { Component, OnInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-services',
  templateUrl: './services.component.html',
  styleUrls: ['./services.component.scss'],
  standalone: false
})
export class ServicesComponent implements OnInit {

  constructor(
    private meta: Meta,
    private title: Title
  ) { }

  ngOnInit(): void {
    // Scroll to top when component initializes
    window.scrollTo(0, 0);
    this.setSEOData();
  }

  private setSEOData(): void {
    // Set page title
    this.title.setTitle('خدماتنا - فالكون ERP | حلول شاملة لإدارة الأعمال');

    // Set meta description
    this.meta.updateTag({
      name: 'description',
      content: 'اكتشف خدمات فالكون ERP الشاملة: نظام المحاسبة، إدارة المخزون، المبيعات والمشتريات، الموارد البشرية، وإدارة المشاريع. حلول متكاملة لجميع احتياجات مؤسستك.'
    });

    // Set keywords
    this.meta.updateTag({
      name: 'keywords',
      content: 'خدمات فالكون ERP, نظام محاسبة, إدارة مخزون, نظام مبيعات, نظام مشتريات, موارد بشرية, إدارة مشاريع, حلول الأعمال, نظام POS, إدارة الأسطول'
    });

    // Open Graph tags
    this.meta.updateTag({
      property: 'og:title',
      content: 'خدماتنا - فالكون ERP | حلول شاملة لإدارة الأعمال'
    });

    this.meta.updateTag({
      property: 'og:description',
      content: 'اكتشف خدمات فالكون ERP الشاملة: نظام المحاسبة، إدارة المخزون، المبيعات والمشتريات، الموارد البشرية، وإدارة المشاريع.'
    });

    this.meta.updateTag({
      property: 'og:url',
      content: 'https://falcon-v.com/web/services'
    });

    // Twitter Card tags
    this.meta.updateTag({
      name: 'twitter:title',
      content: 'خدماتنا - فالكون ERP | حلول شاملة لإدارة الأعمال'
    });

    this.meta.updateTag({
      name: 'twitter:description',
      content: 'اكتشف خدمات فالكون ERP الشاملة: نظام المحاسبة، إدارة المخزون، المبيعات والمشتريات، الموارد البشرية، وإدارة المشاريع.'
    });

    // Additional SEO tags
    this.meta.updateTag({
      name: 'robots',
      content: 'index, follow'
    });
  }
}
