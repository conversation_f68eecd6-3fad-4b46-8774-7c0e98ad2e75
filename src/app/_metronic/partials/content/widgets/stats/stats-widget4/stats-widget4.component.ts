import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexYAxis,
  ApexDataLabels,
  ApexStroke,
  ApexTooltip,
  ApexPlotOptions,
  ApexFill,
  ApexLegend,
  ApexMarkers,
  ApexGrid,
  ApexStates,
  ApexNonAxisChartSeries
} from 'ng-apexcharts';
import { getCSSVariableValue } from '../../../../../kt/_utils';

export type ChartOptions = {
  series: ApexAxisChartSeries | ApexNonAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  dataLabels: ApexDataLabels;
  stroke: ApexStroke;
  tooltip: ApexTooltip;
  plotOptions: ApexPlotOptions;
  fill: ApexFill;
  legend: ApexLegend;
  markers: ApexMarkers;
  grid: ApexGrid;
  colors: string[];
  states: ApexStates;
};

@Component({
  selector: 'app-stats-widget4',
  templateUrl: './stats-widget4.component.html',
  standalone: false
})
export class StatsWidget4Component implements OnInit {
  @Input() svgIcon = '';
  @Input() color = '';
  @Input() description = '';
  @Input() change = '';
  @ViewChild('chartRef', { static: true }) chartRef: ElementRef;
  height: number;

  chartOptions: ChartOptions;
  labelColor: string;
  baseColor: string;
  lightColor: string;

  constructor() {
    this.chartOptions = {
      series: [{
        name: 'Net Profit',
        data: [0, 0, 0, 0, 0],
      }],
      chart: {
        fontFamily: 'inherit',
        type: 'area',
        height: 150,
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: false,
        },
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {},
      legend: {
        show: false,
      },
      dataLabels: {
        enabled: false,
      },
      fill: {
        type: 'solid',
        opacity: 1,
      },
      stroke: {
        curve: 'smooth',
        show: true,
        width: 3,
        colors: [''],
      },
      xaxis: {
        categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          style: {
            colors: '',
            fontSize: '12px',
          },
        },
        crosshairs: {
          show: false,
          position: 'front',
          stroke: {
            color: '#E4E6EF',
            width: 1,
            dashArray: 3,
          },
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        min: 0,
        max: 80,
        labels: {
          show: false,
          style: {
            colors: '',
            fontSize: '12px',
          },
        },
      },
      states: {
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: 'none',
          },
        },
      },
      tooltip: {
        style: {
          fontSize: '12px',
        },
        y: {
          formatter: function (val) {
            return '$' + val + ' thousands';
          },
        },
      },
      colors: [''],
      markers: {
        colors: [''],
        strokeColors: [''],
        strokeWidth: 3,
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        yaxis: {
          lines: {
            show: true
          }
        }
      }
    };
  }

  ngOnInit(): void {
    this.height = 150;
    this.labelColor = getCSSVariableValue('--kt-gray-800');
    this.baseColor = getCSSVariableValue('--kt-' + this.color);
    this.lightColor = getCSSVariableValue('--kt-' + this.color + '-light');

    this.chartOptions = {
      series: [
        {
          name: 'Net Profit',
          data: [30, 45, 32, 70, 40],
        },
      ],
      chart: {
        fontFamily: 'inherit',
        type: 'area',
        height: this.height,
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: false,
        },
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {},
      legend: {
        show: false,
      },
      dataLabels: {
        enabled: false,
      },
      fill: {
        type: 'solid',
        opacity: 1,
      },
      stroke: {
        curve: 'smooth',
        show: true,
        width: 3,
        colors: [this.baseColor],
      },
      xaxis: {
        categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          style: {
            colors: this.labelColor,
            fontSize: '12px',
          },
        },
        crosshairs: {
          show: false,
          position: 'front',
          stroke: {
            color: '#E4E6EF',
            width: 1,
            dashArray: 3,
          },
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        min: 0,
        max: 80,
        labels: {
          show: false,
          style: {
            colors: this.labelColor,
            fontSize: '12px',
          },
        },
      },
      states: {
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: 'none',
          },
        },
      },
      tooltip: {
        style: {
          fontSize: '12px',
        },
        y: {
          formatter: function (val) {
            return '$' + val + ' thousands';
          },
        },
      },
      colors: [this.lightColor],
      markers: {
        colors: [this.lightColor],
        strokeColors: [this.baseColor],
        strokeWidth: 3,
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        yaxis: {
          lines: {
            show: true
          }
        }
      }
    };
  }
}
