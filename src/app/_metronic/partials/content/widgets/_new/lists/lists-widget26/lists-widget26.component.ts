import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-lists-widget26',
    templateUrl: './lists-widget26.component.html',
    styleUrls: ['./lists-widget26.component.scss'],
    standalone: false
})
export class ListsWidget26Component implements OnInit {
  rows: Array<Partial<{ description: string; Supportdescription: string; Facebookdescription: string }>>;

  constructor() {}

  ngOnInit(): void {
    this.rows = [
      { description: 'Falcon Valley' },
      { Supportdescription: 'Support' },
      { Facebookdescription: 'Facebook' },
    ];
  }
}
