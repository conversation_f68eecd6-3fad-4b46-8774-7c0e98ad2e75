import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuDisplayMode, UserPreferencesService } from '../../../layout/core/user-preferences.service';

@Component({
  selector: 'app-apps-button',
  templateUrl: './apps-button.component.html',
  styleUrls: ['./apps-button.component.scss'],
  standalone: false
})
export class AppsButtonComponent implements OnInit {
  @Input() toggleBtnClass: string = '';
  
  constructor(
    private router: Router,
    private userPreferencesService: UserPreferencesService
  ) {}

  ngOnInit(): void {
  }

  // دالة للانتقال إلى شبكة التطبيقات
  navigateToAppsGrid(): void {
    // التأكد من أننا في وضع الشبكة
    if (this.userPreferencesService.menuDisplayMode !== MenuDisplayMode.GRID) {
      this.userPreferencesService.setGridMode();
    }
    
    // الانتقال إلى الصفحة الرئيسية
    this.router.navigate(['/dashboard']);
  }
}
