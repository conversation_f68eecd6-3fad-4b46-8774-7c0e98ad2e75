import {
  Component, HostB<PERSON>ing, On<PERSON><PERSON>roy, OnInit, ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { TranslationService } from '../../../../../../modules/i18n';
import { AuthService, UserType } from '../../../../../../modules/auth';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
    selector: 'app-user-inner',
    templateUrl: './user-inner.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class UserInnerComponent implements OnInit, OnDestroy {
  @HostBinding('class')
  class = `menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg menu-state-primary fw-bold py-4 fs-6 user-inner`;
  @HostBinding('attr.data-kt-menu') dataKtMenu = 'true';
  direction: 'rtl' | 'ltr' = 'ltr';

  language: LanguageFlag;
  user$: Observable<UserType>;
  langs = languages;
  private unsubscribe: Subscription[] = [];

  constructor(
    private auth: AuthService,
    private translationService: TranslationService,
    private cd: ChangeDetectorRef, private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.user$ = this.auth.currentUserSubject.asObservable();
    this.setLanguage(this.translationService.getSelectedLanguage());
  }

  setDirection(_direction: 'rtl' | 'ltr') {
    this.direction = _direction;
    this.cd.detectChanges();
  }

  getCSSURL() {
    return this.sanitizer.bypassSecurityTrustResourceUrl(
      `/assets/css/style.${this.direction}.css`
    );
  }

  logout() {
    this.auth.logout();
    document.location.reload();
  }

  selectLanguage(lang: string) {
    this.translationService.setLanguage(lang);
    this.setLanguage(lang);
    this.setDirection(lang == 'ar' ? 'rtl' : 'ltr');
    // document.location.reload();
    document.body.style.direction = lang == 'ar' ? 'rtl' : 'ltr';
  }

  setLanguage(lang: string) {
    this.langs.forEach((language: LanguageFlag) => {
      if (language.lang === lang) {
        language.active = true;
        this.language = language;
        this.setDirection(lang == 'ar' ? 'rtl' : 'ltr');
        document.body.style.direction = lang == 'ar' ? 'rtl' : 'ltr';
        document.dir = lang == 'ar' ? 'rtl' : 'ltr';
      } else {
        language.active = false;
      }
    });
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}

interface LanguageFlag {
  lang: string;
  name: string;
  flag: string;
  active?: boolean;
}

const languages = [
  {
    lang: 'en',
    name: 'English',
    flag: './assets/media/flags/united-states.svg',
  },
  {
    lang: 'ar',
    name: 'العربية',
    flag: './assets/media/flags/saudi-arabia.svg',
  },
];
