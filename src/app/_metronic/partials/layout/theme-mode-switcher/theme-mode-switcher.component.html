<!-- begin::Menu toggle -->
<div class="btn btn-icon" [ngClass]="toggleBtnClass" data-kt-menu-trigger="{default: 'click', lg: 'hover'}"
  data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
  <ng-container *ngIf="mode$ | async as mode">
    <ng-container *ngIf="mode === 'dark'">
      <span [inlineSVG]="'./assets/media/icons/duotune/general/gen061.svg'" [ngClass]="toggleBtnIconClass"
        class="svg-icon theme-light-hide"></span>
    </ng-container>
    <ng-container *ngIf="mode === 'light'">
      <span [inlineSVG]="'./assets/media/icons/duotune/general/gen060.svg'" [ngClass]="toggleBtnIconClass"
        class="svg-icon theme-dark-hide"></span>
    </ng-container>
  </ng-container>
</div>
<!-- end::Menu toggle -->

<!-- begin::Menu -->
<div
  class='menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-title-gray-700 menu-icon-muted menu-active-bg menu-state-primary fw-semibold py-4 fs-base w-175px'
  data-kt-menu='true'>
  <ng-container *ngIf="menuMode$ | async as menuMode">
    <!-- begin::Menu item  -->
    <div class='menu-item px-3 my-0'>
      <a class="menu-link px-3 py-2" [ngClass]="{'active': menuMode === 'light'}" (click)="switchMode('light')">
        <span class='menu-icon' data-kt-element='icon'>
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen060.svg'" class="svg-icon svg-icon-3"></span>
        </span>
        <span class='menu-title'>Light</span>
      </a>
    </div>
    <!-- end::Menu item -->

    <!-- begin::Menu item  -->
    <div class='menu-item px-3 my-0'>
      <a class="menu-link px-3 py-2" [ngClass]="{'active': menuMode === 'dark'}" (click)="switchMode('dark')">
        <span class='menu-icon' data-kt-element='icon'>
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen061.svg'" class="svg-icon svg-icon-3"></span>
        </span>
        <span class='menu-title'>Dark</span>
      </a>
    </div>
    <!-- end::Menu item -->

    <!-- begin::Menu item  -->
    <div class='menu-item px-3 my-0'>
      <a class="menu-link px-3 py-2" [ngClass]="{'active': menuMode === 'system'}" (click)="switchMode('system')">
        <span class='menu-icon' data-kt-element='icon'>
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen062.svg'" class="svg-icon svg-icon-3"></span>
        </span>
        <span class='menu-title'>System</span>
      </a>
    </div>
    <!-- end::Menu item -->

  </ng-container>

  <!-- begin::Separator -->
  <div class="separator my-3"></div>
  <!-- end::Separator -->

  <!-- begin::Menu item - Layout Mode -->
  <div class="menu-item px-3 my-0">
    <div class="menu-content px-3 py-2 fs-6 text-dark fw-bold">وضع العرض</div>
  </div>
  <!-- end::Menu item -->

  <!-- begin::Menu item - Vertical Mode -->
  <div class='menu-item px-3 my-0'>
    <a class="menu-link px-3 py-2" [ngClass]="{'active': isVerticalMode()}"
       (click)="toggleMenuDisplayMode()" *ngIf="isGridMode()">
      <span class='menu-icon' data-kt-element='icon'>
        <i class="fas fa-list fs-3"></i>
      </span>
      <span class='menu-title'>القائمة الرأسية</span>
    </a>
  </div>
  <!-- end::Menu item -->

  <!-- begin::Menu item - Grid Mode -->
  <div class='menu-item px-3 my-0'>
    <a class="menu-link px-3 py-2" [ngClass]="{'active': isGridMode()}"
       (click)="toggleMenuDisplayMode()" *ngIf="isVerticalMode()">
      <span class='menu-icon' data-kt-element='icon'>
        <i class="fas fa-th-large fs-3"></i>
      </span>
      <span class='menu-title'>وضع الشبكة</span>
    </a>
  </div>
  <!-- end::Menu item -->

</div>
<!-- end::Menu -->
