import { Component, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { ThemeModeService, ThemeModeType } from './theme-mode.service';
import { MenuDisplayMode, UserPreferencesService } from '../../../layout/core/user-preferences.service';

@Component({
    selector: 'app-theme-mode-switcher',
    templateUrl: './theme-mode-switcher.component.html',
    standalone: false
})
export class ThemeModeSwitcherComponent implements OnInit {
  @Input() toggleBtnClass: string = '';
  @Input() toggleBtnIconClass: string = 'svg-icon-2';
  @Input() menuPlacement: string = 'bottom-end';
  @Input() menuTrigger: string = "{default: 'click', lg: 'hover'}";
  mode$: Observable<ThemeModeType>;
  menuMode$: Observable<ThemeModeType>;

  // إضافة متغيرات لوضع عرض القائمة
  menuDisplayMode = MenuDisplayMode;
  currentDisplayMode: MenuDisplayMode;

  constructor(
    private modeService: ThemeModeService,
    private userPreferencesService: UserPreferencesService
  ) {
    this.currentDisplayMode = this.userPreferencesService.menuDisplayMode;
  }

  ngOnInit(): void {
    this.mode$ = this.modeService.mode.asObservable();
    this.menuMode$ = this.modeService.menuMode.asObservable();

    // الاشتراك في تغييرات وضع عرض القائمة
    this.userPreferencesService.menuDisplayMode$.subscribe(mode => {
      this.currentDisplayMode = mode;
    });
  }

  switchMode(_mode: ThemeModeType): void {
    this.modeService.switchMode(_mode);
  }

  // دالة لتبديل وضع عرض القائمة
  toggleMenuDisplayMode(): void {
    this.userPreferencesService.toggleMenuDisplayMode();
  }

  // دوال مساعدة للتحقق من وضع العرض
  isVerticalMode(): boolean {
    return this.currentDisplayMode === MenuDisplayMode.VERTICAL;
  }

  isGridMode(): boolean {
    return this.currentDisplayMode === MenuDisplayMode.GRID;
  }
}
