import { Component, Input, OnInit } from '@angular/core';
import { MenuDisplayMode, UserPreferencesService } from '../../../layout/core/user-preferences.service';

@Component({
  selector: 'app-menu-layout-switcher',
  templateUrl: './menu-layout-switcher.component.html',
  styleUrls: ['./menu-layout-switcher.component.scss'],
  standalone: false
})
export class MenuLayoutSwitcherComponent implements OnInit {
  @Input() toggleBtnClass: string = '';
  menuDisplayMode = MenuDisplayMode;
  currentDisplayMode: MenuDisplayMode;

  constructor(private userPreferencesService: UserPreferencesService) {
    this.currentDisplayMode = this.userPreferencesService.menuDisplayMode;
  }

  ngOnInit(): void {
    this.userPreferencesService.menuDisplayMode$.subscribe(mode => {
      this.currentDisplayMode = mode;
    });
  }

  toggleMenuDisplayMode(): void {
    this.userPreferencesService.toggleMenuDisplayMode();
  }
}
