:host {
  height: 100%;
  margin: 0;

  .flex-root {
    height: 100%;
  }

  .app-grid-fullscreen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: calc(100vh - 65px); // Restar la altura del header
    margin-top: 65px; // Altura del header
    overflow-y: auto;
    z-index: 1;
  }

  .app-main-fullwidth {
    width: 100%;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }
}

.page-loaded {
  app-layout {
    opacity: 1;
    transition: opacity 1s ease-in-out;
  }
}
