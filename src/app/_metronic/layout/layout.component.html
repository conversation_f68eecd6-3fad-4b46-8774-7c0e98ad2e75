<!--begin::App-->
<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
  <!--begin::Page-->
  <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
    <ng-container *ngIf="appHeaderDisplay">
      <!--begin::Header-->
      <app-header id="kt_app_header" class="app-header" [ngClass]="appHeaderDefaultClass">
      </app-header>
      <!--end::Header-->
    </ng-container>

    <!--begin::Wrapper-->
    <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">

      <!-- Mostrar la barra lateral solo en modo vertical -->
      <ng-container *ngIf="appSidebarDisplay && currentDisplayMode === 'vertical'">
        <!--begin::sidebar-->
        <app-sidebar #ktSidebar id="kt_app_sidebar" class="app-sidebar" [ngClass]="appSidebarDefaultClass">
        </app-sidebar>
        <!--end::sidebar-->
      </ng-container>

      <ng-container *ngIf="appSidebarPanelDisplay">
        <!-- TODO: app sidebar panel -->
      </ng-container>

      <!-- Mostrar la cuadrícula de aplicaciones en modo grid solo en la ruta principal -->
      <ng-container *ngIf="currentDisplayMode === 'grid' && isHomePage">
        <div class="app-grid-fullscreen">
          <app-apps-grid-view></app-apps-grid-view>
        </div>
      </ng-container>

      <!-- Mostrar el contenido principal en ambos modos -->
      <div [ngClass]="{'app-main-fullwidth': currentDisplayMode === 'grid', 'app-main': currentDisplayMode === 'vertical'}"
           class="flex-column flex-row-fluid" id="kt_app_main"
           *ngIf="currentDisplayMode === 'vertical' || (currentDisplayMode === 'grid' && !isHomePage)">
        <!--begin::Content wrapper-->
        <div class="d-flex flex-column flex-column-fluid">
          <ng-container *ngIf="appToolbarDisplay">
            <app-toolbar class="app-toolbar" [ngClass]="appToolbarCSSClass" id="kt_app_toolbar"
              [appToolbarLayout]="appToolbarLayout"></app-toolbar>
          </ng-container>
          <app-content id=" kt_app_content" class="app-content" [ngClass]="contentCSSClasses"
            [contentContainerCSSClass]="contentContainerCSSClass" [appContentContiner]="appContentContiner"
            [appContentContainerClass]="appContentContainerClass">
          </app-content>
        </div>
        <!-- haitham stop footer here -->
        <!--end::Content wrapper-->
        <!-- <ng-container *ngIf="appFooterDisplay">
          <app-footer class="app-footer" [ngClass]="appFooterCSSClass" id="kt_app_footer"
            [appFooterContainerCSSClass]="appFooterContainerCSSClass"></app-footer>
        </ng-container> -->
      </div>
      <!--end:::Main-->
    </div>
    <!--end::Wrapper-->

  </div>
  <!--end::Page-->
</div>
<!--end::App-->

<app-scripts-init></app-scripts-init>
<ng-container>
  <app-scroll-top id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true"></app-scroll-top>
</ng-container>
<!-- begin:: Drawers -->
<!-- <app-activity-drawer></app-activity-drawer>
<app-messenger-drawer></app-messenger-drawer> -->
<!-- end:: Drawers -->

<!-- end:: Engage -->
<!-- <app-engages></app-engages> -->
<!-- end:: Engage -->

<!-- begin:: Modals -->
<app-main-modal></app-main-modal>
<!-- <app-invite-users-modal></app-invite-users-modal> -->
<!-- <app-upgrade-plan-modal></app-upgrade-plan-modal> -->
<!-- end:: Modals -->