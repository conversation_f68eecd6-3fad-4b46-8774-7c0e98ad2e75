import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuDisplayMode, UserPreferencesService } from '../../core/user-preferences.service';

interface AppItem {
  title: string;
  route: string;
  icon: string;
  iconColor?: string;
  permission?: any;
}

@Component({
  selector: 'app-apps-grid-view',
  standalone: false,
  templateUrl: './apps-grid-view.component.html',
  styleUrls: ['./apps-grid-view.component.scss']
})
export class AppsGridViewComponent implements OnInit {

  apps: AppItem[] = [
    {
      title: 'الرئيسية',
      route: '/dashboard',
      icon: './assets/media/icons/apps/dashboard.svg'
    },
    {
      title: 'المخزون',
      route: '/inventory',
      icon: './assets/media/icons/apps/inventory.svg'
    },
    {
      title: 'المشتريات',
      route: '/purchases',
      icon: './assets/media/icons/apps/purchases.svg'
    },
    {
      title: 'المبيعات',
      route: '/sales',
      icon: './assets/media/icons/apps/sales.svg'
    },
    {
      title: 'CRM',
      route: '/crm',
      icon: './assets/media/icons/apps/crm.svg'
    },
    {
      title: 'الحسابات',
      route: '/accounting',
      icon: './assets/media/icons/apps/accounting.svg'
    },
    {
      title: 'الموارد البشرية',
      route: '/hr',
      icon: './assets/media/icons/apps/hr.svg'
    },
    {
      title: 'المشاريع',
      route: '/project',
      icon: './assets/media/icons/apps/project.svg'
    },
    {
      title: 'الصيانة',
      route: '/maintenance',
      icon: './assets/media/icons/apps/maintenance.svg'
    },
    {
      title: 'التصنيع',
      route: '/manufacturing',
      icon: './assets/media/icons/apps/manufacturing.svg'
    },
    {
      title: 'إدارة الأملاك',
      route: '/realestate',
      icon: './assets/media/icons/apps/realestate.svg'
    },
    {
      title: 'المعدات',
      route: '/fleet',
      icon: './assets/media/icons/apps/fleet.svg'
    },
    {
      title: 'مساعد الموظفين',
      route: '/staffassistant',
      icon: './assets/media/icons/apps/staffassistant.svg'
    },
    {
      title: 'نقاط البيع',
      route: '/pos',
      icon: './assets/media/icons/apps/pos.svg'
    },
    {
      title: 'الفندق',
      route: '/hotel',
      icon: './assets/media/icons/apps/hotel.svg'
    },
    {
      title: 'الإدارة',
      route: '/general',
      icon: './assets/media/icons/apps/general.svg'
    }
  ];

  constructor(
    private router: Router,
    private userPreferencesService: UserPreferencesService
  ) {}

  ngOnInit(): void {
    // يمكن إضافة أي منطق تهيئة هنا
  }

  // دالة للتعامل مع النقر على أيقونة التطبيق
  onAppClick(route: string, event: Event): void {
    event.preventDefault(); // منع السلوك الافتراضي للرابط

    console.log('App clicked:', route); // للتشخيص

    // التحقق من نوع المسار والتعامل معه بشكل مناسب
    if (route === '/dashboard') {
      // للداشبورد الرئيسية، نحتاج للتبديل إلى وضع القائمة العمودية وفتح الداشبورد
      console.log('Switching to vertical mode and navigating to dashboard');
      this.userPreferencesService.setVerticalMode();

      // الانتقال إلى الداشبورد مع إعادة تحميل الصفحة للتأكد من تطبيق التغييرات
      window.location.href = '/dashboard';
    } else {
      // للوحدات الأخرى، الانتقال مباشرة مع البقاء في وضع الشبكة
      console.log('Navigating to:', route);
      this.router.navigate([route]);
    }
  }
}
