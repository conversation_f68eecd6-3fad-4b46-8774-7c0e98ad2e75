.apps-grid-container {
  padding: 20px;
  background-color: #f5f8fa;
  min-height: 100vh;
  width: 100%;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 25px;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 15px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-decoration: none;
  color: #3f4254;
  cursor: pointer;
  height: 140px;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease;
  }

  .app-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .app-title {
    font-size: 16px;
    font-weight: 500;
    margin-top: 8px;
  }
}

@media (max-width: 768px) {
  .apps-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .app-item {
    padding: 15px 10px;
    height: 120px;

    .app-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 10px;
    }

    .app-title {
      font-size: 14px;
    }
  }
}