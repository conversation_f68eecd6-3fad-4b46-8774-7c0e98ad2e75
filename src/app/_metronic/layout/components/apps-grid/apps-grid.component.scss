.apps-grid-container {
  padding: 20px;
  background-color: #f5f8fa;
  min-height: 100%;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  justify-content: center;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px 10px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-decoration: none;
  color: #3f4254;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .app-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .app-title {
    font-size: 14px;
    font-weight: 500;
    margin-top: 5px;
  }
}

@media (max-width: 768px) {
  .apps-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
  }

  .app-item {
    padding: 10px;

    .app-icon {
      width: 40px;
      height: 40px;
    }

    .app-title {
      font-size: 12px;
    }
  }
}