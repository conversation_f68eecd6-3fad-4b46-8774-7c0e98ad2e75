import { Component, OnInit } from '@angular/core';

interface AppItem {
  title: string;
  route: string;
  icon: string;
  iconColor?: string;
  permission?: any;
}

@Component({
  selector: 'app-apps-grid',
  templateUrl: './apps-grid.component.html',
  styleUrls: ['./apps-grid.component.scss']
})
export class AppsGridComponent implements OnInit {

  apps: AppItem[] = [
    {
      title: 'الرئيسية',
      route: '/dashboard',
      icon: './assets/media/icons/apps/dashboard.svg'
    },
    {
      title: 'المخزون',
      route: '/inventory',
      icon: './assets/media/icons/apps/inventory.svg'
    },
    {
      title: 'المشتريات',
      route: '/purchases',
      icon: './assets/media/icons/apps/purchases.svg'
    },
    {
      title: 'المبيعات',
      route: '/sales',
      icon: './assets/media/icons/apps/sales.svg'
    },
    {
      title: 'CRM',
      route: '/crm',
      icon: './assets/media/icons/apps/crm.svg'
    },
    {
      title: 'الحسابات',
      route: '/accounting',
      icon: './assets/media/icons/apps/accounting.svg'
    },
    {
      title: 'الموارد البشرية',
      route: '/hr',
      icon: './assets/media/icons/apps/hr.svg'
    },
    {
      title: 'المشاريع',
      route: '/project',
      icon: './assets/media/icons/apps/project.svg'
    },
    {
      title: 'الصيانة',
      route: '/maintenance',
      icon: './assets/media/icons/apps/maintenance.svg'
    },
    {
      title: 'التصنيع',
      route: '/manufacturing',
      icon: './assets/media/icons/apps/manufacturing.svg'
    },
    {
      title: 'إدارة الأملاك',
      route: '/realestate',
      icon: './assets/media/icons/apps/realestate.svg'
    },
    {
      title: 'المعدات',
      route: '/fleet',
      icon: './assets/media/icons/apps/fleet.svg'
    },
    {
      title: 'مساعد الموظفين',
      route: '/staffassistant',
      icon: './assets/media/icons/apps/staffassistant.svg'
    },
    {
      title: 'نقاط البيع',
      route: '/pos',
      icon: './assets/media/icons/apps/pos.svg'
    },
    {
      title: 'الفندق',
      route: '/hotel',
      icon: './assets/media/icons/apps/hotel.svg'
    },
    {
      title: 'الإدارة',
      route: '/general',
      icon: './assets/media/icons/apps/general.svg'
    }
  ];

  constructor() {}

  ngOnInit(): void {
    // يمكن إضافة أي منطق تهيئة هنا
  }
}
