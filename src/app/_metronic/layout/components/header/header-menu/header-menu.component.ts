import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { LayoutType } from '../../../core/configs/config';
import { LayoutInitService } from '../../../core/layout-init.service';
import { LayoutService } from '../../../core/layout.service';
import { HttpClient } from '@angular/common/http';
import { filter, map, mergeMap, of } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { PageInfoService } from '../../../core/page-info.service';

@Component({
    selector: 'app-header-menu',
    templateUrl: './header-menu.component.html',
    styleUrls: ['./header-menu.component.scss'],
    standalone: false
})
export class HeaderMenuComponent implements OnInit {
  menuItems: any[] = [];
  constructor(private router: Router,
    private pageInfo: PageInfoService,
    private route: ActivatedRoute,
    private http: HttpClient,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
    private layout: LayoutService, private layoutInit: LayoutInitService) { }

  ngOnInit(): void {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd),
        map(() => {
          let r = this.route.firstChild;
          let child = r;
          while (child) {
            if (child.firstChild) {
              child = child.firstChild;
              r = child;
            } else {
              child = null;
            }
          }
          return r;
        }),
        // Add null check and ensure we return Observable
        mergeMap((rr: any) => {
          if (rr && rr.data) {
            return rr.data;
          } else {
            // Import 'of' from 'rxjs' at the top of the file if it's not already there
            return of({tagId: null});
          }
        })
      )
      .subscribe((d: any) => {
        if (d.tagId) {
          const tagId = d.tagId;
          this.loadMenuItems(tagId);
        } else {
          this.menuItems = [];
        }
      });
    // Add safer initialization with null checks
    if (this.router.navigated) {
      let r = this.route.firstChild;
      // Only load menu items if route data exists and has tagId
      const tagId = r?.snapshot?.data?.tagId;
      if (tagId) {
        this.loadMenuItems(tagId);
      } else {
        // Clear menu items if no tagId is found
        this.menuItems = [];
      }
    }
  }

  loadMenuItems(tagId: number) {
    if (tagId) {
      this.http.get<any>('/api/menuItems/' + tagId)
        .subscribe(r => {
          this.menuItems = r.data;
          this.cd.detectChanges();
        });
    } else {
      this.menuItems = [];
    }
  }
  calculateMenuItemCssClass(url: string): string {
    return checkIsActive(this.router.url, url) ? 'active' : '';
  }

  setBaseLayoutType(layoutType: LayoutType) {
    this.layoutInit.setBaseLayoutType(layoutType);
  }

  setToolbar(toolbarLayout: 'classic' | 'accounting' | 'extended' | 'reports' | 'saas') {
    const currentConfig = { ...this.layout.layoutConfigSubject.value };
    if (currentConfig && currentConfig.app && currentConfig.app.toolbar) {
      currentConfig.app.toolbar.layout = toolbarLayout;
      this.layout.saveBaseConfig(currentConfig)
    }
  }

  getMenuItemName(item: any) {
    return this.translateService.currentLang == 'ar' ? item.nameAr : item.nameEn;
  }
  getSubMenuPlacement(): string {
    return this.translateService.currentLang == 'ar' ? 'bottom-end' : 'bottom-start';
  }

  updateTitle(title: string, url: string) {
    this.pageInfo.updateTitle(title);
    const routes = this.pageInfo.breadcrumbs.getValue();
    if (routes.findIndex(r => r.path == url) < 0) {
      if (routes.length >= 1) {
        routes.push({ path: '', title: '', isActive: false, isSeparator: true });
        routes.forEach(r => {
          r.isActive = false;
        });
      }
      routes.push({ path: url, title: title, isActive: true, isSeparator: false });
      if (routes.length > 10) {
        routes.splice(0, routes.length - 10);
      }
      this.pageInfo.breadcrumbs.next(routes);
    }
  }
}

const getCurrentUrl = (pathname: string): string => {
  return pathname.split(/[?#]/)[0];
};

const checkIsActive = (pathname: string, url: string) => {
  const current = getCurrentUrl(pathname);
  if (!current || !url) {
    return false;
  }

  if (current === url) {
    return true;
  }

  if (current.indexOf(url) > -1) {
    return true;
  }

  return false;
};
