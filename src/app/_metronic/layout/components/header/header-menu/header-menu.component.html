<!-- Dashboard -->

<ng-container *ngIf="menuItems.length>0">
  <ng-container *ngFor="let item of menuItems">
    <!-- One Level -->
    <div class="menu-item me-lg-1" *ngIf="!item.hasChildren">
      <a class="menu-link py-3" routerLinkActive="active menu-here" routerLink="/dashboard"><span class="menu-title"
          translate="MENU.DASHBOARD"></span></a>
    </div>
    <!-- Two Levels or More -->
    <div class="menu-item menu-lg-down-accordion me-lg-1" data-kt-menu-trigger="{default: 'click', lg: 'hover'}"
      [attr.data-kt-menu-placement]="getSubMenuPlacement()" *ngIf="item.hasChildren">
      <span class="menu-link py-3">
        <span class="menu-title">{{getMenuItemName(item)}}</span>
        <span class="menu-arrow d-lg-none"></span>
      </span>
      <div class="
        menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown menu-rounded-0
        py-lg-4
        w-lg-225px
      " data-kt-menu-dismiss="true">
        <ng-container *ngFor="let child of item.children">
          <span *ngIf="child.hasChildren" class="px-6 bold">
            {{getMenuItemName(child)}}
          </span>
          <div  class="menu-item  me-lg-1" data-kt-menu-trigger="{default:'click', lg: 'hover'}"
            data-kt-menu-placement="left-start" *ngIf="!child.hasChildren">
            <a class="menu-link px-8" [routerLink]="child.route" (click)="updateTitle(getMenuItemName(child),child.route)">
              <span class="menu-title">
                {{getMenuItemName(child)}}
              </span></a>
          </div>
          <ng-container *ngIf="child.hasChildren">
            <ng-container *ngFor="let cc of child.children">
              <div class="menu-item me-lg-1" data-kt-menu-trigger="{default:'click', lg: 'hover'}"
                data-kt-menu-placement="left-start">
                <a class="menu-link px-8" [routerLink]="cc.route">
                  <span class="menu-title">
                    {{getMenuItemName(cc)}}
                  </span></a>
              </div>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </ng-container>
</ng-container>
