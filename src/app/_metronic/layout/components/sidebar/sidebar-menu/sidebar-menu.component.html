<!--begin::Menu wrapper-->
<div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5" data-kt-scroll="true"
  data-kt-scroll-activate="true" data-kt-scroll-height="auto"
  data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
  data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">

  <!-- El botón de cambio de modo ahora está en la barra de navegación -->

  <!-- Vertical Menu -->
  <div *ngIf="currentDisplayMode === menuDisplayMode.VERTICAL" class="menu menu-column menu-rounded menu-sub-indention px-3" id="#kt_app_sidebar_menu" data-kt-menu="true"
       data-kt-menu-expand="true">
    <!-- Dashboard -->
    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/dashboard" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen025.svg'" class="svg-icon svg-icon-2"></span>
        </span>
        <span class="menu-title" translate="MENU.DASHBOARD"></span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/hr" routerLinkActive="active"  *hasPermission="{ module: 'hr' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/communication/com005.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.hr' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/inventory" routerLinkActive="active"  *hasPermission="{ module: 'inventory' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/ecommerce/ecm006.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.apps.inventory' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/purchases" routerLinkActive="active"  *hasPermission="{ module: 'purchases' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/ecommerce/ecm002.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.purchase' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/sales" routerLinkActive="active"  *hasPermission="{ module: 'sales' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/finance/fin004.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.sales' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/crm" routerLinkActive="active"  *hasPermission="{ module: 'crm' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen016.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.crm' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/accounting" routerLinkActive="active"  *hasPermission="{ module: 'accounting' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/finance/fin008.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.accounting' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/project" routerLinkActive="active"  *hasPermission="{ module: 'project' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.project' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/maintenance" routerLinkActive="active"  *hasPermission="{ module: 'maintenance' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/coding/cod009.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.maintenance' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/manufacturing" routerLinkActive="active"  *hasPermission="{ module: 'manufacturing' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/abstract/abs013.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.manufacturing' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/realestate" routerLinkActive="active"  *hasPermission="{ module: 'realestate' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen001.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.realestate' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/fleet" routerLinkActive="active"  *hasPermission="{ module: 'fleet' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/maps/map003.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.fleet' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/staffassistant" routerLinkActive="active"  *hasPermission="{ module: 'staffassistant' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/abstract/abs021.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.StaffAssistant' | uppercase | translate }}</span>
      </a>
    </div>


    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/pos" routerLinkActive="active"  *hasPermission="{ module: 'pos' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen007.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.pos' | uppercase | translate }}</span>
      </a>
    </div>

    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/hotel" routerLinkActive="active"  *hasPermission="{ module: 'hotel' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen007.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.hotel' | uppercase | translate }}</span>
      </a>
    </div>


    <!-- <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/permissions" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen048.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.permissions' | uppercase | translate }}</span>
      </a>
    </div> -->
    <!--<div class="menu-item">
      <a class="menu-link without-sub" routerLink="/companies" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen005.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.company' | uppercase | translate }}</span>
      </a>
    </div>-->
    <!-- <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/permissions/roles" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen047.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.roles' | uppercase | translate }}</span>
      </a>
    </div> -->
    <!-- <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/permissions/users" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/general/gen049.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.users' | uppercase | translate }}</span>
      </a>
    </div> -->
    <div class="menu-item">
      <a class="menu-link without-sub" routerLink="/general" routerLinkActive="active"  *hasPermission="{ module: 'admin' }">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/abstract/abs031.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.admin' | uppercase | translate }}</span>
      </a>
    </div>
    <!--<div class="menu-item">
      <a class="menu-link without-sub" routerLink="/settings" routerLinkActive="active">
        <span class="menu-icon">
          <span [inlineSVG]="'./assets/media/icons/duotune/abstract/abs046.svg'" class="svg-icon svg-icon-2"></span>
        </span><span class="menu-title">{{ 'Menu.settings' | uppercase | translate }}</span>
      </a>
    </div>-->



    <!-- Layout builder -->
    <!--<div class="menu-item" *ngIf="isAdmin">
    <a class="menu-link without-sub" routerLink="/companies" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">الشركات</span></a>
  </div>
  <div class="menu-item" *ngIf="isAdmin">
    <a class="menu-link without-sub" routerLink="/users" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">المستخدمين</span></a>
  </div>
  <div class="menu-item">
    <a class="menu-link without-sub" routerLink="/transactions" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">المعاملات</span></a>
  </div>
  <div class="menu-item">
    <a class="menu-link without-sub" routerLink="/inquiries" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">الطلبات</span></a>
  </div>
  <div class="menu-item" *ngIf="isAdmin">
    <a class="menu-link without-sub" routerLink="/permissions" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">الصلاحيات</span></a>
  </div>
  <div class="menu-item" *ngIf="isAdmin">
    <a class="menu-link without-sub" routerLink="/subscriptions" routerLinkActive="active"><span class="menu-icon">
        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-2"></span>
      </span><span class="menu-title">الإشتراكات</span></a>
  </div>-->
    <!-- Separator -->
    <!--<div class="menu-item">
    <div class="menu-content pt-8 pb-2">
      <span class="menu-section text-muted text-uppercase fs-8 ls-1">Crafted</span>
    </div>
  </div>-->
    <!-- Pages -->
    <!--<div class="menu-item menu-accordion" data-kt-menu-trigger="click" routerLinkActive="here show">
    <span class="menu-link"><span class="menu-icon"><span class="svg-icon svg-icon-2"
          [inlineSVG]="'./assets/media/icons/duotune/general/gen022.svg'"></span></span><span
        class="menu-title">Pages</span><span class="menu-arrow"></span></span>
    <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
      <div class="menu-item menu-accordion" routerLinkActive="here show" data-kt-menu-trigger="click">
        <span class="menu-link"><span class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title" data-link="/crafted/pages/profile">Profile</span><span
            class="menu-arrow"></span></span>
        <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/profile/overview"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Overview</span></a>
          </div>
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/profile/projects"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Projects</span></a>
          </div>
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/profile/campaigns"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Campaigns</span></a>
          </div>
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/profile/documents"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Documents</span></a>
          </div>
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/profile/connections"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Connections</span></a>
          </div>
        </div>
      </div>
      <div class="menu-item menu-accordion" routerLinkActive="here show" data-kt-menu-trigger="click">
        <span class="menu-link"><span class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title" data-link="/crafted/pages/wizards">Wizards</span><span
            class="menu-arrow"></span></span>
        <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/wizards/horizontal"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Horizontal</span></a>
          </div>
          <div class="menu-item">
            <a class="menu-link without-sub" routerLinkActive="active"
              routerLink="/crafted/pages/wizards/vertical"><span class="menu-bullet"><span
                  class="bullet bullet-dot"></span></span><span class="menu-title">Vertical</span></a>
          </div>
        </div>
      </div>
    </div>
  </div>-->
    <!-- Accounts -->
    <!--<div class="menu-item menu-accordion" data-kt-menu-trigger="click" routerLinkActive="here show">
    <span class="menu-link"><span class="menu-icon"><span class="svg-icon svg-icon-2"
          [inlineSVG]="'./assets/media/icons/duotune/communication/com006.svg'"></span></span><span class="menu-title"
        data-link="/crafted/account">Accounts</span><span class="menu-arrow"></span></span>
    <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/account/overview"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Overview</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/account/settings"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Settings</span></a>
      </div>
    </div>
  </div>-->
    <!-- Errors -->
    <!--<div class="menu-item menu-accordion" data-kt-menu-trigger="click" routerLinkActive="here show">
    <span class="menu-link"><span class="menu-icon"><span class="svg-icon svg-icon-2"
          [inlineSVG]="'./assets/media/icons/duotune/general/gen040.svg'"></span></span><span class="menu-title"
        data-link="/error">Errors</span><span class="menu-arrow"></span></span>
    <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
      <div class="menu-item">
        <a class="menu-link without-sub" routerLink="/error/404" routerLinkActive="active"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span class="menu-title">Error
            404</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLink="/error/500" routerLinkActive="active"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span class="menu-title">Error
            500</span></a>
      </div>
    </div>
  </div>-->
    <!-- Widgets -->
    <!--<div class="menu-item menu-accordion" data-kt-menu-trigger="click" routerLinkActive="here show">
    <span class="menu-link"><span class="menu-icon"><span class="svg-icon svg-icon-2"
          [inlineSVG]="'./assets/media/icons/duotune/layouts/lay008.svg'"></span></span><span class="menu-title"
        data-link="/crafted/widgets">Widgets</span><span class="menu-arrow"></span></span>
    <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/lists"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Lists</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/statistics"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Statistics</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/charts"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Charts</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/mixed"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Mixed</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/tables"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Tables</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/crafted/widgets/feeds"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span
            class="menu-title">Feeds</span></a>
      </div>
    </div>
  </div>-->
    <!-- Separator -->
    <!-- <div class="menu-item">
    <div class="menu-content pt-8 pb-2">
      <span class="menu-section text-muted text-uppercase fs-8 ls-1">Apps</span>
    </div>
  </div> -->
    <!-- Chat -->
    <!--<div class="menu-item menu-accordion" data-kt-menu-trigger="click" routerLinkActive="here show">
    <span class="menu-link"><span class="menu-icon"><span class="svg-icon svg-icon-2"
          [inlineSVG]="'./assets/media/icons/duotune/communication/com012.svg'"></span></span><span class="menu-title"
        data-link="/apps/chat">Chat</span><span class="menu-arrow"></span></span>
    <div class="menu-sub menu-sub-accordion" routerLinkActive="menu-active-bg">
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/apps/chat/private-chat"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span class="menu-title">Private
            Chat</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/apps/chat/group-chat"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span class="menu-title">Group
            Chart</span></a>
      </div>
      <div class="menu-item">
        <a class="menu-link without-sub" routerLinkActive="active" routerLink="/apps/chat/drawer-chat"><span
            class="menu-bullet"><span class="bullet bullet-dot"></span></span><span class="menu-title">Drawer
            Chart</span></a>
      </div>
    </div>
  </div>-->


  </div>
  <!--end::Vertical Menu-->

  <!-- La cuadrícula ahora se muestra en el componente principal -->
  <!--end::Menu-->
</div>
<!--end::Menu wrapper-->
