import { Component, OnInit } from '@angular/core';
import { TokenKeys } from 'src/app/modules/auth/services';
import { TokenService } from 'src/app/modules/auth/services/token.service';
import { MenuDisplayMode, UserPreferencesService } from '../../../core/user-preferences.service';

@Component({
    selector: 'app-sidebar-menu',
    templateUrl: './sidebar-menu.component.html',
    styleUrls: ['./sidebar-menu.component.scss'],
    standalone: false
})
export class SidebarMenuComponent implements OnInit {
  menuDisplayMode = MenuDisplayMode;
  currentDisplayMode: MenuDisplayMode;

  constructor(
    private tokenSerivce: TokenService,
    private userPreferencesService: UserPreferencesService
  ) {
    this.currentDisplayMode = this.userPreferencesService.menuDisplayMode;
  }

  ngOnInit(): void {
    this.userPreferencesService.menuDisplayMode$.subscribe(mode => {
      this.currentDisplayMode = mode;
    });
  }

  get isAdmin() {
    const auth = this.tokenSerivce.getAuthFromLocalStorage();
    return auth?.user?.roles?.findIndex((r: any) => r.toLowerCase() == 'admin') >= 0;
  }

  toggleMenuDisplayMode(): void {
    this.userPreferencesService.toggleMenuDisplayMode();
  }
}
