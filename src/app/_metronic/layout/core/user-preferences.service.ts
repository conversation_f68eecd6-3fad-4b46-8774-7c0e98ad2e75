import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export enum MenuDisplayMode {
  VERTICAL = 'vertical',
  GRID = 'grid'
}

@Injectable({
  providedIn: 'root'
})
export class UserPreferencesService {
  private menuDisplayModeSubject: BehaviorSubject<MenuDisplayMode>;
  public menuDisplayMode$: Observable<MenuDisplayMode>;

  private readonly STORAGE_KEY = 'falcon-menu-display-mode';

  constructor() {
    // تحميل التفضيلات المحفوظة أو استخدام القيمة الافتراضية
    const savedMode = localStorage.getItem(this.STORAGE_KEY) as MenuDisplayMode;
    this.menuDisplayModeSubject = new BehaviorSubject<MenuDisplayMode>(
      savedMode || MenuDisplayMode.VERTICAL
    );
    this.menuDisplayMode$ = this.menuDisplayModeSubject.asObservable();
  }

  // الحصول على وضع عرض القائمة الحالي
  public get menuDisplayMode(): MenuDisplayMode {
    return this.menuDisplayModeSubject.value;
  }

  // تعيين وضع عرض القائمة
  public setMenuDisplayMode(mode: MenuDisplayMode): void {
    localStorage.setItem(this.STORAGE_KEY, mode);
    this.menuDisplayModeSubject.next(mode);
  }

  // تبديل وضع عرض القائمة
  public toggleMenuDisplayMode(): void {
    const currentMode = this.menuDisplayMode;
    const newMode = currentMode === MenuDisplayMode.VERTICAL
      ? MenuDisplayMode.GRID
      : MenuDisplayMode.VERTICAL;

    this.setMenuDisplayMode(newMode);
  }

  // تعيين وضع القائمة الرأسية
  public setVerticalMode(): void {
    this.setMenuDisplayMode(MenuDisplayMode.VERTICAL);
  }

  // تعيين وضع الشبكة
  public setGridMode(): void {
    this.setMenuDisplayMode(MenuDisplayMode.GRID);
  }
}
