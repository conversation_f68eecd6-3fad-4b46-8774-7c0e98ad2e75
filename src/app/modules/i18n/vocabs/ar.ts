// Arabic
export const locale = {
  lang: 'ar',
  data: {
    TRANSLATOR: {
      SELECT: 'اختر اللغة',
    },
    MENU: {
      NEW: 'جديد',
      ACTIONS: 'إجراءات',
      CREATE_POST: 'إضافة جديد',
      PAGES: 'الصفحات',
      FEATURES: 'المزايا',
      APPS: {
        TITLE: 'التطبيقات',
        INVENTORY: 'المخزون',
      },
      DASHBOARD: 'الرئيسية',
      PERMISSIONS: 'الصلاحيات',
      COMPANY: 'بيانات الشركة',
      USERS: 'المستخدمين',
      ROLES: 'المجموعات',
      SETTINGS: 'الإعدادات',
      GENERAL: 'عام',
      ADMIN: 'الادارة',
      HR: 'الموارد البشرية',
      PURCHASE: 'المشتريات',
      Costs: 'المصروفات',
      CRM: 'CRM',
      ACCOUNTING: 'الحسابات',
      MAINTENANCE: 'الصيانة',
      MANUFACTURING: 'التصنيع',
      PROJECT: 'المشاريع',
      REALESTATE: 'ادارة الاملاك',
      FLEET: 'المعدات',
      POS: 'نقاط البيع',
      HOTEL: 'الفندق',
      SALES: 'المبيعات',
      STAFFASSISTANT: 'مساعد الموظفين',
    },
    AUTH: {
      GENERAL: {
        OR: 'أو',
        SUBMIT_BUTTON: 'حفظ',
        NO_ACCOUNT: 'ليس لديك حساب ?',
        SIGNUP_BUTTON: 'مستخدم جديد',
        FORGOT_BUTTON: 'نسيت كلمة المرور',
        BACK_BUTTON: 'رجوع',
        PRIVACY: 'الخصوصية',
        LEGAL: 'قانوني',
        CONTACT: 'إتصل',
      },
      LOGIN: {
        TITLE: 'الاسم',
        BUTTON: 'تسجيل الدخول',
      },
      FORGOT: {
        TITLE: 'Forgotten Password?',
        DESC: 'Enter your email to reset your password',
        SUCCESS: 'Your account has been successfully reset.',
      },
      REGISTER: {
        TITLE: 'Sign Up',
        DESC: 'Enter your details to create your account',
        SUCCESS: 'Your account has been successfuly registered.',
      },
      INPUT: {
        EMAIL: 'Email',
        FULLNAME: 'Fullname',
        PASSWORD: 'Mot de passe',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: "Nom d'utilisateur",
      },
      VALIDATION: {
        INVALID: "{{name}} n'est pas valide",
        REQUIRED: '{{name}} est requis',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        AGREEMENT_REQUIRED: 'Accepting terms & conditions are required',
        NOT_FOUND: 'The requested {{name}} is not found',
        INVALID_LOGIN: 'The login detail is incorrect',
        REQUIRED_FIELD: 'Required field',
        MIN_LENGTH_FIELD: 'Minimum field length:',
        MAX_LENGTH_FIELD: 'Maximum field length:',
        INVALID_FIELD: 'Field is not valid',
      },
    },
    SALES: {
      PSP: 'مبيعات الاصناف',
      SSP: 'مبعات الخدمات',
      OfProfit: 'من الربح',
      OfTotalBill: 'من اجمالي الفاتورة',
    },
    INVENTORY: {
      WAREHOUSES: {
        NAMEAR: 'الاسم بالعربية',
        NAMEEN: 'الاسم بالانجليزية',
        SHORT: 'الاسم المختصر',
        ADDRESS: 'العنوان',
        SEARCH: 'البحث فى المستودعات',
        CREATE: 'إضافة جديد',
        FILTER: 'تنقية النتائج',
        USERS: 'المستخدمين',
        CREATETITLE: 'إضافة مخزن جديد',
        EDITTITLE: 'تحديث بيانات مخزن',
      },
    },
    COMPANY: {
      NAMEAR: 'الاسم بالعربية',
      NAMEEN: 'الاسم بالانجليزية',
      CRN: 'رقم السجل التجاري',
      ADDRESS: 'العنوان',
      MAIL: 'البريد الالكتروني',
      MOBILE: 'الهاتف',
      HEADER: 'Header',
      FOOTER: 'Footer',
      LOGO: 'الشعار',
      CREATETITLE: 'تحديث بيانات الشركة',
    },
    COMMON: {
      LOADING: 'تحميل...',
      CashBox: 'صندوق النقدية',
      ACTIONS: 'الإجراءات',
      SUBMIT: 'حفظ',
      ROLE: 'اسم المجموعة',
      DELETE: 'حذف',
      EDIT: 'تحديث',
      PRINT: 'طباعة',
      DISCARD: 'تجاهل',
      EXPORT: 'تصدير',
      // POS Common Translations - المفاتيح غير المكررة فقط
      SalesReports: 'تقارير المبيعات',
      BestSellers: 'الأكثر مبيعاً',
      // مفاتيح عامة
      Settings: 'الإعدادات',
      Inventory: 'المخزون',
      Stats: 'الإحصائيات',
      SystemStatus: 'حالة النظام',
      // الفترات الزمنية
      Daily: 'يومي',
      Weekly: 'أسبوعي',
      Monthly: 'شهري',
      // أجهزة نقاط البيع
      CashDrawer: 'درج النقد',
      CustomerDisplay: 'شاشة العميل',
      BarcodeScanner: 'ماسح الباركود',
      // وحدة الإعدادات العامة
      Companies: 'الشركات',
      Branches: 'الفروع',
      Currencies: 'العملات',
      Cities: 'المدن',
      Countries: 'الدول',
      RecentStatistics: 'الإحصائيات الأخيرة',
      Latest: 'أحدث',
      LastOpened: 'آخر فتح',
      ReceiptPrinter: 'طابعة الإيصالات',
      StatusOnline: 'الحالة متصل',
      Ready: 'جاهز',
      LiveData: 'بيانات مباشرة',
      // Dashboard Common
      InventoryOverview: 'نظرة عامة على المخزون',
      InventoryStats: 'إحصائيات المخزون',
      TotalProducts: 'إجمالي المنتجات',
      TotalWarehouses: 'إجمالي المخازن',
      LowStockItems: 'المنتجات منخفضة المخزون',
      InventoryValue: 'قيمة المخزون',
      InventoryMovement: 'حركة المخزون',
      Last30Days: 'آخر 30 يوم',
      StockDistribution: 'توزيع المخزون',
      ByCategory: 'حسب الفئة',
      RecentActivities: 'الأنشطة الأخيرة',
      LatestInventoryTransactions: 'آخر معاملات المخزون',
      ViewAll: 'عرض الكل',
      QuickAccess: 'الوصول السريع',
      CommonInventoryOperations: 'عمليات المخزون الشائعة',
      NewReceipt: 'استلام جديد',
      NewDelivery: 'تسليم جديد',
      NewTransfer: 'نقل جديد',
      ValuationReport: 'تقرير التقييم',
      ManageProducts: 'إدارة المنتجات',
      ManageWarehouses: 'إدارة المخازن',
      ItemsNeedingRestock: 'المنتجات التي تحتاج إلى إعادة تخزين',
      // HR Dashboard
      HROverview: 'نظرة عامة على الموارد البشرية',
      HRStats: 'إحصائيات الموارد البشرية',
      TotalEmployees: 'إجمالي الموظفين',
      AttendanceRate: 'معدل الحضور',
      PendingVacations: 'الإجازات المعلقة',
      TurnoverRate: 'معدل دوران الموظفين',
      DepartmentDistribution: 'توزيع الموظفين حسب الأقسام',
      EmployeesByDepartment: 'الموظفين حسب القسم',
      AttendanceTrends: 'اتجاهات الحضور',
      LatestHRTransactions: 'آخر معاملات الموارد البشرية',
      CommonHROperations: 'عمليات الموارد البشرية الشائعة',
      NewEmployee: 'موظف جديد',
      NewVacation: 'إجازة جديدة',
      AttendanceReports: 'تقارير الحضور',
      NewBonus: 'مكافأة جديدة',
      JobDescriptions: 'الوصوف الوظيفية',
      UpcomingEvents: 'الأحداث القادمة',
      NextTwoWeeks: 'الأسبوعين القادمين',
      EmployeeBirthdays: 'أعياد ميلاد الموظفين',
      ThisMonth: 'هذا الشهر',
      // Main Dashboard
      SystemOverview: 'نظرة عامة على النظام',
      SystemStats: 'إحصائيات النظام',
      ActiveModules: 'الوحدات النشطة',
      TotalUsers: 'إجمالي المستخدمين',
      TotalTransactions: 'إجمالي المعاملات',
      SystemPerformance: 'أداء النظام',
      RecentTransactions: 'أحدث المعاملات',
      ModuleUsage: 'استخدام الوحدات',
      SystemAlerts: 'تنبيهات النظام',
      QuickLinks: 'روابط سريعة',
      // Purchases Dashboard
      PurchasesOverview: 'نظرة عامة على المشتريات',
      PurchasesStats: 'إحصائيات المشتريات',
      TotalPurchases: 'إجمالي المشتريات',
      PendingOrders: 'الطلبات المعلقة',
      TotalSuppliers: 'إجمالي الموردين',
      PurchasesTrends: 'اتجاهات المشتريات',
      TopSuppliers: 'أهم الموردين',
      RecentPurchases: 'أحدث المشتريات',
      PurchasesByCategory: 'المشتريات حسب الفئة',
      PurchaseValue: 'قيمة المشتريات',
      TotalSpent: 'إجمالي الإنفاق',
      ActiveSuppliers: 'الموردين النشطين',
      RequiresAttention: 'تتطلب الاهتمام',
      ByPurchaseVolume: 'حسب حجم المشتريات',
      PurchasesTrendsChart: 'رسم بياني لاتجاهات المشتريات',
      CurrentYear: 'العام الحالي',
      Supplier: 'المورد',
      Amount: 'المبلغ',
      OrderID: 'رقم الطلب',
      Status: 'الحالة',
      Category: 'الفئة',
      NewPurchaseOrder: 'طلب شراء جديد',
      CreateNewOrder: 'إنشاء طلب جديد',
      ApprovePurchases: 'الموافقة على المشتريات',
      PendingApprovals: 'الموافقات المعلقة',
      PurchaseReports: 'تقارير المشتريات',
      GenerateReports: 'إنشاء التقارير',
      ManageSuppliers: 'إدارة الموردين',
      AddEditSuppliers: 'إضافة/تعديل الموردين',
      // Sales Dashboard
      SalesOverview: 'نظرة عامة على المبيعات',
      SalesStats: 'إحصائيات المبيعات',
      TotalSales: 'إجمالي المبيعات',
      TotalRevenue: 'إجمالي الإيرادات',
      TotalCustomers: 'إجمالي العملاء',
      SalesTrends: 'اتجاهات المبيعات',
      TopProducts: 'أفضل المنتجات',
      TopCustomers: 'أهم العملاء',
      RecentSales: 'أحدث المبيعات',
      SalesByRegion: 'المبيعات حسب المنطقة',
      RevenueGrowth: 'نمو الإيرادات',
      ActiveCustomers: 'العملاء النشطين',
      BySalesVolume: 'حسب حجم المبيعات',
      SalesTrendsChart: 'رسم بياني لاتجاهات المبيعات',
      Product: 'المنتج',
      Customer: 'العميل',
      Region: 'المنطقة',
      // CRM Dashboard
      CRMOverview: 'نظرة عامة على إدارة علاقات العملاء',
      CRMStats: 'إحصائيات إدارة علاقات العملاء',
      CRMDashboard: 'لوحة معلومات إدارة علاقات العملاء',
      CustomerRelationshipManagement: 'إدارة علاقات العملاء',
      Active: 'نشط',
      ActiveLeads: 'العملاء المحتملين النشطين',
      NewCustomers: 'العملاء الجدد',
      CustomerSatisfaction: 'رضا العملاء',
      OpenTickets: 'التذاكر المفتوحة',
      LeadConversion: 'تحويل العملاء المحتملين',
      ConversionRate: 'معدل التحويل',
      CustomerRetention: 'الاحتفاظ بالعملاء',
      SalesOpportunities: 'فرص المبيعات',
      GrowthRate: 'معدل النمو',
      MessagesSent: 'الرسائل المرسلة',
      WhatsApp: 'واتساب',
      SMS: 'رسائل نصية',
      Email: 'بريد إلكتروني',
      Satisfied: 'راضون',
      Unsatisfied: 'غير راضين',
      CustomerEngagement: 'تفاعل العملاء',
      Created: 'تم الإنشاء',
      ShowAll: 'عرض الكل',
      Today: 'اليوم',
      Yesterday: 'الأمس',
      Last7Days: 'آخر 7 أيام',
      Last30DaysCRM: 'آخر 30 يوم',
      LastMonth: 'الشهر الماضي',
      RecentActivitiesCRM: 'الأنشطة الأخيرة',
      LatestCustomerInteractions: 'أحدث تفاعلات العملاء',
      NewLeadCreated: 'تم إنشاء عميل محتمل جديد',
      ByUser: 'بواسطة المستخدم',
      CustomerMeeting: 'اجتماع مع العميل',
      WithClient: 'مع العميل',
      ProposalSent: 'تم إرسال عرض',
      ToClient: 'إلى العميل',
      NewEnquiryReceived: 'تم استلام استفسار جديد',
      ViaWebsite: 'عبر الموقع الإلكتروني',
      QuickActions: 'إجراءات سريعة',
      CommonTasks: 'المهام الشائعة',
      AddNewLead: 'إضافة عميل محتمل جديد',
      CreateNewLeadRecord: 'إنشاء سجل عميل محتمل جديد',
      ScheduleMeeting: 'جدولة اجتماع',
      ArrangeCustomerMeeting: 'ترتيب اجتماع مع العميل',
      SendCampaign: 'إرسال حملة',
      CreateMarketingCampaign: 'إنشاء حملة تسويقية',
      GenerateReportsCRM: 'إنشاء تقارير',
      CreateCustomReports: 'إنشاء تقارير مخصصة',
      UpcomingTasks: 'المهام القادمة',
      ScheduledActivities: 'الأنشطة المجدولة',
      DealsPipeline: 'مسار الصفقات',
      SalesForecast: 'توقعات المبيعات',
      NewCampaign: 'حملة جديدة',
      // Accounting Dashboard
      AccountingOverview: 'نظرة عامة على الحسابات',
      AccountingStats: 'إحصائيات الحسابات',
      TotalIncome: 'إجمالي الدخل',
      TotalExpenses: 'إجمالي المصروفات',
      NetProfit: 'صافي الربح',
      AccountsReceivable: 'الحسابات المدينة',
      AccountsPayable: 'الحسابات الدائنة',
      CashFlow: 'التدفق النقدي',
      BudgetVariance: 'تباين الميزانية',
      // Accounting Dashboard New
      AccountingDashboard: 'لوحة تحكم الحسابات',
      AccountingModule: 'الحسابات',
      AccountingRevenue: 'الإيرادات',
      AccountingExpenses: 'المصروفات',
      AccountingReceivables: 'المستحقات',
      AccountingPayables: 'الالتزامات',
      AccountingCash: 'النقدية',
      AccountingRevenueAndExpenses: 'الإيرادات والمصروفات',
      AccountingLastSixMonths: 'الستة أشهر الأخيرة',
      AccountingQuickActions: 'الإجراءات السريعة',
      AccountingCommonOperations: 'العمليات الشائعة',
      AccountingRecentTransactions: 'المعاملات الأخيرة',
      AccountingLastFiveTransactions: 'المعاملات الخمس الأخيرة',
      AccountingTransactionID: 'رقم المعاملة',
      AccountingType: 'النوع',
      AccountingAmount: 'المبلغ',
      AccountingDate: 'التاريخ',
      AccountingStatus: 'الحالة',
      AccountingViewReport: 'عرض التقرير',
      AccountingFinancialReports: 'التقارير المالية',
      AccountingQuickFinancialReports: 'تقارير مالية سريعة',
      AccountingViewAllReports: 'عرض كل التقارير',
      AccountingIncomeStatement: 'قائمة الدخل',
      AccountingBalanceSheet: 'الميزانية العمومية',
      AccountingCashFlowStatement: 'التدفقات النقدية',
      AccountingTotalAssets: 'إجمالي الأصول',
      AccountingNetCashFlow: 'صافي التدفق النقدي',
      AccountingHome: 'الرئيسية',

      // Project Dashboard
      ProjectDashboard: 'لوحة تحكم المشاريع',
      ProjectModule: 'المشاريع',
      ProjectTotal: 'إجمالي المشاريع',
      ProjectActive: 'المشاريع النشطة',
      ProjectCompleted: 'المشاريع المكتملة',
      ProjectPending: 'المشاريع المعلقة',
      TenderTotal: 'إجمالي المناقصات',
      TenderWon: 'المناقصات الفائزة',
      TenderLost: 'المناقصات الخاسرة',
      TenderPending: 'المناقصات المعلقة',
      ProjectProgressChart: 'تقدم المشاريع',
      ProjectLastSixMonths: 'الستة أشهر الأخيرة',
      ProjectQuickActions: 'الإجراءات السريعة',
      ProjectCommonOperations: 'العمليات الشائعة',
      ProjectRecentProjects: 'المشاريع الأخيرة',
      ProjectLastFiveProjects: 'المشاريع الخمس الأخيرة',
      ProjectID: 'رقم المشروع',
      ProjectName: 'اسم المشروع',
      ProjectClient: 'العميل',
      ProjectValue: 'القيمة',
      ProjectProgress: 'التقدم',
      ProjectStatus: 'الحالة',
      TenderStatus: 'حالة المناقصات',
      TenderCurrentStatus: 'الوضع الحالي',
      ProjectViewReport: 'عرض التقرير',
      ProjectViewDetails: 'عرض التفاصيل',
      ProjectViewAll: 'عرض الكل',
      ProjectRecentTenders: 'المناقصات الأخيرة',
      ProjectLastFiveTenders: 'المناقصات الخمس الأخيرة',
      TenderID: 'رقم المناقصة',
      TenderName: 'اسم المناقصة',
      ProjectHome: 'الرئيسية',

      // Maintenance Dashboard
      MaintenanceDashboard: 'لوحة تحكم الصيانة',
      MaintenanceModule: 'الصيانة',
      MaintenanceHome: 'الرئيسية',
      MaintenanceTotalEquipment: 'إجمالي المعدات',
      MaintenanceActiveEquipment: 'المعدات النشطة',
      MaintenanceNeedsMaintenance: 'تحتاج صيانة',
      MaintenanceUnderMaintenance: 'تحت الصيانة',
      MaintenanceTotalMalfunctions: 'إجمالي الأعطال',
      MaintenanceResolvedMalfunctions: 'الأعطال المصلحة',
      MaintenancePendingMalfunctions: 'الأعطال قيد المعالجة',
      MaintenanceCriticalMalfunctions: 'الأعطال الحرجة',
      MaintenanceRequestsOverTime: 'طلبات الصيانة عبر الزمن',
      MaintenanceLastSixMonths: 'الستة أشهر الأخيرة',
      MaintenanceQuickActions: 'الإجراءات السريعة',
      MaintenanceCommonOperations: 'العمليات الشائعة',
      MaintenanceRecentMalfunctions: 'الأعطال الأخيرة',
      MaintenanceLastFiveMalfunctions: 'آخر خمسة أعطال',
      MaintenanceID: 'رقم العطل',
      MaintenanceEquipment: 'المعدات',
      MaintenanceLocation: 'الموقع',
      MaintenanceType: 'النوع',
      MaintenanceDate: 'التاريخ',
      MaintenancePriority: 'الأولوية',
      MaintenanceStatus: 'الحالة',
      MaintenanceMalfunctionsByType: 'الأعطال حسب النوع',
      MaintenanceCurrentDistribution: 'التوزيع الحالي',
      MaintenanceViewReport: 'عرض التقرير',
      MaintenanceViewDetails: 'عرض التفاصيل',
      MaintenanceViewAll: 'عرض الكل',
      MaintenanceRecentRequests: 'طلبات الصيانة الأخيرة',
      MaintenanceLastFiveRequests: 'آخر خمسة طلبات',
      MaintenanceRequestID: 'رقم الطلب',
      MaintenanceDepartment: 'القسم',
      MaintenanceRequestedBy: 'مقدم الطلب',

      // Manufacturing Dashboard
      ManufacturingDashboard: 'لوحة تحكم التصنيع',
      ManufacturingModule: 'التصنيع',
      ManufacturingHome: 'الرئيسية',
      ManufacturingTotalOrders: 'إجمالي أوامر التصنيع',
      ManufacturingActiveOrders: 'أوامر التصنيع النشطة',
      ManufacturingCompletedOrders: 'أوامر التصنيع المكتملة',
      ManufacturingPendingOrders: 'أوامر التصنيع المعلقة',
      ManufacturingTotalProduction: 'إجمالي الإنتاج',
      ManufacturingDailyProduction: 'الإنتاج اليومي',
      ManufacturingWeeklyProduction: 'الإنتاج الأسبوعي',
      ManufacturingMonthlyProduction: 'الإنتاج الشهري',
      ManufacturingProductionTrend: 'اتجاه الإنتاج',
      ManufacturingLastSixMonths: 'الستة أشهر الأخيرة',
      ManufacturingQuickActions: 'الإجراءات السريعة',
      ManufacturingCommonOperations: 'العمليات الشائعة',
      ManufacturingRecentOrders: 'أوامر التصنيع الأخيرة',
      ManufacturingLastFiveOrders: 'آخر خمسة أوامر',
      ManufacturingOrderID: 'رقم الأمر',
      ManufacturingProduct: 'المنتج',
      ManufacturingQuantity: 'الكمية',
      ManufacturingStartDate: 'تاريخ البدء',
      ManufacturingDueDate: 'تاريخ الاستحقاق',
      ManufacturingProgress: 'التقدم',
      ManufacturingStatus: 'الحالة',
      ManufacturingProductionByStage: 'الإنتاج حسب المرحلة',
      ManufacturingCurrentDistribution: 'التوزيع الحالي',
      ManufacturingViewReport: 'عرض التقرير',
      ManufacturingViewDetails: 'عرض التفاصيل',
      ManufacturingViewAll: 'عرض الكل',
      ManufacturingRecentMaterialRequests: 'طلبات المواد الأخيرة',
      ManufacturingLastFiveRequests: 'آخر خمسة طلبات',
      ManufacturingRequestID: 'رقم الطلب',
      ManufacturingMaterial: 'المادة',
      ManufacturingUnit: 'الوحدة',
      ManufacturingRequestDate: 'تاريخ الطلب',
      ManufacturingRequiredDate: 'تاريخ الاحتياج',

      // Real Estate Dashboard
      RealEstateDashboard: 'لوحة تحكم العقارات',
      RealEstateModule: 'العقارات',
      RealEstateHome: 'الرئيسية',
      RealEstateTotalProperties: 'إجمالي العقارات',
      RealEstateRentedProperties: 'العقارات المؤجرة',
      RealEstateAvailableProperties: 'العقارات المتاحة',
      RealEstateUnderMaintenanceProperties: 'تحت الصيانة',
      RealEstateTotalContracts: 'إجمالي العقود',
      RealEstateActiveContracts: 'العقود النشطة',
      RealEstateExpiredContracts: 'العقود المنتهية',
      RealEstateExpiringContracts: 'تنتهي قريباً',
      RealEstateRevenueByMonth: 'الإيرادات الشهرية',
      RealEstateLastSixMonths: 'الستة أشهر الأخيرة',
      RealEstateQuickActions: 'الإجراءات السريعة',
      RealEstateCommonOperations: 'العمليات الشائعة',
      RealEstateRecentContracts: 'العقود الأخيرة',
      RealEstateLastFiveContracts: 'آخر خمسة عقود',
      RealEstateContractID: 'رقم العقد',
      RealEstateProperty: 'العقار',
      RealEstateTenant: 'المستأجر',
      RealEstateStartDate: 'تاريخ البدء',
      RealEstateEndDate: 'تاريخ الانتهاء',
      RealEstateValue: 'القيمة',
      RealEstateStatus: 'الحالة',
      RealEstatePropertiesByType: 'العقارات حسب النوع',
      RealEstateCurrentDistribution: 'التوزيع الحالي',
      RealEstateViewReport: 'عرض التقرير',
      RealEstateViewDetails: 'عرض التفاصيل',
      RealEstateViewAll: 'عرض الكل',
      RealEstateRecentPayments: 'المدفوعات الأخيرة',
      RealEstateLastFivePayments: 'آخر خمسة مدفوعات',
      RealEstatePaymentID: 'رقم الدفعة',
      RealEstateDate: 'التاريخ',
      RealEstateAmount: 'المبلغ',
      RealEstateType: 'النوع',

      // Fleet Dashboard
      FleetDashboard: 'لوحة تحكم الأسطول',
      FleetModule: 'الأسطول',
      FleetHome: 'الرئيسية',
      FleetTotalVehicles: 'إجمالي المركبات',
      FleetActiveVehicles: 'المركبات النشطة',
      FleetInactiveVehicles: 'المركبات غير النشطة',
      FleetUnderMaintenanceVehicles: 'تحت الصيانة',
      FleetTotalDrivers: 'إجمالي السائقين',
      FleetActiveDrivers: 'السائقين النشطين',
      FleetOnLeaveDrivers: 'في إجازة',
      FleetAvailableDrivers: 'المتاحين',
      FleetFuelConsumption: 'استهلاك الوقود',
      FleetLastSixMonths: 'الستة أشهر الأخيرة',
      FleetQuickActions: 'الإجراءات السريعة',
      FleetCommonOperations: 'العمليات الشائعة',
      FleetRecentMovements: 'حركات المركبات الأخيرة',
      FleetLastFiveMovements: 'آخر خمس حركات',
      FleetMovementID: 'رقم الحركة',
      FleetVehicle: 'المركبة',
      FleetDriver: 'السائق',
      FleetStartDate: 'تاريخ البدء',
      FleetEndDate: 'تاريخ الانتهاء',
      FleetDestination: 'الوجهة',
      FleetStatus: 'الحالة',
      FleetVehiclesByType: 'المركبات حسب النوع',
      FleetCurrentDistribution: 'التوزيع الحالي',
      FleetViewReport: 'عرض التقرير',
      FleetViewDetails: 'عرض التفاصيل',
      FleetViewAll: 'عرض الكل',
      FleetRecentInvoices: 'الفواتير الأخيرة',
      FleetLastFiveInvoices: 'آخر خمس فواتير',
      FleetInvoiceID: 'رقم الفاتورة',
      FleetDate: 'التاريخ',
      FleetAmount: 'المبلغ',
      FleetType: 'النوع',

      // Staff Assistant Dashboard
      StaffAssistantDashboard: 'لوحة تحكم مساعد الموظفين',
      StaffAssistantModule: 'مساعد الموظفين',
      StaffAssistantHome: 'الرئيسية',
      StaffAssistantTotalAttendanceDays: 'إجمالي أيام الحضور',
      StaffAssistantPresentDays: 'أيام الحضور',
      StaffAssistantLateDays: 'أيام التأخير',
      StaffAssistantAbsentDays: 'أيام الغياب',
      StaffAssistantTotalRequests: 'إجمالي الطلبات',
      StaffAssistantApprovedRequests: 'الطلبات الموافق عليها',
      StaffAssistantPendingRequests: 'الطلبات قيد الانتظار',
      StaffAssistantRejectedRequests: 'الطلبات المرفوضة',
      StaffAssistantAttendanceByMonth: 'الحضور الشهري',
      StaffAssistantLastSixMonths: 'الستة أشهر الأخيرة',
      StaffAssistantQuickActions: 'الإجراءات السريعة',
      StaffAssistantCommonOperations: 'العمليات الشائعة',
      StaffAssistantRecentRequests: 'الطلبات الأخيرة',
      StaffAssistantLastFiveRequests: 'آخر خمسة طلبات',
      StaffAssistantRequestID: 'رقم الطلب',
      StaffAssistantRequestType: 'نوع الطلب',
      StaffAssistantStartDate: 'تاريخ البدء',
      StaffAssistantEndDate: 'تاريخ الانتهاء',
      StaffAssistantStatus: 'الحالة',
      StaffAssistantRequestsByType: 'الطلبات حسب النوع',
      StaffAssistantCurrentDistribution: 'التوزيع الحالي',
      StaffAssistantViewReport: 'عرض التقرير',
      StaffAssistantViewDetails: 'عرض التفاصيل',
      StaffAssistantViewAll: 'عرض الكل',
      StaffAssistantRecentAttendance: 'حركات الحضور الأخيرة',
      StaffAssistantLastFiveAttendance: 'آخر خمسة أيام',
      StaffAssistantDate: 'التاريخ',
      StaffAssistantCheckIn: 'وقت الحضور',
      StaffAssistantCheckOut: 'وقت الانصراف',
      StaffAssistantCheckin: 'تسجيل الحضور',
      StaffAssistantCheckOut2: 'تسجيل الانصراف',
      StaffAssistantBreakout: 'بداية الاستراحة',
      StaffAssistantBreakin: 'نهاية الاستراحة',

      // Hotel Dashboard
      HotelDashboard: 'لوحة تحكم الفنادق',
      HotelModule: 'الفنادق',
      HotelHome: 'الرئيسية',
      HotelTotalRooms: 'إجمالي الغرف',
      HotelOccupiedRooms: 'الغرف المشغولة',
      HotelAvailableRooms: 'الغرف المتاحة',
      HotelUnderMaintenanceRooms: 'تحت الصيانة',
      HotelTotalGuests: 'إجمالي الضيوف',
      HotelCheckedInGuests: 'الضيوف المسجلين',
      HotelCheckingOutToday: 'المغادرون اليوم',
      HotelReservationsToday: 'الحجوزات اليوم',
      HotelOccupancyRate: 'معدل الإشغال',
      HotelLastSixMonths: 'الستة أشهر الأخيرة',
      HotelQuickActions: 'الإجراءات السريعة',
      HotelCommonOperations: 'العمليات الشائعة',
      HotelUpcomingReservations: 'الحجوزات القادمة',
      HotelNextFiveReservations: 'الخمس حجوزات القادمة',
      HotelReservationID: 'رقم الحجز',
      HotelGuest: 'الضيف',
      HotelRoom: 'الغرفة',
      HotelCheckIn: 'تسجيل الدخول',
      HotelCheckOut: 'تسجيل الخروج',
      HotelStatus: 'الحالة',
      HotelRevenueByService: 'الإيرادات حسب الخدمة',
      HotelCurrentDistribution: 'التوزيع الحالي',
      HotelViewReport: 'عرض التقرير',
      HotelViewDetails: 'عرض التفاصيل',
      HotelViewAll: 'عرض الكل',
      HotelRecentInvoices: 'الفواتير الأخيرة',
      HotelLastFiveInvoices: 'آخر خمس فواتير',
      HotelInvoiceID: 'رقم الفاتورة',
      HotelDate: 'التاريخ',
      HotelAmount: 'المبلغ',
      HotelType: 'النوع',

      // General Dashboard
      GeneralDashboard: 'لوحة تحكم الإعدادات العامة',
      GeneralModule: 'الإعدادات العامة',
      GeneralHome: 'الرئيسية',
      GeneralTotalCompanies: 'إجمالي الشركات',
      GeneralActiveBranches: 'الفروع النشطة',
      GeneralTotalCurrencies: 'إجمالي العملات',
      GeneralTotalCountries: 'إجمالي الدول',
      GeneralTotalUsers: 'إجمالي المستخدمين',
      GeneralActiveUsers: 'المستخدمين النشطين',
      GeneralAdminUsers: 'المستخدمين الإداريين',
      GeneralSystemInfo: 'معلومات النظام',
      GeneralSystemDetails: 'تفاصيل النظام',
      GeneralSystemVersion: 'إصدار النظام',
      GeneralLastUpdate: 'آخر تحديث',
      GeneralDatabaseSize: 'حجم قاعدة البيانات',
      GeneralUserActivity: 'نشاط المستخدمين',
      GeneralLastSixMonths: 'الستة أشهر الأخيرة',
      GeneralQuickActions: 'الإجراءات السريعة',
      GeneralCommonOperations: 'العمليات الشائعة',
      GeneralRecentLogins: 'آخر تسجيلات الدخول',
      GeneralLastFiveLogins: 'آخر خمس تسجيلات دخول',
      GeneralID: 'الرقم',
      GeneralUsername: 'اسم المستخدم',
      GeneralName: 'الاسم',
      GeneralDate: 'التاريخ',
      GeneralIP: 'عنوان IP',
      GeneralStatus: 'الحالة',
      GeneralViewDetails: 'عرض التفاصيل',
      GeneralViewAll: 'عرض الكل',
      // Project Dashboard
      ProjectOverview: 'نظرة عامة على المشاريع',
      ProjectStats: 'إحصائيات المشاريع',
      ActiveProjects: 'المشاريع النشطة',
      CompletedProjects: 'المشاريع المكتملة',
      ProjectProgressStatus: 'تقدم المشاريع',
      ProjectBudget: 'ميزانية المشاريع',
      TeamPerformance: 'أداء الفريق',
      UpcomingDeadlines: 'المواعيد النهائية القادمة',
      ProjectRisks: 'مخاطر المشاريع',
      // Maintenance Dashboard
      MaintenanceOverview: 'نظرة عامة على الصيانة',
      MaintenanceStats: 'إحصائيات الصيانة',
      PendingMaintenance: 'الصيانة المعلقة',
      CompletedMaintenance: 'الصيانة المكتملة',
      MaintenanceCosts: 'تكاليف الصيانة',
      EquipmentStatus: 'حالة المعدات',
      MaintenanceSchedule: 'جدول الصيانة',
      MaintenanceHistory: 'سجل الصيانة',
      // Manufacturing Dashboard
      ManufacturingOverview: 'نظرة عامة على التصنيع',
      ManufacturingStats: 'إحصائيات التصنيع',
      ProductionOutput: 'إنتاجية المصنع',
      ProductionEfficiency: 'كفاءة الإنتاج',
      QualityMetrics: 'مقاييس الجودة',
      InventoryLevels: 'مستويات المخزون',
      MachineryStatus: 'حالة الآلات',
      ProductionSchedule: 'جدول الإنتاج',
      // Real Estate Dashboard
      RealEstateOverview: 'نظرة عامة على العقارات',
      RealEstateStats: 'إحصائيات العقارات',
      TotalProperties: 'إجمالي العقارات',
      OccupiedProperties: 'العقارات المشغولة',
      VacantProperties: 'العقارات الشاغرة',
      RentalIncome: 'دخل الإيجار',
      PropertyMaintenance: 'صيانة العقارات',
      LeaseExpirations: 'انتهاء عقود الإيجار',
      PropertyValuation: 'تقييم العقارات',
      // Fleet Dashboard
      FleetOverview: 'نظرة عامة على الأسطول',
      FleetStats: 'إحصائيات الأسطول',
      TotalVehicles: 'إجمالي المركبات',
      ActiveVehicles: 'المركبات النشطة',
      FleetMaintenanceStatus: 'حالة الصيانة',
      FuelConsumption: 'استهلاك الوقود',
      VehicleUtilization: 'استخدام المركبات',
      DriverPerformance: 'أداء السائقين',
      FleetCosts: 'تكاليف الأسطول',
      // Staff Assistant Dashboard
      StaffAssistantOverview: 'نظرة عامة على مساعد الموظفين',
      StaffAssistantStats: 'إحصائيات مساعد الموظفين',
      PendingRequests: 'الطلبات المعلقة',
      CompletedRequests: 'الطلبات المكتملة',
      RequestCategories: 'فئات الطلبات',
      ResponseTime: 'وقت الاستجابة',
      StaffSatisfaction: 'رضا الموظفين',
      CommonRequests: 'الطلبات الشائعة',
      // POS Dashboard
      POSOverview: 'نظرة عامة على نقاط البيع',
      POSStats: 'إحصائيات نقاط البيع',
      DailySales: 'المبيعات اليومية',
      TopSellingItems: 'العناصر الأكثر مبيعًا',
      SalesByTerminal: 'المبيعات حسب المحطة',
      PaymentMethods: 'طرق الدفع',
      CashierPerformance: 'أداء أمناء الصندوق',
      PeakHours: 'ساعات الذروة',
      POSTerminal: 'محطة نقاط البيع',
      POSManagement: 'إدارة نقاط البيع',
      SalesProcessing: 'معالجة المبيعات',
      ProcessSales: 'معالجة المبيعات',
      OpenTerminal: 'فتح المحطة',
      POSReports: 'تقارير نقاط البيع',
      SalesAnalytics: 'تحليلات المبيعات',
      ViewReports: 'عرض التقارير',
      POSSettings: 'إعدادات نقاط البيع',
      ConfigureSystem: 'تكوين النظام',
      SystemConfiguration: 'إعدادات النظام',
      Configure: 'تكوين',
      POSInventory: 'مخزون نقاط البيع',
      StockManagement: 'إدارة المخزون',
      ManageStock: 'إدارة المخزون',
      ManageInventory: 'إدارة المخزون',
      POSTerminalPlaceholder: 'مكان محطة نقاط البيع',
      DesignWillBeAddedLater: 'سيتم إضافة التصميم لاحقًا',
      AlternativeAccess: 'وصول بديل',
      // إحصائيات نقاط البيع
      AverageSales: 'متوسط المبيعات',
      TotalItems: 'إجمالي العناصر',
      AverageTicket: 'متوسط التذكرة',
      CustomerReturn: 'مرتجعات العملاء',
      POSStatistics: 'الإحصائيات',
      POSDaily: 'يومي',
      POSWeekly: 'أسبوعي',
      POSMonthly: 'شهري',
      POSToday: 'اليوم',
      POSYesterday: 'أمس',
      POSThisWeek: 'هذا الأسبوع',
      POSCurrentMonth: 'الشهر الحالي',
      // Hotel Dashboard
      HotelOverview: 'نظرة عامة على الفندق',
      HotelStats: 'إحصائيات الفندق',
      Occupancy: 'معدل الإشغال',
      AverageRoomRate: 'متوسط سعر الغرفة',
      RevPAR: 'الإيراد لكل غرفة متاحة',
      Reservations: 'الحجوزات',
      GuestSatisfaction: 'رضا النزلاء',
      DepartmentPerformance: 'أداء الأقسام',
      ForecastOccupancy: 'توقعات الإشغال',
      // Admin Dashboard
      AdminOverview: 'نظرة عامة على الإدارة',
      AdminStats: 'إحصائيات الإدارة',
      SystemUsers: 'مستخدمي النظام',
      UserRoles: 'أدوار المستخدمين',
      SystemLogs: 'سجلات النظام',
      BackupStatus: 'حالة النسخ الاحتياطي',
      SystemResources: 'موارد النظام',
      SecurityAlerts: 'تنبيهات الأمان',
      SystemUpdates: 'تحديثات النظام',
      CommonOperations: 'العمليات الشائعة',
      ViewDetails: 'عرض التفاصيل',
      SystemModules: 'وحدات النظام',
      ModulePerformance: 'أداء الوحدات',
      TopActiveUsers: 'أكثر المستخدمين نشاطًا',
      UserActivity: 'نشاط المستخدمين',
      CONTRYNAMEEN: 'الاسم',
      COUNTRYNAME: 'الاسم عربي',
      CODEID: 'الرمز',
      SendSMS: 'ارسال رسالة نصيه',
      SMSInstance: 'مزود الرسائل النصية',
      ServiceProvider: 'مزود الخدمه',
      Default: 'رئيسى',
      UserName: 'اسم المستخدم',
      Password: 'كلمة المرور',
      Reset: 'إعادة ضبط',
      SenderName: 'المرسل',
      AccountType: 'نوع الحساب',
      Company: 'الشركة',
      Country: 'الدولة',
      CostType: 'نوع التكلفة',
      DriverName: 'اسم السائق',
      DefulatNumbers: 'المستلم الافتراضى',
      SaveChanges: 'حفظ',
      Create: 'جديد',
      Edit: 'تعديل',
      Delete: 'حذف',
      Actions: 'اجراء',
      id: 'الكود',
      Name: 'الاسم',
      DeductedFromEmployee: 'اجمالي المخصوم من الموظف',
      SalaryAdditions: 'اضافات على الراتب',
      SalaryDeduction: 'الخصومات والاستقاطعات',
      Template: 'القالب',
      SMSTemplate: 'قوالب الرسائل',
      TemplateName: 'اسم القالب',
      ReceiptTransactions: 'استلام المعقب معاملات',
      About: 'عن',
      Procedure: 'الاجراء',
      Commentator: 'المعقب',
      RecipientType: 'نوع المستلم',
      ClassificationCertificate: 'شهادة التصنيف',
      BenefitDeductionApplication: 'طلب استحقاق او استقطاع',
      Balance: 'الرصيد',
      Previous: 'ألاجازات السابقة',
      Limit: 'الحد المسموح به',
      Vacation: 'الاجازة',
      LatePermissions: 'الاذونات',
      RecipientNumber: 'رقم المستلم',
      Send: 'ارسال',
      WorkFrom: 'الجهة',
      HijriDate: 'التاريخ الهجري',
      IssuingAuthority: 'جهة الاصدار',
      LicencesDate: 'تاريخ الرخصة',
      LaveDate2: 'تاريخ الحركة',
      ResidenceText: 'نص الاقامة',

      InsurancesDueReports: 'مستحقات التأمينات',
      Nationality: 'الجنسيه',
      IdIqama: 'ويحمل اقامة ',
      EmployeeFixed: 'حصة الموظف ثابت',
      EmployeeChanged: 'حصة الموظف متغير',
      CompanyFixed: 'حصة الشركة ثابت',
      CompanyChanged: 'حصة الشركة متغير',
      HousingAllowance: 'بدل السكن',
      OtherAllowance: 'بدلات اخري',
      NameAr: 'الاسم العربي',
      AccName: 'اسم الحساب',
      NameEn: 'الاسم الانجليزى',
      MovementName: 'اسم الحركة',
      DeductionDays: 'ايام الجزاء',
      AttendanceDays: 'أيام الحضور',
      Incoming: 'الوارد',
      Outgoing: 'المنصرف',

      Descriptions: 'الوصف',
      JobResponsibilities: 'واجبات الوظيفة',
      JobConditions: 'شروط الوظيفة',
      Type: 'النوع',
      Phone: 'الهاتف',
      Mobile: 'رقم الجوال',
      EmailAddress: 'البريد الالكتروني',
      From: 'من',
      FromPlace: 'من مكان',
      ToPlace: 'الي مكان',
      FromCostCenter: 'من مركز تكلفة',
      ToCostCenter: 'الي مركز تكلفة',
      To: 'الى',
      Year: 'سنة',
      IP: 'IP',
      Port: 'Port',
      Connect: 'اتصال',
      Currency: 'العمله',
      SaveAction: 'حفظ الحركات المعروفة',
      MainCustomer: 'العميل الرئيسى',
      CustomersType: 'تصنيف العميل',
      DisplayActionFromMachine: 'عرض الحركات من الماكينة',
      DownloadActionFromFile: 'تحميل الحركات من ملف',
      FilePath: 'مسار الملف',
      DeleteActionFromMachine: 'حذف الحركات من الماكينة',
      CustomerLabel: 'العميل',
      LinkedSupplier: 'المورد المرتبط',
      MovementType: 'نوع الحركه',
      Movement: 'الحركة',
      Detaild: 'تفصيلي',
      Address: 'العنوان',
      View: 'عرض',
      CompanySetting: 'اعدادات الشركه',
      FromManage: 'من ادارة',
      CostValue: 'قيمة التكلفة',

      ToManage: 'الي ادارة',
      Management: 'الادارة',
      FromSector: 'من قطاع',
      ToSector: 'الي قطاع',
      Mail: 'البريد',
      Period: 'المدة',
      Attendance: 'الحضور',
      Assignment: 'أمر تكليف',
      Commissioning: ' التكليف',
      DurationInDays: 'المدة بالايام',
      RatingPercentage: 'نسبة التقييم ',
      Assessment: 'التقييم',
      EvaluationElement: 'عنصر التقييم ',

      CommissioningDepartment: 'ادارة التكليف',
      AttendanceReport: ' الحضور والانصراف',
      AddAssessmentItems: 'اضافة عناصر التقييم ',
      TotalEmp: 'حصة الموظف',
      DrivingLicencesReports: 'تقارير رخص القيادة',
      LicencesNum: 'رقم الرخصة',
      OwnerValue: 'حصة صاحب العمل',
      Owner: 'صاحب العمل',
      FromHour: 'من الساعه',
      ToHour: 'الي الساعه',

      BreakOut: 'البريك ',
      BreakIn: ' العودة',
      Leave: 'الانصراف',
      NewContract: 'عقد جديد',
      UploadAttendance: 'سحب الحركات',
      field_1: 'حقل رقم 1',
      field_2: 'حقل رقم 2',
      field_3: 'حقل رقم 3',
      field_4: 'حقل رقم 4',
      field_5: 'حقل رقم 5',
      Print: 'طباعه',
      Cancel: 'الغاء',
      Refresh: 'تحديث',
      BirthDate: 'تاريخ الميلاد',
      DontShowBreviousBalance: 'عدم عرض رصيد ماقبله',
      CustomerId: 'كود العميل',
      ActionName: 'الاجراء',
      JobInTheCompany: 'الوظيفة في الشركة',
      CareerInIdentity: 'المهنة في الهوية',
      CareerInVisa: 'المهنة في التأشيرة',
      ActionNo: 'رقم الاجراء',
      Date: 'التاريخ',
      Debit: 'مدين',
      HRForms: 'النماذج الجاهزه',
      Form: 'النموذج',
      EmployeeId: 'كود الموظف',
      Brief: 'مختصر',
      PrintData: 'بيانات الطباعة',
      Credit: 'دائن',
      empState: 'حالة الموظف',
      Notes: 'ملاحظات',
      Sex: 'الجنس',
      EffectEmployeeStatus: 'التأثير علي حالة الموظف',
      Depart: 'القسم',
      Account: 'الحساب',
      ExportToExcel: 'تصدير الي اكسيل',
      ImportFromExcel: 'استيراد من اكسيل',
      SendViaSMS: 'رسالة نصية',
      SendViaEmail: 'ارسال بريد',
      SendViaWhatsapp: 'ارسال واتس',
      Branch: 'الفرع',
      DocNo: 'رقم السند',
      Acccode2: 'كود الحساب المرتبط',
      AccName2: 'الحساب المرتبط',
      BankCode: 'كود البنك',
      Code: 'الكود',
      Day: 'يوم',
      Month: 'شهر',
      JournalNO: 'رقم القيد',
      NewJournalForAudit: 'قيد للمراجعه',
      SupplierType: 'تصنيف المورد',
      LinkedCustomer: 'العميل المرتبط',
      RequiredNumber: 'العدد المطلوب',
      Course: 'الدورة التدريبية',
      SupplierLabel: 'المورد',
      Invcomno: 'رقم فاتورة المورد',
      InvoiceNo: 'رقم الفاتورة',
      SupplierId: 'كود المورد',
      ModelNo: 'رقم الموديل',
      AssetName: 'أسم الأصل ',
      AssetValue: 'قيمه الأصل ',
      NetAssetValue: 'صافى قيمه الأصل  ',
      AssetCategory: 'تصنيف الأصل',
      NewAssetsList: 'أضافه أصل ',
      AssetsList: ' الأصول ',
      DepreciationList: 'بيان أهلاك الأصول',
      Assets: 'الأصول',
      AssetDisposal: 'أستعباد أصل',
      DisposalType: 'نوع الأستبعاد ',
      LastDepreciationDatefor: 'تاريخ آخر إهلاك لـ',
      DisposalDate: 'تاريخ الأستبعاد',
      AssetsStatement: 'بيان الأصول',
      AssetsDepreciation: 'أهلاك الأصول',
      BondNumber: 'رقم السند',
      AdvanceBalance: 'رصيد السلف',
      PeriodicDiscountValue: 'قيمة الخصم الدوري',
      DepreciationRate: 'معدل الإهلاك',
      DepreciationMethod: 'طريقة الإهلاك',
      SalvageValue: 'قيمة الخردة',
      EntityCode: 'كود الجهة',
      EntityName: 'اسم  الجهة',
      CaseName: 'نوع الحركه',
      CaseId: 'كود الحركه',
      Costid: 'ك مركز التكلفة',
      CostName: 'مركز التكلفة',
      VatNo: 'الرقم الضريبي',
      Serial: 'مسلسل',
      SerialNo: 'السريال',
      financial_entity_Type: 'نوع الكيان',
      financial_entity_Id: 'كود الكيان',
      financial_entity: 'الكيان المالي',
      TraineesNumber: 'عدد المتدربين',
      TrainingSubject: 'موضوع التدريب',
      AboutYear: 'عن العام ',
      SuggestedDuration: 'المده المقترحة',
      DeductionType: 'نوع الخصم',
      BasicData: 'البيانات الاساسية',
      TaxInformation: 'البيانات الضريبية',
      LicensesInformation: 'بيانات التراخيص',
      HomePhone:"الهاتف المنزلي",
      personalEmail:"لبريد الإلكتروني الشخصي",
      Procedures: 'الاجراءات',
      Filter: 'عرض',
      GeneralManager: 'مدير عام الشركة',
      GraduationYearDate: 'تاريخ التخرج',
      GraduationYear: 'سنة التخرج',
      Grade: 'التقدير ',
      GraduationDestination: 'جهة التخرج',
      JobPreviously: 'الوظيفه سابقا',
      ExperienceYears: 'سنوات الخبرة',
      NatureOfWorkAllowance: 'بدل طبيعة العمل',
      LaborOfficeNumber: 'رقم مكتب العمل',
      DeliveryRepresentative: 'مندوب تسليم',
      ReferenceNumber: ' رقم المرجع',
      PurchaseDate: 'تاريخ الشراء',
      CommissioningDate: 'تاريخ التشغيل',
      LineManager: 'المدير المباشر',
      Allowances: 'البدلات',
      Contracts: 'العقود',
      ContractPeriod: 'فترة العقد',
      ThirtyDaysVacationSystem: 'نظام أجازات ثلاثون يوم',
      TotalSalary: 'احتساب البدلات مع الراتب',
      CarryoverLeaveBalance: 'رصيد أجازات مرحل',
      AnnualTicketsValue: 'قيمة التذاكر السنويه',
      AnnualTicketsNumbers: 'عدد التذاكر السنويه',
      WorkersFundDiscountRatio: 'نسبة خصم صندوق عاملين',
      SalaryInInsurance: 'الراتب في التأمينات',
      VirtualCostCenter: 'مركز التكلفه الافتراضي',
      Employees: 'الموظفين',
      Employee: 'الموظفين',
      EmployeeName: 'اسم الموظف',
      NameInPrint: 'الاسم في الطباعة',
      MonthlyDeductionsReports: ' الاستقطاعات الشهرية',
      Update: 'تحديث',
      Department: 'القسم',
      Value: 'المبلغ',
      LastWorkDay: 'اخر يوم عمل',
      Number: 'العدد',
      DeductionFromSalary: 'الجزاء',
      Incentive: 'الاضافه',
      Job: 'الوظيفة',
      Penalty: 'جزاء',
      Medical: 'تأمين طبي',
      SalaryPayment: 'دفعة',
      PayableTo: 'يصرف لأمر',
      ReceivedFrom: 'استلمنا من',
      DocumentaryCredits: 'الاعتمادات المستندية',
      Beneficiary: 'المستفيد',
      Regarding: 'وذالك عن',
      FileLocation: 'موقع الملف',
      NewCashPayments: 'مدفوعات نقدية',
      NewBankPayments: 'مدفوعات بنكيه',
      LettersOfGuaranteeTypes: 'أنواع خطابات الضمان',
      NewLettersOfGuaranteeTypes: ' خطاب ضمان',
      NewLettersOfGuaranteeLimitations: 'حدود خطاب الضمان',
      CashDepositPercentage: 'نسبة التأمين النقدي',
      CashDeposit: 'التأمين النقدي',
      GuaranteeLetterType: 'نوع خطاب الضمان',
      LettersOfGuarantee: 'خطابات الضمان',
      NewLettersOfGuarantee: '  خطابات الضمان',
      FacilityAmount: 'قيمة التسهيلات',
      LettersOfGuaranteeLimitations: 'حدود خطابات الضمان',
      Transfer: 'حوالة',
      Check: 'شيك',
      TransactionNumber: 'رقم التحويل',
      Sickness: 'مرضي',
      Guarantor: 'الضامن',
      Services: 'خدمة',
      ProductLabel: 'صنف',
      TheProduct: 'الصنف',
      Quantity: 'الكمية',
      All: 'الكل',
      InvoiceDate: 'تاريخ الفاتورة',
      Purpose: 'الغرض',
      ShipmentDate: 'تاريخ الشحن ',
      ShippingValue: 'قيمة الشحن',
      ShippingPoliceNumber: 'رقم بوليسية الشحن',
      LeaveTime: 'وقت الخروج',
      BackTime: 'وقت العودة',
      AutoCounterAtExit: 'عداد السيارات عند الخروج',
      CarCounterOnReturn: 'عداد السيارات عند العودة',
      Destination: 'الوجهة',
      Invoiced: 'مفوترة',
      NotInvoiced: 'غير مفوترة',
      BorderNumber: 'رقم الحدود',
      Reserved: 'محجوزه',
      ProductCategories: 'المجموعة الرئيسية',
      ProductSubCategories: 'المجموعة الفرعية',
      Summary: 'المجموع',
      Indirectcost: 'التكلفة الغير مباشرة',
      Loans: 'القروض',
      Custody: 'عهده',
      Warehouse: 'المخزن',
      FromWarehouse: 'من مخزن',
      ToWarehouse: 'الي مخزن',
      Checkin: 'حضور',
      CheckOut: 'انصراف',
      ProjectLocation: 'موقع المشروع',
      CurrentLocation: 'موقعك الحالي',
      Accuracy: 'الدقة',
      difference: 'الفرق',
      breakout: 'بريك',
      breakin: 'عوده',
      Cut_Type: 'الاستقطاع',
      Add_Type: 'الاضافة',
      HiringDate: 'تاريخ التعيين',
      FileName: 'الوثيقة',
      VisaNumber: 'رقم التأشيرة',
      FromDate: 'من تاريخ',
      ToDate: 'الي تاريخ',
      OverTime: 'الوقت الاضافى',
      EXPDate: 'تاريخ الانتهاء',
      HijriEXPDate: 'تاريخ الانتهاء هجري',
      CommercialRegistrationNumber: 'رقم السجل التجاري',
      AbsencePermissions: 'غياب باذن',
      Others: 'أخري',
      Define: 'حدد',
      Driver: 'السائق',
      DocumentType: 'نوع الوثيقه',
      SubordinateName: 'اسم التابع',
      Issuer: 'جهة الاصدار',
      Dependents: 'بيانات التابعين للموظف',
      Relationship: 'صلة القرابة',
      InsuranceCategory: 'فئة التأمين',
      Warning: 'انذار',
      InterruptionDate: 'تاريخ الانقطاع',
      InterruptionDaysNumber: 'عدد ايام الانقطاع',
      Connected: 'متصلة',
      Separated: 'منفصلة',
      Resignation: 'استقالة',
      SalesTaxRegistrationNumber: 'رقم التسجيل في ضريبة المبيعات ',
      TaxCardNumber: 'رقم البطاقة الضريبية',
      ReasonForResignation: 'سبب الاستقالة',
      DownloadSalaryOflastMonth: 'تحميل راتب أخر شهر',
      Certificate: 'شهادة خبرة',
      StatusAndSignature: 'الحالة والتوقيع',
      DirectorSignature: 'توقيع المدير',
      IssueDate: 'تاريخ الاصدار',
      MaritalStatus: ' الحالة الاجتماعيه',
      AddEmployee: 'اضافة موظف',
      EmployeeNameEn: ' اسم بالانجليزي',
      EmployeeNameAr: 'اسم الموظف ',
      DriverNo: 'رقم السائق',
      InsuranceSalary: 'الراتب التأميني',
      CurrentSalary: 'الراتب الحالي',
      SuggestedSalary: 'الراتب المقترح',
      SecondmentContract: 'عقد اعارة',
      FirstParty: 'الطرف الاول',
      SecondParty: 'الطرف الثاني',
      CommercialRegistration: 'السجل التجاري',
      IssuedBy: 'صادر من',
      RepresentedBy: 'ويمثلها',
      Salary: 'الراتب',
      WorkingHoursAndPenalties: 'أوقات العمل ولائحة الجزاءات',
      Project: 'المشروع',
      PassportNumber: 'رقم الجواز',
      JobNumber: 'الرقم الوظيفي',
      WorkInformation: 'بيانات العمل',
      ShiftPlans: 'خطة الورديات',
      IssuingResidence: 'اصدار اقامة',
      TransferringSponsorship: 'نقل كفالة',
      SaveShiftPlan: 'حفظ خطة الورديات',
      FacilityName: 'اسم المنشأة',
      InsurancePosition: 'الموقف التأميني',
      JobPosition: 'الموقف الوظيفي',
      FacilityNumber: ' رقم المنشأة',
      BasicSalary: 'الراتب الاساسي',
      DaysAndHours: 'عدد ايام وساعات العمل',
      EditShiftPlans: 'تعديل خطة الورديات',
      ChangedSalary: ' الراتب المتغير',
      OtherAllowances: 'البدلات الاخري',
      FixedAllowance: 'بدل ثابت',
      DateOfEntry: 'تاريخ دخول البلد',
      OfficialDocuments: 'المستندات الرسمية',
      LocationAllowance: 'بدل موقع',
      SchoolAllowance: 'بدل مدرارس',
      TransportationAllowance: 'بدل انتقال',
      AllowancesAffectedByAbsence: 'البدلات المتأثرة بالغياب',
      AllowancesAffectingVacationExtract: 'بدلات تؤثر في مستخلص الاجازة',
      EndOfServiceAllowances: 'بدلات تؤثر في نهاية الخدمة',
      ServiceLength: 'مدة الخدمة',
      MealAllowance: 'بدل طعام',
      TelephoneAllowance: 'بدل تليفون',
      SalaryInformation: 'مفردات الراتب',
      AttachmentsAndDocuments: 'مرفقات ومستندات',
      DueDays: 'الايام المستحقة',
      Delivered: 'مسلم',
      Responsible: 'المسئول',
      Excellent: 'ممتاز',
      VeryGood: 'جيد جدا',
      Good: 'جيد ',
      Acceptable: 'مقبول',
      Weak: 'ضعيف',
      VisasNumber: 'عدد التأشيرات ',
      VisasGranted: 'التأشيرات الممنوحة',
      AuthorizationsIssued: 'التفاويض الصادرة',
      AvailableAuthorizations: 'التفاويض المتاحة',
      HoldsVisa: 'حاصل علي تأشير ولم يدخل المملكة',
      NotHoldsVisa: 'لم يحصل علي تأشيرة',
      Office: 'المكتب',
      MainVisaNumber: 'رقم التأشيرة الرئيسية',
      ContractNumber: 'رقم العقد',
      OrderDate: 'تاريخ الطلب',
      MaintainWorkingHours: 'المحافظة علي مواعيد العمل',
      AmountOfWorkDone: ' كمية العمل المنجز مقارنة مع متطلبات الوظيفة',
      ProfessionalCapacityDevelopment: 'الحرص علي تطوير القدرات المهنية',
      PlanningAbility: ' القدرة علي التخطيط وتحديد الأهداف والعمل علي تحقيقها ',
      FollowSecurityRules: 'الوعي واتباع قواعد الامن والسلامه ',
      SettingStandardsToMeasurePerformance:
        'القدرة علي وضع معايير لقياس الأداء وتصحيح الانحرافات',
      TakingResponsibility: 'تحمل المسئوليه والمحافظه علي سرية المعلومات ',
      ComplianceWithSystemAndRegulations:
        'الالتزام بالنظام واللوائح المعمول بها في الشركة  ',
      SubordinateCommand: 'القدرة علي قيادة المرؤوسية لتحقيق مستهدفات العمل',
      IncreaseInformation: 'السعي لزيادة المعلومات وتقديم المقترحات',
      NewEmployeeEvaluation: 'تقييم الموظفين الجدد',
      Boss: 'المدير',
      Count: 'عدد',
      OverallRating: 'التقدير العام',
      EmployeeEvaluation: 'التقييم الدورى للموظفين',
      Iqama: 'الإقامة',
      StopExportingBank: 'ايقاف تصدير البنك',
      SuspensionOfVacations: 'ايقاف الاجازات',
      SuspensionOfResidenceRenewal: 'ايقاف تجديد الاقامة',
      SalarySuspension: 'ايقاف الراتب',
      DownPayment: 'دفعة مقدمة',
      PayForOrder: 'تدفع لأمر',
      ExitAndReturn: 'خروج وعودة ',
      DomesticTravel: 'سفر داخلي',
      StatusLabel: 'الحالة',
      MedicalInspectionLetter: 'خطاب عرض على لجنة طبية',
      BedDate: 'تاريخ ملازمة الفراش',
      Attachments: 'مرفقات',
      ShippingAndCorrespondence: 'الشحن والمراسلات',
      ShippingCompany: 'شركة الشحن',
      ShipmentNumber: 'رقم الشحنة',
      Addressee: 'المرسل اليه ',
      Location: 'العنوان',
      Rate: 'نسبة',
      ManualAttendance: 'الحركات اليدويه',
      TimeAttendance: 'وقت الحضور',
      MaximumDelayWithPermission: 'أقصي حد للتأخير باذن',
      MaximumDelayAllowed: 'أقصي حد للتأخير المسموح به ',
      DelayBypass: 'تجاوز التأخير',
      DepartureTime: 'وقت الانصراف',
      ReturnFromRest: 'العودة من الراحة',
      ExitRest: 'خروج الراحة',
      HourCalculatedBy: 'تحسب الساعة بـ',
      PenaltyForAbsenceWithPermission: 'جزاء الغياب باذن',
      PenaltyForAbsenceWithoutPermission: 'جزاء الغياب بدون اذن',
      PenaltyForDelayWithPermission: 'جزاء التأخير باذن',
      Minute: 'دقيقة',
      hour: 'ساعة',
      PenaltyDelayingHourWithoutPermission: 'جزاء تأخير الساعة بدون اذن',
      QuarterDayDelay: 'يحسب 1/4 يوم في حالة ',
      HalfDayDelay: 'يحسب 1/2 يوم في حالة ',
      ThreeQuartersDayDelay: 'يحسب 3/4 يوم في حالة ',
      OneDayDelay: 'يحسب يوم في حالة ',
      GreaterThan: '>',
      WorkSystem: 'نظام العمل ',
      FromSalary: 'من الراتب',
      TrainingForPeriod: 'تدريب لمدة',
      MaximumAddition: 'الحد الأقصى للإضافة',
      Missions: 'مأمورية',
      BuildingNumber: 'رقم المبني',
      City: 'المدينة',
      InsuranceSubscriptionNumber: 'رقم الاشتراك في التأمينات',
      RoomNumber: 'رقم الغرفة',
      CustodyReceipt: 'تسليم عهدة',
      ReasonForLeavingWork: 'سبب ترك العمل',
      TemporaryForPeriod: 'مؤقت لمدة',
      CustodyName: 'اسم العهدة',
      HealthInsurance: 'التامين الصحى',
      InitialKnowledgeForPeriod: 'تعرف مبدئي  لمدة',
      Time: 'الوقت',
      PreviousPermissions: 'الأذون السابقة',
      DelayPermission: 'تأخير باذن',
      DelayPermitted: 'تأخير مسموح به',
      RewardType: 'نوع الاستحقاق أو الاستقطاع',
      Reward: 'المكافأة',
      Itinerary: 'خط السير',
      WorkTasks: 'مهام العمل ',
      Nationalty: 'الجنسية',
      Sector: 'القطاع',
      Bisc_Salary: 'الراتب الاساسى',
      Allawonce: 'البدلات',
      Tickt: 'التذاكر',
      ContractDuration: 'مدة العقد',
      RequestType: 'نوع الاذن',
      OrderType: 'نوع الطلب',
      LaveDate: 'تاريخ ترك العمل',
      ReturnDateFromLastVacation: 'تاريخ العودة من أخر أجازة ',
      WorkDays: 'عدد الايام',
      WorkDayes: 'أيام الخدمه من تاريخ العودة',
      WorkingDaysNumber: 'عدد أيام العمل',
      Net: 'الصافى',
      OutCompany: 'خارج الشركة',
      InCompany: 'داخل الشركة',
      Reasons: 'الأسباب',
      AppointmentLetter: 'اشعار تعيين',
      AdministrativeDecision: 'قرار اداري',
      Attention: 'لفت نظر',
      LegalIssue: 'المساءلة',
      LegalIssues: 'مسالة قانونية',
      LaveDateH: 'تاريخ ترك العمل',
      PlayoffsReports: 'التصفيات',
      MainAcc: 'الحساب الرئيسي',
      FromAcc: 'من حساب',
      Shift: 'في الوردية',
      ToAcc: 'إلي حساب',
      DocumentsReports: ' تقارير الوثائق',
      CostCenter: 'مركز التكلفة',
      TravelsReports: 'تقارير السفريات',
      costCenterGroup: 'مجموعة مراكز التكلفة',
      ShowZeroBalance: 'عرض الأرصده الصفريه',
      Show: 'عرض',
      FromNo: 'من قيد',
      ToNo: 'الى قيد',
      MissingNumbers: 'الارقام المفقودة',
      UnbalancedEntries: 'القيود غير المتزنة',
      TransactionswithoutEntries: 'حركات بدون قيود',
      TransactionswithoutInventoryImpact: 'حركات بدون تأثير على المخازن',
      MergingTransactions: 'دمج الحركات',
      EntryType: 'اليومية',
      NotificationType: 'نوع الإشعار',
      ChequeBookNumber: 'رقم دفتر الشيكات',
      ChequeStatus: 'حالة الشيك',
      CurrentCheckAccount: 'حساب الشيك حاليًا ',
      PostSaveCheckAccount: 'حساب الشيك بعد الحفظ',
      CheckStatusPostDeletion: 'حالة الشيك بعد حذف الحركة',
      CheckNumber: 'رقم الشيك',
      FromSeries: 'من مسلسل',
      ToSeries: 'إلى مسلسل',
      AllDeposits: 'كل الايداعات ',
      ExtractDeposits: 'ايداعات المستخلصلات',
      Design: 'اسم التصميم',
      ListName: 'اسم القائمه',
      CostCenterCode: 'كود مركز التكلفة',
      SalaryGroup: 'مجموعة الراتب',
      JournalEntries: 'القيود اليومية',
      Salesperson: 'مندوب البيع',
      OfficialHolidayForAllEmployeesDuringPeriod:
        'تحديد فترة الاجازه الرسمية لكل الموظفين',
      OfficialHolidayForParticularCategory:
        'تحديد يوم الاجازه الرسمية لفئة معينة ',
      OfficialVacationPeriodForParticularCategory:
        'تحديد فترة الاجازه الرسمية لفئة معينة ',

      DailyVacation: 'العطلة اليومية',
      CancelDelayOnThisDay: 'الغاء التأخير في هذا اليوم',
      OfficialHolidayForSpecificCategoryDuringPeriodOfTime:
        'تحديد يوم الاجازة الرسمية لفئة معينة خلال فترة ',
      PeriodInDays: 'الفترة باألايام',
      OfficialHolidayForAllEmployees: 'تحديد يوم الاجازة الرسمية لكل الموظفين',
      SalarySystem: 'نظام الراتب',
      NumOfDaysAweek: 'عدد ايام الاسبوع',
      NumberOfDaysAMonth: 'عدد ايام الشهر',
      WorkingHours: 'عدد ساعات العمل اليومية',
      Unit: 'الوحدة',
      Barcode: 'الباركود',
      Price: 'السعر',
      DeductionsReports: 'الخصومات',
      jobname: 'الوظيفة',
      BonusesAndIncentives: 'المكافآت والحوافز',
      VacationsReport: 'الاجازات',
      Holidays: 'الاجازات ',
      Permissions: 'الأذونات',
      TimeIn: 'وقت الدخول',
      TimeOut: 'وقت الخروج',
      LateOutTime: 'تأخير غير مسموح به',
      Late: 'التأخير',
      EmpWorkTime: 'عدد ساعات العمل',
      AccCode: 'كود التكلفه',
      MissionsReports: 'المأموريات',
      ManagersReports: 'موظفى ومديرى الاقسام',
      Managers: 'المدراء',
      Manager: 'المدير',
      EmployeeLifeReports: ' حياة موظف',
      NationalityJobsReports: 'اعداد الوظائف لكل جنسية',
      MovementsDuringPeriodReports: 'التنقلات خلال فترة',
      ProjectFrom: 'المشروع المحول منه',
      ProjectTo: 'المشروع المحول اليه',
      HRResponsible: 'مسئول الموارد البشرية',
      RequestDate: 'تاريخ الطلب',
      HRSigned: 'اعتماد مدير الموارد البشرية',
      RecruitedDuringPeriodReports: 'المعينين خلال فترة',
      DueSalariesReports: 'رواتب مستحقة في شهر معين',
      LastComBakVacation: 'اخر عودة من الاجازة',
      Visa: 'التأشيرات',
      StaffTransferBetweenProjects: 'نقل الموظفين بين المشاريع',
      SalaryUpdate: 'تعديل راتب موظف',
      UpdateResidenceDate: 'تحديث تاريخ الاقامة',
      EmployeeRequests: 'طلبات الموظفين',
      HousingAllowancePayments: '  دفعات بدل السكن',
      AdvanceHousingAllowance: 'بدل سكن مقدم',
      SalariesAccount: 'حساب المرتبات',
      SalaryPackage: 'مجموع الرواتب',
      BankName: 'اسم البنك',
      EmployeeBankAccountNumber: 'رقم حساب الموظف بالبنك',
      HealthCareDiscountRate: 'نسبة خصم الرعايه الصحيه',
      UpdateYearsNumber: 'عدد أعوام التحديث',
      DecadeBeginning: 'بداية العقد ',
      AnnualInstallments: 'الأقساط السنوية',
      ApprovedValueYearly: 'القيمة المعتمدة سنويا ',
      ActualValue: 'القيمة الفعلية',

      Ending: 'تصفيه',
      AnnualDues: 'المستحقات السنوية',
      HandwrittenNote: 'مذكرة يدويه',
      StaffCosts: ' تكاليف الموظفين',
      ScheduleAdvances: 'جدولة السلف',
      SalariesPreparation: 'اعداد الرواتب',
      PrintSalaries: 'اعداد الرواتب',
      IncomeTAX: 'ضريبة الدخل',
      MonthlyIqamaCost: 'تكلفة الاقامة الشهرية',
      CarTraffic: '  حركة السيارات',
      Trucking: 'شحن البضائع',
      VisitorsRecord: 'سجل الزائرين',
      ResidenceNumber: 'رقم الاقامة',
      TenderItemNo: 'رقم البند',
      CardNumber: 'رقم البطاقة',
      EstablishmentNumberInInterior: 'رقم المنشأة في الداخلية ',
      ExpiryDateOfZakatAndIncomeCertificate: 'تاريخ انتهاء شهادة الزكاة والدخل',
      ZakatAndIncomeCertificateNumber: 'رقم شهادة الزكاة والدخل',
      ZakatAndIncomeCertificateUniqueNumber:
        'الرقم المميز لشهادة الزكاة والدخل',
      Fax: 'الفاكس',
      Street: 'الشارع',
      Mailbox: 'صندوق البريد',
      Zipcode: 'الرمز البريدي',
      TrainingNeeds: 'الاحتياجات التدريبية',
      AnnualTrainingPlan: 'خطة التدريب السنويه',
      EmployeeTrainingForm: 'نموذج تدريب موظف',
      EvaluateTrainingEffectiveness: ' تقييم فاعلية التدريب',
      TrainingEntities: 'سجل جهات التدريب',
      AttendanceMachines: 'ماكينات الحضور والانصراف',
      Insurances: 'التأمينات',
      MedicalInsuranceNumber: 'رقم التأمين الطبي',
      AdditionToInsuranceDate: 'تاريخ الاضافه للتأمين',
      Municipality: 'البلدية',
      HealthCertificateNumber: 'رقم الشهادة الصحية',
      SocialSecurityNumber: 'رقم التأمين الاجتماعي',
      EgyptianElectronicInvoice: 'الفاتورة الإلكترونية المصرية',
      SaudiEInvoice: 'الفاتورة الإلكترونية السعودية',
      CompanyLogo: 'شعار الشركة',
      AppBackground: 'خلفية البرنامج',
      InsurancePercentageDiscount: 'خصم نسبة التأمين ',
      JoiningInsuranceDate: 'تاريخ الالتحاق بالتأمين الاجتماعي',
      PrintDate: 'تاريخ الطباعه',
      Header: 'رأس الصفحة',
      Footer: 'ذيل الصفحة',
      ExtraDiscountValue: 'قيمة خصم اضافية',
      AnnualLeave: 'الاجازة السنوية',
      Month30Days: 'الشهر 30 يوم ',
      EndOfService: 'نهاية الخدمة',
      ShowingCovenantAndSalafists: 'اظهار العهد والسلفيات ',
      DownloadDueSalaries: 'تحميل الرواتب المستحقة',
      InsuranceNumber: 'الرقم التأميني',
      JoiningMedicalDate: 'تاريخ الالتحاق بالتأمين الطبي',
      EmployeeRank: 'رتبة الموظف',
      MedicalInsuranceValue: 'قيمة التأمين الطبي',
      InsuranceValue: 'قيمة التأمين',
      MoneyData: 'بيانات مادية',
      AdditionalInformation: 'بيانات اضافية',
      CreditAccount: 'حساب السلف',
      AbbreviatedName: 'اسم مختصر',
      CovenantAccount: 'حساب العهد',
      RelativesPhone: 'هاتف الاقارب',
      RecordNumber: 'رقم السجل',
      NumberOfChildren: 'عدد الابناء',
      OtherInformation: 'بيانات أخري',
      MilitarySituation: 'الحالة العسكرية',
      SpousePhone: 'هاتف الزوج/الزوجه',
      RelativesNames: 'اسماء الاقارب',
      PreviousJobAddress: 'عنوان الوظيفة السابقه',
      ReasonForLeavingJob: 'سبب ترك الوظيفة',
      JobLeavingDate: 'تاريخ ترك الوظيفة',
      SpouseName: 'اسم الزوج/الزوجه',
      ShiftPage: 'الورديات',
      Vacations: 'الاجازات',
      AdditionsTypes: 'أنواع الإستحقاقات والإضافات',
      DeductionsTypes: 'أنواع الخصومات والاستقطاعات',
      EmployeesCategories: 'فئات العمل',
      Workplace: 'مكان العمل',
      ShippingCompanies: 'شركات الشحن',
      DocumentsType: 'أنواع الوثائق',
      DocumentNumber: ' رقم المستند',
      SendElectronicInvoice: ' الفاتورة الإلكترونية',
      Sponsors: 'الكفلاء',
      Qualifications: 'المؤهلات',
      Qualification: 'المؤهل',
      Religion: 'الديانة',
      Specialization: 'التخصص',
      AnalyticAccountsLocation: 'موقع مراكز التكلفة',
      PostJournalEntry: 'إصدار قيد',
      NotPostJournalEntry: 'عدم إصدار قيد',
      AggregationbyCostCenters: 'تجميع على حسب مراكز التكلفة',
      AggregationbySubAccounts: 'تجميع على حسب الحسابات الفرعية',
      AggregationbyFinancialEntity: 'تجميع على حسب الكيان المالي',
      OpeningEntry: 'افتتاحى',
      ClosingEntry: 'اقفال',
      EmployeAccountClassification: 'تصنيف الحساب',

      RequestingDepartment: 'القسم الطالب',
      OrderTime: 'وقت الطلب',
      RequestDirectedToUser: 'الطلب موجه الى المستخدم',
      TenderDate: 'تاريخ المناقصة',
      TenderNo: 'رقم المناقصة',
      OrderRequestNo: 'رقم طلب الشراء',
      ResponsiblePerson: 'الشخص المسؤول',
      ClosePerson: 'شخص قريب',
      ResponsiblePersons: 'الاشخاص المسؤولين',
      PurchaseRequisition: 'طلب الشراء',
      FollowUpRequisition: 'متابعة الطلبات',
      NumbeRofRows: 'عدد الصفوف',
      AddSupplier: 'إضافة مورد',
      ShortName2: 'الاسم المختصر 2',
      TaxInfo: 'المعلومات الضريبية',
      AddressInfo: 'المعلومات الضريبية',
      LicenseType: 'نوع الترخيص',
      LicenseNumber: 'رقم الترخيص',
      TaxOffice: 'مأمورية الضرائب',
      TaxFileNumber: 'رقم الملف الضريبي',
      TaxCard: ' البطاقة الضريبية',
      Governorate: 'المحافظة',
      Tombstone2forAddress: 'علامة مميزة 2 للعنوان',
      Tombstone1forAddress: 'علامة مميزة 1 للعنوان',
      ClassificationExpiryDate: 'تاريخ انتهاء التصنيف',
      DefinedAttribute: 'مَعلم مُعرف',
      AddProduct: 'إضافة صنف',
      CommercialClassification: 'التصنيف التجاري',
      SellingPrices: 'أسعار البيع',
      PriceLists: 'قوائم الاسعار',
      QuantityPrices: 'أسعار الكميات',
      SalesPriceByWarehouse: 'أسعار البيع حسب المخزن',
      Suppliers: 'الموردين',
      ItemComponents: 'مكونات الصنف',
      ProductHistory: 'تاريخ الصنف',
      ItemProperties: 'خصائص الصنف',
      AddBarcode: 'إضافة الباركود',
      SystemCodes: 'اكواد المنظومة',
      Length: 'الطول',
      Width: 'العرض',
      Area: 'المساحة',
      Thickness: 'السمك',
      Weight: 'الوزن',
      NumberOfUnitsPerPackage: 'عدد الوحدات داخل العبوة',
      StorageLocation: 'مكان الحفظ',
      ReorderLimit: 'حد إعادة الطلب',
      MaximumLimit: 'الحد الاقصى',
      MinimumLimit: 'الحد الادنى',
      PrintNameAr: 'الاسم في الطباعة عربي',
      PrintNameEn: 'الاسم في الطباعة انجليزي',
      AlwaysInStock: 'دائماً في المخزون',
      PrintBarcode: 'طباعة الباركود',
      StopDealingThisProduct: 'ايقاف التعامل مع هذا الصنف',
      BasicUnit: 'الوحدة الاساسية',
      FromBasicUnit: 'من الوحدة الاساسية',
      OldPrice: 'سعر قديم',
      SellingPrice: 'سعر البيع',
      SellingUnit: 'سعر بيع الكميات',
      PurchasePrice: 'سعر الشراء',
      MinimumPrice: 'اقل سعر',
      number: 'عدد',
      Bouns: 'البونص',
      VirtualProfitMargin: 'نسبة ربح إفتراضية',
      TaxCategory: 'الفئة الضريبية',
      ConsumptionRate: 'معدل الاستهلاك',
      EligibilityRate: 'معدل الاستحقاق',
      WastePercentage: 'نسبة الهالك',
      OtherTax: 'ضريبة اخرى',
      PriceList: 'قائمة الاسعار',
      DiscountPercentage: 'نسبة الخصم',
      QuantityFrom: 'الكمية من',
      QuantityTo: 'الكمية الى',
      Manufacturer: 'الشركة المصنعة',
      ManufacturerCode: 'كود الشركة المصنعة',
      SupplierProductCode: 'كود صنف المورد',
      SupplierPrice: 'سعر المورد',
      SupplierBonus: 'بونص المورد',
      SupplierDiscount: 'نسبة خصم المورد',
      OfferQuantity: 'كمية العرض',
      ProductName: 'اسم الصنف',
      ManufacturingTemplate: 'نموذج التصنيع',
      Factor: 'المعامل',
      OtherInfo: 'معلومات اخرى',
      VariantName: 'اسم الوسم',
      variantValue: 'قيمة الوسم',
      TaxType: 'نوع الضريبة',
      Branch1: 'فرع رقم 1',
      Branch2: 'فرع رقم 2',
      Branch3: 'فرع رقم 3',
      Branch4: 'فرع رقم 4',
      Branch5: 'فرع رقم 5',
      Branch6: 'فرع رقم 6',
      Branch7: 'فرع رقم 7',
      Branch8: 'فرع رقم 8',
      Branch9: 'فرع رقم 9',
      Branch10: 'فرع رقم 10',
      MemoType: 'نوع المذكرة',
      MemoNumber: 'رقم المذكرة',
      ContractStatus: 'صفة التعاقد',
      OnAccount: 'تحمل علي حساب',
      CodeTypeGS1OrEGS: 'نوع الكود GS1 Or EGS',
      CodeGS1OrEGS: 'الكود GS1 Or EGS',
      DataBaseName: 'اسم قاعدة اليانات',
      OpeningBalance: 'الرصيد الافتتاحي',
      PurchaseOrder: 'أمر شراء',
      Direct: 'مباشر',
      FetchFromPurchaseRequest: 'جلب من طلب شراء',
      FetchFromEnvelopeOpeningCommitteePurchaseRequestNo:
        'جلب من لجنة فض مظاريف طلب شراء رقم',

      Shipping: 'الشحن',
      DeliveryPlace: 'مكان التسيم',
      PaymentTerms: ' شروط الدفع',
      DeliveryDate: 'تاريخ التسليم',
      Total: 'الاجمالي',
      Necessity: 'اللزوم',

      RFQ: 'طلب عرض سعر',
      Subject: 'الموضوع',
      Careof: 'عناية ',
      Taxes: 'الضرائب ',
      CommitmentDuration: 'مدة الارتباط',
      SupplyDuration: 'مدة التوريد',
      Days: 'يوماً',
      SearchInOffersFromDate: 'بحث في العروض من تاريخ',
      SearchByPurchaseOrderNumber: 'بحث برقم طلب شراء',
      SearchByOfferNumber: 'بحث برقم العرض',
      SelectionReasonForManager: 'سبب الاختيار للمدير',
      RFQComparisons: 'لجنة فحص المظاريف',
      EntitlementDate: 'تاريخ الاستحقاق',
      PurchaseType: 'نوع المشتريات',
      InvoiceType: 'نوع الفاتورة',
      JournalName: 'اسم اليومية',
      PurchaseInvoice: 'فاتورة مشتريات',
      NewServiceSalesTaxInvoice: 'فاتورة مبيعات خدمة ضريبة ',
      NewSalesTaxInvoice: 'فاتوره مبيعات ضريبيه',
      NewSalesTaxReturns: 'فاتوره مردودات مبيعات ضريبيه',
      NewServiceSalesInvoiceReturns: ' بيان مردودات مبيعات خدمة',
      NewSalesReturns: 'بيان مبيعات',
      NewSalesInvoice: 'بيان مردودات مبيعات',
      NewServiceSalesInvoice: ' بيان مبيعات خدمه',
      NewExchangeRequest: 'طلب صرف',
      PaymentRequest: 'بيانات الطلب',
      FinancialVerificationwithAttachments: 'المراجعه المالية والمرفقات',
      PaymentType: ' نوع الطلب ',
      Tax: 'ضريبي ',
      NonTax: 'غير ضريبي',
      Visitor: 'اسم الزائر',
      VisitNumber: 'رقم الزيارة',
      VisitorsName: 'أسماء الزائرين ',
      VisitPurpose: 'الغرض من الزيارة',
      CarNumber: 'رقم السيارة',
      OutTime: 'وقت الخروج',
      InTime: 'وقت الدخول',
      VisitDate: 'تاريخ الزيارة',
      SalesInvoiceNumber: 'رقم فاتوره المشتريات',
      NewServiceSalesItaxnvoiceReturns: 'فاتورة مبيعات مردودات خدمة ضريبة',
      NewProductsQuotations: 'عرض سعر صنف',
      NewServicesQuotations: 'عرض سعر خدمه',
      PriceIncludingTax: 'السعر شامل الضريبه',
      PurchaseOrderNumber: 'رقم طلب الشراء',
      CustomerDepartmen: 'قسم العميل',
      PresentedTo: 'مقدمه ل ',
      DeliveryTerms: 'شروط التسليم ',
      DeliveryLocation: 'مكان التسليم',
      ShippingMethod: 'طريقه الشحن',
      QuotationTerms: 'شروط العرض',
      PassedBy: 'معتمد من ',
      NewTaxInvoice: 'فاتورة  مبيعات ضريبية',
      SalesType: 'نوع المبيعات',
      ProductCode: 'كود الصنف',
      UUID: 'UUID',
      DueDate: 'تاريخ الاستحقاق',
      EinvoiceStatues: 'حالة الفاتورة الإلكترونية ',
      InvoiceLines: 'بنود الفاتورة',

      ServiceBills: 'فاتورة خدمة',
      DataImport: 'استيراد البيانات',
      Delivery: 'اذن صرف',
      Receipts: 'اذن استلام',
      ExtractionPermit: 'اذن استخراج',
      NetSalesReport:"صافى المبيعات",
      TotalDiscounts:"إجمالي الخصومات",
      TotalSalse_And_Discounts:" إجمالي المبيعات بعد الخصومات",
      TotalReturns:'إجمالي المرتجعات ',
      TotalDiscountsReturns:" إجمالي الخصومات والمرتجعات",
      TotalReturns_And_Discounts:"إجمالي المرتجعات والخصومات",
      SalesTaxReport:"متابعة الضرائب ",
      ContractsReport:"متابعة العقود",
      RentReport:"التاجير",
      QDiff:"فرق الكمية ",
      Qbak:"الكمية المعادة",
      TotalAmount:"إجمالي القيمة",
      RentalDays:"أيام التأجير ",
      LateTime:" التأخير ",
      RenewalWarrantyPeriod: ' تجديد فتره الضمان',
      NewRenewalWarrantyPeriod: 'تجديد فتره ضمان',
      GuaranteeDate: 'تاريخ الضمان',
      ExpirationDate: 'تاريخ الانتهاء',
      GuaranteeValue: ' قيمة الضمان',
      DateTravel: 'تاريخ السفر من',
      TicketsNumber: 'عدد التذاكر',

      GuaranteePercentage: 'نسبة الضمان',
      GuaranteeNumber: 'رقم الضمان',
      OriginalValue: 'القيمة الأصلية',
      NewValue: 'القيمة الجديدة',
      Difference: ' الفرق',
      OriginalInsurance: 'قيمة التأمين الأصلية',
      CashBoxesOpeningBalance: ' رصيد افتتاحي لصناديق النقدية',
      BanksOpeningBalance: 'الرصيد الافتتاحي للبنوك',
      CustomersOpeningBalance: 'الرصيد الافتتاحي للعملاء',
      EmployeesOpeningBalance: 'الرصيد الافتتاحي للموظفين',
      OpiningAndClosingJournals: ' القيود الافتتاحية والأقفال',
      SupplierOpeningBalance: 'الرصيد الافتتاحي للموردين',
      Journal: 'اليومية ',
      Scope: 'نطاق',
      ProjectValueAmount: 'قيمة المشروع',
      ProjectType: ' نوع المشروع',
      SalesReport:"يوميه المبيعات",
      InooiceNOVender:"رقم الفاتورة",
      BankAmount:"تحويلات بنكيه",
      DiscountCalculated:"الخصم المحتسب",
      ExtraMoney_Value:"المبلغ الإضافي",
      SalesReturnReport:"مرتد المبيعات",
      generaltax:"الضريبة العامة",
      Fright:"تكاليف الشحن",
      NewCustomer:"عميل جديد",
      RelativeName:" اسم أحد الأقارب",
      Kinship:"صلة القرابة",
      RelativePhone:"هاتف القريب",
      SpecialAddress:"عنوان فى حاله الطوارى",
      PercentPrice:"النسبه من السعر",
      QNet:"الكمية الصافية",
      QReturn:"الكمية المعادة",
      QSale:"الكمية المباعة",
      SumReturn:" إجمالي الإرجاع",
      SumSale:"إجمالي المبيعات",
      percentQ:"نسبة الكمية",
      UnAverageit:"المتوسط",
      DiscountRate :"نسبة الخصم ",
      ProductSaleReport:"المبيعات خلال فتره أصناف",
      CustomerProductsReport:" موقف عملاء أصناف ",
      RegistryNumber:"رقم السجل",
      January:"يناير",
      Febraury:"فبراير",
      March:"مارس",
      April:"أبريل",
      May:"مايو",
      June:"يونيو",
      July:"يوليو",
      August:"أغسطس",
      September:"سبتمبر",
      October:"أكتوبر",
      November:"نوفمبر",
      December:"ديسمبر",
      SalespersonCustomerReport:"أسماء العملاء لكل مندوب",
      ActivityCode:"كود التصنيف ",
      Short_Name_Ar:"كود مختصر عربي",
      governate:"المحافظه",
      Discount_percent:"نسبه الخصم",
      DiscountAccountName:" اسم حساب الخصم",
      DiscountAccount:"حساب الخصم",
      DeductionsAccountName:"اسم حساب الخصميات",
      InvoiceEarningsReport:" أرباح الفواتير ",
      Profit:"الربح",
      ProfitPercentage:"نسبة الربح",
      Indirect_Cost:" التكلفة غير المباشرة",
      SalespersonCommissionsPercentageReport:"تحصيلات وعمولات المندوبين نسبه",
      SalespersonCommissionsValueReport:"تحصيلات وعمولات المندوبين قيمه ",
      GreaterSales:"المبيعات الأكبر",
      Commission_Rate:" معدل العمولة",
      Commission_Value:"قيمة العمولة",
      TotalSalesReport:"اجمالى مبيعات العملاء خلال فترة",
      TotalSalesBeforTax_AfterDec:"إجمالي المبيعات قبل الضريبة بعد الخصم",
      ValDec:"قيمة الخصم",
      TotalReturnBefortax:" إجمالي الإرجاع قبل الضريبة",
      TotalReturnVAT:"إجمالي إرجاع ضريبة القيمة المضافة",
      TotalSalesBeforTax:" إجمالي المبيعات قبل الضريبة",
      TotalVAT:"إجمالي ضريبة القيمة المضافة",
      mardodat:"المردودات",
      valSales:"قيمة المبيعات",
      DeliveryOrderReport:"متابعة اوامر التوريد",
      OrderNO:"رقم الطلب",
      SalesOrder:"أمر بيع",
      NotCompleted:"غير مكتمل",
      Draft:"مسودة",
      Done:"تم",
      AllSalesOrder:"جميع أوامر البيع",
      AllOrders:"جميع الطلبات",
      AnnualSalesAnalysis:"تحليل المبيعات سنوى أصناف",
      InvoicesReport:"متابعه الفواتير",
      CollectedDay:"المدة الباقية",
      CollectedDate:"تاريخ التحصيل",
      servicetype:"نوع الخدمه",
      Tahseel:"حاله التحصيل",
      SuplayDate:"تاريخ التوريد",
      TahseelNote:" ملاحظة التحصيل",
      TahseelModa:"طريقة التحصيل",
      Isal_Vale:"المبلغ المُصدر",
      MonyStay:" المتبقي",

      saltax:"ضريبة المبيعات",
      TAX_STATION:"مكتب الضريبة",


      ChangingGuaranteeValue: ' تغير قيمه الضمان',
      NewChangingGuaranteeValue: ' تغير قيمه الضمان',
      LettersOfGuaranteeUsed: 'الضمانات المستخدمه',
      NewLettersOfGuaranteeUsed: 'الضمانات المستخدمه',
      PurchaseStatement: 'بيان مشتريات',
      BillsReturns: 'مردودات مشتريات',
      ReturnsServiceBills: 'مردودات خدمة',
      PurchasesTypes: 'انواع المشتريات',
      SupplieresClassification: 'تصنيف الموردين',
      ReceiptsRequest: 'طلب استلام',
      DeliveryRequest: 'طلب صرف',
      JobTitle:"المسمى وظيفي",
      JournalItems: 'عناصر اليومية',
      Administrator: 'الشخص المسؤول',
      ImportfromSalesOrder: 'استيراد من امر بيع',
      Source: 'المصدر',
      Order: 'طلبية',
      Statement: 'البيان',
      PurchaseTaxInvoice: 'فاتورة مشتريات ضريبية',
      ManufacturingOrder: 'امر تصنيع',
      PurchaseInvoiceNumber: 'رقم فاتورة المشتريات',
      SalesInvoice: 'فاتورة مبيعات',
      SalesTaxInvoice: 'فاتورة مبيعات ضريبية',
      ManualTransferNo: 'رقم التحويل اليدوي',
      Transfers: 'التحويل بين المخازن',
      PurchaseRequest: 'طلب شراء',
      DeliveryExecute: 'تنفيذ الصرف',
      DeliveryNo: 'رقم الصرف',
      InvoicesNotFullyDelivered: 'الفواتير التي لم تُسلم بالكامل',
      InvoicesThatHavebeenDelivered: 'الفواتير التي تم تسليمها',
      DeliveryNote: 'تسليم الاصناف',
      SampleDescription: 'وصف العينة',
      CustomersSamples: 'عينات العملاء',
      ReceivingDate: 'تاريخ الاستلام',
      RecipientName: 'اسم المستلم',
      CarNo: 'رقم السيارة',
      EmployeesCustody: 'عهد الموظفين',
      HandingOvertoEmployee: 'الموظف المستلم',
      ReceivingfromEmployee: 'استلام من الموظف',
      WithoutJournal: 'بدون قيد',
      Recipient: 'المستلم',
      InventoryAdjustments: 'محضر جرد',
      GroupByItemProperties: 'تجميع حسب خصائص الصنف',
      WithBalances: 'بالارصده',
      DeductionsAdjustments: 'تسوية بالخصم',
      AdditionadJustments: 'تسوية بالاضافة',
      FetchfromInventoryReport: 'جلب من محضر جرد',
      fetchfromNetConsumption: 'جلب من صافي المستهلك',
      UpdateWarehouse: 'تحديث مخزن',
      UpdateProduct: 'تحديث صنف',
      SetAvregeCost: 'تعيين متوسط التكلفة',
      NetConsumer: 'صافي المستهلك',
      Average: 'المتوسط',
      AllWarehouses: 'كل المخازن',
      ProductsReport: 'تقرير الاصناف',
      AccountCode: 'كود الحساب',
      ItemId: 'كود الصنف',
      UnitName: 'اسم الوحدة',
      BonusReport: 'تقرير البونص',
      ProfitabilityReport: 'تقرير كارت الصنف بربحية',
      TradeType: 'التصنيف التجاري',
      ValuationType: 'التقييم على اساس',
      valuationReport: 'تقرير تقييم المخزون',
      TotalvaluationReport: 'تقرير تقييم المخزون اجمالي',
      ProductCategoriesOnly: 'المجموعات الرئيسية فقط',
      SrialReport: 'البحث عن سيريال',
      ByVariants: 'تجميع على حسب خواص الصنف',
      ProductsBalanceReport: 'تقرير ارصدة الاصناف',
      MovementByOperationsreReport: 'تقرير مراجعة الحركات حسب الحركة',
      MovementByStoreReport: 'تقرير مراجعة الحركات حسب المخزن',
      ReplenishmentReport: 'تقرير أصناف تجاوزت حد الطلب',
      TradeName: 'الاسم التجاري',
      UnitCost: 'سعر الوحدة',
      ValItemTax: 'قيمة ضريبة الصنف',
      PriceOne: 'سعر الوحدة',
      CostPrice: 'سعر التكلفة',
      NumberOfDays: 'عدد الايام',
      StagnantproductsReport: 'متابعة ركود الاصناف',
      ProductsExpiringon: 'الاصناف التي تنتهي صلاحيتها في',
      SupplierEvaluation: 'تقييم الموردين',
      ExpirationDateReport: 'تقرير تاريخ الصلاحية',
      ComponentCostReport: 'تقرير تكلفة مكونات صنف',
      PriceControlReport: 'تقرير مراقبة اسعار الاصناف',
      ConsumptionReport: 'تقرير مراقبة استهلاك الاصناف',
      OperationsControlReport: 'تقرير مراقبة العمليات',
      Taxable: 'ضريبي',
      NonTaxable: 'غير ضريبي',
      Descount: 'الخصم',
      PurchasesReport: 'تقرير يومية المشتريات',
      PurchasesReturnsReport: 'تقرير مردودات المشتريات',
      PurchasesTaxReport: 'تقرير متابعة الضرائب',
      SuppTypeID: 'كود نوع المورد',
      FileNO: 'رقم الملف',
      ByNet: 'بالصافي',
      SuppliersAccountInMonthesReport: 'حساب مورد بالشهور',
      PurchasesPeriodicallyReport: 'مشتريات الموردين خلال فترة',
      MardodatBeforeVAT: 'اجمالي المردودات قبل الضريبة',
      PurchaseBeforeTax: 'اجمالي المشتريات قبل الضريبة',
      TotalPurchaseBeforTaxAfterDec: 'إجمالي المشتريات قبل الضريبة بعد الخصم',
      TotalPurchaseVAT: 'إجمالي ضريبة القيمة المضافة على المشتريات',
      TotalReturnedVAT: 'إجمالي ضريبة القيمة المضافة مردودات',
      Returnes: 'مردودات',
      Valbr: 'اجمالي المشتريات بعد الضريبة',
      SuppliersProductsReport: 'تقرير موقف الموردين اصناف',
      itemSupId: 'كود صنف المورد',
      Supplierbonus: 'بونص المورد',
      SupplierDescount: 'نسبة خصم المورد',
      PurchaseRequisitionsReport: 'تقرير طلبات الشراء',
      PurchasOrderReport: 'تقرير اوامر الشراء',
      PurchaseOrdersPaymentsReport: 'دفعات اوامر الشراء',
      LandedCostReport: 'مصروفات سند التكلفة',
      InvoceStatus: 'حالة الفاتورة',
      Warehouses: 'المحزون',
      FollowUpBillsReport: 'متابعة فواتير المشتريات',
      OperationsTypes: 'انواع العمليات',
      ProductAttributes: 'الخصائص',
      ProductAttributesValues: 'قيم الخصائص',
      TradeClassification: 'التصنيف التجاري',
      Manufacturers: 'الشركات المصنعة',
      Units: 'وحدات القياس',
      Bonus: 'البونص',
      BarcodeGenerate: 'ضبط الباركود',
      SupplierName: 'اسم المورد',
      TotalsProductsBalanceReport: 'ارصدة الاصناف مجمع',
      DocumentaryCreditsType: 'انواع الاعتمادات المستندية',
      LetterOfGuarantee: 'خطاب الضمان',
      LetterOfGuaranteetype: 'نوع خطاب الضمان',
      Bank: 'البنك',
      FacilityValue: 'قيمة التسهيلات',
      Commission: 'العمولة',
      CashInsurance: 'التأمين النقدي',
      DocumentaryCreditLimits: 'حدود الاعتمادات المستندية',
      CashInsurancePercentage: 'نسبة التأمين النقدي',
      LetterofCreditType: 'نوع الاعتماد المستندي',
      LetterofCreditNumber: 'رقم الاعتماد',
      LetterofCreditValue: 'قيمة الاعتماد',
      Transaction: 'العملية',
      DocumentaryCreditsBalance: 'رصيد الاعتمادات المستندية',
      RenewalAccreditationPeriod: 'تجديد فترة الاعتماد',
      ChangeCreditValue: 'تغيير قيمة الاعتماد',
      NewInsuranceValue: 'قيمة التأمين الجديدة',
      LoanTypes: 'أنواع القروض',
      TransactionNo: 'رقم الحركة',
      LargestUnit: 'الوحدة الكبرى',
      MiddleUnit: 'الوحدة المتوسطة',
      MiddleUnitQuantity: 'كمية الوحدة المتوسطة',
      SmallUnitQuantity: 'كمية الوحدة الصغرى',
      SmallUnit: 'الوحدة الصغرى',
      Cost: 'التكلفة',
      DefaultSellingPrice: 'سعر البيع الافتراضي',
      SellingProfitMargin: 'هامش ربح البيع',
      MinPrice: 'السعر الأدنى',
      MinProfitMargin: 'هامش الربح الأدنى',
      LoanLimits: 'حدود القروض',
      LoanType: 'نوع القرض',
      AccountOperator:"مشغل الحساب",
      IDIssueDate:"تاريخ إصدار الهوية",
      FixedPrice:"سعر ثابت",
      DefaultSellingUnit:"وحدة البيع الافتراضية",
      AnalyticAccountsGroups:"مجموعات مراكز التكلفة",
      NewAnalyticAccountsGroups:"مجموعه مركز التكلفة",
      GroupName:"اسم المجموعة",
      AnalyticAccountsDetailed:"مراكز تكلفة تفصيلي",
      AnalyticAccountsTotal:"مراكز تكلفة اجمالي",
      AnalyticAccountsMonthly:"مراكز تكلفة اجمالي بالشهور",
      AnalyticAccountsReport:"موقف مراكز التكلفة",
      IndirectCostRate:"نسبة التكلفة غير المباشرة",
      MonthlyNetConsumer:" صافى المستهلك الشهري",
      TotalConsumer:"اجمالي المستهلك",
      AnalyticAccountsPO:"اومر توريد مراكز التكلفة",
      AnalyticAccountsList:"دليل مراكز التكلفة",
      BankAccountsReport:"حساب البنوك",
      CustomerAccountsReport:"حساب العملاء",
      MandopPercent:"نسبة المندوب",
      PayablesAccountsReport:"حساب الموردين",
      taxcard:"السجل التجارى",
      LedgerReport:"حساب استاذ",
      estinationTypeName:"نوع الجهة المستهدفة",
      StaffAccountsReport:"حساب الموظفين",
      SalespersonAccountReport:"كشف حساب المندوب",
      CashBoxAccountsReport:"حساب صناديق النقدية",
      PartnersAccountsReport:"حساب الشركاء",
      MonthlyAccountReport:"حساب شهري",
      RenewalLoanPeriod:"تجديد فترة القرض",
      LoanNumber:"رقم القرض",
      LoansRepayment:"سداد القروض",
      StartBalance:"الرصيد الافتتاحي",
      AverageCost:"متوسط السعر",
      PurchasedProductsReport:"تقرير المشتريات خلال فترة اصناف",
      LastPurshasCost:"سعراخر شراء",
      QuantityPercentage:"النسبة في الكمية",
      pricePercentage:"النسبة في السعر",
      SalariesPrintReport:"طباعة الرواتب",
      AccountsMirrorReport:"مراة الحسابات",
      SalesTaxesReport:"اقرار ضرائب المبيعات",
      Details:"التفاصيل",
      EditValue:" تعديل القيمة",
      TaxValue:"قيمة الضريبة",
      TotalValue:"القيمة الإجمالية",
      unbalcedJournals:"قيود غير متوازنة",
      missingJournals:"قيود مفقودة",
      JournalEntryReport:"قيود اليومية",
      AgedReceivableReport:"أعمار الديون عملاء",
      AgedPayableReport:"أعمار ديون الموردين",
      GeneralLedgerReport:"حساب استاذ العام",
      includeZeroBalances:" عرض الأرصدة الصفرية",
      groupByFinancialEntity:" تجميع حسب الكيان المالي",
      groupBySubAccount:"تجميع حسب الحسابات الفرعية",
      Advance:"سلفة",
      StatementDate:"تاريخ المستخلص",
      StatementValue:"قيمة المستخلص",
      StatementNumber:"رقم المستخلص",
      LoanValue:"قيمة القرض",
      Duration:"المدة",
      DuratiFinancingPercentageon:"نسبة التمويل",
      BorrowingCost:"تكلفة الاقتراض",
      PaidAmount:"المبلغ المسدد",
      LoanBalances:"أرصدة القرض حسب المشروع",
      Greater_0:"من 0 ل 30",
      Greater_30:"من 31 ل 60",
      Greater_60:"من 61 ل 90",
      Greater_90:"من 91 ل 120",
      Greater_120:"من 121 ل 150",
      Greater_150:"من 151 ل 180",
      Greater_180:"اكبر من 180 يوم",
      NotPostedTransactions:"حركات لم يتم ترحيلها",
      PrepaidExpenses:"المصروفات المدفوعة مقدما",
      PaymentsAndReceiptReport:"حركات سندات الصرف والقبض",
      IndirectCostsSetting:"اعدادات التكاليف الغير مباشرة",
      TrialBalanceReport:"ميزان المراجعة ",
      BalanceCredit :"الرصيد الدائن",
      BeforDaen:"الرصيد الدائن السابق",
      TrialBalanceLevelsReport:"ميزان بالحسابات الرئيسية",
      FieldRequired:"الحقل اجباري",




    },


    SETTINGS: {
      UNITS: {
        TITLE: 'وحدات القياس',
        WEIGHT: {
          TITLE: 'الوزن',
          DESC: 'وحدة قياس الوزس',
          KILOGRAMS: 'الكيلو جرام',
          POUNDS: 'الرطل',
        },
        VOLUME: {
          TITLE: 'الحجم',
          DESC: 'وحدة قياس الحجم',
          METER: 'متر مكعب',
          FEET: 'قدم مكعب',
        },
      },
      PERMISSIONS: 'الصلاحيات',
      PASSWORDRESET: 'إستعادة كلمة المرور',
      PASSWORDRESETDESC: 'تمكين المستخدم من إستعادة كلمة المرور',
    },
    CUSTOMER: {
      TABS: {
        PERSONAL_INFO: 'المعلومات الشخصية',
        ADDRESS: 'العنوان',
        OFFICIALS: 'الفروع و الأشخاص المسئولين و متخذي القرار',
        SALES_PERSON: 'مندوبي البيع',
        RELATIVES: 'بيانات الأقارب و وسائل الاتصال',
      },
      TYPE: {
        INDIVIDUAL: 'فردي',
        COMPANY: 'شركة',
      },
      ARABIC_NAME: 'الاسم',
      ENGLISH_NAME: 'الاسم الانجليزي',
      TITLE: 'اللقب',
      CUSTOMER_CLASSIFIED: 'التصنيف',
      MAIN_CUSTOMER: 'الحساب الرئيسي',
      EMAIL: 'البريد الإلكتروني',
      SHORT_AR: ' الكود المختصر',
      SHORT_EN: 'الكود المختصر 2',
      PHONE: 'الموبايل',
      TEL: 'الهاتف',
      FAX: 'الفاكس',
      ACTIVITY: 'النشاط',
      TAX_STATION: 'موقف الضريبة',
      COLLECTION: 'التحصيل',
      CREDIT_LIMIT: 'الحد الائتماني',
      WEBSITE: 'الموقع الإلكتروني',
      PAYMENT_METHOD: 'طريقة الدفع',
      PRICE_LIST: 'قائمة الأسعار',
      WORKING_TIME: 'وقت العمل',
      NATIONALITY: 'الجنسية',
      DURATION_OF_COLLECTION: 'مدة التحصيل',
      OFFICIAL_HOLIDAY: 'العطلة الرسمية',
      BIRTHDAY: 'تاريخ الميلاد',
      DISCOUNT_PERCENTAGE: 'نسبة الخصم',
      NOTES: 'ملاحظات',
      ACCOUNT_OPERATOR: 'مشغل الحساب',
      ID_NUMBER: 'رقم الهوية',
      WorkPhone:"هاتف العمل",
      ISSUER: 'المُصدر',
      BANK_NAME: 'اسم البنك',
      EXPIRATION_DATE: 'تاريخ الانتهاء',
      ACCOUNT_NUMBER: 'رقم الحساب',
      INCREASE_RATE: 'معدل الزيادة',
      VAT_NUMBER: 'رقم الضريبة المضافة',
      COMMERCIAL_REGISTER: 'السجل التجاري',
      TYPE_CODE: 'رمز النوع',
      ACTIVITY_CODE: 'رمز النشاط',
      COUNTRY: 'الدولة',
      REGION: 'المنطقة',
      CITY: 'المدينة',
      POSTAL_CODE: 'الرمز البريدي',
      BRANCH_ENABLED: 'تم تمكين الفرع',
      BRANCH: 'الفرع',
      BUILDING_NUMBER: 'رقم المبنى',
      FLOOR_NUMBER: 'رقم الطابق',
      OFFICE_NUMBER: 'رقم المكتب',
      LANDMARK: 'العلامة المميزة',
      DISTRICT: 'الحي',
      STREET: 'الشارع',
      ADDITIONAL_INFORMATION: 'معلومات إضافية',
    },
    Salary: {
      ed_sahar: 'الراتب الاساسى',
      elawa: 'البدلات',
      mobile: 'اجمالي الراتب',
      nafaa: 'الحضور',
      Day: 'اجمالي الايام',
      DayGyab: 'خصم غياب',
      Tel: 'اساسى مستحق',
      b_ent: 'بدل انتقال',
      others3: 'بدل طعام',
      b_tab: 'بدل تليفون',
      hawafez: 'بدل سكن',
      brnchs: 'القطاع',
      b_malbas: 'بدلات اخري',
      b_food: 'بدل موقع',
      takher: 'اضافات',
      ed_agaza: 'الوقت الاضافى',
      totalm: 'اجمالي المستحق',
      moneytakher: 'خصم تأخير',
      Temoneygyabl: 'خصم غياب',
      moneygyab: 'خصم جزاء غياب',
      AdvancedPayment: 'خصم دفعه',
      sick: 'خصم مرضى',
      gaza: 'خصومات',
      oth_sal: 'البدلات',
      solfa_cut: 'سلفه',
      Badl_Agaza: 'بدل اجازه',
      tax: 'ضريبة الدخل',
      MedicalInsurance: 'التامين الطبي',
      ins: 'حصة الموظف فى التامينات',
      totalkh: 'اجمالي الخصم',
      houraddagaza: 'العطلات الرسمية',
      daygazaa: 'الاجازات',
      safy: 'صافى المرتب',
      Take_Schole: 'بدل مدارس',
      Take_Percent: 'بدل نسبة',
      BarCode: 'الرقم الوظيفي',
      IqamaID: 'رقم الاقامة',
      Count_Edafy_Hours: 'ساعات اضافى',
      Kafeel: 'الكفيل',
      WorkPlan: 'فئة العمل',
      anohterDescount: 'الراتب التامينى',
      w_a_ins: 'حصة الشركة فى التامينات',
      emp_tamensallery: 'الراتب التامينى',
      emp_tamensalleryCanged: ' الراتب التأمينى المتغير',
      emp_Take_Home: 'بدل السكن',
      TotalSalleary: 'اجمالي الراتب',
      Paid: 'المدفوع',
      PaidForInsurance: 'المدفوع للتأمينات',
      TotalEmpShare: 'اجمالي حصة الموظف',
      LastComBak: 'اخر عودة',

    },
  },
};
