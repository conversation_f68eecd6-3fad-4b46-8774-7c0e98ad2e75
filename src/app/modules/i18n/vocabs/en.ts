// USA
export const locale = {
  lang: 'en',
  data: {
    TRANSLATOR: {
      SELECT: 'Select your language',
    },
    MENU: {
      NEW: 'new',
      ACTIONS: 'Actions',
      CREATE_POST: 'Create New Post',
      PAGES: 'Pages',
      FEATURES: 'Features',
      APPS: {
        TITLE: 'Apps',
        INVENTORY: 'Inventory',
      },
      DASHBOARD: 'Dashboard',
      PERMISSIONS: 'Permissions',
      COMPANY: 'Company Info',
      USERS: 'Users',
      ROLES: 'Roles',
      SETTINGS: 'Settings',
      GENERAL: 'General',
      ADMIN: 'Admin',
      HR: 'HR',
      PURCHASE: 'Purchase',
      Costs: 'Cost',
      CRM: 'CRM',
      ACCOUNTING: 'Accounting',
      MAINTENANCE: 'Maintenance',
      MANUFACTURING: 'Manufacturing',
      PROJECT: 'Project',
      REALESTATE: 'Realestate',
      FLEET: 'Fleet',
      POS: 'POS',
      HOTEL: 'Hotel',
      SALES: 'Sales',
      STAFFASSISTANT: 'Staff Assistant',
    },
    AUTH: {
      GENERAL: {
        OR: 'Or',
        SUBMIT_BUTTON: 'Submit',
        NO_ACCOUNT: "Don't have an account?",
        SIGNUP_BUTTON: 'Sign Up',
        FORGOT_BUTTON: 'Forgot Password',
        BACK_BUTTON: 'Back',
        PRIVACY: 'Privacy',
        LEGAL: 'Legal',
        CONTACT: 'Contact',
      },
      LOGIN: {
        TITLE: 'Login Account',
        BUTTON: 'Sign In',
      },

      FORGOT: {
        TITLE: 'Forgotten Password?',
        DESC: 'Enter your email to reset your password',
        SUCCESS: 'Your account has been successfully reset.',
      },
      REGISTER: {
        TITLE: 'Sign Up',
        DESC: 'Enter your details to create your account',
        SUCCESS: 'Your account has been successfuly registered.',
      },
      INPUT: {
        EMAIL: 'Email',
        FULLNAME: 'Fullname',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: 'Username',
      },
      VALIDATION: {
        INVALID: '{{name}} is not valid',
        REQUIRED: '{{name}} is required',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        AGREEMENT_REQUIRED: 'Accepting terms & conditions are required',
        NOT_FOUND: 'The requested {{name}} is not found',
        INVALID_LOGIN: 'The login detail is incorrect',
        REQUIRED_FIELD: 'Required field',
        MIN_LENGTH_FIELD: 'Minimum field length:',
        MAX_LENGTH_FIELD: 'Maximum field length:',
        INVALID_FIELD: 'Field is not valid',
      },
    },
    INVENTORY: {
      WAREHOUSES: {
        NAMEAR: 'Arabic Name',
        NAMEEN: 'English Name',
        SHORT: 'Short Name',
        ADDRESS: 'Address',
        SEARCH: 'Search in Warehouses',
        CREATE: 'Create New',
        FILTER: 'Filter',
        USERS: 'Users',
        CREATETITLE: 'Create New Warehouse',
        EDITTITLE: 'Edit Warehouse',
      },
    },
    SALES: {
      PSP: 'Product Sales Percentage',
      SSP: 'Service Sales Percentage',
      OfProfit: 'Of Profit',
      OfTotalBill: 'Of The Total Bill',
    },
    COMPANY: {
      NAMEAR: 'Arabic Name',
      NAMEEN: 'English Name',
      CRN: 'Commmercial Registration Number',
      ADDRESS: 'Address',
      MAIL: 'Email',
      MOBILE: 'Mobile',
      HEADER: 'Header',
      FOOTER: 'Footer',
      LOGO: 'Logo',
      CREATETITLE: 'Update Company Info',
    },
    COMMON: {
      LOADING: 'Loading...',
      CashBox: 'Cash Box',
      ACTIONS: 'Actions',
      SUBMIT: 'Submit',
      DriverName: 'Driver Name',
      ROLE: 'Role Name',
      DELETE: 'Delete',
      EDIT: 'Edit',
      PRINT: 'Print',
      DISCARD: 'Discard',
      EXPORT: 'Export',
      // POS Common Translations - Non-duplicate keys only
      SalesReports: 'Sales Reports',
      BestSellers: 'Best Sellers',
      // Common General Keys
      Settings: 'Settings',
      Inventory: 'Inventory',
      Stats: 'Statistics',
      SystemStatus: 'System Status',
      // Time Periods
      Daily: 'Daily',
      Weekly: 'Weekly',
      Monthly: 'Monthly',
      // POS Hardware
      CashDrawer: 'Cash Drawer',
      CustomerDisplay: 'Customer Display',
      BarcodeScanner: 'Barcode Scanner',
      // General Module
      Companies: 'Companies',
      Branches: 'Branches',
      Currencies: 'Currencies',
      Cities: 'Cities',
      Countries: 'Countries',
      RecentStatistics: 'Recent Statistics',
      Latest: 'Latest',
      LastOpened: 'Last Opened',
      ReceiptPrinter: 'Receipt Printer',
      StatusOnline: 'Status Online',
      Ready: 'Ready',
      LiveData: 'Live Data',
      // Dashboard Common
      InventoryOverview: 'Inventory Overview',
      InventoryStats: 'Inventory Statistics',
      TotalProducts: 'Total Products',
      TotalWarehouses: 'Total Warehouses',
      LowStockItems: 'Low Stock Items',
      InventoryValue: 'Inventory Value',
      InventoryMovement: 'Inventory Movement',
      Last30Days: 'Last 30 Days',
      StockDistribution: 'Stock Distribution',
      ByCategory: 'By Category',
      RecentActivities: 'Recent Activities',
      LatestInventoryTransactions: 'Latest Inventory Transactions',
      ViewAll: 'View All',
      QuickAccess: 'Quick Access',
      CommonInventoryOperations: 'Common Inventory Operations',
      NewReceipt: 'New Receipt',
      NewDelivery: 'New Delivery',
      NewTransfer: 'New Transfer',
      ValuationReport: 'Valuation Report',
      ManageProducts: 'Manage Products',
      ManageWarehouses: 'Manage Warehouses',
      ItemsNeedingRestock: 'Items Needing Restock',
      // HR Dashboard
      HROverview: 'HR Overview',
      HRStats: 'HR Statistics',
      TotalEmployees: 'Total Employees',
      AttendanceRate: 'Attendance Rate',
      PendingVacations: 'Pending Vacations',
      TurnoverRate: 'Turnover Rate',
      DepartmentDistribution: 'Department Distribution',
      EmployeesByDepartment: 'Employees by Department',
      AttendanceTrends: 'Attendance Trends',
      LatestHRTransactions: 'Latest HR Transactions',
      CommonHROperations: 'Common HR Operations',
      NewEmployee: 'New Employee',
      NewVacation: 'New Vacation',
      ManualAttendance: 'Manual Attendance',
      AttendanceReports: 'Attendance Reports',
      NewBonus: 'New Bonus',
      JobDescriptions: 'Job Descriptions',
      UpcomingEvents: 'Upcoming Events',
      NextTwoWeeks: 'Next Two Weeks',
      EmployeeBirthdays: 'Employee Birthdays',
      ThisMonth: 'This Month',
      // Main Dashboard
      SystemOverview: 'System Overview',
      SystemStats: 'System Statistics',
      ActiveModules: 'Active Modules',
      TotalUsers: 'Total Users',
      TotalTransactions: 'Total Transactions',
      SystemPerformance: 'System Performance',
      RecentTransactions: 'Recent Transactions',
      ModuleUsage: 'Module Usage',
      SystemAlerts: 'System Alerts',
      QuickLinks: 'Quick Links',
      // Purchases Dashboard
      PurchasesOverview: 'Purchases Overview',
      PurchasesStats: 'Purchases Statistics',
      TotalPurchases: 'Total Purchases',
      PendingOrders: 'Pending Orders',
      TotalSuppliers: 'Total Suppliers',
      PurchasesTrends: 'Purchases Trends',
      TopSuppliers: 'Top Suppliers',
      RecentPurchases: 'Recent Purchases',
      PurchasesByCategory: 'Purchases by Category',
      PurchaseValue: 'Purchase Value',
      TotalSpent: 'Total Spent',
      ActiveSuppliers: 'Active Suppliers',
      RequiresAttention: 'Requires Attention',
      ByPurchaseVolume: 'By Purchase Volume',
      PurchasesTrendsChart: 'Purchases Trends Chart',
      CurrentYear: 'Current Year',
      Supplier: 'Supplier',
      Amount: 'Amount',
      OrderID: 'Order ID',
      Status: 'Status',
      Category: 'Category',
      NewPurchaseOrder: 'New Purchase Order',
      CreateNewOrder: 'Create New Order',
      ApprovePurchases: 'Approve Purchases',
      PendingApprovals: 'Pending Approvals',
      PurchaseReports: 'Purchase Reports',
      GenerateReports: 'Generate Reports',
      ManageSuppliers: 'Manage Suppliers',
      AddEditSuppliers: 'Add/Edit Suppliers',
      // Sales Dashboard
      SalesOverview: 'Sales Overview',
      SalesStats: 'Sales Statistics',
      TotalSales: 'Total Sales',
      TotalRevenue: 'Total Revenue',
      TotalCustomers: 'Total Customers',
      SalesTrends: 'Sales Trends',
      TopProducts: 'Top Products',
      TopCustomers: 'Top Customers',
      RecentSales: 'Recent Sales',
      SalesByRegion: 'Sales by Region',
      RevenueGrowth: 'Revenue Growth',
      ActiveCustomers: 'Active Customers',
      BySalesVolume: 'By Sales Volume',
      SalesTrendsChart: 'Sales Trends Chart',
      Product: 'Product',
      Customer: 'Customer',
      Region: 'Region',
      // CRM Dashboard
      CRMOverview: 'CRM Overview',
      CRMStats: 'CRM Statistics',
      CRMDashboard: 'CRM Dashboard',
      CustomerRelationshipManagement: 'Customer Relationship Management',
      Active: 'Active',
      ActiveLeads: 'Active Leads',
      NewCustomers: 'New Customers',
      CustomerSatisfaction: 'Customer Satisfaction',
      OpenTickets: 'Open Tickets',
      LeadConversion: 'Lead Conversion',
      ConversionRate: 'Conversion Rate',
      CustomerRetention: 'Customer Retention',
      SalesOpportunities: 'Sales Opportunities',
      GrowthRate: 'Growth Rate',
      MessagesSent: 'Messages Sent',
      WhatsApp: 'WhatsApp',
      SMS: 'SMS',
      Email: 'Email',
      Satisfied: 'Satisfied',
      Unsatisfied: 'Unsatisfied',
      CustomerEngagement: 'Customer Engagement',
      Created: 'Created',
      ShowAll: 'Show All',
      Today: 'Today',
      Yesterday: 'Yesterday',
      Last7Days: 'Last 7 Days',
      Last30DaysCRM: 'Last 30 Days',
      LastMonth: 'Last Month',
      RecentActivitiesCRM: 'Recent Activities',
      LatestCustomerInteractions: 'Latest Customer Interactions',
      NewLeadCreated: 'New Lead Created',
      ByUser: 'By User',
      CustomerMeeting: 'Customer Meeting',
      WithClient: 'With Client',
      ProposalSent: 'Proposal Sent',
      ToClient: 'To Client',
      NewEnquiryReceived: 'New Enquiry Received',
      ViaWebsite: 'Via Website',
      QuickActions: 'Quick Actions',
      CommonTasks: 'Common Tasks',
      AddNewLead: 'Add New Lead',
      CreateNewLeadRecord: 'Create New Lead Record',
      ScheduleMeeting: 'Schedule Meeting',
      ArrangeCustomerMeeting: 'Arrange Customer Meeting',
      SendCampaign: 'Send Campaign',
      CreateMarketingCampaign: 'Create Marketing Campaign',
      GenerateReportsCRM: 'Generate Reports',
      CreateCustomReports: 'Create Custom Reports',
      UpcomingTasks: 'Upcoming Tasks',
      ScheduledActivities: 'Scheduled Activities',
      DealsPipeline: 'Deals Pipeline',
      SalesForecast: 'Sales Forecast',
      NewCampaign: 'New Campaign',
      // Accounting Dashboard
      AccountingOverview: 'Accounting Overview',
      AccountingStats: 'Accounting Statistics',
      TotalIncome: 'Total Income',
      TotalExpenses: 'Total Expenses',
      NetProfit: 'Net Profit',
      AccountsReceivable: 'Accounts Receivable',
      AccountsPayable: 'Accounts Payable',
      CashFlow: 'Cash Flow',
      BudgetVariance: 'Budget Variance',
      // Accounting Dashboard New
      AccountingDashboard: 'Accounting Dashboard',
      AccountingModule: 'Accounting',
      AccountingRevenue: 'Revenue',
      AccountingExpenses: 'Expenses',
      AccountingReceivables: 'Receivables',
      AccountingPayables: 'Payables',
      AccountingCash: 'Cash',
      AccountingRevenueAndExpenses: 'Revenue & Expenses',
      AccountingLastSixMonths: 'Last 6 Months',
      AccountingQuickActions: 'Quick Actions',
      AccountingCommonOperations: 'Common Operations',
      AccountingRecentTransactions: 'Recent Transactions',
      AccountingLastFiveTransactions: 'Last 5 Transactions',
      AccountingTransactionID: 'Transaction ID',
      AccountingType: 'Type',
      AccountingAmount: 'Amount',
      AccountingDate: 'Date',
      AccountingStatus: 'Status',
      AccountingViewReport: 'View Report',
      AccountingFinancialReports: 'Financial Reports',
      AccountingQuickFinancialReports: 'Quick Financial Reports',
      AccountingViewAllReports: 'View All Reports',
      AccountingIncomeStatement: 'Income Statement',
      AccountingBalanceSheet: 'Balance Sheet',
      AccountingCashFlowStatement: 'Cash Flow Statement',
      AccountingTotalAssets: 'Total Assets',
      AccountingNetCashFlow: 'Net Cash Flow',
      AccountingHome: 'Home',

      // Project Dashboard
      ProjectDashboard: 'Project Dashboard',
      ProjectModule: 'Projects',
      ProjectTotal: 'Total Projects',
      ProjectActive: 'Active Projects',
      ProjectCompleted: 'Completed Projects',
      ProjectPending: 'Pending Projects',
      TenderTotal: 'Total Tenders',
      TenderWon: 'Won Tenders',
      TenderLost: 'Lost Tenders',
      TenderPending: 'Pending Tenders',
      ProjectProgressChart: 'Project Progress',
      ProjectLastSixMonths: 'Last 6 Months',
      ProjectQuickActions: 'Quick Actions',
      ProjectCommonOperations: 'Common Operations',
      ProjectRecentProjects: 'Recent Projects',
      ProjectLastFiveProjects: 'Last 5 Projects',
      ProjectID: 'Project ID',
      ProjectName: 'Project Name',
      ProjectClient: 'Client',
      ProjectValue: 'Value',
      ProjectProgress: 'Progress',
      ProjectStatus: 'Status',
      TenderStatus: 'Tender Status',
      TenderCurrentStatus: 'Current Status',
      ProjectViewReport: 'View Report',
      ProjectViewDetails: 'View Details',
      ProjectViewAll: 'View All',
      ProjectRecentTenders: 'Recent Tenders',
      ProjectLastFiveTenders: 'Last 5 Tenders',
      TenderID: 'Tender ID',
      TenderName: 'Tender Name',
      ProjectHome: 'Home',

      // Maintenance Dashboard
      MaintenanceDashboard: 'Maintenance Dashboard',
      MaintenanceModule: 'Maintenance',
      MaintenanceHome: 'Home',
      MaintenanceTotalEquipment: 'Total Equipment',
      MaintenanceActiveEquipment: 'Active Equipment',
      MaintenanceNeedsMaintenance: 'Needs Maintenance',
      MaintenanceUnderMaintenance: 'Under Maintenance',
      MaintenanceTotalMalfunctions: 'Total Malfunctions',
      MaintenanceResolvedMalfunctions: 'Resolved Malfunctions',
      MaintenancePendingMalfunctions: 'Pending Malfunctions',
      MaintenanceCriticalMalfunctions: 'Critical Malfunctions',
      MaintenanceRequestsOverTime: 'Maintenance Requests Over Time',
      MaintenanceLastSixMonths: 'Last 6 Months',
      MaintenanceQuickActions: 'Quick Actions',
      MaintenanceCommonOperations: 'Common Operations',
      MaintenanceRecentMalfunctions: 'Recent Malfunctions',
      MaintenanceLastFiveMalfunctions: 'Last 5 Malfunctions',
      MaintenanceID: 'Malfunction ID',
      MaintenanceEquipment: 'Equipment',
      MaintenanceLocation: 'Location',
      MaintenanceType: 'Type',
      MaintenanceDate: 'Date',
      MaintenancePriority: 'Priority',
      MaintenanceStatus: 'Status',
      MaintenanceMalfunctionsByType: 'Malfunctions by Type',
      MaintenanceCurrentDistribution: 'Current Distribution',
      MaintenanceViewReport: 'View Report',
      MaintenanceViewDetails: 'View Details',
      MaintenanceViewAll: 'View All',
      MaintenanceRecentRequests: 'Recent Maintenance Requests',
      MaintenanceLastFiveRequests: 'Last 5 Requests',
      MaintenanceRequestID: 'Request ID',
      MaintenanceDepartment: 'Department',
      MaintenanceRequestedBy: 'Requested By',

      // Manufacturing Dashboard
      ManufacturingDashboard: 'Manufacturing Dashboard',
      ManufacturingModule: 'Manufacturing',
      ManufacturingHome: 'Home',
      ManufacturingTotalOrders: 'Total Manufacturing Orders',
      ManufacturingActiveOrders: 'Active Orders',
      ManufacturingCompletedOrders: 'Completed Orders',
      ManufacturingPendingOrders: 'Pending Orders',
      ManufacturingTotalProduction: 'Total Production',
      ManufacturingDailyProduction: 'Daily Production',
      ManufacturingWeeklyProduction: 'Weekly Production',
      ManufacturingMonthlyProduction: 'Monthly Production',
      ManufacturingProductionTrend: 'Production Trend',
      ManufacturingLastSixMonths: 'Last 6 Months',
      ManufacturingQuickActions: 'Quick Actions',
      ManufacturingCommonOperations: 'Common Operations',
      ManufacturingRecentOrders: 'Recent Manufacturing Orders',
      ManufacturingLastFiveOrders: 'Last 5 Orders',
      ManufacturingOrderID: 'Order ID',
      ManufacturingProduct: 'Product',
      ManufacturingQuantity: 'Quantity',
      ManufacturingStartDate: 'Start Date',
      ManufacturingDueDate: 'Due Date',
      ManufacturingProgress: 'Progress',
      ManufacturingStatus: 'Status',
      ManufacturingProductionByStage: 'Production by Stage',
      ManufacturingCurrentDistribution: 'Current Distribution',
      ManufacturingViewReport: 'View Report',
      ManufacturingViewDetails: 'View Details',
      ManufacturingViewAll: 'View All',
      ManufacturingRecentMaterialRequests: 'Recent Material Requests',
      ManufacturingLastFiveRequests: 'Last 5 Requests',
      ManufacturingRequestID: 'Request ID',
      ManufacturingMaterial: 'Material',
      ManufacturingUnit: 'Unit',
      ManufacturingRequestDate: 'Request Date',
      ManufacturingRequiredDate: 'Required Date',

      // Real Estate Dashboard
      RealEstateDashboard: 'Real Estate Dashboard',
      RealEstateModule: 'Real Estate',
      RealEstateHome: 'Home',
      RealEstateTotalProperties: 'Total Properties',
      RealEstateRentedProperties: 'Rented Properties',
      RealEstateAvailableProperties: 'Available Properties',
      RealEstateUnderMaintenanceProperties: 'Under Maintenance',
      RealEstateTotalContracts: 'Total Contracts',
      RealEstateActiveContracts: 'Active Contracts',
      RealEstateExpiredContracts: 'Expired Contracts',
      RealEstateExpiringContracts: 'Expiring Soon',
      RealEstateRevenueByMonth: 'Monthly Revenue',
      RealEstateLastSixMonths: 'Last 6 Months',
      RealEstateQuickActions: 'Quick Actions',
      RealEstateCommonOperations: 'Common Operations',
      RealEstateRecentContracts: 'Recent Contracts',
      RealEstateLastFiveContracts: 'Last 5 Contracts',
      RealEstateContractID: 'Contract ID',
      RealEstateProperty: 'Property',
      RealEstateTenant: 'Tenant',
      RealEstateStartDate: 'Start Date',
      RealEstateEndDate: 'End Date',
      RealEstateValue: 'Value',
      RealEstateStatus: 'Status',
      RealEstatePropertiesByType: 'Properties by Type',
      RealEstateCurrentDistribution: 'Current Distribution',
      RealEstateViewReport: 'View Report',
      RealEstateViewDetails: 'View Details',
      RealEstateViewAll: 'View All',
      RealEstateRecentPayments: 'Recent Payments',
      RealEstateLastFivePayments: 'Last 5 Payments',
      RealEstatePaymentID: 'Payment ID',
      RealEstateDate: 'Date',
      RealEstateAmount: 'Amount',
      RealEstateType: 'Type',

      // Fleet Dashboard
      FleetDashboard: 'Fleet Dashboard',
      FleetModule: 'Fleet',
      FleetHome: 'Home',
      FleetTotalVehicles: 'Total Vehicles',
      FleetActiveVehicles: 'Active Vehicles',
      FleetInactiveVehicles: 'Inactive Vehicles',
      FleetUnderMaintenanceVehicles: 'Under Maintenance',
      FleetTotalDrivers: 'Total Drivers',
      FleetActiveDrivers: 'Active Drivers',
      FleetOnLeaveDrivers: 'On Leave',
      FleetAvailableDrivers: 'Available',
      FleetFuelConsumption: 'Fuel Consumption',
      FleetLastSixMonths: 'Last 6 Months',
      FleetQuickActions: 'Quick Actions',
      FleetCommonOperations: 'Common Operations',
      FleetRecentMovements: 'Recent Vehicle Movements',
      FleetLastFiveMovements: 'Last 5 Movements',
      FleetMovementID: 'Movement ID',
      FleetVehicle: 'Vehicle',
      FleetDriver: 'Driver',
      FleetStartDate: 'Start Date',
      FleetEndDate: 'End Date',
      FleetDestination: 'Destination',
      FleetStatus: 'Status',
      FleetVehiclesByType: 'Vehicles by Type',
      FleetCurrentDistribution: 'Current Distribution',
      FleetViewReport: 'View Report',
      FleetViewDetails: 'View Details',
      FleetViewAll: 'View All',
      FleetRecentInvoices: 'Recent Invoices',
      FleetLastFiveInvoices: 'Last 5 Invoices',
      FleetInvoiceID: 'Invoice ID',
      FleetDate: 'Date',
      FleetAmount: 'Amount',
      FleetType: 'Type',

      // Staff Assistant Dashboard
      StaffAssistantDashboard: 'Staff Assistant Dashboard',
      StaffAssistantModule: 'Staff Assistant',
      StaffAssistantHome: 'Home',
      StaffAssistantTotalAttendanceDays: 'Total Attendance Days',
      StaffAssistantPresentDays: 'Present Days',
      StaffAssistantLateDays: 'Late Days',
      StaffAssistantAbsentDays: 'Absent Days',
      StaffAssistantTotalRequests: 'Total Requests',
      StaffAssistantApprovedRequests: 'Approved Requests',
      StaffAssistantPendingRequests: 'Pending Requests',
      StaffAssistantRejectedRequests: 'Rejected Requests',
      StaffAssistantAttendanceByMonth: 'Monthly Attendance',
      StaffAssistantLastSixMonths: 'Last 6 Months',
      StaffAssistantQuickActions: 'Quick Actions',
      StaffAssistantCommonOperations: 'Common Operations',
      StaffAssistantRecentRequests: 'Recent Requests',
      StaffAssistantLastFiveRequests: 'Last 5 Requests',
      StaffAssistantRequestID: 'Request ID',
      StaffAssistantRequestType: 'Request Type',
      StaffAssistantStartDate: 'Start Date',
      StaffAssistantEndDate: 'End Date',
      StaffAssistantStatus: 'Status',
      StaffAssistantRequestsByType: 'Requests by Type',
      StaffAssistantCurrentDistribution: 'Current Distribution',
      StaffAssistantViewReport: 'View Report',
      StaffAssistantViewDetails: 'View Details',
      StaffAssistantViewAll: 'View All',
      StaffAssistantRecentAttendance: 'Recent Attendance',
      StaffAssistantLastFiveAttendance: 'Last 5 Days',
      StaffAssistantDate: 'Date',
      StaffAssistantCheckIn: 'Check In Time',
      StaffAssistantCheckOut: 'Check Out Time',
      StaffAssistantCheckin: 'Check In',
      StaffAssistantCheckOut2: 'Check Out',
      StaffAssistantBreakout: 'Break Start',
      StaffAssistantBreakin: 'Break End',

      // Hotel Dashboard
      HotelDashboard: 'Hotel Dashboard',
      HotelModule: 'Hotel',
      HotelHome: 'Home',
      HotelTotalRooms: 'Total Rooms',
      HotelOccupiedRooms: 'Occupied Rooms',
      HotelAvailableRooms: 'Available Rooms',
      HotelUnderMaintenanceRooms: 'Under Maintenance',
      HotelTotalGuests: 'Total Guests',
      HotelCheckedInGuests: 'Checked In Guests',
      HotelCheckingOutToday: 'Checking Out Today',
      HotelReservationsToday: 'Today\'s Reservations',
      HotelOccupancyRate: 'Occupancy Rate',
      HotelLastSixMonths: 'Last 6 Months',
      HotelQuickActions: 'Quick Actions',
      HotelCommonOperations: 'Common Operations',
      HotelUpcomingReservations: 'Upcoming Reservations',
      HotelNextFiveReservations: 'Next 5 Reservations',
      HotelReservationID: 'Reservation ID',
      HotelGuest: 'Guest',
      HotelRoom: 'Room',
      HotelCheckIn: 'Check In',
      HotelCheckOut: 'Check Out',
      HotelStatus: 'Status',
      HotelRevenueByService: 'Revenue by Service',
      HotelCurrentDistribution: 'Current Distribution',
      HotelViewReport: 'View Report',
      HotelViewDetails: 'View Details',
      HotelViewAll: 'View All',
      HotelRecentInvoices: 'Recent Invoices',
      HotelLastFiveInvoices: 'Last 5 Invoices',
      HotelInvoiceID: 'Invoice ID',
      HotelDate: 'Date',
      HotelAmount: 'Amount',
      HotelType: 'Type',

      // General Dashboard
      GeneralDashboard: 'General Settings Dashboard',
      GeneralModule: 'General Settings',
      GeneralHome: 'Home',
      GeneralTotalCompanies: 'Total Companies',
      GeneralActiveBranches: 'Active Branches',
      GeneralTotalCurrencies: 'Total Currencies',
      GeneralTotalCountries: 'Total Countries',
      GeneralTotalUsers: 'Total Users',
      GeneralActiveUsers: 'Active Users',
      GeneralAdminUsers: 'Admin Users',
      GeneralSystemInfo: 'System Information',
      GeneralSystemDetails: 'System Details',
      GeneralSystemVersion: 'System Version',
      GeneralLastUpdate: 'Last Update',
      GeneralDatabaseSize: 'Database Size',
      GeneralUserActivity: 'User Activity',
      GeneralLastSixMonths: 'Last 6 Months',
      GeneralQuickActions: 'Quick Actions',
      GeneralCommonOperations: 'Common Operations',
      GeneralRecentLogins: 'Recent Logins',
      GeneralLastFiveLogins: 'Last 5 Logins',
      GeneralID: 'ID',
      GeneralUsername: 'Username',
      GeneralName: 'Name',
      GeneralDate: 'Date',
      GeneralIP: 'IP Address',
      GeneralStatus: 'Status',
      GeneralViewDetails: 'View Details',
      GeneralViewAll: 'View All',
      // Project Dashboard
      ProjectOverview: 'Project Overview',
      ProjectStats: 'Project Statistics',
      ActiveProjects: 'Active Projects',
      CompletedProjects: 'Completed Projects',
      ProjectProgressStatus: 'Project Progress',
      ProjectBudget: 'Project Budget',
      TeamPerformance: 'Team Performance',
      UpcomingDeadlines: 'Upcoming Deadlines',
      ProjectRisks: 'Project Risks',
      // Maintenance Dashboard
      MaintenanceOverview: 'Maintenance Overview',
      MaintenanceStats: 'Maintenance Statistics',
      PendingMaintenance: 'Pending Maintenance',
      CompletedMaintenance: 'Completed Maintenance',
      MaintenanceCosts: 'Maintenance Costs',
      EquipmentStatus: 'Equipment Status',
      MaintenanceSchedule: 'Maintenance Schedule',
      MaintenanceHistory: 'Maintenance History',
      // Manufacturing Dashboard
      ManufacturingOverview: 'Manufacturing Overview',
      ManufacturingStats: 'Manufacturing Statistics',
      ProductionOutput: 'Production Output',
      ProductionEfficiency: 'Production Efficiency',
      QualityMetrics: 'Quality Metrics',
      InventoryLevels: 'Inventory Levels',
      MachineryStatus: 'Machinery Status',
      ProductionSchedule: 'Production Schedule',
      // Real Estate Dashboard
      RealEstateOverview: 'Real Estate Overview',
      RealEstateStats: 'Real Estate Statistics',
      TotalProperties: 'Total Properties',
      OccupiedProperties: 'Occupied Properties',
      VacantProperties: 'Vacant Properties',
      RentalIncome: 'Rental Income',
      PropertyMaintenance: 'Property Maintenance',
      LeaseExpirations: 'Lease Expirations',
      PropertyValuation: 'Property Valuation',
      // Fleet Dashboard
      FleetOverview: 'Fleet Overview',
      FleetStats: 'Fleet Statistics',
      TotalVehicles: 'Total Vehicles',
      ActiveVehicles: 'Active Vehicles',
      FleetMaintenanceStatus: 'Maintenance Status',
      FuelConsumption: 'Fuel Consumption',
      VehicleUtilization: 'Vehicle Utilization',
      DriverPerformance: 'Driver Performance',
      FleetCosts: 'Fleet Costs',
      // Staff Assistant Dashboard
      StaffAssistantOverview: 'Staff Assistant Overview',
      StaffAssistantStats: 'Staff Assistant Statistics',
      PendingRequests: 'Pending Requests',
      CompletedRequests: 'Completed Requests',
      RequestCategories: 'Request Categories',
      ResponseTime: 'Response Time',
      StaffSatisfaction: 'Staff Satisfaction',
      CommonRequests: 'Common Requests',
      // POS Dashboard
      POSOverview: 'POS Overview',
      POSStats: 'POS Statistics',
      DailySales: 'Daily Sales',
      TopSellingItems: 'Top Selling Items',
      SalesByTerminal: 'Sales by Terminal',
      PaymentMethods: 'Payment Methods',
      CashierPerformance: 'Cashier Performance',
      PeakHours: 'Peak Hours',
      POSTerminal: 'POS Terminal',
      POSManagement: 'POS Management',
      SalesProcessing: 'Sales Processing',
      ProcessSales: 'Process Sales',
      OpenTerminal: 'Open Terminal',
      POSReports: 'POS Reports',
      SalesAnalytics: 'Sales Analytics',
      ViewReports: 'View Reports',
      POSSettings: 'POS Settings',
      ConfigureSystem: 'Configure System',
      SystemConfiguration: 'System Configuration',
      Configure: 'Configure',
      POSInventory: 'POS Inventory',
      StockManagement: 'Stock Management',
      ManageStock: 'Manage Stock',
      ManageInventory: 'Manage Inventory',
      POSTerminalPlaceholder: 'POS Terminal Placeholder',
      DesignWillBeAddedLater: 'Design will be added later',
      AlternativeAccess: 'Alternative Access',
      // POS Statistics
      AverageSales: 'Average Sales',
      TotalItems: 'Total Items',
      AverageTicket: 'Average Ticket',
      CustomerReturn: 'Customer Return',
      POSStatistics: 'Statistics',
      POSDaily: 'Daily',
      POSWeekly: 'Weekly',
      POSMonthly: 'Monthly',
      POSToday: 'Today',
      POSYesterday: 'Yesterday',
      POSThisWeek: 'This Week',
      POSCurrentMonth: 'Current Month',
      // POS Terminal
      Register: 'Register',
      Orders: 'Orders',
      SearchProducts: 'Search products...',
      Events: 'Events',
      Misc: 'Misc',
      Desks: 'Desks',
      Chairs: 'Chairs',
      CustomerTab: 'Customer',
      NoteTab: 'Note',
      QtyButton: 'Qty',
      PriceButton: 'Price',
      PaymentButton: 'Payment',
      TotalAmount: 'Total',
      // Hotel Dashboard
      HotelOverview: 'Hotel Overview',
      HotelStats: 'Hotel Statistics',
      Occupancy: 'Occupancy',
      AverageRoomRate: 'Average Room Rate',
      RevPAR: 'RevPAR',
      Reservations: 'Reservations',
      GuestSatisfaction: 'Guest Satisfaction',
      DepartmentPerformance: 'Department Performance',
      ForecastOccupancy: 'Forecast Occupancy',
      // Admin Dashboard
      AdminOverview: 'Admin Overview',
      AdminStats: 'Admin Statistics',
      SystemUsers: 'System Users',
      UserRoles: 'User Roles',
      SystemLogs: 'System Logs',
      BackupStatus: 'Backup Status',
      SystemResources: 'System Resources',
      SecurityAlerts: 'Security Alerts',
      SystemUpdates: 'System Updates',
      CommonOperations: 'Common Operations',
      ViewDetails: 'View Details',
      SystemModules: 'System Modules',
      ModulePerformance: 'Module Performance',
      TopActiveUsers: 'Top Active Users',
      UserActivity: 'User Activity',
      CONTRYNAMEEN: 'Name EN',
      COUNTRYNAME: 'Name AR',
      CODEID: 'Code',
      Code: 'Code',
      Update: 'Update',
      Day: 'Day',
      JobInTheCompany: 'Job In The Company',
      CareerInIdentity: 'Career in Identity',
      CareerInVisa: 'Career in Visa',
      SendSMS: 'Send SMS',
      SMSInstance: 'SMS Instance',
      ServiceProvider: 'Service Provider',
      Default: 'Default',
      UserName: 'User Name',
      Password: 'Password',
      SenderName: 'Sender Name',
      DefulatNumbers: 'Defulat Numbers',
      SalesTaxRegistrationNumber: 'Sales Tax Registration Number',
      TaxCardNumber: 'Tax card number',
      BuildingNumber: 'Building Number',
      Shift: 'Shift',
      FloorNumber: 'Floor Number',
      RoomNumber: 'Room Number',
      SaveChanges: 'Save Changes',
      Create: 'Create',
      Edit: 'Edit',
      Delete: 'Delete',
      Actions: 'Actions',
      id: 'id',
      Name: 'Name',
      field_1: 'Field 1',
      field_2: 'Field 2',
      field_3: 'Field 3',
      field_4: 'Field 4',
      field_5: 'Field 5',
      Template: 'Template',
      SMSTemplate: 'SMS Template',
      TemplateName: 'Template Name',
      RecipientType: 'Recipient Type',
      Balance: 'Balance',
      RecipientNumber: 'Recipient Number',
      Send: 'Send',
      SecondmentContract: 'SecondmentContract',
      FirstParty: 'First Party',
      CommercialRegistration: 'Commercial Registration',
      IssuedBy: 'Issued By',
      ClosePerson: 'Close Person',
      RepresentedBy: 'Represented By',
      SecondParty: 'Second Party',
      ResidenceText: 'Residence Text',
      Nationality: 'Nationality',
      IdIqama: 'Id-Iqama',
      Management: 'Management',
      EmployeeFixed: 'Employee Fixed',
      EmployeeChanged: 'Employee Changed',

      AuthorizationsIssued: 'Authorizations Issued',
      AvailableAuthorizations: 'Available authorizations',
      HoldsVisa: 'Holds Visa',
      NotHoldsVisa: 'Not Holds Visa',
      Office: 'Office',
      VisasNumber: 'Visas Number',
      VisasGranted: 'Visas granted',
      MainVisaNumber: 'Main Visa Number',
      ContractNumber: 'Contract Number',
      OrderDate: 'Order Date',
      SalarySuspension: 'Salary Suspension',
      CostValue: 'Cost value',
      SellingUnit: 'Selling Unit',

      BondNumber: 'Bond Number',
      AdvanceBalance: 'Advance Balance',
      PeriodicDiscountValue: 'Periodic discount value',
      CompanyFixed: 'Company Fixed',
      CompanyChanged: 'Company Changed',
      HousingAllowance: 'Housing Allowance',
      NameAr: 'Arabic Name',
      NameEn: 'English Name',
      DeductionDays: 'Deduction Days',
      InsurancesDueReports: 'Insurances Due Reports',
      Descriptions: 'Descriptions',
      JobResponsibilities: 'Job Responsibilities',
      JobConditions: 'Job Conditions',
      Type: 'Type',
      Phone: 'Phone',
      Mobile: 'Mobile',
      EmailAddress: 'Email',
      From: 'From',
      FromPlace: 'From Place',
      ToPlace: 'To Place',
      To: 'To',
      Missions: 'Missions',
      Itinerary: 'Itinerary',
      WorkSystem: 'Work System',
      WorkTasks: 'Work Tasks',
      Iqama: 'Iqama',
      Define: 'Define',
      AbsencePermissions: 'Absence Permissions',
      DownPayment: 'Down Payment',
      PayForOrder: 'Pay for an order',
      ExitAndReturn: 'Exit and Return',
      DomesticTravel: 'Domestic Travel',
      FromSalary: 'From Salary',
      MaximumAddition: 'Maximum Addition',
      IP: 'IP',
      GeneralManager: 'General Manager',
      CardNumber: ' Card Number',
      EstablishmentNumberInInterior: 'Establishment Number In Interior',
      ExpiryDateOfZakatAndIncomeCertificate:
        'ExpiryDate Of Zakat And Income Certificate',
      CommercialRegistrationNumber: 'Commercial Registration Number',
      Taxes: 'taxes',
      ZakatAndIncomeCertificateNumber: 'Zakat And Income Certificate Number',
      ZakatAndIncomeCertificateUniqueNumber:
        'Zakat And Income Certificate Unique Number',
      Fax: 'Fax',
      Street: 'Street',
      Zipcode: 'Zip code',
      Connect: 'Connect',
      QuarterDayDelay: 'Quarter Day Delay',
      HalfDayDelay: 'Half Day Delay',
      ThreeQuartersDayDelay: 'Three Quarters Day Delay',
      OneDayDelay: 'One Day Delay',
      GreaterThan: '>',

      DisplayActionFromMachine: 'Display Action From Machine',
      DownloadActionFromFile: 'Download Action From File',
      SaveAction: 'Save Action',
      DeleteActionFromMachine: 'Delete Action From Machine',
      Refresh: 'Refresh',
      MaximumDelayWithPermission: 'Maximum Delay With Permission',
      MaximumDelayAllowed: 'Maximum Delay Allowed',
      DelayBypass: 'Delay Bypass',
      Port: 'Port',
      Period: 'Period',
      SalarySystem: 'Salart System',
      Reset: 'Reset',
      NumOfDaysAweek: 'Number Of Days A Week',
      NumberOfDaysAMonth: 'Number Of Days A Month',
      PenaltyForDelayWithPermission: 'Penalty for delay with permission',
      WorkingHours: 'Working Hours',
      PenaltyDelayingHourWithoutPermission:
        'Penalty for delaying the hour without permission',
      hour: 'hour',
      OfficialHolidayForAllEmployeesDuringPeriod:
        'Determining The Official Holiday For All Employees During The Period',
      Year: 'Year',
      Month: 'Month',
      Previous: 'Previous',
      Limit: 'Limit',
      Currency: 'Currency',
      Procedures: 'Procedures',
      Contracts: 'Contracts',
      MainCustomer: 'Main Customer',
      CustomersType: 'Customers Type',
      CustomerLabel: 'Customer',
      LinkedSupplier: 'Linked Supplier',
      BorderNumber: 'Border Number',
      Scope: 'Scope',

      InvoiceDate: 'Invoice Date',
      ShipmentDate: 'Shipment Date',
      ShippingValue: 'Shipping Value',
      ShippingPoliceNumber: 'Shipping Police Number',
      AutoCounterAtExit: 'Auto Counter At Exit',
      BackTime: 'Back Time',
      CarCounterOnReturn: 'Car Counter On Return',

      Destination: 'Destination',
      LeaveTime: 'Leave Time',
      BasicData: 'Basic Data',
      TaxInformation: ' Tax Information',
      LicensesInformation: 'Licenses Information',
      HomePhone:"HomePhone",
      personalEmail:"personal Email",
      MovementType: 'Movement Type',
      Movement: 'Movement',
      Detaild: 'Detaild',
      UploadAttendance: 'Upload Attendance',
      View: 'View',
      Print: 'Print',
      FromManage: 'From Manage',
      ToManage: 'To Manage',
      Sex: 'Sex',
      Workplace: 'Workplace',
      Attendance: 'Attendance',
      AttendanceReport: 'Attendance Report',
      BreakOut: 'Break Out',
      BreakIn: 'Break In',
      Leave: 'Leave',
      DontShowBreviousBalance: 'Dont Show Previous Balance',
      CustomerId: 'CustomerId',
      ActionName: 'Action Name',
      ActionNo: 'ActionNo',
      Date: 'Date',
      Debit: 'Debit',
      Credit: 'Credit',
      Notes: 'Notes',
      OverTime: 'Over Time',
      EffectEmployeeStatus: 'Effect Employee Status',
      Depart: 'Depart Name',
      Account: 'Account',
      AccountType: 'Account Type',
      AccountTypeId: 'Account TypeId',
      ExportToExcel: 'Export To Excel',
      ImportFromExcel: 'Import From Excel',
      SendViaSMS: 'Send Via SMS',
      SendViaEmail: 'Send Via Email',
      SendViaWhatsapp: 'Send Via Whatsapp',
      Branch: 'Branch',
      SubordinateName: 'Subordinate Name',
      DocNo: 'DocNo',
      Acccode2: 'Linked Account Code',
      AccName: 'Account Name',
      AdditionToInsuranceDate: 'Addition To Insurance Date',
      WorkingHoursAndPenalties: 'Working Hours and Penalties',
      ResidenceNumber: 'Residence Number',
      Dependents: 'Dependents',
      EmployeeNameEn: 'Employee Name English',
      EmployeeNameAr: 'Employee Name Arabic',
      AddEmployee: 'Add Employee',
      Relationship: 'Relationship',
      InsuranceCategory: 'Insurance category',
      ShiftPlans: 'Shift Plans',
      DaysAndHours: 'Days and hours',
      EditShiftPlans: 'Edit Shift Plans',
      TimeAttendance: 'Time Attendance',
      DepartureTime: 'Departure Time',
      ReturnFromRest: 'Return from Rest',
      ExitRest: 'Exit Rest',
      PenaltyForAbsenceWithPermission: 'Penalty For Absence With Permission',
      PenaltyForAbsenceWithoutPermission:
        'Penalty For Absence Without Permission',
      Minute: 'Minute',

      MaritalStatus: 'Marital Status',
      StatusLabel: 'Status',
      AccName2: 'Linked Account Name',
      BankCode: 'Bank Code',
      JournalNO: 'Journal',
      NewJournalForAudit: 'New Journal For Audit',
      InsuranceSalary: 'Insurance Salary',
      SupplierType: 'Supplier Type',
      LinkedCustomer: 'Linked Customer',
      SupplierLabel: 'Supplier',
      Invcomno: 'Supplier Bill NO',
      InvoiceNo: 'Invoice Number',
      SupplierId: 'Supplier Id',
      ModelNo: 'Model Number',
      AssetName: 'Asset Name',
      AssetValue: 'Asset Value',
      NetAssetValue: 'Net Asset Value ',
      AssetCategory: 'Asset Category ',
      NewAssetsList: 'Add Asset ',
      AssetsList: 'Asset List',
      DepreciationList: 'Depreciation List',
      Assets: 'Assets ',
      DisposalType: 'Disposal Type',
      AssetDisposal: 'Asset Disposal',
      LastDepreciationDatefor: 'Last Depreciation Date for',
      DisposalDate: 'Disposal Date',
      AssetsStatement: 'Assets Statement',
      AssetsDepreciation: 'Assets Depreciation',
      DepreciationRate: 'Depreciation Rate',
      DepreciationMethod: 'Depreciation Method',
      SalvageValue: 'Salvage Value',
      DocumentsReports: 'Documents Reports',
      CaseName: 'Case Name',
      CaseId: 'CaseId',
      Costid: 'Analytics ID',
      CostName: 'Analytics Account',
      CostType: 'Cost Type',
      CostCenterCode: 'Cost Center Code',
      VatNo: 'VAT NO',
      Address: 'Address',
      WorkFrom: 'Work From',
      HijriDate: 'Hijri Date',
      GraduationYearDate: 'Graduation Year Date',
      GraduationYear: 'Graduation Year',
      CustodyReceipt: 'Custody Receipt',
      CustodyName: 'Custody Name',
      HealthInsurance: 'Health Insurance',
      DirectorSignature: 'Director Signature',
      Grade: 'Grade',
      ReasonForLeavingWork: 'Reason For Leaving Work',
      TemporaryForPeriod: 'Temporary For Period',
      InitialKnowledgeForPeriod: 'Initial Knowledge For Period',
      TrainingForPeriod: 'Training For Period',
      JobPreviously: 'Job Previously',
      GraduationDestination: 'Graduation Destination',
      ExperienceYears: 'Experience Years',
      LaborOfficeNumber: 'Labor Office Number',
      DeliveryRepresentative: 'Delivery Representative',
      ReferenceNumber: 'Reference Number',
      PurchaseDate: 'Purchase Date',
      CommissioningDate: 'Commissioning Date',
      LineManager: 'Line Manager',
      Allowances: 'Allowances',
      AllowancesAffectedByAbsence: 'Allowances affected by absence',
      AllowancesAffectingVacationExtract:
        'Allowances Affecting Vacation Extract',
      EndOfServiceAllowances: 'End-of-service allowances',
      AttachmentsAndDocuments: 'Attachments And Documents',
      DocumentType: 'Document Type',
      Procedure: 'Procedure',
      Commentator: 'Commentator',
      Certificate: 'Certificate',
      Resignation: 'Resignation',
      ReasonForResignation: 'Reason for resignation',
      Warning: 'Warning',
      InterruptionDate: 'Interruption Date',
      InterruptionDaysNumber: 'Interruption Days Number',
      Connected: 'Connected',
      Separated: 'Separated',
      Issuer: 'Issuer',
      IssueDate: 'Issue Date',
      StatusAndSignature: 'Status and Signature',
      UpdateYearsNumber: 'Update Years Number',
      DecadeBeginning: 'Decade Beginning',
      MemoType: 'Memo Type',
      MemoNumber: 'Memo Number',
      ContractStatus: 'Contract Status',
      OnAccount: 'On Account',
      AnnualInstallments: 'Annual Installments',
      ApprovedValueYearly: 'Approved Value Yearly ',
      StopExportingBank: 'Stop Exporting Bank',
      SuspensionOfVacations: 'Suspension Of Vacations',
      SuspensionOfResidenceRenewal: 'Suspension Of Residence Renewal',
      financial_entity_Type: 'Financial Entity Type',
      financial_entity_Id: 'Financial Entity Id',
      financial_entity: 'Financial Entity',
      CurrentSalary: 'Current Salary',
      SuggestedSalary: 'Suggested Salary',
      ActualValue: 'Actual value',
      Filter: 'Filter',
      Employees: 'Employees',
      Employee: 'Employee',
      SalaryInformation: 'Salary Information',
      EmployeeId: 'Employee Id',
      EmployeeName: 'Employee Name',
      BasicSalary: 'Basic Salary',
      ChangedSalary: 'Changed Salary',
      OtherAllowances: 'Other Allowances',
      FixedAllowance: 'Fixed Allowance',
      LocationAllowance: 'Location Allowance',
      SchoolAllowance: 'School Allowance',
      TelephoneAllowance: 'Telephone Allowance',
      TransportationAllowance: ' Teleportation Allowance',
      MealAllowance: 'Meal Allowance',
      Department: 'Department',
      Jop: 'Jop',
      JournalEntries: 'Journal Entries',
      Indirectcost: 'Indirect Cost',
      FromNo: 'From No',
      ToNo: 'To No',
      MissingNumbers: 'Missing Numbers',
      UnbalancedEntries: 'Unbalanced Entries',
      TransactionswithoutEntries: 'Transactions without Entries',
      TransactionswithoutInventoryImpact:
        'Transactions without Inventory Impact',
      MergingTransactions: 'Merging Transactions',
      FromWarehouse: 'From Warehouse',
      ToWarehouse: 'To Warehouse',
      Warehouse: 'Warehouse',
      Checkin: 'Check -In',
      CheckOut: 'Check-Out',
      Value: 'Value',
      TotalSalary: 'Total Salary',
      DeductionType: 'Deduction Type',
      MovementName: 'Movement Name',
      Serial: 'Serial',
      SerialNo: 'Serial Number',
      LaveDate2: 'Lave Date',
      WorkDayes: 'Work Dayes',
      Incentive: 'Incentive',
      WorkingDaysNumber: 'Working Days Number',
      LegalIssues: 'Legal Issues',
      AdministrativeDecision: 'Administrative Decision',
      Attention: 'Attention',
      AppointmentLetter: 'Appointment Letter',
      OutCompany: 'Out Company',
      InCompany: 'In Company',
      RequestType: 'Request Type',
      ReturnDateFromLastVacation: 'Return date from last vacation ',
      Reasons: 'Reasons',
      ServiceLength: 'Service Length',
      ProjectLocation: 'Project Location',
      CurrentLocation: 'Current Location',
      Location: 'Location',
      Accuracy: 'Accuracy',
      difference: 'Difference',
      breakout: 'Break-Out',
      breakin: 'Break -  In',
      Cut_Type: 'Cut_Type',
      Add_Type: 'Add_Type',
      HiringDate: 'Hiring Date',
      FileName: 'File Name',

      Attachments: 'Attachment',
      ShippingCompany: 'Shipping Company',
      ShipmentNumber: 'Shipment Number',
      Addressee: 'Addressee',
      MedicalInspectionLetter: 'Medical Inspection Letter',
      BedDate: 'Bed Date',
      ShippingAndCorrespondence: 'Shipping And Correspondence',
      FromDate: 'From Date',
      ToDate: 'To Date',
      EXPDate: 'Expiry Date',
      Driver: 'Driver',
      FilePath: 'File Path',
      DriverNo: 'Driver#',
      TravelsReports: 'Travels Reports',
      Salary: 'Salary',
      DueDays: 'Due Days',
      JobNumber: 'Job Number',
      Job: 'Job',
      Commissioning: ' Commissioning',
      Assignment: 'Assignment',
      CommissioningDepartment: 'Commissioning Department',
      ToHour: 'To Hour',
      FromHour: 'From Hour',
      DocumentaryCredits: 'Documentary Credits',

      WorkInformation: 'Work Information',
      IssuingResidence: 'Issuing Residence',
      TransferringSponsorship: 'Transferring Sponsorship',
      SaveShiftPlan: 'Save Shift Plan',
      FacilityName: 'Facility Name',
      InsurancePosition: 'Insurance Position',
      FacilityNumber: 'Facility Number',
      Owner: 'Owner',
      Jobposition: 'Job Position',
      HealthCertificateNumber: 'Health Certificate Number',
      SocialSecurityNumber: 'Social Security Number',
      InsurancePercentageDiscount: 'Insurance Percentage Discount',
      JoiningInsuranceDate: 'Joining Insurance Date',
      MedicalInsuranceNumber: 'Medical Insurance Number',
      EmployeeRank: 'Employee Rank',
      InsuranceValue: 'Insurance Value',
      MedicalInsuranceValue: 'Medical insurance value',
      RecordNumber: 'Record Number',
      MoneyData: 'Money Data',
      AdditionalInformation: 'Additional Information',
      CreditAccount: 'Credit Account',
      CovenantAccount: 'Covenant Account',
      NewContract: 'New Contract',
      CarryoverLeaveBalance: 'Carry-over leave balance',
      AnnualTicketsValue: 'Annual Tickets Value',
      AnnualTicketsNumbers: 'Annual Tickets Numbers',
      AdvanceHousingAllowance: 'Advance Housing Allowance',
      SalariesAccount: 'Salaries Account',
      SalaryPackage: 'Salary Packages',
      HealthCareDiscountRate: 'Health Care Discount Rate',
      EmployeeBankAccountNumber: 'Employee Bank Account Number',
      WorkersFundDiscountRatio: 'Workers Fund Discount Ratio',
      SalaryInInsurance: 'Salary In Insurance',
      VirtualCostCenter: 'Virtual Cost Center',
      ContractPeriod: 'Contract Period',
      ThirtyDaysVacationSystem: 'Thirty days vacation system',
      NatureOfWorkAllowance: 'Nature Of Work Allowance',
      OtherAllowance: 'Other Allowance',
      AbbreviatedName: 'Abbreviated Name',
      RelativesPhone: 'Relatives Phone',
      RelativesNames: 'Relatives Names',
      NumberOfChildren: 'Number of children',
      Mail: 'Mail',
      OtherInformation: 'Other Information',
      MilitarySituation: 'Military Situation',
      PreviousJobAddress: 'Previous Job Address',
      ReasonForLeavingJob: 'Reason For Leaving Job',
      JobLeavingDate: 'Job Leaving Date',
      BankName: 'Bank Name',
      Municipality: 'Municipality',
      Project: 'Project',
      Delivered: 'Delivered',
      Responsible: 'Responsible',
      Boss: 'Boss',
      Count: 'Count',
      PassportNumber: 'Passport Number',
      Rate: 'Rate',
      DateOfEntry: 'Date of entry into the country',
      OfficialDocuments: 'Official Documents',
      Nationalty: 'Nationalty',
      Time: 'Time',
      PreviousPermissions: 'Previous Permissions',
      DelayPermission: 'Delay Permissions',
      DelayPermitted: 'Delay Permitted',
      Sector: 'Sector',
      RewardType: 'Reward Type',
      Reward: 'Reward',
      Bisc_Salary: 'Bisc Salary',
      Allawonce: 'Allawonce',
      HourCalculatedBy: 'The hour is calculated by',
      Tickt: 'Tickt',
      ContractDuration: 'Contract Duration',
      LaveDate: 'Lave Date',
      WorkDays: 'Work Days',
      Net: 'Net',
      LaveDateH: 'Lave Date',
      MainAcc: 'Main Account',
      FromAcc: 'From Account',
      ToAcc: 'To Account',
      Number: 'Number',
      Penalty: 'Penalty',
      Medical: 'Medical',
      SalaryPayment: 'Salary Payment',
      PayableTo: 'Payable To',
      ReceivedFrom: 'Received From',
      Purpose: 'Purpose',
      Transfer: 'Transfer',
      Check: 'Check',
      TransactionNumber: 'Transaction Number',
      Sickness: 'Sickness',
      Loans: 'Loans',
      Custody: 'Custody',
      TotalEmp: ' Employee Share',
      OwnerValue: 'Owner Value',
      DrivingLicencesReports: 'Driving Licences Reports',
      LicencesNum: 'Licences Number',
      IssuingAuthority: 'Issuing Authority',
      LicencesDate: 'Licences Date',
      Services: 'Services',
      ProductLabel: 'Product',
      TheProduct: 'Product',
      Quantity: 'Quantity',
      All: 'All',
      Invoiced: 'Invoiced',
      PrintData: 'Print Data',
      Brief: 'Brief',
      Form: 'Form',
      HRForms: 'HR Forms',
      NotInvoiced: 'Not Invoiced',
      Guarantor: 'Guarantor',
      Reserved: 'Reserved',
      ProductCategories: 'Product Categories',
      ProductSubCategories: 'Product Sub Categories',
      PrintDate: 'Print Date',
      ExtraDiscountValue: 'Extra Discount Value',
      AnnualLeave: 'Annual Leave',
      InsuranceNumber: 'Insurance Number',
      OverallRating: 'Overall Rating',
      EmployeeEvaluation: 'Employee Evaluation',

      NewEmployeeEvaluation: 'New Employee Evaluation',
      SupplierEvaluation: 'Supplier Evaluation',
      Excellent: 'Excellent',
      VeryGood: 'Very Good',
      Good: 'Good ',
      Acceptable: 'Acceptable',
      Weak: 'Weak',
      RequiredNumber: 'Required Number ',
      Course: 'Course ',
      AddAssessmentItems: 'Add Assessment Items',
      RatingPercentage: 'Rating Percentage',
      EvaluationElement: 'Evaluation Element',
      Assessment: 'Assessment',
      TraineesNumber: 'Trainees Number',
      TrainingSubject: 'Training Subject',
      SuggestedDuration: 'Suggested Duration',
      AboutYear: 'About Year',
      EntityCode: 'EntityCode',
      EntityName: 'Entity Name',
      CompanySetting: 'Company Setting',
      City: 'City',
      InsuranceSubscriptionNumber: 'Insurance Subscription Number',

      MaintainWorkingHours: 'Maintain working hours',
      AmountOfWorkDone: 'Amount Of Work Done',
      ProfessionalCapacityDevelopment: 'Professional Capacity Development',
      PlanningAbility: 'Planning Ability',
      FollowSecurityRules: 'Follow Security Rules',
      SettingStandardsToMeasurePerformance:
        'Setting Standards To Measure Performance',
      TakingResponsibility: 'Taking Responsibility',
      ComplianceWithSystemAndRegulations:
        'Compliance With System And Regulations',
      SubordinateCommand: 'Subordinate Command',
      IncreaseInformation: 'Increase Information',
      Incoming: 'Incoming',
      Outgoing: 'Outgoing',

      DeductionFromSalary: 'Penalty',
      CostCenter: 'Analytics Account',
      costCenterGroup: 'Group',
      ShowZeroBalance: 'Showing Zero Balance',
      Show: 'Show',
      EntryType: 'Entry Type',
      NotificationType: 'Notification Type',
      ChequeBookNumber: 'Cheque Book Number',
      ChequeStatus: 'Cheque Status',
      CurrentCheckAccount: 'Current Check Account',
      PostSaveCheckAccount: 'Post-Save Check Account',
      CheckStatusPostDeletion: 'Check Status Post-Deletion',
      CheckNumber: 'Check Number',
      FromSeries: 'From Series',
      ToSeries: 'To Series',
      AllDeposits: 'All Deposits',
      ExtractDeposits: 'Extract Deposits',
      ReceiptTransactions: 'Receipt Transactions',
      BirthDate: 'Birth Date',
      VisaNumber: 'Visa Number',
      Design: 'Select Design',
      LatePermissions: 'Late Permissions',
      BenefitDeductionApplication: 'Benefit Deduction Application',
      About: 'About',
      SalaryAdditions: 'Salary Additions',
      SalaryDeduction: 'Salary Deduction',
      Cancel: 'Cancel',
      OfficialHolidayForParticularCategory:
        'Determining The Official Holiday For Particular Category',
      OfficialVacationPeriodForParticularCategory:
        'Determining The Official Vacation Period For Particular Category',
      ListName: 'List Name',
      SalaryGroup: 'Salary Group',
      DailyVacation: 'DailyVacation',
      Salesperson: 'Salesperson',
      Vacation: 'Vacation',
      CancelDelayOnThisDay: 'Cancel The Delay On This Day',
      OfficialHolidayForSpecificCategoryDuringPeriodOfTime:
        'Determining The Official Holiday For Specific Category During Period Of Time',
      PeriodInDays: 'Period In Days',
      OfficialHolidayForAllEmployees:
        'Determining The Official Holiday For All Employees',
      Unit: 'Unit',
      Barcode: 'Barcode',
      Price: 'Price',
      DeductionsReports: 'Deductions Reports',
      jobname: 'Job Name',
      DeductedFromEmployee: 'Total Deducted From Employee',
      BonusesAndIncentives: 'Bonuses And Incentives',
      VacationsReport: 'Vacations Report',
      Holidays: 'Holidays',
      Permissions: 'Permissions',
      TimeIn: 'Time In',
      empState: 'Employee State',
      TimeOut: 'Time Out',
      LateOutTime: 'Late Out Time',
      Late: 'Late',
      EmpWorkTime: 'Work Time',
      Company: 'Company',
      COUNTRY: 'Country',
      AccCode: 'Account Code',
      MissionsReports: 'Missions Reports',
      Summary: 'Total',
      ManagersReports: 'Managers Reports',
      AttendanceDays: 'Attendance Days',
      EmployeeLifeReports: 'Employee Life Reports',
      Others: 'Others',
      NationalityJobsReports: 'Nationality Jobs Reports',
      MovementsDuringPeriodReports: 'Movements During Period Reports',
      ProjectFrom: 'Project From',
      ProjectTo: 'Project To',
      TicketsNumber: 'Tickets Number',
      DateTravel: 'Date Travel',
      HRResponsible: 'HR Responsible',
      RequestDate: 'Request Date',
      HRSigned: 'HR Signed',
      RecruitedDuringPeriodReports: 'Recruited During Period Reports',
      DueSalariesReports: 'Due Salaries Reports',
      PlayoffsReports: 'Playoffs Reports',
      LastComBakVacation: 'Last Comeback from Vacation',
      LastWorkDay: 'Last Day Work',
      MonthlyDeductionsReports: 'Monthly Deductions Reports',
      Visa: 'Visa',
      StaffTransferBetweenProjects: 'Staff Transfer Between Projects',
      SalaryUpdate: 'Salary Update',
      UpdateResidenceDate: 'Update Residence Date',
      EmployeeRequests: 'Employee Requests',
      HousingAllowancePayments: 'Housing Allowance Payments',
      Ending: 'Ending',
      AnnualDues: 'Annual Dues',
      HandwrittenNote: 'Handwritten Note',
      StaffCosts: 'Staff Costs',
      SalariesPreparation: 'Salaries Preparation',
      PrintSalaries: 'Print Salaries',
      IncomeTAX: 'Income TAX',
      MonthlyIqamaCost: 'Monthly Iqama Cost',
      CarTraffic: 'Car Traffic',
      Trucking: 'Trucking',
      VisitorsRecord: 'Visitors Record',
      TenderItemNo: 'Tender ItemNo',
      TrainingNeeds: 'Training Needs',
      AnnualTrainingPlan: 'Annual Training Plan',
      EmployeeTrainingForm: 'Employee Training Form',
      EvaluateTrainingEffectiveness: 'Evaluate Training Effectiveness',
      TrainingEntities: 'Training Entities',
      AttendanceMachines: 'Attendance Machines',
      Insurances: 'Insurances',
      ShiftPage: 'Shift Page',
      Vacations: 'Vacations',
      AdditionsTypes: 'Additions Types',
      DeductionsTypes: 'Deductions Types',
      EmployeesCategories: 'Employees Categories',
      ShippingCompanies: 'Shipping Companies',
      DocumentsType: 'Documents Type',
      DocumentNumber: 'Document Number',
      SendElectronicInvoice: 'Electronic Invoice',
      NewServiceSalesTaxInvoice: 'Service Sales Tax Invoice',
      NewSalesTaxInvoice: 'Sales Tax Invoice',
      NewSalesTaxReturns: 'Sales Tax Returns Invoice',
      NewServiceSalesInvoiceReturns: 'Service Sales Invoice Returns',
      NewSalesReturns: 'Sales  Returns',
      NewSalesInvoice: 'Sales ',
      NewServiceSalesInvoice: 'Service  Sales ',
      NewExchangeRequest: 'Exchange Request',
      PaymentRequest: 'Payment Request',
      FinancialVerificationwithAttachments:
        'Financial Verification with Attachments',
      PaymentType: 'Payment Type',
      Beneficiary: 'Beneficiary',
      Regarding: 'Regarding',
      FileLocation: 'File Location',
      Visitor: 'Visitor',
      VisitNumber: 'Visit Number',
      VisitorsName: 'Visitors Name',
      VisitPurpose: 'Visit Purpose',
      CarNumber: 'Car Number',
      InTime: 'In Time',
      OutTime: 'Out Time',
      VisitDate: 'Visit Date',
      Tax: 'Tax',
      NonTax: 'Non Tax',
      NewProductsQuotations: 'Products Quotations',
      NewServicesQuotations: 'Services Quotations',
      SalesInvoiceNumber: 'Sales Invoice Number',
      PriceIncludingTax: 'Price Including Tax',
      PurchaseOrderNumber: 'Purchase Order Number',
      CustomerDepartmen: 'Customer Departmen',
      PresentedTo: 'Presented to',
      DeliveryTerms: 'Delivery Terms',
      DeliveryLocation: 'Delivery Location',
      ShippingMethod: 'Shipping Method',
      QuotationTerms: ' Quotation Terms',
      PassedBy: 'Passed By',
      SalesType: 'Sales Type',
      ProductCode: 'Product Code',
      UUID: 'UUID',
      EinvoiceStatues: 'Einvoice Statues',
      DueDate: 'Due Date',
      InvoiceLines: 'Invoice Lines',
      Sponsors: 'Sponsors',
      Qualifications: 'Qualifications',
      Qualification: 'Qualification',
      Specialization: 'Specialization',
      Religion: 'Religion',
      AnalyticAccountsLocation: 'Analytic Accounts Location',
      PostJournalEntry: 'Post Journal Entries',
      NotPostJournalEntry: 'Not Post Journal Entry',
      AggregationbyCostCenters: 'Aggregation by Cost Centers',
      AggregationbySubAccounts: 'Aggregation by Sub-Accounts',
      AggregationbyFinancialEntity: 'Aggregation by Financial Entity',
      OpeningEntry: 'Opening Entry',
      ClosingEntry: 'Closing Entry',
      EmployeAccountClassification: 'Account Classification',
      EgyptianElectronicInvoice: 'Egyptian Electronic Invoice',
      SaudiEInvoice: 'Saudi E-Invoice',
      CompanyLogo: 'Company Logo',
      Managers: 'Managers',
      AppBackground: 'App Background',
      EligibilityRate: 'Eligibility rate',
      DurationInDays: ' duration in days',
      Manager: 'Manager',
      NameInPrint: 'Name in print',
      Month30Days: 'Month 30 days ',
      EndOfService: 'End of service',
      DownloadDueSalaries: ' Download Due Salaries',
      ShowingCovenantAndSalafists: 'Showing Covenant And Salafists',
      DownloadSalaryOflastMonth: 'Download the salary of the last month',
      RequestingDepartment: 'Requesting Department',
      OrderTime: 'Order Time',
      RequestDirectedToUser: 'Request Directed To User',
      TenderDate: 'Tender Date',
      TenderNo: 'Tender Number',
      OrderRequestNo: 'Order Request Number',
      ResponsiblePerson: 'Responsible Person',
      ResponsiblePersons: 'Responsible Persons',
      PurchaseRequisition: 'Purchase Requisition',
      FollowUpRequisition: 'Follow Up Requisition',
      NumbeRofRows: 'NumbeR Of Rows ',
      AddSupplier: 'Add Supplier',
      TaxInfo: 'Tax Info',
      ShortName2: 'Short Name 2',
      AddressInfo: 'Address Info',
      LicenseType: 'License Type',
      LicenseNumber: 'License Number',
      TaxOffice: 'Tax Office',
      TaxFileNumber: 'Tax File Number',
      TaxCard: 'Tax Card',
      Governorate: 'Governorate',
      Tombstone2forAddress: 'tombstone 2 for Address',
      Tombstone1forAddress: 'tombstone 1 for Address',
      ClassificationCertificate: 'Classification Certificate',
      ClassificationExpiryDate: 'Classification Expiry Date',
      DefinedAttribute: 'Defined Attribute',
      AddProduct: 'Add Product',
      CommercialClassification: 'Commercial Classification',
      SellingPrices: 'Selling Prices',
      PriceLists: 'Price Lists',
      QuantityPrices: 'Quantity Prices',
      SalesPriceByWarehouse: 'Sales Price By Warehouse',
      Suppliers: 'Suppliers',
      ItemComponents: 'Item Components',
      ProductHistory: 'Product History',
      ItemProperties: 'Item Properties',
      AddBarcode: 'Add Barcode',
      SystemCodes: 'System Codes',
      Length: 'Length',
      Width: 'Width',
      Area: 'Area',
      Thickness: 'Thickness',
      Weight: 'Weight',
      NumberOfUnitsPerPackage: 'Number Of Units Per Package',
      StorageLocation: 'Storage Location',
      ReorderLimit: 'Reorder Limit',
      MaximumLimit: 'Maximum Limit',
      MinimumLimit: 'Minimum Limit',
      PrintNameAr: 'Print Name Ar',
      PrintNameEn: 'Print Name En',
      AlwaysInStock: 'Always In Stock',
      PrintBarcode: 'Print Barcode',
      StopDealingThisProduct: 'Stop Dealing This Product',
      BasicUnit: 'Basic Unit',
      FromBasicUnit: 'From Basic Unit',
      OldPrice: 'Old Price',
      SellingPrice: 'Selling Price',
      PurchasePrice: 'Purchase Price',
      MinimumPrice: 'Minimum Price',
      number: 'number',
      Bouns: 'Bouns',
      VirtualProfitMargin: 'Virtual Profit Margin',
      TaxCategory: 'Tax Category',
      ConsumptionRate: 'Consumption Rate',
      WastePercentage: 'Waste Percentage',
      OtherTax: 'Other Tax',
      PriceList: 'Price List',
      DiscountPercentage: 'Discount Percentage',
      QuantityFrom: 'Quantity From',
      QuantityTo: 'Quantity To',
      Manufacturer: 'Manufacturer',
      ManufacturerCode: 'Manufacturer Code',
      SupplierProductCode: 'Supplier Product Code',
      SupplierPrice: 'Supplier Price',
      SupplierBonus: 'Supplier Bonus',
      SupplierDiscount: 'Supplier Discount',
      OfferQuantity: 'Offer Quantity',
      ProductName: 'Product Name',
      ManufacturingTemplate: 'Manufacturing Template',
      Factor: 'Factor',
      VariantName: 'Variant Name',
      variantValue: 'variant Value',
      TaxType: 'Tax Type',
      Branch1: 'Branch 1',
      Branch2: 'Branch 2',
      Branch3: 'Branch 3',
      Branch4: 'Branch 4',
      Branch5: 'Branch 5',
      Branch6: 'Branch 6',
      Branch7: 'Branch 7',
      Branch8: 'Branch 8',
      Branch9: 'Branch 9',
      Branch10: 'Branch 10',
      CodeTypeGS1OrEGS: ' Code Type GS1 Or EGS',
      CodeGS1OrEGS: 'Code GS1 Or EGS',
      OtherInfo: 'Other Info',
      DataBaseName: 'Data Base Name',
      OpeningBalance: 'Opening Balance',
      PurchaseOrder: 'Purchase Order',
      Direct: 'Direct',
      FetchFromPurchaseRequest: 'Fetch From Purchase Request',
      FetchFromEnvelopeOpeningCommitteePurchaseRequestNo:
        'Fetch From Envelope Opening Committee Purchase Request No',

      Shipping: 'Shipping',
      DeliveryPlace: 'Delivery Place',
      PaymentTerms: ' Payment Terms',
      DeliveryDate: 'DeliveryDate',
      Total: 'Total',
      Necessity: 'Necessity',
      RFQ: 'Request for Quotation',
      Subject: 'Subject',
      Careof: 'Care of',
      SupplyDuration: 'Supply Duration',
      Days: 'Days',
      SearchInOffersFromDate: 'Search In Offers From Date',
      SearchByPurchaseOrderNumber: 'Search By Purchase Order Number',
      SearchByOfferNumber: 'Search By Offer Number',
      SelectionReasonForManager: 'Selection Reason For Manager',
      RFQComparisons: 'RFQ Comparisons',
      EntitlementDate: 'Entitlement Date',
      PurchaseType: 'Purchase Type',
      InvoiceType: 'Invoice Type',
      JournalName: 'Journal Name',
      PurchaseInvoice: 'Purchase Invoice',
      NewCashPayments: 'Cash Payments',
      NewBankPayments: 'Bank Payments',
      LettersOfGuaranteeTypes: 'Letters Of Guarantee Types',
      NewLettersOfGuaranteeTypes: 'New Letters Of Guarantee Types',
      LettersOfGuaranteeLimitations: 'Letters Of Guarantee Limitations',
      NewLettersOfGuaranteeLimitations: 'New Letters Of Guarantee Limitations',
      CashDepositPercentage: 'Cash Deposit Percentage',
      CashDeposit: ' Cash Deposit',
      GuaranteeLetterType: 'Guarantee Letter Type',
      LettersOfGuarantee: 'Letters Of Guarantee',
      NewLettersOfGuarantee: 'New Letters Of Guarantee',
      FacilityAmount: 'Facility Amount',
      ServiceBills: 'Service Invoice',
      DataImport: 'Data Import',
      Delivery: 'Delivery',
      Receipts: 'Receipts',
      ExtractionPermit: 'Extraction Permit',
      GuaranteeDate: 'Guarantee Date',
      ExpirationDate: 'Expiration Date ',
      GuaranteeValue: 'Guarantee Value',
      GuaranteePercentage: 'Guarantee Percentage',
      GuaranteeNumber: 'Guarantee Number',
      OriginalValue: 'Original Value',
      NewValue: 'New Value',
      Difference: 'Difference ',
      OriginalInsurance: 'Original Insurance',
      CashBoxesOpeningBalance: 'CashBoxesOpeningBalance',
      BanksOpeningBalance: 'Banks Opening Balance',
      CustomersOpeningBalance: 'Customers Opening Balance',
      EmployeesOpeningBalance: 'Employees Opening Balance',
      OpiningAndClosingJournals: 'Opining And Closing Journals',
      SupplierOpeningBalance: 'Supplier Opening Balance',
      Journal: 'Journal ',
      ProjectValueAmount: 'Project Value',
      ProjectType: 'Project Type',
      SalesReport:"Sales Report",
      InooiceNOVender:"Invoice No",
      BankAmount:"Bank Amount",
      DiscountCalculated:"Discount Calculated",
      ExtraMoney_Value:"Extra Money",
      SalesReturnReport:"Sales Return Report",
      generaltax:"General Tax",
      InvoicesReport:"Invoices Report",
      CollectedDay:"Collected Day",
      CollectedDate:"Collected Date",
      servicetype:"Service Type",
      Tahseel:"Collection Status",
      SuplayDate:"Supply Date",
      TahseelNote:"Collection Note",
      TahseelModa:"Collection Method",
      saltax:"Sales Tax",
      Isal_Vale:"Issuance Value",
      MonyStay:"Remaining Balance",
      Fright:"Fright",
      NewCustomer:"New Customer",
      SpecialAddress:"Special Address",
      PercentPrice:"Percent Price",
      QNet:"Quantity Net",
      QReturn:"Quantity Return",
      QSale:"Quantity Sales",
      SumReturn:"Sum Return",
      SumSale:"Sum Sale",
      percentQ:"Percent Quantity",
      UnAverageit:"Average",
      ProductSaleReport:"Product Sale Report",
      CustomerProductsReport:"Customer Products Report",
      RelativeName:"Relative Name",
      Kinship:"Kinship",
      RelativePhone:"Relative Phone",
      DiscountRate :" Discount Rate ",
      RegistryNumber:"Registry Number",
      January:"January",
      Febraury:"Febraury",
      March:"March",
      April:"April",
      May:"May",
      June:"June",
      July:"July",
      August:"August",
      September:"September",
      October:"October",
      November:"November",
      December:"December",
      SalespersonCustomerReport:"Salesperson Customer Report",
      ActivityCode:"Activity Code",
      Short_Name_Ar:"Short Code Arabic",
      governate:"Governate",
      Discount_percent:"Discount Percent",
      DiscountAccountName:"Discount Account Name",
      DiscountAccount:"Discount Account",
      DeductionsAccountName:"Deductions Account Name",
      InvoiceEarningsReport:"Invoice Earnings Report",
      Profit:"Profit",
      Indirect_Cost:"Indirect Cost",
      SalespersonCommissionsPercentageReport:"Salesperson Commissions Percentage Report",
      SalespersonCommissionsValueReport:"Salesperson Commissions Value Report",
      GreaterSales:"Greater Sales",
      Commission_Rate:"Commission Rate",
      Commission_Value:"Commission Value",
      TotalSalesReport:"Total Sales Report",
      TotalSalesBeforTax_AfterDec:"Total Sales Befor Tax After Discount",
      ValDec:"Value Discount",
      TotalReturnBefortax:"Total Return Before Tax",
      TotalReturnVAT:"Total Return VAT",
      TotalSalesBeforTax:"Total Sales Befor Tax",
      TotalVAT:"Total VAT",
      mardodat:"mardodat",
      valSales:"value Sales",
      DeliveryOrderReport:"Delivery Order Report",
      OrderNO:"OrderNO",
      SalesOrder:"Sales Order",
      NotCompleted:"Not Completed",
      Draft:"Draft",
      Done:"Done",
      AllSalesOrder:"All SalesOrder",
      AllOrders:"All Orders",
      ProfitPercentage:"Profit Percentage",
      AnnualSalesAnalysis:"Annual Sales Analysis",
      TAX_STATION:"Tax Station",
      ChangingGuaranteeValue: 'Changing Guarantee Value',
      NewChangingGuaranteeValue: 'New Changing Guarantee Value',
      LettersOfGuaranteeUsed: 'Letters Of Guarantee Used',
      NewLettersOfGuaranteeUsed: 'New Letters Of Guarantee Used',
      LettersOfGuaranteeBalance: 'Letters Of Guarantee Balance',
      RentReport:"Rent Report",
      QDiff:"Quantity Difference",
      Qbak:"Quantity Back",
      TotalAmountValue:"Total Amount",
      RentalDays:"Rental Days",
      LateTime:" Late Time",
      NetSalesReport:"Net Sales Report",
      TotalDiscounts:"Total Discounts",
      TotalSalse_And_Discounts:"Total Sales And Discounts",
      TotalReturns:'Total Returns',
      TotalDiscountsReturns:"Total Discounts Returns",
      TotalReturns_And_Discounts:"Total Returns And Discounts",
      SalesTaxReport:"Sales Tax Report",
      ContractsReport:"Contracts Report",
      RenewalWarrantyPeriod: 'Renewal Warranty Period',
      NewRenewalWarrantyPeriod: 'New Renewal Warranty Period',
      PurchaseStatement: 'Purchase Statement',
      BillsReturns: 'Invoice Returns',
      ReturnsServiceBills: 'Returns Service Invoice',
      PurchasesTypes: 'Purchases Types',
      SupplieresClassification: 'Supplieres Classification',
      ReceiptsRequest: 'Receipts Request',
      DeliveryRequest: 'Delivery Request',
      JobTitle:"Job Title",
      JournalItems: 'Journal Items',
      Administrator: 'Administrator',
      ImportfromSalesOrder: 'Import from Sales Order',
      Source: 'Source',
      PurchaseTaxInvoice: 'Purchase Tax Invoice',
      ManufacturingOrder: 'Manufacturing Order',
      PurchaseInvoiceNumber: 'Purchase Invoice Number',
      Order: 'Order',
      Transfers: 'Transfers',
      Statement: 'Statement',
      ManualTransferNo: 'Manual Transfer Number',
      PurchaseRequest: 'Purchase Request',
      DeliveryExecute: 'Delivery Execute',
      DeliveryNo: 'Delivery Number',
      InvoicesNotFullyDelivered: 'Invoices Not Fully Delivered',
      InvoicesThatHavebeenDelivered: 'Invoices That Have been Delivered',
      DeliveryNote: 'Delivery Note',
      SampleDescription: 'Sample Description',
      CustomersSamples: 'Customers Samples',
      ReceivingDate: 'Receiving Date',
      RecipientName: 'Recipient Name',
      CarNo: 'Car Number',
      EmployeesCustody: 'Employees Custody',
      HandingOvertoEmployee: 'Handing Over to Employee',
      ReceivingfromEmployee: 'Receiving from Employee',
      WithoutJournal: 'Without Journal',
      Recipient: 'Recipient',
      InventoryAdjustments: 'Inventory Adjustments',
      WithBalances: 'With Balances',
      DeductionsAdjustments: 'Deductions Adjustments',
      AdditionadJustments: 'Addition Adjustments',
      FetchfromInventoryReport: 'Fetch from Inventory Report',
      fetchfromNetConsumption: 'fetch from Net Consumption',
      UpdateWarehouse: 'Update Warehouse',
      UpdateProduct: 'Update Product',
      NetConsumer: 'Net Consumer',
      SetAvregeCost: 'Set Average Cost',
      Average: 'Average',
      AllWarehouses: 'All Warehouses',
      ProductsReport: 'Products Report',
      AccountCode: 'Account Code',
      ItemId: 'Product Id',
      UnitName: 'Unit Name',
      BonusReport: 'Bonus Report',
      ProfitabilityReport: 'Profitability Report',
      TradeType: 'Trade Type',
      ValuationType: 'Valuation Type',
      valuationReport: 'valuation Report',
      ProductCategoriesOnly: 'Product Categories Only',
      TotalvaluationReport: 'Total valuation Report',
      SrialReport: 'Srial Report',
      ByVariants: 'By Variants',
      ProductsBalanceReport: 'Products Balance Report',
      MovementByOperationsreReport: 'Movement By Operationsre Report',
      MovementByStoreReport: 'Movement By Store Report',
      ReplenishmentReport: 'Replenishment Report',
      TradeName: 'Trade Name',
      PriceOne: 'Price One',
      NumberOfDays: 'Number Of Days',
      StagnantproductsReport: 'Stagnant products Report',
      ProductsExpiringon: 'Products Expiring on',
      ExpirationDateReport: 'Expiration Date Report',
      ComponentCostReport: 'Component Cost Report',
      PriceControlReport: 'Price Control Report',
      ConsumptionReport: 'Consumption Report',
      OperationsControlReport: 'Operations Control Report',
      OperationsTypes: 'Operations Types',
      ProductAttributes: 'Product Attributes',
      ProductAttributesValues: 'Product Attributes Values',
      TradeClassification: 'Trade Classification',
      Manufacturers: 'Manufacturers',
      Units: 'Units',
      Bonus: 'Bonus',
      BarcodeGenerate: 'Barcode Generate',
      SupplierName: 'Supplier Name',
      TotalsProductsBalanceReport: 'Totals Products Balance Report',
      DocumentaryCreditsType: 'Documentary Credits Type',
      LetterOfGuarantee: 'Letter Of Guarantee',
      LetterOfGuaranteetype: 'Letter Of Guarantee type',
      Bank: 'Bank',
      FacilityValue: 'Facility Value',
      Commission: 'Commission',
      CashInsurance: 'Cash Insurance',
      DocumentaryCreditLimits: 'Documentary Credit Limits',
      CashInsurancePercentage: 'Cash Insurance Percentage',
      LetterofCreditType: 'Letter of Credit Type',
      LetterofCreditNumber: 'Letter Of Credit Number',
      LetterofCreditValue: 'Letter Of Credit Value',
      Transaction: 'Transaction',
      DocumentaryCreditsBalance: 'Documentary Credits Balance',
      RenewalAccreditationPeriod: 'Renewal Accreditation Period',
      ChangeCreditValue: 'Change Credit Value',
      NewInsuranceValue: 'New Insurance Value',
      LoanTypes: 'Loan Types',
      TransactionNo: 'Transaction Number',
      LargestUnit: 'Largest Unit',
      MiddleUnit: 'Middle Unit',
      SmallUnit: 'Small Unit',
      MiddleUnitQuantity: 'Middle Unit Quantity',
      SmallUnitQuantity: 'Small Unit Quantity',
      Cost: 'Cost',
      DefaultSellingPrice: 'Default Selling Price',
      SellingProfitMargin: 'Selling Profit Margin',
      MinPrice: 'Min Price',
      MinProfitMargin: 'Min Profit Margin',
      UnitCost: 'Unit Cost',
      ValItemTax: 'Val Item Tax',
      CostPrice: 'Cost Price',
      Taxable: 'Taxable',
      NonTaxable: 'Non-Taxable',
      Descount: 'Descount',
      PurchasesReport: 'Purchases Report',
      PurchasesReturnsReport: 'Purchases Returns Report',
      PurchasesTaxReport: 'Purchases Tax Report',
      SuppTypeID: 'Supplier Type ID',
      FileNO: 'File Number',
      ByNet: 'By Net',
      SuppliersAccountInMonthesReport: 'Suppliers Account In Monthes Report',
      PurchaseRequisitionsReport: 'Purchase Requisitions Report',
      PurchasOrderReport: 'Purchas Order Report',
      LandedCostReport: 'Landed Cost Report',
      InvoceStatus: 'Invoce Status',
      PurchaseOrdersPaymentsReport: 'Purchase Orders Payments Report',
      FollowUpBillsReport: 'Follow Up Bills Report',
      SuppliersProductsReport: 'Suppliers Products Report',
      itemSupId: 'item Sup Id',
      Supplierbonus: 'Supplier bonus',
      SupplierDescount: 'Supplier Descount',
      Valbr: 'Total Purchase After Tax',
      Returnes: 'Returnes',
      TotalReturnedVAT: 'Total Returnes VAT',
      TotalPurchaseVAT: 'Total Purchase VAT',
      TotalPurchaseBeforTaxAfterDec: 'Total Purchase Befor Tax After Descount',
      MardodatBeforeVAT: 'Returnes Before Tax',
      PurchasesPeriodicallyReport: 'Purchases Periodically Report',
      PurchaseBeforeTax: 'Purchase Before Tax',
      LoanLimits: 'Loan Limits',
      LoanType: 'Loan Type',
      AccountOperator:"Account Operator",
      IDIssueDate:"ID Issue Date",
      FixedPrice:"Fixed Price",
      DefaultSellingUnit:"Default Selling Unit",
      AnalyticAccountsGroups:"Analytic Accounts Groups",
      NewAnalyticAccountsGroups:" New Analytic Accounts Groups",
      GroupName:"Group Name",
      OrderType: 'Order Type',
      AnalyticAccountsDetailed:"Analytic Accounts Detailed",
      AnalyticAccountsTotal:"Analytic Accounts Total",
      AnalyticAccountsMonthly:"Analytic Accounts Monthly",
      AnalyticAccountsReport:"Analytic Accounts Report",
      IndirectCostRate:"Indirect Cost Rate",
      MonthlyNetConsumer:"Monthly Net Consumer",
      TotalConsumer:"Total Consumer",
      AnalyticAccountsPO:"Analytic Accounts PO",
      AnalyticAccountsList:"Analytic Accounts List",
      BankAccountsReport:"Bank Accounts Report",
      CustomerAccountsReport:"Customer Accounts Report",
      MandopPercent:"Mandop Percent",
      PayablesAccountsReport:"Payables Accounts Report",
      taxcard:"tax card",
      LedgerReport:"Ledger Report",
      estinationTypeName:"estination Type Name",
      StaffAccountsReport:"Staff Accounts Report",
      SalespersonAccountReport:"Salesperson Account Report",
      CashBoxAccountsReport:"Cash Box Accounts Report",
      PartnersAccountsReport:"Partners Accounts Report",
      MonthlyAccountReport:"Monthly Account Report",
      RenewalLoanPeriod:"Renewal Loan Period",
      LoanNumber:"Loan Number",
      LoansRepayment:"Loans Repayment",
      StartBalance:"Start Balance",
      PurchasedProductsReport:"Purchased Products Report",
      AverageCost:"Average Cost",
      LastPurshasCost:"Last Purshas Cost",
      QuantityPercentage:"Quantity Percentage",
      pricePercentage:"price Percentage",
      SalariesPrintReport:"Salaries Print Report",
      AccountsMirrorReport:"Accounts Mirror Report",
      SalesTaxesReport:"Sales Taxes Report",
      Details:"Details",
      EditValue:"Edit Value",
      TaxValue:"Tax Value",
      TotalValue:"Total Value",
      unbalcedJournals:"un balced Journals",
      missingJournals:"missing Journals",
      JournalEntryReport:"Journal Entry Report",
      AgedReceivableReport:"Aged Receivable Report",
      AgedPayableReport:"Aged Payable Report",
      GeneralLedgerReport:"General Ledger Report",
      includeZeroBalances:"include Zero Balances",
      groupByFinancialEntity:"group By Financial Entity",
      groupBySubAccount:"group By Sub Account",
      Advance:"Advance",
      StatementDate:"Statement Date",
      LoanValue:"Loan Value",
      StatementValue:"Statement Value",
      Duration:"Duration",
      StatementNumber:"Statement Number",
      DuratiFinancingPercentageon:"DuratiFinancing Percentageon",
      BorrowingCost:"Borrowing Cost",
      PaidAmount:"Paid Amount",
      LoanBalances:"Loan Balances",
      Greater_0:" From 0 to 30",
      Greater_30:" From 31 to 60",
      Greater_60:" From 61 to 90",
      Greater_90:" From 91 to 120",
      Greater_120:" From 121 to 150",
      Greater_150:" From 151 to 180",
      Greater_180:"More Than 180 Days",
      NotPostedTransactions:"Not Posted Transactions Report",
      PrepaidExpenses:"Prepaid Expenses Report",
      PaymentsAndReceiptReport:"Payments And Receipt Report",
      IndirectCostsSetting:"Indirect Costs Setting",
      TrialBalanceReport:"Trial Balance Report",
      BalanceCredit:"Balance Credit ",
      BeforDaen:"Previous Credit Balance",
      TrialBalanceLevelsReport:"Trial Balance Levels Report",
      FieldRequired:"Field Required",

      


    },
    SETTINGS: {
      UNITS: {
        TITLE: 'Measure Units',
        WEIGHT: {
          TITLE: 'Weight',
          DESC: 'Define your weight unit measure',
          KILOGRAMS: 'Kilograms',
          POUNDS: 'Pounds',
        },
        VOLUME: {
          TITLE: 'Volume',
          DESC: 'Define your volume unit measure',
          METER: 'Cubic Meters',
          FEET: 'Cubic Feet',
        },
      },
      PERMISSIONS: 'Permissions',
      PASSWORDRESET: 'Password Reset',
      PASSWORDRESETDESC: 'Enable Password Reset',
    },
    CUSTOMER: {
      TABS: {
        PERSONAL_INFO: 'Personal Info',
        ADDRESS: 'Address',
        OFFICIALS: 'Officials Data',
        SALES_PERSON: 'Sales Person',
        RELATIVES: 'Relatives',
      },
      TYPE: {
        INDIVIDUAL: 'Individual',
        COMPANY: 'Company',
      },
      ARABIC_NAME: 'Arabic Name',
      ENGLISH_NAME: 'English Name',
      TITLE: 'Title',
      CUSTOMER_CLASSIFIED: 'Customer Classified',
      MAIN_CUSTOMER: 'Main Customer',
      EMAIL: 'Email',
      SHORT_AR: 'Short (AR)',
      SHORT_EN: 'Short (EN)',
      PHONE: 'Phone',
      TEL: 'Tel',
      FAX: 'Fax',
      ACTIVITY: 'Activity',
      TAX_STATION: 'Tax Station',
      COLLECTION: 'Collection',
      CREDIT_LIMIT: 'Credit Limit',
      WEBSITE: 'Website',
      PAYMENT_METHOD: 'Payment Method',
      PRICE_LIST: 'Price List',
      WORKING_TIME: 'Working Time',
      NATIONALITY: 'Nationality',
      DURATION_OF_COLLECTION: 'Duration of Collection',
      OFFICIAL_HOLIDAY: 'Official Holiday',
      BIRTHDAY: 'Birthday',
      DISCOUNT_PERCENTAGE: 'Discount Percentage',
      NOTES: 'Notes',
      ACCOUNT_OPERATOR: 'Account Operator',
      ID_NUMBER: 'ID Number',
      WorkPhone:"Work Phone",
      ISSUER: 'Issuer',
      BANK_NAME: 'Bank Name',
      EXPIRATION_DATE: 'Expiration Date',
      ACCOUNT_NUMBER: 'Account Number',
      INCREASE_RATE: 'Increase Rate',
      VAT_NUMBER: 'VAT Number',
      COMMERCIAL_REGISTER: 'Commercial Register',
      TYPE_CODE: 'Type Code',
      ACTIVITY_CODE: 'Activity Code',
      Company: 'Company',
      COUNTRY: 'Country',
      REGION: 'Region',
      CITY: 'City',
      POSTAL_CODE: 'Postal Code',
      BRANCH_ENABLED: 'Branch Enabled',
      BRANCH: 'Branch',
      BUILDING_NUMBER: 'Building Number',
      FLOOR_NUMBER: 'Floor Number',
      OFFICE_NUMBER: 'Office Number',
      LANDMARK: 'Landmark',
      DISTRICT: 'District',
      STREET: 'Street',
      ADDITIONAL_INFORMATION: 'Additional Information',
      w_a_ins: 'Company Social Insurance Contribution',
      emp_tamensallery: 'Insurable Salary',
      emp_tamensalleryCanged: 'Variable Insurable Salary',
      emp_Take_Home: 'Housing Allowance',
      TotalSalleary: 'Total Salary',
      Paid: 'Paid Amount',
      TotalEmpShare: 'Total Employee Share',
      LastComBak: 'Last ComBak',
    },
    Salary: {
      emp_tamensallery: 'Insurance salary',
      ed_sahar: 'Basic Salary',
      elawa: 'Allowances',
      mobile: 'Total Salary',
      nafaa: 'Attendance',
      Day: 'Total Days',
      DayGyab: 'Absence Deduction',
      Tel: 'Due Basic Salary',
      b_ent: 'Transportation Allowance',
      others3: 'Meal Allowance',
      oth_sal: 'Other Salary',
      b_tab: 'Phone Allowance',
      hawafez: 'Housing Allowance',
      brnchs: 'Sector',
      b_malbas: 'Other Allowances',
      b_food: 'Site Allowance',
      takher: 'Additions',
      ed_agaza: 'Overtime',
      totalm: 'Total Earnings',
      moneytakher: 'Late Deduction',
      Temoneygyabl: 'Absence Deduction',
      moneygyab: 'Absence deduction',
      Badl_Agaza: 'Vacation Allowance',
      AdvancedPayment: 'Advance Deduction',
      sick: 'Sick Deduction',
      LastComBak: 'Last ComBack',
      gaza: 'Deductions',
      solfa_cut: 'Loan',
      tax: 'Income Tax',
      MedicalInsurance: 'Medical Insurance',
      ins: 'Employee Social Insurance Contribution',
      totalkh: 'Total Deductions',
      houraddagaza: 'Official Holidays',
      daygazaa: 'Leaves',
      safy: 'Net Salary',
      PaidForInsurance: 'Paid for Insurance',
      Take_Schole: 'School Allowance',
      Take_Percent: 'Percentage Allowance',
      BarCode: 'Employee ID',
      IqamaID: 'Residence ID',
      Count_Edafy_Hours: 'Overtime Hours',
      Kafeel: 'Sponsor',
      WorkPlan: 'Work Category',
      anohterDescount: 'Insurable Salary',
      w_a_ins: 'Company Social Insurance Contribution',
    },
  },
};
