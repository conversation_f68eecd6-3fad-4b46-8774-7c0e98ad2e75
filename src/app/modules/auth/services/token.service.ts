import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { TokenKeys } from '.';
import { AuthModel } from '../models/auth.model';

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private authLocalStorageToken = `${environment.appVersion}-${environment.USERDATA_KEY}`;
  isBrowser: boolean;
  shouldRefreshToken: BehaviorSubject<boolean>;
  constructor() {
    this.isBrowser = typeof window !== undefined;
    this.shouldRefreshToken = new BehaviorSubject<boolean>(false);
  }
  store(key: TokenKeys, value: any) {
    if (this.isBrowser) {
      localStorage.setItem(key, value);
    }
  }

  getAuthFromLocalStorage(): AuthModel | undefined {
    try {
      const lsValue = localStorage.getItem(this.authLocalStorageToken);
      if (!lsValue) {
        return undefined;
      }

      const authData = JSON.parse(lsValue);
      return authData;
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }
  get(key: TokenKeys): any {
    if (this.isBrowser) {
      const t = this.getAuthFromLocalStorage();
      return t ? t[`${key}`] : undefined;
    }
    return 0;
  }
  clear() {
    if (this.isBrowser) {
      localStorage.removeItem(this.authLocalStorageToken);
      // tslint:disable-next-line: forin
      for (const key in TokenKeys) {
        localStorage.removeItem(key);
      }
    }
  }
}
