import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Route, RouterStateSnapshot, Router, UrlSegment } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard  {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {
    console.log('AuthGuard initialized');
  }

  // Para canActivate - usado en app-routing.module.ts
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    console.log('AuthGuard.canActivate called for URL:', state.url);
    return this.checkAuth(state.url);
  }

  // Para canMatch - usado en pages/routing.ts
  canMatch(route: Route, segments: UrlSegment[]) {
    const url = '/' + segments.map(segment => segment.path).join('/');
    console.log('AuthGuard.canMatch called for URL:', url);
    return this.checkAuth(url);
  }

  // Método común para verificar la autenticación
  private checkAuth(url: string): boolean {
    console.log('AuthGuard.checkAuth called for URL:', url);
    const currentUser = this.authService.currentUserValue;
    console.log('Current user:', currentUser ? 'Logged in' : 'Not logged in');
    
    if (currentUser) {
      // Usuario autenticado, permitir acceso
      console.log('User is authenticated, allowing access');
      return true;
    }

    // No autenticado, redirigir a la página de login
    console.log('User is not authenticated, redirecting to login page');
    this.router.navigate(['/auth/login'], { 
      queryParams: { returnUrl: url }
    });
    return false;
  }
}
