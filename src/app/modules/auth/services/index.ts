import { ValidatorFn, FormGroup } from '@angular/forms';
export enum TokenKeys {
  access_Token = 'authToken',
  refresh_token = 'refreshToken',
  expiresIn = 'expiresIn',
  user = 'user',
  permissions = 'permissions',
  roles = 'roles'
}

export const compareValidator = (key1: string, key2: string) => {
  return (control: FormGroup): { [key: string]: any } | null => {
    const ctrl1 = control.get(key1);
    const ctrl2 = control.get(key2);
    return ctrl1?.value === ctrl2?.value ? null : { 'compare': false };
  };
};
