import { Injectable } from '@angular/core';
import { Route, Router, UrlSegment, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from './services/auth.service';
import { TokenService } from './services/token.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard {
  constructor(
    private router: Router,
    private tokenService: TokenService,
    private authService: AuthService) {
    console.log('Real AuthGuard initialized');
  }

  // Para rutas en app-routing.module.ts
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    console.log('Real AuthGuard.canActivate called, URL:', state.url);
    return this.checkAuth(state.url);
  }

  // Para rutas en pages/routing.ts
  canMatch(
    route: Route,
    segments: UrlSegment[]): Observable<boolean> | Promise<boolean> | boolean {
    const url = '/' + segments.map(segment => segment.path).join('/');
    console.log('Real AuthGuard.canMatch called, URL:', url);
    return this.checkAuth(url);
  }

  // Método común para verificar autenticación
  private checkAuth(url: string): boolean {
    console.log('Real AuthGuard checking authentication for URL:', url);
    const isAuthenticated = this.authService.isAuthenticated;
    console.log('Authentication status:', isAuthenticated ? 'Authenticated' : 'Not authenticated');

    if (isAuthenticated) {
      return true;
    }

    // Si no está autenticado, redirigir directamente a login
    console.log('Redirecting to login page');
    this.router.navigate(['/auth/login'], {
      queryParams: { returnUrl: url }
    });
    return false;
  }
}
