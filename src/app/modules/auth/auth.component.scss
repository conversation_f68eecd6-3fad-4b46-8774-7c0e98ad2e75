:host {
  height: 100vh;
  display: block;
  background: linear-gradient(135deg, #f5f8fa 0%, #e9ecef 100%);
}

// Main Auth Container
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(54, 153, 255, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(137, 80, 252, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

// Main Content Area
.auth-main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 1rem;
  position: relative;
  z-index: 1;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 120px); // احتساب ارتفاع التذييل
}

// Footer Section
.auth-footer {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
  border-top: 1px solid rgba(228, 230, 239, 0.5);
  padding: 2rem 1rem 1rem;
  text-align: center;
  position: relative;
  z-index: 1;

  .footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;

    .footer-link {
      color: #7E8299;
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: #3699FF;
        background: rgba(54, 153, 255, 0.1);
        transform: translateY(-1px);
      }
    }
  }

  .footer-copyright {
    p {
      margin: 0;
      color: #B5B5C3;
      font-size: 0.85rem;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .auth-container {
    min-height: 100vh;
  }

  .auth-main-content {
    padding: 1rem 0.5rem;
    min-height: calc(100vh - 100px);
  }

  .auth-footer {
    padding: 1.5rem 1rem 1rem;

    .footer-links {
      gap: 1rem;

      .footer-link {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .auth-main-content {
    padding: 0.5rem 0.25rem;
    min-height: calc(100vh - 80px);
  }

  .auth-footer {
    padding: 1rem 0.5rem 0.5rem;

    .footer-links {
      flex-direction: column;
      gap: 0.5rem;

      .footer-link {
        display: block;
        width: fit-content;
        margin: 0 auto;
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
      }
    }

    .footer-copyright p {
      font-size: 0.75rem;
    }
  }
}

// شاشات كبيرة جداً
@media (min-width: 1200px) {
  .auth-main-content {
    max-width: 500px;
  }
}

// شاشات صغيرة جداً
@media (max-width: 320px) {
  .auth-main-content {
    padding: 0.25rem;
    min-height: calc(100vh - 70px);
  }

  .auth-footer {
    padding: 0.75rem 0.25rem 0.25rem;

    .footer-links .footer-link {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .footer-copyright p {
      font-size: 0.7rem;
    }
  }
}
