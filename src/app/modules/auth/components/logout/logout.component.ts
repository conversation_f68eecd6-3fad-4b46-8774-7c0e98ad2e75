import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
    selector: 'app-logout',
    templateUrl: './logout.component.html',
    styleUrls: ['./logout.component.scss'],
    standalone: false
})
export class LogoutComponent implements OnInit {
  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Clear all tokens
    localStorage.removeItem('falcon-erp-authToken');
    this.authService.logout();

    // Redirect to home page after a short delay
    setTimeout(() => {
      this.router.navigate(['/']);
    }, 2000);
  }
}
