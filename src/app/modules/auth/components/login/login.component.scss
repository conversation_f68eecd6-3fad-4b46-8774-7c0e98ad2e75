:host {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  direction: rtl;
  padding: 1rem;
  box-sizing: border-box;

  @media (max-width: 576px) {
    padding: 0.5rem;
  }

  @media (max-width: 320px) {
    padding: 0.25rem;
  }
}

// Professional Login Card
.login-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  padding: 3rem 2.5rem;
  width: 100%;
  max-width: 480px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3699FF 0%, #8950FC 50%, #1BC5BD 100%);
  }

  // تأكد من عدم تجاوز حدود الشاشة
  @media (max-width: 768px) {
    margin: 0;
    padding: 2rem 1.5rem;
    border-radius: 16px;
    max-width: 100%;
  }

  @media (max-width: 576px) {
    padding: 1.5rem 1rem;
    border-radius: 12px;
  }

  @media (max-width: 320px) {
    padding: 1rem 0.75rem;
    border-radius: 8px;
  }
}

// Header Section
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .brand-logo {
    margin-bottom: 1.5rem;

    .logo-img {
      height: 60px;
      width: auto;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }
  }

  .login-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1E1E2D;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #3699FF 0%, #8950FC 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-subtitle {
    font-size: 1rem;
    color: #7E8299;
    margin: 0;
    font-weight: 500;
  }
}

// Form Styles
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
  display: flex;
  flex-direction: column; // ترتيب العناصر عمودياً
  width: 100%;

  // إضافة مسافة إضافية عند وجود رسائل خطأ
  &:has(.fv-plugins-message-container) {
    margin-bottom: 1.75rem;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: #3F4254;
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
  }

  .input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #E4E6EF;
    border-radius: 8px;
    transition: all 0.3s ease;
    overflow: hidden;
    width: 100%;
    height: 48px; // ارتفاع قياسي ومعقول

    &:hover {
      border-color: #B5B5C3;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    &:focus-within {
      border-color: #3699FF;
      box-shadow: 0 0 0 2px rgba(54, 153, 255, 0.08);
    }

    .input-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px; // عرض قياسي
      height: 100%;
      background: #f8f9fa;
      border-left: 1px solid #E4E6EF;
      flex-shrink: 0;

      i {
        color: #7E8299;
        font-size: 1rem;
        transition: color 0.3s ease;
      }
    }

    &:focus-within .input-icon i {
      color: #3699FF;
    }
  }

  .modern-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0 1rem; // حشو قياسي
    font-size: 1rem; // حجم خط قياسي
    background: transparent;
    color: #3F4254;
    font-weight: 400;
    height: 100%;
    line-height: 1.5;

    &::placeholder {
      color: #B5B5C3;
      font-weight: 400;
      font-size: 0.95rem;
    }

    &:focus {
      outline: none;
      box-shadow: none;
    }

    &.is-valid {
      background-image: none;
    }

    &.is-invalid {
      background-image: none;
    }
  }

  // Validation states
  &:has(.is-valid) .input-group {
    border-color: #1BC5BD;
    box-shadow: 0 0 0 2px rgba(27, 197, 189, 0.1);

    .input-icon {
      background: rgba(27, 197, 189, 0.05);

      i {
        color: #1BC5BD;
      }
    }
  }

  &:has(.is-invalid) .input-group {
    border-color: #F64E60;
    box-shadow: 0 0 0 2px rgba(246, 78, 96, 0.1);

    .input-icon {
      background: rgba(246, 78, 96, 0.05);

      i {
        color: #F64E60;
      }
    }
  }
}

// Alert Styles
.modern-alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-left: 4px solid #F64E60;

  .alert-icon {
    font-size: 1.25rem;
    color: #F64E60;
    margin-left: 0.75rem;
    flex-shrink: 0;
  }

  .alert-content {
    flex: 1;

    strong {
      display: block;
      color: #C53030;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    p {
      margin: 0;
      color: #E53E3E;
      font-size: 0.9rem;
    }
  }
}

// Forgot Password Link
.forgot-password-wrapper {
  text-align: left;
  margin-bottom: 2rem;

  .forgot-password-link {
    color: #3699FF;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;

    i {
      margin-left: 0.5rem;
      font-size: 0.8rem;
    }

    &:hover {
      color: #187DE4;
      text-decoration: none;
      transform: translateX(-2px);
    }
  }
}

// Submit Button
.submit-wrapper {
  margin-bottom: 2rem;

  .modern-btn {
    width: 100%;
    padding: 0.75rem 1.5rem; // حشو قياسي
    border: none;
    border-radius: 8px;
    font-size: 1rem; // حجم خط قياسي
    font-weight: 600;
    background: linear-gradient(135deg, #3699FF 0%, #187DE4 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 48px; // ارتفاع قياسي يتناسب مع الحقول

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 24px rgba(54, 153, 255, 0.3);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      background: #E4E6EF;
      color: #B5B5C3;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    .btn-content,
    .btn-loading {
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        margin-left: 0.5rem;
        font-size: 0.9rem;
      }
    }

    .btn-loading i {
      animation: spin 1s linear infinite;
    }
  }
}

// Footer Section
.login-footer {
  text-align: center;

  .footer-divider {
    position: relative;
    margin: 1.5rem 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #E4E6EF 50%, transparent 100%);
    }

    span {
      background: #ffffff;
      color: #B5B5C3;
      padding: 0 1rem;
      font-size: 0.9rem;
      font-weight: 500;
      position: relative;
      z-index: 1;
    }
  }

  .footer-links {
    .footer-link {
      color: #7E8299;
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      transition: all 0.3s ease;

      i {
        margin-left: 0.5rem;
        font-size: 0.8rem;
      }

      &:hover {
        color: #3699FF;
        text-decoration: none;
        transform: translateY(-1px);
      }
    }
  }
}

// Error Messages
.fv-plugins-message-container {
  margin-top: 0.5rem;
  margin-bottom: 0;
  width: 100%; // عرض كامل
  display: block; // تأكد من أنها تظهر كعنصر منفصل

  span {
    color: #F64E60;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    background: rgba(246, 78, 96, 0.05);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border-right: 3px solid #F64E60;
    animation: slideInDown 0.3s ease-out;
    width: 100%; // عرض كامل للرسالة
    box-sizing: border-box;

    &::before {
      content: '⚠';
      margin-left: 0.5rem;
      font-size: 0.875rem;
      color: #F64E60;
      flex-shrink: 0; // منع تقليص الأيقونة
    }
  }
}

// Animation for error messages
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Animation classes
.login-card {
  animation: fadeInUp 0.6s ease-out;
}

// Responsive Design
@media (max-width: 768px) {
  .login-card {
    margin: 1rem 0.5rem;
    padding: 2rem 1.5rem;
    max-width: 100%;

    .login-header {
      margin-bottom: 2rem;

      .logo-img {
        height: 50px;
      }

      .login-title {
        font-size: 1.75rem;
      }

      .login-subtitle {
        font-size: 0.95rem;
      }
    }

    .form-group {
      margin-bottom: 1.25rem;

      .input-group {
        height: 52px; // ارتفاع مناسب للتابلت

        .input-icon {
          width: 52px;
        }

        .modern-input {
          padding: 0 1.125rem;
          font-size: 1.05rem;
        }
      }
    }

    .modern-btn {
      padding: 0.875rem 1.5rem;
      font-size: 1.05rem;
      height: 52px;
    }
  }
}

@media (max-width: 576px) {
  .login-card {
    margin: 0.5rem 0.25rem;
    padding: 1.5rem 1rem;
    border-radius: 16px;

    .login-header {
      margin-bottom: 1.5rem;

      .logo-img {
        height: 45px;
      }

      .login-title {
        font-size: 1.5rem;
      }

      .login-subtitle {
        font-size: 0.9rem;
      }
    }

    .form-group {
      margin-bottom: 1rem;

      .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
      }

      .input-group {
        height: 50px; // ارتفاع مناسب للهواتف

        .input-icon {
          width: 50px;

          i {
            font-size: 1rem;
          }
        }

        .modern-input {
          padding: 0 1rem;
          font-size: 1rem;
        }
      }
    }

    .modern-btn {
      padding: 0.75rem 1.25rem;
      font-size: 1rem;
      height: 50px;
    }

    .forgot-password-wrapper {
      margin-bottom: 1.5rem;

      .forgot-password-link {
        font-size: 0.85rem;
      }
    }

    .login-footer {
      .footer-divider {
        margin: 1rem 0;

        span {
          font-size: 0.85rem;
        }
      }

      .footer-links .footer-link {
        font-size: 0.85rem;
      }
    }
  }
}

@media (max-width: 320px) {
  .login-card {
    margin: 0.25rem;
    padding: 1rem 0.75rem;

    .login-header {
      margin-bottom: 1rem;

      .logo-img {
        height: 40px;
      }

      .login-title {
        font-size: 1.25rem;
      }

      .login-subtitle {
        font-size: 0.85rem;
      }
    }

    .form-group {
      margin-bottom: 0.875rem;

      .form-label {
        font-size: 0.85rem;
      }

      .input-group {
        min-height: 45px; // ارتفاع للشاشات الصغيرة جداً

        .input-icon {
          width: 45px;

          i {
            font-size: 0.9rem;
          }
        }

        .modern-input {
          padding: 0 0.875rem;
          font-size: 0.95rem;
        }
      }
    }

    .modern-btn {
      padding: 0.625rem 1rem;
      font-size: 0.95rem;
      height: 45px;
    }

    .forgot-password-wrapper {
      margin-bottom: 1.25rem;

      .forgot-password-link {
        font-size: 0.8rem;
      }
    }
  }
}

// شاشات كبيرة جداً
@media (min-width: 1200px) {
  .login-card {
    max-width: 450px;
    padding: 3.5rem 3rem;

    .login-header {
      margin-bottom: 3rem;

      .logo-img {
        height: 70px;
      }

      .login-title {
        font-size: 2.25rem;
      }

      .login-subtitle {
        font-size: 1.1rem;
      }
    }

    .form-group {
      margin-bottom: 2rem;

      .input-group {
        height: 56px; // ارتفاع مناسب للشاشات الكبيرة

        .input-icon {
          width: 56px;

          i {
            font-size: 1.1rem;
          }
        }

        .modern-input {
          padding: 0 1.25rem;
          font-size: 1.1rem;
        }
      }
    }

    .modern-btn {
      padding: 1rem 2rem;
      font-size: 1.1rem;
      height: 56px;
    }
  }
}
