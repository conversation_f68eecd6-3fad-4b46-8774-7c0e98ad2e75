<!--begin::Professional Login Card-->
<div class="login-card">
  <!--begin::Card Header-->
  <div class="login-header">
    <div class="brand-logo">
      <img src="./assets/media/logos/falcon-logo.png" alt="Falcon ERP" class="logo-img" />
    </div>
    <h1 class="login-title">
      مرحباً بك
    </h1>
    <p class="login-subtitle">
      سجل دخولك للوصول إلى نظام فالكون ERP
    </p>
  </div>
  <!--end::Card Header-->

  <!--begin::Form-->
  <form class="login-form" [formGroup]="loginForm" novalidate="novalidate" id="kt_login_signin_form"
    (ngSubmit)="submit()">

    <!-- begin::Alert error-->
    <ng-container *ngIf="hasError">
      <div class="alert alert-danger modern-alert">
        <i class="fas fa-exclamation-triangle alert-icon"></i>
        <div class="alert-content">
          <strong>خطأ في تسجيل الدخول</strong>
          <p>البريد الإلكتروني أو كلمة المرور غير صحيحة</p>
        </div>
      </div>
    </ng-container>
    <!-- end::Alert error-->

    <!--begin::Email Field-->
    <div class="form-group">
      <label class="form-label">البريد الإلكتروني</label>
      <div class="input-group">
        <div class="input-icon">
          <i class="fas fa-envelope"></i>
        </div>
        <input
          class="form-control modern-input"
          type="email"
          name="email"
          formControlName="email"
          autocomplete="off"
          placeholder="أدخل بريدك الإلكتروني"
          [ngClass]="{
            'is-invalid': loginForm.controls['email'].invalid && (loginForm.controls['email'].dirty || loginForm.controls['email'].touched),
            'is-valid': loginForm.controls['email'].valid && (loginForm.controls['email'].dirty || loginForm.controls['email'].touched)
          }" />
      </div>
      <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
          validation: 'required',
          message: 'البريد الإلكتروني مطلوب',
          control: loginForm.controls['email']
        }"></ng-container>
      <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
          validation: 'email',
          message: 'البريد الإلكتروني غير صحيح',
          control: loginForm.controls['email']
        }"></ng-container>
    </div>
    <!--end::Email Field-->

    <!--begin::Password Field-->
    <div class="form-group">
      <label class="form-label">كلمة المرور</label>
      <div class="input-group">
        <div class="input-icon">
          <i class="fas fa-lock"></i>
        </div>
        <input
          class="form-control modern-input"
          dir="ltr"
          type="password"
          name="password"
          autocomplete="off"
          formControlName="password"
          placeholder="أدخل كلمة المرور"
          [ngClass]="{
            'is-invalid': loginForm.controls['password'].invalid && (loginForm.controls['password'].dirty || loginForm.controls['password'].touched),
            'is-valid': loginForm.controls['password'].valid && (loginForm.controls['password'].dirty || loginForm.controls['password'].touched)
          }" />
      </div>
      <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
          validation: 'required',
          message: 'كلمة المرور مطلوبة',
          control: loginForm.controls['password']
        }"></ng-container>
      <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
          validation: 'minlength',
          message: 'يجب أن تحتوي كلمة المرور على 3 أحرف على الأقل',
          control: loginForm.controls['password']
        }"></ng-container>
    </div>
    <!--end::Password Field-->

    <!--begin::Forgot Password Link-->
    <div class="forgot-password-wrapper">
      <a routerLink="/auth/forgot-password" class="forgot-password-link">
        <i class="fas fa-question-circle"></i>
        نسيت كلمة المرور؟
      </a>
    </div>
    <!--end::Forgot Password Link-->

    <!--begin::Submit Button-->
    <div class="submit-wrapper">
      <button type="submit" class="btn btn-primary modern-btn" [disabled]="loginForm.invalid">
        <ng-container *ngIf="isLoading$ | async">
          <span class="btn-loading">
            <i class="fas fa-spinner fa-spin"></i>
            جاري تسجيل الدخول...
          </span>
        </ng-container>
        <ng-container *ngIf="(isLoading$ | async) === false">
          <span class="btn-content">
            <i class="fas fa-sign-in-alt"></i>
            تسجيل الدخول
          </span>
        </ng-container>
      </button>
    </div>
    <!--end::Submit Button-->

    <!--begin::Footer Links-->
    <div class="login-footer">
      <div class="footer-divider">
        <span>أو</span>
      </div>
      <div class="footer-links">
        <a routerLink="/web" class="footer-link">
          <i class="fas fa-home"></i>
          العودة إلى الموقع الرئيسي
        </a>
      </div>
    </div>
    <!--end::Footer Links-->

  </form>
  <!--end::Form-->
</div>
<!--end::Professional Login Card-->

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
  <ng-container *ngIf="control.hasError(validation) && (control.dirty || control.touched)">
    <div class="fv-plugins-message-container">
      <span role="alert">
        {{ message }}
      </span>
    </div>
  </ng-container>
</ng-template>
