import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ProductsCacheService {
  private cachedProducts: any[] = [];
  private lastCacheTime: Date | null = null;
  private cacheExpiryMinutes = 30; // تحديد وقت انتهاء صلاحية التخزين المؤقت
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  // الحصول على المنتجات من التخزين المؤقت أو من الخادم
  getProducts(forceRefresh = false): Observable<any[]> {
    // التحقق مما إذا كان التخزين المؤقت صالحًا
    if (this.isCacheValid() && !forceRefresh) {
      console.log('استخدام البيانات المخزنة مؤقتاً:', this.cachedProducts.length, 'منتج');
      return of(this.cachedProducts);
    }

    // تحميل المنتجات من الخادم
    this.loadingSubject.next(true);
    return this.fetchProductsFromServer().pipe(
      tap(products => {
        this.cachedProducts = products;
        this.lastCacheTime = new Date();
        this.loadingSubject.next(false);
        console.log('تم تحديث البيانات المخزنة مؤقتاً:', products.length, 'منتج');
      }),
      catchError(error => {
        this.loadingSubject.next(false);
        console.error('خطأ في تحميل المنتجات:', error);
        // إذا كان لدينا بيانات مخزنة مؤقتاً، فاستخدمها في حالة حدوث خطأ
        if (this.cachedProducts.length > 0) {
          return of(this.cachedProducts);
        }
        throw error;
      })
    );
  }

  // البحث في المنتجات المخزنة مؤقتًا
  searchCachedProducts(searchTerm: string): any[] {
    if (!this.cachedProducts.length) return [];
    
    if (!searchTerm) {
      return this.cachedProducts;
    }
    
    searchTerm = searchTerm.toLowerCase();
    return this.cachedProducts.filter(product => 
      (product.name && product.name.toLowerCase().includes(searchTerm)) || 
      (product.code && product.code.toLowerCase().includes(searchTerm))
    );
  }

  // تحميل المنتجات من الخادم
  private fetchProductsFromServer(): Observable<any[]> {
    // استخدام نفس مسار API المستخدم في الخدمة الأصلية
    const url = 'api/Product/GetProductList'; 
    return this.http.get<any>(url).pipe(
      map(response => {
        if (response && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  // التحقق من صلاحية التخزين المؤقت
  private isCacheValid(): boolean {
    if (!this.lastCacheTime || !this.cachedProducts.length) {
      return false;
    }

    const now = new Date();
    const diffMs = now.getTime() - this.lastCacheTime.getTime();
    const diffMins = Math.round(diffMs / 60000);
    
    return diffMins < this.cacheExpiryMinutes;
  }

  // تنظيف التخزين المؤقت
  clearCache(): void {
    this.cachedProducts = [];
    this.lastCacheTime = null;
  }
}
