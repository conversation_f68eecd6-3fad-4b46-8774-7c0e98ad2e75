import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-customer-login',
  templateUrl: './customer-login.component.html',
  styleUrls: ['./customer-login.component.scss'],
  standalone: false
})
export class CustomerLoginComponent implements OnInit {

  loginForm: FormGroup;
  isLoading = false;
  showPassword = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private meta: Meta,
    private title: Title
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    this.setSEOData();
  }

  private setSEOData(): void {
    this.title.setTitle('تسجيل الدخول - بورتال العملاء | فالكون ERP');
    
    this.meta.updateTag({
      name: 'description',
      content: 'تسجيل الدخول إلى بورتال العملاء الخاص بفالكون ERP لإدارة عروض الأسعار والفواتير والدعم الفني.'
    });

    this.meta.updateTag({
      name: 'robots',
      content: 'noindex, nofollow'
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit(): void {
    if (this.loginForm.valid && !this.isLoading) {
      this.isLoading = true;
      this.errorMessage = '';

      const { email, password, rememberMe } = this.loginForm.value;

      // TODO: Replace with actual API call
      setTimeout(() => {
        if (email === '<EMAIL>' && password === '123456') {
          // Mock successful login
          localStorage.setItem('customer_token', 'mock_token_123');
          localStorage.setItem('customer_info', JSON.stringify({
            id: 1,
            companyName: 'شركة الأمل للتجارة',
            contactPerson: 'أحمد محمد علي',
            email: email,
            accountNumber: 'ACC-2024-001'
          }));

          this.router.navigate(['/portal']);
        } else {
          this.errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
        this.isLoading = false;
      }, 1500);
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
      if (field.errors['email']) {
        return 'البريد الإلكتروني غير صحيح';
      }
      if (field.errors['minlength']) {
        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }
    }
    return '';
  }

  private getRequiredMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      email: 'البريد الإلكتروني مطلوب',
      password: 'كلمة المرور مطلوبة'
    };
    return messages[fieldName] || 'هذا الحقل مطلوب';
  }

  hasFieldError(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field?.errors && (field.dirty || field.touched));
  }
}
