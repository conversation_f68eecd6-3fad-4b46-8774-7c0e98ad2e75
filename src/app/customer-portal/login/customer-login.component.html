<!-- Customer Login Page -->
<div class="customer-login-page">
  
  <!-- Background Elements -->
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>

  <!-- Login Container -->
  <div class="login-container">
    
    <!-- Logo Section -->
    <div class="logo-section">
      <img src="assets/media/logos/falconerp.png" alt="Falcon ERP" class="logo">
      <h1>بورتال العملاء</h1>
      <p>مرحباً بك في بورتال العملاء الخاص بفالكون ERP</p>
    </div>

    <!-- Login Card -->
    <div class="login-card">
      
      <!-- Card Header -->
      <div class="card-header">
        <h2>تسجيل الدخول</h2>
        <p>أدخل بيانات حسابك للوصول إلى البورتال</p>
      </div>

      <!-- Error Message -->
      <div class="error-message" *ngIf="errorMessage">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <!-- Login Form -->
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        
        <!-- Email Field -->
        <div class="form-group">
          <label for="email">البريد الإلكتروني</label>
          <div class="input-wrapper" [class.error]="hasFieldError('email')">
            <i class="fas fa-envelope input-icon"></i>
            <input
              type="email"
              id="email"
              formControlName="email"
              placeholder="أدخل البريد الإلكتروني"
              [class.error]="hasFieldError('email')"
            >
          </div>
          <div class="field-error" *ngIf="hasFieldError('email')">
            {{ getFieldError('email') }}
          </div>
        </div>

        <!-- Password Field -->
        <div class="form-group">
          <label for="password">كلمة المرور</label>
          <div class="input-wrapper" [class.error]="hasFieldError('password')">
            <i class="fas fa-lock input-icon"></i>
            <input
              [type]="showPassword ? 'text' : 'password'"
              id="password"
              formControlName="password"
              placeholder="أدخل كلمة المرور"
              [class.error]="hasFieldError('password')"
            >
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility()"
            >
              <i [class]="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <div class="field-error" *ngIf="hasFieldError('password')">
            {{ getFieldError('password') }}
          </div>
        </div>

        <!-- Remember Me -->
        <div class="form-options">
          <label class="checkbox-wrapper">
            <input type="checkbox" formControlName="rememberMe">
            <span class="checkmark"></span>
            تذكرني
          </label>
          <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          class="login-btn"
          [disabled]="isLoading"
          [class.loading]="isLoading"
        >
          <span *ngIf="!isLoading">
            <i class="fas fa-sign-in-alt"></i>
            تسجيل الدخول
          </span>
          <span *ngIf="isLoading" class="loading-content">
            <i class="fas fa-spinner fa-spin"></i>
            جاري تسجيل الدخول...
          </span>
        </button>

      </form>

      <!-- Demo Credentials -->
      <div class="demo-credentials">
        <h4>بيانات تجريبية:</h4>
        <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
        <p><strong>كلمة المرور:</strong> 123456</p>
      </div>

    </div>

    <!-- Footer Links -->
    <div class="footer-links">
      <a routerLink="/web" class="footer-link">
        <i class="fas fa-home"></i>
        العودة إلى الموقع الرئيسي
      </a>
      <a routerLink="/auth/login" class="footer-link">
        <i class="fas fa-users-cog"></i>
        دخول الموظفين
      </a>
    </div>

  </div>

  <!-- Features Section -->
  <div class="features-section">
    <h3>ماذا يمكنك فعله في البورتال؟</h3>
    
    <div class="features-grid">
      
      <div class="feature-item">
        <div class="feature-icon">
          <i class="fas fa-file-invoice"></i>
        </div>
        <h4>عروض الأسعار</h4>
        <p>عرض ومتابعة جميع عروض الأسعار الخاصة بك</p>
      </div>

      <div class="feature-item">
        <div class="feature-icon">
          <i class="fas fa-receipt"></i>
        </div>
        <h4>الفواتير والمدفوعات</h4>
        <p>إدارة الفواتير وتتبع حالة المدفوعات</p>
      </div>

      <div class="feature-item">
        <div class="feature-icon">
          <i class="fas fa-headset"></i>
        </div>
        <h4>الدعم الفني</h4>
        <p>تقديم تذاكر الدعم الفني ومتابعة حلولها</p>
      </div>

      <div class="feature-item">
        <div class="feature-icon">
          <i class="fas fa-user-circle"></i>
        </div>
        <h4>الملف الشخصي</h4>
        <p>إدارة معلومات الحساب والإعدادات</p>
      </div>

    </div>
  </div>

</div>
