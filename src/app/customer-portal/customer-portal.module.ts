import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';

// Components
import { CustomerPortalComponent } from './customer-portal.component';
import { CustomerLoginComponent } from './login/customer-login.component';
import { QuotesComponent } from './quotes/quotes.component';
import { InvoicesComponent } from './invoices/invoices.component';
import { SupportComponent } from './support/support.component';
import { PaymentsComponent } from './payments/payments.component';
import { ProfileComponent } from './profile/profile.component';

// Routes
const routes: Routes = [
  { path: 'login', component: CustomerLoginComponent },
  {
    path: '',
    component: CustomerPortalComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: CustomerPortalComponent },
      { path: 'quotes', component: QuotesComponent },
      { path: 'invoices', component: InvoicesComponent },
      { path: 'payments', component: PaymentsComponent },
      { path: 'support', component: SupportComponent },
      { path: 'profile', component: ProfileComponent }
    ]
  }
];

@NgModule({
  declarations: [
    CustomerPortalComponent,
    CustomerLoginComponent,
    QuotesComponent,
    InvoicesComponent,
    SupportComponent,
    PaymentsComponent,
    ProfileComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})
export class CustomerPortalModule { }
