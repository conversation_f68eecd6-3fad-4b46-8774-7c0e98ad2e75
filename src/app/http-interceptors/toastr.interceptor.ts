import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpRequest, HttpEvent, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { Injectable } from '@angular/core';
import { tap } from 'rxjs/operators';
//Test from angular only
////////////////////////

/*import { environment } from "../../environments/environment";*/
@Injectable()
export class ToastrInterceptor implements HttpInterceptor {
  constructor(private toastrService: ToastrService) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const method = req.method;
    return next.handle(req).pipe(
      tap(event => {
        if (method !== 'GET') {
          if (event instanceof HttpResponse) {
            if (event.body.success) {
              if (event.body.message) {
                this.toastrService.success(event.body.message);
              }
            } else {
              if (event.body.message) {
                this.toastrService.error(event.body.message);
              }
            }
          }
        }
      }, error => {
        if (error.status == 403) {
          this.toastrService.error('Error: permission needed!.');
        } else {
          this.toastrService.error('Error: please try again');
        }
      }));
  }
}


//Test from angular only
////////////////////////

//@Injectable()
//export class ToastrInterceptor implements HttpInterceptor {
//  constructor(private toastrService: ToastrService) { }

//  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
//    const method = req.method;
//    let request = req.clone();
//    if (!req.url.startsWith("http")) {
//      request = request.clone({ url: environment.apiUrl + request.url });
//    }
//    if (!req.url.includes("api")) {
//      request = request.clone({ url: request.url.replace(environment.apiUrl, environment.apiUrl + '/api') });
//    }
//    if (request.url.includes(".comapi")) {
//      request = request.clone({ url: request.url.replace('.comapi', '.com/api') });
//    }
//    return next.handle(request).pipe(
//      tap(event => {
//        if (method !== 'GET') {
//          if (event instanceof HttpResponse) {
//            if (event.body.success) {
//              if (event.body.message) {
//                this.toastrService.success(event.body.message);
//              }
//            } else {
//              if (event.body.message) {
//                this.toastrService.error(event.body.message);
//              }
//            }
//          }
//        }
//      }, error => {
//        if (error.status == 403) {
//          this.toastrService.error('Error: permission needed!.');
//        } else {
//          this.toastrService.error('Error: please try again');
//        }
//      }));
//  }
//}

