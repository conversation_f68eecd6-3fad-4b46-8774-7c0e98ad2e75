import { <PERSON>ttpInter<PERSON>, HttpHandler, HttpRequest, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import {environment} from "../../environments/environment";

@Injectable()
export class UrlInterceptor implements HttpInterceptor {
  constructor() { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    let request = req.clone();

    // إذا كان الطلب لا يبدأ بـ http أو https، أضف apiUrl
    if(!request.url.startsWith("http") && !request.url.startsWith("https")) {
      // إذا كان الطلب لا يبدأ بـ / أضفه
      if(!request.url.startsWith("/")) {
        request = request.clone({ url: environment.apiUrl + "/" + request.url });
      } else {
        request = request.clone({ url: environment.apiUrl + request.url });
      }
    }

    // تنظيف الروابط المكررة
    if(request.url.includes("api/api")) {
      request = request.clone({url: request.url.replace('api/api', 'api')});
    }

    if(request.url.includes("apiapi")) {
      request = request.clone({url: request.url.replace('apiapi', 'api')});
    }

    if(request.url.includes(".comapi")) {
      request = request.clone({url: request.url.replace('.comapi', '.com/api')});
    }

    return next.handle(request);
  }
}
