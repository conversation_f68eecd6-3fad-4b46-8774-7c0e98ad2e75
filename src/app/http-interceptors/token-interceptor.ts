import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { TokenService } from '../modules/auth/services/token.service';
import { TokenKeys } from '../modules/auth/services';
import { AuthService } from '../modules/auth';

@Injectable({ providedIn: 'root' })
export class TokenInterceptor implements HttpInterceptor {
  constructor(private tokenService: TokenService,
    private router: Router) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const headers = req.headers
      .set('Authorization', 'bearer ' + this.tokenService.get(TokenKeys.access_Token));
    const authReq = req.clone({ headers });
    return next.handle(authReq)
      .pipe(tap(event => {

      }, (error: HttpErrorResponse) => {
        if (error.status == 401) {
          this.tokenService.shouldRefreshToken.next(true);
        } else if (error.status == 403) {
          this.router.navigate(['/error/403']);
        }
      }));
  }
}
