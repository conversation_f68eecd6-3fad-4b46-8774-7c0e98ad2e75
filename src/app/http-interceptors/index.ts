import { HTTP_INTERCEPTORS } from '@angular/common/http';
// import { HeaderInterceptor } from './header-interceptor';
// import { LoadingInterceptor } from './loading-interceptor';
import { ToastrInterceptor } from './toastr.interceptor';
import { TokenInterceptor } from './token-interceptor';
import {UrlInterceptor} from "./url.interceptor";

export const httpInterceptorProviders = [
  // { provide: HTTP_INTERCEPTORS, useClass: LoadingInterceptor, multi: true },
  // { provide: HTTP_INTERCEPTORS, useClass: HeaderInterceptor, multi: true },
  { provide: HTTP_INTERCEPTORS, useClass: ToastrInterceptor, multi: true },
  { provide: HTTP_INTERCEPTORS, useClass: UrlInterceptor, multi: true },
  { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true }
];
