import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslationService } from './modules/i18n';
// language list
import { locale as enLang } from './modules/i18n/vocabs/en';
import { locale as arLang } from './modules/i18n/vocabs/ar';
import { ThemeModeService } from './_metronic/partials/layout/theme-mode-switcher/theme-mode.service';
import { DrawerComponent, MenuComponent, ScrollComponent, ToggleComponent } from './_metronic/kt/components';
import { Subscription } from 'rxjs';
import {  NavigationCancel, NavigationEnd, Router } from '@angular/router';
import { PageInfoService } from './_metronic/layout';
import config from 'devextreme/core/config';
import {licenseKey} from "./license";



@Component({
    // tslint:disable-next-line:component-selector
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'body[root]',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class AppComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  licenseObserver: MutationObserver;

  constructor(
    private translationService: TranslationService,
    private modeService: ThemeModeService,
    private router: Router,
    // private route: ActivatedRoute,
    private pageInfo: PageInfoService
  ) {
    // register translations
    this.translationService.loadTranslations(
      enLang,
      arLang
    );
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.pageInfo.breadcrumbs.unsubscribe();
    
    // إيقاف مراقب DOM عند تدمير المكون
    if (this.licenseObserver) {
      this.licenseObserver.disconnect();
    }
  }

  ngOnInit() {
    // نقل تكوين الترخيص إلى خدمة الترخيص
    // config({licenseKey});

    this.modeService.init();
    this.routingChanges();
    
    // إضافة مراقب DOM لإزالة رسائل ترخيص DevExpress
    this.setupLicenseMessageRemover();
  }

  routingChanges() {
    const routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd || event instanceof NavigationCancel) {
        this.menuReinitialization();
      }
    });
    this.subscription.add(routerSubscription);
  }

  menuReinitialization() {
    setInterval(() => {
      MenuComponent.reinitialization();
    }, 10);
  }

  // دالة لإزالة رسائل ترخيص DevExpress
  setupLicenseMessageRemover() {
    // إنشاء مراقب للتغييرات في DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(() => {
        // البحث عن جميع العناصر التي قد تحتوي على رسالة الترخيص
        const popups = document.querySelectorAll('.dx-popup-content');
        popups.forEach(popup => {
          if (popup.textContent?.includes('For evaluation purposes only') || 
              popup.textContent?.includes('DevExpress product libraries')) {
            // العثور على النافذة المنبثقة الأصلية وإخفائها
            const popupWrapper = popup.closest('.dx-popup-wrapper');
            if (popupWrapper) {
              popupWrapper.remove();
            }
          }
        });
      });
    });

    // بدء مراقبة التغييرات في DOM
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // تخزين المراقب للتنظيف لاحقاً
    this.licenseObserver = observer;
  }
}
