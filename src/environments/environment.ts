// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { appUrls } from './appUrls';

export const environment = {
  production: false,
  appVersion: 'v8.1.6',
  USERDATA_KEY: 'authf649fc9a5f55',
  isMockEnabled: true,
  // apiUrl:
  //   (window as any).__env.apiUrl == '%%API_URL%%'
  //     ? 'https://api.falcon-v.com/api'
  //     : (window as any).__env.apiUrl,
  // apiUrl: '/api',
  apiUrl: 'http://localhost:5001/api',
  // apiUrl: 'http://***********:5001/api',
  appThemeName: 'Metronic',
  //appPurchaseUrl: 'https://1.envato.market/EA4JP',
  //appHTMLIntegration:
  //  'https://preview.keenthemes.com/metronic8/demo1/documentation/base/helpers/flex-layouts.html',
  //appPreviewUrl: 'https://preview.keenthemes.com/metronic8/angular/demo1/',
  //appPreviewAngularUrl:
  //  'https://preview.keenthemes.com/metronic8/angular/demo1',
  //appPreviewDocsUrl: 'https://preview.keenthemes.com/metronic8/angular/docs',
  //appPreviewChangelogUrl:
  //  'https://preview.keenthemes.com/metronic8/angular/docs/changelog',
  appUrls,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
