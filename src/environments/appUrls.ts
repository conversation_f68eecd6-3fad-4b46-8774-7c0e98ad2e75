export const API_URLS = 'API_URLS';
export const baseUrl = '/api';

export const appUrls = {
  account: baseUrl + '/account/',
  companies: baseUrl + '/Companies/',
  companylist: baseUrl + '/CompanyList/',
  users: baseUrl + '/users/',
  transactions: baseUrl + '/transactions/',
  warehouses: baseUrl + '/warehouses/',
  statistics: baseUrl + '/statistics/',
  permissions: baseUrl + '/permissions/',
  roles: baseUrl + '/roles/',
  countries: baseUrl + '/country/',
  currencies: baseUrl + '/currency/',
  settings: baseUrl + '/settings/',

  hr: baseUrl + '/hr',
  Salaries: baseUrl + '/Salaries/',
  // gewaly added it
  Dependents: baseUrl + '/dependents/',
  UpdateEmployeeData: baseUrl + '/UpdateEmployeeData/',
  ShiftPlans: baseUrl + '/ShiftPlans/',
  OfficialHolidays: baseUrl + '/OfficialHolidays/',
  Daysandhours: baseUrl + '/Daysandhours/',
  EditShiftPlans: baseUrl + '/EditShiftPlans/',
  WorkingHoursAndPenalties: baseUrl + '/WorkingHoursAndPenalties/',
  Missions: baseUrl + '/Missions/',
  OverTime: baseUrl + '/OverTime/',
  ManualAttendance: baseUrl + '/ManualAttendance/',
  BenefitDeductionApplication: baseUrl + '/BenefitDeductionApplication/',
  SalaryAdditions: baseUrl + '/SalaryAdditions/',
  SalaryDeduction: baseUrl + '/SalaryDeduction/',
  ReceiptTransactions: baseUrl + '/ReceiptTransactions/',
  SecondmentContract: baseUrl + '/SecondmentContract/',
  Assignment: baseUrl + '/Assignment/',
  LegalIssues: baseUrl + '/LegalIssues/',
  TicketBooking: baseUrl + '/TicketBooking/',
  AdministrativeDecision: baseUrl + '/AdministrativeDecision/',
  Attention: baseUrl + '/Attention/',
  AppointmentLetter: baseUrl + '/AppointmentLetter/',
  CustodyReceipt: baseUrl + '/CustodyReceipt/',
  HealthInsurance: baseUrl + '/HealthInsurance/',
  Entitlements: baseUrl + '/Entitlements/',
  Certificate: baseUrl + '/Certificate/',
  Resignation: baseUrl + '/Resignation/',
  Warning: baseUrl + '/Warning/',
  MedicalInspectionLetter: baseUrl + '/MedicalInspectionLetter/',
  HrForms: baseUrl + '/HrForms/',
  Contracts: baseUrl + '/Contracts/',
  NewEmployeeEvaluation: baseUrl + '/NewEmployeeEvaluation/',
  EmployeeEvaluation: baseUrl + '/EmployeeEvaluation/',
  MonthlyEvaluation: baseUrl + '/MonthlyEvaluation/',
  TrainingNeeds: baseUrl + '/TrainingNeeds/',
  AnnualTrainingPlan: baseUrl + '/AnnualTrainingPlan/',
  EmployeeTrainingForm: baseUrl + '/EmployeeTrainingForm/',
  EvaluateTrainingEffectiveness: baseUrl + '/EvaluateTrainingEffectiveness/',
  TrainingEntities: baseUrl + '/TrainingEntities/',
  Trucking: baseUrl + '/Trucking/',
  CarTraffic: baseUrl + '/CarTraffic/',
  VisitorsRecord: baseUrl + '/VisitorsRecord/',
  Visa: baseUrl + '/Visa/',
  StaffTransferBetweenProjects: baseUrl + '/StaffTransferBetweenProjects/',
  SalaryUpdate: baseUrl + '/SalaryUpdate/',
  IqamaUpdate: baseUrl + '/NationalIdentity/',
  EmployeeRequests: baseUrl + '/EmployeeRequests/',
  HousingAllowancePayments: baseUrl + '/HousingAllowancePayments/',
  Ending: baseUrl + '/Ending/',
  AnnualDues: baseUrl + '/AnnualDues/',
  HandwrittenNote: baseUrl + '/HandwrittenNote/',
  StaffCosts: baseUrl + '/StaffCosts/',
  ScheduleAdvances: baseUrl + '/ScheduleAdvances/',
  // new
  MyRequests: baseUrl + '/MyRequests/',

  // end
  AttendanceMachines: baseUrl + '/AttendanceMachines/',
  StaffShifts: baseUrl + '/StaffShifts/',
  Vacations: baseUrl + '/Vacations/',
  Employees: baseUrl + '/Employees/',
  AbsencePermissions: baseUrl + '/AbsencePermissions/',
  AdditionsTypes: baseUrl + '/AdditionsTypes/',
  DeductionsTypes: baseUrl + '/DeductionsTypes/',
  EmployeesCategories: baseUrl + '/EmployeesCategories/',
  JobDescriptions: baseUrl + '/JobDescriptions/',
  ShippingCompanies: baseUrl + '/ShippingCompanies/',
  DocumentsType: baseUrl + '/DocumentsType/',
  insurances: baseUrl + '/insurances/',
  Sponsors: baseUrl + '/Sponsors/',
  Qualifications: baseUrl + '/Qualifications/',
  hrReports: baseUrl + '/HrReports/',
  InventoryReports: baseUrl + '/InventoryReports/',

  sales: baseUrl + '/',
  accounting: baseUrl + '/',
  Journal: baseUrl + '/Journal/',
  FinalAccounts: baseUrl + '/FinalAccounts/',
  ClosingPeriod: baseUrl + '/ClosingPeriod/',
  AccountingReports: baseUrl + '/AccountingReports/',
  SalesReports: baseUrl + '/SalesReports/',

  crm: baseUrl + '/',
  WhatsappInstance: baseUrl + '/WhatsappInstance/',
  dashboard: baseUrl + '/',
  fleet: baseUrl + '/',
  general: baseUrl + '/',
  Nationality: baseUrl + '/Nationality/',
  inventory: baseUrl + '/',
  maintenance: baseUrl + '/',
  manufacturing: baseUrl + '/',
  pos: baseUrl + '/',
  project: baseUrl + '/',
  purchase: baseUrl + '/',
  PurchaseReports: baseUrl + '/PurchaseReports/',
  realestate: baseUrl + '/',
  CashBoxes: baseUrl + '/CashBox/',
  TaxGroup: baseUrl + '/TaxGroup/',
  Taxes: baseUrl + '/Taxes/',
  Partners: baseUrl + '/Partners/',
  CostTypes: baseUrl + '/CostTypes/',
  FinancialYear: baseUrl + '/FinancialYear/',
  Banks: baseUrl + '/Banks/',
  BankAccounts: baseUrl + '/BankAccount/',
  ChartOfAccounts: baseUrl + '/ChartofAccounts/',
  AnalyticAccounts: baseUrl + '/AnalyticAccounts/',
  tradeClassification: baseUrl + '/tradeClassification/',
  operationTypes: baseUrl + '/operationTypes/',
  productCategories: baseUrl + '/productCategories/',
  prodcuts: baseUrl + '/',
  productSubCategories: baseUrl + '/productSubCategories/',
  ProductAttributes: baseUrl + '/ProductAttributes/',
  ProductAttributesValues: baseUrl + '/ProductAttributesValues/',
  SalespersonCategories: baseUrl + '/SalespersonCategories/',
  CustomersCategory: baseUrl + '/CustomersCategory/',
  MainCustomer: baseUrl + '/MainCustomer/',
  Guarantor: baseUrl + '/',
  Customers: baseUrl + '/Customer/',
  Leads: baseUrl + '/',
  Suppliers: baseUrl + '/Suppliers/',
  Pricelists: baseUrl + '/Pricelists/',
  Salesperson: baseUrl + '/',
  SMSurl: baseUrl + '/',
  Emailurl: baseUrl + '/',
  units: baseUrl + '/units/',
  manufactures: baseUrl + '/manufactures/',
  SalesType: baseUrl + '/SalesType/',
  SalesTeams: baseUrl + '/SalesTeams/',
  Stages: baseUrl + '/stages/',
  SalespersonCommissions: baseUrl + '/SalespersonCommissions/',
  purchaseTypes: baseUrl + '/purchaseTypes/',
  supplierClassification: baseUrl + '/supplierClassifications/',
  StaffAssistant: baseUrl + '/',
};
