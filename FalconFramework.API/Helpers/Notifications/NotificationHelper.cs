﻿using Microsoft.AspNetCore.SignalR;
using FalconFramework.API.Models;
using FalconFramework.API.Helpers.Email;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.DTOs;

namespace FalconFramework.API.Helpers.Notifications
{
    public class NotificationHelper : INotificationHelper
    {
        private readonly INotificationService _notificationService;
        private readonly IEmailService _emailService;
        private readonly IAppAuthenticationService _userService;
        private readonly IHubContext<NotificationsHub> _hubContext;

        public NotificationHelper(INotificationService notificationService,
            IEmailService emailService,
            IAppAuthenticationService userService,
            IHubContext<NotificationsHub> hub)
        {
            this._notificationService = notificationService;
            this._emailService = emailService;
            this._userService = userService;
            this._hubContext = hub;
        }

        public async Task SendAsync(InputNotification notification)
        {
            if (!string.IsNullOrEmpty(notification.TargetUserId))
            {
                var selectedUser = await _userService.GetUserByIdAsync(notification.TargetUserId);

                if (selectedUser != null)
                {
                    // Send Notification
                    var result = await _notificationService.CreateForAsync(notification);

                    //Add Notification To SignalR
                    await _hubContext.Clients.User(notification.TargetUserId).SendAsync("Notify", new NotificationViewModel
                    {
                        Body = notification.Body,
                        Title = notification.Title,
                        ResourceId = notification.ResourceId,
                        ResourceType = notification.ResourceType,
                        SentDate = DateTime.Now.ToString()
                    });


                    if (!string.IsNullOrEmpty(selectedUser.Email))
                    {
                        //Send Email 
                        await _emailService.SendAsync(new MailMessage
                        {
                            Subject = notification.Title,
                            Body = notification.Body,
                            To = selectedUser.Email
                        });
                    }

                    if (!string.IsNullOrEmpty(selectedUser.PhoneNumber))
                    {
                        //Send Sms

                    }
                }
            }
        }
    }
}