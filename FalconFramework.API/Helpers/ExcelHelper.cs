﻿using ClosedXML.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.Infrastructure.Helpers
{
    public class ExcelHelper
    {
        public static void ExportToExcel<T>(string path, List<T> result) where T : class
        {
            // string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            using (XLWorkbook workBook = new XLWorkbook())
            {
                var worksheet = workBook.Worksheets.Add("Sheet1");

                if (result == null || result.Count == 0)
                {
                    throw new Exception("Result is Empty");
                }

                var firstObj = result.FirstOrDefault();
                var properties = firstObj?.GetType().GetProperties();
                for (int i = 0; i < properties.Length; i++)
                {
                    worksheet.Cell(1, (i + 1)).Value = properties[i].Name;
                }

                for (int i = 1; i <= result.Count; i++)
                {
                    var row = i + 1;
                    for (int x = 0; x < properties.Length; x++)
                    {
                        worksheet.Cell(row, x + 1).Value = result[i - 1].GetType().GetProperty(properties[x].Name).GetValue(result[i - 1])?.ToString();
                    }
                }
                workBook.SaveAs(path);
            }
        }

        public static List<T> GetList<T>(Stream file) where T : class
        {
            List<T> list = new List<T>();
            using (XLWorkbook workBook = new XLWorkbook(file))
            {
                IXLWorksheet workSheet = workBook.Worksheet(0);
                bool firstRow = true;
                List<string> header = new List<string>();
                foreach (IXLRow row in workSheet.Rows())
                {
                    if (firstRow)
                    {
                        foreach (IXLCell cell in row.Cells())
                        {
                            if (!string.IsNullOrEmpty(cell.Value.ToString()))
                            {
                                header.Add(cell.Value.ToString());
                            }
                            else
                            {
                                break;
                            }
                        }
                        firstRow = false;
                    }
                    else
                    {
                        int i = 0;
                        T obj = Activator.CreateInstance<T>();
                        foreach (IXLCell cell in row.Cells())
                        {
                            try
                            {
                                obj.GetType().GetProperty(header[i]).SetValue(obj, cell.Value.ToString());
                            }
                            catch (Exception ex)
                            {

                            }
                            i++;
                        }
                        list.Add(obj);
                    }
                }
                return list;
            }
        }
    }
}