﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace FalconFramework.API.Helpers.Images
{
    public class ImageService : IImageService
    {
        private readonly IWebHostEnvironment _env;

        public ImageService(IWebHostEnvironment hostEnvironment)
        {
            _env = hostEnvironment;
        }

        public string GenerateNewFileName(string fileName)
        {
            var extension = Path.GetExtension(fileName);
            var newName = Guid.NewGuid().ToString();
            return string.Concat(newName, extension);
        }

        public async Task SaveAsync(Stream stream, string fileName, ImageFolders folder)
        {
            var path = GetPhysicalFolderPath(folder, fileName);
            using (var fs = File.Create(path))
            {
                await stream.CopyToAsync(fs);
            }
        }

        public void DeleteImage(ImageFolders location, string imageName)
        {
            var path = GetPhysicalFolderPath(location, imageName);
            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        private string GetPhysicalFolderPath(ImageFolders folder, string fileName)
        {
            var directory = Path.Combine(_env.WebRootPath, "Uploads", Enum.GetName(folder));
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            return Path.Combine(directory, fileName);
        }

        public async Task SaveAsync(IFormFile file, string fileName, ImageFolders folder)
        {
            await SaveAsync(file.OpenReadStream(), fileName, folder);
        }
    }
}
