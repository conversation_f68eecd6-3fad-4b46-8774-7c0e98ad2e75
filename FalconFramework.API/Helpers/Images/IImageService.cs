﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FalconFramework.API.Helpers.Images
{
    public interface IImageService
    {
        Task SaveAsync(IFormFile file, string fileName, ImageFolders folder);
        Task SaveAsync(Stream stream, string fileName, ImageFolders folder);

        string GenerateNewFileName(string fileName);

        void DeleteImage(ImageFolders location, string imageName);
    }
}
