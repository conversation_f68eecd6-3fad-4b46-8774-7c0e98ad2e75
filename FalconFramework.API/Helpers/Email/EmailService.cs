﻿using Microsoft.Extensions.Configuration;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace FalconFramework.API.Helpers.Email
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _config;

        public EmailService(IConfiguration configuration)
        {
            this._config = configuration;
        }
        public string Host
        {
            get { return _config.GetValue<string>("Mail:Host"); }
        }
        public int Port
        {
            get { return _config.GetValue<int>("Mail:Port"); }
        }
        public string Password
        {
            get { return _config.GetValue<string>("Mail:PWD"); }
        }

        public string UserName
        {
            get { return _config.GetValue<string>("Mail:User"); }
        }

        public async Task SendAsync(MailMessage msg)
        {
            try
            {
                using (SmtpClient client = new SmtpClient(Host, Port))
                {
                    client.Credentials = new NetworkCredential(UserName, Password);
                    var msgToSend = new System.Net.Mail.MailMessage
                    {
                        Subject = msg.Subject,
                        Body = msg.Body,
                        IsBodyHtml = true
                    };
                    msgToSend.To.Add(msg.To);
                    client.SendAsync(msgToSend, null);
                }
            }
            catch (System.Exception)
            {

            }
        }
    }
}
