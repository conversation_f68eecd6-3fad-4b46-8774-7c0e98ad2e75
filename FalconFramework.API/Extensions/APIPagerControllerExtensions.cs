using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Data.SqlClient;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;

namespace FalconFramework
{
    public static class APIPagerControllerExtensions
    {
        // Valores predeterminados que se pueden ajustar
        private static int DefaultPageSize = 50;
        private static int MaxPageSizeAllowed = 1000;
        
        public static async Task<IActionResult> GetPagedData<T>(this ControllerBase controller, int currentPage, IQueryable<T> list, int pageSize = 0, dynamic info = null)
        {
            try
            {
                // Check if the input sequence is null
                if (list == null)
                {
                    return controller.BadRequest(new
                    {
                        Success = false,
                        Message = "The provided data set is null"
                    });
                }

                // Validate and adjust parameters dynamically
                if (pageSize <= 0)
                {
                    // Si no se especifica pageSize, usar el valor predeterminado
                    pageSize = DefaultPageSize;
                }
                else if (pageSize > MaxPageSizeAllowed)
                {
                    // Si se especifica un pageSize demasiado grande, limitar al máximo permitido
                    pageSize = MaxPageSizeAllowed;
                }
                
                // Asegurar que currentPage es válido
                currentPage = currentPage < 1 ? 1 : currentPage;
                
                int previousPage = currentPage - 1;
                int startIndex = previousPage * pageSize;
                startIndex = startIndex > 0 ? startIndex : 0;

                int itemsCount = 0;
                decimal pagesCount = 0;
                List<T> pagedData = new List<T>();

                // Get detailed type information for logging
                Type entityType = typeof(T);
                StringBuilder debugInfo = new StringBuilder();
                debugInfo.AppendLine($"Entity Type: {entityType.FullName}");
                debugInfo.AppendLine("Properties:");
                
                foreach (PropertyInfo prop in entityType.GetProperties())
                {
                    string nullable = prop.PropertyType.IsValueType && 
                                     Nullable.GetUnderlyingType(prop.PropertyType) != null ? 
                                     "Nullable" : "Not Nullable";
                    debugInfo.AppendLine($"- {prop.Name} : {prop.PropertyType.Name} ({nullable})");
                }
                
                // Log debug information
                System.Diagnostics.Debug.WriteLine(debugInfo.ToString());

                try
                {
                    // Try to use ToListAsync directly
                    try
                    {
                        pagedData = await list
                            .Skip(startIndex)
                            .Take(pageSize)
                            .ToListAsync();
                            
                        if (currentPage <= 1)
                        {
                            itemsCount = await list.CountAsync();
                            pagesCount = Math.Ceiling(Convert.ToDecimal(itemsCount) / Convert.ToDecimal(pageSize));
                        }
                    }
                    catch (Exception toListEx)
                    {
                        // Collect detailed error information
                        StringBuilder errorDetails = new StringBuilder();
                        errorDetails.AppendLine($"ToListAsync Exception: {toListEx.GetType().FullName}");
                        errorDetails.AppendLine($"Message: {toListEx.Message}");
                        
                        if (toListEx.InnerException != null)
                        {
                            errorDetails.AppendLine($"Inner Exception Type: {toListEx.InnerException.GetType().FullName}");
                            errorDetails.AppendLine($"Inner Message: {toListEx.InnerException.Message}");
                        }
                        
                        // Log error details
                        string fullErrorDetails = errorDetails.ToString();
                        System.Diagnostics.Debug.WriteLine(fullErrorDetails);
                        
                        // Check for SqlNullValueException
                        bool isSqlNullValueException = 
                            toListEx.GetType().Name.Contains("SqlNullValue") || 
                            (toListEx.InnerException != null && toListEx.InnerException.GetType().Name.Contains("SqlNullValue"));
                        
                        if (isSqlNullValueException)
                        {
                            return controller.BadRequest(new
                            {
                                Success = false,
                                Message = "Failed to retrieve data - SQL Null Value Exception",
                                ExceptionType = toListEx.GetType().Name,
                                ExceptionMessage = toListEx.Message,
                                InnerExceptionType = toListEx.InnerException?.GetType().Name,
                                InnerExceptionMessage = toListEx.InnerException?.Message,
                                PropertyDetails = debugInfo.ToString(),
                                StackTrace = toListEx.StackTrace,
                                DetailedError = fullErrorDetails
                            });
                        }
                        
                        // Try alternative approach with AsEnumerable
                        try
                        {
                            var filteredQuery = list.AsEnumerable();
                            
                            // Filter out entities with null Id if property exists
                            if (entityType.GetProperty("Id") != null)
                            {
                                filteredQuery = filteredQuery.Where(item => 
                                {
                                    var idProp = item.GetType().GetProperty("Id");
                                    if (idProp != null)
                                    {
                                        var value = idProp.GetValue(item);
                                        return value != null;
                                    }
                                    return true;
                                });
                            }
                            
                            pagedData = filteredQuery
                                .Skip(startIndex)
                                .Take(pageSize)
                                .ToList();
                                
                            if (currentPage <= 1)
                            {
                                itemsCount = filteredQuery.Count();
                                pagesCount = Math.Ceiling(Convert.ToDecimal(itemsCount) / Convert.ToDecimal(pageSize));
                            }
                        }
                        catch (Exception fallbackEx)
                        {
                            // If alternative approach also failed, return detailed error
                            System.Diagnostics.Debug.WriteLine($"Alternative approach failed: {fallbackEx.Message}");
                            
                            return controller.BadRequest(new
                            {
                                Success = false,
                                Message = "Failed to retrieve data after multiple attempts",
                                ExceptionType = toListEx.GetType().Name,
                                ExceptionMessage = toListEx.Message,
                                FallbackError = fallbackEx.Message,
                                PropertyDetails = debugInfo.ToString(),
                                StackTrace = toListEx.StackTrace
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Return detailed error information
                    System.Diagnostics.Debug.WriteLine($"Error in data processing: {ex.Message}");
                    
                    return controller.BadRequest(new
                    {
                        Success = false,
                        Message = "Error processing data",
                        ExceptionDetails = ex.Message,
                        PropertyInfo = debugInfo.ToString()
                    });
                }

                // Return paginated results with additional metadata
                var response = new
                {
                    Success = true,
                    PagesCount = pagesCount,
                    ItemsCount = itemsCount,
                    CurrentPage = currentPage,
                    PageSize = pageSize,
                    HasPreviousPage = currentPage > 1,
                    HasNextPage = currentPage < pagesCount,
                    Data = pagedData,
                    Message = "Operation Completed Successfully",
                    info
                };
                
                if (currentPage <= 1)
                {
                    return controller.Ok(response);
                }
                
                return controller.Ok(new
                {
                    Success = true,
                    CurrentPage = currentPage,
                    HasPreviousPage = currentPage > 1,
                    HasNextPage = currentPage < pagesCount,
                    Data = pagedData,
                    Message = "Operation Completed Successfully"
                });
            }
            catch (Exception ex)
            {
                // Return general error information
                System.Diagnostics.Debug.WriteLine($"Unhandled exception: {ex.Message}");
                
                return controller.BadRequest(new
                {
                    Success = false,
                    Message = "An error occurred while processing the paging request",
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message
                });
            }
        }
        
        /// <summary>
        /// Configura los parámetros globales de paginación
        /// </summary>
        public static void ConfigurePagination(int defaultPageSize, int maxPageSizeAllowed)
        {
            DefaultPageSize = defaultPageSize > 0 ? defaultPageSize : 50;
            MaxPageSizeAllowed = maxPageSizeAllowed > 0 ? maxPageSizeAllowed : 1000;
        }
    }
}
