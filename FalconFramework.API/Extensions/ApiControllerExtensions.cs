﻿using ClosedXML.Excel;
using FalconFramework.SharedKernel.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace FalconFramework.API
{
    public static class ApiControllerExtensions
    {
        public static IActionResult AppOk(this ControllerBase controller, OperationResult result)
        {
            return controller.Ok(new { success = result.Success, data = result.Payload, message = result.Message });
        }

        public static IActionResult AppSuccess(this ControllerBase controller, dynamic data , string message = "تمت العملية بنجاح!.")
        {
            return controller.Ok(new { success = true, data = data, message = message });
        }

        public static IActionResult AppFailed(this ControllerBase controller, dynamic data)
        {
            return controller.Ok(new { success = false, data = data });
        }

        public static IActionResult AppFailed(this ControllerBase controller, string message = "عفواً حدث خطأ أثناء تنفيذ طلبك", dynamic data = null)
        {
            return controller.Ok(new { success = false, data = data, message = message });
        }

        public static IActionResult AppNotFound(this ControllerBase controller, string msg = "العنصر المطلوب غير موجود")
        {
            return controller.Ok(new { success = false, message = msg });
        }

        public static IActionResult AppInvalidModel(this ControllerBase controller, ModelStateDictionary modelState)
        {
            var msg = string.Join(',', modelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)));
            return controller.Ok(new { success = false, message = msg });
        }

        public static IActionResult ExportToExcelMvc<T>(this ControllerBase controller, string fileName, List<T> result) where T : class
        {
            string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            using (XLWorkbook workBook = new XLWorkbook())
            {
                var worksheet = workBook.Worksheets.Add("Sheet1");

                if (result == null || result.Count == 0)
                {
                    throw new Exception("Result is Empty");
                }

                var firstObj = result.FirstOrDefault();
                var properties = firstObj?.GetType().GetProperties();
                for (int i = 0; i < properties.Length; i++)
                {
                    worksheet.Cell(1, (i + 1)).Value = properties[i].Name;
                }

                for (int i = 1; i <= result.Count; i++)
                {
                    var row = i + 1;
                    for (int x = 0; x < properties.Length; x++)
                    {
                        var value = result[i - 1].GetType().GetProperty(properties[x].Name).GetValue(result[i - 1]);
                        if (value is List<string>)
                        {
                            worksheet.Cell(row, x + 1).Value = string.Join(",", (List<string>)value);
                        }
                        else
                        {
                            worksheet.Cell(row, x + 1).Value = value?.ToString();
                        }
                    }
                }
                using (MemoryStream ms = new MemoryStream())
                {
                    workBook.SaveAs(ms);

                    return controller.File(ms.ToArray(), contentType, fileName);
                }
            }
        }


        public static List<T> GetListFromExcel<T>(this ControllerBase controller, Stream file) where T : class
        {
            List<T> list = new List<T>();
            using (XLWorkbook workBook = new XLWorkbook(file))
            {
                IXLWorksheet workSheet = workBook.Worksheets.ElementAt(0);
                bool firstRow = true;
                List<string> header = new List<string>();
                foreach (IXLRow row in workSheet.Rows())
                {
                    if (firstRow)
                    {
                        foreach (IXLCell cell in row.Cells())
                        {
                            if (!string.IsNullOrEmpty(cell.Value.ToString()))
                            {
                                header.Add(cell.Value.ToString());
                            }
                            else
                            {
                                break;
                            }
                        }
                        firstRow = false;
                    }
                    else
                    {
                        int i = 0;
                        T obj = Activator.CreateInstance<T>();
                        foreach (IXLCell cell in row.Cells())
                        {
                            try
                            {
                                obj.GetType().GetProperty(header[i])?.SetValue(obj, cell.Value.ToString());
                            }
                            catch (Exception ex)
                            {

                            }
                            i++;
                        }
                        list.Add(obj);
                    }
                }
                return list;
            }
        }
    }
}
