﻿using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Attributes
{
    public class HasPermissionAttribute : TypeFilterAttribute
    {
        public HasPermissionAttribute(string moduleName) : base(typeof(PermissionRequirementFilter))
        {
            Arguments = new object[] { moduleName };
        }
    }

    public class PermissionRequirementFilter : IAuthorizationFilter
    {
        readonly string _module;
        private readonly IPermissionService _permissionService;

        public PermissionRequirementFilter(string module, IPermissionService permissionService)
        {
            _module = module;
            this._permissionService = permissionService;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var userId = context.HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                context.Result = new ForbidResult();
                return;
            }

            var permissions = _permissionService.FindByUserAsync(userId).Result;

            if (!permissions.Any(p => p.Module == _module))
            {
                context.Result = new ForbidResult();
                return;
            }


            //var actionName = context.ActionDescriptor.EndpointMetadata.OfType<ModuleActionAttribute>()?.FirstOrDefault()?.ActionName;

            var actionName = context.ActionDescriptor.EndpointMetadata
    .OfType<ModuleActionAttribute>()
    .OrderBy(m => m.ActionName) // Order by ActionName
    .FirstOrDefault()?.ActionName;


            if (!string.IsNullOrEmpty(actionName))
            {
                if (!permissions.Any(p => p.Module == _module
                && Convert.ToBoolean(p.GetType().GetProperty(actionName).GetValue(p)) == true))
                {
                    context.Result = new ForbidResult();
                    return;
                }
            }
            else
            {
                var method = context.HttpContext.Request.Method;

                if (method == "GET" && !permissions.Any(p => p.Module == _module && p.DisplayAction))
                {
                    context.Result = new ForbidResult();
                }
                else if (method == "POST" && !permissions.Any(p => p.Module == _module && p.CreateAction))
                {
                    context.Result = new ForbidResult();
                }
                else if (method == "PUT" && !permissions.Any(p => p.Module == _module && p.UpdateAction))
                {
                    context.Result = new ForbidResult();
                }
                else if (method == "DELETE" && !permissions.Any(p => p.Module == _module && p.DeleteAction))
                {
                    context.Result = new ForbidResult();
                }
            }
        }
    }
}