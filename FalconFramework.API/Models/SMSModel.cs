﻿using DocumentFormat.OpenXml.Office2013.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Models
{
    public class SMSModel
    {
        public string[] Phones { get; set; }
        public int ProviderCaseId { get; set; }
        public string template { get; set; }

        public string RecipientType { get; set; }
        public string RecipientNumber { get; set; }
        public SMSVariants[] variants { get; set; }
    }

    public class SMSVariants
    {
        public string Value { get; set; }
    }
    // mobile 
    // nameAr (Ali).
}
