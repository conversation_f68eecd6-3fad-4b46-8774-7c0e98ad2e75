﻿using FalconFramework.ApplicationCore.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Models
{
    public class InputCompanyViewModel
    {
        [Required]
        public string NameAr { get; set; }
        [Required]
        public string NameEn { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string CRN { get; set; }
        public string Address { get; set; }
        public new IFormFile? Logo { get; set; }
        public new IFormFile? Header { get; set; }
        public new IFormFile? Footer { get; set; }
    }
}
