﻿using FalconFramework.API;
using FalconFramework.API.Helpers.Email;
using FalconFramework.API.Helpers.Images;
using FalconFramework.API.Helpers.Notifications;
using FalconFramework.Web.Api.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Text;

namespace FalconFramework
{
    public static class WebApiStartup
    {
        public static IServiceCollection AddApplicationApi(this IServiceCollection services, IConfiguration config)
        {

            services.AddSignalR();

            services.Configure<AppSettings>(config.GetSection("AppSettings"));

            services.AddJwtAuthenctication(config);

            services.AddTransient<IEmailService, EmailService>();
            services.AddTransient<IImageService, ImageService>();
            services.AddTransient<INotificationHelper, NotificationHelper>();
           
            return services;
        }
    }
}
