﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<!-- <Nullable>enable</Nullable> -->
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Controllers\General\CompaniesController.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.Reporting" Version="2.1.0" />
		<PackageReference Include="ClosedXML" Version="0.104.1" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.0" />
		<PackageReference Include="System.IO.Packaging" Version="9.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FalconFramework.ApplicationCore\FalconFramework.ApplicationCore.csproj" />
		<ProjectReference Include="..\FalconFramework.SharedKernel\FalconFramework.SharedKernel.csproj" />
		<ProjectReference Include="..\Integration.Sms\Integration.Sms.csproj" />
	</ItemGroup>

</Project>
