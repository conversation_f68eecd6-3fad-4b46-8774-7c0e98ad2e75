﻿using FalconFramework.ApplicationCore.Entities;
using FalconFramework.Web.Api.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Text;

namespace FalconFramework
{
    public static class JwtAuthenticationStartup
    {
        public static IServiceCollection AddJwtAuthenctication(this IServiceCollection services, IConfiguration config)
        {
            var settingsSection = config.GetSection("AppSettings");
            AppSettings settings = settingsSection.Get<AppSettings>();
            if (settings != null)
            {
                byte[] key = Encoding.UTF8.GetBytes(settings.Secret);

                services.AddAuthentication(authOptions =>
                    {
                        authOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                        authOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    }).AddJwtBearer(jwtOptions =>
                    {
                        jwtOptions.SaveToken = true;
                        jwtOptions.TokenValidationParameters = new TokenValidationParameters
                        {
                            ValidateAudience = false,
                            ValidateIssuer = false,
                            ValidateIssuerSigningKey = true,
                            IssuerSigningKey = new SymmetricSecurityKey(key),
                            ValidateLifetime = true
                        };
                    }).AddCookie();
            }
            return services;
        }
    }
}
