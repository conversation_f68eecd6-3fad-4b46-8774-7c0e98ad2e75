﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Accounting.Journal)]
    public class JournalController : AppBaseController
    {
        private readonly IJournalService _Journalservice;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public JournalController(IJournalService Journalervice, IUserService userService/*, IImageService imageService*/)
        {
            _Journalservice = Journalervice;
            this._userService = userService;
            //_imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _Journalservice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{name}")]
        public async Task<IActionResult> GetBy(string name)
        {
            var selectedDoc = await _Journalservice.FindForAsync(name);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputJournal model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Journalservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _Journalservice.GetAllForAsync()
                              .OrderByDescending(w => w.id)
                              .Select(w => w.id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputJournal>(item["data"].ToString());
                                data.id = lastId;
                                await _Journalservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputJournal>(item["data"].ToString());
                            await _Journalservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _Journalservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputJournal cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Journalservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _Journalservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }

          
        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Journalservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Journal.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputJournal>(file.OpenReadStream());
            var result = await _Journalservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
