﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Accounting.ChartOfAccounts)]
    public class ChartofAccountsController : AppBaseController
    {
        private readonly IChartofAccountsService _myService;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public ChartofAccountsController(IChartofAccountsService myService, IUserService userService/*, IImageService imageService*/)
        {
            _myService = myService;
            this._userService = userService;
            //_imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _myService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("Tree")]
        public async Task<IActionResult> GetTree(int currentPage = 1)
        {
            var model = await _myService.GetAllForAsync()
                .Select(r => new
                {
                    r.id,
                    r.ParentID,
                    r.NameEn,
                    r.NameAr,
                    r.Code
                }).ToListAsync();
            return this.AppSuccess(model);
        }

        [HttpGet("{name}")]
        public async Task<IActionResult> GetBy(string name)
        {
            var selectedDoc = await _myService.FindForAsync(name);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(inputChartofAccounts model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, inputChartofAccounts cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _myService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _myService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Banks.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<inputChartofAccounts>(file.OpenReadStream());
            var result = await _myService.ImportListAsync(list);
            return this.AppOk(result);
        }

        [HttpGet]
        [Route("ChartofAccountsdropdown")]
        public async Task<IActionResult> ChartofAccountsdropdown()
        {
            var result = await _myService.GetAllForAsync()
                .Select(e => new
                {
                    e.Code,
                    e.NameAr,
                    e.NameEn,
                    e.ISmain
                }).Where(r=>r.ISmain == false).ToListAsync();

            return this.AppSuccess(result);
        }

        [HttpGet]
        [Route("MainChartofAccountsdropdown")]
        public async Task<IActionResult> MainChartOfAccountDropdown()
        {
            var result = await _myService.GetAllForAsync()
                .Select(e => new
                {
                    e.Code,
                    e.NameAr,
                    e.NameEn, e.ISmain
                }).Where(r => r.ISmain == true).ToListAsync();

            return this.AppSuccess(result);
        }


    }
}
