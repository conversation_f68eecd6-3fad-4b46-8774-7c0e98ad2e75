﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Accounting.FinalAccountsTypes)]
    public class FinalBalanceTypesController : AppBaseController
    {
        private readonly IFinalBalanceTypesService _myService;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public FinalBalanceTypesController(IFinalBalanceTypesService myService, IUserService userService/*, IImageService imageService*/)
        {
            _myService = myService;
            this._userService = userService;
            //_imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _myService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{name}")]
        public async Task<IActionResult> GetBy(string name)
        {
            var selectedDoc = await _myService.FindForAsync(name);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputFinalBalanceTypes model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }


        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _myService.GetAllForAsync()
                              .OrderByDescending(w => w.id)
                              .Select(w => w.id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputFinalBalanceTypes>(item["data"].ToString());
                                data.id = lastId;
                                await _myService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputFinalBalanceTypes>(item["data"].ToString());
                            await _myService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _myService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputFinalBalanceTypes modelToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.UpdateForAsync(id, modelToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _myService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }

          
        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _myService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("FinalBalanceTypes.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputFinalBalanceTypes>(file.OpenReadStream());
            var result = await _myService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
