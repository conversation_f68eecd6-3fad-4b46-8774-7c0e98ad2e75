using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Accounting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Accounting
{
    [HasPermission(AppModules.Accounting.Journal)]
    public class AccountThirdPartyController : AppBaseController
    {
        private readonly IAccountThirdPartyService _AccountThirdPartyervice;

        public AccountThirdPartyController(IAccountThirdPartyService AccountThirdPartyervice)
        {
            _AccountThirdPartyervice = AccountThirdPartyervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("Customer")]
        public async Task<IActionResult> CustomerThirdparty(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync().Where (c=> c.TypId == 2);
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("Supplier")]
        public async Task<IActionResult> SupplierThirdparty(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync().Where(c => c.TypId == 3);
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("Emp")]
        public async Task<IActionResult> EmpThirdparty(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync().Where(c => c.TypId == 4);
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("Bank")]
        public async Task<IActionResult> BankThirdparty(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync().Where(c => c.TypId == 5);
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("CashBox")]
        public async Task<IActionResult> CashBoxThirdparty(int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.GetAllForAsync().Where(c => c.TypId == 6);
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _AccountThirdPartyervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputAccountThirdParty model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AccountThirdPartyervice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputAccountThirdParty cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AccountThirdPartyervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _AccountThirdPartyervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _AccountThirdPartyervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _AccountThirdPartyervice.GetAllForAsync()
                              .OrderByDescending(w => w.id)
                              .Select(w => w.id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputAccountThirdParty>(item["data"].ToString());
                                data.id = lastId;
                                await _AccountThirdPartyervice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputAccountThirdParty>(item["data"].ToString());
                            await _AccountThirdPartyervice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _AccountThirdPartyervice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _AccountThirdPartyervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("AccountThirdParty.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputAccountThirdParty>(file.OpenReadStream());
            var result = await _AccountThirdPartyervice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
