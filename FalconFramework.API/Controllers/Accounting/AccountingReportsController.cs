﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Accounting;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.Sales
{
    public class AccountingReportsController : AppBaseController
    {
        private readonly IAccountingeReportService _reportService;
        private readonly IUserService _userService;
        public AccountingReportsController(IAccountingeReportService reportService, IUserService UserService)
        {
            this._reportService = reportService;
            this._userService = UserService;
        }

        [HasPermission(AppModules.Accounting.CustomerAccountsReport)]
        [HttpGet]
        [Route("CustomerAccount")]
        public IActionResult CustomerAccount([FromQuery] CustomerLedger model = null)
        {
            var result = _reportService.CustomerAccount("Customer_Account_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HttpGet]
        [Route("CustomerAccountExcel")]
        public IActionResult ExportToExcel([FromQuery] CustomerLedger model = null)
        {
            var result = _reportService.CustomerAccount("Customer_Account_SP", model, ReportType.StoredProcedure);
            return this.ExportToExcelMvc("CustomerAccount.xlsx", result);
        }

        [HasPermission(AppModules.Accounting.BankAccountsReport)]
        [HttpGet]
        [Route("BanckAccount")]
        public IActionResult BanckAccount([FromQuery] BankLedger model = null)
        {
            var result = _reportService.BankAccount("Bank_Accoutn_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.PayablesAccountsReport)]
        [HttpGet]
        [Route("SupplierAccount")]
        public IActionResult SupplierAccount([FromQuery] SupplierLedger model = null)
        {
            var result = _reportService.SupplierAccount("Supplier_Accoutn_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.LedgerReport)]
        [HttpGet]
        [Route("ledger")]
        public IActionResult LeadgerAccount([FromQuery] LeadgerReport model = null)
        {
            var result = _reportService.LeadgerAccount("Accont_Details", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.StaffAccountsReport)]
        [HttpGet]
        [Route("StaffAccounts")]
        public IActionResult StaffAccountsReport([FromQuery] StaffAccountsViewModel model = null)
        {
            var result = _reportService.StaffAccount("Emp_Account_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.StaffAccountsReport)]
        [HttpGet]
        [Route("SalsemanAccount")]
        public IActionResult SalsemanAccount([FromQuery] SalesmanStatement model = null)
        {
            var result = _reportService.SalsemanAccount("SalesMan_Account_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.CashBoxAccountsReport)]
        [HttpGet]
        [Route("CashBoxAccounts")]
        public IActionResult CashBoxAccounts([FromQuery] CashBoxLedger model = null)
        {
            var result = _reportService.CashBoxAccount("CashBoxLedger_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.PartnersAccountsReport)]
        [HttpGet]
        [Route("PartnerAccount")]
        public IActionResult PartnerAccount([FromQuery] PartnerLeadger model = null)
        {
            var result = _reportService.PartnerAccount("Partner_Accoutn_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.MonthlyAccountReport)]
        [HttpGet]
        [Route("MonthlyAccount")]
        public IActionResult MonthlyAccount([FromQuery] MonthlyAccount model = null)
        {
            var result = _reportService.MonthlyAccount("ACC_Monthly", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.SalariesPrintReport)]
        [HttpGet]
        [Route("Salaries")]
        public async Task<IActionResult> SalariesAsync([FromQuery] SalariesViewModel model = null)
        {

            int loggedUser = (int)await _userService
            .GetAllForAsync()
            .Where(u => u.Id == UserId)
            .Select(us => us.FalconUserId)
            .FirstOrDefaultAsync();

            var result = _reportService.Salaries("_Select_Sallery", model, ReportType.StoredProcedure, loggedUser);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.AccountsMirrorReport)]
        [HttpGet]
        [Route("AccountsMirror")]
        public IActionResult AccountsMirror([FromQuery] AccountsMirrorModel model = null)
        {
            var result = _reportService.AccountsMirror("AccountsMirror", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.SalesTaxesReport)]
        [HttpGet]
        [Route("SalesTaxesReport")]
        public IActionResult SalesTaxesReport([FromQuery] SalesTaxesReportModel model = null)
        {
            var result = _reportService.SalesTaxesReport("Tax_Report", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Accounting.JournalEntryReport)]
        [HttpGet]
        [Route("JournalEntryReport")]
        public IActionResult JournalEntryReport([FromQuery] JournalEntryViewModel model = null)
        {
            var result = _reportService.JournalEntryReport("JournalEntryReport", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.AgedReceivableReport)]
        [HttpGet]
        [Route("AgedReceivableReport")]
        public IActionResult AgedReceivableReport([FromQuery] AgedReceivableViewModel model = null)
        {
            var result = _reportService.AgedReceivable("Religion", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.AgedPayableReport)]
        [HttpGet]
        [Route("AgedPayableReport")]
        public IActionResult AgedPayableReport([FromQuery] AgedPayableViewModel model = null)
        {
            var result = _reportService.AgedPayable("Religion_Supplyer", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.GeneralLedgerReport)]
        [HttpGet]
        [Route("GeneralLedger")]
        public IActionResult GeneralLedger([FromQuery] GeneralLedgerViewModel model = null)
        {
            var result = _reportService.GeneralLedger("Account_Summary", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.NotPostedTransactions)]
        [HttpGet]
        [Route("UnpostedEntries")]
        public IActionResult UnpostedEntries([FromQuery] UnpostedEntriesViewModel model = null)
        {
            var result = _reportService.UnpostedEntries("Journal_is_Not_Migrated", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.PrepaidExpenses)]
        [HttpGet]
        [Route("PrepaidExpenses")]
        public IActionResult PrepaidExpenses([FromQuery] PrepaidExpensesViewModel model = null)
        {
            var result = _reportService.PrepaidExpenses("PrepaidExpensesSP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.PaymentsAndReceiptReport)]
        [HttpGet]
        [Route("ReceivableAndPayableVoucher")]
        public IActionResult ReceivableAndPayableVoucher([FromQuery] ReceivableAndPayableVoucherViewModel model = null)
        {
            var result = _reportService.ReceivableAndPayableVoucher("ReceivableAndPayableVoucher", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.TrialBalanceReport)]
        [HttpGet]
        [Route("TrialBalance")]
        public IActionResult TrialBalance([FromQuery] TrialBalanceViewModel model = null)
        {
            var result = _reportService.TrialBalance("Acc_Mezan", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.RevenuesAnalysisReport)]
        [HttpGet]
        [Route("RevenueAnalysis")]
        public async Task<IActionResult> RevenueAnalysisAsync([FromQuery] RevenueAnalysisViewModel model = null)
        {

            int loggedUser = (int)await _userService
            .GetAllForAsync()
            .Where(u => u.Id == UserId)
            .Select(us => us.FalconUserId)
            .FirstOrDefaultAsync();

            var result = _reportService.RevenueAnalysis("RevenueAnalysis", model, ReportType.StoredProcedure, loggedUser);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.IncomeStatementReport)]
        [HttpGet]
        [Route("ProfetAndLose")]
        public IActionResult ProfetAndLose([FromQuery] ProfetAndLoseViewModel model)
        {
            var result = _reportService.ProfitAndLoss("ProfetAndLose_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.BalanceSheetReport)]
        [HttpGet]
        [Route("BalanceSheet")]
        public IActionResult BalanceSheet([FromQuery] BalanceSheetViewModel model)
        {
            var result = _reportService.BalanceSheet("BalanceSheet_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.EquityChangesReport)]
        [HttpGet]
        [Route("EquityChanges")]
        public IActionResult EquityChanges([FromQuery] EquityChangesViewModel model)
        {
            var result = _reportService.EquityChanges("Acc_Shorakaa", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Accounting.CustomFinancialStatementsReport)]
        [HttpGet]
        [Route("CustomFinancialStatements")]
        public IActionResult CustomFinancialStatements([FromQuery] CustomFinancialStatementsViewModel model)
        {
            string stored = "";
            bool StatementFromChart = true;

            //--  هانجيب قيمه المتغير ده من جدول الاعدادت بعدين

            if (StatementFromChart == true)
            {
                stored = "Acc_Mezanya";
            }
            else
            {
                stored = "Acc_Mezanya_FromItems";
            }

            var result = _reportService.CustomFinancialStatements(stored, model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.EstimatedbudgetDeviationReport)]
        [HttpGet]
        [Route("EstimatedBudgetDeviation")]
        public IActionResult EstimatedBudgetDeviation([FromQuery] EstimatedBudgetDeviationViewModel model)
        {
            var result = _reportService.EstimatedBudgetDeviation("Rate_of_deviation", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Accounting.CashFlowsReport)]
        [HttpGet]
        [Route("CashFlowStatement")]
        public IActionResult CashFlowStatement([FromQuery] CashFlowStatementViewModel model)
        {
            var result = _reportService.CashFlowStatement("CashFlowa", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.DepositsMovementReport)]
        [HttpGet]
        [Route("DepositsMovement")]
        public IActionResult DepositsMovement([FromQuery] DepositsMovementViewModel model)
        {
            var result = _reportService.DepositsMovement("Eda3at_Sp", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.FinancialStatementAnalysis)]
        [HttpGet]
        [Route("FinancialStatementAnalysis")]
        public IActionResult FinancialStatementAnalysis([FromQuery] FinancialStatementAnalysisViewModel model)
        {
            var result = _reportService.FinancialStatementAnalysis("Financial_Ratios_rates", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.CompareAccountsmonthlyReport)]
        [HttpGet]
        [Route("MonthlyAccountComparison")]
        public IActionResult MonthlyAccountComparison([FromQuery] MonthlyAccountComparisonViewModel model)
        {
            var result = _reportService.MonthlyAccountComparison("Acc_Monthly_Compare", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Accounting.ComparingAccountsquarterlyReport)]
        [HttpGet]
        [Route("QuarterlyAccountComparison")]
        public IActionResult QuarterlyAccountComparison([FromQuery] QuarterlyAccountComparisonViewModel model)
        {
            var result = _reportService.QuarterlyAccountComparison("Acc_Quarterly", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Accounting.AccountsSummaryReport)]
        [HttpGet]
        [Route("AccountsSummary")]
        public IActionResult AccountsSummary([FromQuery] AccountsSummaryViewModel model)
        {
            var result = _reportService.AccountsSummary("AccountsSummary_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


    }
}
