using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.API.Controllers
{

    public class AnalyticAccountsController : AppBaseController
    {
        private readonly IAnalyticAccountsService _AnalyticAccountservice;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public AnalyticAccountsController(IAnalyticAccountsService AnalyticAccountservice, IUserService userService/*, IImageService imageService*/)
        {
            _AnalyticAccountservice = AnalyticAccountservice;
            this._userService = userService;
            //_imageService = imageService;
        }


        [HttpGet]
        [Route("Tree")]
        public async Task<IActionResult> GetTree(int currentPage = 1)
        {
            var model = await _AnalyticAccountservice.GetAllForAsync()
                .Select(r => new
                {
                    r.id,
                    r.ParentID,
                    r.NameEn,
                    r.NameAr,
                    r.Code
                }).ToListAsync();
            return this.AppSuccess(model);
        }

        [HttpGet("GetProjectLocation/{code}")]
        public async Task<IActionResult> getProjectLocation(string code)
        {

            var model = await _AnalyticAccountservice
          .GetAllForAsync()
          .Where(e => e.Code == code)
          .Select(r => new
          {
              Latitude = r.Latitude,
              Longitude = r.Longitude
          })
          .FirstOrDefaultAsync();

            return this.AppSuccess(model);
        }


        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _AnalyticAccountservice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{name}")]
        public async Task<IActionResult> GetBy(string name)
        {
            var selectedDoc = await _AnalyticAccountservice.FindForAsync(name);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        [HasPermission(AppModules.Accounting.AnalyticAccountsList)]
        [HttpPost]
        public async Task<IActionResult> Post(InputAnalyticAccounts model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AnalyticAccountservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        [HasPermission(AppModules.Accounting.AnalyticAccountsList)]
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputAnalyticAccounts cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AnalyticAccountservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }



        [HasPermission(AppModules.Accounting.AnalyticAccountsList)]
        [HttpPut("UpdateProjectLocation")]
        public async Task<IActionResult> UpdateProjectLocation(AnalyticAccountsLocation cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AnalyticAccountservice.UpdateForAsync(cateToUpdate.Code, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }





        [HasPermission(AppModules.Accounting.AnalyticAccountsList)]
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _AnalyticAccountservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _AnalyticAccountservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("AnalyticAccounts.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputAnalyticAccounts>(file.OpenReadStream());
            var result = await _AnalyticAccountservice.ImportListAsync(list);
            return this.AppOk(result);
        }
        [HttpGet]
        [Route("AnalyticAccounDropdown")]
        public async Task<IActionResult> AnalyticAccounDropdown()
        {
            var result = await _AnalyticAccountservice.GetAllForAsync()
                .Select(e => new
                {
                    e.Code,
                    e.NameAr,
                    e.NameEn,
                    e.ISmain
                }).Where(r => r.ISmain == false).ToListAsync();

            return this.AppSuccess(result);
        }

        [HttpGet]
        [Route("AnalyticAccountList")]
        public async Task<IActionResult> AnalyticAccountList()
        {
            try
            {
                var result = await _AnalyticAccountservice.AnalyticAccountList();
                return this.AppSuccess(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


    }
}
