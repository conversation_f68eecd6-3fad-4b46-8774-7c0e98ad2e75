using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Accounting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Accounting
{
    [HasPermission(AppModules.Accounting.Taxes)]
    public class TaxesController : AppBaseController
    {
        private readonly ITaxesService _Taxeservice;
        private readonly ITaxGroupService _TaxGroupervice;

        public TaxesController(ITaxesService Taxeservice, ITaxGroupService taxGroupervice)
        {
            _Taxeservice = Taxeservice;
            _TaxGroupervice = taxGroupervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _Taxeservice.GetAllForAsync();
            var groups = await _TaxGroupervice.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info: groups);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _Taxeservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputTaxes model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Taxeservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputTaxes cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Taxeservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _Taxeservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _Taxeservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                 
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                          
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputTaxes>(item["data"].ToString());
                                 
                                await _Taxeservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputTaxes>(item["data"].ToString());
                            await _Taxeservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _Taxeservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Taxeservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Taxes.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputTaxes>(file.OpenReadStream());
            var result = await _Taxeservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
