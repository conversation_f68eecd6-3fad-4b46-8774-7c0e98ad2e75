﻿using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;


namespace FalconFramework.API.Controllers.Accounting
{
    public class FinancialYearController : AppBaseController
    {

        private readonly IFinancialYearService _financialYearService;

        public FinancialYearController(IFinancialYearService financialYearService)
        {
            this._financialYearService = financialYearService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
           var result =  _financialYearService.GetAllForAsync();
              
            return await this.GetPagedData(currentPage, result);
        }

        [HttpGet("{list}")]
        public async Task<IActionResult> Get()
        {
            var result = await _financialYearService.GetAllForAsync()
                .Select(e => e.YearName).ToListAsync();

            return this.AppSuccess(result);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                           
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<FinancialYearModel>(item["data"].ToString());
                                await _financialYearService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<FinancialYearModel>(item["data"].ToString());
                            await _financialYearService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _financialYearService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }
    }
}
