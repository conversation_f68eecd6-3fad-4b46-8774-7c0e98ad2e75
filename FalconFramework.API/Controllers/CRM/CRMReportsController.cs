﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.CRM;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.CRM
{
    public class CRMReportsController : AppBaseController
    {
        private readonly ICRMReportService _reportService;

        public CRMReportsController(ICRMReportService reportService)
        {
            this._reportService = reportService;
        }

        [HasPermission(AppModules.CRM.SaleCRM)]
        [HttpGet]
        [Route("crmSales")]
        public IActionResult CrmSales([FromQuery] CrmSalesInputReport model = null)
        {
            var result = _reportService.CrmSales("_cutting", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


    }
}
