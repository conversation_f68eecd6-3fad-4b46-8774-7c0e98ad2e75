using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.CRM;
using FalconFramework.ApplicationCore.DTOs.CRM;

namespace FalconFramework.API.Controllers.CRM
{
    [HasPermission(AppModules.CRM.WhatsappInstance)]
    public class WhatsappInstanceController : AppBaseController
    {
        private readonly IWhatsappInstanceService _WhatsappInstanceervice;

        public WhatsappInstanceController(IWhatsappInstanceService WhatsappInstanceervice)
        {
            _WhatsappInstanceervice = WhatsappInstanceervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _WhatsappInstanceervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _WhatsappInstanceervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputWhatsappInstance model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _WhatsappInstanceervice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputWhatsappInstance cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _WhatsappInstanceervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _WhatsappInstanceervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _WhatsappInstanceervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _WhatsappInstanceervice.GetAllForAsync()
                              .OrderByDescending(w => w.id)
                              .Select(w => w.id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputWhatsappInstance>(item["data"].ToString());
                                data.id = lastId;
                                await _WhatsappInstanceervice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputWhatsappInstance>(item["data"].ToString());
                            await _WhatsappInstanceervice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _WhatsappInstanceervice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _WhatsappInstanceervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("WhatsappInstance.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputWhatsappInstance>(file.OpenReadStream());
            var result = await _WhatsappInstanceervice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
