using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.CRM;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.CRM;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DocumentFormat.OpenXml.Office2010.Excel;
using FalconFramework.API.Models;
using Integration.Sms.Models;
using Integration.Sms;
using Microsoft.AspNetCore.Http.HttpResults;

namespace FalconFramework.API.Controllers.CRM
{
    [HasPermission(AppModules.CRM.SMSInstance)]
    public class SMSInstanceController : AppBaseController
    {
        private readonly ISMSInstanceService _SMSInstanceervice;


        public SMSInstanceController(ISMSInstanceService SMSInstanceervice)
        {
            _SMSInstanceervice = SMSInstanceervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _SMSInstanceervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _SMSInstanceervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputSMSInstance model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var oldmodel = await _SMSInstanceervice.DeleteForAsync(model.CaseID, UserId);

                    var result = await _SMSInstanceervice.CreateForAsync(model);

                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputSMSInstance cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SMSInstanceervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _SMSInstanceervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _SMSInstanceervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }



        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _SMSInstanceervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("SMSInstance.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputSMSInstance>(file.OpenReadStream());
            var result = await _SMSInstanceervice.ImportListAsync(list);
            return this.AppOk(result);
        }

        [HttpGet]
        [Route("Balance/{caseId}")]
        public async Task<IActionResult> GetBalance(int caseId)
        {
            var provider = await _SMSInstanceervice.FindForAsync(caseId);
            if (provider != null)
            {
                var balance = await SmsHelper.GetBalanceAsync((SmsProvider)caseId, provider.UserName, provider.Password);
                return this.AppSuccess(balance);
            }
            return this.AppNotFound();
        }
        [HttpPost]
        [Route("Send")]
        public async Task<IActionResult> SendSMS(SMSModel model)
        {
            var provider = await _SMSInstanceervice.FindForAsync(model.ProviderCaseId);
            if (provider != null)
            {

                if (model.RecipientType == "individual")
                {
                    var body = model.template;
                    var result = await SmsHelper.SendMessageAsync((SmsProvider)model.ProviderCaseId, new SmsMessage
                    {
                        Numbers = new List<string> { model.RecipientNumber },
                        Body = body,
                        UserName = provider.UserName,
                        Password = provider.Password,
                        SenderName = provider.SenderName,
                        //Balance = 10,
                    });
                    return this.AppSuccess(result);

                }
                else
                {
                    foreach (var item in model.Phones)
                    {
                        var body = model.template;
                        await SmsHelper.SendMessageAsync((SmsProvider)model.ProviderCaseId, new SmsMessage
                        {
                            Numbers = new List<string> { item },
                            Body = body,
                            UserName = provider.UserName,
                            Password = provider.Password,
                            SenderName = provider.SenderName,
                            //Balance = 10,
                        });
                    }
                }
                return this.AppSuccess(null);
            }
            return this.AppNotFound();
        }

    }
}
