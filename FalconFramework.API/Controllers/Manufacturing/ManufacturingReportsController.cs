﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.Manufacturing;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.Manufacturing
{
    public class ManufacturingReportsController : AppBaseController
    {
        private readonly IManufacturingReportService _reportService;

        public ManufacturingReportsController(IManufacturingReportService reportService)
        {
            this._reportService = reportService;
        }

        //[HasPermission(AppModules.HR.DeductionsReports)]
        //[HttpGet]
        //[Route("Manufacturing")]
        //public async Task<IActionResult> Deductions([FromQuery]InputReport model = null)
        //{
        //    var result = _reportService.GetReport("_cutting", model, ReportType.StoredProcedure);

        //    return this.AppSuccess(result);
        //}

        
    }
}
