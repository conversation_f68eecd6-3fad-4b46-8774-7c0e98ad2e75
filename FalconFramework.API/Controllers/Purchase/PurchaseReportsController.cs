﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Purchase;
using FalconFramework.ReportModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.API.Controllers.Purchase
{
    public class PurchaseReportsController : AppBaseController
    {
        private readonly IPurchaseReportService _reportService;
        private readonly IUserService _userService;
        private readonly IFalconUsersService _falconUsersService;

        public PurchaseReportsController(IPurchaseReportService reportService, IUserService userService, IFalconUsersService falconUsersService)
        {
            this._reportService = reportService;
            this._userService = userService;
            this._falconUsersService = falconUsersService;
        }


        [HasPermission(AppModules.Purchase.PurchasesReport)]
        [HttpGet]
        [Route("PurchasesReport")]
        public async Task<IActionResult> ProductsReportAsync([FromQuery] PurchasesReportInputModel model = null)
        {

            int FalconUserId = (int)await _userService
         .GetAllForAsync()
         .Where(u => u.Id == UserId)
         .Select(us => us.FalconUserId)
         .FirstOrDefaultAsync();

            bool IsAdmin = (bool)await _falconUsersService
       .GetAllForAsync()
       .Where(u => u.id == FalconUserId)
       .Select(us => us.IsAdmin)
       .FirstOrDefaultAsync();

            if (IsAdmin == false) { FalconUserId = 0; }

            var result = _reportService.PurchasesReport("Purchased", model, ReportType.StoredProcedure, FalconUserId);

            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Purchase.PurchasesReturnsReport)]
        [HttpGet]
        [Route("PurchasesReturnsReport")]
        public async Task<IActionResult> PurchasesReturnsReportAsync([FromQuery] PurchasesReturnsReportInputModel model = null)
        {

            int FalconUserId = (int)await _userService
       .GetAllForAsync()
       .Where(u => u.Id == UserId)
       .Select(us => us.FalconUserId)
       .FirstOrDefaultAsync();

            bool IsAdmin = (bool)await _falconUsersService
       .GetAllForAsync()
       .Where(u => u.id == FalconUserId)
       .Select(us => us.IsAdmin)
       .FirstOrDefaultAsync();

            var result = _reportService.PurchasesReturnsReport("Purchased_Back", model, ReportType.StoredProcedure, FalconUserId);
            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Purchase.PurchasesTaxReport)]
        [HttpGet]
        [Route("PurchasesTaxReport")]
        public IActionResult PurchasesTaxReport([FromQuery] PurchasesTaxReportInputModel model = null)
        {
            var result = _reportService.PurchasesTaxReport("Tax_Come", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Purchase.SuppliersAccountInMothesReport)]
        [HttpGet]
        [Route("SuppliersAccountInMonthsReport")]
        public IActionResult SuppliersAccountInMonthsReport([FromQuery] SuppliersAccountInMonthsReportInputModel model = null)
        {
            var result = _reportService.SuppliersAccountInMonthsReport("VendersAccountMonths", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Purchase.PurchasesPeriodicallyReport)]
        [HttpGet]
        [Route("PurchasesPeriodicallyReport")]
        public IActionResult PurchasesPeriodicallyReport([FromQuery] PurchasesPeriodicallyReportInputModel model = null)
        {
            var result = _reportService.PurchasesPeriodicallyReport("Purchased_Supp", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Purchase.purchasedProductsReport)]
        [HttpGet]
        [Route("PurchasedProductsReport")]
        public IActionResult PurchasedProductsReport([FromQuery] PurchasedProductsReportInputModel model = null)
        {
            var result = _reportService.PurchasedProductsReport("Purchased_Items", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Purchase.purchasedProductsReport)]
        [HttpGet]
        [Route("SuppliersProductsReport")]
        public IActionResult SuppliersProductsReport([FromQuery] SuppliersProductsReportInputModel model = null)
        {
            string sql = "";
            if (model.ProductId == 0 & model.SupplierId == 0)
            {
                sql = "select  * from View_Item_Vendor order by Item_Id";
            }
            else if (model.ProductId == 1 & model.SupplierId == 0)
            {
                sql = $"select *  from View_Item_Vendor where Item_Id = {model.ProductId} order by Item_Id";
            }
            else if (model.ProductId == 1 & model.SupplierId == 1)
            {
                sql = $"select *  from View_Item_Vendor where Item_Id = {model.ProductId} and suppliers_id = {model.SupplierId} order by Item_Id";
            }
            else if (model.ProductId == 0 & model.SupplierId == 1)
            {
                sql = $"select *  from View_Item_Vendor where suppliers_id = {model.SupplierId} order by Item_Id";
            }
            else
            {
                sql = "select  * from View_Item_Vendor order by Item_Id";
            }
            var result = _reportService.SuppliersProductsReport(sql, model, ReportType.Query);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Purchase.PurchaseRequisitionsReport)]
        [HttpGet]
        [Route("PurchaseRequisitionsReport")]
        public IActionResult PurchaseRequisitionsReport([FromQuery] PurchaseRequisitionsReportInputModel model = null)
        {
            var result = _reportService.PurchaseRequisitionsReport("PurchaseRequisitionsReport", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }






        [HasPermission(AppModules.Purchase.PurchasesReport)]
        [HttpGet]
        [Route("PurchaseOrderReport")]
        public async Task<IActionResult> PurchaseOrderReportAsync([FromQuery] PurchaseOrderReportInputModel model = null)
        {

            int FalconUserId = (int)await _userService
     .GetAllForAsync()
     .Where(u => u.Id == UserId)
     .Select(us => us.FalconUserId)
     .FirstOrDefaultAsync();

            bool IsAdmin = (bool)await _falconUsersService
       .GetAllForAsync()
       .Where(u => u.id == FalconUserId)
       .Select(us => us.IsAdmin)
       .FirstOrDefaultAsync();

            var result = _reportService.PurchaseOrderReport("Amr_Shra_Costs_Select", model, ReportType.StoredProcedure, FalconUserId);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Purchase.PurchaseorderspaymentsReport)]
        [HttpGet]
        [Route("PurchaseOrdersPaymentsReport")]
        public IActionResult PurchaseOrdersPaymentsReport([FromQuery] PurchaseOrdersPaymentsReportInputModel model = null)
        {
            var result = _reportService.PurchaseOrdersPaymentsReport("PO_PayMent", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Purchase.LandedCostReport)]
        [HttpGet]
        [Route("LandedCostReport")]
        public IActionResult LandedCostReport([FromQuery] LandedCostReportInputModel model = null)
        {
            var result = _reportService.LandedCostReport("SELECT tblAccTree.AccName AS AccountName,TblCostTree.AccName AS CostName  ,InvoiceCome_Cost.* FROM dbo.InvoiceCome_Cost\r\n       INNER JOIN dbo.tblAccTree ON tblAccTree.AccCode = InvoiceCome_Cost.AccCode\r\n      Left Outer JOIN dbo.TblCostTree ON TblCostTree.AccCode = InvoiceCome_Cost.costid\r\n      WHERE idate BETWEEN @Date1 AND @Date2", model, ReportType.Query);
            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Purchase.FollowUpbillsReport)]
        [HttpGet]
        [Route("FollowUpBillsReport")]
        public IActionResult FollowUpBillsReport([FromQuery] FollowUpBillsReportInputModel model = null)
        {
            var result = _reportService.FollowUpBillsReport("Invoice_Come_Follow", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }

    }
}
