﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.DTOs.Purchase;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Purchase.SuppliersClassification)]
    public class SupplierClassificationsController : AppBaseController
    {
        private readonly ISupplierClassificationService _SupplierClassificationsService;
        public SupplierClassificationsController(ISupplierClassificationService SupplierClassificationService)
        {
            _SupplierClassificationsService = SupplierClassificationService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _SupplierClassificationsService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _SupplierClassificationsService.FindForAsync(id);
            if (selectedDoc != null)
            {
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }
        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _SupplierClassificationsService.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputSupplierClassification>(item["data"].ToString());
                                data.Id = lastId;
                                await _SupplierClassificationsService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<long>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputSupplierClassification>(item["data"].ToString());
                            await _SupplierClassificationsService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<long>();
                            await _SupplierClassificationsService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputSupplierClassification model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SupplierClassificationsService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputSupplierClassification cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SupplierClassificationsService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _SupplierClassificationsService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _SupplierClassificationsService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("SupplierClassifications.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputSupplierClassification>(file.OpenReadStream());
            var result = await _SupplierClassificationsService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
