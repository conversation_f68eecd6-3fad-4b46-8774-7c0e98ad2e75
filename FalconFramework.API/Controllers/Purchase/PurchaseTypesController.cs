﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.DTOs.Purchase;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Purchase.PurchasesTypes)]
    public class PurchaseTypesController : AppBaseController
    {
        private readonly IPurchaseTypeService _purchaseTypesService;
        public PurchaseTypesController(IPurchaseTypeService purchaseTypeService)
        {
            _purchaseTypesService = purchaseTypeService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _purchaseTypesService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _purchaseTypesService.FindForAsync(id);
            if (selectedDoc != null)
            {
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }
        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _purchaseTypesService.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputPurchaseType>(item["data"].ToString());
                                data.Id = lastId;
                                await _purchaseTypesService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputPurchaseType>(item["data"].ToString());
                            await _purchaseTypesService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _purchaseTypesService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputPurchaseType model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _purchaseTypesService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputPurchaseType cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _purchaseTypesService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _purchaseTypesService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _purchaseTypesService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("purchaseTypes.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputPurchaseType>(file.OpenReadStream());
            var result = await _purchaseTypesService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
