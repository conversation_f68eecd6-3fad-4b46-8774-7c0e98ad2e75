﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces.Project;
using Microsoft.AspNetCore.Mvc;

namespace FalconFramework.API.Controllers.Project
{
    public class ProjectReportsController : AppBaseController
    {
        private readonly IProjectReportService _reportService;

        public ProjectReportsController(IProjectReportService reportService)
        {
            this._reportService = reportService;
        }

        //[HasPermission(AppModules.HR.DeductionsReports)]
        //[HttpGet]
        //[Route("Project")]
        //public IActionResult Deductions([FromQuery] InputReport model = null)
        //{
        //    var result = _reportService.GetReport("_cutting", model, ReportType.StoredProcedure);

        //    return this.AppSuccess(result);
        //}


    }
}
