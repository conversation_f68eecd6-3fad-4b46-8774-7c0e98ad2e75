﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.Sales
{
    public class SalesReportsController : AppBaseController
    {
        private readonly ISalesReportService _reportService;
        private readonly IUserService _userService;
        private readonly IFalconUsersService _falconUsersService;

        public SalesReportsController(ISalesReportService reportService, IUserService userService, IFalconUsersService falconUsersService)
        {
            this._reportService = reportService;
            this._userService = userService;
            this._falconUsersService = falconUsersService;
        }


        [HasPermission(AppModules.Sales.SalesReport)]
        [HttpGet]
        [Route("SalesReport")]
        public async Task<IActionResult> SalesReportAsync([FromQuery] SalesReportInputReport model = null)
        {



            if (model == null)
            {
                model = new SalesReportInputReport();
            }

            int FalconUserId = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.FalconUserId)
           .FirstOrDefaultAsync();

            bool IsAdmin = (bool)await _falconUsersService
       .GetAllForAsync()
       .Where(u => u.id == FalconUserId)
       .Select(us => us.IsAdmin)
       .FirstOrDefaultAsync();

            if (IsAdmin == false) { FalconUserId = 0; }


            var result = _reportService.SalesReport("DaylySales", model, ReportType.StoredProcedure, FalconUserId);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Sales.SalesReturnReport)]
        [HttpGet]
        [Route("SalesReturnReport")]
        public async Task<IActionResult> SalesReturnReportAsync([FromQuery] SalesReturnReportInputReport model = null)
        {
            if (model == null)
            {
                model = new SalesReturnReportInputReport();
            }

            int FalconUserId = (int)await _userService
          .GetAllForAsync()
          .Where(u => u.Id == UserId)
          .Select(us => us.FalconUserId)
          .FirstOrDefaultAsync();

            bool IsAdmin = (bool)await _falconUsersService
          .GetAllForAsync()
          .Where(u => u.id == FalconUserId)
          .Select(us => us.IsAdmin)
          .FirstOrDefaultAsync();

            if (IsAdmin == false) { FalconUserId = 0; }


            var result = _reportService.SalesReturnReport("DaylySales_Back", model, ReportType.StoredProcedure, FalconUserId);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.InvoicesReport)]
        [HttpGet]
        [Route("InvoicesReport")]
        public IActionResult InvoicesReport([FromQuery] InvoicesReportInputReport model = null)
        {
            if (model == null)
            {
                model = new InvoicesReportInputReport();
            }

            var result = _reportService.InvoicesReport("Invoice_Follow", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }






        [HasPermission(AppModules.Sales.ProductSaleReport)]
        [HttpGet]
        [Route("ProductSaleReport")]
        public IActionResult ProductSaleReport([FromQuery] ProductSaleReportInputReport model = null)
        {
            if (model == null)
            {
                model = new ProductSaleReportInputReport();
            }

            var result = _reportService.ProductSaleReport("TotalSalesItem", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.CustomerProducts)]
        [HttpGet]
        [Route("CustomerProductsReport")]
        public IActionResult CustomerProductsReport([FromQuery] CustomerProductsReportInputReport model = null)
        {
            if (model == null)
            {
                model = new CustomerProductsReportInputReport();
            }

            var result = _reportService.CustomerProductsReport("Cust_Item_Report_SP", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.AnnualSalesAnalysis)]
        [HttpGet]
        [Route("AnnualSalesAnalysis")]
        public IActionResult AnnualSalesReport([FromQuery] AnnualSalesReportInputReport model = null)
        {
            if (model == null)
            {
                model = new AnnualSalesReportInputReport();
            }

            var result = _reportService.AnnualSalesReport("YearlySales", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.SalespersonCustomer)]
        [HttpGet]
        [Route("SalespersonCustomerReport")]
        public IActionResult SalespersonCustomerReport([FromQuery] SalespersonCustomerReportInputReport model = null)
        {
            if (model == null)
            {
                model = new SalespersonCustomerReportInputReport();
            }

            var result = _reportService.SalespersonCustomerReport("select * from View_Customer_Mandop where MAndopID =@MAndopID", model, ReportType.Query);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.InvoiceEarningsReport)]
        [HttpGet]
        [Route("InvoiceEarningsReport")]
        public IActionResult InvoiceEarningsReport([FromQuery] InvoiceEarningsReportInputReport model = null)
        {
            if (model == null)
            {
                model = new InvoiceEarningsReportInputReport();
            }

            var result = _reportService.InvoiceEarningsReport("Invoice_Omolat", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.SalespersonCommissionsPercentage)]
        [HttpGet]
        [Route("SalespersonCommissionsPercentage")]
        public IActionResult SalespersonCommissionsPercentage([FromQuery] SalespersonCommissionsPercentageInputReport model = null)
        {
            if (model == null)
            {
                model = new SalespersonCommissionsPercentageInputReport();
            }

            var result = _reportService.SalespersonCommissionsPercentage("Invoice_Omolat_Total", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }







        [HasPermission(AppModules.Sales.SalespersonCommissionsValue)]
        [HttpGet]
        [Route("SalespersonCommissionsValue")]
        public IActionResult SalespersonCommissionsValue([FromQuery] SalespersonCommissionsValueInputReport model = null)
        {
            if (model == null)
            {
                model = new SalespersonCommissionsValueInputReport();
            }

            var result = _reportService.SalespersonCommissionsValue("Invoice_Omolat_Total_Value", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Sales.TotalSalesReport)]
        [HttpGet]
        [Route("TotalSalesReport")]
        public IActionResult TotalSalesReport([FromQuery] TotalSalesReportInputReport model = null)
        {
            if (model == null)
            {
                model = new TotalSalesReportInputReport();
            }

            var result = _reportService.TotalSalesReport("Sales_Customer", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.DeliveryOrderReport)]
        [HttpGet]
        [Route("DeliveryOrderReport")]
        public IActionResult DeliveryOrderReport([FromQuery] DeliveryOrderReportInputReport model = null)
        {
            if (model == null)
            {
                model = new DeliveryOrderReportInputReport();
            }

            var result = _reportService.DeliveryOrderReport("orders_Offers_Sp", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }






        [HasPermission(AppModules.Sales.RentReport)]
        [HttpGet]
        [Route("RentReport")]
        public IActionResult RentReport([FromQuery] RentReportInputReport model = null)
        {
            if (model == null)
            {
                model = new RentReportInputReport();
            }

            var result = _reportService.RentReport("Loan_materials", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.ProductOrdersReport)]
        [HttpGet]
        [Route("ProductOrdersReport")]
        public async Task<IActionResult> ProductOrdersReportAsync([FromQuery] ProductOrdersReportInputReport model = null)
        {
            if (model == null)
            {
                model = new ProductOrdersReportInputReport();
            }

            int FalconUserId = (int)await _userService
         .GetAllForAsync()
         .Where(u => u.Id == UserId)
         .Select(us => us.FalconUserId)
         .FirstOrDefaultAsync();

            var result = _reportService.ProductOrdersReport("ProductOrdersReport", model, ReportType.StoredProcedure, FalconUserId);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Sales.NetSales)]
        [HttpGet]
        [Route("NetSales")]
        public IActionResult NetSales([FromQuery] NetSalesInputReport model = null)
        {
            if (model == null)
            {
                model = new NetSalesInputReport();
            }

            var result = _reportService.NetSales("SalesNet", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }




        [HasPermission(AppModules.Sales.ContractsReport)]
        [HttpGet]
        [Route("ContractsReport")]
        public IActionResult ContractsReport([FromQuery] ContractsReportInputReport model = null)
        {
            if (model == null)
            {
                model = new ContractsReportInputReport();
            }

            var result = _reportService.ContractsReport("JadwalaReport", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }





        [HasPermission(AppModules.Sales.DeliveryReport)]
        [HttpGet]
        [Route("DeliveryReport")]
        public IActionResult DeliveryReport([FromQuery] DeliveryReportInputReport model = null)
        {
            if (model == null)
            {
                model = new DeliveryReportInputReport();
            }

            var result = _reportService.DeliveryReport("Delivery_bill_Rep_SP", model, ReportType.StoredProcedure);
            return this.AppSuccess(result);
        }



    }
}
