using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.Interfaces.HR;

namespace FalconFramework.API.Controllers.Sales
{
    public class CustomerController : AppBaseController
    {
        private readonly ICustomerService _CustomerService;

        public CustomerController(ICustomerService CustomerService)
        {
            this._CustomerService = CustomerService;
        }

        [HttpGet]
        [Route("ContactsDropdown")]
        public IActionResult ContactsDropdown([FromQuery] ContactsDropdownInputReport model = null)
        {

            var result = _CustomerService.ContactsDropdown("select Customer_id as ID , 'Customer' +' : '+ cast(Customer_id as nvarchar(10)) +' : '+ customer_name  as Name from customers  union    select suppliers_id , 'Supplier' +' : '+ cast(suppliers_id as nvarchar(10)) +' : '+ suppliers_name    from suppliers union  select emp_code , 'Employee' +' : '+ cast(emp_code as nvarchar(10)) +' : '+ emp_name       from emp union   select Id , 'Partner' +' : '+ cast(Id as nvarchar(10))+PartnerName  from Partners ", model, ReportType.Query);

 //var result = _CustomerService.GetUsingSQL("select Customer_id , customer_name , Customer_Name_EN , 'Customer' as type  from customers\r\nunion \r\nselect suppliers_id , suppliers_name , Supplier_Name_EN , 'Supplier' as type  from suppliers\r\nunion \r\nselect emp_code , emp_name , SecondName , 'Employee' as type  from emp\r\nunion \r\nselect Id , PartnerName , PartnerName , 'Partner' as type  from Partners", model, ReportType.Query);

            return this.AppSuccess(result);
        }


        [HttpGet]
        [Route("Dropdown")]
        public async Task<IActionResult> CustomerDropdown()
        {
            try
            {
                var result = await _CustomerService.GetAllForAsync()
                    .Where(e => e.Id > 0) 
                    .Select(e => new
                    {
                        e.Id,
                        NameAr = e.NameAr ?? string.Empty,
                        NameEn = e.NameEn ?? string.Empty
                    }).ToListAsync();

                return this.AppSuccess(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CustomerDropdown: {ex.Message}");
                
                return this.AppFailed($"Error retrieving customer dropdown: {ex.Message}");
            }
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            try
            {
                var model = _CustomerService.GetAllForAsync()
                    .Where(c => c.Id > 0);
                
                return await this.GetPagedData(currentPage, model);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in Get: {ex.Message}");
                
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                
                return this.AppFailed($"Error retrieving customers: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _CustomerService.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputCustomer model)
        {
            try
            {
                long lastId = await _CustomerService.GetAllForAsync()
                              .Where(w => w.Id > 0)
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                model.Id = lastId + 1;
                if (ModelState.IsValid)
                {
                    var result = await _CustomerService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputCustomer cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _CustomerService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _CustomerService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            try
            {
                var model = _CustomerService.Search(input.Term)
                    .Where(c => c.Id > 0); // Filtrar posibles Ids nulos
                    
                return await this.GetPagedData(currentPage, model, 1000);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in Search: {ex.Message}");
                
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                
                return this.AppFailed($"Error searching customers: {ex.Message}");
            }
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _CustomerService.GetAllForAsync()
                              .Where(w => w.Id > 0) 
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputCustomer>(item["data"].ToString());
                                data.Id = lastId;
                                await _CustomerService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<long>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputCustomer>(item["data"].ToString());
                            await _CustomerService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<long>();
                            await _CustomerService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            try
            {
                var result = await _CustomerService.GetAllForAsync()
                    .Where(c => c.Id > 0) 
                    .ToListAsync();
                    
                return this.ExportToExcelMvc("CustomersCategory.xlsx", result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExportToExcel: {ex.Message}");
                
                return this.AppFailed($"Error exporting to Excel: {ex.Message}");
            }
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputCustomer>(file.OpenReadStream());
            var result = await _CustomerService.ImportListAsync(list);
            return this.AppOk(result);
        }

    }
}
