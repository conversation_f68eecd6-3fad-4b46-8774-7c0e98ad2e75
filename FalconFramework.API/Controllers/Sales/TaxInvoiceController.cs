﻿using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Mvc;

namespace FalconFramework.API.Controllers.Sales
{
    public class TaxInvoiceController : AppBaseController
    {
        private readonly ITaxInvoiceService _TaxInvoiceService;

        public TaxInvoiceController(ITaxInvoiceService TaxInvoiceService)
        {
            this._TaxInvoiceService = TaxInvoiceService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _TaxInvoiceService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet]
        [Route("InuputProducts")]
        public IActionResult GetinpoutProductView()
        {
            var data = new List<InputProductViewModel>();
            return this.AppSuccess(data);
        }

        [HttpGet]
        [Route("GetInvoiceList")]
        public IActionResult GetInvoiceList([FromQuery] InvoiceListInputReport model = null)
        {
            
            var result = _TaxInvoiceService.InvoiceList("select i.InvoiceNo , case when i.CaseId = 2 then c.customer_name when i.CaseId = 7 then s.suppliers_name when i.CaseId = 16  then e.emp_name end as 'Customer',case when i.CaseId = 2 then 'Customer' when i.CaseId = 7  then 'Supplier' when i.CaseId = 16  then 'employee' end as 'Type' , i.idate as InvoiceDate , i.CollectedDate as DueDate , '' as Activities , i.aTotal as 'TaxExcluded' , i.saltax as 'Tax', i.net as 'Total' , i.Tahseel as Payment , '' as 'Status' , '' as SourceDocument , i.notes as 'Reference', i.Mandop_Name as 'Salesperson', i.COMP_ID as 'Company','' as Currency,'' as 'EInvoicing' ,i.CaseId from invoiceout i \r\nleft join customers c on c.Customer_id = i.ClientID and CaseId = 2\r\nleft join suppliers s on s.suppliers_id = i.ClientID and CaseId = 7\r\nleft join emp e on e.emp_code = i.ClientID and CaseId = 16 where i.idate between @FromDate and @ToDate ", model, ReportType.Query);


            return this.AppSuccess(result);
        }
    }
}
