using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Sales
{
    [HasPermission(AppModules.Sales.SalesTeams)]
    public class SalesTeamsController : AppBaseController
    {
        private readonly ISalesTeamsService _SalesTeamservice;
        private readonly IUserService _userService;

        public SalesTeamsController(ISalesTeamsService SalesTeamservice, IUserService userService)
        {
            _SalesTeamservice = SalesTeamservice;
            _userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        { 
            var groups = await _userService.GetAllForAsync()
                .Select(u => new { u.FalconUserId, u.FullName })
                .ToListAsync();
            var model = _SalesTeamservice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model, info: groups);

         
            
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _SalesTeamservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputSalesTeams model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalesTeamservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputSalesTeams cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalesTeamservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _SalesTeamservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _SalesTeamservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                          
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputSalesTeams>(item["data"].ToString());
                               
                                await _SalesTeamservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputSalesTeams>(item["data"].ToString());
                            await _SalesTeamservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _SalesTeamservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _SalesTeamservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("SalesTeams.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputSalesTeams>(file.OpenReadStream());
            var result = await _SalesTeamservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
