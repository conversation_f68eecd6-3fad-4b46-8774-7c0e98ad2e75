using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Sales
{
    [HasPermission(AppModules.Sales.SalespersonCommission)]
    public class SalespersonCommissionsController : AppBaseController
    {
        private readonly ISalespersonCommissionsService _SalespersonCommissionservice;
        private readonly ISalespersonService _SalespersonService;

        public SalespersonCommissionsController(ISalespersonCommissionsService SalespersonCommissionservice , ISalespersonService salespersonService)
        {
            _SalespersonCommissionservice = SalespersonCommissionservice;
            _SalespersonService = salespersonService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _SalespersonCommissionservice.GetAllForAsync();
           
            var salesperson = await _SalespersonService.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info:salesperson) ;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _SalespersonCommissionservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputSalespersonCommissions model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalespersonCommissionservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputSalespersonCommissions cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalespersonCommissionservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _SalespersonCommissionservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _SalespersonCommissionservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _SalespersonCommissionservice.GetAllForAsync()
                              .OrderByDescending(w => w.id)
                              .Select(w => w.id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputSalespersonCommissions>(item["data"].ToString());
                                data.id = lastId;
                                await _SalespersonCommissionservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputSalespersonCommissions>(item["data"].ToString());
                            await _SalespersonCommissionservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _SalespersonCommissionservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _SalespersonCommissionservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("SalespersonCommissions.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputSalespersonCommissions>(file.OpenReadStream());
            var result = await _SalespersonCommissionservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
