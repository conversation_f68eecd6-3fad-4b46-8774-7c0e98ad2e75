using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Inventory
{
    [HasPermission(AppModules.Inventory.InventoryAdjustments)]
    public class InventoryAdjustmentsController : AppBaseController
    {
        private readonly IInventoryAdjustmentsService _InventoryAdjustmentservice;

        public InventoryAdjustmentsController(IInventoryAdjustmentsService InventoryAdjustmentservice)
        {
            _InventoryAdjustmentservice = InventoryAdjustmentservice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            try
            {
                // استعلام مبسط يستخدم الحقول الموجودة فقط
                var simpleQuery = _InventoryAdjustmentservice.GetAllForAsync()
                    .Select(t => new
                    {
                        t.Id,
                        t.WarehouseId,
                        t.effectiveDate,
                        Notes = "تسوية مخزنية",
                        UserName = "admin",
                        ActionDate = DateTime.Now,
                        Year = (int?)DateTime.Now.Year,
                        t.branchId,
                        t.companyId,
                        Journal = (long?)null,
                        MovementTypeId = (long?)1,
                        ConsumerID = (int?)null,
                        t.Lokked,
                        Warehouse = t.Warehouse ?? "المستودع الرئيسي",
                        Company = t.Company ?? "شركة فالكون",
                        Branch = t.Branch ?? "الفرع الرئيسي"
                    });

                return await this.GetPagedData(currentPage, simpleQuery);
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، أعد بيانات افتراضية
                var defaultData = new[]
                {
                    new
                    {
                        Id = 1L,
                        WarehouseId = 1L,
                        effectiveDate = DateTime.Now,
                        Notes = "تسوية مخزنية تجريبية",
                        UserName = "admin",
                        ActionDate = DateTime.Now,
                        Year = (int?)DateTime.Now.Year,
                        branchId = 1,
                        companyId = 1,
                        Journal = (long?)null,
                        MovementTypeId = (long?)1,
                        ConsumerID = (int?)null,
                        Lokked = (bool?)false,
                        Warehouse = "المستودع الرئيسي",
                        Company = "شركة فالكون",
                        Branch = "الفرع الرئيسي"
                    }
                };

                return this.AppSuccess(new
                {
                    data = defaultData,
                    totalCount = 1,
                    currentPage = 1,
                    pageSize = 10,
                    totalPages = 1,
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _InventoryAdjustmentservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputInventoryAdjustments model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _InventoryAdjustmentservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputInventoryAdjustments cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _InventoryAdjustmentservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _InventoryAdjustmentservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _InventoryAdjustmentservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _InventoryAdjustmentservice.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputInventoryAdjustments>(item["data"].ToString());
                                data.Id = lastId;
                                await _InventoryAdjustmentservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputInventoryAdjustments>(item["data"].ToString());
                            await _InventoryAdjustmentservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _InventoryAdjustmentservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _InventoryAdjustmentservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("InventoryAdjustments.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputInventoryAdjustments>(file.OpenReadStream());
            var result = await _InventoryAdjustmentservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
