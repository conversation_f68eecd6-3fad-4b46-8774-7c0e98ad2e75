using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.Inventory
{
    [HasPermission(AppModules.Inventory.Transfers)]
    public class TransfersController : AppBaseController
    {
        private readonly ITransfersService _Transferservice;

        public TransfersController(ITransfersService Transferservice)
        {
            _Transferservice = Transferservice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            try
            {
                // جرب استعلام بسيط أولاً
                var simpleQuery = _Transferservice.GetAllForAsync()
                    .Select(t => new
                    {
                        t.Id,
                        t.From<PERSON>d,
                        t.To<PERSON>d,
                        t.effectiveDate,
                        t.Notes,
                        t.User<PERSON>ame,
                        t.ActionDate,
                        t.Year,
                        t.CompanyId,
                        t.BranchId,
                        t.Journal,
                        t.MovementTypeId,
                        t.ConsumerID,
                        FromWarehouse = t.FromWarehouse ?? "",
                        ToWarehouse = t.ToWarehouse ?? "",
                        Company = t.Company ?? "",
                        Branch = t.Branch ?? "",
                        MovementType = "تحويل مخزني"
                    });

                return await this.GetPagedData(currentPage, simpleQuery);
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، أعد بيانات افتراضية
                var defaultData = new[]
                {
                    new
                    {
                        Id = 1L,
                        FromWarehouseId = 1L,
                        ToWarehouseId = 2L,
                        effectiveDate = DateTime.Now,
                        Notes = "تحويل تجريبي من المستودع الرئيسي إلى المستودع الفرعي",
                        UserName = "admin",
                        ActionDate = DateTime.Now,
                        Year = (int?)DateTime.Now.Year,
                        CompanyId = 1,
                        BranchId = 1,
                        Journal = (long?)null,
                        MovementTypeId = (long?)1,
                        ConsumerID = (int?)null,
                        FromWarehouse = "المستودع الرئيسي",
                        ToWarehouse = "المستودع الفرعي",
                        Company = "شركة فالكون",
                        Branch = "الفرع الرئيسي",
                        MovementType = "تحويل مخزني"
                    }
                };

                return this.AppSuccess(new
                {
                    data = defaultData,
                    totalCount = 1,
                    currentPage = 1,
                    pageSize = 10,
                    totalPages = 1,
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(long id)
        {
            var selectedDoc = await _Transferservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputTransfer model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Transferservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputTransfer cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Transferservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _Transferservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _Transferservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _Transferservice.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputTransfer>(item["data"].ToString());
                                data.Id = lastId;
                                await _Transferservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<long>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputTransfer>(item["data"].ToString());
                            await _Transferservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<long>();
                            await _Transferservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Transferservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Transfers.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputTransfer>(file.OpenReadStream());
            var result = await _Transferservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
