using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.Inventory.Receipts)]
    public class ReceiptsController : AppBaseController
    {
        private readonly IReceiptsService _Receiptservice;

        public ReceiptsController(IReceiptsService Receiptservice)
        {
            _Receiptservice = Receiptservice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _Receiptservice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            try
            {
                var selectedDoc = await _Receiptservice.FindForAsync(id);
                if (selectedDoc != null)
                {
                    //selectedDoc.Logs = selectedDoc.Logs
                    //    ?.OrderByDescending(l => l.SentDate).ToList();
                    return this.AppSuccess(selectedDoc);
                }
                return this.AppNotFound();
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"Error in GetBy: {ex.Message}");
                return this.AppFailed(ex);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputReceipts model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Receiptservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputReceipts cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Receiptservice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _Receiptservice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _Receiptservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _Receiptservice.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputReceipts>(item["data"].ToString());
                                data.Id = lastId;
                                await _Receiptservice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputReceipts>(item["data"].ToString());
                            await _Receiptservice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _Receiptservice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(ReceiptsFilterModel input = null, int currentPage = 1)
        {
            var result = _Receiptservice.Filter(input);
            return await this.GetPagedData(currentPage, result, 1000);

        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Receiptservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Receipts.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputReceipts>(file.OpenReadStream());
            var result = await _Receiptservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
