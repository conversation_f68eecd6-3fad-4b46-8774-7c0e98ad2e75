using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.Inventory.DeliveryRequest)]
    public class DeliveryRequestController : AppBaseController
    {
        private readonly IDeliveryRequestService _DeliveryRequestervice;

        public DeliveryRequestController(IDeliveryRequestService DeliveryRequestervice)
        {
            _DeliveryRequestervice = DeliveryRequestervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _DeliveryRequestervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _DeliveryRequestervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputDeliveryRequest model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _DeliveryRequestervice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputDeliveryRequest cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _DeliveryRequestervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _DeliveryRequestervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _DeliveryRequestervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }
        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(DeliveryRequestFilterModel input = null, int currentPage = 1)
        {
            var result = _DeliveryRequestervice.Filter(input);
            return await this.GetPagedData(currentPage, result, 1000);

        }
         

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _DeliveryRequestervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("DeliveryRequest.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputDeliveryRequest>(file.OpenReadStream());
            var result = await _DeliveryRequestervice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
