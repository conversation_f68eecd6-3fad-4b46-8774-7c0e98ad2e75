﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Inventory.Operations)]
    public class OperationTypesController : AppBaseController
    {
        private readonly IOperationTypesService _operationTypesService;
        public OperationTypesController(IOperationTypesService opService)
        {
            _operationTypesService = opService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _operationTypesService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(long id)
        {
            var selectedDoc = await _operationTypesService.FindForAsync(id);
            if (selectedDoc != null)
            {
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }
        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _operationTypesService.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputOperationType>(item["data"].ToString());
                                data.Id = lastId;
                                await _operationTypesService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputOperationType>(item["data"].ToString());
                            await _operationTypesService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _operationTypesService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputOperationType model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _operationTypesService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputOperationType cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _operationTypesService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _operationTypesService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _operationTypesService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("OperationTypes.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputOperationType>(file.OpenReadStream());
            var result = await _operationTypesService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
