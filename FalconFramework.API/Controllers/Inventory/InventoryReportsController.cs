﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Mvc;


namespace FalconFramework.API.Controllers.Inventory
{
    public class InventoryReportsController : AppBaseController
    {
        private readonly IInventoryReportService _reportService;

        public InventoryReportsController(IInventoryReportService reportService)
        {
            this._reportService = reportService;
        }

        [HasPermission(AppModules.Inventory.ProductsReport)]
        [HttpGet]
        [Route("products_report")]
        public IActionResult ProductsReport([FromQuery] products_reportInputReport model = null)
        {
            var result = _reportService.products_report("Finde_Item_Withowut", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.BonusReport)]
        [HttpGet]
        [Route("bonus_report")]
        public IActionResult BonusReport([FromQuery] BonusReportInputReport model )
        {
            var result = _reportService.bonus_report("select ItemId as 'Prodcut Id', Item_Name as 'Product Name' , Quantity  , ItemIdPonas as 'id',ItemPonas as 'Name' , Quantityponas as 'Bonus Quantity' from V_Item_Ponas Where ItemId = @ProdctID", model, ReportType.Query);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.ComponentCostReport)]
        [HttpGet]
        [Route("component_cost_report")]
        public IActionResult ComponentCostReport([FromQuery] ComponentCostReportInputReport model = null)
        {
            var result = _reportService.component_cost_report("Get_ItemContint", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Inventory.ExpirationDateReport)]
        [HttpGet]
        [Route("expiration_date_report")]
        public IActionResult ExpirationDateReport([FromQuery] ExpirationDateReportInputReport model = null)
        {
            var result = _reportService.expiration_date_report("EpireDate_SP", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Inventory.MovementByOperationsReport)]
        [HttpGet]
        [Route("movement_by_operations_report")]
        public IActionResult MovementByOperationsReport([FromQuery] MovementByOperationsReportInputReport model = null)
        {
            var result = _reportService.movement_by_operations_report("Item_Movment", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.Inventory.MovementByStoreReport)]
        [HttpGet]
        [Route("movement_by_store_report")]
        public IActionResult MovementByStoreReport([FromQuery] MovementByStoreReportInputReport model = null)
        {
            var result = _reportService.movement_by_store_report("Item_Movment_Whith_Stor", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.OperationsControlReport)]
        [HttpGet]
        [Route("operations_control_report")]
        public IActionResult OperationsControlReport([FromQuery] OperationsControlReportInputReport model = null)
        {
            var result = _reportService.operations_control_report("OperationsControlSheet", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }



        [HasPermission(AppModules.Inventory.PriceControlReport)]
        [HttpGet]
        [Route("price_control_report")]
        public IActionResult PriceControlReport([FromQuery] PriceControlReportInputReport model = null)
        {
            var result = _reportService.PriceControlReport("ItemPriceControle", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.ProductsBalanceReport)]
        [HttpGet]
        [Route("products_balance_report")]
        public IActionResult ProductsBalanceReport([FromQuery] ProductsBalanceReportInputReport model = null)
        {
            var result = _reportService.ProductsBalanceReport("GardItems", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.ProfitabilityReport)]
        [HttpGet]
        [Route("ItemCardValues")]
        public IActionResult ProfitabilityReport([FromQuery] ProfitabilityReportInputReport model = null)
        {
            var result = _reportService.ProfitabilityReport("GetImeCard_Price", model, ReportType.StoredProcedure);
           
            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.ReplenishmentReport)]
        [HttpGet]
        [Route("replenishment_report")]
        public IActionResult ReplenishmentReport([FromQuery] ReplenishmentReportInputReport model = null)
        {
            var result = _reportService.ReplenishmentReport("Replenishment", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.SrialReport)]
        [HttpGet]
        [Route("ProductSNReport")]
        public IActionResult SrialReport([FromQuery] SrialReportInputReport model = null)
        {
            var result = _reportService.SrialReport("ProductSN", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.StagnantproductsReport)]
        [HttpGet]
        [Route("StagnantProductsReport")]
        public IActionResult StagnantproductsReport([FromQuery] StagnantproductsReportInputReport model = null)
        {
            var result = _reportService.StagnantproductsReport("StagnantProductsReport", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.TotalsProductsBalanceReport)]
        [HttpGet]
        [Route("AllProductsBalanceReport")]
        public IActionResult TotalsProductsBalanceReport([FromQuery] TotalsProductsBalanceReportInputReport model = null)
        {
            var result = _reportService.TotalsProductsBalanceReport("Item_Balance_All_Stores", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.Inventory.ValuationReport)]
        [HttpGet]
        [Route("InventoryValuationReport")]
        public IActionResult ValuationReport([FromQuery] ValuationReportInputReport model = null)
        {
            var result = _reportService.ValuationReport("Stor_Takyeem", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


    }
}
