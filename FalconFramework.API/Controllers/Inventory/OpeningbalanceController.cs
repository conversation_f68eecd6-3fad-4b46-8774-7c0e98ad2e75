using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.Inventory.Openingbalance)]
    public class OpeningbalanceController : AppBaseController
    {
        private readonly IOpeningbalanceService _Openingbalanceervice;

        public OpeningbalanceController(IOpeningbalanceService Openingbalanceervice)
        {
            _Openingbalanceervice = Openingbalanceervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _Openingbalanceervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(long id)
        {
            var selectedDoc = await _Openingbalanceervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputOpeningbalance model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Openingbalanceervice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputOpeningbalance cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _Openingbalanceervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _Openingbalanceervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _Openingbalanceervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }



        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(OpeningbalanceFilterModel input = null, int currentPage = 1)
        {
            var result = _Openingbalanceervice.Filter(input);
            return await this.GetPagedData(currentPage, result, 1000);

        }



        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _Openingbalanceervice.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputOpeningbalance>(item["data"].ToString());
                                data.Id = lastId;
                                await _Openingbalanceervice.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputOpeningbalance>(item["data"].ToString());
                            await _Openingbalanceervice.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _Openingbalanceervice.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Openingbalanceervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Openingbalance.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputOpeningbalance>(file.OpenReadStream());
            var result = await _Openingbalanceervice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
