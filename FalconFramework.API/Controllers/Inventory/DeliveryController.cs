using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace FalconFramework.API.Controllers.Inventory
{
    /// <summary>
    /// Controller for managing delivery operations in the inventory system
    /// </summary>
    [HasPermission(AppModules.Inventory.Delivery)]
    public class DeliveryController : AppBaseController
    {
        private readonly IDeliveryService _deliveryService;

        /// <summary>
        /// Constructor for DeliveryController
        /// </summary>
        /// <param name="deliveryService">Delivery service dependency</param>
        public DeliveryController(IDeliveryService deliveryService)
        {
            _deliveryService = deliveryService;
        }

        /// <summary>
        /// Get a paged list of all delivery records
        /// </summary>
        /// <param name="currentPage">Current page number (default: 1)</param>
        /// <returns>Paged list of delivery records</returns>
        [HttpGet]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _deliveryService.GetAllForAsync().AsNoTracking();
            return await this.GetPagedData(currentPage, model);
        }

        /// <summary>
        /// Get a specific delivery record by ID including its delivery lines
        /// </summary>
        /// <param name="id">The ID of the delivery record</param>
        /// <returns>The delivery record with its lines if found, or NotFound</returns>
        [HttpGet("{id}")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _deliveryService.FindForAsync(id);
            if (selectedDoc != null)
            {
                // DeliveryLines should be included in the result from FindForAsync
                // If not, you may need to modify the service implementation
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        /// <summary>
        /// Get delivery lines for a specific delivery record
        /// </summary>
        /// <param name="id">The ID of the delivery record</param>
        /// <returns>The delivery lines for the specified delivery</returns>
        [HttpGet("{id}/lines")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetDeliveryLines(int id)
        {
            var selectedDoc = await _deliveryService.FindForAsync(id);
            if (selectedDoc != null && selectedDoc.DeliveryLines != null)
            {
                return this.AppSuccess(selectedDoc.DeliveryLines);
            }
            return this.AppNotFound();
        }

        /// <summary>
        /// Create a new delivery record
        /// </summary>
        /// <param name="model">The delivery data to create</param>
        /// <returns>Result of the creation operation</returns>
        [HttpPost]
        [ModuleAction(Actions.Create)]
        public async Task<IActionResult> Post(InputDelivery model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _deliveryService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                // Log the exception details
                return this.AppFailed($"Error creating delivery: {ex.Message}");
            }
        }

        /// <summary>
        /// Update an existing delivery record
        /// </summary>
        /// <param name="id">The ID of the record to update</param>
        /// <param name="deliveryToUpdate">The updated delivery data</param>
        /// <returns>Result of the update operation</returns>
        [HttpPut]
        [Route("{id}")]
        [ModuleAction(Actions.Update)]
        public async Task<IActionResult> Put(int id, InputDelivery deliveryToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _deliveryService.UpdateForAsync(id, deliveryToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                // Log the exception details
                return this.AppFailed($"Error updating delivery: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete a delivery record
        /// </summary>
        /// <param name="id">The ID of the record to delete</param>
        /// <returns>Result of the delete operation</returns>
        [HttpDelete]
        [Route("{id}")]
        [ModuleAction(Actions.Delete)]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _deliveryService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                // Log the exception details
                return this.AppFailed($"Error deleting delivery: {ex.Message}");
            }
        }

        /// <summary>
        /// Search for delivery records by term
        /// </summary>
        /// <param name="input">Search input model</param>
        /// <param name="currentPage">Current page number</param>
        /// <returns>Search results</returns>
        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _deliveryService.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        /// <summary>
        /// Filter delivery records based on criteria
        /// </summary>
        /// <param name="input">Filter criteria</param>
        /// <param name="currentPage">Current page number</param>
        /// <returns>Filtered results</returns>
        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(DeliveryFilterModel input = null, int currentPage = 1)
        {
            var result = _deliveryService.Filter(input);
            return await this.GetPagedData(currentPage, result, 1000);
        }

        /// <summary>
        /// Process batch operations (insert, update, delete)
        /// </summary>
        /// <param name="model">List of operations to perform</param>
        /// <returns>Result of the batch operation</returns>
        [HttpPost]
        [Route("Batch")]
        [ModuleAction(Actions.Update)]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _deliveryService.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();

                foreach (var item in model)
                {
                    string operationType = item["type"]?.Value<string>();

                    switch (operationType)
                    {
                        case "insert":
                            lastId++;
                            if (item["data"]?.ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputDelivery>(item["data"].ToString());
                                data.Id = lastId;
                                await _deliveryService.CreateForAsync(data);
                            }
                            break;

                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpdate = JsonConvert.DeserializeObject<InputDelivery>(item["data"].ToString());
                            await _deliveryService.UpdateForAsync(idToUpdate, dataToUpdate);
                            break;

                        case "remove":
                            var id = item["key"].Value<int>();
                            await _deliveryService.DeleteForAsync(id, UserId);
                            break;
                    }
                }

                return this.AppSuccess("Batch operations completed successfully");
            }
            catch (Exception ex)
            {
                // Log the exception details
                return this.AppFailed($"Batch operation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Export delivery records to Excel
        /// </summary>
        /// <returns>Excel file</returns>
        [HttpGet]
        [Route("Excel")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _deliveryService.GetAllForAsync().AsNoTracking().ToListAsync();
            return this.ExportToExcelMvc("Delivery.xlsx", result);
        }

        /// <summary>
        /// Import delivery records from Excel
        /// </summary>
        /// <param name="file">Excel file to import</param>
        /// <returns>Result of the import operation</returns>
        [HttpPost]
        [Route("Excel")]
        [ModuleAction(Actions.Create)]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return this.AppFailed("No file was uploaded");
            }

            try
            {
                var list = this.GetListFromExcel<InputDelivery>(file.OpenReadStream());
                var result = await _deliveryService.ImportListAsync(list);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed($"Import failed: {ex.Message}");
            }
        }
    }
}
