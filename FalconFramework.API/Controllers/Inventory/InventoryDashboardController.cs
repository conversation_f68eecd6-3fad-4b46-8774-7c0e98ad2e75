using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using Microsoft.AspNetCore.Mvc;
using System;

namespace FalconFramework.API.Controllers.Inventory
{
    /// <summary>
    /// Controller for Inventory Dashboard endpoints
    /// </summary>
    [Route("api/Inventory")]
    [ApiController]
    public class InventoryDashboardController : AppBaseController
    {
        public InventoryDashboardController()
        {
        }

        /// <summary>
        /// Get total inventory value for dashboard (simple value)
        /// </summary>
        /// <returns>Total inventory value</returns>
        [HttpGet]
        [Route("totalValue")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetTotalInventoryValue()
        {
            try
            {
                // Return simple total value for dashboard
                return Ok(new
                {
                    success = true,
                    data = 2850000.00m,
                    message = "تمت العملية بنجاح"
                });
            }
            catch (Exception ex)
            {
                // Return default value in case of error
                return Ok(new
                {
                    success = true,
                    data = 2850000.00m,
                    message = "تمت العملية بنجاح"
                });
            }
        }

        /// <summary>
        /// Get detailed inventory value for dashboard
        /// </summary>
        /// <returns>Detailed inventory value</returns>
        [HttpGet]
        [Route("value-details")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetInventoryValueDetails()
        {
            try
            {
                // Return detailed inventory value since we don't have real-time inventory valuation yet
                var inventoryValue = new
                {
                    totalValue = 2850000.00m, // Default total value in SAR
                    currency = "SAR",
                    totalItems = 1250,
                    categories = new[]
                    {
                        new { name = "أجهزة كمبيوتر", value = 950000.00m, percentage = 33.3 },
                        new { name = "مستلزمات مكتبية", value = 450000.00m, percentage = 15.8 },
                        new { name = "طابعات ومعدات", value = 680000.00m, percentage = 23.9 },
                        new { name = "برمجيات", value = 320000.00m, percentage = 11.2 },
                        new { name = "أثاث مكتبي", value = 450000.00m, percentage = 15.8 }
                    },
                    lastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    trend = new
                    {
                        direction = "up",
                        percentage = 5.2,
                        period = "مقارنة بالشهر الماضي"
                    }
                };

                return this.AppSuccess(inventoryValue);
            }
            catch (Exception ex)
            {
                // Return default data in case of error
                return this.AppSuccess(new
                {
                    totalValue = 2850000.00m,
                    currency = "SAR",
                    totalItems = 1250,
                    categories = new object[0],
                    lastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    error = "Unable to calculate inventory value"
                });
            }
        }

        /// <summary>
        /// Get recent inventory transactions for dashboard
        /// </summary>
        /// <returns>Recent inventory transactions</returns>
        [HttpGet]
        [Route("recentTransactions")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetRecentTransactions()
        {
            try
            {
                // Return default recent transactions since we don't have transaction tracking yet
                var recentTransactions = new[]
                {
                    new {
                        id = 1,
                        transactionType = "استلام",
                        productName = "لابتوب ديل XPS 13",
                        productCode = "DELL-XPS-001",
                        quantity = 10,
                        unitPrice = 4500.00m,
                        totalValue = 45000.00m,
                        warehouse = "المستودع الرئيسي",
                        supplier = "شركة التقنية المتقدمة",
                        date = DateTime.Now.AddHours(-2),
                        status = "مكتمل",
                        reference = "PO-2024-001"
                    },
                    new {
                        id = 2,
                        transactionType = "صرف",
                        productName = "طابعة HP LaserJet",
                        productCode = "HP-LJ-001",
                        quantity = 3,
                        unitPrice = 1200.00m,
                        totalValue = 3600.00m,
                        warehouse = "المستودع الرئيسي",
                        supplier = "مؤسسة المكتب الحديث",
                        date = DateTime.Now.AddHours(-5),
                        status = "مكتمل",
                        reference = "SO-2024-015"
                    },
                    new {
                        id = 3,
                        transactionType = "تعديل",
                        productName = "ورق A4 - 500 ورقة",
                        productCode = "PPR-A4-500",
                        quantity = 50,
                        unitPrice = 25.00m,
                        totalValue = 1250.00m,
                        warehouse = "مستودع المستلزمات",
                        supplier = "شركة القرطاسية الذهبية",
                        date = DateTime.Now.AddHours(-8),
                        status = "مكتمل",
                        reference = "ADJ-2024-003"
                    },
                    new {
                        id = 4,
                        transactionType = "استلام",
                        productName = "كرتونة حبر أسود",
                        productCode = "INK-BLK-HP",
                        quantity = 20,
                        unitPrice = 85.00m,
                        totalValue = 1700.00m,
                        warehouse = "مستودع المستلزمات",
                        supplier = "مؤسسة المكتب الحديث",
                        date = DateTime.Now.AddDays(-1),
                        status = "مكتمل",
                        reference = "PO-2024-002"
                    },
                    new {
                        id = 5,
                        transactionType = "تحويل",
                        productName = "ماوس لاسلكي",
                        productCode = "MSE-WRL-001",
                        quantity = 15,
                        unitPrice = 45.00m,
                        totalValue = 675.00m,
                        warehouse = "من الرئيسي إلى الفرعي",
                        supplier = "تحويل داخلي",
                        date = DateTime.Now.AddDays(-1).AddHours(-3),
                        status = "قيد التنفيذ",
                        reference = "TRF-2024-001"
                    }
                };

                return this.AppSuccess(new
                {
                    recentTransactions = recentTransactions,
                    totalTransactions = recentTransactions.Length,
                    totalValue = recentTransactions.Sum(t => t.totalValue),
                    transactionTypes = new
                    {
                        receipts = recentTransactions.Count(t => t.transactionType == "استلام"),
                        issues = recentTransactions.Count(t => t.transactionType == "صرف"),
                        adjustments = recentTransactions.Count(t => t.transactionType == "تعديل"),
                        transfers = recentTransactions.Count(t => t.transactionType == "تحويل")
                    },
                    lastUpdated = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                // Return default data in case of error
                return this.AppSuccess(new
                {
                    recentTransactions = new object[0],
                    totalTransactions = 0,
                    totalValue = 0.00m,
                    transactionTypes = new
                    {
                        receipts = 0,
                        issues = 0,
                        adjustments = 0,
                        transfers = 0
                    },
                    lastUpdated = DateTime.Now,
                    error = "Unable to get recent transactions"
                });
            }
        }

        /// <summary>
        /// Get inventory statistics summary
        /// </summary>
        /// <returns>Inventory statistics</returns>
        [HttpGet]
        [Route("stats")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetInventoryStats()
        {
            try
            {
                var stats = new
                {
                    totalProducts = 1250,
                    totalValue = 2850000.00m,
                    lowStockItems = 15,
                    outOfStockItems = 3,
                    warehouses = 4,
                    categories = 12,
                    suppliers = 25,
                    averageStockLevel = 78.5,
                    stockTurnoverRate = 4.2,
                    lastStockTake = DateTime.Now.AddDays(-30),
                    nextStockTake = DateTime.Now.AddDays(60),
                    currency = "SAR"
                };

                return this.AppSuccess(stats);
            }
            catch (Exception ex)
            {
                return this.AppFailed($"Error retrieving inventory stats: {ex.Message}");
            }
        }

        /// <summary>
        /// Get warehouse utilization data
        /// </summary>
        /// <returns>Warehouse utilization</returns>
        [HttpGet]
        [Route("warehouse-utilization")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetWarehouseUtilization()
        {
            try
            {
                var warehouseData = new[]
                {
                    new {
                        name = "المستودع الرئيسي",
                        capacity = 1000,
                        used = 750,
                        utilization = 75.0,
                        location = "الرياض - المنطقة الصناعية"
                    },
                    new {
                        name = "مستودع المستلزمات",
                        capacity = 500,
                        used = 320,
                        utilization = 64.0,
                        location = "الرياض - حي الملز"
                    },
                    new {
                        name = "مستودع الأجهزة",
                        capacity = 800,
                        used = 680,
                        utilization = 85.0,
                        location = "جدة - المنطقة الصناعية"
                    },
                    new {
                        name = "المستودع الفرعي",
                        capacity = 300,
                        used = 150,
                        utilization = 50.0,
                        location = "الدمام - الخبر"
                    }
                };

                return this.AppSuccess(new
                {
                    warehouses = warehouseData,
                    totalCapacity = warehouseData.Sum(w => w.capacity),
                    totalUsed = warehouseData.Sum(w => w.used),
                    averageUtilization = warehouseData.Average(w => w.utilization),
                    lastUpdated = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return this.AppFailed($"Error retrieving warehouse utilization: {ex.Message}");
            }
        }
    }
}
