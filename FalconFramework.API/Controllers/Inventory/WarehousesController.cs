﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Inventory.Warehouses)]
    public class WarehousesController : AppBaseController
    {
        private readonly IWarehouseService _warehouseService;
        private readonly IBranchService _branchService;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public WarehousesController(IWarehouseService warehouseService,
            IBranchService branchService,
            IUserService userService/*, IImageService imageService*/)
        {
            _warehouseService = warehouseService;
            this._branchService = branchService;
            this._userService = userService;
            //_imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _warehouseService.GetAllForAsync();
            var branches = await _branchService.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info: branches);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(long id)
        {
            var selectedDoc = await _warehouseService.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }
        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _warehouseService.GetAllForAsync()
                              .OrderByDescending(w => w.Id)
                              .Select(w => w.Id)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputWarehouse>(item["data"].ToString());
                                data.Id = lastId;
                                await _warehouseService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputWarehouse>(item["data"].ToString());
                            await _warehouseService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _warehouseService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputWarehouse model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _warehouseService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputWarehouse cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _warehouseService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _warehouseService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }



        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _warehouseService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("warehouses.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputWarehouse>(file.OpenReadStream());
            var result = await _warehouseService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
