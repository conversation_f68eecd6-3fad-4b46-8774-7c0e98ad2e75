﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Inventory.ProductSubCategories)]
    public class ProductSubCategoriesController : AppBaseController
    {
        private readonly IProductSubCategoryService _productSubCategoryService;
        private readonly IProductCategoryService _productCategoryService;
        public ProductSubCategoriesController(IProductSubCategoryService ProductCategoryService,
            IProductCategoryService productCategoryService)
        {
            _productSubCategoryService = ProductCategoryService;
            _productCategoryService = productCategoryService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _productSubCategoryService.GetAllForAsync();
            var groups = await _productCategoryService.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info: groups);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _productSubCategoryService.FindForAsync(id);
            if (selectedDoc != null)
            {
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        //[HttpPost("{id}")]
        //[Route("GetByCategoryId")]
        //public async Task<IActionResult> GetByCategoryId(int id)
        //{
        //    var model = _productSubCategoryService.GetAllForAsync().Where(r => r.groupid == id);

        //    return await this.GetPagedData(1, model);
        //}


        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                int lastId = await _productSubCategoryService.GetAllForAsync()
                              .OrderByDescending(w => w.Subid)
                              .Select(w => w.Subid)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputProductSubCategory>(item["data"].ToString());
                                data.Subid = lastId;
                                await _productSubCategoryService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputProductSubCategory>(item["data"].ToString());
                            await _productSubCategoryService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _productSubCategoryService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputProductSubCategory model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _productSubCategoryService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputProductSubCategory cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _productSubCategoryService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _productSubCategoryService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _productSubCategoryService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("ProductCategories.xlsx", result);
        }

        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputProductSubCategory>(file.OpenReadStream());
            var result = await _productSubCategoryService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}