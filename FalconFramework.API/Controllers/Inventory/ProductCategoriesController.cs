﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Inventory.ProductCategories)]
    public class ProductCategoriesController : AppBaseController
    {
        private readonly IProductCategoryService _productCategoryService;
        private readonly IBranchService _branchService;
        private readonly IPurchaseTypeService _tradeClassificationService;
        public ProductCategoriesController(IProductCategoryService ProductCategoryService,
            IBranchService branchService,
            IUserService userService,
            IPurchaseTypeService tradeClassificationService)
        {
            _productCategoryService = ProductCategoryService;
            this._branchService = branchService;
            _tradeClassificationService = tradeClassificationService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _productCategoryService.GetAllForAsync();
       
            var tradeClassifications = await _tradeClassificationService.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info: tradeClassifications);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(long id)
        {
            var selectedDoc = await _productCategoryService.FindForAsync(id);
            if (selectedDoc != null)
            {
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                long lastId = await _productCategoryService.GetAllForAsync()
                              .OrderByDescending(w => w.groupid)
                              .Select(w => w.groupid)
                              .FirstOrDefaultAsync();
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                            lastId++;
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputProductCategory>(item["data"].ToString());
                                data.groupid = lastId;
                                await _productCategoryService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputProductCategory>(item["data"].ToString());
                            await _productCategoryService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _productCategoryService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputProductCategory model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _productCategoryService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(long id, InputProductCategory cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _productCategoryService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(long id)
        {
            try
            {
                var result = await _productCategoryService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _productCategoryService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("ProductCategories.xlsx", result);
        }

        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputProductCategory>(file.OpenReadStream());
            var result = await _productCategoryService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
