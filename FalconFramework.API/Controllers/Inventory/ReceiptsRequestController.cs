using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.Inventory;
using FalconFramework.ApplicationCore.DTOs.Inventory;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.Inventory.ReceiptsRequest)]
    public class ReceiptsRequestController : AppBaseController
    {
        private readonly IReceiptsRequestService _ReceiptsRequestervice;

        public ReceiptsRequestController(IReceiptsRequestService ReceiptsRequestervice)
        {
            _ReceiptsRequestervice = ReceiptsRequestervice;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _ReceiptsRequestervice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _ReceiptsRequestervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputReceiptsRequest model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _ReceiptsRequestervice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputReceiptsRequest cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _ReceiptsRequestervice.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _ReceiptsRequestervice.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }



        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(ReceiptsRequestFilterModel input = null, int currentPage = 1)
        {
            var result = _ReceiptsRequestervice.Filter(input);
            return await this.GetPagedData(currentPage, result, 1000);

        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _ReceiptsRequestervice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }
         

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _ReceiptsRequestervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("ReceiptsRequest.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputReceiptsRequest>(file.OpenReadStream());
            var result = await _ReceiptsRequestervice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
