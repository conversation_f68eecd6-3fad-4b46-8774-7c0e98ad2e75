﻿using AspNetCore.Reporting;
using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Drawing.Imaging;

namespace FalconFramework.Controllers
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class ReportsController : Controller
    {
        private readonly IWebHostEnvironment _env;
        private readonly IWarehouseService _warehouseService;
        //private readonly ICompanyService _compService;

        public ReportsController(IWebHostEnvironment env, IWarehouseService warehouseService)
        {
            this._env = env;
            this._warehouseService = warehouseService;
            //this._compService = compService;
        }
        [HasPermission(AppModules.Inventory.Warehouses)]
        [ModuleAction(Actions.Print)]
        public async Task<IActionResult> Warehouses(bool withLogo = false)
        {
            var model = await _warehouseService.GetAllForAsync().ToListAsync();
            var company = new List<CompanyViewModel>();
            var directory = AppContext.BaseDirectory;
            var path = directory + "/Reports/Report1.rdlc";
            LocalReport report = new LocalReport(path);
            report.AddDataSource("DataSet1", model);

            Dictionary<string, string> reportParams = new Dictionary<string, string>();
            if (withLogo)
            {
                //var selectedCompany = await _compService.GetAllForAsync().ToListAsync();
                //selectedCompany.ForEach(u =>
                //{
                //    u.Header = this.ConvertToBase64(u.Header, "Companies");
                //    u.Footer = this.ConvertToBase64(u.Footer, "Companies");
                //    u.Logo = this.ConvertToBase64(u.Logo, "Companies");
                //});
                //report.AddDataSource("DataSet2", selectedCompany);
              
            }
            else
            {
                report.AddDataSource("DataSet2", new List<CompanyViewModel>());
            }
            var result = report.Execute(RenderType.Pdf, parameters: reportParams);
            return File(result.MainStream, "application/pdf");
        }

        public async Task<IActionResult> Company()
        {


            var directory = AppContext.BaseDirectory;
            var path = directory + "/Reports/CompanyReport.rdlc";
            LocalReport report = new LocalReport(path);


            Dictionary<string, string> reportParams = new Dictionary<string, string>();

            //var selectedCompany = await _compService.GetAllForAsync().ToListAsync();
            //selectedCompany.ForEach(u =>
            //{
            //    u.Header = this.ConvertToBase64(u.Header, "Companies");
            //    u.Footer = this.ConvertToBase64(u.Footer, "Companies");
            //    u.Logo = this.ConvertToBase64(u.Logo, "Companies");
            //});

            //report.AddDataSource("DataSet1", selectedCompany);

            //reportParams.Add("Logo", this.ConvertToBase64(selectedCompany.Logo, "Companies"));

            var result = report.Execute(RenderType.Pdf, parameters: reportParams);
            return File(result.MainStream, "application/pdf");
        }




        private string ConvertToBase64(string imageUrl, string folderName)
        {
            var path = $"{_env.WebRootPath}/Uploads/{folderName}/{imageUrl}";
            using (var image = Image.FromFile(path))
            {
                using (var ms = new MemoryStream())
                {
                    image.Save(ms, ImageFormat.Png);

                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }
    }
}
