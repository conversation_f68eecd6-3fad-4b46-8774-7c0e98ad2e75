﻿using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace FalconFramework.API.Controllers
{
    public class UsersController : AppBaseController
    {
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;

        public UsersController(IUserService userService, IRoleService roleService)
        {
            _userService = userService;
            this._roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _userService.GetAllForAsync();
            var roles = await _roleService.GetAllForAsync().ToListAsync();
            return await this.GetPagedData(currentPage, model, info: roles);
        }

        [HttpGet]
        [Route("Names")]
        public async Task<IActionResult> GetNames()
        {
            var model = await _userService.GetAllForAsync()
                .Select(u => new { u.FalconUserId, u.FullName })
                .ToListAsync();
            return this.AppSuccess(model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(string id)
        {
            var selectedDoc = await _userService.FindForAsync(id);
            if (selectedDoc != null)
            {
                var roles = await _roleService.GetAllForAsync().ToListAsync();
                return Ok(new { success = true, data = selectedDoc, roles, message = "" });
            }
            return this.AppNotFound();
        }

        [HttpGet]
        [Route("Roles/List")]
        public async Task<IActionResult> GetRoles()
        {
            var roles = await _roleService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(roles);
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputUser model)
        {
            try
            {
                if (!User.IsInRole(AppUserRoles.SystemAdmin) && model.Roles.Contains(AppUserRoles.SystemAdmin))
                {
                    return this.AppFailed($"You need permissions to add users to  role : {AppUserRoles.SystemAdmin} ");
                }

                var result = await _userService.CreateForAsync(model);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(string id, InputUserToUpdate cateToUpdate)
        {
            try
            {
                if (!User.IsInRole(AppUserRoles.SystemAdmin) && cateToUpdate.Roles.Contains(AppUserRoles.SystemAdmin))
                {
                    return this.AppFailed($"You need permissions to add users to role : {AppUserRoles.SystemAdmin} ");
                }
                var result = await _userService.UpdateForAsync(id, cateToUpdate);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await _userService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Activate/{id}")]
        public async Task<IActionResult> Activate(string id)
        {
            try
            {
                var result = await _userService.ActivateAsync(id);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }


        [HttpPost]
        [Route("Search")]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _userService.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 20);
        }

        [HttpPost]
        [Route("Filter")]
        public async Task<IActionResult> Filter(UserFilterModel input, int currentPage = 1)
        {
            var model = _userService.Filter(input);
            return await this.GetPagedData(currentPage, model, 20);
        }
    }
}
