﻿using FalconFramework.ApplicationCore.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Text;

namespace FalconFramework.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AppBaseController : ControllerBase
    {
        public string UserId
        {
            get
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                return userId;
            }
        }

        public int EmployeeId
        {
            get
            {
                var EmployeeId = Convert.ToInt32( User.FindFirstValue(AppClaimTypes.EmployeeId));
                return EmployeeId;
            }
        }
    }
}
