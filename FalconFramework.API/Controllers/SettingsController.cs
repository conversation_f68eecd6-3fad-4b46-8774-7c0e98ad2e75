﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Helpers.Images;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    //[HasPermission(AppModules.Companies)]
    public class SettingsController : AppBaseController
    {
        private readonly ISettingsService _settingsService;
        private readonly IImageService _imageService;

        public SettingsController(ISettingsService settingService, IImageService imageService)
        {
            this._settingsService = settingService;
            this._imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var model = await _settingsService.GetAllForAsync()
                .FirstOrDefaultAsync();
            if (model == null)
            {
                model = new SettingsViewModel();
            }
            return this.AppSuccess(model);
        }

        [HttpPost]
        public async Task<IActionResult> Post(InputSettings model)
        {
            try
            {
                var result = await _settingsService.UpdateForAsync(0, model);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

    }
}

