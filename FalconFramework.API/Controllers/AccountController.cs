﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.DTOs;
using Microsoft.AspNetCore.Authorization;
using System.Data;
using System.Net.Mail;
using FalconFramework.API.Helpers.Email;
using FalconFramework.Web.Api.Models;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using System.Security.Principal;

namespace FalconFramework.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController : ControllerBase
    {
        IAppAuthenticationService _authService;
        private readonly UserManager<ApplicationUser> _userManager;
        AppSettings _appSettings;
        IEmailService _emailService;
        private readonly IPermissionService _permissionService;

        public AccountController(
            IAppAuthenticationService authSrvce,
            UserManager<ApplicationUser> userManager,
            IOptionsMonitor<AppSettings> settings,
            IEmailService emailService,
            IPermissionService permissionService)
        {
            _authService = authSrvce;
            _userManager = userManager;
            _appSettings = settings.CurrentValue;
            _emailService = emailService;
            this._permissionService = permissionService;
        }

        [HttpPost]
        [Route("Login")]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (ModelState.IsValid)
            {
                var result = await _authService.ValidateUserAsync(model.Email, model.Password, model.RememberMe);
                if (result.Success)
                {
                    var user = result.Payload as UserViewModel;
                    var claims = new ClaimsIdentity(new Claim[]
                    {
                        new Claim(ClaimTypes.NameIdentifier,user.Id),
                        new Claim(AppClaimTypes.CompanyId,user.CompanyId ?? ""),
                        new Claim(JwtRegisteredClaimNames.Jti,Guid.NewGuid().ToString())
                    });

                    foreach (string role in user.Roles)
                    {
                        claims.AddClaim(new Claim(ClaimTypes.Role, role));
                    }

                    var token = await GetAccessTokenAsync(claims);
                    var permissions = (await _permissionService.FindByUserAsync(user.Id)).Select(p => new
                    {
                        Module = p.Module,
                        //Actions = p.Actions
                        p.CreateAction,
                        p.UpdateAction,
                        p.DeleteAction,
                        p.DisplayAction,
                        p.PrintAction
                    });
                    var identity = new ClaimsIdentity(new List<Claim> {
                          new Claim(ClaimTypes.NameIdentifier,user.Id),
                        new Claim(type: ClaimTypes.Email, value: user.Email),
                        new Claim(type: ClaimTypes.Name,value: user.FullName)}, CookieAuthenticationDefaults.AuthenticationScheme);

                    await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(identity),
                        new AuthenticationProperties
                        {
                            IsPersistent = true,
                            AllowRefresh = true,
                            ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30),
                        });
                    return this.Ok(new
                    {
                        authToken = token.AccessToken,
                        expiresIn = token.Expires,
                        RefreshToken = token.RefreshToken,
                        user = new
                        {
                            user.Email,
                            user.Mobile,
                            user.Roles,
                            user.FullName
                        },
                        permissions
                    });
                }
                return this.AppOk(result);
            }
            return this.AppFailed(ModelState);
        }

        private async Task<TokenViewModel> GetAccessTokenAsync(ClaimsIdentity claims)
        {
            var key = Encoding.ASCII.GetBytes(_appSettings.Secret);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = claims,
                Expires = DateTime.UtcNow.AddDays(3),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var jwtHandler = new JwtSecurityTokenHandler();

            var token = jwtHandler.CreateToken(tokenDescriptor);
            var access_token = jwtHandler.WriteToken(token);
            var userId = claims.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            var refreshToken = await _authService.SaveRefreshTokenAsync(new InputRefreshToken
            {
                JwtId = token.Id,
                UserId = userId,
            });

            return new TokenViewModel
            {
                AccessToken = access_token,
                Expires = token.ValidTo.Ticks,
                RefreshToken = refreshToken.Payload
            };
        }

        [HttpPost]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (ModelState.IsValid)
            {
                var result = await _authService.CreateForAsync(new InputUser
                {
                    Email = model.Email,
                    PhoneNumber = model.Mobile,
                    Password = model.Password,
                    FalconUserId = model.FalconUserId,
                    EmployeeId = model .EmployeeId
                });
                if (result.Success)
                {
                    var user = result.Payload;
                    var claims = new ClaimsIdentity(new Claim[]
                    {
                        new Claim(ClaimTypes.NameIdentifier,user.Id)
                    });

                    var token = await GetAccessTokenAsync(claims);

                    return this.AppSuccess(new
                    {
                        token.AccessToken,
                        token.Expires,
                        token.RefreshToken,
                        user = new
                        {
                            user.Email,
                            user.Mobile
                        }
                    });
                }
                return this.AppOk(result);
            }
            return this.AppFailed(ModelState);
        }


        [HttpPost]
        [Authorize]
        [Route("Logout")]
        public async Task<IActionResult> LogOut()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            await _authService.SignOut();
            return this.AppSuccess(null, message: "تم تسجيل خروجك بنجاح");
        }

        [HttpPost]
        [Authorize]
        [Route("Change")]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            var result = await _authService.ChangePasswordAsync(UserId, model.CurrentPassword, model.NewPassword);
            return Ok(result);
        }

        public string UserId
        {
            get
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                return userId;
            }
        }


        public string EmployeeId
        {
            get
            {
                var EmployeeId = User.FindFirstValue(AppClaimTypes.EmployeeId);
                return EmployeeId;
            }
        }

        public string CompanyId
        {
            get
            {
                var companyId = User.FindFirstValue(AppClaimTypes.CompanyId);
                return companyId;
            }
        }

        [HttpPost]
        [Route("Forgot")]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordViewModel model)
        {

            if (ModelState.IsValid)
            {
                var selectedUser = await _userManager.FindByEmailAsync(model.Email);
                if (selectedUser != null)
                {
                    //var token = await _userManager.GetAuthenticationTokenAsync(selectedUser, model.Email);
                    var code = await _userManager.GenerateTwoFactorTokenAsync(selectedUser, TokenOptions.DefaultPhoneProvider);
                    StringBuilder sb = new StringBuilder();
                    sb.AppendLine("Reset Password Code : ");
                    sb.AppendFormat("<strong>{0}</strong>", code);
                    await _emailService.SendAsync(new Helpers.Email.MailMessage
                    {
                        Subject = "Reset Password",
                        To = model.Email,
                        Body = sb.ToString()
                    });
                }
                return this.AppSuccess("تم إرسال رسالة لبريدك الالكتروني");
            }

            return this.AppFailed();
        }

        [HttpPost]
        [Route("Verify")]
        public async Task<IActionResult> VerifyCode(VerifyPasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var selectedUser = await _userManager.FindByEmailAsync(model.Email);
                if (selectedUser != null)
                {
                    var result = await _userManager.VerifyTwoFactorTokenAsync(selectedUser, TokenOptions.DefaultPhoneProvider, model.Code);
                    if (result)
                    {
                        return this.AppSuccess("تم التحقق بنجاح, يمكنك الان تغيير كلمة المرور");
                    }
                    else
                    {
                        return this.AppFailed("عفواً حدث خطأ أثناء تنفيذ طلبك");
                    }
                }
            }
            return this.AppInvalidModel(ModelState);
        }
        [HttpPost]
        [Route("Reset")]
        public async Task<IActionResult> ResetPassword(ResetPasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var selectedUser = await _userManager.FindByEmailAsync(model.Email);
                if (selectedUser != null)
                {
                    var result = await _userManager.VerifyTwoFactorTokenAsync(selectedUser, TokenOptions.DefaultPhoneProvider, model.Code);
                    if (result)
                    {
                        selectedUser.SecurityStamp = Guid.NewGuid().ToString();
                        selectedUser.PasswordHash = _userManager.PasswordHasher.HashPassword(selectedUser, model.Password);
                        await _userManager.UpdateAsync(selectedUser);
                        return this.AppSuccess("تم تغير كلمة المرور بنجاح. يمكنك الان تسجيل الدخول");
                    }
                    return this.AppFailed();
                }
            }
            return this.AppInvalidModel(ModelState);
        }

        //[HttpGet]
        //[Route("Manage")]
        //[Authorize(Roles = "Super")]
        //public async Task<IActionResult> Manage()
        //{
        //    ApplicationUser adminUser = null;
        //    var users = await _userManager.GetUsersInRoleAsync("Admin");
        //    foreach (var user in users)
        //    {
        //        if (!await _userManager.IsInRoleAsync(user, "Super"))
        //        {
        //            adminUser = user;
        //        }
        //    }
        //    return this.AppSuccess(new
        //    {
        //        Id = adminUser.Id,
        //        Email = adminUser.Email
        //    });
        //}

        //[HttpPost]
        //[Route("Manage")]
        //[Authorize(Roles = "Super")]
        //public async Task<IActionResult> Manage(ManageViewModel model)
        //{
        //    if (ModelState.IsValid)
        //    {
        //        var selectedUser = await _userManager.FindByIdAsync(model.Id);
        //        if (selectedUser != null)
        //        {
        //            selectedUser.Email = selectedUser.UserName = model.Email;
        //            selectedUser.EmailConfirmed = true;
        //            if (!string.IsNullOrEmpty(model.Password))
        //            {
        //                selectedUser.SecurityStamp = Guid.NewGuid().ToString();
        //                selectedUser.PasswordHash = _userManager.PasswordHasher.HashPassword(selectedUser, model.Password);
        //            }
        //            await _userManager.UpdateAsync(selectedUser);
        //            return this.AppSuccess("تم تغيير كلمة المرور بنجاح");
        //        }
        //    }
        //    return this.AppInvalidModel(ModelState);
        //}

        [HttpGet]
        [Route("Info")]
        [Authorize]
        public async Task<IActionResult> GetInfo()
        {
            var user = await _authService.GetUserInfoByIdAsync(UserId);

            if (user == null)
            {
                return this.AppNotFound();
            }
            return this.AppSuccess(user);
        }

        [HttpPost]
        [Route("UpdateInfo")]
        [Authorize]
        public async Task<IActionResult> UpdateInfo(InputUser model)
        {
            var result = await _authService.UpdateUserAsync(UserId, model);
            return this.AppOk(result);
        }


        [HttpPost]
        [Route("RefreshToken")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken(TokenRequest tokenRequest)
        {
            if (ModelState.IsValid)
            {
                var result = await VerifyToken(tokenRequest);

                if (result == null)
                {
                    return this.AppFailed("Invalid tokens");
                }

                return result;
            }

            return this.AppFailed("Invalid payload");
        }

        private async Task<IActionResult> VerifyToken(TokenRequest tokenRequest)
        {
            var jwtTokenHandler = new JwtSecurityTokenHandler();

            try
            {
                var key = Encoding.ASCII.GetBytes(_appSettings.Secret);
                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = false,
                    RequireExpirationTime = false,

                    // Allow to use seconds for expiration of token
                    // Required only when token lifetime less than 5 minutes
                    // THIS ONE
                    //ClockSkew = TimeSpan.Zero
                };

                // This validation function will make sure that the token meets the validation parameters
                // and its an actual jwt token not just a random string
                var principal = jwtTokenHandler.ValidateToken(tokenRequest.Token, tokenValidationParameters, out var validatedToken);

                // Now we need to check if the token has a valid security algorithm
                if (validatedToken is JwtSecurityToken jwtSecurityToken)
                {
                    var result = jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase);

                    if (result == false)
                    {
                        return null;
                    }
                }

                // Will get the time stamp in unix time
                var utcExpiryDate = long.Parse(principal.Claims.OrderBy(x => x.Issuer).FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Exp).Value);

                // we convert the expiry date from seconds to the date
                var expDate = UnixTimeStampToDateTime(utcExpiryDate);

                if (expDate > DateTime.UtcNow)
                {
                    return this.AppFailed("We cannot refresh this since the token has not expired");
                }

                // Check the token we got if its saved in the db
                var storedRefreshToken = await _authService.GetRefreshTokenAsync(tokenRequest.RefreshToken);

                if (storedRefreshToken == null)
                {
                    return this.AppFailed("refresh token doesnt exist");
                }

                // Check the date of the saved token if it has expired
                if (DateTime.UtcNow > storedRefreshToken.ExpiryDate)
                {
                    return this.AppFailed("token has expired, user needs to relogin");
                }

                // check if the refresh token has been used
                if (storedRefreshToken.IsUsed)
                {
                    return this.AppFailed("token has been used");
                }

                // Check if the token is revoked
                if (storedRefreshToken.IsRevoked)
                {
                    return this.AppFailed("token has been revoked");
                }

                // we are getting here the jwt token id
                var jti = principal.Claims.SingleOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti).Value;

                // check the id that the recieved token has against the id saved in the db
                if (storedRefreshToken.JwtId != jti)
                {
                    return this.AppFailed("the token doenst mateched the saved token");
                }

                var isUsedResult = await _authService.MarKAsUsedRefreshTokenAsync(tokenRequest.RefreshToken);

                var selectedUser = await _authService.GetUserByIdAsync(storedRefreshToken.UserId);
                var claims = new ClaimsIdentity(new Claim[]
                   {
                        new Claim(ClaimTypes.NameIdentifier,selectedUser.Id),
                        new Claim(JwtRegisteredClaimNames.Jti,Guid.NewGuid().ToString())
                   });

                foreach (var role in selectedUser.Roles)
                {
                    claims.AddClaim(new Claim(ClaimTypes.Role, role.RoleName));
                }

                var generatedTokens = await GetAccessTokenAsync(claims);

                //var selectedSettings = await _userService.GetSettings(storedRefreshToken.UserId);
                List<PermissionViewModel> permissions = await _permissionService.FindByUserAsync(storedRefreshToken.UserId);
                var modules = permissions.Select(p => p.Module);

                return Ok(new
                {
                    authToken = generatedTokens.AccessToken,
                    RefreshToken = generatedTokens.RefreshToken,
                    ExpiresIn = generatedTokens.Expires,
                    User = new
                    {
                        selectedUser.Email,
                        selectedUser.PhoneNumber,
                        selectedUser.Roles,
                        //selectedUser.Logo,
                        selectedUser.FirstName,
                        selectedUser.LastName,
                        selectedUser.FullName
                    },
                    //  Settings = selectedSettings.Data,
                    Modules = modules
                });
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private DateTime UnixTimeStampToDateTime(double unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
            dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToUniversalTime();
            return dtDateTime;
        }
    }
}
