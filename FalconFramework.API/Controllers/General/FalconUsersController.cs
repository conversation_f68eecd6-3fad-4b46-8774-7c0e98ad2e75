﻿using DocumentFormat.OpenXml.Wordprocessing;
using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;


namespace FalconFramework.API.Controllers.Sales
{
    public class FalconUsersController : AppBaseController
    {
        private readonly IFalconUsersService _FalconUsersService;
        private readonly IUserService _userService;
        public FalconUsersController(IFalconUsersService FalconUsersService, IUserService userService)
        {
            this._FalconUsersService = FalconUsersService;
            this._userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _FalconUsersService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _FalconUsersService.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputFalconUsers model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _FalconUsersService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputFalconUsers cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _FalconUsersService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _FalconUsersService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }

        [HttpGet]
        [Route("Names")]
        public async Task<IActionResult> GetNames()
        {
            var model = await _FalconUsersService.GetAllForAsync()
                .Select(u => new { u.id, u.Name })
                .ToListAsync();
            return this.AppSuccess(model);
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _FalconUsersService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("FalconUsers.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputFalconUsers>(file.OpenReadStream());
            var result = await _FalconUsersService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
