﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Helpers.Images;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.General.Companies)]
    public class CompaniesController : AppBaseController
    {
        private readonly ICompanyService _companyService;
        private readonly IImageService _imageService;

        public CompaniesController(ICompanyService companyService, IImageService imageService)
        {
            this._companyService = companyService;
            this._imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var model = await _companyService.GetAllForAsync()
                .FirstOrDefaultAsync();
            if (model == null)
            {
                model = new CompanyViewModel();
            }
            return this.AppSuccess(model);
        }

        [HttpPost]
        public async Task<IActionResult> PostAsync([FromForm] InputCompanyViewModel model)
        {
            try
            {
                InputCompany modelToUpdate = new InputCompany { Address = model.Address, NameAr = model.NameAr, NameEn = model.NameEn, CRN = model.CRN, Email = model.Email, Mobile = model.Mobile };
                if (model.Logo != null)
                {
                    modelToUpdate.Logo = _imageService.GenerateNewFileName(model.Logo.FileName);
                }
                if (model.Header != null)
                {
                    modelToUpdate.Header = _imageService.GenerateNewFileName(model.Header.FileName);
                }
                if (model.Footer != null)
                {
                    modelToUpdate.Footer = _imageService.GenerateNewFileName(model.Footer.FileName);
                }

                var result = await _companyService.UpdateForAsync(null, modelToUpdate);
                if (result.Success)
                {
                    if (!string.IsNullOrEmpty(modelToUpdate.Logo) && model.Logo != null)
                    {
                        await _imageService.SaveAsync(model.Logo, modelToUpdate.Logo, ImageFolders.Companies);
                    }
                    if (!string.IsNullOrEmpty(modelToUpdate.Header) && model.Header != null)
                    {
                        await _imageService.SaveAsync(model.Header, modelToUpdate.Header, ImageFolders.Companies);
                    }
                    if (!string.IsNullOrEmpty(modelToUpdate.Footer) && model.Footer != null)
                    {
                        await _imageService.SaveAsync(model.Footer, modelToUpdate.Footer, ImageFolders.Companies);
                    }
                }
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }

    }
}
