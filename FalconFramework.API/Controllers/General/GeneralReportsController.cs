﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.General;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.General
{
    public class GeneralReportsController : AppBaseController
    {
        private readonly IGeneralReportService _reportService;

        public GeneralReportsController(IGeneralReportService reportService)
        {
            this._reportService = reportService;
        }

        //[HasPermission(AppModules.HR.DeductionsReports)]
        //[HttpGet]
        //[Route("General")]
        //public IActionResult Deductions([FromQuery] InputReport model = null)
        //{
        //    var result = _reportService.GetReport("_cutting", model, ReportType.StoredProcedure);

        //    return this.AppSuccess(result);
        //}


    }
}
