﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Admin.Currencies)]
    public class CurrencyController : AppBaseController
    {
        private readonly ICurrencyService _currencyService;

        public CurrencyController(ICurrencyService currencyService)
        {
            this._currencyService = currencyService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage)
        {
            var result = _currencyService.GetAllForAsync();
            return await this.GetPagedData(currentPage, result);
        }

    }
}
