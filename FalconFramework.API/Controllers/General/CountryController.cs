﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    [HasPermission(AppModules.Admin.Countries)]
    public class CountryController : AppBaseController
    {
        private readonly ICountryService _countryService;

        public CountryController(ICountryService countryService)
        {
            this._countryService = countryService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage)
        {
            var result = _countryService.GetAllForAsync();
            return await this.GetPagedData(currentPage, result);
        }

    }
}
