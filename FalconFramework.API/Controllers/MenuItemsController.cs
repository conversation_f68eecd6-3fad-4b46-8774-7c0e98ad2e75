﻿using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    public class MenuItemsController : AppBaseController
    {
        private readonly IMenuItemService _menuItemService;
        private readonly IPermissionService _permissionService;

        public MenuItemsController(IMenuItemService menuItemService, IPermissionService permissionService)
        {
            _menuItemService = menuItemService;
            this._permissionService = permissionService;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            var userModules = await _permissionService.FindModulesByUserAsync(UserId);
            var model = await _menuItemService.GetAllByFor(id)
                .ToListAsync();
            var items = recursiveCheck(model, userModules, model);

            return this.AppSuccess(items);
        }

        private static List<MenuItemViewModel> recursiveCheck(List<MenuItemViewModel> items, List<string> userModules, List<MenuItemViewModel> source)
        {
            foreach (var item in items.ToList ())
            {
                if (!userModules.Contains(item.ModuleName))
                {
                    if (item.HasChildren)
                    {
                        item.Children = recursiveCheck(item.Children.ToList(), userModules, item.Children.ToList());
                    }
                    else
                    {
                        source.Remove(item);
                    }
                }
            }
            foreach (var item in source.ToList())
            {
                if (!item.HasChildren && item.Route == null)
                {
                    source.Remove(item);
                }
            }
            return source;
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputMenuItem model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _menuItemService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

    }
}
