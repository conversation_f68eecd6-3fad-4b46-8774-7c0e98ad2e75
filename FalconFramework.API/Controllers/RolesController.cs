﻿using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    public class RolesController : AppBaseController
    {
        private readonly IRoleService _roleService;

        public RolesController(IRoleService roleService)
        {
            this._roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var result = await _roleService.GetAllForAsync()
                .ToListAsync();
            return this.AppSuccess(result);
        }


        [HttpPost]
        public async Task<IActionResult> Post(InputRole model)
        {
            var result = await _roleService.CreateForAsync(model);
            return this.AppOk(result);
        }


        [HttpPut("{id}")]
        public async Task<IActionResult> Put(string id, InputRole model)
        {
            var result = await _roleService.UpdateForAsync(id, model);
            return this.AppOk(result);
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            var result = await _roleService.DeleteForAsync(id, UserId);
            return this.AppOk(result);
        }
    }
}
