﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.Fleet;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.Fleet
{
    public class FleetReportsController : AppBaseController
    {
        private readonly IFleetReportService _reportService;

        public FleetReportsController(IFleetReportService reportService)
        {
            this._reportService = reportService;
        }

        [HasPermission(AppModules.Fleet.EquipmentBalanceReport)]
        [HttpGet]
        [Route("EquipmentBalanceReport")]
        public IActionResult EquipmentBalanceReport([FromQuery] EquipmentBalanceReportInputReport model = null)
        {
            var result = _reportService.EquipmentBalanceReport("_cutting", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


    }
}
