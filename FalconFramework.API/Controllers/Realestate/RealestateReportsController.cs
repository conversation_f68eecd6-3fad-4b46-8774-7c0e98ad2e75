﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.Realestate;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.Realestate
{
    public class RealestateReportsController : AppBaseController
    {
        private readonly IRealestateReportService _reportService;

        public RealestateReportsController(IRealestateReportService reportService)
        {
            this._reportService = reportService;
        }

        //[HasPermission(AppModules.HR.DeductionsReports)]
        //[HttpGet]
        //[Route("Realestate")]
        //public IActionResult Deductions([FromQuery] InputReport model = null)
        //{
        //    var result = _reportService.GetReport("_cutting", model, ReportType.StoredProcedure);

        //    return this.AppSuccess(result);
        //}


    }
}
