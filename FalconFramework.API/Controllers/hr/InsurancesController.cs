﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.HR.Insurances)]
    public class InsurancesController : AppBaseController
    {
        private readonly IInsurancesService _myService;

        public InsurancesController(IInsurancesService MyService)
        {
            _myService = MyService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _myService.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _myService.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputInsurances model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, InputInsurances cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _myService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _myService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                
                foreach (var item in model)
                {
                    switch (item["type"].Value<string>())
                    {
                        case "insert":
                          
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<InputInsurances>(item["data"].ToString());
                               
                                await _myService.CreateForAsync(data);
                            }
                            break;
                        case "update":
                            var idToUpdate = item["key"].Value<int>();
                            var dataToUpate = JsonConvert.DeserializeObject<InputInsurances>(item["data"].ToString());
                            await _myService.UpdateForAsync(idToUpdate, dataToUpate);
                            break;
                        case "remove":
                            var id = item["key"].Value<int>();
                            await _myService.DeleteForAsync(id, UserId);
                            break;
                    }
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }


        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _myService.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("Insurances.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputInsurances>(file.OpenReadStream());
            var result = await _myService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
