﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.API.Controllers.hr
{
    public class DeductionsReportController : AppBaseController
    {
        private readonly IAttendanceMachinesService _AttendanceMachineservice;
        private readonly IUserService _userService;

        //private readonly IImageService _imageService;
        public DeductionsReportController(IAttendanceMachinesService AttendanceMachineservice, IUserService userService/*, IImageService imageService*/)
        {
            _AttendanceMachineservice = AttendanceMachineservice;
            _userService = userService;
            //_imageService = imageService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {
            var model = _AttendanceMachineservice.GetAllForAsync();
            return await this.GetPagedData(currentPage, model);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _AttendanceMachineservice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(InputAttendanceMachines model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _AttendanceMachineservice.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        
         

        [HttpGet]
        [Route("Users/list")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _userService.GetAllForAsync().ToListAsync();
            return this.AppSuccess(result);
        }

        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _AttendanceMachineservice.Search(input.Term);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpPost]
        [Route("Filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Filter(AttendanceMachinesFilterModel input, int currentPage = 1)
        {
            var model = _AttendanceMachineservice.Filter(input);
            return await this.GetPagedData(currentPage, model, 1000);
        }

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _AttendanceMachineservice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("AttendanceMachines.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<InputAttendanceMachines>(file.OpenReadStream());
            var result = await _AttendanceMachineservice.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
