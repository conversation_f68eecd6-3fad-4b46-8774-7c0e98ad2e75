﻿using FalconFramework.API.Attributes;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.hr
{
    public class HrReportsController : AppBaseController
    {
        private readonly IHRReportService _reportService;

        public HrReportsController(IHRReportService reportService)
        {
            this._reportService = reportService;
        }

        [HasPermission(AppModules.HR.DeductionsReports)]
        [HttpGet]
        [Route("Deductions")]
        public IActionResult Deductions([FromQuery] DeductionsInputReport model = null)
        {
            var result = _reportService.DeductionsReport("_cutting", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.HR.BonusesandincentivesReports)]
        [HttpGet]
        [Route("Bonuses")]
        public IActionResult Bonuses([FromQuery] BonusesInputReport model = null)
        {
            var result = _reportService.BonusesRerport("_salary_plass_2", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.HR.LoansReports)]
        [HttpGet]
        [Route("Loans")]
        public IActionResult Loans([FromQuery] LoansInputReport model = null)
        {
            var result = _reportService.LoanReport("EmpSolfaState", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.HR.VacationsReports)]
        [HttpGet]
        [Route("VacationsReport")]
        public IActionResult VacationsReport([FromQuery] VacationsReportInputReport model = null)
        {
            var result = _reportService.VacationsReport("_VacationsCalculate", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.HR.DocumentsReports)]
        [HttpGet]
        [Route("DocumentsReports")]
        public IActionResult DocumentsReports([FromQuery] DocumentsReportsInputReport model = null)
        {
            var result = _reportService.DocumentsReports("SELECT  ISNULL(VEmp.emp_code, 0) AS emp_code ,  ISNULL(VEmp.emp_name, 'مستندات الشركة') AS emp_name ,  ISNULL(VEmp.jname, '--') AS jname ,  ISNULL(VEmp.depart_name, '--') AS depart_name ,  ISNULL(VEmp.emp_jopdate, ActionDate) AS emp_jopdate ,  ISNULL(VEmp.emp_mob, '--') AS emp_mob ,  ISNULL(VEmp.emp_mail, '--') AS emp_mail ,  ISNULL(VEmp.emp_tel, '--') AS emp_tel ,  ISNULL(VEmp.emp_add, '--') AS emp_add ,  Files.FileName ,  Files.FilePath ,  Files.FileNotes ,  Files.UserAdd ,  Files.ActionDate ,  Files.UserEdit ,  Files.WathaekType_ID ,  Files.Wfrom ,  Files.Wstart ,  Files.Wstart2 ,  Files.Wenddate ,  Files.Wenddate2 FROM    VEmp  RIGHT OUTER JOIN Files ON VEmp.emp_code = Files.emp_code WHERE Wenddate BETWEEN @Date1 AND @Date2 ORDER BY emp_code", model, ReportType.Query);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.HR.DrivinglicencesReports)]
        [HttpGet]
        [Route("Driving_licences")]
        public IActionResult DrivingLicencesReport([FromQuery] DrivingLicencesInputReport model = null)
        {
            var result = _reportService.DrivingLicencesReport("select  emp_code,emp_name  ,driver_no  ,driver_from   ,date_driver1   ,date_driver_2  ,driver_1   ,driver_2   from emp where date_driver_2 between  @Date1 and @Date2", model, ReportType.Query);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.HR.DueSalariesReports)]
        [HttpGet]
        [Route("DueSalariesReports")]
        public IActionResult DueSalariesReports([FromQuery] DueSalariesReportsInputReport model = null)
        {
            var result = _reportService.DueSalariesReports("_Salary_Statu", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }


        [HasPermission(AppModules.HR.EmployeelifeReports)]
        [HttpGet]
        [Route("Employee_life")]
        public IActionResult Employee_life([FromQuery] Employee_lifeInputReport model = null)
        {
            var result = _reportService.Employee_LifeReport("Employee_Life", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        [HasPermission(AppModules.HR.InsurancesdueReports)]
        [HttpGet]
        [Route("Insurances_Due")]
        public IActionResult Insurances_Due([FromQuery] Insurances_DueInputReport model = null)
        {
            var result = _reportService.Insurances_DueReport("Insurances_dues", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);

        }


        [HasPermission(AppModules.HR.ManagersReports)]
        [HttpGet]
        [Route("ManagersReport")]
        public IActionResult ManagersReport([FromQuery] ManagersReportInputReport model = null)
        {
            var result = _reportService.ManagersReport($"select  emp_name  , jname , emp_code , IqamaID , BarCode  FROM VEmp  where depart_id = {model.DepartId}", model, ReportType.Query);

            return this.AppSuccess(result);


        }


        [HasPermission(AppModules.HR.TravelsReports)]
        [HttpGet]
        [Route("TravelsReports")]
        public IActionResult TravelsReports([FromQuery] TravelsReportsInputReport model = null)
        {
            var result = _reportService.TravelsReports("TravelsReports", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }

        [HasPermission(AppModules.HR.RecruitedduringPeriodReports)]
        [HttpGet]
        [Route("RecruitedReport")]
        public IActionResult RecruitedReport([FromQuery] RecruitedReportInputReport model = null)
        {
            var result = _reportService.RecruitedReport("RecruitedReport", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }


        [HasPermission(AppModules.HR.MissionsReports)]
        [HttpGet]
        [Route("MissionsReport")]
        public IActionResult MissionsReport([FromQuery] MissionsReportInputReport model = null)
        {
            var result = _reportService.MissionsReport("_Mamoryat", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }

        [HasPermission(AppModules.HR.MonthlyDeductionsReports)]
        [HttpGet]
        [Route("Monthly_Deductions")]
        public IActionResult Monthly_Deductions([FromQuery] Monthly_DeductionsInputReport model = null)
        {
            var result = _reportService.Monthly_Deductions("Monthly_Deductions", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);

        }

        [HasPermission(AppModules.HR.MovementsDuringPeriodReports)]
        [HttpGet]
        [Route("Movements_During")]
        public IActionResult Movements_During([FromQuery] Movements_DuringInputReport model = null)
        {
            var result = _reportService.GetMovements_During_Report("SELECT * FROM dbo.Transfer_Emp WHERE Request_Date BETWEEN @DateFrom and  @DateTo", model, ReportType.Query);

            return this.AppSuccess(result);

        }


        [HasPermission(AppModules.HR.NationalityJobsReports)]
        [HttpGet]
        [Route("Nationality_Jobs")]
        public IActionResult Nationality_Jobs([FromQuery] Nationality_JobsInputReport model = null)
        {
            var result = _reportService.Nationality_Jobs("Nationality_Jobs", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }


        [HasPermission(AppModules.HR.OverTimeReports)]
        [HttpGet]
        [Route("OverTimeReport")]
        public IActionResult OverTimeReport([FromQuery] OverTimeReportInputReport model = null)
        {
            var result = _reportService.OverTimeReport("_OverTime", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }


        [HasPermission(AppModules.HR.PermissionsReports)]
        [HttpGet]
        [Route("PermissionsReport")]
        public IActionResult PermissionsReport([FromQuery] PermissionsReportInputReport model = null)
        {
            var result = _reportService.PermissionsReport("_ezn", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }



        [HasPermission(AppModules.HR.PlayoffsReports)]
        [HttpGet]
        [Route("PlayoffsReport")]
        public IActionResult PlayoffsReport([FromQuery] PlayoffsReportInputReport model = null)
        {
            var result = _reportService.PlayoffsReport("PlayoffsReport", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }

        [HasPermission(AppModules.HR.AttendanceReports)]
        [HttpGet]
        [Route("AttendanceReport")]
        public IActionResult AttendanceReport([FromQuery] AttendanceReportInputReport model = null)
        {
            var result = _reportService.AttendanceReport("_CalculateMonth", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);
        }

        /// <summary>
        /// Get recent HR activities for dashboard
        /// </summary>
        /// <returns>Recent activities data</returns>
        [HttpGet]
        [Route("recent-activities")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetRecentActivities()
        {
            try
            {
                // Return default activities since we don't have activity tracking yet
                var recentActivities = new[]
                {
                    new {
                        id = 1,
                        type = "Employee Hired",
                        description = "New employee Ahmed Mohamed joined the company",
                        date = DateTime.Now.AddDays(-1),
                        icon = "fas fa-user-plus",
                        priority = "high"
                    },
                    new {
                        id = 2,
                        type = "Vacation Approved",
                        description = "Vacation request approved for Sara Ali",
                        date = DateTime.Now.AddDays(-2),
                        icon = "fas fa-calendar-check",
                        priority = "medium"
                    },
                    new {
                        id = 3,
                        type = "Training Completed",
                        description = "Safety training completed by 15 employees",
                        date = DateTime.Now.AddDays(-3),
                        icon = "fas fa-graduation-cap",
                        priority = "low"
                    },
                    new {
                        id = 4,
                        type = "Performance Review",
                        description = "Monthly performance reviews scheduled",
                        date = DateTime.Now.AddDays(-4),
                        icon = "fas fa-chart-line",
                        priority = "medium"
                    },
                    new {
                        id = 5,
                        type = "Policy Update",
                        description = "HR policies updated and published",
                        date = DateTime.Now.AddDays(-5),
                        icon = "fas fa-file-alt",
                        priority = "low"
                    }
                };

                return this.AppSuccess(recentActivities);
            }
            catch (Exception ex)
            {
                return this.AppFailed($"Error retrieving recent activities: {ex.Message}");
            }
        }

        /// <summary>
        /// Get upcoming HR events for dashboard
        /// </summary>
        /// <returns>Upcoming events data</returns>
        [HttpGet]
        [Route("upcoming-events")]
        [ModuleAction(Actions.Display)]
        public IActionResult GetUpcomingEvents()
        {
            try
            {
                // Return default events since we don't have event management yet
                var upcomingEvents = new[]
                {
                    new {
                        id = 1,
                        title = "Monthly Team Meeting",
                        description = "All hands meeting for project updates",
                        date = DateTime.Now.AddDays(3),
                        location = "Conference Room A",
                        type = "meeting",
                        attendees = 25
                    },
                    new {
                        id = 2,
                        title = "Safety Training Session",
                        description = "Mandatory safety training for new employees",
                        date = DateTime.Now.AddDays(7),
                        location = "Training Center",
                        type = "training",
                        attendees = 12
                    },
                    new {
                        id = 3,
                        title = "Performance Review Deadline",
                        description = "Deadline for submitting Q4 performance reviews",
                        date = DateTime.Now.AddDays(10),
                        location = "HR Department",
                        type = "deadline",
                        attendees = 0
                    },
                    new {
                        id = 4,
                        title = "Company Annual Event",
                        description = "Annual company celebration and awards ceremony",
                        date = DateTime.Now.AddDays(30),
                        location = "Main Auditorium",
                        type = "event",
                        attendees = 150
                    }
                };

                return this.AppSuccess(upcomingEvents);
            }
            catch (Exception ex)
            {
                return this.AppFailed($"Error retrieving upcoming events: {ex.Message}");
            }
        }
    }
}
