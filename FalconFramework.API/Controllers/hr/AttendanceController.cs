﻿using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers.hr
{
    public class AttendanceController : AppBaseController
    {
        private readonly IManualAttendanceService _manualAttendanceService;
        private readonly IEmployeeService _employeeService;

        public AttendanceController(
            IManualAttendanceService manualAttendanceService,
            IEmployeeService employeeService)
        {
            _manualAttendanceService = manualAttendanceService;
            _employeeService = employeeService;
        }

        [HttpGet]
        [Route("rate")]
        public async Task<IActionResult> GetAttendanceRate()
        {
            try
            {
                // الحصول على إجمالي عدد الموظفين
                var totalEmployees = await _employeeService.GetAllForAsync()
                    .Where(e => e.JobStatus == "نشط") // فقط الموظفين النشطين
                    .CountAsync();

                if (totalEmployees == 0)
                {
                    return this.AppSuccess(new { rate = 0 });
                }

                // الحصول على عدد الموظفين الحاضرين اليوم
                var today = DateTime.Today;

                // استخدام استعلام مباشر على قاعدة البيانات للحصول على سجلات الحضور
                var attendanceRecords = await _manualAttendanceService.GetPresentEmployeesCount();

                // حساب معدل الحضور
                var attendanceRate = (double)attendanceRecords / totalEmployees * 100;

                return this.AppSuccess(new
                {
                    rate = Math.Round(attendanceRate, 2),
                    presentEmployees = attendanceRecords,
                    totalEmployees = totalEmployees
                });
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }
    }
}
