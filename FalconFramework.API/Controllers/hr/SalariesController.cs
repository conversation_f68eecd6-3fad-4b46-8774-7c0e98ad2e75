﻿ 
using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.HR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace FalconFramework.API.Controllers.hr
{
    [HasPermission(AppModules.HR.SalaryAdditions)]
    public class SalariesController : AppBaseController
    {
        private readonly ISalariesService _SalariesService;

        public SalariesController(ISalariesService SalariesService)
        {
            _SalariesService = SalariesService;
        }
         

        // POST api/<controller>
        [HttpPost]
        public async Task<IActionResult> Post(SalariesInput model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalariesService.CreateForAsync(model);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {

                return this.AppFailed(ex);
            }
        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> Put(int id, SalariesInput cateToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var result = await _SalariesService.UpdateForAsync(id, cateToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _SalariesService.DeleteForAsync(id, UserId);
                return this.AppOk(result);
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex);
            }
        }

       

        [HttpPost]
        [Route("Batch")]
        public async Task<IActionResult> Batch(List<JObject> model)
        {
            try
            {
                
                foreach (var item in model)
                {
                   
                           
                            if (item["data"].ToString() != "{}")
                            {
                                var data = JsonConvert.DeserializeObject<SalariesInput>(item["data"].ToString());
                              
                                await _SalariesService.CreateForAsync(data);
                            }
                          
                        
                   
                }
                return this.AppSuccess("");
            }
            catch (Exception ex)
            {
                return this.AppFailed(ex.Message);
            }
        }



        [HasPermission(AppModules.HR.PrintSalaries)]
        [HttpGet]
        [Route("Salaries")]
        public IActionResult Salaries([FromQuery] SalariesInput model = null)
        {
            var result = _SalariesService.Salaries("_Select_Sallery", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }

        [HasPermission(AppModules.HR.SalariesPreparation)]
        [HttpGet]
        [Route("SalariesPreparation")]
        public IActionResult CalcSalaries([FromQuery] CalcSalariesInput model = null)
        {
            var result = _SalariesService.CalcSalaries("_SalaryShit", model, ReportType.StoredProcedure);

            return this.AppSuccess(result);


        }


        [HttpGet]
        [Route("Excel")]
            public IActionResult ExportToExcel([FromQuery] SalariesInput model = null)
        {
            var result = _SalariesService.Salaries("_CalculateMonth", model, ReportType.StoredProcedure);

            return this.ExportToExcelMvc("SalaryAdditions.xlsx", result);
        }
        [HttpPost]
        [Route("Excel")]
        public async Task<IActionResult> ImportToExcel(IFormFile file)
        {
            var list = this.GetListFromExcel<SalariesInput>(file.OpenReadStream());
            var result = await _SalariesService.ImportListAsync(list);
            return this.AppOk(result);
        }
    }
}
