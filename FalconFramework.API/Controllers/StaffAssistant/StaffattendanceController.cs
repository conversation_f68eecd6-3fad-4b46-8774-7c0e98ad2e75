using FalconFramework.API.Attributes;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.Interfaces;
using FalconFramework.ApplicationCore.Interfaces.Sales;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FalconFramework.ApplicationCore.Interfaces.StaffAssistant;
using FalconFramework.ApplicationCore.DTOs.StaffAssistant;
using FalconFramework.ApplicationCore.Interfaces.HR;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.DTOs.HR;

namespace FalconFramework.API.Controllers.Sales
{
    [HasPermission(AppModules.StaffAssistant.Attendance)]
    public class StaffattendanceController : AppBaseController
    {
        private readonly IStaffattendanceService _Staffattendanceervice;
        private readonly IAnalyticAccountsService _AnalyticAccountservice;
        private readonly IEmployeeService _EmployeeService;
        private readonly IUserService _userService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public StaffattendanceController(IStaffattendanceService Staffattendanceervice,
            IAnalyticAccountsService AnalyticAccountservice,
            IEmployeeService EmployeeService,
            IUserService userService,
            IHttpContextAccessor httpContextAccessor)
        {
            _AnalyticAccountservice = AnalyticAccountservice;
            _Staffattendanceervice = Staffattendanceervice;
            _userService = userService;
            _EmployeeService = EmployeeService;
            _httpContextAccessor = httpContextAccessor;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int currentPage = 1)
        {

            int loggedEmp = 0;

            //loggedEmp = EmployeeId;


            loggedEmp = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.EmployeeId)
           .FirstOrDefaultAsync();

            DateTime today = DateTime.Now.Date;
             
           var EmpData =  await _EmployeeService
          .GetAllForAsync()
          .Where(u => u.Id == loggedEmp).FirstOrDefaultAsync();

            string Costid = EmpData.CostID.Value.ToString ();

            var model = _Staffattendanceervice.GetAllForAsync().Where(ec=> ec.id == loggedEmp && ec.Date.Month== today.Month && ec.Date.Year == today.Year);


            if (EmpData.CostID == null)
            {
                var analyticaccounts = await _AnalyticAccountservice.GetAllForAsync().ToListAsync();
                return await this.GetPagedData(currentPage, model, info: analyticaccounts);
            }
            else {

                var analyticaccounts = await _AnalyticAccountservice.GetAllForAsync().Where(r => r.Code == Costid).ToListAsync();
                return await this.GetPagedData(currentPage, model, info: analyticaccounts);

            }
           
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBy(int id)
        {
            var selectedDoc = await _Staffattendanceervice.FindForAsync(id);
            if (selectedDoc != null)
            {
                //selectedDoc.Logs = selectedDoc.Logs
                //    ?.OrderByDescending(l => l.SentDate).ToList();
                return this.AppSuccess(selectedDoc);
            }
            return this.AppNotFound();
        }


        [HttpPut("CheckIn")]
        public async Task<IActionResult> CheckIn(StaffattendanceCheckIn RecodrdToUpdate)
        {
            try
            {
                 

                int loggedEmp = (int)await _userService
            .GetAllForAsync()
            .Where(u => u.Id == UserId)
            .Select(us => us.EmployeeId)
            .FirstOrDefaultAsync();

                if (ModelState.IsValid)
                {
                    var result = await _Staffattendanceervice.UpdateForAsync(loggedEmp, RecodrdToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }


        [HttpPut("CheckOut")]
        public async Task<IActionResult> CheckOut(StaffattendanceCheckOut RecodrdToUpdate)
        {
            try
            { 

                if (ModelState.IsValid)
                {
                    int loggedEmp = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.EmployeeId)
           .FirstOrDefaultAsync();

                    var result = await _Staffattendanceervice.UpdateForAsync(loggedEmp, RecodrdToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }


        
        
        [HttpPut("BreakIn")]
        public async Task<IActionResult> BreakIn( StaffattendanceBreakIn RecodrdToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    int loggedEmp = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.EmployeeId)
           .FirstOrDefaultAsync();

                    var result = await _Staffattendanceervice.UpdateForAsync(loggedEmp, RecodrdToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }



         
       
        [HttpPut("BreakOut")]
        public async Task<IActionResult> BreakOut(StaffattendanceBreakOut RecodrdToUpdate)
        {
            try
            {
                if (ModelState.IsValid)
                {

                    int loggedEmp = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.EmployeeId)
           .FirstOrDefaultAsync();

                    var result = await _Staffattendanceervice.UpdateForAsync(loggedEmp, RecodrdToUpdate);
                    return this.AppOk(result);
                }
                else
                {
                    return this.AppInvalidModel(ModelState);
                }
            }
            catch (Exception ex)
            {
                //Log ex
                return this.AppFailed(ex);
            }
        }


        //// DELETE api/<controller>/5
        //[HttpDelete]
        //[Route("{id}")]
        //public async Task<IActionResult> Delete(int id)
        //{
        //    try
        //    {
        //        var result = await _Staffattendanceervice.DeleteForAsync(id, UserId);
        //        return this.AppOk(result);
        //    }
        //    catch (Exception ex)
        //    {
        //        return this.AppFailed(ex);
        //    }
        //}



        [HttpPost]
        [Route("filter")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> filter(StaffattendanceFilterModel input, int currentPage = 1)
        {
            int loggedEmp = (int)await _userService
           .GetAllForAsync()
           .Where(u => u.Id == UserId)
           .Select(us => us.EmployeeId)
           .FirstOrDefaultAsync();

            var model = _Staffattendanceervice.Filter(input, loggedEmp);

          
            return await this.GetPagedData(currentPage, model );
        }


        [HttpPost]
        [Route("Search")]
        [ModuleAction(Actions.Display)]
        public async Task<IActionResult> Search(InputSearchModel input, int currentPage = 1)
        {
            var model = _Staffattendanceervice.Search(input.date);
            return await this.GetPagedData(currentPage, model, 1000);
        }

       

        [HttpGet]
        [Route("Excel")]
        public async Task<IActionResult> ExportToExcel()
        {
            var result = await _Staffattendanceervice.GetAllForAsync().ToListAsync();
            return this.ExportToExcelMvc("StaffAssistant.xlsx", result);
        }
        //[HttpPost]
        //[Route("Excel")]
        //public async Task<IActionResult> ImportToExcel(IFormFile file)
        //{
        //    var list = this.GetListFromExcel<StaffattendanceViewModel>(file.OpenReadStream());
        //    var result = await _Staffattendanceervice.ImportListAsync(list);
        //    return this.AppOk(result);
        //}
    }
}
