﻿using AspNetCore.ReportingServices.ReportProcessing.ReportObjectModel;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.Constants;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.API.Controllers
{
    public class PermissionsController : AppBaseController
    {
        private readonly IPermissionService _permissionService;
        private readonly IRoleService _roleService;

        public PermissionsController(IPermissionService permissionService, IRoleService roleService)
        {
            this._permissionService = permissionService;
            this._roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var members = typeof(AppModules).GetMembers().Where(m => m.MemberType == MemberTypes.Field || m.MemberType == MemberTypes.NestedType);
            List<ModuleInfo> listOfModules = new List<ModuleInfo>();
            foreach (var member in members)
            {
                if (member.MemberType == MemberTypes.Field)
                {
                    listOfModules.Add(new ModuleInfo { Name = member.Name, Value = member.Name });
                }
                else
                {
                    var nestedM = typeof(AppModules).GetNestedTypes().OrderBy(x => x.Name).FirstOrDefault(t => t.Name == member.Name)
                        .GetFields().Select(f => new ModuleInfo { Name = f.Name, Value = f.GetRawConstantValue() as string });
                    listOfModules.Add(new ModuleInfo { Name = member.Name, Value = member.Name, Children = nestedM.ToList() });
                }
            }
            var modules = typeof(AppModules).GetFields().Select(f => f.GetRawConstantValue()).Where(f => f != null);
            var nestedModules = typeof(AppModules).GetNestedTypes()
                .SelectMany(t => t.GetFields()
                .Select(f => f.GetRawConstantValue())).Where(f => f != null);
            var roles = await _roleService.GetAllForAsync().ToListAsync();
            var result = new
            {
                Modules = listOfModules,
                Actions = typeof(Actions).GetFields().Select(f => f.GetRawConstantValue()).Where(f => f != null),
                Roles = roles
            };
            return this.AppSuccess(result);
        }

        [HttpGet("{roleName}")]
        public async Task<IActionResult> Get(string roleName)
        {
            var result = await _permissionService.FindByRoleAsync(roleName);
            return this.AppSuccess(result);
        }

        [HttpPost]
        public async Task<IActionResult> Post(List<InputPermissionModel> model)
        {
            var result = await _permissionService.CreateForAsync(model);
            var permissions = (await _permissionService.FindByUserAsync(UserId)).Select(p => new
            {
                Module = p.Module,
                //Actions = p.Actions
                p.CreateAction,
                p.UpdateAction,
                p.DeleteAction,
                p.DisplayAction,
                p.PrintAction
            });
            result.Payload = permissions;
            return this.AppOk(result);
        }
    }
}
