﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FalconFramework.API.Models;
using FalconFramework.ApplicationCore.DTOs;

namespace FalconFramework
{
    [Authorize]
    public class NotificationsHub : Hub
    {

        public NotificationsHub()
        {

        }

        public void Notify(NotificationViewModel model)
        {

        }
        public override Task OnConnectedAsync()
        {
            return base.OnConnectedAsync();
        }

        public override Task OnDisconnectedAsync(Exception exception)
        {
            return base.OnDisconnectedAsync(exception);
        }
    }
}
