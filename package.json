{"name": "FalconERPApp", "version": "8.1.6", "scripts": {"prestart": "node aspnetcore-https", "start": "run-script-os", "start:windows": "ng serve  --port 4200 --ssl --ssl-cert \"%APPDATA%\\ASP.NET\\https\\%npm_package_name%.pem\" --ssl-key \"%APPDATA%\\ASP.NET\\https\\%npm_package_name%.key\" --proxy-config proxy.conf.js", "start:default": "ng serve  --port 4200 --ssl --ssl-cert \"$HOME/.aspnet/https/${npm_package_name}.pem\" --ssl-key \"$HOME/.aspnet/https/${npm_package_name}.key\" --proxy-config proxy.conf.js", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "rtl": "webpack --config=rtl.config.js", "ltr": "webpack --config=ltr.config.js"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/localize": "^19.0.0", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "19.0.0", "@angular/platform-server": "^19.0.0", "@angular/router": "19.0.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@fortawesome/fontawesome-free": "^6.6.0", "@microsoft/signalr": "^8.0.7", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ng-select/ng-select": "^14.7.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@popperjs/core": "2.11.8", "@typescript-eslint/eslint-plugin": "^8.13.0", "ag-grid-angular": "^33.2.4", "ag-grid-community": "^33.2.4", "animate.css": "4.1.1", "apexcharts": "^4.7.0", "autoprefixer": "^10.4.20", "awesome-image-viewer": "^1.0.60", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "clipboard": "2.0.11", "devextreme": "^24.2.6", "devextreme-angular": "^24.2.6", "devextreme-aspnet-data-nojquery": "^4.0.2", "devextreme-schematics": "^1.7.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "file-saver-es": "^2.0.5", "howler": "^2.2.4", "jquery": "^3.7.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "line-awesome": "^1.3.0", "moment": "^2.30.1", "ng-apexcharts": "^1.15.0", "ng-inline-svg-2": "^15.0.1", "ngx-clipboard": "^16.0.0", "nouislider": "^15.8.1", "object-path": "^0.11.8", "prism-themes": "^1.9.0", "prismjs": "^1.29.0", "recordrtc": "^5.6.2", "run-script-os": "^1.1.6", "rxjs": "^7.8.0", "socicon": "3.0.5", "sweetalert2": "^11.19.1", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/schematics": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "19.0.0", "@types/bootstrap": "^5.2.10", "@types/file-saver-es": "^2.0.3", "@types/howler": "^2.2.12", "@types/jasmine": "^5.1.4", "@types/node": "^18.18.0", "@types/object-path": "^0.11.4", "@types/prismjs": "^1.26.5", "@types/recordrtc": "^5.6.14", "@types/sass-loader": "^8.0.9", "@typescript-eslint/parser": "^8.13.0", "css-loader": "^7.1.2", "del": "^8.0.0", "devextreme-cli": "^1.7.1", "devextreme-themebuilder": "^24.1.7", "eslint": "^9.14.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "mini-css-extract-plugin": "^2.9.2", "ngx-toastr": "^19.0.0", "rtlcss-webpack-plugin": "4.0.7", "sass-loader": "^16.0.3", "typescript": "~5.5.2", "webpack": "^5.96.1", "webpack-cli": "^5.1.4"}, "resolutions": {"autoprefixer": "10.4.5"}}