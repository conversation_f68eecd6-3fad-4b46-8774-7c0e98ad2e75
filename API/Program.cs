using DotNetEnv;
using FalconFramework;
using FalconFramework.Infrastructure.Data;
using Integration.Salla.Core.API;
using Integration.WhatsAppBusiness;
using Integration.WhatsAppBusiness.WebAPI.Helpers;
using Integration.WhatsAppBusiness.WebAPI.Middlewares;
using Integration.WhatsAppBusiness.WebAPI.Models;
using Integration.Zid.API;
using Microsoft.OpenApi.Models;
using System.Net;
using System.Security.Cryptography.X509Certificates;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllersWithViews().AddNewtonsoftJson();
builder.Services.AddCors();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Falcon Framework API Docs", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme. 
                      Enter your token in the text input below.
                      Example: '12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "Bearer",
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });
});

// Register custom services
builder.Services.AddWhatsAppServices();
builder.Services.AddDataAccessServices(builder.Configuration);
builder.Services.AddMemoryCache();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddApplicationApi(builder.Configuration);
builder.Services.AddSallaAPI();
builder.Services.AddZidAPI();
builder.Services.AddScoped<IAppCacheHelper, AppCacheHelper>();
builder.Services.Configure<WebHookUrlsOptions>(builder.Configuration.GetSection("Webhooks"));




var app = builder.Build();

//// Redirect root to Swagger
app.Use(async (context, next) =>
{
    if (context.Request.Path == "/")
    {
        context.Response.Redirect("/swagger");
        return;
    }
    await next();
});

//await DbInitializer.Seed(app.Services);

app.UseDeveloperExceptionPage();
app.UseSwagger();
// Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Falcon ERP API V1");
});

//app.UseSwaggerUI(action =>
//{
//    action.EnablePersistAuthorization();
//});



//// Configure the HTTP request pipeline
//if (app.Environment.IsDevelopment() || app.Configuration.GetValue<bool>("RunInDocker"))
//{
//    app.UseDeveloperExceptionPage();
//    app.UseSwagger();
//    // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
//    app.UseSwaggerUI(c =>
//    {
//        c.SwaggerEndpoint("/swagger/v1/swagger.json", "My API V1");
//    });

//    //app.UseSwaggerUI(action =>
//    //{
//    //    action.EnablePersistAuthorization();
//    //});
//}
//else
//{

//    app.UseExceptionHandler("/Home/Error");
//    app.UseHsts();
//}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseRouting();
app.UseCors(c =>
{
    c.AllowAnyMethod()
     .AllowAnyHeader()
     .AllowAnyOrigin();
});

app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<SettingsMiddleware>();

app.UseWhen(context => context.Request.Path.Value.Contains("api/wbsalla"), appBuilder =>
{
    appBuilder.UseMiddleware<SallaMiddleware>();
});

app.UseWhen(context => context.Request.Path.Value.Contains("api/wbzid"), appBuilder =>
{
    appBuilder.UseMiddleware<ZidMiddleware>();
});

app.MapControllerRoute(
    name: "default",
    pattern: "api/{controller}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "mvc",
    pattern: "{controller}/{action=Index}/{id?}");

//app.MapFallbackToFile("index.html");

app.MapHub<NotificationsHub>("/notifications");


Env.Load();

app.Run();
