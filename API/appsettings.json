{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.DataProtection": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=sql.falcon-v.com,5777;Initial Catalog=FalconCloudERPDBDemo;Integrated Security=false;Trust Server Certificate=true;uid=FalconCloudsa;password=****************;"}, "AppSettings": {"Secret": "aU9MMXFmWVlfbjdoNjRtbUNkYjdtVGlicTNWZVVmVWJFa0pyZUd1SW9OREVYSEdtc0JJYTI5UEN", "ExcelFilePath": "initialData.xlsx"}, "Mail": {"Host": "mail.falcon-v.com", "Port": "465", "PWD": "falcon-v@2022", "User": "<EMAIL>"}, "Webhooks": {"Whatsapp": "https://demoerp.falcon-v.com/api/WbWebhooks", "Salla": "https://demoerp.falcon-v.com/api/WbStoreWebhooks/salla", "Zid": "https://demoerp.falcon-v.com/api/WbStoreWebhooks/zid"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:5001"}}}}