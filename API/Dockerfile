# from windows
#FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
# from mac
FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/aspnet:9.0 AS base

#USER $APP_UID
WORKDIR /app
#EXPOSE 8080
EXPOSE 5000

ENV ConnectionStrings__DefaultConnection="Data Source=sql.falcon-v.com,5777;Initial Catalog=FalconCloudERPDBDemo;Integrated Security=false;Trust Server Certificate=true;uid=FalconCloudsa;password=****************;"
ENV Webhooks__Whatsapp=https://demoerp.falcon-v.com/api/WbWebhooks
ENV Webhooks__Salla=https://demoerp.falcon-v.com/api/WbStoreWebhooks/salla
ENV Webhooks__Zid=https://demoerp.falcon-v.com/api/WbStoreWebhooks/zid


# from windows
#FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

# from mac
FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/sdk:9.0 AS build


#ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["API/API.csproj", "API/"]
COPY ["EInvoiceKSADemo.Helpers/Integration.Zatca.csproj", "EInvoiceKSADemo.Helpers/"]
COPY ["ETA/Integration.ETA.csproj", "ETA/"]
COPY ["FalconFramework.API/FalconFramework.API.csproj", "FalconFramework.API/"]
COPY ["FalconFramework.ApplicationCore/FalconFramework.ApplicationCore.csproj", "FalconFramework.ApplicationCore/"]
COPY ["FalconFramework.SharedKernel/FalconFramework.SharedKernel.csproj", "FalconFramework.SharedKernel/"]
COPY ["Integration.Sms/Integration.Sms.csproj", "Integration.Sms/"]
COPY ["FalconFramework.Infrastructure/FalconFramework.Infrastructure.csproj", "FalconFramework.Infrastructure/"]
COPY ["FalconFramework.ReportViewer/FalconFramework.ReportViewer.csproj", "FalconFramework.ReportViewer/"]
COPY ["Integration.Salla.Core.API/Integration.Salla.Core.API.csproj", "Integration.Salla.Core.API/"]
COPY ["Integration.Salla.Core.Web.Authorization/Integration.Salla.Core.Web.Authorization.csproj", "Integration.Salla.Core.Web.Authorization/"]
COPY ["Integration.WhatsAppBusiness.CloudAPI/Integration.WhatsAppBusiness.CloudAPI.csproj", "Integration.WhatsAppBusiness.CloudAPI/"]
COPY ["Integration.WhatsAppBusiness.DataAccess/Integration.WhatsAppBusiness.DataAccess.csproj", "Integration.WhatsAppBusiness.DataAccess/"]
COPY ["Integration.WhatsAppBusiness.WebAPI/Integration.WhatsAppBusiness.WebAPI.csproj", "Integration.WhatsAppBusiness.WebAPI/"]
COPY ["Integration.Zid.Core/Integration.Zid.Core.csproj", "Integration.Zid.Core/"]
COPY ["Integration.WooCommerce/Integration.WooCommerce.csproj", "Integration.WooCommerce/"]
RUN dotnet restore "API/API.csproj"
COPY . .
WORKDIR "/src/API"
RUN dotnet build "API.csproj" -c Release -o /app/build


FROM build AS publish
#ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "API.dll"]