<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9d3a2af8-f62b-4b62-a774-577705809d75</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <ApplicationIcon>cl.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="cl.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EInvoiceKSADemo.Helpers\Integration.Zatca.csproj" />
    <ProjectReference Include="..\ETA\Integration.ETA.csproj" />
    <ProjectReference Include="..\FalconFramework.API\FalconFramework.API.csproj" />
    <ProjectReference Include="..\FalconFramework.ApplicationCore\FalconFramework.ApplicationCore.csproj" />
    <ProjectReference Include="..\FalconFramework.Infrastructure\FalconFramework.Infrastructure.csproj" />
    <ProjectReference Include="..\FalconFramework.ReportViewer\FalconFramework.ReportViewer.csproj" />
    <ProjectReference Include="..\FalconFramework.SharedKernel\FalconFramework.SharedKernel.csproj" />
    <ProjectReference Include="..\Integration.Salla.Core.API\Integration.Salla.Core.API.csproj" />
    <ProjectReference Include="..\Integration.Salla.Core.Web.Authorization\Integration.Salla.Core.Web.Authorization.csproj" />
    <ProjectReference Include="..\Integration.Sms\Integration.Sms.csproj" />
    <ProjectReference Include="..\Integration.WhatsAppBusiness.CloudAPI\Integration.WhatsAppBusiness.CloudAPI.csproj" />
    <ProjectReference Include="..\Integration.WhatsAppBusiness.DataAccess\Integration.WhatsAppBusiness.DataAccess.csproj" />
    <ProjectReference Include="..\Integration.WhatsAppBusiness.WebAPI\Integration.WhatsAppBusiness.WebAPI.csproj" />
    <ProjectReference Include="..\Integration.WooCommerce\Integration.WooCommerce.csproj" />
    <ProjectReference Include="..\Integration.Zid.Core\Integration.Zid.Core.csproj" />
  </ItemGroup>

</Project>
