﻿using ETA.Models;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Data;
using System.Dynamic;
using System.Threading.Tasks;

namespace ETA
{
    public class SendEinvoice
    {
        public string pinpass;
        public string ActivationKey;
        public string client_id;
        public string client_secret_1;
        public string client_secret_2;
        public string varEinvoiceType;
        public string TokenCertificate;
        public string DocumentVerson;

        public string FalconSignerURL;
        // public DataSet returnds;

        private string BaseUrl;

        EtaAPIToken _etaapitoken = new EtaAPIToken();

        public OperationResult sendinvoice(InvoiceBody invoice_sn)
        {
            /* asunc task for sending invoice  */

            _etaapitoken.client_id = client_id;
            _etaapitoken.client_secret_1 = client_secret_1;
            _etaapitoken.client_secret_2 = client_secret_2;

            getBaseUrl();


            String tok = _etaapitoken.GetBearerToken();

            var client = new RestClient(BaseUrl);
            var request = new RestRequest("/api/" + DocumentVerson + "/documentsubmissions", Method.Post);
            request.AddHeader("authorization", @"Bearer " + tok);
            request.AddHeader("cache-control", "no-cache");
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("User-Agent", "odoo");
            request.AddHeader("Accept", "*/*");
            request.AddHeader("Accept-Encoding", "gzip, deflate, br");
            request.AddHeader("Connection", "keep-alive");
            request.AddHeader("Accept", "application/json");
            request.RequestFormat = DataFormat.Json;

            Boolean uingservice = false;
            string data;
            if (uingservice)
            {
                data = SignerHelper.Sign(new RequestBody { Data = invoice_sn, TokenPin = pinpass, TokenCertificate = TokenCertificate, ActivationKey = ActivationKey }, FalconSignerURL);

            }
            else
            {

                data = SignerHelper.SignLocal(invoice_sn, pinpass, TokenCertificate);
            }

            try

            {

                if (data == null)
                {
                    return OperationResult.Failed("No Data  found From Falcon Service");

                }

            }
            catch (Exception ex)
            {
                return OperationResult.Failed(ex.Message);
            }

            if (data != null)
            {

                if (data.Contains("No slots found"))
                {
                    return OperationResult.Failed("token not found");

                }

                var result = client.Execute(request.AddJsonBody(data));


                if ((int)result.StatusCode == 202)
                {
                    return OperationResult.Succeeded(data: result.Content);
                    //return "Invoice Submited";
                }
                else
                {
                    return OperationResult.Failed(result.ErrorMessage + result.StatusCode.ToString());
                }

                //if (result.Content != string.Empty)
                //{

                //    try
                //    {
                //        // JObject o = JObject.Parse(result.Content);

                //        //returnds = SignerHelper.ReadDataFromJson(o.ToString());

                //        return result.Content;

                //    }
                //    catch (Exception)
                //    {

                //    }

                //}

            }
            return OperationResult.Failed();

        }



        public OperationResult print_invoice(string electronicInvoiceID)
        {

            getBaseUrl();

            var client = new RestClient();
            var url = BaseUrl + "/api/v1/documents/" + electronicInvoiceID + "/details";

            _etaapitoken.client_id = client_id;
            _etaapitoken.client_secret_1 = client_secret_1;
            _etaapitoken.client_secret_2 = client_secret_2;

            String tok = _etaapitoken.GetBearerToken();

            var request = new RestRequest(url, Method.Get);
            request.AddHeader("authorization", @"Bearer " + tok);
            request.AddHeader("cache-control", "no-cache");
            request.AddHeader("Content-Type", "application/json");
            var result = client.Execute(request);

            //JObject o = JObject.Parse(result.Content);

            //returnds = SignerHelper.ReadDataFromJson(o.ToString());

            return OperationResult.Succeeded(data: result.Content);
        }

        //<parser-error>
        private void getBaseUrl()
        {

            if (varEinvoiceType == "production")
            {
                BaseUrl = "https://api.invoicing.eta.gov.eg";
                _etaapitoken.TokenAPIUrl = "https://id.eta.gov.eg/connect/token";

            }
            else
            {
                //BaseUrl = "https://id.preprod.eta.gov.eg";
                BaseUrl = "https://api.preprod.invoicing.eta.gov.eg";
                _etaapitoken.TokenAPIUrl = "https://id.preprod.eta.gov.eg/connect/token";
            }
        }

        public OperationResult Cancel_invoice(string electronicInvoiceID)
        {

            var body = new jsonObject();
            body.status = "cancelled";
            body.reason = "cancelled";


            getBaseUrl();

            var client = new RestClient();
            var url = BaseUrl + "/api/v1.0/documents/state/" + electronicInvoiceID + "/state";

            _etaapitoken.client_id = client_id;
            _etaapitoken.client_secret_1 = client_secret_1;
            _etaapitoken.client_secret_2 = client_secret_2;

            String tok = _etaapitoken.GetBearerToken();

            var request = new RestRequest(url, Method.Put);
            request.AddHeader("authorization", @"Bearer " + tok);
            request.AddHeader("cache-control", "no-cache");
            request.AddHeader("Content-Type", "application/json");

            request.AddJsonBody(body);
            var result = client.Execute(request);

            if ((int)result.StatusCode == 200)
            {
                return OperationResult.Succeeded("Invoice Canceld");
            }
            else
            {
                return OperationResult.Failed(result.ErrorMessage + result.StatusCode.ToString());
            }

        }

        public OperationResult reject_invoice(string electronicInvoiceID)
        {

            var body = new jsonObject();
            body.status = "rejected";
            body.reason = "rejected";


            getBaseUrl();

            var client = new RestClient();
            var url = BaseUrl + "/api/v1.0/documents/state/" + electronicInvoiceID + "/state";

            _etaapitoken.client_id = client_id;
            _etaapitoken.client_secret_1 = client_secret_1;
            _etaapitoken.client_secret_2 = client_secret_2;

            String tok = _etaapitoken.GetBearerToken();

            var request = new RestRequest(url, Method.Put);
            request.AddHeader("authorization", @"Bearer " + tok);
            request.AddHeader("cache-control", "no-cache");
            request.AddHeader("Content-Type", "application/json");
            request.AddJsonBody(body);
            var result = client.Execute(request);

            if ((int)result.StatusCode == 200)
            {
                return OperationResult.Succeeded("Invoice Canceld");
            }
            else
            {
                return OperationResult.Failed(result.ErrorMessage + result.StatusCode.ToString());
            }

        }



    }

    public class jsonObject
    {

        public string status { get; set; }
        public string reason { get; set; }
        //jsonObject.status = "cancelled";
        //jsonObject.reason = "cancelled";

    }
}
