﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Ocsp;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ETA
{
    public  class send_eta_class
    {
        public static DataSet ReadDataFromJson(string jsonString, XmlReadMode mode = XmlReadMode.Auto)
        {
            //// Note:Json convertor needs a json with one node as root
            jsonString = $"{{ \"rootNode\": {{{jsonString.Trim().TrimStart('{').TrimEnd('}')}}} }}";
            //// Now it is secure that we have always a Json with one node as root 
            var xd = JsonConvert.DeserializeXmlNode(jsonString);

            //// DataSet is able to read from XML and return a proper DataSet
            var result = new DataSet();
            result.ReadXml(new System.Xml.XmlNodeReader(xd), mode);
            return result;
        }

        public static  HttpClient _httpclient = new HttpClient();
        public static string GetBearerToken()
        {
            String token = "";

            _httpclient.DefaultRequestHeaders.Clear();

            using (var request = new HttpRequestMessage(HttpMethod.Post, config_class.tok_url))
                {

                    //  cli.DefaultRequestHeaders.Add("Accept", "application/json");

                    request.Headers.TryAddWithoutValidation("cache-control", "no-cache");
                    request.Headers.TryAddWithoutValidation("content-type", "application/x-www-form-urlencoded");
                    request.Headers.TryAddWithoutValidation("User-Agent", "swap_erp");
                   
                    Dictionary<string, string> para = new Dictionary<string, string>();
                    para.Add("client_id", config_class.client_id);
                    para.Add("client_secret", config_class.client_secret);
                    para.Add("grant_type", "client_credentials");


                    request.Content = new FormUrlEncodedContent(para);

                    HttpResponseMessage response = _httpclient.SendAsync(request).Result;

                    string apiResponse = response.Content.ReadAsStringAsync().Result;
                    token = JObject.Parse(apiResponse)["access_token"].ToString();
                    string jj = token;
                        return token;

                }

            return "";
        }
        public static async Task< string> pos_connect()
        {

            String token = "";
           
                _httpclient.DefaultRequestHeaders.Clear();

                using (var request = new HttpRequestMessage(HttpMethod.Post, config_class.tok_url))
                    {
                try
                {
                    request.Headers.TryAddWithoutValidation("cache-control", "no-cache");
                    request.Headers.TryAddWithoutValidation("content-type", "application/x-www-form-urlencoded");
                    request.Headers.TryAddWithoutValidation("User-Agent", "swap_erp");
                    request.Headers.TryAddWithoutValidation("Accept", "*");
                    request.Headers.TryAddWithoutValidation("Connection", "keep-alive");
                    request.Headers.TryAddWithoutValidation("Accept-Encoding", "gzip, deflate, br");
                    request.Headers.TryAddWithoutValidation("posserial", config_class.pos_eta_serial);
                    request.Headers.TryAddWithoutValidation("pososversion", config_class.os);
                    request.Headers.TryAddWithoutValidation("presharedkey", "");


                    Dictionary<string, string> para = new Dictionary<string, string>();
                  
                   
                    para.Add("content-type", "application/x-www-form-urlencoded");
                    para.Add("grant_type", "client_credentials");
                    para.Add("client_id", config_class.client_id);
                    para.Add("client_secret", config_class.client_secret);

                    request.Content = new FormUrlEncodedContent(para);

                    HttpResponseMessage response = _httpclient.SendAsync(request).Result;

                    string apiResponse = response.Content.ReadAsStringAsync().Result;
                    token = JObject.Parse(apiResponse)["access_token"].ToString();

                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        token = JObject.Parse(apiResponse)["access_token"].ToString();
                    }
                    else
                    {
                        token = null;
                    }


                }
                catch (Exception ex)
                {
                    string msg = ex.Message;
                    

                }

                //  cli.DefaultRequestHeaders.Add("Accept", "application/json");





            }





            return token;


        }

        async static Task<string> get_recipt_to_update_async(string uuid)
        {
            string tok = GetBearerToken();

            try
            {
                _httpclient.DefaultRequestHeaders.Clear();

                string uri = config_class.BaseUrl + "/api/v1/receiptsubmissions/" + uuid + "/details?PageNo=1&PageSize=100";

                using (var request = new HttpRequestMessage(HttpMethod.Get, uri))
                {
                    request.Headers.TryAddWithoutValidation("authorization", String.Format("Bearer {0}", tok));
                    request.Headers.TryAddWithoutValidation("cache-control", "no-cache");


                    HttpResponseMessage response = _httpclient.SendAsync(request).Result;

                    string apiResponse = response.Content.ReadAsStringAsync().Result;

                    JObject o = JObject.Parse(apiResponse);

                    DataSet ds = ReadDataFromJson(o.ToString());

                    // gridView3.Columns.Clear();
                    DataTable dt = ds.Tables[0];

                    if (dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];

                        return await Task.FromResult(dr["status"].ToString());
                    }

                }


            }

            catch (Exception x)
            {
                string e = x.Message;

            }

            return "";

        }

        public static string get_recipt_to_update(string uuid)
        {
            string tok = GetBearerToken();


            try
            {
                _httpclient.DefaultRequestHeaders.Clear();

                string uri = config_class.BaseUrl + "/api/v1/receiptsubmissions/" + uuid + "/details?PageNo=1&PageSize=100";


                using (var request = new HttpRequestMessage(HttpMethod.Get, uri))
                {
                    request.Headers.TryAddWithoutValidation("authorization", String.Format("Bearer {0}", tok));
                    request.Headers.TryAddWithoutValidation("cache-control", "no-cache");

                    HttpResponseMessage response = _httpclient.SendAsync(request).Result;

                    string apiResponse = response.Content.ReadAsStringAsync().Result;

                    JObject o = JObject.Parse(apiResponse);

                    DataSet ds = ReadDataFromJson(o.ToString());

                    // gridView3.Columns.Clear();
                    DataTable dt = ds.Tables[0];

                    if (dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];



                        return dr["status"].ToString();
                    }

                }


            }

            catch (Exception x)
            {
                string e = x.Message;

               
            }

            return "";


        }
        public static async Task<send_respons_model> send_recipt(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool calc_uuid, bool refund)
        {
            string tok = await pos_connect();
            if (tok == null || tok=="")
            {
                tok = GetBearerToken();
            }
            string data = ETA_pos.pos_signature(doc, _buyer, _seller, liens, calc_uuid, refund);


            string uri = config_class.BaseUrl + "/api/v1/receiptsubmissions";



            _httpclient.DefaultRequestHeaders.Clear();
            _httpclient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("authorization", @"Bearer " + tok);
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("cache-control", "no-cache");

            //  request.AddHeader("Cookie", "75fd0698a2e84d6b8a3cb94ae54530f3=054e7d0fb7353830e763d83ee8bd30d6");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent", "swap-erp");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Accept", "*");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Encoding", "gzip, deflate, br");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Connection", "keep-alive");

            var cont = new StringContent(data, Encoding.UTF8, "application/json");
            var res =await _httpclient.PostAsync(uri, cont);

                string  apiResponse =await  res.Content.ReadAsStringAsync();
               if( res.IsSuccessStatusCode == true)
                {


                    try
                    {

                        JObject o = JObject.Parse(apiResponse);
                        DataSet ds = ReadDataFromJson(o.ToString());

                        string submissionId = ds.Tables[0].Rows[0]["submissionId"].ToString();
                   
                        string status =  get_recipt_to_update(submissionId);

                        send_respons_model reposnse = new send_respons_model();
                    reposnse.uuid = doc.uuid; reposnse.status = status; reposnse.internal_id = doc.receiptNumber; reposnse.submissionid = submissionId;
                    return reposnse;
                }
                    catch (Exception ex)
                    {
                    Console.WriteLine($"Error.{ex}");
                }

                }


         
            //   gridView1.SetRowCellValue(rowno, coll_error, result.Content);
           
            return null;
        }

        public static async Task<send_respons_model> send_recipt(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool calc_uuid)
        {
            string tok = await pos_connect();
            if (tok == null)
            {
                tok = GetBearerToken();
            }


            bool refund = false;
            if (doc.receiptType.ToLower() != "r")
                refund = false;
            else
                refund = true;
            string data = ETA_pos.pos_signature(doc, _buyer, _seller, liens, calc_uuid, refund);


            string uri = config_class.BaseUrl + "/api/v1/receiptsubmissions";



            // client.BaseAddress = new Uri(loggedUser.serverUrl + "/");
            _httpclient.DefaultRequestHeaders.Clear();
            _httpclient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("authorization", @"Bearer " + tok);
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("cache-control", "no-cache");

            //  request.AddHeader("Cookie", "75fd0698a2e84d6b8a3cb94ae54530f3=054e7d0fb7353830e763d83ee8bd30d6");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent", "swap-erp");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Accept", "*/*");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Encoding", "gzip, deflate, br");
            _httpclient.DefaultRequestHeaders.TryAddWithoutValidation("Connection", "keep-alive");

            var cont = new StringContent(data, Encoding.UTF8, "application/json");
            var res = await _httpclient.PostAsync(uri, cont);

            string apiResponse = await res.Content.ReadAsStringAsync();
            if (res.IsSuccessStatusCode == true)
            {


                try
                {

                    JObject o = JObject.Parse(apiResponse);
                    DataSet ds = ReadDataFromJson(o.ToString());

                    string submissionId = ds.Tables[0].Rows[0]["submissionId"].ToString();

                    string status = get_recipt_to_update(submissionId);

                    send_respons_model reposnse = new send_respons_model();
                    reposnse.uuid = doc.uuid; reposnse.status = status; reposnse.internal_id = doc.receiptNumber; reposnse.submissionid = submissionId;
                    return reposnse;


                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error.{ex}");
                }

            }



            //   gridView1.SetRowCellValue(rowno, coll_error, result.Content);

            return null;
        }
    }
}
