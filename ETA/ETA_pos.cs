﻿using Newtonsoft.Json;
using Org.BouncyCastle.Tls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ETA
{

    public class ETA_pos
    {


        public static List<object> _Pos_liens(List<pos_liens> liens)
        {
            List<object> invoiceLines = new List<object>();
            decimal[] itemDiscountData = { };

            foreach (var item in liens)
            {
                List<object> commercialDiscountData = item.discountlist.Cast<object>().ToList();

                List<object> taxableItems = item.taxelists.Cast<object>().ToList();

                invoiceLines.Add(new
                {
                    description = item.description,
                    itemType = item.itemType,
                    itemCode = item.itemCode,
                    unitType = item.unitType,
                    quantity = Math.Round(item.quantity, 5),
                    unitPrice = Math.Round(item.unitPrice, 5),
                    internalCode = item.internalCode,
                    totalSale = Math.Round(item.totalSale, 5) != 0 ? Math.Round(item.totalSale, 5) : 0,
                    total = Math.Round(item.total, 5) != 0 ? Math.Round(item.total, 5) : 0,
                    valueDifference = 0,
                    netSale = Math.Round(item.netSale, 5),
                    commercialDiscountData,
                    itemDiscountData,
                    taxableItems
                });
            }
            return invoiceLines;

        }

        public static object return_seller(pos_seller _issuer)
        {
            var o = new
            {
                rin = _issuer.rin,
                companyTradeName = _issuer.companyTradeName,
                branchCode = _issuer.branchCode,
                branchAddress = new
                {

                    country = _issuer.country,
                    governate = _issuer.governate,
                    regionCity = _issuer.regionCity,
                    street = _issuer.street,
                    buildingNumber = _issuer.buildingNumber,
                    postalCode = _issuer.postalCode,
                    floor = _issuer.floor,
                    room = _issuer.floor,
                    landmark = "",
                    additionalInformation = ""
                },



                deviceSerialNumber = _issuer.device_serial,

                syndicateLicenseNumber = "",
                activityCode = _issuer.activityCode
            };

            return o;
        }
        public static object return_reciver(Buyer _buyer)
        {

            var o = new
            {
                type = _buyer.type,
                id = _buyer.id,
                name = _buyer.name,
                mobileNumber = _buyer.mobileNumber,
                paymentNumber = _buyer.paymentNumber
            };
            return o;
        }

        public static string pos_signature(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool calc_uuid, bool refund)
        {
            string data = "";
            string uuid;
            if (calc_uuid == true)
            {


                if (!refund)
                {
                    uuid = get_uuid(doc, _buyer, _seller, liens, false, false);
                }
                else
                {
                    uuid = get_uuid(doc, _buyer, _seller, liens, false, true);
                }
                doc.uuid = uuid;
            }

            if (!refund)
            {
                data = recipt(doc, _buyer, _seller, liens, true);
            }
            else
            {
                data = recipt_refund(doc, _buyer, _seller, liens, true);
            }

            //  uuid= Signer.add_pos_uuid(data);

            //  System.Windows.Forms.MessageBox.Show(refund.ToString());


            string singendedata = Signer.add_pos_signatuer(data, "");
            return singendedata;
        }


        public static string get_uuid(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool with_uuid, bool refund)
        {
            string uuid = "";

            if (!refund)
            {
                uuid = Signer.add_pos_uuid(recipt(doc, _buyer, _seller, liens, false));

            }
            else
            {
                uuid = Signer.add_pos_uuid(recipt_refund(doc, _buyer, _seller, liens, false));
            }

            // doc.uuid = uuid;
            return uuid;
        }

        public static string recipt(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool with_uuid)
        {


            List<object> itemData = _Pos_liens(liens);
            var seller = return_seller(_seller);
            var buyer = return_reciver(_buyer);
            List<object> taxTotals = doc.total_tax_lis.Cast<object>().ToList();
            var obj = new
            {

                header = new
                {
                    dateTimeIssued = doc.dateTimeIssued.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    receiptNumber = doc.receiptNumber,
                    uuid = with_uuid == false ? "" : doc.uuid,
                    previousUUID = doc.previousUUID,


                    referenceOldUUID = "",
                    currency = doc.currency,
                    sOrderNameCode = "",
                    orderdeliveryMode = "",
                    grossWeight = 0,
                    exchangeRate = 0,
                    netWeight = 0

                },
                documentType = new
                {
                    receiptType = doc.receiptType,
                    typeVersion = doc.typeVersion

                },

                #region seller
                seller
                #endregion
                      ,
                #region reciver
                buyer
               ,



                #endregion


                itemData
                          ,
                totalCommercialDiscount = Math.Round(doc.totalCommercialDiscount, 5)
                          ,
                totalSales = Math.Round(doc.totalSales, 5)
                          ,
                netAmount = Math.Round(doc.netAmount, 5),
                feesAmount = 0
                          ,
                taxTotals
                          ,
                totalAmount = Math.Round(doc.totalAmount, 5),

                paymentMethod = doc.paymentMethod

            };



            string json = JsonConvert.SerializeObject(obj, Formatting.Indented);

            return json;
        }

        public static string recipt_refund(pos_document doc, Buyer _buyer, pos_seller _seller, List<pos_liens> liens, bool with_uuid)
        {


            List<object> itemData = _Pos_liens(liens);
            var seller = return_seller(_seller);
            var buyer = return_reciver(_buyer);
            List<object> taxTotals = doc.total_tax_lis.Cast<object>().ToList();
            var obj = new
            {

                header = new
                {
                    dateTimeIssued = doc.dateTimeIssued.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    receiptNumber = doc.receiptNumber,
                    uuid = with_uuid == false ? "" : doc.uuid,
                    previousUUID = doc.previousUUID,
                    referenceUUID = doc.referenceUUID,

                    referenceOldUUID = "",
                    currency = doc.currency,
                    exchangeRate = 0,
                    sOrderNameCode = "",
                    orderdeliveryMode = "",
                    grossWeight = 0,
                    netWeight = 0

                },
                documentType = new
                {
                    receiptType = doc.receiptType,
                    typeVersion = doc.typeVersion

                },

                #region seller
                seller
                #endregion
                      ,
                #region reciver
                buyer
               ,



                #endregion


                itemData
                          ,
                totalCommercialDiscount = Math.Round(doc.totalCommercialDiscount, 5)
                          ,
                totalSales = Math.Round(doc.totalSales, 5)
                          ,
                netAmount = Math.Round(doc.netAmount, 5),
                feesAmount = 0
                          ,
                taxTotals
                          ,
                totalAmount = Math.Round(doc.totalAmount, 5),

                paymentMethod = doc.paymentMethod





            };



            string json = JsonConvert.SerializeObject(obj, Formatting.Indented);

            return json;
        }

    }







    public class pos_liens
    {
        public string description { get; set; }
        public string itemType { get; set; }
        public string itemCode { get; set; }
        public string unitType { get; set; }
        public decimal quantity { get; set; }
        public decimal unitPrice { get; set; }
        public string internalCode { get; set; }
        public decimal totalSale { get; set; }
        public decimal total { get; set; }
        public decimal valueDifference { get; set; }
        public decimal netSale { get; set; }
        public decimal[] itemDiscountData { get; set; }
        public List<invoice_line_taxelist> taxelists { get; set; }
        public List<commercialDiscountData> discountlist { get; set; }

    }
    public class commercialDiscountData
    {
        public decimal amount { get; set; }
        public string description { get; set; }
    }
    public class pos_seller
    {
        public string rin { get; set; }
        public string companyTradeName { get; set; }
        public string country { get; set; } = "EG";
        public string branchCode { get; set; } = "0";
        public string governate { get; set; } = "cairo";
        public string regionCity { get; set; } = "cairo";
        public string street { get; set; } = "";
        public string activityCode { get; set; }
        public string device_serial { get; set; }
        public string buildingNumber { get; set; } = "1";
        public string postalCode { get; set; } = "1";
        public string floor { get; set; } = "1";
        public string room { get; set; } = "1";
    }
 

    public class Buyer
    {
        public string type { get; set; } = "P";
        public string id { get; set; } = "";
        public string name { get; set; } = "cash";
        public string mobileNumber { get; set; } = "";

        public string paymentNumber { get; set; } = "";
    }
    public class pos_document
    {
        public DateTime dateTimeIssued { get; set; }
        public string receiptNumber { get; set; }
        public string referenceUUID { get; set; }
        public string uuid { get; set; }
        public string previousUUID { get; set; }
        public string referenceOldUUID { get; set; }
        public string currency { get; set; }
        public decimal exchangeRate { get; set; }
        public string sOrderNameCode { get; set; }
        public string orderdeliveryMode { get; set; }
        public decimal grossWeight { get; set; }
        public decimal netWeight { get; set; }
        public string receiptType { get; set; }
        public string typeVersion { get; set; }
        public decimal totalCommercialDiscount { get; set; }
        public decimal totalSales { get; set; }
        public decimal netAmount { get; set; }
        public decimal feesAmount { get; set; }

        public decimal totalAmount { get; set; }


        public List<invoice_total_taxes> total_tax_lis { get; set; }
        public string paymentMethod { get; set; }





    }

}
