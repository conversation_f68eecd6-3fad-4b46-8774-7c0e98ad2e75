﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace ETA
{
    public class get_invoicejasn
    {

        Reciver client_data;
        IssuerData issuer_data;
        List<Invoiceline> inv_liens;
        public get_invoicejasn(IssuerData issuer)
        {
            issuer_data = issuer;

        }
        List<object> invoiceLines;

        List<object> taxTotals;

        public object return_issuer()
        {
            var o = new
            {
                address = new
                {
                    branchID = issuer_data.branchID,
                    country = issuer_data.country,
                    governate = issuer_data.governate,
                    regionCity = issuer_data.regionCity,
                    street = issuer_data.street,
                    buildingNumber = issuer_data.buildingNumber,
                    postalCode = issuer_data.postalCode,
                    floor = issuer_data.floor,
                    room = issuer_data.room,
                    landmark = issuer_data.landmark,
                    additionalInformation = ""
                },



                type = "B",
                id = issuer_data.id,
                name = issuer_data.name
            };
            return o;
        }

        public object return_reciver()
        {

            var o = new
            {
                address = new
                {
                    branchID = client_data.branchID,
                    country = client_data.country,
                    governate = client_data.governate,
                    regionCity = client_data.regionCity,
                    street = client_data.street,
                    buildingNumber = client_data.buildingNumber,
                    postalCode = client_data.postalCode,
                    floor = client_data.floor,
                    room = client_data.room,
                    landmark = client_data.landmark,
                    additionalInformation = ""
                },



                type = client_data.type,
                id = client_data.id,
                name = client_data.name
            };
            return o;
        }

        public object return_invoicedata(document_data invoice_data)
        {
            var o = new 
            {
                documentType = invoice_data.documentType,
                documentTypeVersion = invoice_data.documentTypeVersion,
                dateTimeIssued = Convert.ToDateTime(invoice_data.dateTimeIssued).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                taxpayerActivityCode = invoice_data.taxpayerActivityCode,
                internalID = invoice_data.internalID,
                purchaseOrderReference = invoice_data.purchaseOrderReference,
                purchaseOrderDescription = invoice_data.purchaseOrderDescription,
                salesOrderReference = invoice_data.salesOrderReference,
                salesOrderDescription = invoice_data.salesOrderDescription,
                proformaInvoiceNumber = invoice_data.proformaInvoiceNumber,
                payment = new
                {
                    bankName = invoice_data.bankName,
                    bankAddress = invoice_data.bankAddress,
                    bankAccountNo = invoice_data.bankAccountNo,
                    bankAccountIBAN = invoice_data.bankAccountIBAN,
                    swiftCode = invoice_data.swiftCode,
                    terms = ""

                },
                delivery = new
                {
                    approach = "",
                    packaging = "",
                    dateValidity = ""
                },
                invoiceLines,
                totalDiscountAmount=invoice_data.totalDiscountAmount,
                totalSalesAmount=invoice_data.totalSalesAmount,
                netAmount=invoice_data.netAmount,
                taxTotals,
                totalAmount=invoice_data.totalAmount,
                extraDiscountAmount=invoice_data.extraDiscountAmount,
                totalItemsDiscountAmount=invoice_data.totalItemsDiscountAmount


            };
            return o;
        }




    }


    // model for isser data  u have to fill all data if proprty is null then it must be ""
    public class IssuerData
    {
        // all this properties is mandatory
        public string branchID { get; set; } = "0";
        public string country { get; set; } = "EG";
        public string governate { get; set; } = "cairo";
        public string regionCity { get; set; } = "cairo";
        public string street { get; set; } = "";


        public string buildingNumber { get; set; } = "1";
        public string postalCode { get; set; } = "1";
        public string floor { get; set; } = "1";
        public string room { get; set; } = "1";
        public string id { get; set; } = "";

        public string name { get; set; } = "";

        //optinal properties
        public string landmark { get; set; } = "";
        public string additionalInformation { get; set; } = "";
        
    



    }

    public class Reciver
    {
        public string branchID { get; set; }
        public string country { get; set; } = "EG";
        public string governate { get; set; } = "cairo";
        public string regionCity { get; set; } = "cairo";
        public string street { get; set; } = "cairo";


        public string buildingNumber { get; set; } = "1";
        public string postalCode { get; set; } = "1";
        public string floor { get; set; } = "1";
        public string room { get; set; } = "1";

        public string landmark { get; set; } = "1";
        public string additionalInformation { get; set; }
        public string type { get; set; } = "P";
        public string id { get; set; }

        public string name { get; set; }
    }

    public class document_data
    {
        public string documentType { get; set; }
        public string documentTypeVersion { get; set; }
        public DateTime dateTimeIssued { get; set; }
        public string taxpayerActivityCode { get; set; }    
        // document serial 
        public string internalID { get; set; }
        public string purchaseOrderReference { get; set; } = "";
        public string purchaseOrderDescription { get; set; } = "";

        public string salesOrderReference { get; set; } = "";
        public string salesOrderDescription { get; set; } = "";
        public string proformaInvoiceNumber { get; set; } = "";

        public string bankName { get; set; }
        public string bankAddress { get; set; } = "";
        public string bankAccountNo { get; set; } = "";
        public string bankAccountIBAN { get; set; } = "";
        public string swiftCode { get; set; } = "";
        public string terms { get; set; } = "";
        public string approach { get; set; } = "";
        public string packaging { get; set; } = "";
        public string dateValidity { get;  set; } = "";

        public double totalDiscountAmount { get; set; } = 0.00000;
        public double totalSalesAmount { get; set; } = 0.0000;
        public double netAmount { get;  set; } = 0.00000;

        public double totalAmount { get; set; } = 0.00000;
        public double extraDiscountAmount { get; set; } = 0.00000;
        public double totalItemsDiscountAmount { get; set; } = 0.00000;

    }

    public class Invoiceline
    {
      

        public string description { get; set; }
        public string itemType { get; set; }
        public string itemCode { get; set; }
        public string unitType { get; set; }
        public decimal quantity { get; set; }

        public string internalCode { get; set; }

        public decimal salesTotal { get; set; }
        public decimal total { get; set; }
        public decimal valueDifference { get; set; }
        public decimal totalTaxableFees { get; set; }

        public decimal netTotal { get; set; }
        public decimal itemsDiscount { get; set; }

        public string currencySold { get; set; }
        public decimal amountEGP { get; set; }
        public decimal amountSold { get; set; }
        public decimal currencyExchangeRate { get; set; }
        public decimal discount_rate { get; set; }
        public decimal discount_amount { get; set; }
        public List<invoice_line_taxelist> taxex { get; set; }

    }

    public class invoice_line_taxelist
    {
        public string taxType { get; set; }
        public decimal amount { get; set; }
        public string subType { get; set; }
        public decimal rate { get; set; }
    }
    public class invoice_total_taxes
    {
        public string taxType { get; set; }
        public decimal amount { get; set; }
    }

}
