﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ETA.Models
{
    public class OperationResult
    {
        public bool Success { get; set; }

        public string Message { get; set; }

        public dynamic Data { get; set; }


        public OperationResult(bool success, string messag, dynamic data)
        {
            this.Success = success;
            this.Message = messag;
            this.Data = data;
        }

        public static OperationResult Succeeded(string msg = "Operation Completed Successfully!.", dynamic data = null)
        {
            return new OperationResult(true, msg, data);
        }
        public static OperationResult NotFound(string msg = "Item Not Found!.", dynamic data = null)
        {
            return new OperationResult(false, msg, data);
        }
        public static OperationResult Failed(string msg = "Operation Failed!.", dynamic data = null)
        {
            return new OperationResult(false, msg, data);
        }

        public static OperationResult Existed(string msg = "Item Already Exisited!.", dynamic data = null)
        {
            return new OperationResult(false, msg, data);
        }
    }
}
