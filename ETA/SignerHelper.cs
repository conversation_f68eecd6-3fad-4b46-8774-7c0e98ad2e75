﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Xml;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
namespace ETA
{

  

    public class SignerHelper
    {

        public static DataSet ReadDataFromJson(string jsonString, XmlReadMode mode = XmlReadMode.Auto)
        {


            //// Note:Json convertor needs a json with one node as root
            jsonString = $"{{ \"rootNode\": {{{jsonString.Trim().TrimStart('{').TrimEnd('}')}}} }}";
            //// Now it is secure that we have always a Json with one node as root 
            var xd = JsonConvert.DeserializeXmlNode(jsonString);

            //// DataSet is able to read from XML and return a proper DataSet
            var result = new DataSet();
             
            result.ReadXml(new XmlNodeReader(xd), mode);
             
            return result;
        }

        public static string SignLocal(InvoiceBody Data, string TokenPin, string TokenCertificate)
        {
            try
            {
                SignerStream signer = new SignerStream(TokenPin, TokenCertificate);

                string jsonString =   JsonConvert.SerializeObject(Data, Newtonsoft.Json.Formatting.Indented);
                

                var signedData = signer.add_signatuer(jsonString);

                return signedData;
            }
            catch (Exception ex)
            {

                return ex.Message.ToString();
            }
           

        }
        public static string Sign(RequestBody body, string FalconSignerURL)
        {
            string serviceurl = FalconSignerURL;
            var client = new RestClient(serviceurl);

            var request = new RestRequest();

            request.AddJsonBody(body);

            var res = client.Execute<string>(request, Method.Post);
            return res.Data;
             
        }
    }

    public class RequestBody
    {
        public string TokenPin { get; set; }
        public string TokenCertificate { get; set; }
        public InvoiceBody Data { get; set; }
        public string ActivationKey { get; set; }
    }
}
