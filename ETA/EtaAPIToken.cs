﻿using Newtonsoft.Json.Linq;
using RestSharp;
using System;

namespace ETA
{
    public class EtaAPIToken
    {
        public string TokenAPIUrl = "";
        public string client_id = "";
        public string client_secret_1 = "";
        public string client_secret_2 = "";

        public string GetBearerToken()
        {
            try
            {
                var client = new RestClient(TokenAPIUrl);
                var request = new RestRequest("", Method.Post);
                request.AddHeader("cache-control", "no-cache");
                request.AddHeader("content-type", "application/x-www-form-urlencoded");
                request.AddParameter("application/x-www-form-urlencoded", "grant_type=client_credentials&client_id=" +
                    client_id + "&client_secret=" + client_secret_1, ParameterType.RequestBody);
                var response = client.Execute(request);

                dynamic resp = JObject.Parse(response.Content);
                String token = resp.access_token;

                return token;
            }
            catch (Exception)
            {

            }

            return "";
        }

    }
}
