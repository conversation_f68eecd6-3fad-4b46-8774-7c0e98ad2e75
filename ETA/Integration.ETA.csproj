<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <!--<Nullable>enable</Nullable>-->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pkcs11Interop" Version="5.1.2" />
    <PackageReference Include="Pkcs11Interop.X509Store" Version="1.0.0" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.0" />
  </ItemGroup>

</Project>
