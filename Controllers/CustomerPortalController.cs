using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomerPortalController : ControllerBase
    {
        private readonly ILogger<CustomerPortalController> _logger;

        public CustomerPortalController(ILogger<CustomerPortalController> logger)
        {
            _logger = logger;
        }

        // Customer Login
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] CustomerLoginRequest request)
        {
            try
            {
                // TODO: Implement actual authentication logic
                // For now, return mock data
                
                if (request.Email == "<EMAIL>" && request.Password == "123456")
                {
                    var response = new CustomerLoginResponse
                    {
                        Success = true,
                        Token = GenerateMockToken(),
                        Customer = new CustomerInfo
                        {
                            Id = 1,
                            CompanyName = "شركة الأمل للتجارة",
                            ContactPerson = "أحمد محمد علي",
                            Email = "<EMAIL>",
                            Phone = "+************",
                            AccountNumber = "ACC-2024-001",
                            Status = "نشط",
                            JoinDate = DateTime.Parse("2024-01-15"),
                            SubscriptionPlan = "الباقة المتقدمة",
                            SubscriptionExpiry = DateTime.Parse("2025-01-15")
                        }
                    };

                    return Ok(response);
                }

                return Unauthorized(new { message = "بيانات تسجيل الدخول غير صحيحة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during customer login");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Get Customer Dashboard Data
        [HttpGet("dashboard")]
        [Authorize] // Add authorization when implemented
        public async Task<IActionResult> GetDashboard()
        {
            try
            {
                // TODO: Get actual customer ID from token
                var customerId = 1;

                var dashboardData = new CustomerDashboard
                {
                    Statistics = new DashboardStatistics
                    {
                        TotalQuotes = 12,
                        PendingQuotes = 3,
                        TotalInvoices = 45,
                        UnpaidInvoices = 2,
                        TotalTickets = 8,
                        OpenTickets = 1,
                        TotalPayments = 125000
                    },
                    RecentActivities = GetMockRecentActivities(),
                    QuickActions = GetMockQuickActions()
                };

                return Ok(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer dashboard");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Get Customer Quotes
        [HttpGet("quotes")]
        [Authorize]
        public async Task<IActionResult> GetQuotes([FromQuery] string? status = null, [FromQuery] string? search = null)
        {
            try
            {
                var quotes = GetMockQuotes();

                // Apply filters
                if (!string.IsNullOrEmpty(status) && status != "all")
                {
                    quotes = quotes.Where(q => q.StatusClass == status).ToList();
                }

                if (!string.IsNullOrEmpty(search))
                {
                    quotes = quotes.Where(q => 
                        q.Title.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        q.Id.Contains(search, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                return Ok(quotes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer quotes");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Get Customer Invoices
        [HttpGet("invoices")]
        [Authorize]
        public async Task<IActionResult> GetInvoices([FromQuery] string? status = null, [FromQuery] string? search = null)
        {
            try
            {
                var invoices = GetMockInvoices();

                // Apply filters
                if (!string.IsNullOrEmpty(status) && status != "all")
                {
                    invoices = invoices.Where(i => i.StatusClass == status).ToList();
                }

                if (!string.IsNullOrEmpty(search))
                {
                    invoices = invoices.Where(i => 
                        i.Title.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        i.Id.Contains(search, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer invoices");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Get Customer Support Tickets
        [HttpGet("support/tickets")]
        [Authorize]
        public async Task<IActionResult> GetSupportTickets([FromQuery] string? status = null, [FromQuery] string? priority = null)
        {
            try
            {
                var tickets = GetMockSupportTickets();

                // Apply filters
                if (!string.IsNullOrEmpty(status) && status != "all")
                {
                    tickets = tickets.Where(t => t.StatusClass == status).ToList();
                }

                if (!string.IsNullOrEmpty(priority) && priority != "all")
                {
                    tickets = tickets.Where(t => t.PriorityClass == priority).ToList();
                }

                return Ok(tickets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting support tickets");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Create Support Ticket
        [HttpPost("support/tickets")]
        [Authorize]
        public async Task<IActionResult> CreateSupportTicket([FromBody] CreateTicketRequest request)
        {
            try
            {
                // TODO: Implement actual ticket creation logic
                var ticket = new SupportTicket
                {
                    Id = $"TK-2024-{new Random().Next(100, 999)}",
                    Title = request.Title,
                    Description = request.Description,
                    Priority = request.Priority,
                    PriorityClass = GetPriorityClass(request.Priority),
                    Status = "مفتوحة",
                    StatusClass = "open",
                    Category = request.Category,
                    CreatedDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    LastUpdate = DateTime.Now.ToString("yyyy-MM-dd"),
                    AssignedTo = "فريق الدعم الفني"
                };

                return Ok(new { success = true, ticket = ticket, message = "تم إنشاء التذكرة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating support ticket");
                return StatusCode(500, new { message = "حدث خطأ في الخادم" });
            }
        }

        // Helper Methods
        private string GenerateMockToken()
        {
            // TODO: Implement actual JWT token generation
            return "mock_jwt_token_" + Guid.NewGuid().ToString();
        }

        private string GetPriorityClass(string priority)
        {
            return priority switch
            {
                "منخفضة" => "low",
                "متوسطة" => "medium",
                "عالية" => "high",
                "عاجلة" => "urgent",
                _ => "medium"
            };
        }

        private List<RecentActivity> GetMockRecentActivities()
        {
            return new List<RecentActivity>
            {
                new RecentActivity
                {
                    Id = "QT-2024-015",
                    Type = "quote",
                    Title = "عرض سعر جديد",
                    Description = "تم إنشاء عرض سعر رقم QT-2024-015",
                    Time = "منذ ساعتين",
                    Icon = "fas fa-file-invoice"
                },
                new RecentActivity
                {
                    Id = "INV-2024-089",
                    Type = "invoice",
                    Title = "تم دفع الفاتورة",
                    Description = "تم دفع الفاتورة رقم INV-2024-089",
                    Time = "أمس",
                    Icon = "fas fa-check-circle"
                }
            };
        }

        private List<QuickAction> GetMockQuickActions()
        {
            return new List<QuickAction>
            {
                new QuickAction
                {
                    Title = "طلب عرض سعر",
                    Description = "احصل على عرض سعر مخصص لاحتياجاتك",
                    Icon = "fas fa-file-plus",
                    Action = "request_quote"
                },
                new QuickAction
                {
                    Title = "تذكرة دعم فني",
                    Description = "احصل على المساعدة من فريق الدعم",
                    Icon = "fas fa-ticket-alt",
                    Action = "create_ticket"
                }
            };
        }

        private List<Quote> GetMockQuotes()
        {
            return new List<Quote>
            {
                new Quote
                {
                    Id = "QT-2024-015",
                    Title = "نظام إدارة المخزون",
                    Date = "2024-12-20",
                    Amount = 45000,
                    Status = "قيد المراجعة",
                    StatusClass = "pending",
                    ValidUntil = "2025-01-20",
                    Description = "نظام متكامل لإدارة المخزون والمستودعات"
                }
            };
        }

        private List<Invoice> GetMockInvoices()
        {
            return new List<Invoice>
            {
                new Invoice
                {
                    Id = "INV-2024-089",
                    Title = "فاتورة نظام إدارة المخزون",
                    Date = "2024-12-20",
                    DueDate = "2025-01-20",
                    Amount = 45000,
                    PaidAmount = 45000,
                    Status = "مدفوعة",
                    StatusClass = "paid",
                    Description = "فاتورة تنفيذ وتركيب نظام إدارة المخزون"
                }
            };
        }

        private List<SupportTicket> GetMockSupportTickets()
        {
            return new List<SupportTicket>
            {
                new SupportTicket
                {
                    Id = "TK-2024-156",
                    Title = "مشكلة في تسجيل الدخول",
                    Description = "لا أستطيع تسجيل الدخول إلى النظام منذ أمس",
                    Priority = "عالية",
                    PriorityClass = "high",
                    Status = "مفتوحة",
                    StatusClass = "open",
                    Category = "مشكلة تقنية",
                    CreatedDate = "2024-12-25",
                    LastUpdate = "2024-12-26",
                    AssignedTo = "أحمد محمد - الدعم الفني"
                }
            };
        }
    }
}
