# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

# cache
/.angular

# dependencies
/node_modules

# profiling files
chrome-profiler-events*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
/.vs/ClientApp/FileContentIndex/188f9d19-6419-413c-9f17-d29c2a5fcfd1.vsidx
/.vs/ClientApp/FileContentIndex/bdf93ca1-16c9-4cda-9c98-acc7f131ca37.vsidx
/.vs/ClientApp/FileContentIndex/e9988256-7d5f-41bf-bdc4-11e4d61d26eb.vsidx
/.vs/ClientApp/FileContentIndex/21a459f0-680e-4189-a56b-440d6741f510.vsidx
