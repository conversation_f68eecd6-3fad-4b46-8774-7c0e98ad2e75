// <auto-generated />
using System;
using FalconFramework.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FalconFramework.Infrastructure.Migrations
{
    [DbContext(typeof(FalconDbContext))]
    [Migration("20241221120000_AddInventoryAdjustmentsFields")]
    partial class AddInventoryAdjustmentsFields
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true)
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            // Model configuration will be inherited from the previous migration
            // This is a simplified designer file for the AddInventoryAdjustmentsFields migration
#pragma warning restore 612, 618
        }
    }
}
