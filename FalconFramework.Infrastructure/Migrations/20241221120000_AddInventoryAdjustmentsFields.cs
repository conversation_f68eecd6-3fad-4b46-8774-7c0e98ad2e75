using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FalconFramework.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddInventoryAdjustmentsFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "year",
                table: "Stores_Scan",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "notes",
                table: "Stores_Scan",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ActionDate",
                table: "Stores_Scan",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserName",
                table: "Stores_Scan",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "Journal",
                table: "Stores_Scan",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "MovementTypeId",
                table: "Stores_Scan",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConsumerID",
                table: "Stores_Scan",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "year",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "notes",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "ActionDate",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "UserName",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "Journal",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "MovementTypeId",
                table: "Stores_Scan");

            migrationBuilder.DropColumn(
                name: "ConsumerID",
                table: "Stores_Scan");
        }
    }
}
