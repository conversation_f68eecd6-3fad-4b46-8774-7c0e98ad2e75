using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FalconFramework.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class inventory250514 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
          

            migrationBuilder.DropColumn(
                name: "VariantNameId",
                table: "Talab_Sarf_Items");

            migrationBuilder.DropColumn(
                name: "VariantValueId",
                table: "Talab_Sarf_Items");

           

         

           
            migrationBuilder.AddColumn<decimal>(
                name: "ReturnQuantity",
                table: "Talab_Sarf_Items",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VariantName",
                table: "Talab_Sarf_Items",
                type: "nvarchar(max)",
                nullable: true);

         

          

           

            // تم تعليق إنشاء فهارس وقيود المفاتيح الأجنبية مؤقتًا لتجنب مشاكل البيانات الموجودة
            // migrationBuilder.CreateIndex(
            //     name: "IX_Items_Item_unit2",
            //     table: "Items",
            //     column: "Item_unit2");

            // migrationBuilder.CreateIndex(
            //     name: "IX_Items_Item_unit3",
            //     table: "Items",
            //     column: "Item_unit3");

            // migrationBuilder.AddForeignKey(
            //     name: "FK_Items_unit_Item_unit2",
            //     table: "Items",
            //     column: "Item_unit2",
            //     principalTable: "unit",
            //     principalColumn: "unitcode",
            //     onDelete: ReferentialAction.Restrict);

            // migrationBuilder.AddForeignKey(
            //     name: "FK_Items_unit_Item_unit3",
            //     table: "Items",
            //     column: "Item_unit3",
            //     principalTable: "unit",
            //     principalColumn: "unitcode",
            //     onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Items_unit_Item_unit2",
                table: "Items");

            migrationBuilder.DropForeignKey(
                name: "FK_Items_unit_Item_unit3",
                table: "Items");

            migrationBuilder.DropIndex(
                name: "IX_Items_Item_unit2",
                table: "Items");

            migrationBuilder.DropIndex(
                name: "IX_Items_Item_unit3",
                table: "Items");

            

        

            migrationBuilder.DropColumn(
                name: "ReturnQuantity",
                table: "Talab_Sarf_Items");

            migrationBuilder.DropColumn(
                name: "VariantName",
                table: "Talab_Sarf_Items");

           

           
             
            migrationBuilder.AddColumn<int>(
                name: "VariantNameId",
                table: "Talab_Sarf_Items",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "VariantValueId",
                table: "Talab_Sarf_Items",
                type: "int",
                nullable: true);

            
 
          
        }
    }
}
