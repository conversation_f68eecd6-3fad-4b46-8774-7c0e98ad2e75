using FalconFramework.ApplicationCore.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using static FalconFramework.AppModules;
using System.Drawing;
using FalconFramework.re.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.Infrastructure.Services;
using System.Reflection.Emit;

namespace FalconFramework.Infrastructure.Data
{
    public class FalconDbContext : IdentityDbContext<ApplicationUser, ApplicationRole,
        string, IdentityUserClaim<string>, ApplicationUserRole,
        IdentityUserLogin<string>, IdentityRoleClaim<string>, IdentityUserToken<string>>
    {
        public FalconDbContext(DbContextOptions options) : base(options)
        {

        }
        public virtual DbSet<ManufacturingQulityChecks> ManufacturingQulityChecks { get; set; }
        public virtual DbSet<InventoryQulityChecks> InventoryQulityChecks { get; set; }
        public virtual DbSet<ManufacturingQulityControl> ManufacturingQulityControl { get; set; }
        public virtual DbSet<InventoryQulityControl> InventoryQulityControl { get; set; }
        public virtual DbSet<SMSTemplate> SMSTemplate { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplate { get; set; }
        public virtual DbSet<SalesPaymentTerms> SalesPaymentTerms { get; set; }
        public virtual DbSet<AAA_Last_Update> AAA_Last_Update { get; set; }
        public virtual DbSet<Stages> Stages { get; set; }
        public virtual DbSet<Acc_Type_tree> Acc_Type_tree { get; set; }
        public virtual DbSet<AccMezanya> AccMezanya { get; set; }
        public virtual DbSet<Account_Group> Account_Group { get; set; }
        public virtual DbSet<Account_Link> Account_Link { get; set; }
        public virtual DbSet<Account_Lokd> Account_Lokd { get; set; }
        public virtual DbSet<Account_Third_Party> Account_Third_Party { get; set; }
        public virtual DbSet<AccountBudget> AccountBudget { get; set; }
        public virtual DbSet<FalconAccounts> FalconAccounts { get; set; }
        public virtual DbSet<Accounts_Code_Day> Accounts_Code_Day { get; set; }
        public virtual DbSet<Accounts_Code_MR> Accounts_Code_MR { get; set; }
        public virtual DbSet<AdminDecision> AdminDecision { get; set; }
        public virtual DbSet<Agents> Agents { get; set; }
        public virtual DbSet<Air_Lines> Air_Lines { get; set; }
        public virtual DbSet<AkdCode> AkdCode { get; set; }
        public virtual DbSet<AmnDawla> AmnDawla { get; set; }
        public virtual DbSet<Amr_Sheraa> Amr_Sheraa { get; set; }
        public virtual DbSet<Amr_Sheraa_Item> Amr_Sheraa_Item { get; set; }
        public virtual DbSet<Amr_Shieraa_Remarks> Amr_Shieraa_Remarks { get; set; }
        public virtual DbSet<Amrshogl_items_Cost> Amrshogl_items_Cost { get; set; }
        public virtual DbSet<AngleQuantity> AngleQuantity { get; set; }
        public virtual DbSet<anglerased> anglerased { get; set; }
        public virtual DbSet<Angles> Angles { get; set; }
        public virtual DbSet<AppAlert> AppAlert { get; set; }
        public virtual DbSet<Appointment> Appointment { get; set; }
        public virtual DbSet<Appointmet_Seurity> Appointmet_Seurity { get; set; }
        public virtual DbSet<aqsat> aqsat { get; set; }
        public virtual DbSet<Archive> Archive { get; set; }
        public virtual DbSet<ArchiveTypes> ArchiveTypes { get; set; }
        public virtual DbSet<ArchiveTypes_Sub> ArchiveTypes_Sub { get; set; }
        public virtual DbSet<ArdAlaLagnaTbya> ArdAlaLagnaTbya { get; set; }
        public virtual DbSet<Arrival_Report> Arrival_Report { get; set; }
        public virtual DbSet<AslGroups> AslGroups { get; set; }
        public virtual DbSet<AslName> AslName { get; set; }
        public virtual DbSet<AslNameAdd> AslNameAdd { get; set; }
        public virtual DbSet<AslSubGroup> AslSubGroup { get; set; }
        public virtual DbSet<AslType> AslType { get; set; }
        public virtual DbSet<AssemplyFrom> AssemplyFrom { get; set; }
        public virtual DbSet<AssemplyTo> AssemplyTo { get; set; }
        public virtual DbSet<Asset_Of_Branch> Asset_Of_Branch { get; set; }
        public virtual DbSet<AssimplyPatch> AssimplyPatch { get; set; }
        public virtual DbSet<averag> averag { get; set; }
        public virtual DbSet<AVGtbl> AVGtbl { get; set; }
        public virtual DbSet<BalanceSheet> BalanceSheet { get; set; }
        public virtual DbSet<Bank_Accounts> Bank_Accounts { get; set; }
        public virtual DbSet<Bank_Code> Bank_Code { get; set; }
        public virtual DbSet<BanK_Convert> BanK_Convert { get; set; }
        public virtual DbSet<BankAccountName> BankAccountName { get; set; }
        public virtual DbSet<BankNetwork> BankNetwork { get; set; }
        public virtual DbSet<BankNetWorkSecurity> BankNetWorkSecurity { get; set; }
        public virtual DbSet<BankSecurity> BankSecurity { get; set; }
        public virtual DbSet<berchasebetweendate> berchasebetweendate { get; set; }
        public virtual DbSet<BerchisingType> BerchisingType { get; set; }
        public virtual DbSet<BlanceSheetTable> BlanceSheetTable { get; set; }
        public virtual DbSet<book_check> book_check { get; set; }
        public virtual DbSet<Box> Box { get; set; }
        public virtual DbSet<Box_Balanes> Box_Balanes { get; set; }
        public virtual DbSet<Box_Convert> Box_Convert { get; set; }
        public virtual DbSet<Box_Moves> Box_Moves { get; set; }
        public virtual DbSet<BoxSecurity> BoxSecurity { get; set; }
        public virtual DbSet<Branch> Branch { get; set; }
        public virtual DbSet<BranchSecurity> BranchSecurity { get; set; }
        public virtual DbSet<Byan> byan { get; set; }
        public virtual DbSet<byan_Discounts> byan_Discounts { get; set; }
        public virtual DbSet<byan_ezn> byan_ezn { get; set; }
        public virtual DbSet<byan_item> byan_item { get; set; }
        public virtual DbSet<byan_item_ezn> byan_item_ezn { get; set; }
        public virtual DbSet<byan_item_Temb> byan_item_Temb { get; set; }
        public virtual DbSet<byan_temb> byan_temb { get; set; }
        public virtual DbSet<cars> cars { get; set; }
        public virtual DbSet<CarsContractPaid> CarsContractPaid { get; set; }
        public virtual DbSet<carsTable> carsTable { get; set; }
        public virtual DbSet<Cartraffic> Cartraffic { get; set; }
        public virtual DbSet<CarTransfeer> CarTransfeer { get; set; }
        public virtual DbSet<cartype> cartype { get; set; }
        public virtual DbSet<Cashflow> Cashflow { get; set; }
        public virtual DbSet<CashierMovement> CashierMovement { get; set; }
        public virtual DbSet<CHarg_CO> CHarg_CO { get; set; }
        public virtual DbSet<CHarg_Contact> CHarg_Contact { get; set; }
        public virtual DbSet<Charge_Items> Charge_Items { get; set; }
        public virtual DbSet<chartaccount> chartaccount { get; set; }
        public virtual DbSet<Check_In> Check_In { get; set; }
        public virtual DbSet<CheckIN> CheckIN { get; set; }
        public virtual DbSet<Chek_Book> Chek_Book { get; set; }
        public virtual DbSet<chek_book_come> chek_book_come { get; set; }
        public virtual DbSet<chek_book_Out> chek_book_Out { get; set; }
        public virtual DbSet<City> City { get; set; }
        public virtual DbSet<Clinc> Clinc { get; set; }
        public virtual DbSet<collateral> collateral { get; set; }
        public virtual DbSet<CollectMoney> CollectMoney { get; set; }
        public virtual DbSet<CollectMoney_Items> CollectMoney_Items { get; set; }
        public virtual DbSet<CollectmonyMenu_tbl> CollectmonyMenu_tbl { get; set; }
        public virtual DbSet<Colors> Colors { get; set; }
        public virtual DbSet<commandorder> commandorder { get; set; }
        public virtual DbSet<Company> Company { get; set; }
        public virtual DbSet<COMPANY_INFO> COMPANY_INFO { get; set; }
        public virtual DbSet<Companies> Companies { get; set; }
        public virtual DbSet<CompanyCategories> CompanyCategories { get; set; }
        public virtual DbSet<contactpaynet> contactpaynet { get; set; }
        public virtual DbSet<contract> contract { get; set; }
        public virtual DbSet<Contract_Fallow> Contract_Fallow { get; set; }
        public virtual DbSet<Contract_Home_Allaw> Contract_Home_Allaw { get; set; }
        public virtual DbSet<Contract_invoices> Contract_invoices { get; set; }
        public virtual DbSet<ContractDetails> ContractDetails { get; set; }
        public virtual DbSet<ContractHead> ContractHead { get; set; }
        public virtual DbSet<ContractHeader> ContractHeader { get; set; }
        public virtual DbSet<ContractLines> ContractLines { get; set; }
        public virtual DbSet<ContractType> ContractType { get; set; }
        public virtual DbSet<Control_of_Item> Control_of_Item { get; set; }
        public virtual DbSet<conttype> conttype { get; set; }
        public virtual DbSet<CoolCode> CoolCode { get; set; }
        public virtual DbSet<coplaces> coplaces { get; set; }
        public virtual DbSet<Cost_Sup_Group> Cost_Sup_Group { get; set; }
        public virtual DbSet<CostAvreg> CostAvreg { get; set; }
        public virtual DbSet<CostGroups> CostGroups { get; set; }

        public virtual DbSet<CostItemsBudget> CostItemsBudget { get; set; }
        public virtual DbSet<CostItemsBudget_History> CostItemsBudget_History { get; set; }
        public virtual DbSet<CostProjects> CostProjects { get; set; }
        public virtual DbSet<CostSteeps> CostSteeps { get; set; }
        public virtual DbSet<CostTypeName> CostTypeName { get; set; }
        public virtual DbSet<CountDayHour> CountDayHour { get; set; }
        public virtual DbSet<Country> Country { get; set; }
        public virtual DbSet<Curaunes> Curaunes { get; set; }
        public virtual DbSet<Cust_Extract> Cust_Extract { get; set; }
        public virtual DbSet<Cust_Extract_Final> Cust_Extract_Final { get; set; }
        public virtual DbSet<Cust_Extract_Items> Cust_Extract_Items { get; set; }
        public virtual DbSet<Cust_Follw> Cust_Follw { get; set; }
        public virtual DbSet<Cust_Item_Report> Cust_Item_Report { get; set; }
        public virtual DbSet<ClinicCustomer> ClinicCustomer { get; set; }
        public virtual DbSet<customer_account> customer_account { get; set; }
        public virtual DbSet<customer_compaint> customer_compaint { get; set; }
        public virtual DbSet<customer_contact> customer_contact { get; set; }
        public virtual DbSet<customer_emp> customer_emp { get; set; }
        public virtual DbSet<Customer_Files> Customer_Files { get; set; }
        public virtual DbSet<Customer_Mandop> Customer_Mandop { get; set; }
        public virtual DbSet<customer_survey> customer_survey { get; set; }
        public virtual DbSet<Customer_Wared> Customer_Wared { get; set; }
        public virtual DbSet<CustomerAcdItems> CustomerAcdItems { get; set; }
        public virtual DbSet<CustomerAcdVisits> CustomerAcdItems_items { get; set; }
        public virtual DbSet<CustomerAcd> CustomerAcd { get; set; }
        public virtual DbSet<CustomerAndSuplierShortAccount> CustomerAndSuplierShortAccount { get; set; }
        public virtual DbSet<CustomerCall> CustomerCall { get; set; }
        public virtual DbSet<CustomerFallow> CustomerFallow { get; set; }
        public virtual DbSet<CustomerFallow_Notes> CustomerFallow_Notes { get; set; }
        public virtual DbSet<Customers> customers { get; set; }
        public virtual DbSet<customers_requirement> customers_requirement { get; set; }
        public virtual DbSet<customers_requirement_items> customers_requirement_items { get; set; }
        public virtual DbSet<customers_types> customers_types { get; set; }
        public virtual DbSet<cut_code> cut_code { get; set; }
        public virtual DbSet<Cutting> cutting { get; set; }
        public virtual DbSet<cv> cv { get; set; }
        public virtual DbSet<cvhead> cvhead { get; set; }
        public virtual DbSet<DailySaleRep> DailySaleRep { get; set; }
        public virtual DbSet<DashBoardPermison> DashBoardPermison { get; set; }
        public virtual DbSet<data> data { get; set; }
        public virtual DbSet<dawry> dawry { get; set; }
        public virtual DbSet<DaylyReport> DaylyReport { get; set; }
        public virtual DbSet<deliver_product> deliver_product { get; set; }
        public virtual DbSet<Delivery_bill> Delivery_bill { get; set; }
        public virtual DbSet<delivery_Note_Quantity> delivery_Note_Quantity { get; set; }
        public virtual DbSet<DeliveryNotes> DeliveryNotes { get; set; }
        public virtual DbSet<departments> departments { get; set; }
        public virtual DbSet<Dependents> Dependents { get; set; }
        public virtual DbSet<Descount_Type> Descount_Type { get; set; }
        public virtual DbSet<DeviceCode> DeviceCode { get; set; }
        public virtual DbSet<DeviceStops> DeviceStops { get; set; }
        public virtual DbSet<devtest> devtest { get; set; }
        public virtual DbSet<DIC> DIC { get; set; }
        public virtual DbSet<doctor> doctor { get; set; }
        public virtual DbSet<Documentary> Documentary { get; set; }
        public virtual DbSet<Documentary_ChangeValue> Documentary_ChangeValue { get; set; }
        public virtual DbSet<Documentary_of_Extend> Documentary_of_Extend { get; set; }
        public virtual DbSet<Documentary_of_Guarantee> Documentary_of_Guarantee { get; set; }
        public virtual DbSet<Documentary_of_Guarantee_Used> Documentary_of_Guarantee_Used { get; set; }
        public virtual DbSet<Drawing_Log> Drawing_Log { get; set; }
        public virtual DbSet<DrwaingType> DrwaingType { get; set; }
        public virtual DbSet<Due_Vection> Due_Vection { get; set; }
        public virtual DbSet<Due_Vection_Items> Due_Vection_Items { get; set; }
        public virtual DbSet<Religion> Religion { get; set; }
        public virtual DbSet<Egarat> Egarat { get; set; }
        public virtual DbSet<Egarat_items> Egarat_items { get; set; }

        public virtual DbSet<EkhlaaTaraf> EkhlaaTaraf { get; set; }
        public virtual DbSet<EkhlaaTarafNhaay> EkhlaaTarafNhaay { get; set; }
        public virtual DbSet<EkhlatTarafMoar> EkhlatTarafMoar { get; set; }
        public virtual DbSet<El_Koyod_El_Yawmia> El_Koyod_El_Yawmia { get; set; }
        public virtual DbSet<El_Koyod_El_Yawmia_Request> El_Koyod_El_Yawmia_Request { get; set; }
        public virtual DbSet<El_Koyod_Master> El_Koyod_Master { get; set; }
        public virtual DbSet<El_Koyod_TEMP> El_Koyod_TEMP { get; set; }
        public virtual DbSet<elawat_code> elawat_code { get; set; }
        public virtual DbSet<elmrahel> elmrahel { get; set; }
        public virtual DbSet<EmailSetting> EmailSetting { get; set; }
        public virtual DbSet<Employee> Employee { get; set; }
        public virtual DbSet<Emp_Account> Emp_Account { get; set; }
        public virtual DbSet<Emp_Comming> Emp_Comming { get; set; }
        public virtual DbSet<emp_edafy> emp_edafy { get; set; }
        public virtual DbSet<emp_ezn> emp_ezn { get; set; }
        public virtual DbSet<emp_ghyab_bezn> emp_ghyab_bezn { get; set; }
        public virtual DbSet<Emp_History> Emp_History { get; set; }
        public virtual DbSet<Emp_Iqama_Costs> Emp_Iqama_Costs { get; set; }
        public virtual DbSet<Emp_Iqama_Costs_Journal> Emp_Iqama_Costs_Journal { get; set; }
        public virtual DbSet<emp_mamoryat> emp_mamoryat { get; set; }
        public virtual DbSet<Emp_memo> Emp_memo { get; set; }
        public virtual DbSet<Emp_memo_Items> Emp_memo_Items { get; set; }
        public virtual DbSet<Emp_Mosta7kat> Emp_Mosta7kat { get; set; }
        public virtual DbSet<Emp_Requests> Emp_Requests { get; set; }
        public virtual DbSet<Emp_Sallary_History> Emp_Sallary_History { get; set; }
        public virtual DbSet<Emp_Vacation> Emp_Vacation { get; set; }
        public virtual DbSet<Emp_Vacation_Flow> Emp_Vacation_Flow { get; set; }
        public virtual DbSet<EmpCosts> EmpCosts { get; set; }
        public virtual DbSet<EmpForm_Extradition> EmpForm_Extradition { get; set; }
        public virtual DbSet<EmpSallery> EmpSallery { get; set; }
        public virtual DbSet<EmpStatusHistory> EmpStatusHistory { get; set; }
        public virtual DbSet<EmpTamen> EmpTamen { get; set; }
        public virtual DbSet<EnzarMozaf> EnzarMozaf { get; set; }
        public virtual DbSet<EsnSarf> EsnSarf { get; set; }
        public virtual DbSet<EsnSarf_items> EsnSarf_items { get; set; }
        public virtual DbSet<EsnSarfItemOhda> EsnSarfItemOhda { get; set; }
        public virtual DbSet<EsnSarfOhda> EsnSarfOhda { get; set; }
        public virtual DbSet<Estekala> Estekala { get; set; }
        public virtual DbSet<Evaluation> Evaluation { get; set; }
        public virtual DbSet<Evaluation_Messge> Evaluation_Messge { get; set; }
        public virtual DbSet<EvaluationItem> EvaluationItems { get; set; }
        public virtual DbSet<EvaluationMonthly> EvaluationMonthly { get; set; }
        public virtual DbSet<ExchangeMaterials> ExchangeMaterials { get; set; }
        public virtual DbSet<ExchangeMaterials_Item> ExchangeMaterials_Item { get; set; }
        public virtual DbSet<Experince> Experince { get; set; }
        public virtual DbSet<Explains> Explain { get; set; }
        public virtual DbSet<Extracts_Deductions> Extracts_Deductions { get; set; }
        public virtual DbSet<Eznbyan> Eznbyan { get; set; }
        public virtual DbSet<Eznbyan_item> Eznbyan_item { get; set; }
        public virtual DbSet<EznEdafa> EznEdafa { get; set; }
        public virtual DbSet<EznEdafa_item> EznEdafa_item { get; set; }

        public virtual DbSet<EznType> EznType { get; set; }
        public virtual DbSet<Falcon_Mails_From> Falcon_Mails_From { get; set; }

        public virtual DbSet<Falcon_Mails_To> Falcon_Mails_To { get; set; }

        public virtual DbSet<Falcon_Migration> Falcon_Migration { get; set; }
        public virtual DbSet<Falcon_Sitting> Falcon_Sitting { get; set; }
        public virtual DbSet<FalconSubGroups> FalconSubGroups { get; set; }

        public virtual DbSet<Fallow_Lon_Items> Fallow_Lon_Items { get; set; }
        public virtual DbSet<favoriteform> favoriteform { get; set; }
        public virtual DbSet<Files> Files { get; set; }
        public virtual DbSet<File_Path> File_Path { get; set; }
        public virtual DbSet<FileAttached> FileAttached { get; set; }
        public virtual DbSet<Files_Arch> Files_Arch { get; set; }
        public virtual DbSet<Files_Shraa> Files_Shraa { get; set; }
        public virtual DbSet<Filing> Filing { get; set; }
        public virtual DbSet<FiltersTable> FiltersTable { get; set; }
        public virtual DbSet<Final_Ratio_Setting> Final_Ratio_Setting { get; set; }
        public virtual DbSet<FinalBalanceForms> FinalBalanceForms { get; set; }
        public virtual DbSet<FinalbalanceGroup> FinalbalanceGroup { get; set; }
        public virtual DbSet<FinalBalanceTypes> FinalBalanceTypes { get; set; }
        public virtual DbSet<FinalMenuSittings> FinalMenuSittings { get; set; }
        public virtual DbSet<FinalTypes> FinalTypes { get; set; }
        public virtual DbSet<FinancialYear> FinancialYear { get; set; }
        public virtual DbSet<Fixed_Aset_Calc_Add> Fixed_Aset_Calc_Add { get; set; }
        public virtual DbSet<FoodCustom> FoodCustom { get; set; }
        public virtual DbSet<Form_Accountability> Form_Accountability { get; set; }
        public virtual DbSet<Form_Business_Items> Form_Business_Items { get; set; }
        public virtual DbSet<Form_BusinessTravel> Form_BusinessTravel { get; set; }
        public virtual DbSet<Form_Commission> Form_Commission { get; set; }
        public virtual DbSet<Form_Emp_Contract> Form_Emp_Contract { get; set; }
        public virtual DbSet<Form_LoanContract> Form_LoanContract { get; set; }
        public virtual DbSet<Form_NoticeAppointment> Form_NoticeAppointment { get; set; }
        public virtual DbSet<Form_NoticeAppointment_Items> Form_NoticeAppointment_Items { get; set; }
        public virtual DbSet<Form_Receipt> Form_Receipt { get; set; }
        public virtual DbSet<Form_Request> Form_Request { get; set; }
        public virtual DbSet<FormDrow_Attention> FormDrow_Attention { get; set; }
        public virtual DbSet<FristStok> FristStok { get; set; }
        public virtual DbSet<FristStok_items> FristStok_items { get; set; }
        public virtual DbSet<fsolname> fsolname { get; set; }
        public virtual DbSet<GARD> GARD { get; set; }
        public virtual DbSet<Grantee> Grantee { get; set; }
        public virtual DbSet<Groups> Groups { get; set; }
        public virtual DbSet<Groups_Sub> Groups_Sub { get; set; }
        public virtual DbSet<GroupSubSecurity> GroupSubSecurity { get; set; }
        public virtual DbSet<Guard_Item> Guard_Item { get; set; }
        public virtual DbSet<GYM_Comming> GYM_Comming { get; set; }
        public virtual DbSet<GYM_Contracts> GYM_Contracts { get; set; }
        public virtual DbSet<GYM_LOG> GYM_LOG { get; set; }
        public virtual DbSet<GYM_Plans> GYM_Plans { get; set; }
        public virtual DbSet<Hafez> Hafez { get; set; }
        public virtual DbSet<HelberTable> HelberTable { get; set; }
        public virtual DbSet<Hotel_INV> Hotel_INV { get; set; }
        public virtual DbSet<Housekeeping> Housekeeping { get; set; }
        public virtual DbSet<Images> Images { get; set; }
        public virtual DbSet<improvedsection> improvedsection { get; set; }

        public virtual DbSet<Indirect_Cost> Indirect_Cost { get; set; }
        public virtual DbSet<Indirect_Cost_Amrshogl> Indirect_Cost_Amrshogl { get; set; }
        public virtual DbSet<Indirect_Cost_Items> Indirect_Cost_Items { get; set; }
        public virtual DbSet<Indirect_Cost_Items_Amrshogl> Indirect_Cost_Items_Amrshogl { get; set; }
        public virtual DbSet<Indirect_Cost_Items_Setting> Indirect_Cost_Items_Setting { get; set; }
        public virtual DbSet<Insurance> Insurance { get; set; }
        public virtual DbSet<Inv_CollectHistory> Inv_CollectHistory { get; set; }
        public virtual DbSet<Inv_ComNnotax_File> Inv_ComNnotax_File { get; set; }
        public virtual DbSet<INV_Ponas> INV_Ponas { get; set; }
        public virtual DbSet<invcome> invcome { get; set; }
        public virtual DbSet<invcome_ezn> invcome_ezn { get; set; }
        public virtual DbSet<invcome_temb> invcome_temb { get; set; }
        public virtual DbSet<invcomeitem> invcomeitem { get; set; }
        public virtual DbSet<invcomeitem_ezn> invcomeitem_ezn { get; set; }
        public virtual DbSet<invcomeitem_temb> invcomeitem_temb { get; set; }
        public virtual DbSet<invcomeitemNoTax> invcomeitemNoTax { get; set; }
        public virtual DbSet<invcomeitemNoTax_ezn> invcomeitemNoTax_ezn { get; set; }
        public virtual DbSet<invcomeitemNoTaxTemb> invcomeitemNoTaxTemb { get; set; }
        public virtual DbSet<invcomeNoTax> invcomeNoTax { get; set; }
        public virtual DbSet<invcomeNoTax_ezn> invcomeNoTax_ezn { get; set; }
        public virtual DbSet<invcomeNoTaxTemb> invcomeNoTaxTemb { get; set; }
        public virtual DbSet<InvComNoTax_Ezn_File> InvComNoTax_Ezn_File { get; set; }
        public virtual DbSet<inventory> inventory { get; set; }
        public virtual DbSet<Invoice_Discounts> Invoice_Discounts { get; set; }
        public virtual DbSet<InvoiceCollect> InvoiceCollect { get; set; }
        public virtual DbSet<InvoiceCome_Cost> InvoiceCome_Cost { get; set; }
        public virtual DbSet<InvoiceComeNotTax_Cost> InvoiceComeNotTax_Cost { get; set; }
        public virtual DbSet<Invoiceout> invoiceout { get; set; }
        public virtual DbSet<invoiceout_ezn> invoiceout_ezn { get; set; }
        public virtual DbSet<invoiceout_Guardians> invoiceout_Guardians { get; set; }
        public virtual DbSet<invoiceout_temb> invoiceout_temb { get; set; }
        public virtual DbSet<invoiceoutitem> invoiceoutitem { get; set; }
        public virtual DbSet<invoiceoutitem_ezn> invoiceoutitem_ezn { get; set; }
        public virtual DbSet<invoiceoutitem_Guardians> invoiceoutitem_Guardians { get; set; }
        public virtual DbSet<invoiceoutitem_temb> invoiceoutitem_temb { get; set; }
        public virtual DbSet<InvoiceTransport> InvoiceTransport { get; set; }
        public virtual DbSet<InvoiceTransportItem> InvoiceTransportItem { get; set; }
        public virtual DbSet<isalstudent> isalstudent { get; set; }
        public virtual DbSet<IsalTawredBox> IsalTawredBox { get; set; }
        public virtual DbSet<Item_Card> Item_Card { get; set; }
        public virtual DbSet<Item_Card_Journal> Item_Card_Journal { get; set; }
        public virtual DbSet<Item_Quantity_Price> Item_Quantity_Price { get; set; }
        public virtual DbSet<Item_store_Cost> Item_store_Cost { get; set; }
        public virtual DbSet<Item_Store_Prices> Item_Store_Prices { get; set; }
        public virtual DbSet<Item_Stores> Item_Stores { get; set; }
        public virtual DbSet<Item_Category> ItemCategory { get; set; }
        public virtual DbSet<CustomersSamples> itemclint { get; set; }
        public virtual DbSet<ItemContent> ItemContent { get; set; }
        public virtual DbSet<ItemOfferInPeriod> ItemOfferInPeriod { get; set; }
        public virtual DbSet<ItemOfferInPeriod_items> ItemOfferInPeriod_items { get; set; }
        public virtual DbSet<Product> Product { get; set; }
        public virtual DbSet<Items_Barcode> Items_Barcode { get; set; }
        public virtual DbSet<items_client> items_client { get; set; }
        public virtual DbSet<SalesTeams> SalesTeams { get; set; }
        public virtual DbSet<Items_Ponas> Items_Ponas { get; set; }
        public virtual DbSet<ItemSerialNumbers> ItemSerialNumbers { get; set; }
        public virtual DbSet<ItemSide1> ItemSide1 { get; set; }
        public virtual DbSet<ItemSide2> ItemSide2 { get; set; }

        public virtual DbSet<ItemVariants> ItemVariants { get; set; }
        public virtual DbSet<ItemVariantType> ItemVariantType { get; set; }
        public virtual DbSet<ItemVariantValues> ItemVariantValues { get; set; }
        public virtual DbSet<ItemVendor> ItemVendor { get; set; }
        public virtual DbSet<ItemVendor_Temp> ItemVendor_Temp { get; set; }
        public virtual DbSet<Job_Order_Service_Request> Job_Order_Service_Request { get; set; }
        public virtual DbSet<Job_Order_Services_Item> Job_Order_Services_Item { get; set; }
        public virtual DbSet<Job_Orders> Job_Orders { get; set; }
        public virtual DbSet<jobs> jobs { get; set; }
        public virtual DbSet<Jornal> Jornal { get; set; }
        public virtual DbSet<Kafeel> Kafeel { get; set; }
        public virtual DbSet<keadSitting> keadSitting { get; set; }
        public virtual DbSet<kind_check> kind_check { get; set; }
        public virtual DbSet<kinship> kinship { get; set; }
        public virtual DbSet<lastmovedate> lastmovedate { get; set; }
        public virtual DbSet<Letter_ChangeValue> Letter_ChangeValue { get; set; }
        public virtual DbSet<Letter_of_Extend> Letter_of_Extend { get; set; }
        public virtual DbSet<Letter_of_Guarantee> Letter_of_Guarantee { get; set; }
        public virtual DbSet<Letter_of_Guarantee_Used> Letter_of_Guarantee_Used { get; set; }
        public virtual DbSet<level_room> level_room { get; set; }
        public virtual DbSet<Load_Report> Load_Report { get; set; }
        public virtual DbSet<Loan_Extend> Loan_Extend { get; set; }
        public virtual DbSet<Loan_Limit> Loan_Limit { get; set; }
        public virtual DbSet<Loan_materials_Result> Loan_materials_Result { get; set; }
        public virtual DbSet<Loan_Payments> Loan_Payments { get; set; }
        public virtual DbSet<Loan_Used> Loan_Used { get; set; }
        public virtual DbSet<LoanType> LoanType { get; set; }
        public virtual DbSet<MachineAndToolInvoice> MachineAndToolInvoice { get; set; }
        public virtual DbSet<MachineAndToolInvoiceHistory> MachineAndToolInvoiceHistory { get; set; }
        public virtual DbSet<machinelist> machinelist { get; set; }
        public virtual DbSet<MachineMove> MachineMove { get; set; }
        public virtual DbSet<MachineMoveExtraCost> MachineMoveExtraCost { get; set; }
        public virtual DbSet<MadeCompany> MadeCompany { get; set; }
        public virtual DbSet<MailGroup> MailGroup { get; set; }
        public virtual DbSet<Main_Properties_list> Main_Properties_list { get; set; }
        public virtual DbSet<MainCustomer> MainCustomer { get; set; }
        public virtual DbSet<MaintenanceType> MaintenanceType { get; set; }
        public virtual DbSet<ManagerRep> ManagerRep { get; set; }
        public virtual DbSet<Managment> Managment { get; set; }
        public virtual DbSet<Mandop_Moda_Omola> Mandop_Moda_Omola { get; set; }
        public virtual DbSet<MandopOmola> MandopOmola { get; set; }
        public virtual DbSet<MandopOmola_Total> MandopOmola_Total { get; set; }
        public virtual DbSet<Manufactur_Plan> Manufactur_Plan { get; set; }
        public virtual DbSet<Manufactur_Plan_TEMP> Manufactur_Plan_TEMP { get; set; }
        public virtual DbSet<Mardodat_Byan> Mardodat_Byan { get; set; }
        public virtual DbSet<Mardodat_Byan_items> Mardodat_Byan_items { get; set; }
        public virtual DbSet<Mardodat_Invcome> Mardodat_Invcome { get; set; }
        public virtual DbSet<Mardodat_InvComeItem> Mardodat_InvComeItem { get; set; }
        public virtual DbSet<Mardodat_InviceOut> Mardodat_InviceOut { get; set; }
        public virtual DbSet<Mardodat_Invoice_Com_NotTax> Mardodat_Invoice_Com_NotTax { get; set; }
        public virtual DbSet<Mardodat_Invoice_Com_NotTax_Items> Mardodat_Invoice_Com_NotTax_Items { get; set; }
        public virtual DbSet<Mardodat_InvoiceOutItem> Mardodat_InvoiceOutItem { get; set; }
        public virtual DbSet<masrofatcode> masrofatcode { get; set; }
        public virtual DbSet<mdaily> mdaily { get; set; }
        public virtual DbSet<MenuSystem> MenuSystem { get; set; }
        public virtual DbSet<MenuUser> MenuUser { get; set; }
        public virtual DbSet<Mezanya> Mezanya { get; set; }
        public virtual DbSet<MezanyaReport> MezanyaReport { get; set; }
        public virtual DbSet<MobilAppInfo> MobilAppInfo { get; set; }
        public virtual DbSet<Mokayfat> Mokayfat { get; set; }
        public virtual DbSet<Mokayfat_Items> Mokayfat_Items { get; set; }
        public virtual DbSet<Mokayfat_Tarkeep> Mokayfat_Tarkeep { get; set; }
        public virtual DbSet<Mostakhlas_Head> Mostakhlas_Head { get; set; }
        public virtual DbSet<Mostakhlas_items> Mostakhlas_items { get; set; }
        public virtual DbSet<motab3a> motab3a { get; set; }
        public virtual DbSet<MOVES> MOVES { get; set; }
        public virtual DbSet<mplan> mplan { get; set; }
        public virtual DbSet<MYDOCUMENTSHOME> MYDOCUMENTSHOME { get; set; }
        public virtual DbSet<MYMESSAGES> MYMESSAGES { get; set; }
        public virtual DbSet<name_hospital> name_hospital { get; set; }
        public virtual DbSet<name_roof> name_roof { get; set; }
        public virtual DbSet<Nationality> Nationality { get; set; }
        public virtual DbSet<newemp_takyeem> newemp_takyeem { get; set; }
        public virtual DbSet<Notes> Notes { get; set; }
        public virtual DbSet<Notes_Setting> Notes_Setting { get; set; }
        public virtual DbSet<Notification> Notification { get; set; }
        public virtual DbSet<oldemp_takyeem> oldemp_takyeem { get; set; }
        public virtual DbSet<Operators> Operators { get; set; }
        public virtual DbSet<order_items> order_items { get; set; }
        public virtual DbSet<ordercommanitems> ordercommanitems { get; set; }
        public virtual DbSet<ordermaintainance> ordermaintainance { get; set; }
        public virtual DbSet<orders> orders { get; set; }
        public virtual DbSet<OstazCode> OstazCode { get; set; }
        public virtual DbSet<Partner_Account> Partner_Account { get; set; }
        public virtual DbSet<Partners> Partners { get; set; }
        public virtual DbSet<PayMentMethod_Invoice> PayMentMethod_Invoice { get; set; }
        public virtual DbSet<Payments> Payments { get; set; }
        public virtual DbSet<PaymentTerms> PaymentTerms { get; set; }
        public virtual DbSet<PaymentTerms_Items> PaymentTerms_Items { get; set; }
        public virtual DbSet<performa_invoice> performa_invoice { get; set; }
        public virtual DbSet<performa_items> performa_items { get; set; }

        public virtual DbSet<persons> persons { get; set; }
        public virtual DbSet<PhoneGroups> PhoneGroups { get; set; }
        public virtual DbSet<PhoneNote> PhoneNote { get; set; }
        public virtual DbSet<Plans> Plans { get; set; }
        public virtual DbSet<PlanSecurity> PlanSecurity { get; set; }
        public virtual DbSet<Po_Draft> Po_Draft { get; set; }
        public virtual DbSet<Po_Draft_Items> Po_Draft_Items { get; set; }
        public virtual DbSet<PO_Payments> PO_Payments { get; set; }
        public virtual DbSet<Po_Pyment_Journal> Po_Pyment_Journal { get; set; }
        public virtual DbSet<POQ> POQ { get; set; }
        public virtual DbSet<POQ_Items> POQ_Items { get; set; }
        public virtual DbSet<Ports> Ports { get; set; }
        public virtual DbSet<PosSalleryTable> PosSalleryTable { get; set; }
        public virtual DbSet<Prenter_Setting> Prenter_Setting { get; set; }
        public virtual DbSet<PrepaidExpenses> PrepaidExpenses { get; set; }
        public virtual DbSet<PriceControleHistory> PriceControleHistory { get; set; }
        public virtual DbSet<priceList> priceList { get; set; }
        public virtual DbSet<PriceListType> PriceListType { get; set; }
        public virtual DbSet<ProEdafyEmpHour> ProEdafyEmpHour { get; set; }
        public virtual DbSet<ProEmpHour> ProEmpHour { get; set; }
        public virtual DbSet<project_category> project_category { get; set; }
        public virtual DbSet<Project_ToDo> Project_ToDo { get; set; }
        public virtual DbSet<ProjectNames> ProjectNames { get; set; }
        public virtual DbSet<Projects> Projects { get; set; }
        public virtual DbSet<ProMachineHour> ProMachineHour { get; set; }
        public virtual DbSet<Properties_list> Properties_list { get; set; }
        public virtual DbSet<Properties_List_Favorites> Properties_List_Favorites { get; set; }
        public virtual DbSet<Branches> Prunshes { get; set; }
        public virtual DbSet<purchase_order> purchase_order { get; set; }
        public virtual DbSet<purchase_order_item> purchase_order_item { get; set; }
        public virtual DbSet<Purchase_Share> Purchase_Share { get; set; }
        public virtual DbSet<purchase_takyeem> purchase_takyeem { get; set; }
        public virtual DbSet<PurchasesRep> PurchasesRep { get; set; }
        public virtual DbSet<Qualification> Qualification { get; set; }
        public virtual DbSet<Rate_price> Rate_price { get; set; }
        public virtual DbSet<Rate_Query> Rate_Query { get; set; }
        public virtual DbSet<Rate_Query_items> Rate_Query_items { get; set; }
        public virtual DbSet<rcevedfile> rcevedfile { get; set; }
        public virtual DbSet<Regions> Regions { get; set; }
        public virtual DbSet<RenContractPaid> RenContractPaid { get; set; }
        public virtual DbSet<RentAgain> RentAgain { get; set; }
        public virtual DbSet<RentContract_List> RentContract_List { get; set; }
        public virtual DbSet<RentContract_List_Extension> RentContract_List_Extension { get; set; }
        public virtual DbSet<RentContract_List_Extension_Head> RentContract_List_Extension_Head { get; set; }
        public virtual DbSet<RentContract_List_Payments> RentContract_List_Payments { get; set; }
        public virtual DbSet<Rents> Rents { get; set; }
        public virtual DbSet<RentsItems> RentsItems { get; set; }
        public virtual DbSet<ReportDesign> ReportDesign { get; set; }
        public virtual DbSet<ReportDesign_Adv> ReportDesign_Adv { get; set; }
        public virtual DbSet<ReportDesign_Temp> ReportDesign_Temp { get; set; }
        public virtual DbSet<Request_Currency> Request_Currency { get; set; }
        public virtual DbSet<Request_Currency_Files> Request_Currency_Files { get; set; }
        public virtual DbSet<request_for_quotations> request_for_quotations { get; set; }
        public virtual DbSet<request_for_quotations_item> request_for_quotations_item { get; set; }
        public virtual DbSet<Resource_List> Resource_List { get; set; }
        public virtual DbSet<Resturant_Tables> Resturant_Tables { get; set; }
        public virtual DbSet<Result_dailysales> Result_dailysales { get; set; }
        public virtual DbSet<Result_dailysales_Back> Result_dailysales_Back { get; set; }
        public virtual DbSet<Result_Purchased> Result_Purchased { get; set; }
        public virtual DbSet<Result_Purchased_Back> Result_Purchased_Back { get; set; }
        public virtual DbSet<room> room { get; set; }
        public virtual DbSet<Room_Price> Room_Price { get; set; }
        public virtual DbSet<Rooms> Rooms { get; set; }
        public virtual DbSet<Rptledgersubtotal> Rptledgersubtotal { get; set; }
        public virtual DbSet<RptTrailbalanceAll> RptTrailbalanceAll { get; set; }
        public virtual DbSet<RptTrailbalanceFromTo> RptTrailbalanceFromTo { get; set; }
        public virtual DbSet<RSS> RSS { get; set; }
        public virtual DbSet<runcommand> runcommand { get; set; }
        public virtual DbSet<RunCommandItem> RunCommandItem { get; set; }
        public virtual DbSet<RuningSteep> RuningSteep { get; set; }
        public virtual DbSet<safname> safname { get; set; }
        public virtual DbSet<salary_plass> salary_plass { get; set; }
        public virtual DbSet<SalaryReport> SalaryReport { get; set; }
        public virtual DbSet<SalaryReport_Gosi> SalaryReport_Gosi { get; set; }
        public virtual DbSet<SaleContract_Draft> SaleContract_Draft { get; set; }
        public virtual DbSet<SaleContract_List> SaleContract_List { get; set; }
        public virtual DbSet<SaleContract_List_Comission> SaleContract_List_Comission { get; set; }
        public virtual DbSet<SaleContract_List_Payments> SaleContract_List_Payments { get; set; }
        public virtual DbSet<SaleContractPaid> SaleContractPaid { get; set; }
        public virtual DbSet<Sales_move_items> Sales_move_items { get; set; }
        public virtual DbSet<SalesMan_types> SalesMan_types { get; set; }
        public virtual DbSet<sallery> sallery { get; set; }
        public virtual DbSet<ScalMove> ScalMove { get; set; }
        public virtual DbSet<Schedule_of_Expenses> Schedule_of_Expenses { get; set; }
        public virtual DbSet<Scope> Scope { get; set; }

        public virtual DbSet<Sectors> Sectors { get; set; }
        public virtual DbSet<Sector> Sector { get; set; }
        public virtual DbSet<Sender> Sender { get; set; }
        public virtual DbSet<SendInsurance> SendInsurance { get; set; }
        public virtual DbSet<SequanceGroups> SequanceGroups { get; set; }
        public virtual DbSet<service> service { get; set; }
        public virtual DbSet<Service_Group_Codes> Service_Group_Codes { get; set; }
        public virtual DbSet<Service_Order_Types> Service_Order_Types { get; set; }
        public virtual DbSet<Service_Type> Service_Type { get; set; }
        public virtual DbSet<Services_Requset_Type> Services_Requset_Type { get; set; }
        public virtual DbSet<SetFirstItemStores> SetFirstItemStores { get; set; }
        public virtual DbSet<Shift> Shift { get; set; }
        public virtual DbSet<Shiping_Details> Shiping_Details { get; set; }
        public virtual DbSet<ShipingBill> ShipingBill { get; set; }
        public virtual DbSet<ShipmentData> ShipmentData { get; set; }
        public virtual DbSet<Shipping> Shipping { get; set; }
        public virtual DbSet<ShonKanonya> ShonKanonya { get; set; }
        public virtual DbSet<ShorakaaTable> Shorakaa { get; set; }
        public virtual DbSet<SignatureDepartment> SignatureDepartment { get; set; }
        public virtual DbSet<Signed> Signed { get; set; }
        public virtual DbSet<Signed_Q_Table> Signed_Q_Table { get; set; }
        public virtual DbSet<Signed_Settings> Signed_Settings { get; set; }
        public virtual DbSet<Signing> Signing { get; set; }
        public virtual DbSet<SiteGroups> SiteGroups { get; set; }
        public virtual DbSet<Sites> Sites { get; set; }
        public virtual DbSet<Sms_Settings> Sms_Settings { get; set; }
        public virtual DbSet<solaf> solaf { get; set; }
        public virtual DbSet<solafaccount> solafaccount { get; set; }
        public virtual DbSet<statement> statement { get; set; }
        public virtual DbSet<statement_item> statement_item { get; set; }
        public virtual DbSet<status> status { get; set; }
        public virtual DbSet<SteepTachkhesRepaer> SteepTachkhesRepaer { get; set; }
        public virtual DbSet<Store_End> Store_End { get; set; }
        public virtual DbSet<Warehouse> Warehouse { get; set; }
        public virtual DbSet<Stores_Consumer> Stores_Consumer { get; set; }
        public virtual DbSet<Stores_Consumer_items> Stores_Consumer_items { get; set; }
        public virtual DbSet<Stores_Scan> Stores_Scan { get; set; }
        public virtual DbSet<Stores_Scan_items> Stores_Scan_items { get; set; }
        public virtual DbSet<Stores_Transfares> Stores_Transfares { get; set; }
        public virtual DbSet<Stores_Transfares_items> Stores_Transfares_items { get; set; }
        public virtual DbSet<StoreSecurity> StoreSecurity { get; set; }
        public virtual DbSet<StorTakyeeem> StorTakyeeem { get; set; }
        public virtual DbSet<StoSteeps> StoSteeps { get; set; }
        public virtual DbSet<StuBasicMony> StuBasicMony { get; set; }
        public virtual DbSet<student_data> student_data { get; set; }
        public virtual DbSet<StudentCount> StudentCount { get; set; }
        public virtual DbSet<studint_comming> studint_comming { get; set; }
        public virtual DbSet<StuMatrial> StuMatrial { get; set; }
        public virtual DbSet<StuMatrialDegree> StuMatrialDegree { get; set; }
        public virtual DbSet<StuMatrialDegreeItems> StuMatrialDegreeItems { get; set; }
        public virtual DbSet<StuSecondMasrof> StuSecondMasrof { get; set; }
        public virtual DbSet<SubCategory> SubCategory { get; set; }
        public virtual DbSet<SuccessPartners> SuccessPartners { get; set; }
        public virtual DbSet<supplier_contact> supplier_contact { get; set; }
        public virtual DbSet<Supplier_Extract_Final> Supplier_Extract_Final { get; set; }
        public virtual DbSet<Supplier_Extract_Head> Supplier_Extract_Head { get; set; }
        public virtual DbSet<Supplier_Extract_Items> Supplier_Extract_Items { get; set; }
        public virtual DbSet<Suppliers> suppliers { get; set; }
        public virtual DbSet<SuppType> SuppType { get; set; }
        public virtual DbSet<Systems> Systems { get; set; }
        public virtual DbSet<Systems_Menu> Systems_Menu { get; set; }
        public virtual DbSet<Systems_Menu_Users> Systems_Menu_Users { get; set; }
        public virtual DbSet<Systems_Sub> Systems_Sub { get; set; }
        public virtual DbSet<t_Clock_List> t_Clock_List { get; set; }
        public virtual DbSet<t_PrivDesc> t_PrivDesc { get; set; }
        public virtual DbSet<t_UserPriv> t_UserPriv { get; set; }
        public virtual DbSet<t_UsersList> t_UsersList { get; set; }
        public virtual DbSet<Table_OMOLAT> Table_OMOLAT { get; set; }
        public virtual DbSet<Table_Parameter> Table_Parameter { get; set; }
        public virtual DbSet<Table_Student_State> Table_Student_State { get; set; }
        public virtual DbSet<tahseelrep> tahseelrep { get; set; }
        public virtual DbSet<TahselType_Tiem> TahselType_Tiem { get; set; }
        public virtual DbSet<Talab_Edafa> Talab_Edafa { get; set; }
        public virtual DbSet<Talab_Edafa_Items> Talab_Edafa_Items { get; set; }
        public virtual DbSet<Talab_Sarf> Talab_Sarf { get; set; }
        public virtual DbSet<Talab_Sarf_Items> Talab_Sarf_Items { get; set; }
        public virtual DbSet<TalabTasweya> TalabTasweya { get; set; }
        public virtual DbSet<Taqseet> Taqseet { get; set; }
        public virtual DbSet<Taqseet_Items> Taqseet_Items { get; set; }
        public virtual DbSet<Target_Omola> Target_Omola { get; set; }
        public virtual DbSet<Target_Omola_Value> Target_Omola_Value { get; set; }
        public virtual DbSet<task_order> task_order { get; set; }
        public virtual DbSet<task_order_item> task_order_item { get; set; }
        public virtual DbSet<Taslem_Machine> Taslem_Machine { get; set; }
        public virtual DbSet<TasnefTogary> TasnefTogary { get; set; }
        public virtual DbSet<TaswyaEdafa> TaswyaEdafa { get; set; }
        public virtual DbSet<TaswyaEdafa_item> TaswyaEdafa_item { get; set; }
        public virtual DbSet<TaswyaKhasm> TaswyaKhasm { get; set; }
        public virtual DbSet<TaswyaKhasm_item> TaswyaKhasm_item { get; set; }
        public virtual DbSet<TaswyaMony> TaswyaMony { get; set; }
        public virtual DbSet<TaswyaMony_Emp> TaswyaMony_Emp { get; set; }
        public virtual DbSet<TaswyaMonyEmpHead> TaswyaMonyEmpHead { get; set; }
        public virtual DbSet<Taswyat_Bank> Taswyat_Bank { get; set; }
        public virtual DbSet<tawzee> tawzee { get; set; }
        public virtual DbSet<Tax> Tax { get; set; }
        public virtual DbSet<TaxGroup> TaxGroup { get; set; }
        public virtual DbSet<Tbl_SUN_Patch> Tbl_SUN_Patch { get; set; }
        public virtual DbSet<tblAccTree> tblAccTree { get; set; }
        public virtual DbSet<TblAccTreeAZ> TblAccTreeAZ { get; set; }
        public virtual DbSet<tblbarcode> tblbarcode { get; set; }
        public virtual DbSet<tblChat> tblChat { get; set; }
        public virtual DbSet<Tblcost_TreeMaster> Tblcost_TreeMaster { get; set; }
        public virtual DbSet<TblCostTree> TblCostTree { get; set; }
        public virtual DbSet<TblTree_Master> TblTree_Master { get; set; }
        public virtual DbSet<TempAcc> TempAcc { get; set; }
        public virtual DbSet<Tender_Content> Tender_Content { get; set; }
        public virtual DbSet<Tender_Fany_Maly_Rep> Tender_Fany_Maly_Rep { get; set; }
        public virtual DbSet<Tender_Forms> Tender_Forms { get; set; }
        public virtual DbSet<Tender_Group_Security> Tender_Group_Security { get; set; }
        public virtual DbSet<Tender_Groups> Tender_Groups { get; set; }
        public virtual DbSet<tender_items> tender_items { get; set; }
        public virtual DbSet<Tender_Monaksat_Type> Tender_Monaksat_Type { get; set; }
        public virtual DbSet<Tender_Other_Co_Type> Tender_Other_Co_Type { get; set; }
        public virtual DbSet<Tender_Owners> Tender_Owners { get; set; }
        public virtual DbSet<Tender_Tech> Tender_Tech { get; set; }
        public virtual DbSet<Tender_Word_Secsion> Tender_Word_Secsion { get; set; }
        public virtual DbSet<Tender_Work> Tender_Work { get; set; }
        public virtual DbSet<Tender_Work_Items_Details> Tender_Work_Items_Details { get; set; }
        public virtual DbSet<Tender_Work_Items_Details_Cost> Tender_Work_Items_Details_Cost { get; set; }
        public virtual DbSet<Tender_Works_item> Tender_Works_item { get; set; }
        public virtual DbSet<tenders> tenders { get; set; }
        public virtual DbSet<Tenders_Add> Tenders_Add { get; set; }
        public virtual DbSet<Tenders_Classes> Tenders_Classes { get; set; }
        public virtual DbSet<Tenders_Consalts> Tenders_Consalts { get; set; }
        public virtual DbSet<Tenders_other_Company> Tenders_other_Company { get; set; }
        public virtual DbSet<Tenders_State> Tenders_State { get; set; }
        public virtual DbSet<Tenders_Type> Tenders_Type { get; set; }
        public virtual DbSet<TendersOwnersAdd> TendersOwnersAdd { get; set; }
        public virtual DbSet<Tendr_Basic_Work> Tendr_Basic_Work { get; set; }
        public virtual DbSet<Tendr_Mony> Tendr_Mony { get; set; }
        public virtual DbSet<testproduct> testproduct { get; set; }
        public virtual DbSet<Tickets> Tickets { get; set; }
        public virtual DbSet<timeworkdoc> timeworkdoc { get; set; }
        public virtual DbSet<TooleAndMachineReceved> TooleAndMachineReceved { get; set; }
        public virtual DbSet<TooleMove> TooleMove { get; set; }
        public virtual DbSet<TooleMoveExtraCost> TooleMoveExtraCost { get; set; }
        public virtual DbSet<Total_DaylySale> Total_DaylySale { get; set; }
        public virtual DbSet<trainer> trainer { get; set; }
        public virtual DbSet<trainerprogram> trainerprogram { get; set; }
        public virtual DbSet<trainingneed> trainingneed { get; set; }
        public virtual DbSet<trainingplanofyear> trainingplanofyear { get; set; }
        public virtual DbSet<trainingtakyeem> trainingtakyeem { get; set; }
        public virtual DbSet<trans> trans { get; set; }
        public virtual DbSet<Transactions> Transactions { get; set; }
        public virtual DbSet<TranseToAnotheSchool> TranseToAnotheSchool { get; set; }
        public virtual DbSet<Transfer_Emp> Transfer_Emp { get; set; }
        public virtual DbSet<Transfer_Opration> Transfer_Opration { get; set; }
        public virtual DbSet<Transfer_Opration_Item> Transfer_Opration_Item { get; set; }
        public virtual DbSet<Transport_Order> Transport_Order { get; set; }
        public virtual DbSet<TransToTameenSehy> TransToTameenSehy { get; set; }
        public virtual DbSet<Tree_Acco_Supjects> Tree_Acco_Supjects { get; set; }
        public virtual DbSet<Tree_Account_Helb> Tree_Account_Helb { get; set; }
        public virtual DbSet<Tree_burchase> Tree_burchase { get; set; }
        public virtual DbSet<Tree_Burchase_Supjects> Tree_Burchase_Supjects { get; set; }
        public virtual DbSet<Tree_Cost> Tree_Cost { get; set; }
        public virtual DbSet<Tree_Cost_Security> Tree_Cost_Security { get; set; }
        public virtual DbSet<Tree_Cost_Supjects> Tree_Cost_Supjects { get; set; }
        public virtual DbSet<Tree_Emp_Supjects> Tree_Emp_Supjects { get; set; }
        public virtual DbSet<Tree_Employee> Tree_Employee { get; set; }
        public virtual DbSet<Tree_Estate> Tree_Estate { get; set; }
        public virtual DbSet<Tree_Estate_Supjects> Tree_Estate_Supjects { get; set; }
        public virtual DbSet<Tree_Excu_Supjects> Tree_Excu_Supjects { get; set; }
        public virtual DbSet<Tree_Excutive> Tree_Excutive { get; set; }
        public virtual DbSet<Tree_Fanya> Tree_Fanya { get; set; }
        public virtual DbSet<Tree_Fanya_Supjects> Tree_Fanya_Supjects { get; set; }
        public virtual DbSet<Tree_Gawdah_Supjects> Tree_Gawdah_Supjects { get; set; }
        public virtual DbSet<Tree_Gawodah> Tree_Gawodah { get; set; }
        public virtual DbSet<Tree_GM> Tree_GM { get; set; }
        public virtual DbSet<Tree_Gm_supjects> Tree_Gm_supjects { get; set; }
        public virtual DbSet<Tree_IT> Tree_IT { get; set; }
        public virtual DbSet<Tree_IT_Supjects> Tree_IT_Supjects { get; set; }
        public virtual DbSet<Tree_Makbodat_Bank> Tree_Makbodat_Bank { get; set; }
        public virtual DbSet<Tree_Makbodat_box> Tree_Makbodat_box { get; set; }
        public virtual DbSet<Tree_Masroft_Bank> Tree_Masroft_Bank { get; set; }
        public virtual DbSet<Tree_Masroft_box> Tree_Masroft_box { get; set; }
        public virtual DbSet<Tree_PO> Tree_PO { get; set; }
        public virtual DbSet<Tree_PO_Supjects> Tree_PO_Supjects { get; set; }
        public virtual DbSet<Tree_Tech> Tree_Tech { get; set; }
        public virtual DbSet<Tree_Tech_Supjects> Tree_Tech_Supjects { get; set; }
        public virtual DbSet<TreeSecurity> TreeSecurity { get; set; }
        public virtual DbSet<trenerform> trenerform { get; set; }
        public virtual DbSet<unit> unit { get; set; }
        public virtual DbSet<UpDate_Salary> UpDate_Salary { get; set; }
        public virtual DbSet<UpdateEmpID> UpdateEmpID { get; set; }
        public virtual DbSet<User_Info> User_Info { get; set; }
        public virtual DbSet<UserProfile> UserProfile { get; set; }
        public virtual DbSet<FalconUsers> FalconUsers { get; set; }
        public virtual DbSet<UserSystem> UserSystem { get; set; }
        public virtual DbSet<vacation_code> vacation_code { get; set; }
        public virtual DbSet<VATSetting> VATSetting { get; set; }
        public virtual DbSet<Vehicle_List> Vehicle_List { get; set; }
        public virtual DbSet<venderaccount> venderaccount { get; set; }
        public virtual DbSet<Vg_Receiving> Vg_Receiving { get; set; }
        public virtual DbSet<Vg_Receiving_Item> Vg_Receiving_Item { get; set; }
        public virtual DbSet<Vg_Sales> Vg_Sales { get; set; }
        public virtual DbSet<Vg_Sales_Items> Vg_Sales_Items { get; set; }
        public virtual DbSet<Vg_SuplierBill> Vg_SuplierBill { get; set; }
        public virtual DbSet<Vg_SuplierBill_items> Vg_SuplierBill_items { get; set; }
        public virtual DbSet<View_Status_Stores_Resulte> View_Status_Stores_Resulte { get; set; }
        public virtual DbSet<Visa> Visa { get; set; }
        public virtual DbSet<Visa_Sub> Visa_Sub { get; set; }
        public virtual DbSet<Visitors> Visitors { get; set; }
        public virtual DbSet<VoidItems> VoidItems { get; set; }
        public virtual DbSet<wathaek> wathaek { get; set; }
        public virtual DbSet<WathaekType> WathaekType { get; set; }
        public virtual DbSet<WayBill> WayBill { get; set; }
        public virtual DbSet<WhatsappSetting> WhatsappSetting { get; set; }
        public virtual DbSet<whaves> whaves { get; set; }
        public virtual DbSet<WordFile> WordFile { get; set; }
        public virtual DbSet<workPlan> workPlan { get; set; }
        public virtual DbSet<WorkPlanEdite> WorkPlanEdite { get; set; }
        public virtual DbSet<XOTickets> XOTickets { get; set; }


        ///// <summary>
        ///// ////////////////////////////////////////////////////////////////////
        ///// </summary>

        public virtual DbSet<MenuItem> MenuItems { get; set; }
        public virtual DbSet<Permission> Permissions { get; set; }

        public virtual DbSet<Curaunes> Currencies { get; set; }
        public virtual DbSet<Country> Countries { set; get; }
        public virtual DbSet<ProductCategory> ProductCategories { set; get; }


        public virtual DbSet<Setting> Settings { set; get; }


        public virtual DbSet<RefreshToken> RefreshTokens { get; set; }
        public virtual DbSet<Notification> Notifications { set; get; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            //ModelConfigurationBuilder.DefaultTypeMapping()



            base.OnModelCreating(builder);

            builder.Entity<ApplicationUser>()
               .ToTable("AspNetUsers")
               .HasMany(u => u.Roles)
               .WithOne(r => r.User)
               .HasPrincipalKey(u => u.Id)
               .HasForeignKey(r => r.UserId)
               .IsRequired();

            builder.Entity<ApplicationRole>()
               .ToTable("AspNetRoles")
               .HasMany(u => u.UserRoles)
               .WithOne(r => r.Role)
               .HasPrincipalKey(u => u.Id)
               .HasForeignKey(r => r.RoleId)
               .IsRequired();



            builder.Entity<ApplicationUserRole>()
                .HasNoDiscriminator();

            builder.Entity<InventoryQulityChecks>()
           .HasIndex(x => x.InventoryQulityControlId);
            builder.Entity<InventoryQulityChecks>()
                .HasIndex(x => x.ProductId);


            foreach (var relationship in builder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))

            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;

            }


        }
    }
}
