using AutoMapper;
using DocumentFormat.OpenXml.Wordprocessing;
using FalconFramework.ApplicationCore.DTOs;
using FalconFramework.ApplicationCore.DTOs.Accounting;
using FalconFramework.ApplicationCore.DTOs.CRM;
using FalconFramework.ApplicationCore.DTOs.General;
using FalconFramework.ApplicationCore.DTOs.HR;
using FalconFramework.ApplicationCore.DTOs.Inventory;
using FalconFramework.ApplicationCore.DTOs.Maintenance;
using FalconFramework.ApplicationCore.DTOs.Manufacturing;
using FalconFramework.ApplicationCore.DTOs.Project;
using FalconFramework.ApplicationCore.DTOs.Purchase;
using FalconFramework.ApplicationCore.DTOs.Realestate;
using FalconFramework.ApplicationCore.DTOs.Sales;
using FalconFramework.ApplicationCore.DTOs.StaffAssistant;
using FalconFramework.ApplicationCore.Entities;

namespace FalconFramework
{
    public class AutoMapperProfiles
    {
        public class CompaniesProfile : Profile
        {
            public CompaniesProfile()
            {
                CreateMap<Companies, CompanyListViewModel>()
                    .ForMember(dest => dest.id, op => op.MapFrom(src => src.Id))
                    .ForMember(dest => dest.name, op => op.MapFrom(src => src.Name));
            }
        }

        public class StaffattendanceProfile : Profile
        {
            public StaffattendanceProfile()
            {
                CreateMap<Emp_Comming, StaffattendanceCheckIn>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.CheckIn, op => op.MapFrom(e => e.Emp_Time_Comming));
                CreateMap<Emp_Comming, StaffattendanceCheckOut>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.CheckOut, op => op.MapFrom(e => e.Emp_Time_Out));
                CreateMap<Emp_Comming, StaffattendanceBreakOut>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.BreakOut, op => op.MapFrom(e => e.brakeOut));
                CreateMap<Emp_Comming, StaffattendanceBreakIn>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.BreakIn, op => op.MapFrom(e => e.brakein));

                CreateMap<StaffattendanceCheckIn, Emp_Comming>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.Emp_Time_Comming, op => op.MapFrom(e => e.CheckIn));
                CreateMap<StaffattendanceCheckOut, Emp_Comming>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.Emp_Time_Out, op => op.MapFrom(e => e.CheckOut));
                CreateMap<StaffattendanceBreakOut, Emp_Comming>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.brakeOut, op => op.MapFrom(e => e.BreakOut));
                CreateMap<StaffattendanceBreakIn, Emp_Comming>()
                 .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostID))
                 .ForMember(M => M.brakein, op => op.MapFrom(e => e.BreakIn));

                CreateMap<Emp_Comming, StaffattendanceViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.employeeId))
                    .ForMember(M => M.Date, op => op.MapFrom(e => e.emp_datecomming))
                    .ForMember(M => M.DayName, op => op.MapFrom(e => e.dayname))
                    .ForMember(M => M.CheckIn, op => op.MapFrom(e => e.Emp_Time_Comming))
                    .ForMember(M => M.CheckOut, op => op.MapFrom(e => e.Emp_Time_Out))
                    .ForMember(M => M.BreakIn, op => op.MapFrom(e => e.brakein))
                    .ForMember(M => M.BreakOut, op => op.MapFrom(e => e.brakeOut))
                    .ForMember(M => M.OverTime, op => op.MapFrom(e => e.Extra_Hours_Values))
                    .ForMember(M => M.AnalyticCode, op => op.MapFrom(e => e.CostID));

                CreateMap<StaffattendanceViewModel, Emp_Comming>()
                .ForMember(M => M.employeeId, op => op.MapFrom(e => e.id))
                   .ForMember(M => M.emp_datecomming, op => op.MapFrom(e => e.Date))
                   .ForMember(M => M.dayname, op => op.MapFrom(e => e.DayName))
                   .ForMember(M => M.Emp_Time_Comming, op => op.MapFrom(e => e.CheckIn))
                   .ForMember(M => M.Emp_Time_Out, op => op.MapFrom(e => e.CheckOut))
                   .ForMember(M => M.brakein, op => op.MapFrom(e => e.BreakIn))
                   .ForMember(M => M.brakeOut, op => op.MapFrom(e => e.BreakOut))
                   .ForMember(M => M.Extra_Hours_Values, op => op.MapFrom(e => e.OverTime))
                   .ForMember(M => M.CostID, op => op.MapFrom(e => e.AnalyticCode));

            }
        }

        //Inventory




        public class AdditionAdjustmentsProfile : Profile
        {
            public AdditionAdjustmentsProfile()
            {
                CreateMap<InputAdditionAdjustments, TaswyaEdafa>()

               .ForMember(M => M.Edafadate, op => op.MapFrom(e => e.effectiveDate))
               .ForMember(M => M.gardno, op => op.MapFrom(e => e.AdjustmentNo))
               .ForMember(M => M.year, op => op.MapFrom(e => e.Year))
               .ForMember(M => M.notes, op => op.MapFrom(e => e.Notes))
               .ForMember(M => M.companyId, op => op.MapFrom(e => e.CompanyId))
               .ForMember(M => M.branchId, op => op.MapFrom(e => e.BranchId))
               .ForMember(M => M.Journal, op => op.MapFrom(e => e.Journal))
               .ForMember(M => M.eznTypeId, op => op.MapFrom(e => e.MovementTypeId))
               .ForMember(M => M.gardno, op => op.MapFrom(e => e.AdjustmentNo));

                CreateMap<TaswyaEdafa, AdditionAdjustmentsViewModel>()
               .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))

               .ForMember(M => M.AdjustmentNo, op => op.MapFrom(e => e.gardno))
               .ForMember(M => M.MovementType, op => op.MapFrom(e => e.EznType.TypeName))
               .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name));

                CreateMap<TaswyaEdafa_item, AdditionAdjustmentsLines>()
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID));

            }
        }

        public class NetConsumerProfile : Profile
        {
            public NetConsumerProfile()
            {
                CreateMap<InputNetConsumer, Stores_Consumer>()
                    .ForMember(M => M.GardID, op => op.MapFrom(e => e.id));
                CreateMap<Stores_Consumer, NetConsumerViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.GardID));
            }
        }

        public class DeductionsAdjustmentsProfile : Profile
        {
            public DeductionsAdjustmentsProfile()
            {

                CreateMap<InputDeductionsAdjustments, TaswyaKhasm>()
              .ForMember(M => M.Sarfdate, op => op.MapFrom(e => e.Date))
              .ForMember(M => M.gardno, op => op.MapFrom(e => e.AdjustmentNo));
                CreateMap<TaswyaKhasm, DeductionsAdjustmentsViewModel>()
               .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
               .ForMember(M => M.Date, op => op.MapFrom(e => e.Sarfdate))
               .ForMember(M => M.AdjustmentNo, op => op.MapFrom(e => e.gardno))
               .ForMember(M => M.MovementType, op => op.MapFrom(e => e.EznType.TypeName))
               .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name));

                CreateMap<TaswyaKhasm_item, DeductionsAdjustmentsLines>()
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID));

            }
        }

        public class ReceiptsProfile : Profile
        {
            public ReceiptsProfile()
            {
                CreateMap<InputReceipts, EznEdafa>()
                 .ForMember(M => M.EdafaID, op => op.MapFrom(e => e.Id))
                 .ForMember(M => M.Edafadate, op => op.MapFrom(e => e.EffectiveDate));
                CreateMap<EznEdafa, ReceiptsViewModel>()
                    .ForMember(M => M.Id, op => op.MapFrom(e => e.EdafaID))
               .ForMember(M => M.EffectiveDate, op => op.MapFrom(e => e.Edafadate))
               .ForMember(M => M.MovementType, op => op.MapFrom(e => e.EznType.TypeName))
                .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name));

                CreateMap<EznEdafa_item, ReceiptsLines>()
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID))
                 ;

            }
        }

        public class InventoryAdjustmentsProfile : Profile
        {
            public InventoryAdjustmentsProfile()
            {
                CreateMap<InputInventoryAdjustments, Stores_Scan>()
                    .ForMember(M => M.GardID, op => op.MapFrom(e => e.Id))
                    .ForMember(M => M.ScanDate, op => op.MapFrom(e => e.effectiveDate))
                    .ForMember(M => M.warehouseId, op => op.MapFrom(e => e.WarehouseId))
                    .ForMember(M => M.branchId, op => op.MapFrom(e => e.branchId))
                    .ForMember(M => M.companyId, op => op.MapFrom(e => e.companyId))
                    .ForMember(M => M.year, op => op.MapFrom(e => e.Year))
                    .ForMember(M => M.notes, op => op.MapFrom(e => e.Notes))
                    .ForMember(M => M.ActionDate, op => op.MapFrom(e => e.ActionDate))
                    .ForMember(M => M.UserName, op => op.MapFrom(e => e.UserName))
                    .ForMember(M => M.Journal, op => op.MapFrom(e => e.Journal))
                    .ForMember(M => M.MovementTypeId, op => op.MapFrom(e => e.MovementTypeId))
                    .ForMember(M => M.ConsumerID, op => op.MapFrom(e => e.ConsumerID))
                    .ForMember(M => M.Lokked, op => op.MapFrom(e => e.Lokked));

                CreateMap<Stores_Scan, InventoryAdjustmentsViewModel>()
                   .ForMember(M => M.Id, op => op.MapFrom(e => e.GardID))
                   .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.ScanDate))
                   .ForMember(M => M.WarehouseId, op => op.MapFrom(e => e.warehouseId))
                   .ForMember(M => M.branchId, op => op.MapFrom(e => e.branchId))
                   .ForMember(M => M.companyId, op => op.MapFrom(e => e.companyId))
                   .ForMember(M => M.Year, op => op.MapFrom(e => e.year))
                   .ForMember(M => M.Notes, op => op.MapFrom(e => e.notes))
                   .ForMember(M => M.ActionDate, op => op.MapFrom(e => e.ActionDate))
                   .ForMember(M => M.UserName, op => op.MapFrom(e => e.UserName))
                   .ForMember(M => M.Journal, op => op.MapFrom(e => e.Journal))
                   .ForMember(M => M.MovementTypeId, op => op.MapFrom(e => e.MovementTypeId))
                   .ForMember(M => M.ConsumerID, op => op.MapFrom(e => e.ConsumerID))
                   .ForMember(M => M.Lokked, op => op.MapFrom(e => e.Lokked))
                   .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse != null ? e.Warehouse.store_name : ""))
                   .ForMember(M => M.Company, op => op.MapFrom(e => e.Company != null ? e.Company.Name : ""))
                   .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch != null ? e.Branch.Purnsh_name : ""));

                CreateMap<Stores_Scan_items, InventoryAdjustmentsLines>()
            .ForMember(M => M.InventoryAdjustmentsId, op => op.MapFrom(e => e.GardID));
            }
        }


        public class EmployeesCustodyProfile : Profile
        {
            public EmployeesCustodyProfile()
            {
                CreateMap<InputEmployeesCustody, EsnSarfOhda>()
                    .ForMember(M => M.SarfID, op => op.MapFrom(e => e.Id))
                    .ForMember(M => M.Sarfdate, op => op.MapFrom(e => e.effectiveDate))
                    .ForMember(M => M.year, op => op.MapFrom(e => e.Year))
                    .ForMember(M => M.notes, op => op.MapFrom(e => e.Notes))
                    .ForMember(M => M.EznTypeId, op => op.MapFrom(e => e.MovementTypeId))
                    .ForMember(M => M.branchId, op => op.MapFrom(e => e.BranchId))
                    .ForMember(M => M.companyId, op => op.MapFrom(e => e.CompanyId));
                CreateMap<EsnSarfOhda, EmployeesCustodyViewModel>()
                 .ForMember(M => M.Id, op => op.MapFrom(e => e.SarfID))
                  .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.Sarfdate))
                    .ForMember(M => M.MovementType, op => op.MapFrom(e => e.EznType.TypeName))
                    .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                    .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                    .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name));

                CreateMap<EsnSarf_items, EmployeesCustodyLines>()
                .ForMember(M => M.EmployeesCustodyId, op => op.MapFrom(e => e.SarfID))
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.SalesUnitId, op => op.MapFrom(e => e.SaleUnitID))
                .ForMember(M => M.UnitQuantity, op => op.MapFrom(e => e.Unit_Quantity))
                .ForMember(M => M.UnitPrice, op => op.MapFrom(e => e.Unit_Price))
                .ForMember(M => M.DefualtItemCost, op => op.MapFrom(e => e.Defualt_ItemCost))
                .ForMember(M => M.UnitBalance, op => op.MapFrom(e => e.Unit_Balance))
                .ForMember(M => M.WarehouseId, op => op.MapFrom(e => e.Store_ID))

                ;
            }
        }

        public class CustomersSamplesProfile : Profile
        {
            public CustomersSamplesProfile()
            {
                CreateMap<InputCustomersSamples, CustomersSamples>();

                CreateMap<CustomersSamples, CustomersSamplesViewModel>()
                .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name))
                .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name));

            }
        }

        public class DeliveryNoteProfile : Profile
        {
            public DeliveryNoteProfile()
            {
                CreateMap<InputDeliveryNote, Delivery_bill>()
                    .ForMember(M => M.Delivery_ID, op => op.MapFrom(e => e.Delivery_ID));
                CreateMap<Delivery_bill, DeliveryNoteViewModel>()
                 .ForMember(M => M.Delivery_ID, op => op.MapFrom(e => e.Delivery_ID));
            }
        }


        public class DeliveryExecuteProfile : Profile
        {
            public DeliveryExecuteProfile()
            {
                CreateMap<InputDeliveryExecute, Item_Card>()
                 .ForMember(M => M.Item_Id, op => op.MapFrom(e => e.id));
                CreateMap<Item_Card, DeliveryExecuteViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.Item_Id));
            }
        }


        public class TransfersProfile : Profile
        {
            public TransfersProfile()
            {
                CreateMap<InputTransfer, Stores_Transfares>()
                 .ForMember(M => M.TransId, op => op.MapFrom(e => e.Id))
                 .ForMember(M => M.TransDate, op => op.MapFrom(e => e.effectiveDate))
                 .ForMember(M => M.Notes, op => op.MapFrom(e => e.Notes))
                 .ForMember(M => M.UserName, op => op.MapFrom(e => e.UserName))
                 .ForMember(M => M.ActionDate, op => op.MapFrom(e => e.ActionDate))
                 .ForMember(M => M.year, op => op.MapFrom(e => e.Year))
                 .ForMember(M => M.branchId, op => op.MapFrom(e => e.BranchId))
                 .ForMember(M => M.companyId, op => op.MapFrom(e => e.CompanyId))
                 .ForMember(M => M.Journal, op => op.MapFrom(e => e.Journal))
                 .ForMember(M => M.TalabSarfID, op => op.MapFrom(e => e.MovementTypeId));

                CreateMap<Stores_Transfares, TransfersViewModel>()
                 .ForMember(M => M.Id, op => op.MapFrom(e => e.TransId))
                 .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.TransDate))
                 .ForMember(M => M.Notes, op => op.MapFrom(e => e.Notes))
                 .ForMember(M => M.UserName, op => op.MapFrom(e => e.UserName))
                 .ForMember(M => M.ActionDate, op => op.MapFrom(e => e.ActionDate))
                 .ForMember(M => M.Year, op => op.MapFrom(e => e.year))
                 .ForMember(M => M.BranchId, op => op.MapFrom(e => e.branchId))
                 .ForMember(M => M.CompanyId, op => op.MapFrom(e => e.companyId))
                 .ForMember(M => M.Journal, op => op.MapFrom(e => e.Journal))
                 .ForMember(M => M.MovementTypeId, op => op.MapFrom(e => e.TalabSarfID))
                 .ForMember(M => M.FromWarehouse, op => op.MapFrom(e => e.FromWarehouse != null ? e.FromWarehouse.store_name : ""))
                 .ForMember(M => M.ToWarehouse, op => op.MapFrom(e => e.ToWarehouse != null ? e.ToWarehouse.store_name : ""))
                 .ForMember(M => M.Company, op => op.MapFrom(e => e.Company != null ? e.Company.Name : ""))
                 .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch != null ? e.Branch.Purnsh_name : ""))
                 .ForMember(M => M.MovementType, op => op.MapFrom(e => "تحويل مخزني"))
                 .ForMember(M => M.TransferLines, op => op.Ignore());

                CreateMap<Stores_Transfares_items, TransferLines>()
                .ForMember(M => M.TransferId, op => op.MapFrom(e => e.TransId))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.ProductId, op => op.MapFrom(e => e.itemid))
                .ForMember(M => M.productName, op => op.MapFrom(e => ""))
                .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID))
                .ForMember(M => M.UnitName, op => op.MapFrom(e => e.Unite))
                .ForMember(M => M.Unite, op => op.MapFrom(e => e.Unite))
                .ForMember(M => M.Price, op => op.MapFrom(e => e.Price))
                .ForMember(M => M.Quantity, op => op.MapFrom(e => e.Quantity))
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.Bonus, op => op.MapFrom(e => e.Bounas))
                .ForMember(M => M.UnitQuantity, op => op.MapFrom(e => e.Unit_Quantity))
                .ForMember(M => M.UnitPrice, op => op.MapFrom(e => e.Unit_Price))
                .ForMember(M => M.DefualtItemCost, op => op.MapFrom(e => e.Defualt_ItemCost))
                .ForMember(M => M.UnitCost, op => op.MapFrom(e => e.UnitCost))
                .ForMember(M => M.UnitBalance, op => op.MapFrom(e => e.Unit_Balance))
                .ForMember(M => M.Rate, op => op.MapFrom(e => e.Rate))
                .ForMember(M => M.CostAllItemOut, op => op.MapFrom(e => e.CostAllItemOut))
                .ForMember(M => M.Margin, op => op.MapFrom(e => e.maksap))
                .ForMember(M => M.CostId, op => op.MapFrom(e => e.CostID))
                .ForMember(M => M.Account_ID, op => op.MapFrom(e => 0))
                .ForMember(M => M.WarehouseId, op => op.MapFrom(e => 0))
                .ForMember(M => M.Store_Account, op => op.MapFrom(e => 0))
                .ForMember(M => M.NotesPerLine, op => op.MapFrom(e => ""))
                .ForMember(M => M.VariantName, op => op.MapFrom(e => e.VariantName))
                .ForMember(M => M.ManufacturingOrder, op => op.MapFrom(e => 0))
                .ForMember(M => M.deliveryOrder, op => op.MapFrom(e => 0))
                .ForMember(M => M.Scrap, op => op.MapFrom(e => 0))
                .ForMember(M => M.AnalyticAccountId, op => op.MapFrom(e => ""))
                .ForMember(M => M.AnalyticAccountName, op => op.MapFrom(e => ""));

                CreateMap<TransferLines, Stores_Transfares_items>()
                .ForMember(M => M.TransId, op => op.MapFrom(e => e.TransferId))
                .ForMember(M => M.serial, op => op.MapFrom(e => e.LineNo))
                .ForMember(M => M.itemid, op => op.MapFrom(e => e.ProductId))
                .ForMember(M => M.Unite, op => op.MapFrom(e => e.Unite))
                .ForMember(M => M.Price, op => op.MapFrom(e => e.Price))
                .ForMember(M => M.Quantity, op => op.MapFrom(e => e.Quantity))
                .ForMember(M => M.total, op => op.MapFrom(e => e.Amount))
                .ForMember(M => M.SN, op => op.MapFrom(e => e.SerialNumber))
                .ForMember(M => M.Bounas, op => op.MapFrom(e => e.Bonus))
                .ForMember(M => M.Unit_Quantity, op => op.MapFrom(e => e.UnitQuantity))
                .ForMember(M => M.Unit_Price, op => op.MapFrom(e => e.UnitPrice))
                .ForMember(M => M.Defualt_ItemCost, op => op.MapFrom(e => e.DefualtItemCost))
                .ForMember(M => M.UnitCost, op => op.MapFrom(e => e.UnitCost))
                .ForMember(M => M.Unit_Balance, op => op.MapFrom(e => e.UnitBalance))
                .ForMember(M => M.Rate, op => op.MapFrom(e => e.Rate))
                .ForMember(M => M.CostAllItemOut, op => op.MapFrom(e => e.CostAllItemOut))
                .ForMember(M => M.maksap, op => op.MapFrom(e => e.Margin))
                .ForMember(M => M.SaleUnitID, op => op.MapFrom(e => e.unitId))
                .ForMember(M => M.CostID, op => op.MapFrom(e => e.CostId))
                .ForMember(M => M.VariantName, op => op.MapFrom(e => e.VariantName));
            }
        }

        public class OpeningbalanceProfile : Profile
        {
            public OpeningbalanceProfile()
            {
                CreateMap<InputOpeningbalance, FristStok>()
                     .ForMember(M => M.FSID, op => op.MapFrom(e => e.Id));
                CreateMap<FristStok, OpeningbalanceViewModel>()
                .ForMember(M => M.Id, op => op.MapFrom(e => e.FSID))
                .ForMember(M => M.WarehouseId, op => op.MapFrom(e => e.warehouseId))
                .ForMember(M => M.BranchId, op => op.MapFrom(e => e.branchId))
                .ForMember(M => M.CompanyId, op => op.MapFrom(e => e.companyId))
                .ForMember(M => M.Year, op => op.MapFrom(e => (long?)e.year))
                .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.FSdate))
                .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name))
                .ForMember(M => M.OpeningbalanceLines, op => op.MapFrom(e => e.FristStok_items));

                CreateMap<FristStok_items, OpeningbalanceLines>()
                .ForMember(M => M.DeliveryId, op => op.MapFrom(e => e.FSID))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => (long?)e.serial))
                .ForMember(M => M.ProductId, op => op.MapFrom(e => (long?)e.itemid))
                .ForMember(M => M.unitId, op => op.MapFrom(e => (long?)e.SaleUnitID));
            }
        }

        public class ReceiptsRequestProfile : Profile
        {
            public ReceiptsRequestProfile()
            {
                CreateMap<InputReceiptsRequest, Talab_Edafa>()
                    .ForMember(M => M.EdafaID, op => op.MapFrom(e => e.Id));
                CreateMap<Talab_Edafa, ReceiptsRequestViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.EdafaID))
                .ForMember(M => M.EffectiveDate, op => op.MapFrom(e => e.Edafadate))
                .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name));
            }
        }

        public class DeliveryRequestProfile : Profile
        {
            public DeliveryRequestProfile()
            {
                CreateMap<InputDeliveryRequest, Talab_Sarf>()
                    .ForMember(M => M.SarfID, op => op.MapFrom(e => e.Id))
                    .ForMember(M => M.Sarfdate, op => op.MapFrom(e => e.effectiveDate))
                    .ForMember(M => M.notes, op => op.MapFrom(e => e.Notes))
                    .ForMember(M => M.year, op => op.MapFrom(e => e.Year));

                CreateMap<Talab_Sarf, DeliveryRequestViewModel>()
                    .ForMember(M => M.Id, op => op.MapFrom(e => e.SarfID))
                    .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.Sarfdate))
                    .ForMember(M => M.Notes, op => op.MapFrom(e => e.notes))
                    .ForMember(M => M.Year, op => op.MapFrom(e => (long?)e.year))
                    .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                    .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                    .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name))
                    .ForMember(M => M.DeliveryRequestLines, op => op.MapFrom(e => e.ReceiptsRequest_items));

                CreateMap<Talab_Sarf_Items, DeliveryRequestLine>()
                    .ForMember(M => M.DeliveryRequestId, op => op.MapFrom(e => e.SarfID))
                    .ForMember(M => M.ProductId, op => op.MapFrom(e => e.productId))
                    .ForMember(M => M.productName, op => op.MapFrom(e => e.Product.Item_Name))
                    .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID))
                    .ForMember(M => M.UnitName, op => op.MapFrom(e => e.SaleUnitName))
                    .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.serial))
                    .ForMember(M => M.Bonus, op => op.MapFrom(e => e.Bounas))
                    .ForMember(M => M.WarehouseId, op => op.MapFrom(e => e.Store_ID))
                    .ForMember(M => M.Store_Account, op => op.MapFrom(e => e.Store_Account))
                    .ForMember(M => M.VariantName, op => op.MapFrom(e => e.VariantName))
                    .ForMember(M => M.PreviousQuantity, op => op.MapFrom(e => e.PreviousQuantity))
                    .ForMember(M => M.ReturnQuantity, op => op.MapFrom(e => e.ReturnQuantity))
                    .ForMember(M => M.NetConsumer, op => op.MapFrom(e => e.NetConsumer))
                    .ForMember(M => M.Remaining, op => op.MapFrom(e => e.Remaining));
            }
        }
        public class DeliveryProfile : Profile
        {
            public DeliveryProfile()
            {

                CreateMap<InputDelivery, EsnSarf>()
                        .ForMember(M => M.SarfID, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.Sarfdate, op => op.MapFrom(e => e.effectiveDate))
                        .ForMember(M => M.InvNO, op => op.MapFrom(e => e.InvoiceNO))
                        .ForMember(M => M.year, op => op.MapFrom(e => e.Year))
                        .ForMember(M => M.notes, op => op.MapFrom(e => e.Notes))
                        .ForMember(M => M.EznTypeId, op => op.MapFrom(e => e.MovementTypeId))
                        .ForMember(M => M.branchId, op => op.MapFrom(e => e.BranchId))
                        .ForMember(M => M.companyId, op => op.MapFrom(e => e.CompanyId))
                        .ForMember(M => M.TO_StoreID, op => op.MapFrom(e => e.TO_WarehouseId))
                        .ForMember(M => M.ProjectID, op => op.MapFrom(e => e.ProjectID));


                CreateMap<EsnSarf, DeliveryViewModel>()
                    .ForMember(M => M.Id, op => op.MapFrom(e => e.SarfID))
                    .ForMember(M => M.effectiveDate, op => op.MapFrom(e => e.Sarfdate))
                    .ForMember(M => M.MovementType, op => op.MapFrom(e => e.EznType.TypeName))
                    .ForMember(M => M.Warehouse, op => op.MapFrom(e => e.Warehouse.store_name))
                    .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch.Purnsh_name))
                    .ForMember(M => M.Company, op => op.MapFrom(e => e.Company.Name));

                CreateMap<EsnSarf_items, DeliveryLines>()
                .ForMember(M => M.DeliveryId, op => op.MapFrom(e => e.SarfID))
                .ForMember(M => M.Amount, op => op.MapFrom(e => e.total))
                .ForMember(M => M.SerialNumber, op => op.MapFrom(e => e.SN))
                .ForMember(M => M.LineNo, op => op.MapFrom(e => e.serial))
                .ForMember(M => M.Bonus, op => op.MapFrom(e => e.Bounas))
                .ForMember(M => M.unitId, op => op.MapFrom(e => e.SaleUnitID))
                .ForMember(M => M.UnitQuantity, op => op.MapFrom(e => e.Unit_Quantity))
                .ForMember(M => M.UnitPrice, op => op.MapFrom(e => e.Unit_Price))
                .ForMember(M => M.DefualtItemCost, op => op.MapFrom(e => e.Defualt_ItemCost))
                .ForMember(M => M.UnitBalance, op => op.MapFrom(e => e.Unit_Balance))
                .ForMember(M => M.Margin, op => op.MapFrom(e => e.maksap))
                .ForMember(M => M.WarehouseId, op => op.MapFrom(e => e.Store_ID))

                ;


            }
        }

        public class BonusProfile : Profile
        {
            public BonusProfile()
            {
                CreateMap<InputBonus, Items_Ponas>()
               .ForMember(M => M.Datefrom, op => op.MapFrom(e => e.FromDate))
               .ForMember(M => M.Datefrom, op => op.MapFrom(e => e.ToDate));
                CreateMap<Items_Ponas, BonusViewModel>()
                    .ForMember(M => M.FromDate, op => op.MapFrom(e => e.Datefrom))
                    .ForMember(M => M.ToDate, op => op.MapFrom(e => e.Datefrom))
                    .ForMember(M => M.Product, op => op.MapFrom(e => e.Product.Item_Name));


            }
        }

        public class ProductAttributesProfile : Profile
        {
            public ProductAttributesProfile()
            {
                CreateMap<InputProductAttributes, ItemVariantType>()
               .ForMember(M => M.VariantNameId, op => op.MapFrom(e => e.id))
               .ForMember(M => M.VariantName, op => op.MapFrom(e => e.Name));
                CreateMap<ItemVariantType, ProductAttributesViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.VariantNameId))
               .ForMember(M => M.Name, op => op.MapFrom(e => e.VariantName));
            }
        }


        public class ProductAttributesValueProfile : Profile
        {
            public ProductAttributesValueProfile()
            {
                CreateMap<InputProductAttributesValues, ItemVariantValues>()
               .ForMember(M => M.VariantValueId, op => op.MapFrom(e => e.id))
               .ForMember(M => M.VariantValue, op => op.MapFrom(e => e.Name));

                CreateMap<ItemVariantValues, ProductAttributesValuesViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.VariantValueId))
               .ForMember(M => M.Name, op => op.MapFrom(e => e.VariantValue));
            }
        }



        public class WarehouseProfile : Profile
        {
            public WarehouseProfile()
            {
                CreateMap<Warehouse, WarehouseViewModel>()
                    .ForMember(s => s.Name, op => op.MapFrom(w => w.store_name))
                    .ForMember(s => s.keepber, op => op.MapFrom(w => w.emp_name))
                    .ForMember(s => s.BranchId, op => op.MapFrom(w => w.ProunchID))
                    .ForMember(s => s.Address, op => op.MapFrom(w => w.store_address));

                CreateMap<InputWarehouse, Warehouse>()
                .ForMember(s => s.store_name, op => op.MapFrom(w => w.Name))
                .ForMember(s => s.emp_name, op => op.MapFrom(w => w.keepber))
                .ForMember(s => s.ProunchID, op => op.MapFrom(w => w.BranchId))
                .ForMember(s => s.store_address, op => op.MapFrom(w => w.Address));
            }
        }

        public class ProductsProfile : Profile
        {
            public ProductsProfile()
            {
                CreateMap<InputProducts, Product>()
                   .ForMember(M => M.Item_Name_AR, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.Item_Name, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.price, op => op.MapFrom(e => e.Price));

                CreateMap<Product, ProductsViewModel>()
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.Item_Name_AR))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.Item_Name))
                    .ForMember(M => M.Price, op => op.MapFrom(e => e.price))
                    .ForMember(M => M.Category, op => op.MapFrom(e => e.Category != null ? e.Category.En_Name : null))
                    .ForMember(M => M.SubCategory, op => op.MapFrom(e => e.SubCategory != null ? e.SubCategory.En_Sub_Name : null))
                    .ForMember(M => M.Branch, op => op.MapFrom(e => e.Branch != null ? e.Branch.Purnsh_name : null))
                    .ForMember(M => M.Company, op => op.MapFrom(e => e.Company != null ? e.Company.Name : null))
                   ;

            }
        }




        //Accounting


        public class AccountThirdPartyProfile : Profile
        {
            public AccountThirdPartyProfile()
            {
                CreateMap<InputAccountThirdParty, Account_Third_Party>().ReverseMap()
               .ForMember(M => M.id, op => op.MapFrom(e => e.Id))
               .ForMember(M => M.NameAr, op => op.MapFrom(e => e.CaseName))
               .ForMember(M => M.NameEn, op => op.MapFrom(e => e.CaseName_EN));
                CreateMap<Account_Third_Party, AccountThirdPartyViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.Id))
               .ForMember(M => M.NameAr, op => op.MapFrom(e => e.CaseName))
               .ForMember(M => M.NameEn, op => op.MapFrom(e => e.CaseName_EN));
            }
        }


        public class AnalyticAccountsGroupsProfile : Profile
        {
            public AnalyticAccountsGroupsProfile()
            {
                CreateMap<InputAnalyticAccountsGroups, CostGroups>()
                 .ForMember(M => M.ClassificationId, op => op.MapFrom(e => e.id))
                 .ForMember(M => M.ClassificationName, op => op.MapFrom(e => e.Name));
                CreateMap<CostGroups, AnalyticAccountsGroupsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ClassificationId))
                 .ForMember(M => M.Name, op => op.MapFrom(e => e.ClassificationName));
            }
        }


        public class AnalyticAccountsLocationProfile : Profile
        {
            public AnalyticAccountsLocationProfile()
            {
                CreateMap<AnalyticAccountsLocation, TblCostTree>()
                 .ForMember(M => M.AccCode, op => op.MapFrom(e => e.Code));


                CreateMap<TblCostTree, AnalyticAccountsLocation>()
                .ForMember(M => M.Code, op => op.MapFrom(e => e.AccCode));

            }
        }

        public class myrequestsProfile : Profile
        {
            public myrequestsProfile()
            {
                CreateMap<myrequestsViewModel, Emp_Requests>()
                 .ForMember(M => M.Emp_ID, op => op.MapFrom(e => e.id));


                CreateMap<Emp_Requests, myrequestsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.Emp_ID));

            }
        }



        public class IndirectCostsSettingProfile : Profile
        {
            public IndirectCostsSettingProfile()
            {
                CreateMap<InputIndirectCostsSetting, Indirect_Cost_Items_Setting>()
                .ForMember(M => M.Item_Index, op => op.MapFrom(e => e.id));
                CreateMap<Indirect_Cost_Items_Setting, IndirectCostsSettingViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.Item_Index));
            }
        }

        public class AssetsDepreciationProfile : Profile
        {
            public AssetsDepreciationProfile()
            {
                CreateMap<InputAssetsDepreciation, Fixed_Aset_Calc_Add>()
                .ForMember(M => M.AslID, op => op.MapFrom(e => e.id));
                CreateMap<Fixed_Aset_Calc_Add, AssetsDepreciationViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.AslID));
            }
        }

        public class AssetsListProfile : Profile
        {
            public AssetsListProfile()
            {
                CreateMap<InputAssetsList, AslName>()
                .ForMember(M => M.AslID, op => op.MapFrom(e => e.id));
                CreateMap<AslName, AssetsListViewModel>()
             .ForMember(M => M.id, op => op.MapFrom(e => e.AslID));
            }
        }

        public class LoansRepaymentProfile : Profile
        {
            public LoansRepaymentProfile()
            {
                CreateMap<InputLoansRepayment, Loan_Payments>()
                .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Loan_Payments, LoansRepaymentViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class RenewalLoanPeriodProfile : Profile
        {
            public RenewalLoanPeriodProfile()
            {
                CreateMap<InputRenewalLoanPeriod, Loan_Extend>()
                .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Loan_Extend, RenewalLoanPeriodViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }


        public class LoansProfile : Profile
        {
            public LoansProfile()
            {
                CreateMap<InputLoans, Loan_Used>()
                .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Loan_Used, LoansViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class LoanLimitsProfile : Profile
        {
            public LoanLimitsProfile()
            {
                CreateMap<InputLoanLimits, Loan_Limit>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Loan_Limit, LoanLimitsViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }


        public class ChangeCreditValueProfile : Profile
        {
            public ChangeCreditValueProfile()
            {
                CreateMap<InputChangeCreditValue, Documentary_ChangeValue>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Documentary_ChangeValue, ChangeCreditValueViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class RenewalAccreditationPeriodProfile : Profile
        {
            public RenewalAccreditationPeriodProfile()
            {
                CreateMap<InputRenewalAccreditationPeriod, Documentary_of_Extend>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Documentary_of_Extend, RenewalAccreditationPeriodViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class DocumentaryCreditsProfile : Profile
        {
            public DocumentaryCreditsProfile()
            {
                CreateMap<InputDocumentaryCredits, Documentary_of_Guarantee_Used>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Documentary_of_Guarantee_Used, DocumentaryCreditsViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }


        public class DocumentaryCreditLimitsProfile : Profile
        {
            public DocumentaryCreditLimitsProfile()
            {
                CreateMap<InputDocumentaryCreditLimits, Documentary_of_Guarantee>();

                CreateMap<Documentary_of_Guarantee, DocumentaryCreditLimitsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class DocumentaryCreditsTypeProfile : Profile
        {
            public DocumentaryCreditsTypeProfile()
            {
                CreateMap<InputDocumentaryCreditsType, Documentary>();

                CreateMap<Documentary, DocumentaryCreditsTypeViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class ChangingGuaranteeValueProfile : Profile
        {
            public ChangingGuaranteeValueProfile()
            {
                CreateMap<InputChangingGuaranteeValue, Letter_ChangeValue>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Letter_ChangeValue, ChangingGuaranteeValueViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class LettersOfGuaranteeBalanceProfile : Profile
        {
            public LettersOfGuaranteeBalanceProfile()
            {
                CreateMap<InputLettersOfGuaranteeBalance, Letter_of_Extend>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Letter_of_Extend, LettersOfGuaranteeBalanceViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class LettersOfGuaranteeUsedProfile : Profile
        {
            public LettersOfGuaranteeUsedProfile()
            {
                CreateMap<InputLettersOfGuaranteeUsed, Letter_of_Guarantee_Used>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Letter_of_Guarantee_Used, LettersOfGuaranteeUsedViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class LettersOfGuaranteeLimitationsProfile : Profile
        {
            public LettersOfGuaranteeLimitationsProfile()
            {
                CreateMap<InputLettersOfGuaranteeLimitations, Letter_of_Guarantee>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<Letter_of_Guarantee, LettersOfGuaranteeLimitationsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class LettersOfGuaranteeTypesProfile : Profile
        {
            public LettersOfGuaranteeTypesProfile()
            {
                CreateMap<InputLettersOfGuaranteeTypes, collateral>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<collateral, LettersOfGuaranteeTypesViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class NotesPayableProfile : Profile
        {
            public NotesPayableProfile()
            {
                CreateMap<InputNotesPayable, chek_book_come>();

                CreateMap<chek_book_come, NotesPayableViewModel>();

            }
        }

        public class NotesReceivableProfile : Profile
        {
            public NotesReceivableProfile()
            {
                CreateMap<InputNotesReceivable, chek_book_Out>();

                CreateMap<chek_book_Out, NotesReceivableViewModel>();

            }
        }


        public class ChequesBookProfile : Profile
        {
            public ChequesBookProfile()
            {
                CreateMap<InputChequesBook, Chek_Book>()
                    .ForMember(M => M.IDBookChek, op => op.MapFrom(e => e.id));
                CreateMap<Chek_Book, ChequesBookViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.IDBookChek));
            }
        }

        public class NoticesProfile : Profile
        {
            public NoticesProfile()
            {
                CreateMap<InputNotices, Notification>()
                    .ForMember(M => M.Id, op => op.MapFrom(e => e.id));
                CreateMap<Notification, NoticesViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.Id));
            }
        }

        public class CustodysettlementProfile : Profile
        {
            public CustodysettlementProfile()
            {
                CreateMap<InputCustodysettlement, TaswyaMonyEmpHead>()
                    .ForMember(M => M.TaswyaID, op => op.MapFrom(e => e.id));
                CreateMap<TaswyaMonyEmpHead, CustodysettlementViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.TaswyaID));
            }
        }

        public class JournalEntriesProfile : Profile
        {
            public JournalEntriesProfile()
            {
                CreateMap<InputJournalEntries, El_Koyod_El_Yawmia>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<El_Koyod_El_Yawmia, JournalEntriesViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class BankReceiptsProfile : Profile
        {
            public BankReceiptsProfile()
            {
                CreateMap<InputBankReceipts, Tree_Makbodat_Bank>()
                    .ForMember(M => M.TaswyaID, op => op.MapFrom(e => e.id));
                CreateMap<Tree_Makbodat_Bank, BankReceiptsViewModel>()
              .ForMember(M => M.id, op => op.MapFrom(e => e.TaswyaID));
            }
        }

        public class BankPaymentsProfile : Profile
        {
            public BankPaymentsProfile()
            {
                CreateMap<InputBankPayments, Tree_Masroft_Bank>()
                   .ForMember(M => M.TaswyaID, op => op.MapFrom(e => e.id));
                CreateMap<Tree_Masroft_Bank, BankPaymentsViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.TaswyaID));
            }
        }

        public class CashReceiptsProfile : Profile
        {
            public CashReceiptsProfile()
            {
                CreateMap<InputCashReceipts, Tree_Makbodat_box>()
                    .ForMember(M => M.TaswyaID, op => op.MapFrom(e => e.id));
                CreateMap<Tree_Makbodat_box, CashReceiptsViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.TaswyaID));
            }
        }

        public class CashPaymentsProfile : Profile
        {
            public CashPaymentsProfile()
            {
                CreateMap<InputCashPayments, Tree_Masroft_box>()
                    .ForMember(M => M.TaswyaID, op => op.MapFrom(e => e.id));
                CreateMap<Tree_Masroft_box, CashPaymentsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.TaswyaID));
            }
        }
        public class JournalForAuditProfile : Profile
        {
            public JournalForAuditProfile()
            {
                CreateMap<InputJournalForAudit, El_Koyod_El_Yawmia_Request>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<El_Koyod_El_Yawmia_Request, JournalForAuditViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }

        public class ExchangeRequestProfile : Profile
        {
            public ExchangeRequestProfile()
            {
                CreateMap<InputExchangeRequest, Request_Currency>()
                    .ForMember(M => M.Request_CurrencyID, op => op.MapFrom(e => (long)e.id));
                CreateMap<Request_Currency, ExchangeRequestViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => (int)e.Request_CurrencyID));
            }
        }

        public class TaxesProfile : Profile
        {
            public TaxesProfile()
            {
                CreateMap<InputTaxes, Tax>()
                 .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
                 .ForMember(M => M.NameAR, op => op.MapFrom(e => e.NameAr))
                 .ForMember(M => M.NameEN, op => op.MapFrom(e => e.NameEn))
                 .ForMember(M => M.TaxGruop_ID, op => op.MapFrom(e => e.GruopId))
                 .ForMember(M => M.CodeID, op => op.MapFrom(e => e.Code));


                CreateMap<Tax, TaxesViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                 .ForMember(M => M.NameAr, op => op.MapFrom(e => e.NameAR))
                 .ForMember(M => M.NameEn, op => op.MapFrom(e => e.NameEN))
                 .ForMember(M => M.GruopId, op => op.MapFrom(e => e.TaxGruop_ID))
                 .ForMember(M => M.Code, op => op.MapFrom(e => e.CodeID));
            }
        }

        public class TaxGroupProfile : Profile
        {
            public TaxGroupProfile()
            {
                CreateMap<InputTaxGroup, TaxGroup>()
               .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
               .ForMember(M => M.NameAR, op => op.MapFrom(e => e.Name));
                CreateMap<TaxGroup, TaxGroupViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
               .ForMember(M => M.Name, op => op.MapFrom(e => e.NameAR));
            }
        }


        public class PartnersProfile : Profile
        {
            public PartnersProfile()
            {
                CreateMap<InputPartners, Partners>()
                .ForMember(M => M.Id, op => op.MapFrom(e => e.id))
                .ForMember(M => M.PartnerName, op => op.MapFrom(e => e.Name));

                CreateMap<Partners, PartnersViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.Id))
                .ForMember(M => M.Name, op => op.MapFrom(e => e.PartnerName));
            }
        }

        public class CostTypesProfile : Profile
        {
            public CostTypesProfile()
            {
                CreateMap<InputCostTypes, CostTypeName>()
                .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
                .ForMember(M => M.CostTypeName_AR, op => op.MapFrom(e => e.NameAr))
                .ForMember(M => M.CostTypeName_EN, op => op.MapFrom(e => e.NameEn)); ;

                CreateMap<CostTypeName, CostTypesViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                .ForMember(M => M.NameAr, op => op.MapFrom(e => e.CostTypeName_AR))
                .ForMember(M => M.NameEn, op => op.MapFrom(e => e.CostTypeName_EN));
            }
        }



        public class EstimatedBudgetProfile : Profile
        {
            public EstimatedBudgetProfile()
            {
                CreateMap<InputEstimatedBudget, AccountBudget>()
                    .ForMember(M => M.AccountID, op => op.MapFrom(e => e.id));
                CreateMap<AccountBudget, EstimatedBudgetViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.AccountID));
            }
        }


        public class AccountsRoutingProfile : Profile
        {
            public AccountsRoutingProfile()
            {
                CreateMap<InputAccountsRouting, keadSitting>()
                    .ForMember(M => M.AccID, op => op.MapFrom(e => e.id));
                CreateMap<keadSitting, AccountsRoutingViewModel>()
             .ForMember(M => M.id, op => op.MapFrom(e => e.AccID));
            }
        }

        public class FinancialAnalysisPreparationProfile : Profile
        {
            public FinancialAnalysisPreparationProfile()
            {
                CreateMap<InputFinancialAnalysisPreparation, Final_Ratio_Setting>()
                    .ForMember(M => M.FinalMenuID, op => op.MapFrom(e => e.id));
                CreateMap<Final_Ratio_Setting, FinancialAnalysisPreparationViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.FinalMenuID));
            }
        }

        public class ClosingPeriodProfile : Profile
        {
            public ClosingPeriodProfile()
            {
                CreateMap<InputClosingPeriod, Account_Lokd>()
                .ForMember(M => M.TimeID, op => op.MapFrom(e => e.id));
                CreateMap<Account_Lokd, ClosingPeriodViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.TimeID));
            }
        }

        public class FinancialYearModelProfile : Profile
        {
            public FinancialYearModelProfile()
            {
                CreateMap<FinancialYearModel, FinancialYear>();
                CreateMap<FinancialYear, FinancialYearModel>();
            }
        }

        public class JournalProfile : Profile
        {
            public JournalProfile()
            {
                CreateMap<InputJournal, OstazCode>()
                    .ForMember(M => M.OstazID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.OstazName, op => op.MapFrom(e => e.Name));
                CreateMap<OstazCode, JournalViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.OstazID))
                    .ForMember(M => M.Name, op => op.MapFrom(e => e.OstazName));

            }
        }
        public class AnalyticAccountsProfile : Profile
        {




            //public int? ParentID { get; set; }

            //public double? Latitude { get; set; }
            //public double? Longitude { get; set; }

            public AnalyticAccountsProfile()
            {
                CreateMap<InputAnalyticAccounts, TblCostTree>().ReverseMap()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.AccName))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.AccName_EN))
                    .ForMember(M => M.Code, op => op.MapFrom(e => e.AccCode));
                CreateMap<TblCostTree, AnalyticAccountsViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
               .ForMember(M => M.NameAr, op => op.MapFrom(e => e.AccName))
               .ForMember(M => M.NameEn, op => op.MapFrom(e => e.AccName_EN))
               .ForMember(M => M.Code, op => op.MapFrom(e => e.AccCode));
                CreateMap<TblCostTree, AnalyticAccountsViewList>()
              .ForMember(M => M.AnalyticAccountId, op => op.MapFrom(e => e.AccCode))
              .ForMember(M => M.AnalyticAccountName, op => op.MapFrom(e => e.AccName));

            }
        }

        public class CashBoxProfile : Profile
        {
            public CashBoxProfile()
            {
                CreateMap<InputCashBox, Box>().ReverseMap()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.BoxID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.BoxName));
                CreateMap<Box, CashBoxViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.BoxID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.BoxName));


            }
        }

        public class BanksProfile : Profile
        {
            public BanksProfile()
            {
                CreateMap<InputBanks, Bank_Code>()
                    .ForMember(M => M.id_Bank, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.Bank_Name, op => op.MapFrom(e => e.Name));
                CreateMap<Bank_Code, BanksViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.id_Bank))
                    .ForMember(M => M.Name, op => op.MapFrom(e => e.Bank_Name));


            }
        }



        public class BankAccountNameProfile : Profile
        {
            public BankAccountNameProfile()
            {
                CreateMap<InputBankAccount, BankAccountName>()
                     .ForMember(M => M.AccountID, op => op.MapFrom(e => e.id))
                     .ForMember(M => M.id_Bank, op => op.MapFrom(e => e.BankId))
                     .ForMember(M => M.AccountName, op => op.MapFrom(e => e.NameAr))
                     .ForMember(M => M.Bank_Location, op => op.MapFrom(e => e.Location))
                     .ForMember(M => M.AccountNum, op => op.MapFrom(e => e.AccountNumber))
                     .ForMember(M => M.COMP_ID, op => op.MapFrom(e => e.CompanyId))
                     .ForMember(M => M.ProunchID, op => op.MapFrom(e => e.BranchId));
                CreateMap<BankAccountName, BankAccountViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.AccountID))
                    .ForMember(M => M.BankId, op => op.MapFrom(e => e.id_Bank))
                     .ForMember(M => M.NameAr, op => op.MapFrom(e => e.AccountName))
                     .ForMember(M => M.Location, op => op.MapFrom(e => e.Bank_Location))
                     .ForMember(M => M.AccountNumber, op => op.MapFrom(e => e.AccountNum))
                     .ForMember(M => M.CompanyId, op => op.MapFrom(e => e.COMP_ID))
                     .ForMember(M => M.BranchId, op => op.MapFrom(e => e.ProunchID));


            }
        }

        public class ChartofAccountProfile : Profile
        {
            public ChartofAccountProfile()
            {
                CreateMap<inputChartofAccounts, tblAccTree>().ReverseMap()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.AccName))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.AccName_En))
                    .ForMember(M => M.Code, op => op.MapFrom(e => e.AccCode));
                CreateMap<tblAccTree, ChartofAccountsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.AccName))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.AccName_En))
                    .ForMember(M => M.Code, op => op.MapFrom(e => e.AccCode));


            }
        }



        public class FinalAccountsProfile : Profile
        {
            public FinalAccountsProfile()
            {
                CreateMap<InputFinalAccounts, Mezanya>()
                .ForMember(M => M.MezanyaID, op => op.MapFrom(e => e.id))
                .ForMember(M => M.Mezanya_Name, op => op.MapFrom(e => e.NameAr))
                .ForMember(M => M.Mezanya_Name_En, op => op.MapFrom(e => e.NameEn));
                CreateMap<Mezanya, FinalAccountsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.MezanyaID))
                .ForMember(M => M.NameAr, op => op.MapFrom(e => e.Mezanya_Name))
                .ForMember(M => M.NameEn, op => op.MapFrom(e => e.Mezanya_Name_En));


            }
        }



        public class FinalBalanceTypesProfile : Profile
        {
            public FinalBalanceTypesProfile()
            {
                CreateMap<InputFinalBalanceTypes, FinalBalanceTypes>()
                .ForMember(M => M.ClassificationId, op => op.MapFrom(e => e.id))
                .ForMember(M => M.ClassificationName_Ar, op => op.MapFrom(e => e.NameAr))
                .ForMember(M => M.ClassificationName, op => op.MapFrom(e => e.NameEn));
                CreateMap<FinalBalanceTypes, FinalBalanceTypesViewModel>()
               .ForMember(M => M.id, op => op.MapFrom(e => e.ClassificationId))
                .ForMember(M => M.NameAr, op => op.MapFrom(e => e.ClassificationName_Ar))
                .ForMember(M => M.NameEn, op => op.MapFrom(e => e.ClassificationName));


            }
        }




        //CRM

        public class EmailInstanceProfile : Profile
        {
            public EmailInstanceProfile()
            {
                CreateMap<InputEmailInstance, EmailSetting>();

                CreateMap<EmailSetting, EmailInstanceViewModel>();

            }
        }

        public class EmailTemplateProfile : Profile
        {
            public EmailTemplateProfile()
            {
                CreateMap<InputEmailTemplate, EmailTemplate>();

                CreateMap<EmailTemplate, EmailTemplateViewModel>();

            }
        }

        public class SMSTemplateProfile : Profile
        {
            public SMSTemplateProfile()
            {
                CreateMap<InputSMSTemplate, SMSTemplate>();

                CreateMap<SMSTemplate, SMSTemplateViewModel>();

            }
        }
        public class SMSInstanceProfile : Profile
        {
            public SMSInstanceProfile()
            {
                CreateMap<InputSMSInstance, Sms_Settings>();

                CreateMap<Sms_Settings, SMSInstanceViewModel>();

            }
        }

        public class WhatsappInstanceProfile : Profile
        {
            public WhatsappInstanceProfile()
            {
                CreateMap<InputWhatsappInstance, WhatsappSetting>()
                .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                CreateMap<WhatsappSetting, WhatsappInstanceViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
            }
        }


        public class CustomerSurveyProfile : Profile
        {
            public CustomerSurveyProfile()
            {
                CreateMap<InputCustomerSurvey, customer_survey>()
                  .ForMember(M => M.suppliers_id, op => op.MapFrom(e => e.id));

                CreateMap<customer_survey, CustomerSurveyViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.suppliers_id));
            }
        }

        public class CustomerRequestsProfile : Profile
        {
            public CustomerRequestsProfile()
            {
                CreateMap<InputCustomerRequests, customers_requirement>()
                 .ForMember(M => M.requierid, op => op.MapFrom(e => e.id));

                CreateMap<customers_requirement, CustomerRequestsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.requierid));
            }
        }

        public class complaintsProfile : Profile
        {
            public complaintsProfile()
            {
                CreateMap<Inputcomplaints, customer_compaint>();

                CreateMap<customer_compaint, complaintsViewModel>();
            }
        }

        public class LeadsProfile : Profile
        {
            public LeadsProfile()
            {
                CreateMap<InputLeads, Company>()
                 .ForMember(M => M.CompanyID, op => op.MapFrom(e => e.id))
                 .ForMember(M => M.CompanyNamEn, op => op.MapFrom(e => e.NameEn))
                 .ForMember(M => M.CompanyName, op => op.MapFrom(e => e.NameAr));

                CreateMap<Company, LeadsViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.CompanyID))
                 .ForMember(M => M.NameEn, op => op.MapFrom(e => e.CompanyNamEn))
                 .ForMember(M => M.NameAr, op => op.MapFrom(e => e.CompanyName));
            }
        }

        public class StagesProfile : Profile
        {
            public StagesProfile()
            {
                CreateMap<InputStages, Stages>();

                CreateMap<Stages, StagesViewModel>();

            }
            public class CRMProfile : Profile
            {
                public CRMProfile()
                {
                    CreateMap<InputCRM, CustomerFallow>()
                     .ForMember(M => M.ID, op => op.MapFrom(e => e.id));

                    CreateMap<CustomerFallow, CRMViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }


            public class SectorsProfile : Profile
            {
                public SectorsProfile()
                {
                    CreateMap<InputSectors, Sector>()
                     .ForMember(M => M.SectorID, op => op.MapFrom(e => e.id));

                    CreateMap<Sector, SectorsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.SectorID))
                    .ForMember(M => M.Name, op => op.MapFrom(e => e.SectorName));
                }
            }

            public class CommercialActivitiesProfile : Profile
            {
                public CommercialActivitiesProfile()
                {
                    CreateMap<InputCommercialActivities, Rooms>()
                         .ForMember(M => M.RoomId, op => op.MapFrom(e => e.id))
                          .ForMember(M => M.RoomName, op => op.MapFrom(e => e.name));

                    CreateMap<Rooms, CommercialActivitiesViewModel>()
                         .ForMember(M => M.id, op => op.MapFrom(e => e.RoomId))
                          .ForMember(M => M.name, op => op.MapFrom(e => e.RoomName));
                }
            }
            public class CustomerCategoriesProfile : Profile
            {
                public CustomerCategoriesProfile()
                {
                    CreateMap<InputCustomerCategories, CompanyCategories>()
                        .ForMember(M => M.CategoryID, op => op.MapFrom(e => e.id))
                          .ForMember(M => M.CategoryName, op => op.MapFrom(e => e.name));

                    CreateMap<CompanyCategories, CustomerCategoriesViewModel>()
                          .ForMember(M => M.id, op => op.MapFrom(e => e.CategoryID))
                          .ForMember(M => M.name, op => op.MapFrom(e => e.CategoryName));
                }
            }





            //Fleet
            public class EquipmentProfile : Profile
            {
                public EquipmentProfile()
                {
                    CreateMap<InputEquipment, machinelist>().ReverseMap();

                    CreateMap<machinelist, EquipmentViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.machiinedi));
                }
            }


            //General


            public class DepartmentsProfile : Profile
            {
                public DepartmentsProfile()
                {
                    CreateMap<InputDepartments, departments>()
                     .ForMember(M => M.depart_id, op => op.MapFrom(e => e.id))
                     .ForMember(M => M.depart_name, op => op.MapFrom(e => e.NameAr))
                     .ForMember(M => M.EnglishName, op => op.MapFrom(e => e.NameEn));
                    CreateMap<departments, DepartmentsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.depart_id))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.depart_name))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnglishName));
                }
            }

            public class CityesProfile : Profile
            {
                public CityesProfile()
                {
                    CreateMap<InputCityes, City>()
                        .ForMember(M => M.CityID, op => op.MapFrom(e => e.id));
                    CreateMap<City, CityesViewModel>()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.CityID));
                }
            }
            public class BranchProfile : Profile
            {
                public BranchProfile()
                {
                    CreateMap<InputBranch, Branches>().ReverseMap()
                        .ForMember(b => b.id, op => op.MapFrom(e => e.Id))
                        .ForMember(b => b.Name, op => op.MapFrom(e => e.Purnsh_name));
                    CreateMap<Branches, BranchViewModel>()
                        .ForMember(b => b.id, op => op.MapFrom(e => e.Id))
                        .ForMember(b => b.Name, op => op.MapFrom(e => e.Purnsh_name));

                }
            }
            public class FalconUsersProfile : Profile
            {
                public FalconUsersProfile()
                {
                    CreateMap<InputFalconUsers, FalconUsers>().ReverseMap()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.UserID))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.UserName));
                    CreateMap<FalconUsers, FalconUsersViewModel>()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.UserID))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.UserName));

                }
            }

            public class CountryProfile : Profile
            {
                public CountryProfile()
                {
                    CreateMap<Country, CountryViewModel>()
                   .ForMember(M => M.Id, op => op.MapFrom(e => e.CountryID));

                }

            }

            public class CurauneslProfile : Profile
            {
                public CurauneslProfile()
                {

                    CreateMap<CurrencyViewModel, Curaunes>()
                        .ForMember(M => M.CurID, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.CurName, op => op.MapFrom(e => e.NameAr))
                        .ForMember(M => M.CuraunesEN, op => op.MapFrom(e => e.NameEn));

                    CreateMap<Curaunes, CurrencyViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.CurID))
                        .ForMember(M => M.NameAr, op => op.MapFrom(e => e.CurName))
                        .ForMember(M => M.NameEn, op => op.MapFrom(e => e.CuraunesEN));
                }


            }


            //HR


            public class HRFormsProfile : Profile
            {
                public HRFormsProfile()
                {
                    CreateMap<InputHRForms, Employee>().ReverseMap();

                    CreateMap<Employee, HRFormsViewModel>();

                }
            }

            public class ManualAttendanceProfile : Profile
            {
                public ManualAttendanceProfile()
                {
                    CreateMap<InputManualAttendance, Emp_Comming>().ReverseMap();

                    CreateMap<Emp_Comming, ManualAttendanceViewModel>()
          .ForMember(M => M.id, op => op.MapFrom(e => e.employeeId));
                }
            }

            public class EntitlementsProfile : Profile
            {
                public EntitlementsProfile()
                {
                    CreateMap<InputEntitlements, Due_Vection>().ReverseMap();

                    CreateMap<Due_Vection, EntitlementsViewModel>()
         .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class ScheduleAdvancesProfile : Profile
            {
                public ScheduleAdvancesProfile()
                {
                    CreateMap<InputScheduleAdvances, solafaccount>();

                    CreateMap<solafaccount, ScheduleAdvancesViewModel>();

                }
            }

            public class StaffCostsProfile : Profile
            {
                public StaffCostsProfile()
                {
                    CreateMap<InputStaffCosts, EmpCosts>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<EmpCosts, StaffCostsViewModel>()
                .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class HandwrittenNoteProfile : Profile
            {
                public HandwrittenNoteProfile()
                {
                    CreateMap<InputHandwrittenNote, Emp_memo>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Emp_memo, HandwrittenNoteViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class HousingAllowancePaymentsProfile : Profile
            {
                public HousingAllowancePaymentsProfile()
                {
                    CreateMap<InputHousingAllowancePayments, Contract_Home_Allaw>()
                          .ForMember(M => M.Emp_Code, op => op.MapFrom(e => e.id));
                    CreateMap<Contract_Home_Allaw, HousingAllowancePaymentsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.Emp_Code));
                }
            }

            public class EmployeeRequestsProfile : Profile
            {
                public EmployeeRequestsProfile()
                {
                    CreateMap<InputEmployeeRequests, Emp_Requests>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Emp_Requests, EmployeeRequestsViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class SalaryUpdateProfile : Profile
            {
                public SalaryUpdateProfile()
                {
                    CreateMap<InputSalaryUpdate, UpDate_Salary>()
                          .ForMember(M => M.Emp_Code, op => op.MapFrom(e => e.id));
                    CreateMap<UpDate_Salary, SalaryUpdateViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.Emp_Code));
                }
            }

            public class StaffTransferBetweenProjectsProfile : Profile
            {
                public StaffTransferBetweenProjectsProfile()
                {
                    CreateMap<InputStaffTransferBetweenProjects, Transfer_Emp>()
                          .ForMember(M => M.TransferID, op => op.MapFrom(e => e.id));
                    CreateMap<Transfer_Emp, StaffTransferBetweenProjectsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.TransferID));
                }
            }


            public class VisaProfile : Profile
            {
                public VisaProfile()
                {
                    CreateMap<InputVisa, Visa>()
                          .ForMember(M => M.Visa_code, op => op.MapFrom(e => e.id));
                    CreateMap<Visa, VisaViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.Visa_code));
                }
            }
            public class VisitorsRecordProfile : Profile
            {
                public VisitorsRecordProfile()
                {
                    CreateMap<InputVisitorsRecord, Visitors>()
                          .ForMember(M => M.VisitID, op => op.MapFrom(e => e.id));
                    CreateMap<Visitors, VisitorsRecordViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.VisitID));
                }
            }

            public class CarTrafficProfile : Profile
            {
                public CarTrafficProfile()
                {
                    CreateMap<InputCarTraffic, Cartraffic>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Cartraffic, CarTrafficViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TruckingProfile : Profile
            {
                public TruckingProfile()
                {
                    CreateMap<InputTrucking, Charge_Items>()
                          .ForMember(M => M.ChargeID, op => op.MapFrom(e => e.id));
                    CreateMap<Charge_Items, TruckingViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.ChargeID));
                }
            }


            public class TrainingEntitiesProfile : Profile
            {
                public TrainingEntitiesProfile()
                {
                    CreateMap<InputTrainingEntities, trainer>()
                          .ForMember(M => M.trid, op => op.MapFrom(e => e.id));
                    CreateMap<trainer, TrainingEntitiesViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.trid));
                }
            }


            public class EvaluateTrainingEffectivenessProfile : Profile
            {
                public EvaluateTrainingEffectivenessProfile()
                {
                    CreateMap<InputEvaluateTrainingEffectiveness, trainingtakyeem>()
                          .ForMember(M => M.emp_id, op => op.MapFrom(e => e.id));
                    CreateMap<trainingtakyeem, EvaluateTrainingEffectivenessViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.emp_id));
                }
            }

            public class EmployeeTrainingFormProfile : Profile
            {
                public EmployeeTrainingFormProfile()
                {
                    CreateMap<InputEmployeeTrainingForm, trenerform>()
                          .ForMember(M => M.tfid, op => op.MapFrom(e => e.id));
                    CreateMap<trenerform, EmployeeTrainingFormViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.tfid));
                }
            }


            public class AnnualTrainingPlanProfile : Profile
            {
                public AnnualTrainingPlanProfile()
                {
                    CreateMap<InputAnnualTrainingPlan, trainingplanofyear>()
                          .ForMember(M => M.tyid, op => op.MapFrom(e => e.id));
                    CreateMap<trainingplanofyear, AnnualTrainingPlanViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.tyid));
                }
            }


            public class TrainingNeedsProfile : Profile
            {
                public TrainingNeedsProfile()
                {
                    CreateMap<InputTrainingNeeds, trainingneed>()
                          .ForMember(M => M.tnid, op => op.MapFrom(e => e.id));
                    CreateMap<trainingneed, TrainingNeedsViewModel>()
                       .ForMember(M => M.id, op => op.MapFrom(e => e.tnid));
                }
            }

            public class MonthlyEvaluationProfile : Profile
            {
                public MonthlyEvaluationProfile()
                {
                    CreateMap<InputMonthlyEvaluation, EvaluationMonthly>();

                    CreateMap<EvaluationMonthly, MonthlyEvaluationViewModel>();

                }
            }

            public class EmployeeEvaluationProfile : Profile
            {
                public EmployeeEvaluationProfile()
                {
                    CreateMap<InputEmployeeEvaluation, oldemp_takyeem>()
                    .ForMember(M => M.emp_id, op => op.MapFrom(e => e.id));
                    CreateMap<oldemp_takyeem, EmployeeEvaluationViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.emp_id));
                }
            }

            public class NewEmployeeEvaluationProfile : Profile
            {
                public NewEmployeeEvaluationProfile()
                {
                    CreateMap<InputNewEmployeeEvaluation, newemp_takyeem>()
                          .ForMember(M => M.emp_id, op => op.MapFrom(e => e.id));
                    CreateMap<newemp_takyeem, NewEmployeeEvaluationViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.emp_id));
                }
            }
            public class ContractsProfile : Profile
            {
                public ContractsProfile()
                {
                    CreateMap<InputContracts, Form_Emp_Contract>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_Emp_Contract, ContractsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class MedicalInspectionLetterProfile : Profile
            {
                public MedicalInspectionLetterProfile()
                {
                    CreateMap<InputMedicalInspectionLetter, ArdAlaLagnaTbya>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<ArdAlaLagnaTbya, MedicalInspectionLetterViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class WarningProfile : Profile
            {
                public WarningProfile()
                {
                    CreateMap<InputWarning, EnzarMozaf>()
                       .ForMember(M => M.EnzarID, op => op.MapFrom(e => e.id));
                    CreateMap<EnzarMozaf, WarningViewModel>()
                      .ForMember(M => M.id, op => op.MapFrom(e => e.EnzarID));
                }
            }


            public class ResignationProfile : Profile
            {
                public ResignationProfile()
                {
                    CreateMap<InputResignation, Estekala>()
                     .ForMember(M => M.EID, op => op.MapFrom(e => e.id));
                    CreateMap<Estekala, ResignationViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.EID));
                }
            }

            public class CertificateProfile : Profile
            {
                public CertificateProfile()
                {
                    CreateMap<InputCertificate, Experince>()
                          .ForMember(M => M.ExpID, op => op.MapFrom(e => e.Id));
                    CreateMap<Experince, CertificateViewModel>()
                  .ForMember(M => M.Id, op => op.MapFrom(e => e.ExpID));
                }
            }

            public class HealthInsuranceProfile : Profile
            {
                public HealthInsuranceProfile()
                {
                    CreateMap<InputHealthInsurance, TransToTameenSehy>()
                          .ForMember(M => M.HID, op => op.MapFrom(e => e.id));
                    CreateMap<TransToTameenSehy, HealthInsuranceViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.HID));
                }
            }

            public class CustodyReceiptProfile : Profile
            {
                public CustodyReceiptProfile()
                {
                    CreateMap<InputCustodyReceipt, EmpForm_Extradition>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<EmpForm_Extradition, CustodyReceiptViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }


            public class AppointmentLetterProfile : Profile
            {
                public AppointmentLetterProfile()
                {
                    CreateMap<InputAppointmentLetter, Form_NoticeAppointment>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_NoticeAppointment, AppointmentLetterViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class AttentionProfile : Profile
            {
                public AttentionProfile()
                {
                    CreateMap<InputAttention, FormDrow_Attention>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<FormDrow_Attention, AttentionViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class AdministrativeDecisionProfile : Profile
            {
                public AdministrativeDecisionProfile()
                {
                    CreateMap<InputAdministrativeDecision, AdminDecision>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<AdminDecision, AdministrativeDecisionViewModel>()
                      .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TicketBookingProfile : Profile
            {
                public TicketBookingProfile()
                {
                    CreateMap<InputTicketBooking, Form_Request>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_Request, TicketBookingViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class LegalIssuesProfile : Profile
            {
                public LegalIssuesProfile()
                {
                    CreateMap<InputLegalIssues, Form_Accountability>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.Id));
                    CreateMap<Form_Accountability, LegalIssuesViewModel>()
                   .ForMember(M => M.Id, op => op.MapFrom(e => e.ID));
                }
            }

            public class AssignmentProfile : Profile
            {
                public AssignmentProfile()
                {
                    CreateMap<InputAssignment, Form_Commission>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_Commission, AssignmentViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class SecondmentContractProfile : Profile
            {
                public SecondmentContractProfile()
                {
                    CreateMap<InputSecondmentContract, Form_LoanContract>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_LoanContract, SecondmentContractViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class ReceiptTransactionsProfile : Profile
            {
                public ReceiptTransactionsProfile()
                {
                    CreateMap<InputReceiptTransactions, Form_Receipt>()
                          .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Form_Receipt, ReceiptTransactionsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class SalaryDeductionProfile : Profile
            {
                public SalaryDeductionProfile()
                {
                    CreateMap<InputSalaryDeduction, Cutting>()
                          .ForMember(M => M.cut_id, op => op.MapFrom(e => e.id));
                    CreateMap<Cutting, SalaryDeductionViewModel>()
                         .ForMember(M => M.id, op => op.MapFrom(e => e.cut_id));
                }
            }

            public class SalaryAdditionsProfile : Profile
            {
                public SalaryAdditionsProfile()
                {
                    CreateMap<InputSalaryAdditions, Hafez>()
                         .ForMember(M => M.plassid, op => op.MapFrom(e => e.id));
                    CreateMap<Hafez, SalaryAdditionsViewModel>()
                         .ForMember(M => M.id, op => op.MapFrom(e => e.plassid));
                }
            }

            public class Benefit_DeductionApplicationProfile : Profile
            {
                public Benefit_DeductionApplicationProfile()
                {
                    CreateMap<InputBenefit_DeductionApplication, salary_plass>()
                        .ForMember(M => M.plassid, op => op.MapFrom(e => e.id));
                    CreateMap<salary_plass, Benefit_DeductionApplicationViewModel>()
                          .ForMember(M => M.id, op => op.MapFrom(e => e.plassid));


                }
            }

            public class LatePermissionsProfile : Profile
            {
                public LatePermissionsProfile()
                {
                    CreateMap<InputLatePermissions, emp_ezn>()
                        .ForMember(M => M.emp_id, op => op.MapFrom(e => e.id));
                    CreateMap<emp_ezn, LatePermissionsViewModel>()
                          .ForMember(M => M.id, op => op.MapFrom(e => e.emp_id));
                }
            }

            public class VacationProfile : Profile
            {
                public VacationProfile()
                {
                    CreateMap<InputVacation, Emp_Vacation>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Emp_Vacation, VacationViewModel>()
                         .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class OverTimeProfile : Profile
            {
                public OverTimeProfile()
                {
                    CreateMap<InputOverTime, emp_edafy>()
                        .ForMember(M => M.emp_id, op => op.MapFrom(e => e.Id));
                    CreateMap<emp_edafy, OverTimeViewModel>()
                         .ForMember(M => M.Id, op => op.MapFrom(e => e.emp_id));
                }
            }


            public class AbsencePermissionsProfile : Profile
            {
                public AbsencePermissionsProfile()
                {
                    CreateMap<InputAbsencePermissions, emp_ghyab_bezn>()
                        .ForMember(M => M.Emp_Code, op => op.MapFrom(e => e.Id));
                    CreateMap<emp_ghyab_bezn, AbsencePermissionsViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.Emp_Code));
                }
            }


            public class MissionsProfile : Profile
            {
                public MissionsProfile()
                {
                    CreateMap<InputMissions, Form_BusinessTravel>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.Id))
                    .ForMember(M => M.EmpName, op => op.MapFrom(e => e.name));
                    CreateMap<Form_BusinessTravel, MissionsViewModel>()
                     .ForMember(M => M.Id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.name, op => op.MapFrom(e => e.EmpName));

                }
            }


            public class WorkingHoursAndPenaltiesProfile : Profile
            {
                public WorkingHoursAndPenaltiesProfile()
                {
                    CreateMap<InputWorkingHoursAndPenalties, WorkPlanEdite>()
                        .ForMember(M => M.DayName, op => op.MapFrom(e => e.name));
                    CreateMap<WorkPlanEdite, WorkingHoursAndPenaltiesViewModel>()

                    .ForMember(M => M.name, op => op.MapFrom(e => e.DayName));
                }
            }


            public class DaysAndHoursProfile : Profile
            {
                public DaysAndHoursProfile()
                {
                    CreateMap<InputDaysAndHours, CountDayHour>()
                        .ForMember(M => M.planID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.worksystem, op => op.MapFrom(e => e.name));
                    CreateMap<CountDayHour, DaysAndHoursViewModel>()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.planID))
                    .ForMember(M => M.name, op => op.MapFrom(e => e.worksystem));
                }
            }


            public class ShiftPlansProfile : Profile
            {
                public ShiftPlansProfile()
                {
                    CreateMap<InputShiftPlans, Emp_Comming>()
                         .ForMember(M => M.employeeId, op => op.MapFrom(e => e.id));
                    CreateMap<Emp_Comming, ShiftPlansViewModel>()
                         .ForMember(M => M.Id, op => op.MapFrom(e => e.employeeId))
                         .ForMember(M => M.ArName, op => op.MapFrom(e => e.Employee.NameAr))
                         .ForMember(M => M.Date, op => op.MapFrom(e => e.emp_datecomming))
                         .ForMember(M => M.EnName, op => op.MapFrom(e => e.Employee.NameEn))
                         .ForMember(M => M.Dayname, op => op.MapFrom(e => e.dayname))
                         .ForMember(M => M.ShiftId, op => op.MapFrom(e => e.ShiftID))
                         .ForMember(M => M.ShiftName, op => op.MapFrom(e => e.Shifts.ShiftName))
                         .ForMember(M => M.planId, op => op.MapFrom(e => e.planid))
                         .ForMember(M => M.planName, op => op.MapFrom(e => e.Plan.PlanName));
                }
            }

            public class DependentsProfile : Profile
            {
                public DependentsProfile()
                {
                    CreateMap<InputDependents, Dependents>()
                             .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.Kinship_Name, op => op.MapFrom(e => e.name));
                    CreateMap<Dependents, DependentsViewModel>()
                         .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.name, op => op.MapFrom(e => e.Kinship_Name));
                }
            }

            public class QualificationsProfile : Profile
            {
                public QualificationsProfile()
                {
                    CreateMap<InputQualifications, Qualification>()
                    .ForMember(M => M.QualificationID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.Qualification_Name, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn));

                    CreateMap<Qualification, QualificationsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.QualificationID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.Qualification_Name))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName))
                    ;
                }
            }


            public class SponsorsProfile : Profile
            {
                public SponsorsProfile()
                {
                    CreateMap<InputSponsors, Kafeel>()
                    .ForMember(M => M.KafeelID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.KafeelName, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.KafeelPhone, op => op.MapFrom(e => e.Phone))
                    .ForMember(M => M.KafeelTypeID, op => op.MapFrom(e => e.TypeName));
                    CreateMap<Kafeel, SponsorsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.KafeelID))
                   .ForMember(M => M.NameAr, op => op.MapFrom(e => e.KafeelName))
                   .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName))
                   .ForMember(M => M.Phone, op => op.MapFrom(e => e.KafeelPhone))
                   .ForMember(M => M.TypeName, op => op.MapFrom(e => e.KafeelTypeID));
                }
            }
            public class DocumentsTypeProfile : Profile
            {
                public DocumentsTypeProfile()
                {
                    CreateMap<InputDocumentsType, WathaekType>()
                    .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.WName, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn));

                    CreateMap<WathaekType, DocumentsTypeViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.WName))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName));
                }
            }


            public class ShippingCompaniesProfile : Profile
            {
                public ShippingCompaniesProfile()
                {
                    CreateMap<InputShippingCompanies, CHarg_CO>()
                     .ForMember(M => M.Customer_id, op => op.MapFrom(e => e.id))
                     .ForMember(M => M.customer_name, op => op.MapFrom(e => e.Name))
                     .ForMember(M => M.address, op => op.MapFrom(e => e.Address))
                     .ForMember(M => M.phone, op => op.MapFrom(e => e.Phone))
                     .ForMember(M => M.mobile, op => op.MapFrom(e => e.Mobile))
                     .ForMember(M => M.site, op => op.MapFrom(e => e.Website))
                     .ForMember(M => M.scope, op => op.MapFrom(e => e.Scope))
                     .ForMember(M => M.ntes, op => op.MapFrom(e => e.Notes))
                     .ForMember(M => M.CityID, op => op.MapFrom(e => e.CityId))
                     .ForMember(M => M.COMP_ID, op => op.MapFrom(e => e.CompanyId))
                     .ForMember(M => M.ProunchID, op => op.MapFrom(e => e.BranchId))
                     ;
                    CreateMap<CHarg_CO, ShippingCompaniesViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.Customer_id))
                     .ForMember(M => M.Name, op => op.MapFrom(e => e.customer_name))
                     .ForMember(M => M.Address, op => op.MapFrom(e => e.address))
                     .ForMember(M => M.Phone, op => op.MapFrom(e => e.phone))
                     .ForMember(M => M.Mobile, op => op.MapFrom(e => e.mobile))
                     .ForMember(M => M.Website, op => op.MapFrom(e => e.site))
                     .ForMember(M => M.Scope, op => op.MapFrom(e => e.scope))
                     .ForMember(M => M.Notes, op => op.MapFrom(e => e.ntes))
                    ;
                }
            }


            public class EmployeesCategoriesProfile : Profile
            {
                public EmployeesCategoriesProfile()
                {
                    CreateMap<InputEmployeesCategories, workPlan>()
                    .ForMember(M => M.Planid, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.planName, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn));

                    CreateMap<workPlan, EmployeesCategoriesViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.Planid))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.planName))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName));

                }
            }


            public class JobDescriptionsProfile : Profile
            {
                public JobDescriptionsProfile()
                {
                    CreateMap<InputJobDescriptions, jobs>()
                    .ForMember(M => M.jobid, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.jname, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.supgm, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.respons, op => op.MapFrom(e => e.Responsibilities))
                    .ForMember(M => M.describe, op => op.MapFrom(e => e.Descriptions))
                    .ForMember(M => M.jtermes, op => op.MapFrom(e => e.Conditions));

                    CreateMap<jobs, JobDescriptionsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.jobid))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.jname))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.supgm));
                }
            }

            public class DeductionsTypesProfile : Profile
            {
                public DeductionsTypesProfile()
                {
                    CreateMap<InputDeductionsTypes, cut_code>()
                     .ForMember(M => M.cut_code_id, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.cut_code_name, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.COMP_ID, op => op.MapFrom(e => e.CompanyID))
                    .ForMember(M => M.ProunchID, op => op.MapFrom(e => e.BranchID))
                    .ForMember(M => M.CutDay, op => op.MapFrom(e => e.DeductionDays));

                    CreateMap<cut_code, DeductionsTypesViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.cut_code_id))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.cut_code_name))
                    .ForMember(M => M.DeductionDays, op => op.MapFrom(e => e.CutDay));


                }
            }




            public class AdditionsTypesProfile : Profile
            {
                public AdditionsTypesProfile()
                {
                    CreateMap<InputAdditionsTypes, elawat_code>()
                     .ForMember(M => M.elawat_code_id, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.elawat_code_name, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.COMP_ID, op => op.MapFrom(e => e.CompanyID))
                    .ForMember(M => M.ProunchID, op => op.MapFrom(e => e.BranchID));
                    CreateMap<elawat_code, AdditionsTypesViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.elawat_code_id))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.elawat_code_name));

                }
            }

            public class VacationsProfile : Profile
            {
                public VacationsProfile()
                {
                    CreateMap<InputVacations, vacation_code>()
                    .ForMember(M => M.vacation_id, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.vacation_name, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn))
                    .ForMember(M => M.COMP_ID, op => op.MapFrom(e => e.CompanyID))
                    .ForMember(M => M.ProunchID, op => op.MapFrom(e => e.BranchID));
                    CreateMap<vacation_code, VacationsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.vacation_id))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.vacation_name));
                }
            }

            public class EmployeeProfile : Profile
            {
                public EmployeeProfile()
                {
                    CreateMap<InputEmployee, Employee>();

                    CreateMap<Employee, EmployeeViewModel>()

                        .ForMember(emp => emp.DepartmentName, op => op.MapFrom(src => src.Department != null ? src.Department.depart_name : null))

                        .ForMember(emp => emp.BranchName, op => op.MapFrom(src => src.Branch != null ? src.Branch.Purnsh_name : null))

                        .ForMember(emp => emp.CompanyName, op => op.MapFrom(src => src.Company != null ? src.Company.Name : null))

                        .ForMember(emp => emp.ShiftName, op => op.MapFrom(src => src.Shift != null ? src.Shift.ShiftName : null))

                        .ForMember(emp => emp.job, op => op.MapFrom(src => src.Job != null ? src.Job.jname : null))

                        .ForMember(emp => emp.NationalityName, op => op.MapFrom(src => src.Nationality != null ? src.Nationality.Nationality_AR : null))
                        .ForMember(emp => emp.SectorName, op => op.MapFrom(src => src.Sector != null ? src.Sector.Sec_name : null))

                        .ForMember(emp => emp.KafeelName, op => op.MapFrom(src => src.Kafeel != null ? src.Kafeel.KafeelName : null))

                        .ForMember(emp => emp.QualificationName, op => op.MapFrom(src => src.Qualification != null ? src.Qualification.Qualification_Name : null))
                        .ForMember(emp => emp.ReligionName, op => op.MapFrom(src => src.Religion != null ? src.Religion.DyanaName : null));
                    CreateMap<Employee, EmployeeSalariesViewModel>()
                       .ForMember(emp => emp.ChangedSalary, op => op.MapFrom(e => e.FixedTax))
                       .ForMember(emp => emp.MealAllowance, op => op.MapFrom(e => e.FoodAllowance))
                       .ForMember(emp => emp.LocationAllowance, op => op.MapFrom(e => e.WorkNatureAllowance))
                       .ForMember(emp => emp.TelephoneAllowance, op => op.MapFrom(e => e.CommunicationAllowance))

                        .ForMember(emp => emp.DepartmentName, op => op.MapFrom(src => src.Department != null ? src.Department.depart_name : null))

                        .ForMember(emp => emp.BranchName, op => op.MapFrom(src => src.Branch != null ? src.Branch.Purnsh_name : null))

                        .ForMember(emp => emp.CompanyName, op => op.MapFrom(src => src.Company != null ? src.Company.Name : null))

                        .ForMember(emp => emp.ShiftName, op => op.MapFrom(src => src.Shift != null ? src.Shift.ShiftName : null))

                        .ForMember(emp => emp.job, op => op.MapFrom(src => src.Job != null ? src.Job.jname : null))

                        .ForMember(emp => emp.NationalityName, op => op.MapFrom(src => src.Nationality != null ? src.Nationality.Nationality_AR : null))
                        .ForMember(emp => emp.SectorName, op => op.MapFrom(src => src.Sector != null ? src.Sector.Sec_name : null))

                        .ForMember(emp => emp.KafeelName, op => op.MapFrom(src => src.Kafeel != null ? src.Kafeel.KafeelName : null))

                        .ForMember(emp => emp.QualificationName, op => op.MapFrom(src => src.Qualification != null ? src.Qualification.Qualification_Name : null))
                        .ForMember(emp => emp.ReligionName, op => op.MapFrom(src => src.Religion != null ? src.Religion.DyanaName : null));
                }
            }

            public class InsurancesProfile : Profile
            {
                public InsurancesProfile()
                {
                    CreateMap<InputInsurances, EmpTamen>().ReverseMap()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                    CreateMap<EmpTamen, InsurancesViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));

                }
            }

            public class NationalityProfile : Profile
            {
                public NationalityProfile()
                {
                    CreateMap<InputNationality, Nationality>()
                    .ForMember(M => M.NationalityID, op => op.MapFrom(e => e.id))
                    .ForMember(M => M.Nationality_AR, op => op.MapFrom(e => e.NameAr))
                    .ForMember(M => M.Nationality_En, op => op.MapFrom(e => e.NameEn));
                    CreateMap<Nationality, NationalityViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.NationalityID))
                    .ForMember(M => M.NameAr, op => op.MapFrom(e => e.Nationality_AR))
                    .ForMember(M => M.NameEn, op => op.MapFrom(e => e.Nationality_En));

                }
            }


            public class AttendanceMachinesProfile : Profile
            {
                public AttendanceMachinesProfile()
                {
                    CreateMap<t_Clock_List, AttendanceMachinesViewModel>()
                        .ForMember(emp => emp.Id, op => op.MapFrom(e => e.N_ClockIdx))
                        .ForMember(emp => emp.Enabled, op => op.MapFrom(e => e.N_Enabled))
                        .ForMember(emp => emp.Port, op => op.MapFrom(e => e.N_Port))
                        .ForMember(emp => emp.Function, op => op.MapFrom(e => e.MFunction))
                        .ForMember(emp => emp.IP, op => op.MapFrom(e => e.S_IP))
                        .ForMember(emp => emp.Name, op => op.MapFrom(e => e.S_ClockName));

                    CreateMap<InputAttendanceMachines, t_Clock_List>()
                        .ForMember(emp => emp.N_ClockIdx, op => op.MapFrom(e => e.Id))
                        .ForMember(emp => emp.N_Enabled, op => op.MapFrom(e => e.Enabled))
                        .ForMember(emp => emp.N_Port, op => op.MapFrom(e => e.Port))
                        .ForMember(emp => emp.MFunction, op => op.MapFrom(e => e.Function))
                        .ForMember(emp => emp.S_IP, op => op.MapFrom(e => e.IP))
                        .ForMember(emp => emp.S_ClockName, op => op.MapFrom(e => e.Name));

                }
            }


            public class StaffShiftsProfile : Profile
            {
                public StaffShiftsProfile()
                {
                    CreateMap<InputStaffShifts, Shift>()
                    .ForMember(t => t.ShiftID, op => op.MapFrom(e => e.id))
                    .ForMember(t => t.En_ShiftName, op => op.MapFrom(e => e.NameEn))
                    .ForMember(t => t.ShiftName, op => op.MapFrom(e => e.NameAr));
                    CreateMap<Shift, StaffShiftsViewModel>()
                    .ForMember(t => t.id, op => op.MapFrom(e => e.ShiftID))
                    .ForMember(t => t.NameEn, op => op.MapFrom(e => e.En_ShiftName))
                    .ForMember(t => t.NameAr, op => op.MapFrom(e => e.ShiftName));
                }
            }

            public class SettingsProfile : Profile
            {
                public SettingsProfile()
                {
                    CreateMap<InputSettings, Setting>();
                    CreateMap<Setting, SettingsViewModel>();
                }
            }



            // Project

            public class SubcontractorsExtractsProfile : Profile
            {
                public SubcontractorsExtractsProfile()
                {
                    CreateMap<InputSubcontractorsExtracts, Supplier_Extract_Head>()
                        .ForMember(M => M.Extract_ID, op => op.MapFrom(e => e.id));
                    CreateMap<Supplier_Extract_Head, SubcontractorsExtractsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.Extract_ID));
                }
            }

            public class ProjectContractsTypesProfile : Profile
            {
                public ProjectContractsTypesProfile()
                {
                    CreateMap<ProjectInputContractsTypes, ContractType>()
                        .ForMember(M => M.groupid, op => op.MapFrom(e => e.id));
                    CreateMap<ContractType, ProjectContractsTypesViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.groupid));
                }
            }

            public class AssignmentOrderProfile : Profile
            {
                public AssignmentOrderProfile()
                {
                    CreateMap<InputAssignmentOrder, Project_ToDo>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.id));
                    CreateMap<Project_ToDo, AssignmentOrderViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.Id));
                }
            }

            public class SampleApprovalProfile : Profile
            {
                public SampleApprovalProfile()
                {
                    CreateMap<InputSampleApproval, Signed_Q_Table>()
                    .ForMember(M => M.TenderID, op => op.MapFrom(e => e.id));
                    CreateMap<Signed_Q_Table, SampleApprovalViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.TenderID));
                }
            }


            public class DrawingProfile : Profile
            {
                public DrawingProfile()
                {
                    CreateMap<InputDrawing, Drawing_Log>()
                     .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Drawing_Log, DrawingViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class BillsofMaterialsStudyProfile : Profile
            {
                public BillsofMaterialsStudyProfile()
                {
                    CreateMap<InputBillsofMaterialsStudy, Tender_Work_Items_Details_Cost>()
                     .ForMember(M => M.Tender_Works_item_ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Work_Items_Details_Cost, BillsofMaterialsStudyViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.Tender_Works_item_ID));
                }
            }


            public class BillsofMaterialsProfile : Profile
            {
                public BillsofMaterialsProfile()
                {
                    CreateMap<InputBillsofMaterials, POQ_Items>()
                    .ForMember(M => M.TenderID, op => op.MapFrom(e => e.id));
                    CreateMap<POQ_Items, BillsofMaterialsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.TenderID));
                }
            }

            public class PriceAnalysisItemsProfile : Profile
            {
                public PriceAnalysisItemsProfile()
                {
                    CreateMap<InputPriceAnalysisItems, Tender_Work_Items_Details>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Work_Items_Details, PriceAnalysisItemsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class SubWorksProfile : Profile
            {
                public SubWorksProfile()
                {
                    CreateMap<InputSubWorks, Tender_Work>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Work, SubWorksViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class MainworksProfile : Profile
            {
                public MainworksProfile()
                {
                    CreateMap<InputMainworks, Tendr_Basic_Work>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tendr_Basic_Work, MainworksViewModel>()
                      .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TenderComponentsProfile : Profile
            {
                public TenderComponentsProfile()
                {
                    CreateMap<InputTenderComponents, Tender_Content>()
                        .ForMember(M => M.Tender_Content_Srial, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Content, TenderComponentsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.Tender_Content_Srial));
                }
            }

            public class TenderModelsProfile : Profile
            {
                public TenderModelsProfile()
                {
                    CreateMap<InputTenderModels, Tender_Forms>()
                        .ForMember(M => M.ID_Form, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Forms, TenderModelsViewModel>()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.ID_Form));
                }
            }

            public class TenderGroupsTypesProfile : Profile
            {
                public TenderGroupsTypesProfile()
                {
                    CreateMap<InputTenderGroupsTypes, Tender_Groups>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Groups, TenderGroupsTypesViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class ConsultantsProfile : Profile
            {
                public ConsultantsProfile()
                {
                    CreateMap<InputConsultants, Tenders_Consalts>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tenders_Consalts, ConsultantsViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TendersTypeProfile : Profile
            {
                public TendersTypeProfile()
                {
                    CreateMap<InputTendersType, Tenders_Type>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tenders_Type, TendersTypeViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TypeOfAttitudeTenderProfile : Profile
            {
                public TypeOfAttitudeTenderProfile()
                {
                    CreateMap<InputTypeOfAttitudeTender, Tenders_State>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tenders_State, TypeOfAttitudeTenderViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }



            //Realestate

            public class InstallmentReceiptVoucherProfile : Profile
            {
                public InstallmentReceiptVoucherProfile()
                {
                    CreateMap<InputInstallmentReceiptVoucher, SaleContractPaid>()
                        .ForMember(M => M.ContractID, op => op.MapFrom(e => e.id));
                    CreateMap<SaleContractPaid, InstallmentReceiptVoucherViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.ContractID));
                }
            }


            public class BookingAndContractingProfile : Profile
            {
                public BookingAndContractingProfile()
                {
                    CreateMap<InputBookingAndContracting, SaleContract_List>();

                    CreateMap<SaleContract_List, BookingAndContractingViewModel>();

                }
            }

            public class RentReceiptVoucherProfile : Profile
            {
                public RentReceiptVoucherProfile()
                {
                    CreateMap<InputRentReceiptVoucher, RenContractPaid>()
                        .ForMember(M => M.QestID, op => op.MapFrom(e => e.id));
                    CreateMap<RenContractPaid, RentReceiptVoucherViewModel>()
                      .ForMember(M => M.id, op => op.MapFrom(e => e.QestID));
                }
            }


            public class RentContructExtensionProfile : Profile
            {
                public RentContructExtensionProfile()
                {
                    CreateMap<InputRentContructExtension, RentContract_List_Extension_Head>();

                    CreateMap<RentContract_List_Extension_Head, RentContructExtensionViewModel>();

                }
            }


            public class RealEstateUnitsProfile : Profile
            {
                public RealEstateUnitsProfile()
                {
                    CreateMap<InputRealEstateUnits, Properties_list>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Properties_list, RealEstateUnitsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class RentContructProfile : Profile
            {
                public RentContructProfile()
                {
                    CreateMap<InputRentContruct, RentContract_List>();

                    CreateMap<RentContract_List, RentContructViewModel>();

                }
            }

            public class MainPropertyProfile : Profile
            {
                public MainPropertyProfile()
                {
                    CreateMap<InputMainProperty, Main_Properties_list>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.id));
                    CreateMap<Main_Properties_list, MainPropertyViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.Id));
                }
            }


            //Sales


            public class SalesTaxReturnsProfile : Profile
            {
                public SalesTaxReturnsProfile()
                {
                    CreateMap<InputSalesTaxReturns, Mardodat_InviceOut>()
                        .ForMember(M => M.InvoiceNo, op => op.MapFrom(e => e.id));
                    CreateMap<Mardodat_InviceOut, SalesTaxReturnsViewModel>()
                        .ForMember(vm => vm.id, op => op.MapFrom(e => e.InvoiceNo));
                }
            }


            public class WarrantiesProfile : Profile
            {
                public WarrantiesProfile()
                {
                    CreateMap<InputWarranties, Grantee>();

                    CreateMap<Grantee, WarrantiesViewModel>();


                }
            }

            public class QuotationsProfile : Profile
            {
                public QuotationsProfile()
                {
                    CreateMap<InputQuotations, performa_invoice>()
                        .ForMember(M => M.performa_id, op => op.MapFrom(e => e.id));
                    CreateMap<performa_invoice, QuotationsViewModel>()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.performa_id));
                }
            }

            public class SalesInvoiceProfile : Profile
            {
                public SalesInvoiceProfile()
                {
                    CreateMap<InputSalesInvoice, Byan>();

                    CreateMap<Byan, SalesInvoiceViewModel>();
                }
            }
            public class GuarantorProfile : Profile
            {
                public GuarantorProfile()
                {
                    CreateMap<InputGuarantor, persons>().ReverseMap()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.idno))
                        .ForMember(vm => vm.name, op => op.MapFrom(e => e.Pname));
                    CreateMap<persons, GuarantorViewModel>()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.idno))
                        .ForMember(vm => vm.name, op => op.MapFrom(e => e.Pname));

                }
            }

            public class MainCustomerProfile : Profile
            {
                public MainCustomerProfile()
                {
                    CreateMap<InputMainCustomer, MainCustomer>()
                         .ForMember(vm => vm.Mcid, op => op.MapFrom(e => e.id))
                        .ForMember(vm => vm.CustomerName, op => op.MapFrom(e => e.name))
                         .ForMember(vm => vm.CustLevel, op => op.MapFrom(e => e.PriceList))
                        .ForMember(vm => vm.CustDescount, op => op.MapFrom(e => e.DescountPercent));

                    CreateMap<MainCustomer, MainCustomerViewModel>()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.Mcid))
                        .ForMember(vm => vm.name, op => op.MapFrom(e => e.CustomerName))
                         .ForMember(vm => vm.PriceList, op => op.MapFrom(e => e.CustLevel))
                        .ForMember(vm => vm.DescountPercent, op => op.MapFrom(e => e.CustDescount));
                }
            }


            public class PricelistsProfile : Profile
            {
                public PricelistsProfile()
                {
                    CreateMap<InputPricelists, PriceListType>()
                         .ForMember(vm => vm.PriceListID, op => op.MapFrom(e => e.id))
                        .ForMember(vm => vm.PriceLitName, op => op.MapFrom(e => e.name));

                    CreateMap<PriceListType, PricelistsViewModel>()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.PriceListID))
                        .ForMember(vm => vm.name, op => op.MapFrom(e => e.PriceLitName));
                }
            }


            public class SalespersonCategoriesProfile : Profile
            {
                public SalespersonCategoriesProfile()
                {
                    CreateMap<InputSalespersonCategories, SalesMan_types>()
                        .ForMember(vm => vm.TypeCode, op => op.MapFrom(e => e.id))
                        .ForMember(vm => vm.TypeName, op => op.MapFrom(e => e.nameAr))
                        .ForMember(vm => vm.TypeName_EN, op => op.MapFrom(e => e.nameEn));
                    CreateMap<SalesMan_types, SalespersonCategoriesViewModel>()
                        .ForMember(vm => vm.id, op => op.MapFrom(e => e.TypeCode))
                        .ForMember(vm => vm.nameAr, op => op.MapFrom(e => e.TypeName))
                        .ForMember(vm => vm.nameEn, op => op.MapFrom(e => e.TypeName_EN));
                }
            }

            public class SalespersonProfile : Profile
            {
                public SalespersonProfile()
                {
                    CreateMap<InputSalesperson, customer_emp>()
                        .ForMember(vm => vm.MAndopID, op => op.MapFrom(e => e.id))
                        .ForMember(vm => vm.MandopName, op => op.MapFrom(e => e.nameAr))
                        .ForMember(vm => vm.EnName, op => op.MapFrom(e => e.nameEn))
                        .ForMember(vm => vm.SalesManTypeId, op => op.MapFrom(e => e.SalesManTypeId));
                    CreateMap<customer_emp, SalespersonViewModel>()
                        .ForMember(vm => vm.id, op => op.MapFrom(e => e.MAndopID))
                        .ForMember(vm => vm.nameAr, op => op.MapFrom(e => e.MandopName))
                        .ForMember(vm => vm.nameEn, op => op.MapFrom(e => e.EnName))
                        .ForMember(vm => vm.CategoryName, op => op.MapFrom(e => e.SalespersonCategory));

                }
            }

            public class SalespersonCommissionsProfile : Profile
            {
                public SalespersonCommissionsProfile()
                {
                    CreateMap<InputSalespersonCommissions, Mandop_Moda_Omola>()
                    .ForMember(vm => vm.ModaID, op => op.MapFrom(e => e.id))
                        .ForMember(vm => vm.Modda, op => op.MapFrom(e => e.CollectionPeriod))
                        .ForMember(vm => vm.ModaPercent, op => op.MapFrom(e => e.CollectionPercent))
                        .ForMember(vm => vm.MAndopID, op => op.MapFrom(e => e.SalespersonId));
                    CreateMap<Mandop_Moda_Omola, SalespersonCommissionsViewModel>()
                        .ForMember(vm => vm.id, op => op.MapFrom(e => e.ModaID))
                        .ForMember(vm => vm.CollectionPeriod, op => op.MapFrom(e => e.Modda))
                        .ForMember(vm => vm.CollectionPercent, op => op.MapFrom(e => e.ModaPercent))
                        .ForMember(vm => vm.SalespersonId, op => op.MapFrom(e => e.MAndopID));
                }
            }
            public class SalesAndCommissionTermsProfile : Profile
            {
                public SalesAndCommissionTermsProfile()
                {
                    CreateMap<InputSalesAndCommissionTerms, Target_Omola>();

                    CreateMap<Target_Omola, SalesAndCommissionTermsViewModel>();
                }
            }


            public class CustomersCategoryProfile : Profile
            {
                public CustomersCategoryProfile()
                {
                    CreateMap<InputCustomersCategory, customers_types>()
                         .ForMember(vm => vm.TypeCode, op => op.MapFrom(e => e.id))
                          .ForMember(vm => vm.TypeName, op => op.MapFrom(e => e.nameAr))
                           .ForMember(vm => vm.TypeName_EN, op => op.MapFrom(e => e.nameEn))
                            .ForMember(vm => vm.branchId, op => op.MapFrom(e => e.BranchID))
                             .ForMember(vm => vm.companyId, op => op.MapFrom(e => e.ComanyID));

                    CreateMap<customers_types, CustomersCategoryViewModel>()
                         .ForMember(vm => vm.id, op => op.MapFrom(e => e.TypeCode))
                          .ForMember(vm => vm.nameAr, op => op.MapFrom(e => e.TypeName))
                           .ForMember(vm => vm.nameEn, op => op.MapFrom(e => e.TypeName_EN))
                            .ForMember(vm => vm.BranchID, op => op.MapFrom(e => e.branchId))
                             .ForMember(vm => vm.ComanyID, op => op.MapFrom(e => e.companyId));
                }
            }

            public class SalesTaxInvoiceProfile : Profile
            {
                public SalesTaxInvoiceProfile()
                {
                    CreateMap<TaxInvoiceViewModel, Invoiceout>().ReverseMap()
                         .ForMember(vm => vm.InvoiceNo, op => op.MapFrom(e => e.InvoiceNo))
                        .ForMember(vm => vm.InvoiceDate, op => op.MapFrom(e => e.idate))
                        .ForMember(vm => vm.Customer, op => op.MapFrom(e => e.customer.NameAr));
                    CreateMap<Invoiceout, TaxInvoiceViewModel>()
                        .ForMember(vm => vm.InvoiceNo, op => op.MapFrom(e => e.InvoiceNo))
                        .ForMember(vm => vm.InvoiceDate, op => op.MapFrom(e => e.idate))
                        .ForMember(vm => vm.Customer, op => op.MapFrom(e => e.customer.NameAr));
                }
            }

            public class SalesTeamsProfile : Profile
            {
                public SalesTeamsProfile()
                {
                    CreateMap<InputSalesTeams, SalesTeams>();

                    CreateMap<SalesTeams, SalesTeamsViewModel>();
                }
            }

            public class SalesTypeProfile : Profile
            {
                public SalesTypeProfile()
                {
                    CreateMap<InputSalesType, Service_Type>()
                         .ForMember(M => M.Service_ID, op => op.MapFrom(e => e.id))
                        .ForMember(M => M.Service_Type_Name, op => op.MapFrom(e => e.nameAr))
                        .ForMember(M => M.Service_Type_Name_EN, op => op.MapFrom(e => e.nameEn));
                    CreateMap<Service_Type, SalesTypeViewModel>()
                        .ForMember(M => M.id, op => op.MapFrom(e => e.Service_ID))
                        .ForMember(M => M.nameAr, op => op.MapFrom(e => e.Service_Type_Name))
                        .ForMember(M => M.nameEn, op => op.MapFrom(e => e.Service_Type_Name_EN));
                }
            }

            public class CustomerModelProfile : Profile
            {
                public CustomerModelProfile()
                {
                    CreateMap<CustomerViewModel, Customers>()
                        .ForMember(dest => dest.customerGroupId, op => op.MapFrom(src => src.customerGroupId));

                    CreateMap<Customers, CustomerViewModel>()


                        // Nullable long fields
                        .ForMember(dest => dest.SalesPersonId, op => op.MapFrom(src => (long?)src.SalesPersonId))
                        .ForMember(dest => dest.customerGroupId, op => op.MapFrom(src => (long?)src.customerGroupId))
                        .ForMember(dest => dest.MainCustomerId, op => op.MapFrom(src => (long?)src.MainCustomerId))
                        .ForMember(dest => dest.CityID, op => op.MapFrom(src => (long?)src.CityID))
                        .ForMember(dest => dest.ID_Number, op => op.MapFrom(src => (long?)src.ID_Number))
                        .ForMember(dest => dest.IncomAccountCode, op => op.MapFrom(src => (long?)src.IncomAccountCode))
                        .ForMember(dest => dest.TaxAccountCode, op => op.MapFrom(src => (long?)src.TaxAccountCode))
                        .ForMember(dest => dest.DiscountAccount, op => op.MapFrom(src => (long?)src.DiscountAccount))
                        .ForMember(dest => dest.DeductionsAccount, op => op.MapFrom(src => (long?)src.DeductionsAccount))
                        .ForMember(dest => dest.MaintenanceAccID, op => op.MapFrom(src => (long?)src.MaintenanceAccID))

                        // Nullable int fields
                        .ForMember(dest => dest.SupervisorId, op => op.MapFrom(src => (int?)src.SupervisorId))
                        .ForMember(dest => dest.OperatorID, op => op.MapFrom(src => (int?)src.OperatorID))
                        .ForMember(dest => dest.CustcountryID, op => op.MapFrom(src => (int?)src.CustcountryID))
                        .ForMember(dest => dest.PriceList, op => op.MapFrom(src => (int?)src.PriceList))
                        .ForMember(dest => dest.CollectionPeriod, op => op.MapFrom(src => (int?)src.CollectionPeriod))

                        // Nullable boolean fields
                        .ForMember(dest => dest.flag, op => op.MapFrom(src => (bool?)src.flag))
                        .ForMember(dest => dest.IsRealEstateCustomer, op => op.MapFrom(src => (bool?)src.IsRealEstateCustomer));

                    CreateMap<InputCustomer, Customers>()
                       .ForMember(dest => dest.customerGroupId, op => op.MapFrom(src => src.customerGroupId));

                    CreateMap<Customers, InputCustomer>()


                        // Nullable long fields
                        .ForMember(dest => dest.SalesPersonId, op => op.MapFrom(src => (long?)src.SalesPersonId))
                        .ForMember(dest => dest.customerGroupId, op => op.MapFrom(src => (long?)src.customerGroupId))
                        .ForMember(dest => dest.MainCustomerId, op => op.MapFrom(src => (long?)src.MainCustomerId))
                        .ForMember(dest => dest.CityID, op => op.MapFrom(src => (long?)src.CityID))
                        .ForMember(dest => dest.ID_Number, op => op.MapFrom(src => (long?)src.ID_Number))
                        .ForMember(dest => dest.IncomAccountCode, op => op.MapFrom(src => (long?)src.IncomAccountCode))
                        .ForMember(dest => dest.TaxAccountCode, op => op.MapFrom(src => (long?)src.TaxAccountCode))
                        .ForMember(dest => dest.DiscountAccount, op => op.MapFrom(src => (long?)src.DiscountAccount))
                        .ForMember(dest => dest.DeductionsAccount, op => op.MapFrom(src => (long?)src.DeductionsAccount))
                        .ForMember(dest => dest.MaintenanceAccID, op => op.MapFrom(src => (long?)src.MaintenanceAccID))

                        // Nullable int fields
                        .ForMember(dest => dest.SupervisorId, op => op.MapFrom(src => (int?)src.SupervisorId))
                        .ForMember(dest => dest.OperatorID, op => op.MapFrom(src => (int?)src.OperatorID))
                        .ForMember(dest => dest.CustcountryID, op => op.MapFrom(src => (int?)src.CustcountryID))
                        .ForMember(dest => dest.PriceList, op => op.MapFrom(src => (int?)src.PriceList))
                        .ForMember(dest => dest.CollectionPeriod, op => op.MapFrom(src => (int?)src.CollectionPeriod))

                        // Nullable boolean fields
                        .ForMember(dest => dest.flag, op => op.MapFrom(src => (bool?)src.flag))
                        .ForMember(dest => dest.IsRealEstateCustomer, op => op.MapFrom(src => (bool?)src.IsRealEstateCustomer));
                }
            }
            public class TradeClassificationProfile : Profile
            {
                public TradeClassificationProfile()
                {
                    CreateMap<InputTradeClassification, TasnefTogary>()
                         .ForMember(M => M.TradeTypeID, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.TradeType, op => op.MapFrom(e => e.Name));
                    CreateMap<TasnefTogary, TradeClassificationViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.TradeTypeID))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.TradeType)); ;
                }
            }

            public class OperationTypesProfile : Profile
            {
                public OperationTypesProfile()
                {
                    CreateMap<InputOperationType, EznType>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.TypeName, op => op.MapFrom(e => e.Name));
                    CreateMap<EznType, OperationTypeViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.TypeName)); ;
                }
            }

            public class ProductCategoryProfile : Profile
            {
                public ProductCategoryProfile()
                {
                    CreateMap<InputProductCategory, Groups>();
                    CreateMap<Groups, ProductCategoryViewModel>();
                }
            }
            public class ProductSubCategoryProfile : Profile
            {
                public ProductSubCategoryProfile()
                {
                    CreateMap<InputProductSubCategory, Groups_Sub>();
                    CreateMap<Groups_Sub, ProductSubCategoryViewModel>();
                }
            }

            public class UnitProfile : Profile
            {
                public UnitProfile()
                {
                    CreateMap<InputUnit, unit>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.unitname, op => op.MapFrom(e => e.NameAr))
                        .ForMember(M => M.unitname_en, op => op.MapFrom(e => e.NameEn))
                        .ForMember(M => M.CodeID, op => op.MapFrom(e => e.Code));
                    CreateMap<unit, UnitViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.NameAr, op => op.MapFrom(e => e.unitname))
                        .ForMember(M => M.NameEn, op => op.MapFrom(e => e.unitname_en))
                        .ForMember(M => M.Code, op => op.MapFrom(e => e.CodeID));
                    CreateMap<unit, UnitViewModelList>()
                       .ForMember(M => M.UnitId, op => op.MapFrom(e => e.Id))
                       .ForMember(m => m.UnitName, opt => opt.MapFrom((src, dest, destMember, context) =>
                        "ar" == "ar"
                        ? src.unitname
                        : src.unitname_en))
                        .ForMember(M => M.Code, op => op.MapFrom(e => e.CodeID));
                }
            }

            public class ManufactureProfile : Profile
            {
                public ManufactureProfile()
                {
                    CreateMap<InputManufacture, MadeCompany>()
                        .ForMember(M => M.com_id, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.com_name_ar, op => op.MapFrom(e => e.NameAr))
                        .ForMember(M => M.com_name_en, op => op.MapFrom(e => e.NameEn))
                        .ForMember(M => M.com_tel, op => op.MapFrom(e => e.Phone))
                        .ForMember(M => M.com_address, op => op.MapFrom(e => e.Address));

                    CreateMap<MadeCompany, ManufactureViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.com_id))
                        .ForMember(M => M.NameAr, op => op.MapFrom(e => e.com_name_ar))
                        .ForMember(M => M.NameEn, op => op.MapFrom(e => e.com_name_en))
                        .ForMember(M => M.Phone, op => op.MapFrom(e => e.com_tel))
                        .ForMember(M => M.Address, op => op.MapFrom(e => e.com_address));
                }
            }


            //Manufacturing

            public class CompetitorsProfile : Profile
            {
                public CompetitorsProfile()
                {
                    CreateMap<InputCompetitors, Tenders_other_Company>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tenders_other_Company, CompetitorsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class CompetitorsTyepProfile : Profile
            {
                public CompetitorsTyepProfile()
                {
                    CreateMap<InputCompetitorsTyep, Tender_Other_Co_Type>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Other_Co_Type, CompetitorsTyepViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TendersDeductionsProfile : Profile
            {
                public TendersDeductionsProfile()
                {
                    CreateMap<InputTendersDeductions, Extracts_Deductions>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.id));
                    CreateMap<Extracts_Deductions, TendersDeductionsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.Id));
                }
            }

            public class TendersProfile : Profile
            {
                public TendersProfile()
                {
                    CreateMap<InputTenders, Tenders_Add>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id))
                        .ForMember(M => M.TheName, op => op.MapFrom(e => e.NameAr))
                        .ForMember(M => M.EnName, op => op.MapFrom(e => e.NameEn));
                    CreateMap<Tenders_Add, TendersViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ID))
                  .ForMember(M => M.NameAr, op => op.MapFrom(e => e.TheName))
                  .ForMember(M => M.NameEn, op => op.MapFrom(e => e.EnName));
                }
            }


            public class BusinessTypeProfile : Profile
            {
                public BusinessTypeProfile()
                {
                    CreateMap<InputBusinessType, Tender_Word_Secsion>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Word_Secsion, BusinessTypeViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }

            public class TenderActivityTypesProfile : Profile
            {
                public TenderActivityTypesProfile()
                {
                    CreateMap<InputTenderActivityTypes, Tender_Monaksat_Type>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tender_Monaksat_Type, TenderActivityTypesViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }


            public class TenderArrangeTypesProfile : Profile
            {
                public TenderArrangeTypesProfile()
                {
                    CreateMap<InputTenderArrangeTypes, Tenders_Classes>()
                        .ForMember(M => M.ID, op => op.MapFrom(e => e.id));
                    CreateMap<Tenders_Classes, TenderArrangeTypesViewModel>()
                      .ForMember(M => M.id, op => op.MapFrom(e => e.ID));
                }
            }


            public class ManufacturingPlanProfile : Profile
            {
                public ManufacturingPlanProfile()
                {
                    CreateMap<InputManufacturingPlan, Manufactur_Plan>()
                        .ForMember(M => M.ItemCode, op => op.MapFrom(e => e.id));
                    CreateMap<Manufactur_Plan, ManufacturingPlanViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.ItemCode));
                }
            }


            public class AssemblyAndDisassemblyProfile : Profile
            {
                public AssemblyAndDisassemblyProfile()
                {
                    CreateMap<InputAssemblyAndDisassembly, AssemplyFrom>()
                        .ForMember(M => M.AssID, op => op.MapFrom(e => e.id));
                    CreateMap<AssemplyFrom, AssemblyAndDisassemblyViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.AssID));
                }
            }

            public class OrdersProfile : Profile
            {
                public OrdersProfile()
                {
                    CreateMap<InputOrders, runcommand>()
                        .ForMember(M => M.commandid, op => op.MapFrom(e => e.id));
                    CreateMap<runcommand, OrdersViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.commandid));
                }
            }


            //  Maintenance


            public class OpenCallProfile : Profile
            {
                public OpenCallProfile()
                {
                    CreateMap<InputOpenCall, CustomerCall>()
                        .ForMember(M => M.callCode, op => op.MapFrom(e => e.id));
                    CreateMap<CustomerCall, OpenCallViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.callCode));
                }
            }

            public class MaintenanceContractProfile : Profile
            {
                public MaintenanceContractProfile()
                {
                    CreateMap<InputMaintenanceContract, CustomerAcd>()
                        .ForMember(M => M.CustAkdID, op => op.MapFrom(e => e.id));
                    CreateMap<CustomerAcd, MaintenanceContractViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.CustAkdID));
                }
            }

            public class DiagnosisStepsProfile : Profile
            {
                public DiagnosisStepsProfile()
                {
                    CreateMap<InputDiagnosisSteps, SteepTachkhesRepaer>()
                        .ForMember(M => M.SteepTshkhesID, op => op.MapFrom(e => e.id));
                    CreateMap<SteepTachkhesRepaer, DiagnosisStepsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.SteepTshkhesID));
                }
            }


            public class FaultsStagesProfile : Profile
            {
                public FaultsStagesProfile()
                {
                    CreateMap<InputFaultsStages, StoSteeps>()
                        .ForMember(M => M.AkdID, op => op.MapFrom(e => e.id));
                    CreateMap<StoSteeps, FaultsStagesViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.AkdID));
                }
            }


            public class DeviceMalfunctionProfile : Profile
            {
                public DeviceMalfunctionProfile()
                {
                    CreateMap<InputDeviceMalfunction, DeviceStops>()
                        .ForMember(M => M.StopID, op => op.MapFrom(e => e.id));
                    CreateMap<DeviceStops, DeviceMalfunctionViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.StopID));
                }
            }

            public class CallTypesProfile : Profile
            {
                public CallTypesProfile()
                {
                    CreateMap<InputCallTypes, CoolCode>()
                        .ForMember(M => M.AkdID, op => op.MapFrom(e => e.id));
                    CreateMap<CoolCode, CallTypesViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.AkdID));
                }
            }

            public class ContractsTypesProfile : Profile
            {
                public ContractsTypesProfile()
                {
                    CreateMap<InputContractsTypes, AkdCode>()
                        .ForMember(M => M.AkdID, op => op.MapFrom(e => e.id));
                    CreateMap<AkdCode, ContractsTypesViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.AkdID));
                }
            }

            public class CalibrationDeviceProfile : Profile
            {
                public CalibrationDeviceProfile()
                {
                    CreateMap<InputCalibrationDevice, devtest>()
                        .ForMember(M => M.devno, op => op.MapFrom(e => e.id));
                    CreateMap<devtest, CalibrationDeviceViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.devno));
                }
            }


            public class PeriodictrafficProfile : Profile
            {
                public PeriodictrafficProfile()
                {
                    CreateMap<InputPeriodictraffic, dawry>()
                        .ForMember(M => M.dawryid, op => op.MapFrom(e => e.id));
                    CreateMap<dawry, PeriodictrafficViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.dawryid));
                }
            }


            public class MaintenanceRequestsProfile : Profile
            {
                public MaintenanceRequestsProfile()
                {
                    CreateMap<InputMaintenanceRequests, ordermaintainance>()
                        .ForMember(M => M.omid, op => op.MapFrom(e => e.id));
                    CreateMap<ordermaintainance, MaintenanceRequestsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.omid));
                }
            }


            public class DepartmentConsumptionRateProfile : Profile
            {
                public DepartmentConsumptionRateProfile()
                {
                    CreateMap<InputDepartmentConsumptionRate, improvedsection>()
                        .ForMember(M => M.improvedid, op => op.MapFrom(e => e.id));
                    CreateMap<improvedsection, DepartmentConsumptionRateViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.improvedid));
                }
            }

            public class MalfunctionsProfile : Profile
            {
                public MalfunctionsProfile()
                {
                    CreateMap<InputMalfunctions, mdaily>()
                        .ForMember(M => M.mdid, op => op.MapFrom(e => e.id));
                    CreateMap<mdaily, MalfunctionsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.mdid));
                }
            }


            //Purchase



            public class BillsReturnsProfile : Profile
            {
                public BillsReturnsProfile()
                {
                    CreateMap<InputBillsReturns, Mardodat_Invcome>()
                        .ForMember(M => M.InvoiceNo, op => op.MapFrom(e => e.id));
                    CreateMap<Mardodat_Invcome, BillsReturnsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.InvoiceNo));
                }
            }


            public class ReturnsServiceBillsProfile : Profile
            {
                public ReturnsServiceBillsProfile()
                {
                    CreateMap<InputReturnsServiceBills, Mardodat_Invcome>()
                        .ForMember(M => M.InvoiceNo, op => op.MapFrom(e => e.id));
                    CreateMap<Mardodat_Invcome, ReturnsServiceBillsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.InvoiceNo));
                }
            }


            public class ServiceBillsProfile : Profile
            {
                public ServiceBillsProfile()
                {
                    CreateMap<InputServiceBills, invcome>()
                        .ForMember(M => M.InvoiceNo, op => op.MapFrom(e => e.id));
                    CreateMap<invcome, ServiceBillsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.InvoiceNo));
                }
            }



            public class BillsProfile : Profile
            {
                public BillsProfile()
                {
                    CreateMap<InputBills, invcome>()
                        .ForMember(M => M.InvoiceNo, op => op.MapFrom(e => e.id));
                    CreateMap<invcome, BillsViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.InvoiceNo));
                }
            }

            public class RFQComparisonsProfile : Profile
            {
                public RFQComparisonsProfile()
                {
                    CreateMap<InputRFQComparisons, request_for_quotations_item>()
                        .ForMember(M => M.request_no, op => op.MapFrom(e => e.id));
                    CreateMap<request_for_quotations_item, RFQComparisonsViewModel>()
                    .ForMember(M => M.id, op => op.MapFrom(e => e.request_no));
                }
            }

            public class RFQProfile : Profile
            {
                public RFQProfile()
                {
                    CreateMap<InputRFQ, request_for_quotations>()
                        .ForMember(M => M.request_no, op => op.MapFrom(e => e.id));
                    CreateMap<request_for_quotations, RFQViewModel>()
                   .ForMember(M => M.id, op => op.MapFrom(e => e.request_no));
                }
            }

            public class PurchaseOrdersProfile : Profile
            {
                public PurchaseOrdersProfile()
                {
                    CreateMap<InputPurchaseOrders, Amr_Sheraa>()
                        .ForMember(M => M.performa_id, op => op.MapFrom(e => e.id));
                    CreateMap<Amr_Sheraa, PurchaseOrdersViewModel>()
                     .ForMember(M => M.id, op => op.MapFrom(e => e.performa_id));
                }
            }


            public class PurchaseRequisitionProfile : Profile
            {
                public PurchaseRequisitionProfile()
                {
                    CreateMap<InputPurchaseRequisition, purchase_order>()
                        .ForMember(M => M.order_no, op => op.MapFrom(e => e.id));
                    CreateMap<purchase_order, PurchaseRequisitionViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.order_no));
                }
            }

            public class SupplierEvaluationProfile : Profile
            {
                public SupplierEvaluationProfile()
                {
                    CreateMap<InputSupplierEvaluation, purchase_takyeem>()
                        .ForMember(M => M.suppliers_id, op => op.MapFrom(e => e.id));
                    CreateMap<purchase_takyeem, SupplierEvaluationViewModel>()
                  .ForMember(M => M.id, op => op.MapFrom(e => e.suppliers_id));
                }
            }

            public class SuppliersProfile : Profile
            {
                public SuppliersProfile()
                {
                    CreateMap<InputSuppliers, Suppliers>();


                    CreateMap<Suppliers, SuppliersViewModel>()
                   ;
                }
            }

            public class PurchasesTermsProfile : Profile
            {
                public PurchasesTermsProfile()
                {
                    CreateMap<InputPurchasesTerms, PaymentTerms>()
                        .ForMember(M => M.TermID, op => op.MapFrom(e => e.id));
                    CreateMap<PaymentTerms, PurchasesTermsViewModel>()
                 .ForMember(M => M.id, op => op.MapFrom(e => e.TermID));
                }
            }
            public class PurchaseTypeProfile : Profile
            {
                public PurchaseTypeProfile()
                {
                    CreateMap<InputPurchaseType, BerchisingType>()
                         .ForMember(M => M.BeurchisingID, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.BeurchisingTypeMoves, op => op.MapFrom(e => e.Name));
                    CreateMap<BerchisingType, PurchaseTypeViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.BeurchisingID))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.BeurchisingTypeMoves));
                }
            }

            public class SupplierClassificationProfile : Profile
            {
                public SupplierClassificationProfile()
                {
                    CreateMap<InputSupplierClassification, SuppType>()
                         .ForMember(M => M.SuppTypeID, op => op.MapFrom(e => e.Id))
                        .ForMember(M => M.TypeName, op => op.MapFrom(e => e.Name));
                    CreateMap<SuppType, SupplierClassificationViewModel>()
                        .ForMember(M => M.Id, op => op.MapFrom(e => e.SuppTypeID))
                        .ForMember(M => M.Name, op => op.MapFrom(e => e.TypeName));
                }
            }
        }
    }
}
