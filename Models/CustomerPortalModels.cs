using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    // Authentication Models
    public class CustomerLoginRequest
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    public class CustomerLoginResponse
    {
        public bool Success { get; set; }
        public string Token { get; set; } = string.Empty;
        public CustomerInfo Customer { get; set; } = new();
        public string Message { get; set; } = string.Empty;
    }

    // Customer Information
    public class CustomerInfo
    {
        public int Id { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string ContactPerson { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string TaxNumber { get; set; } = string.Empty;
        public string CommercialRegister { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime JoinDate { get; set; }
        public string SubscriptionPlan { get; set; } = string.Empty;
        public DateTime SubscriptionExpiry { get; set; }
    }

    // Dashboard Models
    public class CustomerDashboard
    {
        public DashboardStatistics Statistics { get; set; } = new();
        public List<RecentActivity> RecentActivities { get; set; } = new();
        public List<QuickAction> QuickActions { get; set; } = new();
    }

    public class DashboardStatistics
    {
        public int TotalQuotes { get; set; }
        public int PendingQuotes { get; set; }
        public int TotalInvoices { get; set; }
        public int UnpaidInvoices { get; set; }
        public int TotalTickets { get; set; }
        public int OpenTickets { get; set; }
        public decimal TotalPayments { get; set; }
    }

    public class RecentActivity
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }

    public class QuickAction
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }

    // Quote Models
    public class Quote
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusClass { get; set; } = string.Empty;
        public string ValidUntil { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<QuoteItem> Items { get; set; } = new();
    }

    public class QuoteItem
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }
    }

    // Invoice Models
    public class Invoice
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public string DueDate { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusClass { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public List<InvoiceItem> Items { get; set; } = new();
    }

    public class InvoiceItem
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }
    }

    // Payment Models
    public class Payment
    {
        public string Id { get; set; } = string.Empty;
        public string InvoiceId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Date { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string StatusClass { get; set; } = string.Empty;
        public string Reference { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    // Support Ticket Models
    public class SupportTicket
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string PriorityClass { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string StatusClass { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string CreatedDate { get; set; } = string.Empty;
        public string LastUpdate { get; set; } = string.Empty;
        public string AssignedTo { get; set; } = string.Empty;
        public List<TicketMessage> Messages { get; set; } = new();
    }

    public class TicketMessage
    {
        public int Id { get; set; }
        public string Message { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string SenderType { get; set; } = string.Empty; // customer, support
        public DateTime CreatedAt { get; set; }
        public List<string> Attachments { get; set; } = new();
    }

    public class CreateTicketRequest
    {
        [Required(ErrorMessage = "عنوان التذكرة مطلوب")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "وصف المشكلة مطلوب")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "الأولوية مطلوبة")]
        public string Priority { get; set; } = string.Empty;

        [Required(ErrorMessage = "التصنيف مطلوب")]
        public string Category { get; set; } = string.Empty;
    }

    // Profile Update Models
    public class UpdateProfileRequest
    {
        [Required(ErrorMessage = "اسم الشركة مطلوب")]
        public string CompanyName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الشخص المسؤول مطلوب")]
        public string ContactPerson { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        public string Phone { get; set; } = string.Empty;

        public string Mobile { get; set; } = string.Empty;

        [Required(ErrorMessage = "العنوان مطلوب")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessage = "المدينة مطلوبة")]
        public string City { get; set; } = string.Empty;

        [Required(ErrorMessage = "الدولة مطلوبة")]
        public string Country { get; set; } = string.Empty;

        public string PostalCode { get; set; } = string.Empty;
        public string TaxNumber { get; set; } = string.Empty;
        public string CommercialRegister { get; set; } = string.Empty;
    }

    public class ChangePasswordRequest
    {
        [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [MinLength(8, ErrorMessage = "كلمة المرور يجب أن تكون 8 أحرف على الأقل")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور غير متطابقة")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    // Notification Settings
    public class NotificationSettings
    {
        public bool EmailNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public bool QuoteUpdates { get; set; } = true;
        public bool InvoiceReminders { get; set; } = true;
        public bool SystemUpdates { get; set; } = false;
        public bool MarketingEmails { get; set; } = false;
    }

    // API Response Models
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class PaginatedResponse<T>
    {
        public List<T> Data { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
