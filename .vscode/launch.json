{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/API/bin/Debug/net9.0/API.dll", "args": [], "cwd": "${workspaceFolder}/API", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}}, {"name": ".NET Core Attach", "type": "coreclr", "request": "attach"}, {"name": "Docker .NET Launch", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "netCore": {"appProject": "${workspaceFolder}/API/API.csproj"}}, {"name": "Docker Node.js Launch", "type": "node", "request": "launch", "preLaunchTask": "docker-run: debug"}]}