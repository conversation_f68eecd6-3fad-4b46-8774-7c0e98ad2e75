{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/API/API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/API/API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/API/API.csproj"], "problemMatcher": "$msCompile"}, {"type": "docker-build", "label": "docker-build: debug", "dependsOn": ["build"], "dockerBuild": {"tag": "falconclouderp:dev", "target": "base", "dockerfile": "${workspaceFolder}/API/Dockerfile", "context": "${workspaceFolder}", "pull": true}, "netCore": {"appProject": "${workspaceFolder}/API/API.csproj"}}, {"type": "docker-build", "label": "docker-build: release", "dependsOn": ["build"], "dockerBuild": {"tag": "falconclouderp:latest", "dockerfile": "${workspaceFolder}/API/Dockerfile", "context": "${workspaceFolder}", "platform": {"os": "linux", "architecture": "amd64"}, "pull": true}, "netCore": {"appProject": "${workspaceFolder}/API/API.csproj"}}, {"type": "docker-run", "label": "docker-run: debug", "dependsOn": ["docker-build: debug"], "dockerRun": {}, "netCore": {"appProject": "${workspaceFolder}/API/API.csproj", "enableDebugging": true}}, {"type": "docker-run", "label": "docker-run: release", "dependsOn": ["docker-build: release"], "dockerRun": {}, "netCore": {"appProject": "${workspaceFolder}/API/API.csproj"}}, {"type": "docker-build", "label": "docker-build", "platform": "node", "dockerBuild": {"dockerfile": "${workspaceFolder}/API/Dockerfile", "context": "${workspaceFolder}/API/ClientApp", "pull": true}, "node": {"package": "${workspaceFolder}/API/ClientApp/package.json"}}]}