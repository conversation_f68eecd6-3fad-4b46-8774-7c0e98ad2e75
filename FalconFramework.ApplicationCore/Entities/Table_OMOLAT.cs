//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Table_OMOLAT
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string InvoiceType { get; set; }
        public string InviceTaxState { get; set; }
        public long? InvoiceNO { get; set; }
        public DateTime? tahseldate { get; set; }
        public string Mandop_Name { get; set; }
        public string ServiceType { get; set; }
        public string Tahseel { get; set; }
        public string customer_name { get; set; }
        public long? itemid { get; set; }
        public string ITEMS_ITEM_NAME { get; set; }
        public Nullable<double> Q { get; set; }
        public Nullable<double> PriceOne { get; set; }
        public Nullable<double> ItemCost { get; set; }
        public DateTime? idate { get; set; }
        public Nullable<double> TotalCost { get; set; }
        public Nullable<double> TotalPrice { get; set; }
        public Nullable<double> ValPonas { get; set; }
        public Nullable<double> Chargevalue { get; set; }
        public Nullable<double> ExactCharge { get; set; }
        public Nullable<double> ItemMaksab { get; set; }
        public string UseerName { get; set; }
        public string Vmaksap_Perce { get; set; }
        public DateTime? ActionDate { get; set; }
        public int? store_id { get; set; }
        public int? MAndopID { get; set; }
    }
}
