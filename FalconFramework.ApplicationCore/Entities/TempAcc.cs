using System.ComponentModel.DataAnnotations.Schema;

public class TempAcc
{
    public int id { get; set; }
    public int MainAccId {get;set;}
	public string MainAccCode {get;set;}
	public string MainAccName {get;set;}
	public int AccId {get;set;}
	public string AccCode {get;set;}
	public string AccName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Maden {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Daen {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal StartMaden {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Startdaen {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal BeforMaden {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Befordaen {get;set;}
	public int userId {get;set;}
	public int AccLevel {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Balance {get;set;}
	public int RelatedID {get;set;}
}