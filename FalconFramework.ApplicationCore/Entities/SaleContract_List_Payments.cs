//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(SaleContract_List_Id), nameof(gno))]
    public partial class SaleContract_List_Payments
    {
        public long SaleContract_List_Id { get; set; }
        public int gno { get; set; }
        public Nullable<double> Amount { get; set; }
        public DateTime? gdate { get; set; }
        public DateTime? pdate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? payed { get; set; }
        public string paystate { get; set; }
        public Nullable<byte> post { get; set; }
        public long? accno { get; set; }
        public string Hdate { get; set; }
        public string AmountOnly { get; set; }
        public long? docNo { get; set; }
        public string BankName { get; set; }
        public string InstallmentType { get; set; }
    }
}
