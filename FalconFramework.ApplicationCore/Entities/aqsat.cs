//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class aqsat
    {
        public short ContYear { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long gcontno { get; set; }
        public string conttype { get; set; }
        public byte gno { get; set; }
        public int camount { get; set; }
        public string gdate { get; set; }
        public string pdate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? payed { get; set; }
        public string paystate { get; set; }
        public Nullable<byte> post { get; set; }
        public string accno { get; set; }
        public DateTime? DEDate { get; set; }
        public DateTime? DETime { get; set; }
        public string UserCOde { get; set; }
        public string compName { get; set; }
        public string docNo { get; set; }
    }
}
