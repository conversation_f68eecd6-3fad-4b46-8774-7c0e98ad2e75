using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Supplier_Extract_Head
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int Extract_ID {get;set;}
	public int Extract_No {get;set;}
	public DateTime Extract_Date {get;set;}
	public string Extract_Type {get;set;}
	public int Supp_ID {get;set;}
	public string Supp_Name {get;set;}
	public string Supject {get;set;}
	public DateTime ContractDate {get;set;}
	public int Contract_NO {get;set;}
	public DateTime FromDate {get;set;}
	public DateTime ToDate {get;set;}
	public string Extract_Titel {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Egmaly {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Egmaly_Monsaref {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Descount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Mostahakat {get;set;}

	public string Egmaly_Only {get;set;}
	public string Egmaly_Monsaref_only {get;set;}
	public string Descount_only {get;set;}
	public string Mostahakat_only {get;set;}
	public long ProjectID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal AdvancePaymentPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal AdvancePayment {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Insurance_BusinessPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Insurance_Business {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal SumPrevExtracts {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal VatPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal VatValue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal gtaxPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal gtaxValue {get;set;}
	public int Journal {get;set;}
	public double ReserveDiscount {get;set;}
	public double DeductValue {get;set;}
	public double DeductPercent {get;set;}
	public int ProunchID {get;set;}
	public int BurshaseType {get;set;}
}