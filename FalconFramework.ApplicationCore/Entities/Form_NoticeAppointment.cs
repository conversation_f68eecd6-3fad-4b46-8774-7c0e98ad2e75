//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_NoticeAppointment
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public long? EmpCode { get; set; }
        public string EmpName { get; set; }
        public DateTime? Date { get; set; }
        public string JopName { get; set; }
        public string DepartMent { get; set; }
        public string Branch { get; set; }
        public string Nationality { get; set; }
        public string Qualification { get; set; }
        public string Specialization { get; set; }
        public string graduatYear { get; set; }
        public string Apprcioation { get; set; }
        public string PointOfGraduation { get; set; }
        public string LastJop { get; set; }
        public string ExpYear { get; set; }
        public string ReasonForLadving { get; set; }
        public int? moakat { get; set; }
        public int? taref { get; set; }
        public int? Trainint { get; set; }
        public string Notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
