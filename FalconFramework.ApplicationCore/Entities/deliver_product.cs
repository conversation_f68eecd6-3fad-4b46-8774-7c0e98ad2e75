//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class deliver_product
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long productid { get; set; }
        public long? runcommandid { get; set; }
        public long? orderno { get; set; }
        public DateTime? prodactdate { get; set; }
        public long? Customer_id { get; set; }
        public string describe { get; set; }
        public long? quantity { get; set; }
        public string notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
