//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class InvoiceComeNotTax_Cost
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long? InvoiceNo { get; set; }
        public int? Serial { get; set; }
        public long? AccCode { get; set; }
        public string Details { get; set; }
        public string Notes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Valcost { get; set; }
        public long? costid { get; set; }
        public string Costname { get; set; }
        public long? JournalID { get; set; }
    }
}
