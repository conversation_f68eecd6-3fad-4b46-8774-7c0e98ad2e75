

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SuppType
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long SuppTypeID { get; set; }
        public string TypeName { get; set; }
        public bool? flag { get; set; }
        public bool? showinsales { get; set; }
    }
}
