//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Taqseet
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public long? CustID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MonyValue { get; set; }
        public int? Cycle { get; set; }
        public int? CycleLoop { get; set; }
        public string Notes { get; set; }
    
        public virtual ICollection<Taqseet_Items> Taqseet_Items { get; set; }
    }
}
