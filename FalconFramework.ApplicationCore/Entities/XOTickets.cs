//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class XOTickets
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public   Guid RowId { get; set; }
        public DateTime? IssueDate { get; set; }
        public int? Command { get; set; }
        public string AirlineName { get; set; }
        public string AirlineNumber { get; set; }
        public string AirlineCode { get; set; }
        public string PassengerName { get; set; }
        public int? PassengerType { get; set; }
        public long? TicketNumber { get; set; }
        public int? ConjunctionNumber { get; set; }
        public string Route { get; set; }
        public string PNR { get; set; }
        public string BookingAgencyIATA { get; set; }
        public string FirstOwnerIATA { get; set; }
        public string CurrentAgencyIATA { get; set; }
        public string TicketingAgencyIATA { get; set; }
        public string Agent { get; set; }
        public int? PaymentType { get; set; }
        public long? OrderNo { get; set; }
        public long? Customer { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fare { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tax1 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tax2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tax3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalFare { get; set; }
        public Nullable<double> CommissionRate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CommissionAmount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RefundPenaltyFee { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RefundAgencyFee { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BCIn { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BCOut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Nett { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Revenue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Cash { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SpanAmount { get; set; }
        public bool? IsApproved { get; set; }
        public bool? IsInternational { get; set; }
        public string Note { get; set; }
        public Guid? TransactionID { get; set; }
        public long? OwnerID { get; set; }
        public string DepartureAirportCode { get; set; }
        public string ArrivalAirportCode { get; set; }
        public DateTime? DepartureDateTime { get; set; }
        public DateTime? ArrivalDateTime { get; set; }
        public int? PassengerID { get; set; }
        public bool? IsVoided { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Markup { get; set; }
        public int? ReservationID { get; set; }
        public int? Count { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RefundComission { get; set; }
        public string BoxName { get; set; }
        public string BankName { get; set; }
        public string CashPart { get; set; }
        public string SpanPart { get; set; }
        public string Curns_Name { get; set; }
        public int? BranchID { get; set; }
        public bool? Sigend { get; set; }
    }
}
