//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class cvhead
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long CVID { get; set; }
        public string name { get; set; }
        public DateTime? pdate { get; set; }
        public string nationality { get; set; }
        public string idno { get; set; }
        public string idfrom { get; set; }
        public string address { get; set; }
        public string msate { get; set; }
        public string jopneed { get; set; }
        public string notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
