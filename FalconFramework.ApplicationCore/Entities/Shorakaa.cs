

using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;

[Table("Shorakaa")]
public class ShorakaaTable
{
    public int id { get; set; }
    public string Details {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal MonyHead {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Ehtyaty {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Kard {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Arbah {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Shorakaa {get;set;}
	public string TheYear {get;set;}
}