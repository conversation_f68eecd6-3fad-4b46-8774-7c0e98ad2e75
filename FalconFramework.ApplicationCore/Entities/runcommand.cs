

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class runcommand
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long commandid { get; set; }
        public long? orderid { get; set; }
        public long? Customer_id { get; set; }
        public long? orderno { get; set; }
        public DateTime? commanddate { get; set; }
        public string FinalCheckEmp { get; set; }
        public string FinalCheckNotes { get; set; }
        public string FinalCheckDate { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
