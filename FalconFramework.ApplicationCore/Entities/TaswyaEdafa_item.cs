

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(taswyaEdafaId), nameof(serial))]
    public partial class TaswyaEdafa_item
    {
        [ForeignKey("TaswyaEdafa")]
        
        [Column("EdafaID")]
        public long taswyaEdafaId { get; set; }
        public int? serial { get; set; }
        
        //[ForeignKey("Items")]
        [Column("itemid")]
        public long productId { get; set; }

        [Column("Unite")]
        public string SaleUnitName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        public string SN { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bounas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defualt_ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostAllItemOut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maksap { get; set; }

        public int? SaleUnitID { get; set; }
        public long? CostID { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }
        public string VariantName { get; set; }

        public virtual TaswyaEdafa TaswyaEdafa { get; set; }
        public virtual Product Product { get; set; }
     

    }
}
