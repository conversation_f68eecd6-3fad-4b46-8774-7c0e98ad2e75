

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class commandorder
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long performa_id { get; set; }
        public string Customer_id { get; set; }
        public string customername { get; set; }
        public long? serial { get; set; }
        public DateTime? performa_date { get; set; }
        public string intro { get; set; }
        public DateTime? performa_date_end { get; set; }
        public string payment_termes { get; set; }
        public string delivery_termes { get; set; }
        public string delivery_place { get; set; }
        public string shipping_type { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? sales_tax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? general_tax { get; set; }
        public string notes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string performaonly { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
