using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(serialitem))]
public class invcomeitemNoTax_ezn
{
	public long InvoiceNo {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
	public int Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
}