//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TaswyaMony_Emp
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public long TaswyaID { get; set; }
        public int? serial { get; set; }
        public DateTime? TaswyaDate { get; set; }
        public Nullable<double> TaswyaValue { get; set; }
        public long? FromAccount { get; set; }
        public long? ToAccount { get; set; }
        public long? Journal { get; set; }
        public string Notes { get; set; }
        public bool? Cust { get; set; }
        public bool? Vender { get; set; }
        public bool? Tree { get; set; }
        public string AccountName { get; set; }
        public bool? solfa { get; set; }
        public long? CostId { get; set; }
        public long? DocNO { get; set; }
        public string DayName { get; set; }
        public bool? Tobank { get; set; }
        public long? Acccode2 { get; set; }
        public string AccName2 { get; set; }
        public bool? toohda { get; set; }
        public bool? ToBox { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
        public long? VATACC { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VATPercent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VATValue { get; set; }
        public string VATNO { get; set; }
        public int? CostTypeID { get; set; }
        public int? destTypeId { get; set; }
        public int? destId { get; set; }
        public string destName { get; set; }
        public string Note2 { get; set; }
        public string destTypeName { get; set; }
        public int? year { get; set; }
    }
}
