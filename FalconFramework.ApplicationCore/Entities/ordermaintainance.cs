//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class ordermaintainance
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long omid { get; set; }
        public long? depart_id { get; set; }
        public long? machiinedi { get; set; }
        public DateTime? omdate { get; set; }
        public long? emp_id { get; set; }
        public string descripe { get; set; }
        public bool? stop_or_no { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
