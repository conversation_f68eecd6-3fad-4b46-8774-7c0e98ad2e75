
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(commandid), nameof(serial))]
    public partial class RunCommandItem
    {
        public long commandid { get; set; }
        public int? serial { get; set; }
        public long? itemCode { get; set; }
        public string ItemName { get; set; }
        public string unite { get; set; }
        public Nullable<double> QCust { get; set; }
        public Nullable<double> QInStores { get; set; }
        public Nullable<double> Qneed { get; set; }
        public Nullable<double> QtoRuning { get; set; }
        public string ItemCustCode { get; set; }
        public string Notes { get; set; }
        public string SteepName { get; set; }
        public string DepartName { get; set; }
        public string DateNeed { get; set; }
        public string DrawNO { get; set; }
        public string UserHave { get; set; }
        public string UserSigne { get; set; }
        public string DateHave { get; set; }
        public string DateEnd { get; set; }
        public string SteepChakingEmp { get; set; }
        public string SteepChikingDate { get; set; }
        public string SteepChikingNotes { get; set; }
        public string UserName { get; set; }
        public DateTime? ActionDate { get; set; }
    }
}
