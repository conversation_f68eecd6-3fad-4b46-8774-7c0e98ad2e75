using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Tbl_SUN_Patch
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public string AccCode {get;set;}
	public string AccName {get;set;}
	public string Note1 {get;set;}
	public string Note2 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Amount {get;set;}
	public string D_or_C {get;set;}
	public long CostID {get;set;}
	public DateTime aDate {get;set;}
	public string PERIODDate {get;set;}
	public string CaseName {get;set;}
	public string Acccode2 {get;set;}
}