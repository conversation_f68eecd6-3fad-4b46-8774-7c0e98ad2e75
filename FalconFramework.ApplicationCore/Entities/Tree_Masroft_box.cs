//-------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Tree_Masroft_box
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long TaswyaID { get; set; }
        public int serial { get; set; }
        public DateTime? TaswyaDate { get; set; }
        public Nullable<double> TaswyaValue { get; set; }
        public long? FromAccount { get; set; }
        public long? ToAccount { get; set; }
        public bool? IsToCost { get; set; }
        public string CostType { get; set; }
        public string CostName { get; set; }
        public long? Journal { get; set; }
        public string Notes { get; set; }
        public long? CostID { get; set; }
        public string Not1 { get; set; }
        public string DayName { get; set; }
        public string Doc_No { get; set; }
        public long? Acccode2 { get; set; }
        public string AccName2 { get; set; }
        public bool? ToTree { get; set; }
        public bool? ToCust { get; set; }
        public bool? Tobank { get; set; }
        public bool? ToVender { get; set; }
        public bool? ToSolfa { get; set; }
        public bool? ToOhda { get; set; }
        public string HeadNote { get; set; }
        public bool? ToBox { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
        public int? ProunchID { get; set; }
        public long? VATACC { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VATPercent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VATValue { get; set; }
        public string VATNO { get; set; }
        public int? CostTypeID { get; set; }
        public int? MandopId { get; set; }
        public int? year { get; set; }
        public int? ProjectID { get; set; }
        public string MachineName { get; set; }
        public string MachineID { get; set; }
        public int? destTypeId { get; set; }
        public string destTypeName { get; set; }
        public int? destId { get; set; }
        public string destName { get; set; }
        public int? CurnToID { get; set; }
        public Nullable<double> CurnRate { get; set; }
        public Nullable<double> CurnAmount { get; set; }
        public int? CurID { get; set; }
        public int? OstazID { get; set; }
    }
}
