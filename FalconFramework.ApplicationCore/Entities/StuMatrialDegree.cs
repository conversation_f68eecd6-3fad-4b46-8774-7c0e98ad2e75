//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class StuMatrialDegree
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int MatID { get; set; }
        public int? StuID { get; set; }
        public string Term { get; set; }
        public int? MarhalaCode { get; set; }
        public int? SafCode { get; set; }
        public string StuMarhalato { get; set; }
        public string StuSafTo { get; set; }
        public int? totalmatrial { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? totalStudent { get; set; }
        public int? stupersent { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
