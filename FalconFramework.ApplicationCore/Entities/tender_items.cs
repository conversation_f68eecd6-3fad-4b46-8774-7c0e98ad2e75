
using Microsoft.EntityFrameworkCore;

[PrimaryKey(nameof(tender_no), nameof(serial))]
public class tender_items
{
	public long tender_no {get;set;}
	public long user_id {get;set;}
	public DateTime action_date {get;set;}
	public long serial {get;set;}
	public string item {get;set;}
	public long quantity {get;set;}
	public string describe {get;set;}
	public string notes {get;set;}
	public bool flag {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}