//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class improvedsection
    {
        [Key]
        public long improvedid { get; set; }
        public long? depart_id { get; set; }
        public long? machiinedi { get; set; }
        public DateTime? improveddate { get; set; }
        public string itemid { get; set; }
        public string itemdescrip { get; set; }
        public string unites { get; set; }
        public long? itemquantity { get; set; }
        public Nullable<double> price { get; set; }
        public Nullable<double> total { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Supplire { get; set; }
        public string MaintenanceType { get; set; }
    }
}
