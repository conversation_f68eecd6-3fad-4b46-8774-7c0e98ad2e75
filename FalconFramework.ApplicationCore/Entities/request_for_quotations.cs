//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class request_for_quotations
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long request_no { get; set; }
        public long? order_no { get; set; }
        public long? suppliers_id { get; set; }
        public DateTime? request_date { get; set; }
        public int? departid { get; set; }
        public string tax { get; set; }
        public string waydafa { get; set; }
        public string timetawred { get; set; }
        public string place { get; set; }
        public string ertebat { get; set; }
        public string paymentterm { get; set; }
        public DateTime? action_date { get; set; }
        public string Username { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Request_Subject { get; set; }
        public string Supp_Contact { get; set; }
        public string DocNO { get; set; }
        public string empName { get; set; }
    }
}
