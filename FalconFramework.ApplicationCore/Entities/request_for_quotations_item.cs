
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(request_no), nameof(serial))]
public class request_for_quotations_item
{
	public long request_no {get;set;}
	public long serial {get;set;}
	public string item {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal priceone {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal total {get;set;}
	public string notes {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
	public string gmsign {get;set;}
	public string gmnotes {get;set;}
	public int ItemCode {get;set;}
}