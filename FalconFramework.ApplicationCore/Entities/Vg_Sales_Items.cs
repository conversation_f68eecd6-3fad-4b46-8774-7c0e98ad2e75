//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Vg_Sales_Items
    {
        public long Id { get; set; }
        public int SupplierID { get; set; }
        public int ReceivingID { get; set; }
        public long ItemCode { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Quantiry { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Returns { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal NetQuantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Total { get; set; }
        public string PaymentTypes { get; set; }
        public int CustomerID { get; set; }
        public long EmpCode { get; set; }
        public int? Vg_Sales_Id { get; set; }
        public string ItemName { get; set; }
        public int? Q_Statuse { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TaxValue { get; set; }
    
        public virtual Vg_Sales Vg_Sales { get; set; }
    }
}
