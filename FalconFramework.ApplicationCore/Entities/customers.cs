using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using FalconFramework.ApplicationCore.Entities;


[Table("customers", Schema = "dbo")]
public class Customers
{
	[Key]
	[DatabaseGenerated(DatabaseGeneratedOption.None)]

	[Column("Customer_id")]
	public long Id { get; set; }
	[Column("MAndopID")]
	public long SalesPersonId { get; set; }
	[Column("customer_name")]
	public string NameAr { get; set; }
	public string address { get; set; }
	public string phone { get; set; }
	public string fax { get; set; }
	public string mobile { get; set; }
	public string site { get; set; }
	[Column("scope")]
	public string CustomerActivity { get; set; }

	[Column("ntes")]
	public string Notes { get; set; }
	public string user_id { get; set; }
	public DateTime action_date { get; set; }

	[Column("TypeCode")]
	[ForeignKey("CustomerGroup")]
    public long customerGroupId { get; set; }


	[Column("Mcid")]
	public long MainCustomerId { get; set; }
	public long CityID { get; set; }
	public bool flag { get; set; }
	[Column("ProunchID")]
	public int? branchId { get; set; }
	[Column("COMP_ID")]
	public int? companyId { get; set; }
	public string custtime { get; set; }
	public string custVacaiotn { get; set; }
	[Column("Taxstate")]
	public string TaxStatus { get; set; }

	[Column("paystate")]
	public string Collection { get; set; }

	[Column("waypay")]
	public string PaymentMethod { get; set; }

	[Column("custlevel")]
	public int PriceList { get; set; }

	[Column("TahseelModa")]

	public int CollectionPeriod { get; set; }
	[Column("Customer_Name_EN")]
	public string NameEn { get; set; }
	public string Short_Name_Ar { get; set; }
	public string Short_Name_EN { get; set; }
	[Column("EmpSuperFizer")]
	public int SupervisorId { get; set; }

	public double Limet_money { get; set; }
	public string Nationality { get; set; }
	[Column("brth_date")]
	public DateTime BirthDate { get; set; }

	public string Discount_percent { get; set; }
	[Column("TreeCode")]
	public long AccountCode { get; set; }

	[Column("TreeName")]
	public string AccountName { get; set; }

	public int OperatorID { get; set; }
	public long ID_Number { get; set; }
	public string ID_Date_start { get; set; }
	public string ID_Date_End { get; set; }
	public string ID_From { get; set; }
	[Column("Mokadama")]
	public long AdvancePaymentAccount { get; set; }

	[Column("Mokadama_Name")]
	public string AdvancePaymentAccountName { get; set; }

	[Column("Mo7tagazat")]
	public long RetentionAccount { get; set; }

	[Column("Mo7tagazat_Name")]
	public string RetentionAccountName { get; set; }

	[Column("Dman")]
	public long GuaranteeAccount { get; set; }

	[Column("Dman_Name")]
	public string GuaranteeAccountName { get; set; }

	public string IBAN_NO { get; set; }
	public string BankName { get; set; }
	public string EMail { get; set; }
	[Column(TypeName = "decimal(18, 2)")]
	public decimal Increase_Rate { get; set; }
	public long Tax_registration_Number { get; set; }
	public bool IsRealEstateCustomer { get; set; }
	public string LoginPassword { get; set; }
	public string LoginPasswordSalt { get; set; }
	[Column("Togary")]
	public string CommercialRegisterNumber { get; set; }

	public string Qualification { get; set; }
	public string PersonalMail { get; set; }
	public string WorkPhone { get; set; }
	public string HomePhone { get; set; }
	public string RelativeName_1 { get; set; }
	public string RelativeType_1 { get; set; }
	public string RelativePhone_1 { get; set; }
	public string RelativeName_2 { get; set; }
	public string RelativeType_2 { get; set; }
	public string RelativePhone_2 { get; set; }
	public string RelativeName_3 { get; set; }
	public string RelativeType_3 { get; set; }
	public string RelativePhone_3 { get; set; }
	public string EmergencyAddress { get; set; }
	public long IncomAccountCode { get; set; }
	public string IncomAccountName { get; set; }
	public long TaxAccountCode { get; set; }
	public string TaxAccountName { get; set; }
	public long DiscountAccount { get; set; }
	public long DeductionsAccount { get; set; }
	public string DiscountAccountName { get; set; }
	public string DeductionsAccountName { get; set; }
	public int CustcountryID { get; set; }
	[Column("governate")]
	public string Governorate { get; set; }

	[Column("regionCity")]
	public string City { get; set; }

	public string buildingNumber { get; set; }
	public string postalCode { get; set; }
	public string Custfloor { get; set; }
	public string room { get; set; }
	public string landmark { get; set; }
	public string additionalInformation { get; set; }
	public string ActivityCode { get; set; }
	public long MaintenanceAccID { get; set; }
	public string MaintenanceName { get; set; }
	public string district { get; set; }
	public string street { get; set; }
	public string Business_personal { get; set; }
	public string IdentityType { get; set; }
	public string IdentityNumber { get; set; }

	public virtual Branches Branch { get; set; }
	public virtual Companies Company { get; set; }

	public virtual customers_types CustomerGroup { get; set; }

}