

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Plans
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int PalnID { get; set; }
        public string PlanName { get; set; }
        public DateTime? DateAdded { get; set; }
    }
}
