
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(UserID), nameof(SubCode), nameof(AccCode))]
    public partial class Rptledgersubtotal
    {
        public string UserID { get; set; }
        public int SubCode { get; set; }
        public string AccCode { get; set; }
        public string AccName { get; set; }
        public Nullable<double> prevdebit { get; set; }
        public Nullable<double> prevcredit { get; set; }
        public Nullable<double> debit { get; set; }
        public Nullable<double> credit { get; set; }
        public Nullable<double> rseddebit { get; set; }
        public Nullable<double> rsedcredit { get; set; }
        public string SubName { get; set; }
        public Nullable<double> Opendebit { get; set; }
        public Nullable<double> Opencredit { get; set; }
    }
}
