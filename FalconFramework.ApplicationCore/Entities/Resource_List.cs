//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Resource_List
    {
        [Key]
        public long Resource_Id { get; set; }
        public string Resource_No { get; set; }
        public string Resource_Name { get; set; }
        public string Resource_Type { get; set; }
        public string Base_Unit_Measure { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Cost { get; set; }
        public string Profit_Calculation { get; set; }
        public string Profit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        public string Gen_Prod_Posting_Group { get; set; }
        public string Search_Name { get; set; }
        public bool? IsResources { get; set; }
    }
}
