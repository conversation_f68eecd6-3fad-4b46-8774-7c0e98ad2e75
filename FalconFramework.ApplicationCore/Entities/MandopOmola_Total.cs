using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class MandopOmola_Total
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public int MandopID {get;set;}
	public string Mandop_Name {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalSales {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Targetvalue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TargetPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal OmolaValue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal net_Omola {get;set;}
}