

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Vehicle_List
    {
        [Key]
        public long Vehicle_Id { get; set; }
        public string Vehicle_No { get; set; }
        public string Vehicle_Description { get; set; }
        public string Vehicle_Division { get; set; }
        public string Vehicle_SerialNo { get; set; }
        public string Customer_No { get; set; }
        public DateTime? Arabic_Fitness_date { get; set; }
        public string Fitness_Status { get; set; }
        public DateTime? Fitness_Date { get; set; }
        public bool? AFS_Vehicle { get; set; }
        public string Service_Company { get; set; }
        public string Service_Group_Code { get; set; }
    }
}
