using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Manufactur_Plan
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long ItemCode {get;set;}
	public string ItemName {get;set;}
	public string FormName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
	public string NotesPerLine {get;set;}
	public int FormNameID {get;set;}
	public int progress {get;set;}
}