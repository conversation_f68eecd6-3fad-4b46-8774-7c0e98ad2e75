
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class contract
    {
        public short ContYear { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long contno { get; set; }
        public string conttype { get; set; }
        public string fileno { get; set; }
        public long? idno { get; set; }
        public long? kidno1 { get; set; }
        public long? kidno2 { get; set; }
        public string shaseno { get; set; }
        public long? saccno { get; set; }
        public long? baccno { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Cost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? fpay { get; set; }
        public string sstate { get; set; }
        public Nullable<byte> Collecter { get; set; }
        public Nullable<byte> Collplace { get; set; }
        public string contdate { get; set; }
        public Nullable<byte> gestcount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? gestval { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? fgest { get; set; }
        public string fgsd { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? lgest { get; set; }
        public string lgsd { get; set; }
        public string fshahid { get; set; }
        public string fshahidid { get; set; }
        public string sshahid { get; set; }
        public string sshahidid { get; set; }
        public string remarks { get; set; }
        public bool? caschOrLoan { get; set; }
        public bool? AutoTransGest { get; set; }
        public string dateOrNo { get; set; }
        public string spgst1no { get; set; }
        public int? spgst1val { get; set; }
        public string spgst2no { get; set; }
        public int? spgst2val { get; set; }
        public string spgst3no { get; set; }
        public int? spgst3val { get; set; }
        public string spgst4no { get; set; }
        public int? spgst4val { get; set; }
        public string spgst5no { get; set; }
        public int? spgst5val { get; set; }
        public string spgst6no { get; set; }
        public int? spgst6val { get; set; }
        public Nullable<byte> post { get; set; }
        public bool? remintfg { get; set; }
        public string cus_bnk_accno { get; set; }
        public int? ItemSupplier { get; set; }
        public string SuppInvNum { get; set; }
        public string SuppInvDate { get; set; }
        public string contAmounttxt { get; set; }
        public string ContDescription { get; set; }
        public Nullable<float> Space { get; set; }
        public string Size { get; set; }
        public Nullable<float> Weight { get; set; }
        public Nullable<float> BorderLN { get; set; }
        public Nullable<float> BorderLS { get; set; }
        public Nullable<float> BorderLE { get; set; }
        public Nullable<float> BorderLW { get; set; }
        public string BorderN { get; set; }
        public string BorderS { get; set; }
        public string BorderE { get; set; }
        public string BorderW { get; set; }
        public int? ContStatus { get; set; }
        public int? SMSMode { get; set; }
        public int? RecVersion { get; set; }
        public string UserCOde { get; set; }
        public long? Journal { get; set; }
        public bool? IsBox { get; set; }
        public bool? IsBank { get; set; }
        public long? Box_Bank_ID { get; set; }
        public string BakyAmount { get; set; }
        public DateTime? ExpDate { get; set; }
        public string MDate2 { get; set; }
        public string ExpDate2 { get; set; }
        public string DayName { get; set; }
    }
}
