//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Transfer_Emp
    {
        [Key]
        public long TransferID { get; set; }
        public long? Emp_ID { get; set; }
        public string Emp_Name { get; set; }
        public string Project_From { get; set; }
        public int? Project_From_ID { get; set; }
        public string Project_To { get; set; }
        public int? Project_To_ID { get; set; }
        public string HR_Responsible { get; set; }
        public string HR_Note { get; set; }
        public DateTime? Request_Date { get; set; }
        public string HR_Managere { get; set; }
        public string HR_Managere_Note { get; set; }
        public DateTime? HR_Managere_Date { get; set; }
        public string Project_From_Managere { get; set; }
        public string Project_From_Managere_Note { get; set; }
        public DateTime? Project_From_Managere_Date { get; set; }
        public string Project_To_Managere { get; set; }
        public string Project_To_Managere_Note { get; set; }
        public DateTime? Project_To_Managere_Date { get; set; }
        public string Cost_From { get; set; }
        public long? Cos_From_ID { get; set; }
        public string Cos_To { get; set; }
        public long? Cos_To_ID { get; set; }
        public bool? Signed { get; set; }
    }
}
