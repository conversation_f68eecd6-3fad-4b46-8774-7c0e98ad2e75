

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Groups
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long groupid { get; set; }
        public string groupname { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string En_Name { get; set; }
        public string Short_Code { get; set; }
        public bool? ShowINCashier { get; set; }
        public int? ClassificationID { get; set; }

        public virtual IEnumerable<Groups_Sub> productSubCategory { get; set; }
    }
}
