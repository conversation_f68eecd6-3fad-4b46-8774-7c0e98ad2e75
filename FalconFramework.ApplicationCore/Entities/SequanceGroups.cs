﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class SequanceGroups
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ID { get; set; }
        public string SequanceName { get; set; }
        public string TAGID { get; set; }
        public string Cond { get; set; }
        public string Trans_Action { get; set; }
        public int BranchID { get; set; }
        public long LastID { get; set; }
    }
}
