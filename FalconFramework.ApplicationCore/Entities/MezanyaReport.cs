using System.ComponentModel.DataAnnotations.Schema;

public class <PERSON>zanyaReport
{
    public int id { get; set; }
    public DateTime FromDate {get;set;}
	public DateTime ToDate {get;set;}
	public string DesignName {get;set;}
	public string MezanyaName {get;set;}
	public string Title {get;set;}
	public string AccID {get;set;}
	public string AccName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Maden {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal MadenTotal {get;set;}
	public string Title1 {get;set;}
	public string AccID1 {get;set;}
	public string AccName1 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Daen {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal DaenTotal {get;set;}
	public int Tasnef1 {get;set;}
	public string tasnefname1 {get;set;}
	public int Tasnef2 {get;set;}
	public string tasnefname2 {get;set;}
}