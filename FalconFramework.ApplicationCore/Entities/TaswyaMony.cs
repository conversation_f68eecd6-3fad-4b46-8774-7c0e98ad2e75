//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TaswyaMony
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long TaswyaID { get; set; }
        public int? serial { get; set; }
        public DateTime? TaswyaDate { get; set; }
        public Nullable<double> TaswyaValue { get; set; }
        public long? FromAccount { get; set; }
        public long? ToAccount { get; set; }
        public bool? IsToCost { get; set; }
        public string CostType { get; set; }
        public string CostName { get; set; }
        public long? Journal { get; set; }
        public string Notes { get; set; }
        public string Not1 { get; set; }
        public string DayName { get; set; }
        public string Doc_No { get; set; }
        public bool? To_Cust { get; set; }
        public bool? To_Vend { get; set; }
        public bool? To_Chart { get; set; }
        public bool? To_Emp { get; set; }
        public bool? To_Bank { get; set; }
        public string ToAccountName { get; set; }
        public bool? solfa { get; set; }
        public long? Acccode2 { get; set; }
        public string AccName2 { get; set; }
        public long? CostId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Daen { get; set; }
        public bool? ToBox { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
    }
}
