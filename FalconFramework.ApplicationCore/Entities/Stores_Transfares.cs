

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Stores_Transfares
    {
        public Stores_Transfares()
        {
            this.Stores_Transfares_items = new HashSet<Stores_Transfares_items>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long TransId { get; set; }

        [ForeignKey(nameof(FromWarehouse))]
        [Column("FormStore")]
        public long FromWarehouseId { get; set; }

        [ForeignKey(nameof(ToWarehouse))]
        [Column("ToStore")]
        public long ToWarehouseId { get; set; }
        public DateTime? TransDate { get; set; }
        public string Notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public long? Journal { get; set; }
        public int? TalabMowadID { get; set; }
        public int? ProjectID { get; set; }
        public string ManualTransfer { get; set; }
        public long? TalabSarfID { get; set; }
        public int? year { get; set; }


        public virtual ICollection<Stores_Transfares_items> Stores_Transfares_items { get; set; }
        public virtual Warehouse FromWarehouse { get; set; }
        public virtual Warehouse ToWarehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }


    }
}
