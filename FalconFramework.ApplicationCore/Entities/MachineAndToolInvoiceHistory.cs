using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class MachineAndToolInvoiceHistory
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public DateTime FromDate {get;set;}
	public DateTime ToDate {get;set;}
	public string EznSader {get;set;}
	public string EznWared {get;set;}
	public string MahineName {get;set;}
	public string MoveFrom {get;set;}
	public string MoveTO {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal DayCount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Daily {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Monthly {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ExtraAmount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal QCOome {get;set;}
	public DateTime IncomeDate {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Balance {get;set;}
	public bool IsTooles {get;set;}
	public int prevdayes {get;set;}
	public int curentdayes {get;set;}
}