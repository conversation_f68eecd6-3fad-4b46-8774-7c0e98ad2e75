//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SaleContract_Draft
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long SaleContract_List_Id { get; set; }
        public int gno { get; set; }
        public Nullable<double> Amount { get; set; }
        public DateTime? gdate { get; set; }
        public string AmountOnly { get; set; }
        public string CustomerName { get; set; }
        public string PropretyName { get; set; }
        public Nullable<double> ProprtyTotal { get; set; }
        public Nullable<double> AdvancePayment { get; set; }
        public Nullable<double> FristPayment { get; set; }
        public Nullable<double> MentPayment { get; set; }
    }
}
