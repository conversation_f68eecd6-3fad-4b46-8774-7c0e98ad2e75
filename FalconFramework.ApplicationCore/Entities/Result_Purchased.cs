using System.ComponentModel.DataAnnotations.Schema;

public class Result_Purchased
{
    public int id { get; set; }
    public int InvoiceNo {get;set;}
	public string store_name {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public DateTime idate {get;set;}
	public string suppliers_name {get;set;}
	public string InooiceNOVender {get;set;}
	public string TaxType {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal saltax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal generaltax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Fright {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Descount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal net {get;set;}
	public long Journal {get;set;}
	public long SuppID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Valitemtax {get;set;}
	public string groupname {get;set;}
	public string subname {get;set;}
	public string Barcode {get;set;}
	public int BoxId {get;set;}
	public string Boxname {get;set;}
	public bool flag {get;set;}
	public string Traffic_Classification {get;set;}
	public int DriverID {get;set;}
}