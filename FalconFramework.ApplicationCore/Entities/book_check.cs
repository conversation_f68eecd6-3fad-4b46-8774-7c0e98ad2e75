//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class book_check
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long? code_check { get; set; }
        public int? code_patient { get; set; }
        public string name_patient { get; set; }
        public DateTime? date_check { get; set; }
        public int? code_dep { get; set; }
        public string name_dep { get; set; }
        public string name_doc { get; set; }
        public int? code_doc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? check_value { get; set; }
        public int? code_service { get; set; }
        public string name_service { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? price_service { get; set; }
        public int? Currancey_ID { get; set; }
        public string Currency { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total_check { get; set; }
        public bool flage_book { get; set; }
        public string PriceList_Name { get; set; }
        public string kindcheck { get; set; }
    }
}
