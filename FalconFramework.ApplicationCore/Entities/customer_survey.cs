using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class customer_survey
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int suppliers_id {get;set;}
	public DateTime takyeemdate {get;set;}
	public long sumval {get;set;}
	public string percennt {get;set;}
	public string userid {get;set;}
	public DateTime actiondate {get;set;}
	public bool flag {get;set;}
	public int COMP_ID {get;set;}
	public int ProunchID {get;set;}

}