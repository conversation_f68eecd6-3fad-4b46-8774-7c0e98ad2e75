//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class IsalTawredBox
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int IsalID { get; set; }
        public string Manname { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? IsalVal { get; set; }
        public string ValAR { get; set; }
        public string Notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
