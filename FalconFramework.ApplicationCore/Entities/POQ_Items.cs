 

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [<PERSON><PERSON>ey(nameof(TenderID), nameof(Srial))]
    public partial class POQ_Items
    {
        public long TenderID { get; set; }
        public int? Tender_BasicID { get; set; }
        public int? tender_workID { get; set; }
        public int Srial { get; set; }
        public string Tender_Item_No { get; set; }
        public string Details { get; set; }
        public string UnitName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tahmilat_Perc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Totalprice2 { get; set; }
        public long? FormID { get; set; }
        public long? GroupID { get; set; }
        public long? Tender_ContentID { get; set; }
        public string Notes { get; set; }
        public bool? Loked { get; set; }
        public string LokedUser { get; set; }
        public string LokedDate { get; set; }
        public Nullable<double> HandPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? HandPrice2 { get; set; }
    }
}
