﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class Permission
    {
        public Permission()
        {
            Id = Guid.NewGuid().ToString();
        }
        public string Id { get; set; }

        public string Role { get; set; }
        public string Module { get; set; }
        public bool PrintAction { get; set; }
        public bool DisplayAction { get; set; }
        public bool DeleteAction { get; set; }
        public bool CreateAction { get; set; }
        public bool UpdateAction { get; set; }
    }
}
