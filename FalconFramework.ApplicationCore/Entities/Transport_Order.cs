//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Transport_Order
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public long? CustomerID { get; set; }
        public long? OprationID { get; set; }
        public DateTime? MDate { get; set; }
        public string HDate { get; set; }
        public string Policy_Number { get; set; }
        public string NoteNo { get; set; }
        public string Container_Size { get; set; }
        public string ContainerWeight { get; set; }
        public int? Qty { get; set; }
        public int? Transported { get; set; }
        public int? Remaining { get; set; }
        public int? BoxsCount { get; set; }
        public string Container_No { get; set; }
        public string Transport_Type { get; set; }
        public int? Places_From { get; set; }
        public int? Places_To { get; set; }
        public bool? Driver_In { get; set; }
        public bool? Car_In { get; set; }
        public long? Driver { get; set; }
        public long? Car_No { get; set; }
        public string Car_Type { get; set; }
        public string Place_transport { get; set; }
        public string Note { get; set; }
        public bool? Container { get; set; }
        public bool? Roll { get; set; }
        public bool? Bag { get; set; }
        public bool? Twist { get; set; }
        public bool? Package { get; set; }
        public bool? Other { get; set; }
        public bool? Status { get; set; }
        public int? year { get; set; }
    }
}
