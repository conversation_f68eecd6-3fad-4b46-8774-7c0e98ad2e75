//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Letter_of_Guarantee_Used
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public int? Collateral_ID { get; set; }
        public int? Bank_ID { get; set; }
        public string Collateral_NO { get; set; }
        public string Opration { get; set; }
        public Nullable<double> Collateral_Value { get; set; }
        public Nullable<double> Collateral_Rat { get; set; }
        public Nullable<double> Collateral_Mony { get; set; }
        public string Notes { get; set; }
        public DateTime? Date_Strat { get; set; }
        public DateTime? Date_End { get; set; }
        public Nullable<double> commission { get; set; }
        public long? JournalEnd { get; set; }
        public long? KIdIDEnd { get; set; }
        public Nullable<double> NewValue { get; set; }
        public Nullable<double> NewINs { get; set; }
        public long? KaidID { get; set; }
        public long? Journal { get; set; }
        public int? OstazID { get; set; }
        public long? CostID { get; set; }
        public int? CurrnceID { get; set; }
        public DateTime? ActionDate { get; set; }
        public long? ProjectID { get; set; }
        public long? VedorID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ProjectValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GuaranteeRatio { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Vat_Value { get; set; }
    }
}
