//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Main_Properties_list
    {
        [Key]
        public int Id { get; set; }
        public string Name { get; set; }
        public string En_Name { get; set; }
        public int? TypeID { get; set; }
        public int? CityID { get; set; }
        public int? CountryID { get; set; }
        public string Address { get; set; }
        public string Location { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RentRate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RenewRate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SaleRate { get; set; }
        public string PropertyNo { get; set; }
        public int? FloorCounts { get; set; }
        public long? CustomerID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VAT { get; set; }
        public long? LastContractID { get; set; }
        public bool? Avilable { get; set; }
        public string TypeName { get; set; }
    }
}
