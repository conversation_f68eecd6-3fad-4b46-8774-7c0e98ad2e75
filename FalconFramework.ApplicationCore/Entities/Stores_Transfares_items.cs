

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [<PERSON><PERSON><PERSON>(nameof(TransId), nameof(serial))]
    public partial class Stores_Transfares_items
    {
        public long TransId { get; set; }
        public int? serial { get; set; }
        public int? itemid { get; set; }
        public string Unite { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        public Nullable<double> Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        public string SN { get; set; }
        public Nullable<double> Bounas { get; set; }
        public Nullable<double> Unit_Quantity { get; set; }
        public Nullable<double> Unit_Price { get; set; }
        public Nullable<double> Defualt_ItemCost { get; set; }
        public Nullable<double> UnitCost { get; set; }
        public Nullable<double> Unit_Balance { get; set; }
        public Nullable<double> Rate { get; set; }
        public Nullable<double> CostAllItemOut { get; set; }
        public Nullable<double> maksap { get; set; }
        public int? SaleUnitID { get; set; }
        public long? CostID { get; set; }
        public string VariantName { get; set; }
    }
}
