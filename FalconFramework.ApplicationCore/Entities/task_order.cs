//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class task_order
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long task_order_no { get; set; }
        public DateTime? task_order_date { get; set; }
        public long? suppliers_id { get; set; }
        public string insurance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? sale_tax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? general_tax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string invoice_only { get; set; }
        public long? user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
