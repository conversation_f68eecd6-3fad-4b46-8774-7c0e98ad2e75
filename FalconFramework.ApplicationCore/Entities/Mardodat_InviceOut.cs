//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Mardodat_InviceOut
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long InvoiceNo { get; set; }
        public DateTime? idate { get; set; }
        public long? ClientID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? saltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? generaltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string aArabicTotal { get; set; }
        public string invtype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monycome { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monystill { get; set; }
        public string notes { get; set; }
        public long? iyeer { get; set; }
        public int? store_id { get; set; }
        public string servicetype { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Tahseel { get; set; }
        public string TahseelNote { get; set; }
        public DateTime? tahseldate { get; set; }
        public long? InvEznNo { get; set; }
        public long? Journal { get; set; }
        public string Mandop_Name { get; set; }
        public int? saletype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount_Percent { get; set; }
        public int? MandopID { get; set; }
        public string DocNo { get; set; }
        public int? BoxID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Mony_Bank { get; set; }
        public int? BankAccountID { get; set; }
        public string BankName { get; set; }
        public byte[] QR_Code { get; set; }
        public string UUID { get; set; }
        public TimeSpan? InvTime { get; set; }
    }
}
