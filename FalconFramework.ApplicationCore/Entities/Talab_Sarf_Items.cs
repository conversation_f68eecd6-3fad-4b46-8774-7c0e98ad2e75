

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [<PERSON><PERSON><PERSON>(nameof(SarfID), nameof(serial))]
    public partial class Talab_Sarf_Items
    {
        public long SarfID { get; set; }
        public int? serial { get; set; }
        [Column("itemid")]
        public long productId { get; set; }

        [Column("Unite")]
        public string SaleUnitName { get; set; }

        public Nullable<double> Price { get; set; }
        public Nullable<double> Quantity { get; set; }
        public Nullable<double> total { get; set; }
        public string SN { get; set; }
        public long? CostId { get; set; }
        public Nullable<double> Bounas { get; set; }
        public Nullable<double> Unit_Quantity { get; set; }
        public Nullable<double> Unit_Price { get; set; }
        public Nullable<double> Defualt_ItemCost { get; set; }
        public Nullable<double> UnitCost { get; set; }
        public Nullable<double> Unit_Balance { get; set; }
        public Nullable<double> Rate { get; set; }
        public Nullable<double> CostAllItemOut { get; set; }
        public Nullable<double> maksap { get; set; }
        public int? SaleUnitID { get; set; }
        public long? Account_ID { get; set; }
        public int? Store_ID { get; set; }
        public long? Store_Account { get; set; }
        public string VariantName { get; set; }
        public decimal? PreviousQuantity { get; set; }    
        public decimal? ReturnQuantity { get; set; }
        public decimal? NetConsumer { get; set; }
        public decimal? Remaining { get; set; }


        public virtual Talab_Sarf Talab_Sarf { get; set; }

        public virtual Product Product { get; set; }


    }
}
