

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Notes
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Note_Srial { get; set; }
        public long Not_ID { get; set; }
        public string Note_Name { get; set; }
        public DateTime? NoteDate { get; set; }
        public string Note_gjry { get; set; }
        public string Note_Supject { get; set; }
        public string Note_to { get; set; }
        public string Note_to_sefa { get; set; }
        public string Tahya { get; set; }
        public string Note_Content { get; set; }
        public string End1 { get; set; }
        public string End2 { get; set; }
        public string Sign_Name1 { get; set; }
        public string Sign1 { get; set; }
        public string Sign_Name2 { get; set; }
        public string Sign2 { get; set; }
        public string Sign_Name3 { get; set; }
        public string Sign3 { get; set; }
        public int? DepartID { get; set; }
        public string Depart_Name { get; set; }
        public string Doc_No { get; set; }
        public string GM { get; set; }
        public string EM { get; set; }
    }
}
