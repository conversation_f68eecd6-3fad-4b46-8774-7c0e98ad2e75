using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class t_UsersList
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public string S_UserId {get;set;}
	public string S_UserName {get;set;}
	public string S_Title {get;set;}
	public string S_Pwd {get;set;}
	public int N_FIELD_1 {get;set;}
	public int N_FIELD_2 {get;set;}
	public string S_FIELD_1 {get;set;}
	public string S_FIELD_2 {get;set;}
}