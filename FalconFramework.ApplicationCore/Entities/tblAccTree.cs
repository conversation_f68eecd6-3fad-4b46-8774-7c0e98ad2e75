

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class tblAccTree
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public long? AccCode { get; set; }
        public string AccName { get; set; }
        public long? ParentID { get; set; }
        public bool? AccType { get; set; }
        public int? AccLevel { get; set; }
        public bool? ISmain { get; set; }
        public int? MezanyaID { get; set; }
        public int? ACC_Type_ID { get; set; }
        public string LongCode { get; set; }
        public string AccName_En { get; set; }
        public string AccountGroup_ID { get; set; }
        public long? ClassificationId { get; set; }
        public long? SortID { get; set; }
        public bool? Machine_Mandatory { get; set; }
        public bool? IsMadeen { get; set; }
        public long? CostID { get; set; }
        public bool? NOCostAllowed { get; set; }
        
    }
}
