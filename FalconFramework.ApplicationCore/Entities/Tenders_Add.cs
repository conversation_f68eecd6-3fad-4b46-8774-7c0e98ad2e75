
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Tenders_Add
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public string Type { get; set; }
        public string TheName { get; set; }
        public DateTime? DateArrive { get; set; }
        public DateTime? DateAdd { get; set; }
        public string UserAdd { get; set; }
        public bool? Flag { get; set; }
        public long? CoCount { get; set; }
        public long? OurLevel { get; set; }
        public long? TenderStateID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GoodPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Percentneed { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? OurPrice { get; set; }
        public string ConsaltName { get; set; }
        public string Founder { get; set; }
        public string GM { get; set; }
        public string GNotes { get; set; }
        public string ConsaltPhone { get; set; }
        public string EnName { get; set; }
        public string ShortName { get; set; }
        public string ShortCode { get; set; }
        public long? CustomerId { get; set; }
    }
}
