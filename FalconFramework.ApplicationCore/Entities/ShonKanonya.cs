using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Shon<PERSON>anonya
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int LawID {get;set;}
	public int EmpCode {get;set;}
	public string Wrong {get;set;}
	public string Correct {get;set;}
	public DateTime ActionDate {get;set;}
	public string UserName {get;set;}
	public string GmSign {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}