//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Tender_Tech
    {
        [Key]
        public long ID { get; set; }
        public long? TendeID { get; set; }
        public long? MonaksaID { get; set; }
        public long? TenderTypeID { get; set; }
        public long? WorkSesionID { get; set; }
        public long? CuntryID { get; set; }
        public long? CityID { get; set; }
        public DateTime? DateTech { get; set; }
        public DateTime? DateMaly { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TenderValue { get; set; }

        public string TenderValueMony { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TenderTime { get; set; }
        public string TendervalueTime { get; set; }
        public string UserAdd { get; set; }
        public DateTime? DateAdd { get; set; }
        public long? TenderClassID { get; set; }
        public long? TenderOtherCoID { get; set; }
        public long? TenderStateID { get; set; }
        public long? ConsaltID { get; set; }
        public long? OwnerID { get; set; }
        public int? Tenders_ConsaltsID { get; set; }
        public DateTime? StartDate { get; set; }
        public Nullable<double> OpiningCollected { get; set; }
        public bool? IsStoped { get; set; }
        public string Contract_No { get; set; }
        public string Letar_No { get; set; }
        public DateTime? ReceivDate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? AdvancePaymentPercent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? AdvancePayment { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Insurance_BusinessPercent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Insurance_Business { get; set; }
    }
}
