//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RentAgain
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long RentID { get; set; }
        public int RaginID { get; set; }
        public DateTime? RentDateAgin { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rtimegin { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ragintamen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? raginvalue { get; set; }
    }
}
