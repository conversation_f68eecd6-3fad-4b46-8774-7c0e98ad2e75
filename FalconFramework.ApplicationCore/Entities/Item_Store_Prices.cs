//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Item_Store_Prices
    {
        public long Id { get; set; }
        public int? StoreID { get; set; }
        public long? ItemCode { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Profit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? price { get; set; }
        public int? UnitCode { get; set; }
        public int? PranchID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Cost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MinPrice { get; set; }
    }
}
