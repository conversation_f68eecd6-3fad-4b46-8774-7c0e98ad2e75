

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
public class berchasebetweendate
{
      [Key]
     [DatabaseGenerated(DatabaseGeneratedOption.None)]
     public long itemid {get;set;}
	public string itemname {get;set;}
	public string unit {get;set;}
	public double quantity {get;set;}
	public double valsum {get;set;}
        [Column(TypeName = "decimal(18, 2)")]
        public decimal avrege {get;set;}
        [Column(TypeName = "decimal(18, 2)")]
        public decimal pricepercent {get;set;}
        [Column(TypeName = "decimal(18, 2)")]
        public decimal qpercent {get;set;}
	public string FromDate {get;set;}
	public string ToDate {get;set;}
	public string store {get;set;}
	public string tasnef {get;set;}
	public string grouup {get;set;}
	public string supgroup {get;set;}
	public string moared {get;set;}
	public bool flag {get;set;}
        [Column(TypeName = "decimal(18, 2)")]
        public decimal LastPurshas {get;set;}
}   
}
