using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class trans
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public string accno {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal amount {get;set;}
	public string ttype {get;set;}
	public string tdate {get;set;}
	public int transClass {get;set;}
	public short TransYear {get;set;}
	public long docno {get;set;}
	public string doctype {get;set;}
	public short ContYear {get;set;}
	public string contno {get;set;}
	public string conttype {get;set;}
	public byte gestno {get;set;}
	public string name {get;set;}
	public string remarks {get;set;}
	public short bankno {get;set;}
	public string chiqueno {get;set;}
	public string CHEQUDATE {get;set;}
	public string UserCOde {get;set;}
}