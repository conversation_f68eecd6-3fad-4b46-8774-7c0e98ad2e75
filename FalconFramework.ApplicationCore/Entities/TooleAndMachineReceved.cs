//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TooleAndMachineReceved
    {
        [Key]
        public long ID { get; set; }
        public bool? IsTooles { get; set; }
        public string Notes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        public DateTime? IncomeDate { get; set; }
        public TimeSpan? IncoeTime { get; set; }
        public long? MoveID { get; set; }
        public int? CounterEnd { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BalanceAfter { get; set; }
        public int? DayCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? prevBalance { get; set; }
    }
}
