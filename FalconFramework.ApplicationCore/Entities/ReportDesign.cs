//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class ReportDesign
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ID { get; set; }
        public string DesignName { get; set; }
        public string Printer { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PageHeight { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PageWidth { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TopMarign { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BottomMargin { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RightMargin { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? LeftMargin { get; set; }
        public string ReportXML { get; set; }
        public bool? IsRoll { get; set; }
        public long? TAGID { get; set; }
        public int? ProunchID { get; set; }
    }
}
