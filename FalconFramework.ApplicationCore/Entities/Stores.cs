
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
    [Table("Stores")]
    public partial class Warehouse
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        [Column("store_id")]
        public long Id { get; set; }
        public string store_name { get; set; }
        public string store_address { get; set; }
        public string emp_name { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string City { get; set; }
        public bool? IsMain { get; set; }
    }
}
