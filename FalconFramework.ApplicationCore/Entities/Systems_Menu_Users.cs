

using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(UserID), nameof(MenuID))]
    public partial class Systems_Menu_Users
    {
        public int? UserID { get; set; }
        public int MenuID { get; set; }
        public bool? SSave { get; set; }
        public bool? SEdit { get; set; }
        public bool? SDelte { get; set; }
        public bool? SPrint { get; set; }
        public bool? SView { get; set; }
    }
}
