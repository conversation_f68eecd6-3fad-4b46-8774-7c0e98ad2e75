//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class StuMatrial
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int MatCode { get; set; }
        public int? MarhalaCode { get; set; }
        public int? SafCode { get; set; }
        public string MatName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MatAmount { get; set; }
        public int? d1 { get; set; }
        public int? d2 { get; set; }
        public int? d3 { get; set; }
        public int? d4 { get; set; }
        public int? d5 { get; set; }
        public int? d6 { get; set; }
        public int? d7 { get; set; }
        public int? d8 { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
