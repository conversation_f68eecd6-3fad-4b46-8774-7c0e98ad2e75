

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    [Table("itemclint")]
    public partial class CustomersSamples
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long id { get; set; }
        public long? customerid { get; set; }
        public string itemdescripe { get; set; }
        public long? empid { get; set; }
        public byte[] imge { get; set; }
        public DateTime? datecome { get; set; }
        public DateTime? dateout { get; set; }

        [Column("mancach")]
        public string Recipient { get; set; }
        public string idcard { get; set; }
        public string carno { get; set; }
        public string notes { get; set; }

         [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }

         public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
    }
}
