using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class VoidItems
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long InvNo {get;set;}
	public DateTime ADate {get;set;}
	public string Notes {get;set;}
	public double Amount {get;set;}
	public string WhyVoid {get;set;}
	public string TableName {get;set;}
	public string SalesMan {get;set;}
	public string ItemName {get;set;}
}