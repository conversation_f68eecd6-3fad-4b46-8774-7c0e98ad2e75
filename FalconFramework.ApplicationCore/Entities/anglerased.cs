﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class anglerased
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int? angelid { get; set; }
        public string mkas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? somk { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? tol { get; set; }
        public long rasedkabl { get; set; }
        public string projectname { get; set; }
        public string projectmanegeer { get; set; }
        public string emp { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? qt { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rasedbaad { get; set; }
        public DateTime? adate { get; set; }
        public string auser { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
    }
}
