﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class Notification
    {
        public Notification()
        {
            Id = Guid.NewGuid().ToString();
            SentDate = DateTime.Now;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public string Id { set; get; }
        public DateTime SentDate { get; set; }
        public string Title { set; get; }
        public string TargetUserId { set; get; }
        public string ResourceId { set; get; }
        public string SenderUserId { set; get; }
        public string Body { set; get; }
        public bool IsRead { get; set; }

        public virtual ApplicationUser TargetUser { get; set; }
        public virtual ApplicationUser SenderUser { get; set; }
    }
}
