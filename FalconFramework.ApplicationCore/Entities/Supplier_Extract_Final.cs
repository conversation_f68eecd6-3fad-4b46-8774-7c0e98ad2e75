using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Supplier_Extract_Final
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int Extract_ID {get;set;}
	public int Line_NO {get;set;}
	public string LineData {get;set;}
	public long AccountCode {get;set;}
	public string AccountName {get;set;}
	public double AmmountPercent {get;set;}
	public double PrevousAmmount {get;set;}
	public double CurrentAmmount {get;set;}
	public double TotalAmount {get;set;}
	public string Notes {get;set;}
	public long CaseId {get;set;}
	public string CaseName {get;set;}
}