//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Target_Omola_Value
    {
        [Key]
        public long ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Target_From { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Target_To { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Commission { get; set; }
    }
}
