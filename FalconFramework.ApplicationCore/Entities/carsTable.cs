//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class carsTable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public string shaseno { get; set; }
        public string boardno { get; set; }
        public string estno { get; set; }
        public string estmissu { get; set; }
        public string estmdate { get; set; }
        public int typecar { get; set; }
        public string modno { get; set; }
        public string color { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? carcustoms { get; set; }
        public string remarks { get; set; }
        public string carsource { get; set; }
        public string Boughtby { get; set; }
        public string BuyDate { get; set; }
        public int? LastownerType { get; set; }
        public int? CarCategory { get; set; }
        public string lastowner { get; set; }
        public string lastSaleType { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? LastSalePrice { get; set; }
        public DateTime? DEDate { get; set; }
        public string UserCOde { get; set; }
        public int? BoughtbyID { get; set; }
        public int? lastownerid { get; set; }
        public long? Item_Id { get; set; }
    }
}
