//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Project_ToDo
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        public long? Project_ID { get; set; }
        public string ContractNO { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ContractValue { get; set; }
        public string WorkPlace { get; set; }
        public string WorkLocation { get; set; }
        public string WorkDetails { get; set; }
        public string Recipient_Supervisor { get; set; }
        public string Project_Engineer { get; set; }
    }
}
