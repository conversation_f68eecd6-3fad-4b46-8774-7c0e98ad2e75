

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TblCostTree
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ID { get; set; }
        public string AccCode { get; set; }
        public string AccName { get; set; }
        public int ParentID { get; set; }
        public bool? AccType { get; set; }
        public int AccLevel { get; set; }
        public bool? ISmain { get; set; }
        public long? CustID { get; set; }
        public string ShortCode { get; set; }
        public string LongCode { get; set; }
        public bool? Flag { get; set; }
        public Nullable<double> Percent_Allawonce { get; set; }
        public string AccName_EN { get; set; }
        public int? ClassificationId { get; set; }
        public int? SortID { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }

        //public virtual IEnumerable<emp> Employees { get; set; }


    }
}
