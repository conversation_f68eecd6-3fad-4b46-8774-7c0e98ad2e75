//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Mokayfat_Tarkeep
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long PerformaID { get; set; }
        public string serial { get; set; }
        public string Details { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        public string Unit { get; set; }
    }
}
