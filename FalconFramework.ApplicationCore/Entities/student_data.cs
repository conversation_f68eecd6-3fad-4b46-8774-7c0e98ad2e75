//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class student_data
    {
        public long? id { get; set; }
        public long? syear { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long code_eltaleb { get; set; }
        public string esm_eltaleb { get; set; }
        public string el3enwan { get; set; }
        public string enow3 { get; set; }
        public string eldeyana { get; set; }
        public string eljenseya { get; set; }
        public long? idmarhala { get; set; }
        public long? idsaf { get; set; }
        public string halet_eltaleb { get; set; }
        public string elrakam_elkawmy { get; set; }
        public DateTime? tarehk_elmelad { get; set; }
        public string elsen_elhaly { get; set; }
        public string elsen_awel_october { get; set; }
        public string rakeam_elhatef_elmanzely { get; set; }
        public string rakam_elmobail { get; set; }
        public string elmadrasa_elmehawel_menha { get; set; }
        public string esm_waley_amer_taleb { get; set; }
        public string wazefet_waley_amer { get; set; }
        public string email_waley_amer_taleb { get; set; }
        public string rakam_kawmy_waly_amer_taleb { get; set; }
        public string elmoahel_ederasy_lwaly_amer_taleb { get; set; }
        public string rkam_tawarek { get; set; }
        public string username { get; set; }
        public DateTime? actiondate { get; set; }
        public string relatef { get; set; }
        public long? idfasl { get; set; }
        public bool? flag { get; set; }
        public byte[] StuPIC { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string StudyYear { get; set; }
        public int? AcadmyCode { get; set; }
        public string enName { get; set; }
        public byte[] elmeladpic { get; set; }
        public string mothername { get; set; }
        public string motherlern { get; set; }
        public string hometel { get; set; }
        public string mothejoptel { get; set; }
        public string motherjop { get; set; }
        public string mothermail { get; set; }
    }
}
