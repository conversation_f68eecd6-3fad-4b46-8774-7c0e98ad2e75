using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(Extract_ID), nameof(Line_NO))]
public class Supplier_Extract_Items
{
	public int Extract_ID {get;set;}
	public int Line_NO {get;set;}
	public string ItemTendr_NO {get;set;}
	public string Details {get;set;}
	public string UnitName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quntity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quantity_Prev {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal QuantityNow {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal QuantityTotal {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal SarfPercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalMony {get;set;}

	public string Notes {get;set;}
	public double CurrentValue {get;set;}
	public double TotalValue {get;set;}
	public double ReservePrevious {get;set;}
	public double CurrentReserve {get;set;}
	public double TotalReverse {get;set;}
	public double CurrentDiscount {get;set;}
	public double PrevoiusDiscount {get;set;}
	public double TotalDiscount {get;set;}
	public double NetItem {get;set;}
	public double AdvancePaymentPercent_Line {get;set;}
	public double AdvancePayment_line {get;set;}
	public double Insurance_BusinessPercent_LIne {get;set;}
	public double Insurance_Business_Line {get;set;}
	public double DeductPercent_Line {get;set;}
	public double Deduct_Line {get;set;}
	public double FinalNet {get;set;}
	public double VatPercentLine {get;set;}
	public double VatValueLine {get;set;}
	public double GnlTaxPercent {get;set;}
	public double GnlTaxValueLine {get;set;}
}