using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class StorTakyeeem
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public int GINDEX {get;set;}
	public int GITEMCODE {get;set;}
	public string GITEMNAME {get;set;}
	public string GUNIT {get;set;}
	public double GRASED {get;set;}
	public double GPRICE {get;set;}
	public double GPRICE_Val {get;set;}
	public double GHproceCome {get;set;}
	public double GHproceCome_Val {get;set;}
	public double Glow_Price_Come {get;set;}
	public double Glow_Price_Come_val {get;set;}
	public double Glast_PriceCome {get;set;}
	public double Glast_PriceCome_val {get;set;}
	public double GComAvareg {get;set;}
	public double GComAvareg_val {get;set;}
	public double GPrice_Sale {get;set;}
	public double GPrice_Sale_val {get;set;}
	public double GHieh_Price_Sale {get;set;}
	public double GHieh_Price_Sale_val {get;set;}
	public double GLow_Price_Sale {get;set;}
	public double GLow_Price_Sale_val {get;set;}
	public double GLast_Price_Sale {get;set;}
	public double GLast_Price_Sale_val {get;set;}
	public double GCome_Frst {get;set;}
	public double GCome_Frst_val {get;set;}
	public double GAvrag_Sale {get;set;}
	public double GAvrag_Sale_val {get;set;}
	public double Gfriststock {get;set;}
	public double Gfriststock_val {get;set;}
	public int GSOTRID {get;set;}
	public string GSTORENAME {get;set;}
}