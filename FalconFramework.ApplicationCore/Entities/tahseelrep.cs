//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(InvoiceNo), nameof(tax))]
    public partial class tahseelrep
    {
        public string customer_name { get; set; }
        public long InvoiceNo { get; set; }
        public DateTime? idate { get; set; }
        public DateTime? SuplayDate { get; set; }
        public int? TahseelModa { get; set; }
        public DateTime? CollectedDate { get; set; }
        public int? CollectedDay { get; set; }
        public DateTime? tahseldate { get; set; }
        public string TahseelNote { get; set; }
        public string Tahseel { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        public int saltax { get; set; }
        public int generaltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string invtype { get; set; }
        public string servicetype { get; set; }
        public long? Journal { get; set; }
        public string custtime { get; set; }
        public string custVacaiotn { get; set; }
        public string paystate { get; set; }
        public string waypay { get; set; }
        public DateTime? Fallowdate { get; set; }
        public string ChekDate { get; set; }
        public long? ChekNO { get; set; }
        public string BankName { get; set; }
        public bool? TaxDes { get; set; }
        public bool? TaxDoc { get; set; }
        public string UserColletMony { get; set; }
        public long? Isal_NO { get; set; }
        public long? Defrent_Collect { get; set; }
        public Nullable<double> Isal_Vale { get; set; }
        public string Mandop_Name { get; set; }
        public string tax { get; set; }
        public Nullable<double> MonyStay { get; set; }
        public bool? Selected { get; set; }
        public Nullable<double> NewCollected { get; set; }
        public Nullable<double> Diffrent { get; set; }
        public long? AccountID { get; set; }
        public string AccountName { get; set; }
        public long? CustomerID { get; set; }
        public long? CollectedJournal { get; set; }
        public int? MandopID { get; set; }
        public int? invtypeID { get; set; }
    }
}
