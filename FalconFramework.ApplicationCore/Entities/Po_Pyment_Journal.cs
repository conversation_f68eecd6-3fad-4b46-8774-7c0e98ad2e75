
using Microsoft.EntityFrameworkCore;

[<PERSON><PERSON><PERSON>(nameof(PayNO), nameof(PO_NO))]
public class Po_Pyment_Journal
{
	public long PayNO {get;set;}
	public long PO_NO {get;set;}
	public string Vender_Name {get;set;}
	public double Value {get;set;}
	public DateTime Action_Date {get;set;}
	public string PayMentType {get;set;}
	public long Journal {get;set;}
	public string AccName {get;set;}
	public long PO_Payments_ID {get;set;}
	public string DocNO {get;set;}
	public double VAT {get;set;}
	public double VATPercent {get;set;}
}