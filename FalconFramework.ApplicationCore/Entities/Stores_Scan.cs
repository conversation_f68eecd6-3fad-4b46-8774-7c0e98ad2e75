

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Stores_Scan
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long GardID { get; set; }
        public DateTime? ScanDate { get; set; }
        [Column("StoreID")]
        public long warehouseId { get; set; }

        [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }

        public int? year { get; set; }
        public string notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public int? ConsumerID { get; set; }
        public bool? Lokked { get; set; }

        public virtual ICollection<Stores_Scan_items> Stores_Scan_items { get; set; }

        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }

    }
}
