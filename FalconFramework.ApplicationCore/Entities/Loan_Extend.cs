//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Loan_Extend
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public int? Letter_ID { get; set; }
        public string Collateral_NO { get; set; }
        public string Notes { get; set; }
        public DateTime? Date_Strat { get; set; }
        public DateTime? Date_End { get; set; }
        public Nullable<double> commission { get; set; }
        public long? KaidID { get; set; }
        public long? Journal { get; set; }
        public int? OstazID { get; set; }
        public long? CostID { get; set; }
        public int? CurrnceID { get; set; }
        public DateTime? ActionDate { get; set; }
    }
}
