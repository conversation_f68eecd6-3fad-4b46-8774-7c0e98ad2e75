using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class MOVES
{

    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int InvoiceNo {get;set;}
	public DateTime MOVE_DATE {get;set;}
	public TimeSpan aTime {get;set;}
	public string STORE_STO_Name {get;set;}
	public string TREASURIES_TRE_Name {get;set;}
	public int ITEMS_ITEM_CODE {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string Curns_Name {get;set;}
	public string NameClient {get;set;}
	public string NameEmp {get;set;}
	public string PaymentWay {get;set;}
	public int Q {get;set;}
	public int TotalBefore {get;set;}
	public int TotalAfter {get;set;}
	public string ITEMS_Units {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPriceAfter {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aValTaxSell {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aPirTaxSell {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aValTaxGeneral {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aPirTaxGeneral {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPriceBefore {get;set;}
	public string SN {get;set;}
	public int InvoiceNoDet {get;set;}
	public int ItemNo {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Fright {get;set;}
	public string MandobName {get;set;}
	public string NameClientCatigroy {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}