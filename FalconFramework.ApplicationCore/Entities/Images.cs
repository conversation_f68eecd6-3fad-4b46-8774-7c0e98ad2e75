

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Images
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int ImageID { get; set; }
        public string ImageName { get; set; }
        public DateTime? DateAdd { get; set; }
        public int? ProductID { get; set; }
    }
}
