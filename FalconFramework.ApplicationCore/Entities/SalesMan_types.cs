

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SalesMan_types
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long TypeCode { get; set; }
        public string TypeName { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string TypeName_EN { get; set; }
        public virtual IEnumerable<customer_emp> Salespersons { get; set; }
}
}
