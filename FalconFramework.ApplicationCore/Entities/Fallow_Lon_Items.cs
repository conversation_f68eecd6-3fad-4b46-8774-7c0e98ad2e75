
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(LonID), nameof(itemid))]
public class Fallow_Lon_Items
{
	public long LonID {get;set;}
	public long itemid {get;set;}
	public double Q {get;set;}
	public double Qbak {get;set;}
	public double Qtotal {get;set;}
	public double QDiff {get;set;}
	public long InvoiceNo {get;set;}
	public long ClientID {get;set;}
	public DateTime ActionDate {get;set;}
	public long Journal {get;set;}
	public long StoreId {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
	public int Period {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalAmount {get;set;}
}