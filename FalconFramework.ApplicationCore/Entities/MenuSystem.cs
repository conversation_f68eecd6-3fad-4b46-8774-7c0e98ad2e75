

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class MenuSystem
    {
        public int ID { get; set; }
        public int? parentID { get; set; }
        public string MenuCode { get; set; }
        public string SystemCode { get; set; }
        public string menu_title { get; set; }
        public string menu_Name { get; set; }
        public byte[] menu_image { get; set; }
        public bool? Ext { get; set; }
        public bool? HaveTrans { get; set; }
        public bool? MainRote { get; set; }
        public string PageCode { get; set; }
        public string PageName { get; set; }
        public string TableName { get; set; }
        public string SelectedAccount { get; set; }
        public string Deleted { get; set; }
        public string PageUrl { get; set; }
        public string ImageUrl { get; set; }
    }
}
