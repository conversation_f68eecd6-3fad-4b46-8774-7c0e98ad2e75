

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class room
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int code_room { get; set; }
        public string number_room { get; set; }
        public int? code_level { get; set; }
        public int? code_hospital { get; set; }
        public int? code_roof { get; set; }
        public int? number_bed { get; set; }
        public bool? room_flage { get; set; }
        public bool? emptyroom { get; set; }
        public int? code_status { get; set; }
        public bool? it_Reserv { get; set; }
        public string RoomStates { get; set; }
        public string Housekeeping_Status { get; set; }
    }
}
