//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class PayMentMethod_Invoice
    {
        [Key]
        public int ID { get; set; }
        public long? InvoiceID { get; set; }
        public long? BankID { get; set; }
        public string Payment_method { get; set; }
        public Nullable<double> PayValue { get; set; }
        public string SwapCard { get; set; }
        public long? AccID { get; set; }
        public string AccName { get; set; }
        public bool? IsTax { get; set; }
        public int? AccType { get; set; }
        public bool? JornalPosted { get; set; }
        public DateTime? Adate { get; set; }
    }
}
