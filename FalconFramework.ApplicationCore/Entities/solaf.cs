//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class solaf
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long solafid { get; set; }
        public long? emp_id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? solafval { get; set; }
        public DateTime? solafdate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? solafDiscount { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
