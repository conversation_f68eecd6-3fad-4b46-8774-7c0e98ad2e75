//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("ItemCategory")]
    public partial class Item_Category
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int ItemCategory { get; set; }
        public string Name { get; set; }
        public bool? Flag { get; set; }
        public DateTime? DateAdd { get; set; }
    }
}
