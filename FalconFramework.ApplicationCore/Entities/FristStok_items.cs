
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(FSID), nameof(serial))]
    public partial class FristStok_items
    {
        public long FSID { get; set; }
        public int? serial { get; set; }
        public int? itemid { get; set; }
        public string Unite { get; set; }
        public Nullable<double> Price { get; set; }
        public Nullable<double> Quantity { get; set; }
        public Nullable<double> total { get; set; }
        public string SN { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bounas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defualt_ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostAllItemOut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maksap { get; set; }
        public int? SaleUnitID { get; set; }
        public string VariantName { get; set; }
    
        public virtual FristStok FristStok { get; set; }
    }
}
