

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class jobs
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long jobid { get; set; }
        public string jname { get; set; }
        public long? departmentid { get; set; }
        public string gm { get; set; }
        public string supjop { get; set; }
        public string supgm { get; set; }
        public string describe { get; set; }
        public string respons { get; set; }
        public string jtermes { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
