//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_Request
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public long? EmpCode { get; set; }
        public string EmpName { get; set; }
        public DateTime? Date { get; set; }
        public string FromPlace { get; set; }
        public string ToPlace { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Back { get; set; }
        public int? TikitNO { get; set; }
        public int? Days { get; set; }
        public string Notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
