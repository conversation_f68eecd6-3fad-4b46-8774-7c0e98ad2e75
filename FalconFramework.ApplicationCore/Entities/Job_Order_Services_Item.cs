//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Job_Order_Services_Item
    {
        [Key]
        public long Job_order_Services_Item { get; set; }
        public string Order_Id { get; set; }
        public string Services_Type { get; set; }
        public string Services_No { get; set; }
        public string Item_Resourse { get; set; }
        public string Location_Code { get; set; }
        public string Unit_of_Mear { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Cost { get; set; }
        public int? Quantity_To_Shipped { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Sale_Price { get; set; }
        public int? Discount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Services_Net { get; set; }
    }
}
