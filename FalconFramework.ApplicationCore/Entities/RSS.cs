

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RSS
    {
        
        public int id { get; set; }
        public string @object { get; set; }
        public DateTime? dt1 { get; set; }
        public DateTime? dt2 { get; set; }
        public bool? del { get; set; }
        public bool? daily { get; set; }
        public bool? monthly { get; set; }
        public bool? nothing { get; set; }
        public string note { get; set; }
        public string dt { get; set; }
        public int? hour { get; set; }
        public int? beginhour { get; set; }
    }
}
