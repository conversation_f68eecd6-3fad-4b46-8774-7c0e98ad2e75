//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class MobilAppInfo
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        public string AppTitel { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public string AppSubTitel { get; set; }
        public string Address { get; set; }
        public string Logo { get; set; }
        public string OneSignalAppId { get; set; }
        public string OneSignalAppResetKey { get; set; }
        public string OneSignalUserAut { get; set; }
        public string OneSignalAppId_IOS { get; set; }
        public string OneSignalAppResetKey_IOS { get; set; }
        public string OneSignalUserAut_IOS { get; set; }
        public string facebook { get; set; }
        public string instagram { get; set; }
        public string pinterest { get; set; }
        public string twitter { get; set; }
        public string google_plus { get; set; }
        public string Currancey { get; set; }
        public string AppTitel2 { get; set; }
        public string GMail { get; set; }
        public string GMailPass { get; set; }
        public string SiteUrl { get; set; }
    }
}
