

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class purchase_order
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long order_no { get; set; }
        public string depart { get; set; }
        public string Manager { get; set; }
        public string employee { get; set; }
        public DateTime? order_date { get; set; }
        public string order_Time { get; set; }
        public string GmSignature { get; set; }
        public string StorSignature { get; set; }
        public string purshasingSignature { get; set; }
        public bool? flag { get; set; }
        public DateTime? action_date { get; set; }
        public string userName { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string OrderType { get; set; }
        public string CustomerName { get; set; }
        public string ContactPerson { get; set; }
        public string TenderNO { get; set; }
        public DateTime? DateTender { get; set; }
        public string TenderTamen { get; set; }
        public string UsersWilMake { get; set; }
        public string Br_Notes { get; set; }
        public string DocNO { get; set; }
        public string Others { get; set; }
        public string PMO_Notes { get; set; }
        public int? MyDepartID { get; set; }
        public bool? Signed { get; set; }
        public string GNlNote { get; set; }
        public string MalyaNotes { get; set; }
        public string AmrShera_ID { get; set; }
        public int? ProjectID { get; set; }
    }
}
