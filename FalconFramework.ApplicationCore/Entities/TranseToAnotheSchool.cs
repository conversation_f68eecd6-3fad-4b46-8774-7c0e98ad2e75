//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TranseToAnotheSchool
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int TID { get; set; }
        public int? StuID { get; set; }
        public string TimeInSchool { get; set; }
        public string SchoolTo { get; set; }
        public string WhayTranse { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rased { get; set; }
        public string Book { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        public string GMSigne { get; set; }
        public string Confirem { get; set; }
        public DateTime? ConfDate { get; set; }
    }
}
