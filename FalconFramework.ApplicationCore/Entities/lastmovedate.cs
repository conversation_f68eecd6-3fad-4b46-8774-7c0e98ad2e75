

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class lastmovedate
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int? di { get; set; }
        public DateTime lastdate { get; set; }
        public int? ProunchID { get; set; }
    }
}
