﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class WhatsappSetting
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ID { get; set; }
        public string Name { get; set; }
        public string AccessToken { get; set; }
        public string BusinessAccount_Id { get; set; }
        public string PhoneNumber_Id { get; set; }
        public string Business_Id { get; set; }
        public bool IsPrimary { get; set; }
        public string DefaultReceiptNumber { get; set; }
    }
}
