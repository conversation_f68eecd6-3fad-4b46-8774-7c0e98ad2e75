

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Talab_Sarf
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long SarfID { get; set; }
        public int? Customer_id { get; set; }
        public long? InvNO { get; set; }
        [Column("store_id")]
        public long warehouseId { get; set; }
        public DateTime? Sarfdate { get; set; }
        public int? year { get; set; }
        public string notes { get; set; }
        [Column("EZnTypeID")]
        public int EznTypeId { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public long? InVUsed { get; set; }
        public int? ProjectID { get; set; }
        public bool? Signed { get; set; }
        public string SigneNotes { get; set; }
        public string UserSigend { get; set; }
        public DateTime? SigneDateTime { get; set; }
        public bool? Lokked { get; set; }

        public virtual ICollection<Talab_Sarf_Items> ReceiptsRequest_items { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
        public virtual EznType EznType { get; set; }

    }
}
