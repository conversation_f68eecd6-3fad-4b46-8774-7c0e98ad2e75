//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Projects
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Pproject_ID { get; set; }
        public string Project_Name { get; set; }
        public int? cat_id { get; set; }
        public long? Project_NO { get; set; }
        public string Project_Code { get; set; }
        public string project_State { get; set; }
        public string Customer_Type { get; set; }
        public long? Customer_id { get; set; }
        public string Custome_Name { get; set; }
        public string Contact_Person { get; set; }
        public DateTime? Date_start { get; set; }
        public DateTime? Date_end { get; set; }
        public string Emp_Responsable { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
