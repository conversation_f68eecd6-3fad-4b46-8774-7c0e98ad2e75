

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RptTrailbalanceAll
    {
        public bool? ISmain { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long? AccCode { get; set; }
        public string AccName { get; set; }
        public int? ACC_Type_ID { get; set; }
        public string Type_Name { get; set; }
        public int? ClassificationId { get; set; }
        public string ClassificationName { get; set; }
        public int? AccountGroup_ID { get; set; }
        public string AccountGroup_Name { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal balanceMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal BalanceDaen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal StartMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal StartDaen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal BeforMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal BeforDaen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Daen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal totalMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal totalDaen { get; set; }

        public long? SortID { get; set; }
        public string N1 { get; set; }
        public string N2 { get; set; }
        public string N3 { get; set; }
        public string N4 { get; set; }
        public string N5 { get; set; }
        public string N6 { get; set; }
        public string N7 { get; set; }
        public string N8 { get; set; }
        public string N9 { get; set; }
        public string N10 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? balance_Maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Balance_Daen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevBalanceMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevBalanceDaen { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevTotalMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevTotalDaen { get; set; }
    }
}
