using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
    [Table("ManufacturingQulityControl")]
    public class ManufacturingQulityControl
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public int? CategoriesId { get; set; }
        public int? SubCategoryId { get; set; }
        public long? ProductId { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public string? Instructions { get; set; }

        [Column(TypeName = "nvarchar(255)")]
        public string? Title { get; set; }

        public int? TypeId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Norm { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ToleranceFrom { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ToleranceTo { get; set; }
    }
}
