//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class invoiceoutitem_Guardians
    {
        public long ID { get; set; }
        public int invoiceout_Guardians_id { get; set; }
        public int? serial { get; set; }
        public long? Customer_id { get; set; }
        public int? GuardCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GuardPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GuardTotalPrice { get; set; }
        public int? SupervisorCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SupervisorPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SupervisorTotalPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VehiclesTotalPrice { get; set; }
        public int? VehiclesCount { get; set; }
        public int? Contact_ID { get; set; }
    }
}
