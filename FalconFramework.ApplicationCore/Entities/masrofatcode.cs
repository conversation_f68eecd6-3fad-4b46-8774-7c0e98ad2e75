

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class masrofatcode
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long masrofid { get; set; }
        public string mosrofname { get; set; }
        public string masroftype { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
