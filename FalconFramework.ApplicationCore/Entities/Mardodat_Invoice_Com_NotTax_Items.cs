
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(serialitem))]

public class Mardodat_Invoice_Com_NotTax_Items
{
	public long InvoiceNo {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
	public double ponas_percent {get;set;}
	public double ponsa_val {get;set;}
	public double descount_percent {get;set;}
	public double descount_val {get;set;}
	public double total_quantity {get;set;}
	public double ItemCost {get;set;}
	public double ItemMaksab {get;set;}
	public double DefaltCost {get;set;}
	public long CostId {get;set;}
	public double Rate {get;set;}
	public double Unit_Quantity {get;set;}
	public double Unit_Price {get;set;}
	public double Unit_Balance {get;set;}
	public double CostAllItemOut {get;set;}
	public double Defualt_ItemCost {get;set;}
	public int SaleUnitID {get;set;}
	public double UnitCost {get;set;}
	public int VariantNameId {get;set;}
	public int VariantValueId {get;set;}
}