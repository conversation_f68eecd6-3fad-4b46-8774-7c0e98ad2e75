
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(DefaulID), nameof(itemid))]
public class ItemContent
{
	public long DefaulID {get;set;}
	public long itemid {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
	public int unitCode {get;set;}
	public int GradeId {get;set;}
}