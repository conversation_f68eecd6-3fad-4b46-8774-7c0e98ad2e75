

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Talab_Edafa
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long EdafaID { get; set; }
        public int? suppliers_id { get; set; }
        public long? INVNO { get; set; }
        [Column("store_id")]
        public long warehouseId { get; set; }
        public DateTime? Edafadate { get; set; }
        public int? year { get; set; }
        public string notes { get; set; }
        [Column("EZnTypeID")]
        public int EznTypeId { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public bool? IsUsed { get; set; }
        public bool? Signed { get; set; }
        public string SigneNotes { get; set; }
        public string UserSigend { get; set; }
        public DateTime? SigneDateTime { get; set; }

        public virtual ICollection<Talab_Edafa_Items> DeliveryRequest_items { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
        public virtual EznType EznType { get; set; }

    }
}
