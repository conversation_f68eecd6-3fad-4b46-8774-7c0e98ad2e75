
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class Item
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Item_Id { get; set; }

        [Column("itm_code2")]
        public string ShortCode { get; set; }
        public string Barcode { get; set; }
        [Column("itm_ismedicine")]
        public int? IsStorable { get; set; }
        [Column("Item_Unit_Id")]
        public int UnitId { get; set; }

        [Column("Item_unit2")]
        public int? SecondaryUnitId { get; set; }

        [Column("Item_unit3")]
        public int? ThirdUnitId { get; set; }

        [Column("EqulUnit", TypeName = "decimal(18,2)")]
        public decimal? SecondaryUnitRate { get; set; }

        [Column("EqulUnit2",TypeName = "decimal(18, 2)")]
        public decimal? ThirdUnitRate { get; set; }

         [Column("UnitEqualed")]
        public string SecondaryUnit { get; set; }

        [Column("Item_Gro_Id")]
        public int CategoryId { get; set; }

        [Column("Item_Gro_Sub_Id")]
        public int SubCategoryId { get; set; }

        [Column("TradeTypeID")]
        public int TradeClassificationID { get; set; }


        [Column("Item_Name")]
        public string NameAr { get; set; }

         [Column("Item_Name_AR")]
        public string NameEn { get; set; }

         [Column("itm_request_limit")]
        public int? ItmRequestLimit { get; set; }
       [Column("MaxQ")]
        public int? MaxQuantity { get; set; }

[       Column("MINQ")]
        public int? MinQuantity { get; set; }

        [Column("price")]
        public double? Price { get; set; }
        [Column("gomla")]
        public double? WholesalePrice { get; set; }

       [Column("FixPrice")]
       public bool? PruchasePriceFexed { get; set; }

        public int? Tax { get; set; }
       [Column("HameshRebh")]
        public int? ProfitMargin { get; set; }

        [Column("aNotes")]
        public string AdditionalNotes { get; set; }

       [Column("actiondate")]
public DateTime? ActionDate { get; set; }

        public string UserName { get; set; }
        public bool? flag { get; set; }
        public byte[] Pic { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }

        [Column("PriceFexed")]
        public bool? IsFixedPrice { get; set; }
       [Column("PriceCome")]
public double? PurchasePrice { get; set; }
        [Column("hgomla")]
public double? WholesaleMargin { get; set; }

        [Column("hmonaksat")]
public double? TenderMargin { get; set; }

        [Column("pricemonaksat")]
public double? TenderPrice { get; set; }

       [Column("hbig")]
public double? HighestPriceMargin { get; set; }

[Column("pricbig")]
public double? HighestPrice { get; set; }

       [Column("hmedum")]
public double? MediumPriceMargin { get; set; }

[Column("pricemedum")]
public double? MediumPrice { get; set; }

       [Column("hlow")]
public double? LowPriceMargin { get; set; }

[Column("pricelow")]
public double? LowPrice { get; set; }

        public string PartNo { get; set; }
        public double? DescountPercent { get; set; }
       [Column("PonasPercent")]
public double? BonusPercentage { get; set; }

        [Column("com_id",TypeName = "decimal(18, 2)")]

        public decimal? ManufacturerId { get; set; }
        [Column("itm_com_code")]
public string ManufacturerItemCode { get; set; }

       [Column("itm_has_expire")]
public int? HasExpiryDate { get; set; }

       [Column("itm_active")]
public bool? IsActive { get; set; }

       [Column("itm_freez")]
public bool? IsFrozen { get; set; }

        public string itm_scientific_n1 { get; set; }
        public string itm_scientific_n2 { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_scientific_group_id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_usage_manner_id { get; set; }
        public int? itm_isprev { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_itf_id { get; set; }
        public int? itm_stop_sell { get; set; }
        public int? itm_stop_pur { get; set; }
        public int? itm_print_barcode { get; set; }
        public int? itm_allow_discount { get; set; }
        public Nullable<double> itm_max_disc_per { get; set; }
        public Nullable<double> itm_max_disc_val { get; set; }
        public int? itm_print_name { get; set; }
        public int? itm_sales_avreage_period { get; set; }
        public int? itm_srvc { get; set; }
        public int? itm_origin { get; set; }
        public int? itm_isShortage { get; set; }
        public int? itm_favourite { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_location { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_default_limit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_purchase_unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_sell_unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? itm_max_disc_value_NotUsed { get; set; }
        public string StorePlace { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Waigt { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ChargDesc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GomrokDesc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GomrakFinshDesc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? OtherDesc { get; set; }
        public string OntherNameDesc { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Shehada { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? OtherShehada { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Boxcost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tabkher { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? InernalCharg { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Purchase { get; set; }
        public string Quntity_Price { get; set; }
        public byte[] Pic1 { get; set; }
        public Nullable<double> Width { get; set; }
        public Nullable<double> highet { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Thikness { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CountInBox { get; set; }
        public string ImagePha { get; set; }
        public bool? IsOffer { get; set; }
        public bool? Always_in_stock { get; set; }
        public string ImagePha2 { get; set; }
        public string Barcode2 { get; set; }
        public string Barcode3 { get; set; }
        public string ItemPrintName { get; set; }
        public string ItemPrintNameEN { get; set; }
        public long? AccountCode { get; set; }
        public bool? IsWeight { get; set; }
        public Nullable<double> AnotherVat { get; set; }
        public string etaCodeType { get; set; }
      
    public string WooCommerceCode { get; set; }
    public string SallaCode { get; set; }
    public string ZidCode { get; set; }
    public string EtaCodeType2 { get; set; }
    public string EtaCodeType3 { get; set; }
    public string EtaCodeType4 { get; set; }
    public string EtaCodeType5 { get; set; }
    public string EtaCodeType6 { get; set; }
    public string EtaCodeType7 { get; set; }
    public string EtaCodeType8 { get; set; }
    public string EtaCodeType9 { get; set; }
    public string EtaCodeType10 { get; set; }
    public string EtaCode2 { get; set; }
    public string EtaCode3 { get; set; }
    public string EtaCode4 { get; set; }
    public string EtaCode5 { get; set; }
    public string EtaCode6 { get; set; }
    public string EtaCode7 { get; set; }
    public string EtaCode8 { get; set; }
    public string EtaCode9 { get; set; }
    public string EtaCode10 { get; set; }
    public string VAT_Type { get; set; }
    public string TaxCategoryReasonCode { get; set; }
    public string TaxCategoryReason { get; set; }
    public bool Taxable { get; set; }



    }
}
