

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [<PERSON>Key(nameof(InvoiceNo), nameof(serialitem))]
    public partial class byan_item
    {
        public long InvoiceNo { get; set; }
        public long serialitem { get; set; }

        [Column("itemid")]
        public long productId { get; set; }

        public string ITEMS_ITEM_NAME { get; set; }
        public string unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PriceOne { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalPrice { get; set; }
        public string SN { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bonas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? despecent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemDescount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? AllQuantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemMaksab { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DefaltCost { get; set; }
        public long? CostId { get; set; }
        public string oparateno { get; set; }
        public string rased { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bounas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defualt_ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostAllItemOut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maksap { get; set; }
        public int? SaleUnitID { get; set; }
        public Nullable<double> Item_Unit_Id { get; set; }
        public Nullable<double> ValItemTax { get; set; }
        public Nullable<double> DefualtVAT { get; set; }
        public bool? Reserved { get; set; }
        public Nullable<double> orginalPrice { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }

        public virtual Byan Byan { get; set; }
        public virtual Product Product { get; set; }
    }
}
