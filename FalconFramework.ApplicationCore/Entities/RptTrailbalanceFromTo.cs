

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RptTrailbalanceFromTo
    {
        public bool? ISmain { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long AccCode { get; set; }
        public string AccName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rseddebit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rsedcredit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? debit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? credit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalDebit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalCredit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Opendebit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? OpenCredit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? prevdebit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? prevcredit { get; set; }
        public long? SortID { get; set; }
        public string financial_entity { get; set; }
        public string financial_entity_Type { get; set; }
        public int? Financial_entity_TypeID { get; set; }
        public long? Financial_entity_Id { get; set; }
        public long? AccCode2 { get; set; }
        public string AccName2 { get; set; }
        public int? CaseID { get; set; }
        public int? currency { get; set; }
        public int? currencyTo { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? prefmandenbalance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? prefdaenbalance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevBalanceMaden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BrevBalanceDaen { get; set; }
    }
}
