//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class orders
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long orderid { get; set; }
        public long? orderno { get; set; }
        public DateTime? orderdate { get; set; }
        public long? Customer_id { get; set; }
        public long? commandid { get; set; }
        public string username { get; set; }
        public DateTime? action_date { get; set; }
        public long? performa_id { get; set; }
        public DateTime? performa_date { get; set; }
        public DateTime? performa_date_end { get; set; }
        public string payment_termes { get; set; }
        public string delivery_termes { get; set; }
        public string delivery_place { get; set; }
        public string shipping_type { get; set; }
        public string notes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        public string performaonly { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public bool? Signed { get; set; }
        public string SigneNotes { get; set; }
        public string UserSigend { get; set; }
        public DateTime? SigneDateTime { get; set; }
    }
}
