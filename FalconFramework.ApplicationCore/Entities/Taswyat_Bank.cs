//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Taswyat_Bank
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Id { get; set; }
        public long? Bank_id { get; set; }
        public string AccountbankId { get; set; }
        public string type { get; set; }
        public DateTime? Actiondate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? val { get; set; }
        public string notes { get; set; }
        public long? accid { get; set; }
        public string accname { get; set; }
        public bool? chart { get; set; }
        public bool? cust { get; set; }
        public bool? bank { get; set; }
        public bool? cutt1 { get; set; }
        public bool? cutt2 { get; set; }
        public bool? To_Vend { get; set; }
    }
}
