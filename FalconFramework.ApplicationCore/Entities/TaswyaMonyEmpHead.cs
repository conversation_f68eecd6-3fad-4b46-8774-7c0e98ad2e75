

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TaswyaMonyEmpHead
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long TaswyaID { get; set; }
        public long? EmpID { get; set; }
        public string HeadNote { get; set; }
        public DateTime? ActionDate { get; set; }
        public int? OstazID { get; set; }
    }
}
