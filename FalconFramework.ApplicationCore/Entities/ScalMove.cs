using System.ComponentModel.DataAnnotations.Schema;

public class Scal<PERSON>ove
{
	public long id {get;set;}
	public string carno {get;set;}
	public string maktorano {get;set;}
	public string mhafza {get;set;}
	public string cartype {get;set;}
	public string shohna {get;set;}
	public string customername {get;set;}
	public string drivername {get;set;}
	public string oparetoretype {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wcar1 {get;set;}
	public DateTime datecar1 {get;set;}
	public string timecar1 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wmaktora1 {get;set;}
	public DateTime datemaktora1 {get;set;}
	public string timemaktora1 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wcar2 {get;set;}
	public DateTime datecar2 {get;set;}
	public string timecar2 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wmaktora2 {get;set;}
	public DateTime datemaktora2 {get;set;}
	public string timemaktora2 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wcar {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wmaktora {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal wnet {get;set;}

	public string username {get;set;}
	public string wnotes {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValCard {get;set;}
}