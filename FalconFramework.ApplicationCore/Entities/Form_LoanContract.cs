//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_LoanContract
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public DateTime? Date { get; set; }
        public string DayName { get; set; }
        public string FirstParty { get; set; }
        public string RecFirst { get; set; }
        public string FirstRecordFrom { get; set; }
        public string AliasFirst { get; set; }
        public string SeconParty { get; set; }
        public string RecordSecond { get; set; }
        public string SecondRecordFrom { get; set; }
        public string AliasSecond { get; set; }
        public string Emp { get; set; }
        public string Nationality { get; set; }
        public string NoStay { get; set; }
        public string jop { get; set; }
        public string Moda { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
