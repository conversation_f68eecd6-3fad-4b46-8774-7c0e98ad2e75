using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class View_Status_Stores_Resulte
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int Item_Code {get;set;}
	public string Item_Name {get;set;}
	public string Item_Unit {get;set;}
	public int Item_Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_Total {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}