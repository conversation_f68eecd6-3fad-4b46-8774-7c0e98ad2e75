using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Jornal
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int Seyial {get;set;}
	public string Detis {get;set;}
	public long AccID {get;set;}
	public string AccName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Maden {get;set;}
	public string Daen {get;set;}
	public long costid {get;set;}
	public string CostName {get;set;}
	public int CathID {get;set;}
	public string CatchType {get;set;}
	public int FlagNo {get;set;}
	public bool Flag {get;set;}
	public long Cust_Supp_Doc_ID {get;set;}
	public int ScreenType {get;set;}
}