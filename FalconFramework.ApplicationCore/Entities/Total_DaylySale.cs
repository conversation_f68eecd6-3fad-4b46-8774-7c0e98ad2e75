using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Total_DaylySale
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int id {get;set;}
	public string Notes {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public int InvCount {get;set;}
	public double Cash {get;set;}
	public double Credit {get;set;}
	public double Visa {get;set;}
}