

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class departments
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long depart_id { get; set; }
        public string depart_name { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Short_Name { get; set; }
        public string EnglishName { get; set; }

        public int ParentId { get; set; }
        public int rowindex { get; set; }

    }
}
