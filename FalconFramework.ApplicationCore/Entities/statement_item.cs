
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(state_no), nameof(serial))]
public class statement_item
{

	public long state_no {get;set;}
	public long serial {get;set;}
	public string item {get;set;}
	public long quantity {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal total {get;set;}
	public long user_id {get;set;}
	public DateTime action_date {get;set;}
	public bool flag {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}