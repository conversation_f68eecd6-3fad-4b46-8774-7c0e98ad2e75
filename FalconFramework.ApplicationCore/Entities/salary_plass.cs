//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class salary_plass
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long plassid { get; set; }
        public DateTime? plassdate { get; set; }
        public long? emp_id { get; set; }
        public long? elawat_code_id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? plassval { get; set; }
        public string dateYear { get; set; }
        public string datemonth { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Notes { get; set; }
        public long? CostID { get; set; }
        public string CostName { get; set; }
    }
}
