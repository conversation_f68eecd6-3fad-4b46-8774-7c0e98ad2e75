

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{

   

    public partial class customer_emp
    {
        // Constructor
        public customer_emp()
        {
            // Initialize SalespersonCategory if needed
            SalespersonCategory = new SalesMan_types();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int MAndopID { get; set; }
        public string MandopName { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public bool? IsFrom_Mksab { get; set; }
        public bool? IsFrom_Egmaly { get; set; }
        public Nullable<double> PercentVaue { get; set; }
        public bool? IsFrom_Target { get; set; }
        public Nullable<double> TargetValue { get; set; }
        public Nullable<double> ServicePercent { get; set; }
        public int? UserID { get; set; }
        public byte[] MandopImag { get; set; }
        public bool? srv_IsFrom_Mksab { get; set; }
        public bool? srv_IsFrom_Egmaly { get; set; }
        public string EnName { get; set; }
        public long? EmpCode { get; set; }

        public long? SalesManTypeId { get; set; }

        [ForeignKey("SalesManTypeId")]
        public virtual SalesMan_types SalespersonCategory { get; set; }

       
        

        

    }
}
