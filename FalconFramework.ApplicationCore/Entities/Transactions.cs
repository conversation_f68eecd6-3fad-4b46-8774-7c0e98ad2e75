

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Transactions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Trans_No { get; set; }
        public int Trans_Emp_no { get; set; }
        public DateTime Trans_Date { get; set; }
        public string Trans_User { get; set; }
        public int? Trans_Clock { get; set; }
        public Nullable<double> Trans_Fn { get; set; }
        public int? N_Trans_Type { get; set; }
    }
}
