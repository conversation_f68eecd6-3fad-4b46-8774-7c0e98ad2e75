//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class mdaily
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long mdid { get; set; }
        public DateTime? datestop { get; set; }
        public string machinename { get; set; }
        public long? machiinedi { get; set; }
        public string depart_name { get; set; }
        public string descripe { get; set; }
        public string mdnotes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string SolvDescripe { get; set; }
        public DateTime? datestart { get; set; }
    }
}
