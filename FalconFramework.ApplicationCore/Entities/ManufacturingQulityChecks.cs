using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{
    [Table("ManufacturingQulityChecks")]
    [Index(nameof(ManufacturingQulityControlId))]
    [Index(nameof(ProductId))]
    public class ManufacturingQulityChecks
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public DateTime CheckDate { get; set; } = DateTime.Now;

        [Required]
        public int ManufacturingQulityControlId { get; set; }

        [ForeignKey(nameof(ManufacturingQulityControlId))]
        public virtual ManufacturingQulityControl ManufacturingQulityControl { get; set; }

        public int? ProductId { get; set; }

        [MaxLength(50)]
        public string? CheckResult { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MeasuredValue { get; set; }

        public bool? IsPassed { get; set; }

        public string? Notes { get; set; }
        [Column(TypeName = "nvarchar(255)")]
        public string? CreatedBy { get; set; }

        public long? DeleveryID { get; set; }
    }
}
