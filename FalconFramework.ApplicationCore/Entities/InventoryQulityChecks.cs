using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{
    [Table("InventoryQulityChecks")]
    [Index(nameof(InventoryQulityControlId))]
    [Index(nameof(ProductId))]
    public class InventoryQulityChecks
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public DateTime CheckDate { get; set; } = DateTime.Now;

        [Required]
        public int InventoryQulityControlId { get; set; }

        [ForeignKey(nameof(InventoryQulityControlId))]
        public virtual InventoryQulityControl InventoryQulityControl { get; set; }

        public int? ProductId { get; set; } 

        [MaxLength(50)]
        public string? CheckResult { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MeasuredValue { get; set; }

        public bool? IsPassed { get; set; }

        public string? Notes { get; set; }

        [Column(TypeName = "nvarchar(255)")]
        public string? CreatedBy { get; set; }

        public long? DeleveryID { get; set; }
    }
}
