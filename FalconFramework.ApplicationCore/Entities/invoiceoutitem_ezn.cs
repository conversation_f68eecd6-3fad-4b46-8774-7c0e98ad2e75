using FalconFramework.ApplicationCore.Entities;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[PrimaryKey(nameof(InvoiceNo), nameof(serialitem))]
public class invoiceoutitem_ezn
{
    [ForeignKey("invoiceout_ezn")]
    public long InvoiceNo { get; set; }

    public long serialitem { get; set; }
    [Column("itemid")]
    public long productId { get; set; }
    public string ITEMS_ITEM_NAME { get; set; }
    public string unit { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne { get; set; }
    public int Q { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice { get; set; }
    public string SN { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Itemtax { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Valitemtax { get; set; }

    public virtual invoiceout_ezn invoiceout_ezn { get; set; }
    public virtual Product Product { get; set; }
}