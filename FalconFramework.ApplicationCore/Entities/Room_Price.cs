//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Room_Price
    {
        [Key]
        public int price_ID { get; set; }
        public int? Room_ID { get; set; }
        public int? Room_No { get; set; }
        public int? code_level { get; set; }
        public int? code_hospital { get; set; }
        public int? RoomType_ID { get; set; }
        public int? Rate_Code { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        public int? Curunce_ID { get; set; }
    }
}
