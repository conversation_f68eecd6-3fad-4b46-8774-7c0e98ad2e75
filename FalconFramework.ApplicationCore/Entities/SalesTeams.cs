using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SalesTeams
    {
        [Key]
        public int id { get; set; }
        public string name { get; set; }
        public bool use_leads { get; set; }
        public bool use_opportunities { get; set; }
        public string email { get; set; }
        public int? user_id { get; set; }
    }
}
