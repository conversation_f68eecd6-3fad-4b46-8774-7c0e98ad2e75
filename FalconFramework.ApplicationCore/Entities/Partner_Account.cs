//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Partner_Account
    {
        [Key]
        public long id { get; set; }
        public long venderid { get; set; }
        public string year { get; set; }
        public string actionname { get; set; }
        public long? invno_ormony_id { get; set; }
        public DateTime? movedate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daen { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        public string Notes { get; set; }
        public int? CurID { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
        public long? Journal { get; set; }
        public string DocNo { get; set; }
        public bool? IsStartedBalance { get; set; }
    }
}
