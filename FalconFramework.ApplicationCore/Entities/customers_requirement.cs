//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class customers_requirement
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long requierid { get; set; }
        public long? Customer_id { get; set; }
        public DateTime? requirement_date { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }
        public int? departID { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string MandopName { get; set; }
    }
}
