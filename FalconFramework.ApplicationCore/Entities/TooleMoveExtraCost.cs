//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TooleMoveExtraCost
    {
        [Key]
        public long ID { get; set; }
        public long? MachineMoveID { get; set; }
        public DateTime? DateExtra { get; set; }
        public Nullable<double> ExtraAmountLine { get; set; }
        public string ExtracostData { get; set; }
        public string ExtraCostNote { get; set; }
    }
}
