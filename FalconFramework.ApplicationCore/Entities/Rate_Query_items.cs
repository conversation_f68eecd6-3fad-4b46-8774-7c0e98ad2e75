using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Rate_Query_items
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long Rate_Query_ID {get;set;}
	public string Room_type {get;set;}
	public string Room_Level {get;set;}
	public int Room_ID {get;set;}
	public string Room_No {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Room_Price {get;set;}

	public string Curns_Name {get;set;}
	public string rate_price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal lbldescountpercent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValDiscount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal txt_Price {get;set;}
	public string onlytxt {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal txtmonycome {get;set;}
	public string CmbBox {get;set;}
	public string Txt_Notes {get;set;}
	public bool cheked_Out {get;set;}
	public DateTime Arrive_Datee {get;set;}
	public int NO_Of_Dayes {get;set;}
	public DateTime Lev_Date {get;set;}
}