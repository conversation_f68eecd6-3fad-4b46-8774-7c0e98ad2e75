//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(TenderID), nameof(Srial))]
    public partial class Signed_Q_Table
    {
        public long TenderID { get; set; }
        public int? Tender_BasicID { get; set; }
        public int? tender_workID { get; set; }
        public long? FormID { get; set; }
        public long? GroupID { get; set; }
        public long? Tender_ContentID { get; set; }
        public int Srial { get; set; }
        public int? ItemCode { get; set; }
        public string ItemName { get; set; }
        public string FactoryName { get; set; }
        public string VendorName { get; set; }
        public string VendorLevel { get; set; }
        public string State { get; set; }
        public string Note { get; set; }
        public string Unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        public bool? Loked { get; set; }
        public string LokedUser { get; set; }
        public string LokedDate { get; set; }
    }
}
