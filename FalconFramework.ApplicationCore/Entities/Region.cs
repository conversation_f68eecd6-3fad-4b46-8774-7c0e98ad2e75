

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("Region")]
    public partial class Regions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int RegionID { get; set; }
        public string RegionName { get; set; }
        public int? CityID { get; set; }
        public int? CountryID { get; set; }
    }
}
