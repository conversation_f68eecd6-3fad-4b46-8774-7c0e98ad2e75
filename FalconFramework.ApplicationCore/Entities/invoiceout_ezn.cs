

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class invoiceout_ezn
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long InvoiceNo { get; set; }

        public DateTime? idate { get; set; }
        [ForeignKey("customer")]
        [Column("ClientID")]
        public long? customerId { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? saltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? generaltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string aArabicTotal { get; set; }
        public string invtype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monycome { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monystill { get; set; }
        public string notes { get; set; }
        public long? iyeer { get; set; }
        [Column("store_id")]
        public long? warehouseId { get; set; }
        public string servicetype { get; set; }
        public bool? flag { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public bool? IsUsed { get; set; }
        public string Mandop_Name { get; set; }

        public virtual ICollection<invoiceoutitem_ezn> invoiceoutitem_ezn { get; set; }

        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
        public virtual Customers customer { get; set; }

    }
}
