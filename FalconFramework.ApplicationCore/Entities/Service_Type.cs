//--------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Service_Type
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Service_ID { get; set; }
        public string Service_Type_Name { get; set; }
        public bool? flag { get; set; }
        public string Service_Type_Name_EN { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public int? invType { get; set; }
        public bool? IsRealEstate_Type { get; set; }
    }
}
