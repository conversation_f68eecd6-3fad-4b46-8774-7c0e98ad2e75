using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(serialitem))]

public class byan_item_Temb
{
	public long InvoiceNo {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Bonas {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal despecent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemDescount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal AllQuantity {get;set;}

}