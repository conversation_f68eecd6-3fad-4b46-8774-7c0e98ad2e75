﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class t_Clock_List
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int? N_ClockIdx { get; set; }
        public string? S_ClockName { get; set; }
        public int? N_Enabled { get; set; }
        public string? S_IP { get; set; }
        public int? N_Port { get; set; }
        public string? MFunction { get; set; }
    }
}
