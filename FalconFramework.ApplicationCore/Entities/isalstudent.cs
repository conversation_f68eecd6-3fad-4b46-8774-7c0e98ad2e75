//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class isalstudent
    {
        [Key]
        public long ID { get; set; }
        public int? kestNO { get; set; }
        public int? marhalaid { get; set; }
        public int? safcode { get; set; }
        public int? acadmyno { get; set; }
        public string accountname { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? valaccount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? income { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rased { get; set; }
    }
}
