using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class supplier_contact
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long serial {get;set;}
	public long suppliers_id {get;set;}
	public string contact_person {get;set;}
	public string titel_job {get;set;}
	public string mobile {get;set;}
	public string phone {get;set;}
	public string email {get;set;}
	public string department {get;set;}
	public bool flag {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}