﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    [PrimaryKey(nameof(RentID), nameof(item_index))]
    public class RentsItems
    {
        public long RentID { get; set; }
        public int item_index { get; set; }
        public long itemid { get; set; }
        public string ItemName { get; set; }
        public string itemunit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal itemprice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal renttime { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal itemquntity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal itemTotal { get; set; }
        public int UnitID { get; set; }
        public int store_id { get; set; }
    }
}
