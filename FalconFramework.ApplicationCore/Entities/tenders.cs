using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class tenders
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long tender_no {get;set;}
	public long Customer_id {get;set;}
	public DateTime tender_date {get;set;}
	public DateTime date_end {get;set;}
	public DateTime date_open_maly {get;set;}
	public DateTime date_open_fanny {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price_tender {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal vlaue_insurance {get;set;}
	public bool flag {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}