//
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(InvoiceNo), nameof(serialitem))]
    public partial class byan_item_ezn
    {
        public long InvoiceNo { get; set; }
        public long serialitem { get; set; }
        public long? itemid { get; set; }
        public string ITEMS_ITEM_NAME { get; set; }
        public string unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PriceOne { get; set; }
        public int? Q { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalPrice { get; set; }
        public string SN { get; set; }
        public bool? IsFast { get; set; }
        public bool? Isprinted { get; set; }
        public bool? Done { get; set; }
        public bool? IsHold { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bonas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemMaksab { get; set; }
        public long? CostId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostAllItemOut { get; set; }
        public int? Item_Unit_Id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defualt_ItemCost { get; set; }
        public int? SaleUnitID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maksap { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DefaltCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Itemtax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Valitemtax { get; set; }
        public long Id { get; set; }
        public bool? Canceld { get; set; }
        public Nullable<double> DefualtVAT { get; set; }
        public string VariantName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? despecent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemDescount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? orginalPrice { get; set; }
    }
}
