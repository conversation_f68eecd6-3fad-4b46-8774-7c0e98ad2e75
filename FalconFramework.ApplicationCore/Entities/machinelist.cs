
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class machinelist
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long machiinedi { get; set; }
        public long? depart_id { get; set; }
        public string depart_name { get; set; }
        public string machinename { get; set; }
        public string Balad { get; set; }
        public string machinemodel { get; set; }
        public DateTime? machinedate { get; set; }
        public string Mcolre { get; set; }
        public int? Counterfirst { get; set; }
        public string Chaseh { get; set; }
        public string Motor { get; set; }
        public string Loha { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public byte[] Image1 { get; set; }
        public byte[] image2 { get; set; }
        public byte[] image3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MachineValeu { get; set; }
        public DateTime? BrDate { get; set; }
        public string Vendorname { get; set; }
        public string Payway { get; set; }
        public string Estmarah { get; set; }
        public DateTime? EsdarEstmarahdate { get; set; }
        public string EsdarEstmarah { get; set; }
        public DateTime? EndDateEstmara { get; set; }
        public string TameenNO { get; set; }
        public DateTime? TameenEsdar { get; set; }
        public string Tameenco { get; set; }
        public DateTime? EndTamendate { get; set; }
        public string MCode { get; set; }
        public string Notes { get; set; }
        public string Statuse { get; set; }
        public int? McolreID { get; set; }
        public string NameDiscripe { get; set; }
        public bool? TaslemStatus { get; set; }
        public long? DriverID { get; set; }
        public long? CostID { get; set; }
        public int? TasneefID { get; set; }
        public int? AslCode { get; set; }
        public string OwnerName { get; set; }
        public string InsuranceType { get; set; }
        public DateTime? Check_Expir { get; set; }
        public string AlarmNotes { get; set; }
        public Nullable<double> MonthAmount { get; set; }
        public Nullable<double> DayAmount { get; set; }
    }
}
