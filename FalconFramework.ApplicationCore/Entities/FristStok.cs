

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{

    public partial class FristStok
    {

        [Key]
        public long FSID { get; set; }
        [Column("store_id")]
        public long warehouseId { get; set; }
        public DateTime? FSdate { get; set; }
        public int? year { get; set; }
        public string notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }
        [Column("COMP_ID")]
        public int companyId { get; set; }

        public long? Journal { get; set; }

        public virtual ICollection<FristStok_items> FristStok_items { get; set; }

        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }


    }
}
