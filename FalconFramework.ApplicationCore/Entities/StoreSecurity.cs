//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class StoreSecurity
    {
        [Key]
        public long ID { get; set; }
        public int? BoxID { get; set; }
        public int? UserID { get; set; }
        public bool? Effict_Stores { get; set; }
        public bool? Effict_Store_Sarf { get; set; }
        public bool? Effict_Stores_Taswya_Edafa { get; set; }
        public bool? Effict_Store_Taswya_Sarf { get; set; }
        public bool? Effict_ConvertBetweenStores { get; set; }
        public bool? Effict_Stores_From_Invoices { get; set; }
        public bool? Effict_Stores_Mardodat_Invoices { get; set; }
        public bool? Effict_Stores_From_byan { get; set; }
        public bool? Effict_Stores_Mardodat_byan { get; set; }
       
    }
}
