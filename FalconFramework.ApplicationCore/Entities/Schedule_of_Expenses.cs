//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Schedule_of_Expenses
    {
        [Key]
        public long ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MonyValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DueValue { get; set; }
        public DateTime? DueDate { get; set; }
        public string Note { get; set; }
        public long? DueJournal { get; set; }
        public long? FromAcc { get; set; }
        public long? ToAcc { get; set; }
        public bool? flag { get; set; }
        public long? PayJournal { get; set; }
        public int? UserID { get; set; }
        public long? CostID { get; set; }
        public int? FromCaseId { get; set; }
        public string FromCaseName { get; set; }
        public int? ToCaseID { get; set; }
        public string ToCaseName { get; set; }
        public long? FromAcc2 { get; set; }
        public long? ToAcc2 { get; set; }
        public string FromAccName2 { get; set; }
        public string ToAccName2 { get; set; }
    }
}
