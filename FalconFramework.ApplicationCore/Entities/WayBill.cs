//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class WayBill
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long WaybillID { get; set; }
        public int? CompanyID { get; set; }
        public int? BranchID { get; set; }
        public DateTime? date { get; set; }
        public int? UserID { get; set; }
        public string LoadingPoint { get; set; }
        public bool? LoadSeaport { get; set; }
        public bool? LoadTerminal { get; set; }
        public bool? LoadWhouse { get; set; }
        public bool? LoadAirport { get; set; }
        public string ArrivalPoint { get; set; }
        public bool? ArrivalSeaport { get; set; }
        public bool? ArrivalTerminal { get; set; }
        public bool? ArrivalWhouse { get; set; }
        public bool? ArrivalAirport { get; set; }
        public long? CustomerID { get; set; }
        public string Quantity { get; set; }
        public long? ReceivedID { get; set; }
        public long? CarrierID { get; set; }
        public long? ShipmentID { get; set; }
        public string TruckNumber { get; set; }
        public long? DriverID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TransportAmount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Demurrage { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Other { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SubTotal { get; set; }
        public DateTime? LoadArrivalDate { get; set; }
        public TimeSpan? LoadArrivalTime { get; set; }
        public DateTime? LoadDepartureDate { get; set; }
        public TimeSpan? LoadDepartureTime { get; set; }
        public DateTime? DeliveryArrivalDate { get; set; }
        public TimeSpan? DeliveryArrivalTime { get; set; }
        public DateTime? DeliveryunLoadingDate { get; set; }
        public TimeSpan? DeliveryunLoadingTime { get; set; }
    }
}
