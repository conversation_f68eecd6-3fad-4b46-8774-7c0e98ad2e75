//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Systems_Menu
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int MenuID { get; set; }
        public int SystemCode { get; set; }
        public int SupSysCode { get; set; }
        public string SystemMenuName { get; set; }
        public string ESystemMenuName { get; set; }
        public string MenuTag { get; set; }
        public string MenU_URL { get; set; }
        public bool? SSave { get; set; }
        public bool? SEdit { get; set; }
        public bool? SDelte { get; set; }
        public bool? SPrint { get; set; }
        public bool? SView { get; set; }
        public string AreaName { get; set; }
        public int? SupervisorID { get; set; }
        public int? AppCode { get; set; }
        public string Other_SystemMenuName { get; set; }
    }
}
