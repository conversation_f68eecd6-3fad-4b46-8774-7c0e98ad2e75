
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class TasnefTogary
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int TradeTypeID { get; set; }
        public string TradeType { get; set; }
        public bool? flag { get; set; }
    }
}
