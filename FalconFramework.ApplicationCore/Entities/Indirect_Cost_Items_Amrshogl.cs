
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(orderid), nameof(Item_Index))]
public class Indirect_Cost_Items_Amrshogl
{
	public long orderid {get;set;}
	public string Item_Index {get;set;}
	public string Details {get;set;}
	public string Unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
	public int TypeID {get;set;}
}