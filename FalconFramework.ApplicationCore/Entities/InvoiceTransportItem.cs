
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvID), nameof(OprationID))]
public class InvoiceTransportItem
{
	public long InvID {get;set;}
	public long OprationID {get;set;}
	public long TransportID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TValue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValDriver {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValOffice {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValClearance {get;set;}
}