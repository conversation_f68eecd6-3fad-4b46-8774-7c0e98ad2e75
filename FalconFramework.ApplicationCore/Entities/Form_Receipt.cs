//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_Receipt
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public long? EmpCode { get; set; }
        public string EmpName { get; set; }
        public DateTime? Date { get; set; }
        public string Month { get; set; }
        public string Dayname { get; set; }
        public string Inx { get; set; }
        public string DocDate { get; set; }
        public string DocName { get; set; }
        public string TypeDoc { get; set; }
        public string DocCount { get; set; }
        public string DocNotes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public bool? Selected { get; set; }
        public string FallowNotes { get; set; }
        public DateTime? FallowDate { get; set; }
        public bool? Deleverid { get; set; }
        public long? Emp_Expeditor { get; set; }
        public string process { get; set; }
    }
}
