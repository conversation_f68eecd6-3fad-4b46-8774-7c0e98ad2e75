using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class FinalBalanceForms
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public int MezaniaID {get;set;}
	public int GroupId {get;set;}
	public long FinalMenuID {get;set;}
	public long Aaccount_GroupID {get;set;}
	public int MethodID {get;set;}
	public string MethodTitele {get;set;}
	public int Switch_to_positive {get;set;}
	public int AccCase {get;set;}
}