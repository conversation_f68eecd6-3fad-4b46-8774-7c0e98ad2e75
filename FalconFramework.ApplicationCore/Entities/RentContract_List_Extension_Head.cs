

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RentContract_List_Extension_Head
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        public string UserAdd { get; set; }
        public DateTime? DateAdd { get; set; }
        public string ExtensionNote { get; set; }
    }
}
