using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

[<PERSON>Key(nameof(GardID), nameof(Item_Id))]
public class Stores_Consumer_items
{
    
    public long GardID {get;set;}
	public long Item_Id {get;set;}
	public double First_Period {get;set;}
	public double Cash_purchases {get;set;}
	public double Credit_purchases {get;set;}
	public double AllCome {get;set;}
	public double CurrentBalance {get;set;}
	public double NetOut {get;set;}
	public double Avvrage {get;set;}
	public double Total {get;set;}
}