//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Mostakhlas_Head
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long MID { get; set; }
        public long? Cust_ID { get; set; }
        public DateTime? MDate { get; set; }
        public string MOperator { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MSumValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MsumOurValue { get; set; }
        public string Notes { get; set; }
    }
}
