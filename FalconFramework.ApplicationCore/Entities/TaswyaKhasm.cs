

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class TaswyaKhasm
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        [Column("SarfID")]
        public long Id { get; set; }
        [Column("store_id")]
        public long warehouseId { get; set; }
        public DateTime? Sarfdate { get; set; }
        public int? year { get; set; }
        public string notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public long? Journal { get; set; }
        [Column("EZnTypeID")]
        public int EznTypeId { get; set; }
        public int? gardno { get; set; }
        public int? ConsumerID { get; set; }

        public virtual ICollection<TaswyaKhasm_item> TaswyaEdafa_item { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        //public virtual Companies Company { get; set; }
        public virtual EznType EznType { get; set; }
    }
}
