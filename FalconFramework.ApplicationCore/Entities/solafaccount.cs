//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class solafaccount
    {
        public long id { get; set; }
        public long? empcode { get; set; }
        public string year { get; set; }
        public DateTime? movedate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daen { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? solafpercent { get; set; }
        public string Notes { get; set; }
        public string Note2 { get; set; }
        public bool? flag { get; set; }
    }
}
