
using System.ComponentModel.DataAnnotations.Schema;

public class Indirect_Cost_Items
{
    public int id { get; set; }
    public long ProjectCode {get;set;}
	public string Item_Index {get;set;}
	public string Details {get;set;}
	public string Unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
	public int TypeID {get;set;}
}