
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class StoSteeps
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int AkdID { get; set; }
        public string AkdName { get; set; }
        public bool? flag { get; set; }
    }
}
