//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Stores_Consumer
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long GardID { get; set; }
        public DateTime? ScanDate { get; set; }
        public int? StoreID { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public int? Item_Gro_Id { get; set; }
        public int? Item_Gro_Sub_Id { get; set; }
        public string HDate { get; set; }
    }
}
