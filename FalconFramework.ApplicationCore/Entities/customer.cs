

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("customer", Schema = "dbo")]
    public class ClinicCustomer
    {
        [Key]
        [Column("code_custome")]
        public int Id { get; set; }

        [Column("name_customer")]
        [StringLength(50)]
        public string Name { get; set; }

        [Column("date_from")]
        public DateTime? DateFrom { get; set; }

        [Column("date_to")]
        public DateTime? DateTo { get; set; }

        [Column("code_servic")]
        public int? CodeService { get; set; }

        [Column("name_service")]
        [StringLength(50)]
        public string NameService { get; set; }

        [Column("price_service")]
        public int? PriceService { get; set; }

        [Column("number_day")]
        public int? NumberDay { get; set; }

        [Column("customer_flage")]
        public bool CustomerFlag { get; set; }

        [Column("PriceList_Name")]
        [StringLength(100)]
        public string PriceListName { get; set; }
    }

}
