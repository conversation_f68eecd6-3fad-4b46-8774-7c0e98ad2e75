
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Tender_Works_item
    {
        [Key]
        public long ID { get; set; }
        public int? Tender_Basic_id { get; set; }
        public long? Tender_Work_ID { get; set; }
        public string Tender_Work_items { get; set; }
        public long? tenders_Code { get; set; }
        public long? Tender_Group_ID { get; set; }
        public int? ID_Form { get; set; }
        public int? ContryID { get; set; }
        public int? CityID { get; set; }
        public long? Tender_Content_ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        public string UserAdd { get; set; }
        public DateTime? DateAdd { get; set; }
        public string unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Totalprice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Hamesh { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Totalprice2 { get; set; }
        public string Notes { get; set; }
        public string ItemTendr_NO { get; set; }
    }
}
