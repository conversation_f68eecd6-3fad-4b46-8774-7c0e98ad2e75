//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Insurance
    {
        
        public int COMP_ID { get; set; }
       
        public int ProunchID { get; set; }
      
        public int InsuranceType { get; set; }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int InsuranceNo { get; set; }
        public int? ReceiptNo { get; set; }
        public DateTime? InsuranceDate { get; set; }
        public DateTime? ReturnDate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Value { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? InsurancePercent { get; set; }
        public int? InsurancePeriod { get; set; }
        public int? InvNo { get; set; }
        public int? InvType { get; set; }
        public DateTime? InvDate { get; set; }
        public int? CustID { get; set; }
        public string CustName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? InvValue { get; set; }
        public int? TenderNo { get; set; }
        public DateTime? TenderDate { get; set; }
        public string TenderName { get; set; }
        public bool? TenderType { get; set; }
        public string Notes { get; set; }
        public string Insurance_State { get; set; }
    }
}
