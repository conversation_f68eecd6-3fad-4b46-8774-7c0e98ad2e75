using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(performa_id), nameof(serial))]
public class ordercommanitems
{
	public long performa_id {get;set;}
	public long serial {get;set;}
	public string item_name {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
	public string unit {get;set;}
	public long quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal total {get;set;}
	public string user_id {get;set;}
	public DateTime action_date {get;set;}
	public bool flag {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}