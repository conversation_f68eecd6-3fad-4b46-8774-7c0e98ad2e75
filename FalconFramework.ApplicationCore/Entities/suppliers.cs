

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("suppliers", Schema = "dbo")]
    public partial class Suppliers
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        [Column("suppliers_id")]
        public long Id { get; set; }

        [Column("suppliers_name")]
        public string NameAr { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string fax { get; set; }
        public string mobile { get; set; }
        public string site { get; set; }
        public string email { get; set; }

        [Column("scope")]
        public string SupplierActivity { get; set; }

        [Column("togary")]
        public string CommercialRegisterNumber { get; set; }

        [Column("saletax")]
        public string VATNumber { get; set; }

        [Column("taxcard")]
        public string TaxCardNumber { get; set; }

        [Column("mamorya")]
        public string TaxAuthorityOffice { get; set; }

        [Column("fileNO")]
        public string TaxFileNumber { get; set; }

        public string notes { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        [Column("ProunchID")]
        public int? branchId { get; set; }
        [Column("COMP_ID")]
        public int? companyId { get; set; }
        [Column("TahseelModa")]
        public int? CollectionDuration { get; set; }

        public string Short_Name_Ar { get; set; }
        [Column("Supplier_Name_EN")]
        public string NameEn { get; set; }

        public string Short_Name_EN { get; set; }
        [Column("SuppTypeID")]
        public long? SupplierGroupID { get; set; }

        [Column("TreeCode")]
        public long? AccountCode { get; set; }

        [Column("TreeName")]
        public string AccountName { get; set; }

        [Column("Mokadama")]
        public long? AdvancePaymentAccount { get; set; }

        [Column("Mokadama_Name")]
        public string AdvancePaymentAccountName { get; set; }

        [Column("Mo7tagazat")]
        public long? RetentionAccountCode { get; set; }

        [Column("Mo7tagazat_Name")]
        public string RetentionAccountName { get; set; }

        [Column("Dman")]
        public long? WorkGuaranteeAccountCode { get; set; }

        [Column("Dman_Name")]
        public string WorkGuaranteeAccountName { get; set; }


        [Column(TypeName = "decimal(18, 2)")]
        public decimal? commSales { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? commQuntity { get; set; }
        public string Mandoub_Name { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Mand_commSales { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Mand_commQuntity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Mand_Fixed { get; set; }
        public long? IncomAccountCode { get; set; }
        public string IncomAccountName { get; set; }
        public long? TaxAccountCode { get; set; }
        public string TaxAccountName { get; set; }
        public long? DiscountAccount { get; set; }
        public long? DeductionsAccount { get; set; }
        public string DiscountAccountName { get; set; }
        public string DeductionsAccountName { get; set; }
        public int? SuppliercountryID { get; set; }
        [Column("governate")]
        public string Governorate { get; set; }

        [Column("regionCity")]
        public string City { get; set; }

        public string buildingNumber { get; set; }
        public string postalCode { get; set; }
        public string Custfloor { get; set; }
        public string room { get; set; }
        public string landmark { get; set; }
        public string additionalInformation { get; set; }
        public string ActivityCode { get; set; }

        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }

        public virtual SuppType SupplierGroup { get; set; }


    }
}
