//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class MachineMove
    {
        [Key]
        public int ID { get; set; }
        public int? MachineID { get; set; }
        public int? ProjectID { get; set; }
        public int? Counter { get; set; }
        public DateTime? MoveDate { get; set; }
        public DateTime? DateAdd { get; set; }
        public long? DriverID { get; set; }
        public TimeSpan? TripStartTime { get; set; }
        public Nullable<double> TripAmount { get; set; }
        public long? CustomerID { get; set; }
        public DateTime? DateEnd { get; set; }
        public int? CounterEnd { get; set; }
        public TimeSpan? TripEndTime { get; set; }
        public string TripNotes { get; set; }
        public Nullable<double> ExtraAmount { get; set; }
        public Nullable<double> Net { get; set; }
        public Nullable<double> MonthAmount { get; set; }
        public Nullable<double> DayAmount { get; set; }
        public bool? IsMonthly { get; set; }
        public Nullable<double> Quantity { get; set; }
        public Nullable<double> TotalAmount { get; set; }
        public Nullable<double> QuantityCome { get; set; }
        public DateTime? LastInoiceDate { get; set; }
        public int? DayCount { get; set; }
    }
}
