//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Item_Stores
    {
        [Key]
        public int id { get; set; }
        public int store_id { get; set; }
        public int? Item_Id { get; set; }
        public string ExpDate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? wared { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monsaref { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rased { get; set; }
        public int? year { get; set; }
        public DateTime? ActionDate { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Barcode { get; set; }
        public string oparateno { get; set; }
    }
}
