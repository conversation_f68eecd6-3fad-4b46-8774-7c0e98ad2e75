

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Rooms
    {
        [Key]
        public int RoomId { get; set; }
        public string RoomName { get; set; }
        public string Short_Name { get; set; }
        public bool? flag { get; set; }
        public string EnglishName { get; set; }
        public byte[] TableFreeImage { get; set; }
        public byte[] TableUsedImage { get; set; }
    }
}
