public class WorkPlanEdite
{
    public int id { get; set; }
    public int ShiftID {get;set;}
	public int Planid {get;set;}
	public string DayName {get;set;}
	public string DateStart {get;set;}
	public string DateEnd {get;set;}
	public double HourLate {get;set;}
	public double HourLatePERM {get;set;}
	public double Apsent {get;set;}
	public double ApsentPERM {get;set;}
	public double MaxEdafy {get;set;}
	public string WorkType {get;set;}
	public double MaxLate {get;set;}
	public double normallate {get;set;}
	public double latestart {get;set;}
	public double daylate {get;set;}
	public double daylate25 {get;set;}
	public double daylate50 {get;set;}
	public double daylate75 {get;set;}
	public string <PERSON><PERSON><PERSON><PERSON> {get;set;}
	public string <PERSON><PERSON>ein {get;set;}
}