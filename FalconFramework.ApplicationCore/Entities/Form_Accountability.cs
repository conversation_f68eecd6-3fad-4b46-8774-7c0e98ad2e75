//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_Accountability
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public long? EmpCode { get; set; }
        public string EmpName { get; set; }
        public DateTime? Date { get; set; }
        public string dayname { get; set; }
        public string Details { get; set; }
        public string Reasons { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public bool? IsInside { get; set; }
        public string Accountability { get; set; }
    }
}
