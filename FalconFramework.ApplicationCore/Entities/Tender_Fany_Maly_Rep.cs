using System.ComponentModel.DataAnnotations.Schema;

public class Tender_Fany_Maly_Rep
{
    public int id { get; set; }
    public string FromDate {get;set;}
	public string ToDate {get;set;}
	public long srial {get;set;}
	public long TenderID {get;set;}
	public string TenderName {get;set;}
	public string MonaksaType {get;set;}
	public string Owner {get;set;}
	public string datefany {get;set;}
	public string datemaly {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ourvalue {get;set;}
	public int Countco {get;set;}
	public int ourlevel {get;set;}
	public string co1 {get;set;}
	public string co1type {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal co1val {get;set;}
	public string co12 {get;set;}
	public string co2type {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal co2val {get;set;}
	public string TenderState {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal val_Study {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal val_First {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal val_Study_time {get;set;}
	public string val_Study_IS {get;set;}
	public int count_Study {get;set;}
	public int Count_First {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal count_Study_time {get;set;}
	public string countl_Study_IS {get;set;}
	public string pervallevel1 {get;set;}
	public string pervalwith {get;set;}
	public string percountlevel1 {get;set;}
	public string percountwith {get;set;}
}