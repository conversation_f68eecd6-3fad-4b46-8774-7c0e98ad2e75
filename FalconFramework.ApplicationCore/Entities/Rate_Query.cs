

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Rate_Query
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Rate_Query_ID { get; set; }
        public DateTime? Arrive_Datee { get; set; }
        public int? NO_Of_Dayes { get; set; }
        public DateTime? Lev_Date { get; set; }
        public int? Adult_Count { get; set; }
        public int? Child_Count { get; set; }
        public int? Room_Count { get; set; }
        public string Guest_Name { get; set; }
        public long? Guest_ID { get; set; }
        public DateTime? Action_Date { get; set; }
        public bool? flag { get; set; }
        public bool? ISDone { get; set; }
        public bool? Cheked_IN { get; set; }
    }
}
