

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(InvoiceNo), nameof(serialitem))]
    public partial class invoiceoutitem
    {

        [ForeignKey("invoiceout")]
        public long InvoiceNo { get; set; }
        public long serialitem { get; set; }

        [Column("itemid")]
        public long productId { get; set; }
        public string ITEMS_ITEM_NAME { get; set; }
        public string unit { get; set; }
        public Nullable<double> PriceOne { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        public Nullable<double> TotalPrice { get; set; }
        public string SN { get; set; }
        public Nullable<double> Itemtax { get; set; }
        public Nullable<double> Valitemtax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemMaksab { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DefaltCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? despecent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ItemDescount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bounas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defualt_ItemCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Unit_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostAllItemOut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maksap { get; set; }
        public long? CostID { get; set; }
        public int? SaleUnitID { get; set; }
        public int? Item_Unit_Id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bonas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? allquantity { get; set; }
        public bool? Reserved { get; set; }
        public Nullable<double> orginalPrice { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }

        public virtual Invoiceout invoiceout { get; set; }
        public virtual Product Product { get; set; }
    }
}
