//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Item_store_Cost
    {
        [Key]
        public long ID { get; set; }
        public int? store_id { get; set; }
        public int? Item_Id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? wared { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monsaref { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? rased { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CostPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Avreg { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? sumline { get; set; }
    }
}
