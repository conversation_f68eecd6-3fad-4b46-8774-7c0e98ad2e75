

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class persons
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long idno { get; set; }
        public string Pname { get; set; }
        public string idsource { get; set; }
        public string iddate { get; set; }
        public string address { get; set; }
        public string Workaddress { get; set; }
        public string phone { get; set; }
        public string mobile { get; set; }
        public string Nationality { get; set; }
        public string JobName { get; set; }
        public string Proffession { get; set; }
        public string Rank { get; set; }
        public string Grade { get; set; }
        public string remarks { get; set; }
        public DateTime? DEDate { get; set; }
        public DateTime? DETime { get; set; }
        public string UserCOde { get; set; }
        public long? AccID { get; set; }
        public string AccName { get; set; }
        public string IBAN_NO { get; set; }
        public string BankName { get; set; }
        public long? Customer_ID { get; set; }
    }
}
