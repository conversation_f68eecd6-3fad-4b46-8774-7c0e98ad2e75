//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Item_Card
    {
        [Key]
        public long ItemCard_ID { get; set; }
        public long store_id { get; set; }
        public long? Item_Id { get; set; }
        public string VenderOrCustomer { get; set; }
        public long? EznNO { get; set; }
        public long? InvNO { get; set; }
        public DateTime? ActionDate { get; set; }
        public Nullable<double> QCome { get; set; }
        public Nullable<double> PriceCome { get; set; }
        public Nullable<double> AllPriceCome { get; set; }
        public Nullable<double> Qout { get; set; }
        public Nullable<double> PriceOut { get; set; }
        public Nullable<double> AllPriceOut { get; set; }
        public Nullable<double> CostAllItemOut { get; set; }
        public long Year { get; set; }
        public Nullable<double> VallItemsInStores { get; set; }
        public Nullable<double> Maksab { get; set; }
        public string ActionName { get; set; }
        public string SN { get; set; }
        public long FirstStokID { get; set; }
        public long EsnSarfID { get; set; }
        public long EznEdafaID { get; set; }
        public int InvtaxID { get; set; }
        public int ByanID { get; set; }
        public int TaswyaEdafaID { get; set; }
        public int TaswyaKhasmID { get; set; }
        public int ConvertBetwenStores { get; set; }
        public int TagmeaSanf { get; set; }
        public int TalabMowadID { get; set; }
        public int? Mardodat_inv_ID { get; set; }
        public int? Mardoat_byan_id { get; set; }
        public int Inv_com_ID { get; set; }
        public int Inv_com_noTax_ID { get; set; }
        public int? Mardodat_Inv_Com_Id { get; set; }
        public int? Mardodat_Inv_Com_Nottax_ID { get; set; }
        public int Srial { get; set; }
        public int TalabMowadINDEX { get; set; }
        public string MoveType { get; set; }
        public long? LonID { get; set; }
        public int? SarfOhdID { get; set; }
        public int? SaleUnitID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SaleRate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SaleQuantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SalePrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Avreg_Cost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Current_Avreg { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Current_Balance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Current_Value { get; set; }
        public int? Vg_Receiving_ID { get; set; }
        public long? RentId { get; set; }
        public long? Byan_Ezn_Id { get; set; }
        public bool? IsStartedBalance { get; set; }
        public int? RequsteItem_Id { get; set; }
        public int? RequsteItem_INDEX { get; set; }
        public string UserName { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }
    
        public virtual Vg_Receiving Vg_Receiving { get; set; }
    }
}
