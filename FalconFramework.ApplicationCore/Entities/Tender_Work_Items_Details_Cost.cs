
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(TenderID), nameof(Item_ID))]
public class Tender_Work_Items_Details_Cost
{
	public long TenderID {get;set;}
	public long Tender_BasicID {get;set;}
	public long tender_workID {get;set;}
	public long FormID {get;set;}
	public long GroupID {get;set;}
	public int Tender_ContentID {get;set;}
	public long Tender_Works_item_ID {get;set;}
	public long Tender_Work_Items_Details_ID {get;set;}
	public long Item_ID {get;set;}
	public string Item_Name {get;set;}
	public string UnitName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Cost_Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal SumCost {get;set;}
	public string Notes {get;set;}
	public string UserAdd {get;set;}
	public DateTime DateAdd {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Estehlak_Percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Halek_Percent {get;set;}
	public string ItemFrom {get;set;}
	public string FromTelphone {get;set;}
}