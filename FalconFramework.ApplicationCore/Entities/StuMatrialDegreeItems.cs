using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class StuMatrialDegreeItems
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int MatID {get;set;}
	public int StuID {get;set;}
	public string Term {get;set;}
	public string MatName {get;set;}
	public int MatAmount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal StuDegree {get;set;}
	public string StuTakder {get;set;}
}