using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Tendr_Mony
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long TenderID {get;set;}
	public long CoID {get;set;}
	public string COLevel {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Normal {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Helth {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Cahro {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Other {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal FirstSum {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Percentage {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
	public string Notes {get;set;}
}