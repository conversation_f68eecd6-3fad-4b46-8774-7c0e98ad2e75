

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Job_Order_Service_Request
    {
        [Key]
        public long Services_Order_Requset_Id { get; set; }
        public string Order_Id { get; set; }
        public int? Service_Request_Type_Id { get; set; }
        public string Service_Request_Code { get; set; }
        public string Service_Request_Description { get; set; }
    }
}
