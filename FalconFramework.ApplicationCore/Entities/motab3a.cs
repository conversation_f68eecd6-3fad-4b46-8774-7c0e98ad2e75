using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class motab3a
{

    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long commandid {get;set;}
	public long Customer_id {get;set;}
	public long qt {get;set;}
	public long drawno {get;set;}
	public DateTime mdate {get;set;}
	public string stage {get;set;}
	public bool fa7s {get;set;}
	public long emp_id {get;set;}
	public string notes {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}