

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class statement
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long state_no { get; set; }
        public DateTime? state_date { get; set; }
        public long? Customer_id { get; set; }
        public string insurance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string state_only { get; set; }
        public long? user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
