

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{



    public partial class customers_types
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long TypeCode { get; set; }
        public string TypeName { get; set; }
        public bool? flag { get; set; }
        [Column("ProunchID")]
        public int? branchId { get; set; }
        [Column("COMP_ID")]
        public int? companyId { get; set; }
        public string TypeName_EN { get; set; }

        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
    }
}
