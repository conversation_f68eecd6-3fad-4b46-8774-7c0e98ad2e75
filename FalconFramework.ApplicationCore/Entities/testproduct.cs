using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class testproduct
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long testid {get;set;}
	public long Customer_id {get;set;}
	public long qt {get;set;}
	public DateTime testdate {get;set;}
	public long drawno {get;set;}
	public string describe {get;set;}
	public string valtest {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}