

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class unit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Column("unitcode")]
        public long Id { get; set; }
        public string unitname { get; set; }
        public string unitname_en { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Unit_En_Name { get; set; }
        public string CodeID { get; set; }
    }
}
