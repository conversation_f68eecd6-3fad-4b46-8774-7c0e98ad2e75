

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(InvoiceNo), nameof(serialitem))]
    public partial class invcomeitem
    {
        public long InvoiceNo { get; set; }
        public long serialitem { get; set; }
        [Column("itemid")]
        public long productId { get; set; }
        public string ITEMS_ITEM_NAME { get; set; }
        public string unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PriceOne { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalPrice { get; set; }
        public string SN { get; set; }
        public long? CostId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Itemtax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Valitemtax { get; set; }
        public Nullable<double> descount_percent { get; set; }
        public Nullable<double> descount_val { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ponas_percent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ponsa_val { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total_quantity { get; set; }
        public string oparaten { get; set; }
        public string oparateno { get; set; }
        public long? Account_ID { get; set; }
        public int? Store_ID { get; set; }
        public int? Store_Account { get; set; }
        public string NotePerLine { get; set; }
        public int? Vender_ID_Line { get; set; }
        public string VAT_NO_Line { get; set; }
        public long? VenderAccount { get; set; }
        public string INvenderNO { get; set; }
        public int? Unit_Id_Line { get; set; }
        public string ItemBarcode { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }

        public virtual invcome invcome { get; set; }

        public virtual Product Product { get; set; }
    }
}
