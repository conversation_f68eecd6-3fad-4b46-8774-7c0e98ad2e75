

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("byan")]
    public partial class Byan
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long InvoiceNo { get; set; }
        public DateTime? idate { get; set; }
        [ForeignKey("customer")]
        [Column("ClientID")]
        public long? customerId { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Net { get; set; }
        public string aArabicTotal { get; set; }
        public string invtype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monycome { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monystill { get; set; }
        public string notes { get; set; }
        public long? iyeer { get; set; }
        [Column("store_id")]
        public long? warehouseId { get; set; }
        public string servicetype { get; set; }
        public bool? flag { get; set; }
        [Column("ProunchID")]
        public int branchId { get; set; }

        [ForeignKey("Company")]
        [Column("COMP_ID")]
        public int companyId { get; set; }
        public string Tahseel { get; set; }
        public string TahseelNote { get; set; }
        public DateTime? tahseldate { get; set; }
        public long? InvEznNo { get; set; }
        public bool? Isused { get; set; }
        public long? Journal { get; set; }
        public DateTime? CollectedDate { get; set; }
        public DateTime? Fallowdate { get; set; }
        public DateTime? SuplayDate { get; set; }
        public string ChekDate { get; set; }
        public long? ChekNO { get; set; }
        public string BankName { get; set; }
        public bool? TaxDes { get; set; }
        public bool? TaxDoc { get; set; }
        public string UserColletMony { get; set; }
        public long? Isal_NO { get; set; }
        public Nullable<double> Isal_Vale { get; set; }
        public long? Defrent_Collect { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount_Percent { get; set; }
        public int? saletype { get; set; }
        public string Mandop_Name { get; set; }
        public string dayname { get; set; }
        public string InvTime { get; set; }
        public long? MandopID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Cash { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CardValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tips { get; set; }
        public long? BoxID { get; set; }
        public string DocNo { get; set; }
        public string DriverName { get; set; }
        public long? ProjectID { get; set; }
        public long? ExtractCount { get; set; }
        public long? CollectedJournal { get; set; }
        public Nullable<double> ExtraMoney_Percent { get; set; }
        public Nullable<double> ExtraMoney_Value { get; set; }
        public int? PrintedCount { get; set; }
        public long? ProjectID_Tender_Item_ID { get; set; }
        public bool? Closed { get; set; }
        public int? SheapingID { get; set; }
        public int? TableID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VatValue { get; set; }
        public int? CaseId { get; set; }
        public Nullable<double> GnlTaxPercent { get; set; }
        public Nullable<double> GnlTaxValue { get; set; }
        public string UserName { get; set; }
        public bool? DiscontperItems { get; set; }
        public bool? JornalPosted { get; set; }
        public byte[] QR_Code { get; set; }
        public string UUID { get; set; }
        public string SourceName { get; set; }

        public virtual ICollection<byan_item> byan_item { get; set; }

        public virtual Warehouse Warehouse { get; set; }
        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
        public virtual Customers customer { get; set; }

    }
}
