//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Falcon_Mails_To
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long MessageID { get; set; }
        public int? GroupID { get; set; }
        public string GroupName { get; set; }
        public int? SupGroupID { get; set; }
        public string SupGroup { get; set; }
        public int? UsrToID { get; set; }
        public string UserTo { get; set; }
        public int? UserFromID { get; set; }
        public string UserFrom { get; set; }
        public string MessageTitle { get; set; }
        public string MessageContint { get; set; }
        public DateTime? DateAdd { get; set; }
        public bool? Flage { get; set; }
    }
}
