﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class Loan_Limit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public int Collateral_ID { get; set; }
        public int Bank_ID { get; set; }
        public string Collateral_NO { get; set; }
        public string Opration { get; set; }
        public double Collateral_Value { get; set; }
        public double Collateral_Rat { get; set; }
        public double Collateral_Mony { get; set; }
        public string Notes { get; set; }
        public DateTime Date_Strat { get; set; }
        public DateTime Date_End { get; set; }
        public double commission { get; set; }
    }
}
