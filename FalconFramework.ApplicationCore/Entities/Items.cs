

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using FalconFramework.ApplicationCore.Entities;
using FalconFramework.ApplicationCore.DTOs.Inventory;

[Table("Items")]
public class Product
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]

    [Column("Item_Id")]
    public long Id { get; set; }
    public string itm_code2 { get; set; }
    public string Barcode { get; set; }
    public int itm_ismedicine { get; set; }

    [ForeignKey("Unit1")]
    [Column("Item_Unit_Id")]
    public long unit1Id { get; set; }


    [Column("Item_unit2")]
    public long? unit2Id { get; set; }


    [Column("Item_unit3")]
    public long? unit3Id { get; set; }



    [Column(TypeName = "decimal(18, 2)")]
    public decimal EqulUnit { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal EqulUnit2 { get; set; }
    public string UnitEqualed { get; set; }

    [ForeignKey("Category")]
    [Column("Item_Gro_Id")]
    public long categoryId { get; set; }


    [ForeignKey("SubCategory")]
    [Column("Item_Gro_Sub_Id")]
    public int subCategoryId { get; set; }


    [ForeignKey("Classification")]
    [Column("TradeTypeID")]
    public int classificationId { get; set; }
    public string Item_Name { get; set; }
    public string Item_Name_AR { get; set; }
    public int itm_request_limit { get; set; }
    public int MaxQ { get; set; }
    public int MINQ { get; set; }
    public double price { get; set; }
    public double gomla { get; set; }
    public bool FixPrice { get; set; }
    public int Tax { get; set; }
    public int HameshRebh { get; set; }
    public string aNotes { get; set; }
    public DateTime actiondate { get; set; }
    public string UserName { get; set; }
    public bool flag { get; set; }
    public byte[] Pic { get; set; }

    [Column("ProunchID")]
    public int branchId { get; set; }

    [ForeignKey("Company")]
    [Column("COMP_ID")]
    public int companyId { get; set; }

    public bool PriceFexed { get; set; }
    public double priceCome { get; set; }
    public double hgomla { get; set; }
    public double hmonaksat { get; set; }
    public double pricemonaksat { get; set; }
    public double hbig { get; set; }
    public double pricbig { get; set; }
    public double hmedum { get; set; }
    public double pricemedum { get; set; }
    public double hlow { get; set; }
    public double pricelow { get; set; }
    public string PartNo { get; set; }
    public double DescountPercent { get; set; }
    public double PonasPercent { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal com_id { get; set; }
    public string itm_com_code { get; set; }
    public int itm_has_expire { get; set; }
    public string itm_active { get; set; }
    public int itm_freez { get; set; }
    public string itm_scientific_n1 { get; set; }
    public string itm_scientific_n2 { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_scientific_group_id { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_usage_manner_id { get; set; }
    public int itm_isprev { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_itf_id { get; set; }
    public int itm_stop_sell { get; set; }
    public int itm_stop_pur { get; set; }
    public int itm_print_barcode { get; set; }
    public int itm_allow_discount { get; set; }
    public double itm_max_disc_per { get; set; }
    public double itm_max_disc_val { get; set; }
    public int itm_print_name { get; set; }
    public int itm_sales_avreage_period { get; set; }
    public int itm_srvc { get; set; }
    public int itm_origin { get; set; }
    public int itm_isShortage { get; set; }
    public int itm_favourite { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_location { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_default_limit { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_purchase_unit { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_sell_unit { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal itm_max_disc_value_NotUsed { get; set; }
    public string StorePlace { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Waigt { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ChargDesc { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal GomrokDesc { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal GomrakFinshDesc { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal OtherDesc { get; set; }
    public string OntherNameDesc { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Shehada { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal OtherShehada { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Boxcost { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Tabkher { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal InernalCharg { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Purchase { get; set; }
    public string Quntity_Price { get; set; }
    public byte[] Pic1 { get; set; }
    public double Width { get; set; }
    public double highet { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Thikness { get; set; }
    [Column(TypeName = "decimal(18, 2)")]
    public decimal CountInBox { get; set; }
    public string ImagePha { get; set; }
    public bool IsOffer { get; set; }
    public bool Always_in_stock { get; set; }
    public string ImagePha2 { get; set; }
    public string Barcode2 { get; set; }
    public string Barcode3 { get; set; }
    public string ItemPrintName { get; set; }
    public string ItemPrintNameEN { get; set; }

    public long AccountCode { get; set; }
    public bool IsWeight { get; set; }
    public double AnotherVat { get; set; }
    public string etaCodeType { get; set; }
    public string WooCommerceCode { get; set; }
    public string SallaCode { get; set; }
    public string ZidCode { get; set; }

    public virtual Groups Category { get; set; }
    public virtual Groups_Sub SubCategory { get; set; }
    public virtual Branches Branch { get; set; }
    public virtual Companies Company { get; set; }
    public virtual unit Unit1 { get; set; }
    public virtual unit Unit2 { get; set; }
    public virtual unit Unit3 { get; set; }
    public virtual TasnefTogary Classification { get; set; }


}
