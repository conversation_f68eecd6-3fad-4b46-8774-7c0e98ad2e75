//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Form_BusinessTravel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long ID { get; set; }
        public long? EmpCode { get; set; }
        public string EmpName { get; set; }
        public string FromPlace { get; set; }
        public string ToPlace { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Days { get; set; }
        public string Stay { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Deposit { get; set; }
        public string DepositTo { get; set; }
        public bool? OutAndBack { get; set; }
        public bool? Internal { get; set; }
        public bool? Other { get; set; }
        public string Notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
