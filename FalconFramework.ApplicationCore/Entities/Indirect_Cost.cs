
using System.ComponentModel.DataAnnotations.Schema;

public class Indirect_Cost
{
    public int id { get; set; }
    public long ProjectCode {get;set;}
	public string ProjectName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Direct_Cost_Total {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Tahmilat {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Indirect_cost_Perc {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Indirect_cost_Total {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Indirect_Direct_cost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Administrative_costs_perc {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Administrative_costs {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Administrative_Indirect_Direct_cost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Arpah_Perc {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ProPrice {get;set;}
}