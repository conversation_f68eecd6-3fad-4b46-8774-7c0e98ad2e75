

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(InvoiceNo), nameof(serialitem))]
    public partial class invcomeitemNoTax
    {
        public long Id { get; set; }
        public long InvoiceNo { get; set; }
        public long serialitem { get; set; }
        public long? itemid { get; set; }
        public string ITEMS_ITEM_NAME { get; set; }
        public string unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PriceOne { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Q { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TotalPrice { get; set; }
        public string SN { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ponas_percent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ponsa_val { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? descount_percent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? descount_val { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total_quantity { get; set; }
        public string oparateno { get; set; }
        public long? CostId { get; set; }
        public int? Rate { get; set; }
        public int? Unit_Id_Line { get; set; }
        public int? VariantNameId { get; set; }
        public int? VariantValueId { get; set; }
    
        public virtual invcomeNoTax invcomeNoTax { get; set; }
    }
}
