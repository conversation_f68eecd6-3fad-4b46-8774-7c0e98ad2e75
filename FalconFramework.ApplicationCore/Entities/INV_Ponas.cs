using System.ComponentModel.DataAnnotations.Schema;

public class INV_Ponas
{
    public int id { get; set; }
    public long InvoiceNo {get;set;}
	public long ByanNo {get;set;}
	public long EznNO {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public long itemponas {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
}