//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class ItemOfferInPeriod_items
    {
        public long Id { get; set; }
        public int? serial { get; set; }
        public long? itemid { get; set; }
        public int? UniteId { get; set; }
        public string Unite { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PercentPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? total { get; set; }
    }
}
