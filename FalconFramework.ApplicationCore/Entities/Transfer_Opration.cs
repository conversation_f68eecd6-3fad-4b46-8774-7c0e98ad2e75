//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Transfer_Opration
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public DateTime? aDate { get; set; }
        public string aDate2 { get; set; }
        public int? CustID { get; set; }
        public string PolicyNumber { get; set; }
        public string PolicyNot { get; set; }
        public int? PolicyCount { get; set; }
        public int? Delivered { get; set; }
        public string ShipmentType { get; set; }
        public string Size { get; set; }
        public string Weight { get; set; }
        public string Note { get; set; }
        public bool? Container { get; set; }
        public bool? Bunch { get; set; }
        public bool? Roll { get; set; }
        public bool? Box { get; set; }
        public bool? Bag { get; set; }
        public bool? Other { get; set; }
        public int? year { get; set; }
        public bool? Locked { get; set; }
    }
}
