//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(Id), nameof(RentContract_List_Id), nameof(gno))]
    public partial class RentContract_List_Extension
    {
        public int Id { get; set; }
        public long RentContract_List_Id { get; set; }
        public byte gno { get; set; }
        public Nullable<double> AmountOld { get; set; }
        public DateTime? gdateOld { get; set; }
        public Nullable<double> Amount { get; set; }
        public DateTime? gdate { get; set; }
        public string Hdate { get; set; }
        public string AmountOnly { get; set; }
        public string UserAdd { get; set; }
        public DateTime? DateAdd { get; set; }
        public string ExtensionNote { get; set; }
    }
}
