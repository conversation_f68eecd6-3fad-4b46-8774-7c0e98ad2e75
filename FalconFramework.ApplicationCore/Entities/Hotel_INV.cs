using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Hotel_INV
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long INV_ID {get;set;}
	public long Srial {get;set;}
	public long Gust_ID {get;set;}
	public string Gust_Name {get;set;}
	public long Room_ID {get;set;}
	public long Room_NO {get;set;}
	public long Servic_ID {get;set;}
	public string Service_Name {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Mony_Come {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Mony_stel {get;set;}
	public string Box_Name {get;set;}
	public string CurncyName {get;set;}
	public bool IS_Done {get;set;}
	public DateTime ActionDate {get;set;}
}