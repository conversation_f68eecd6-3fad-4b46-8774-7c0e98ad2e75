

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Items_Ponas
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long PonasID { get; set; }

        [Column("ItemId")]
        public long ProductId { get; set; }
        public Nullable<double> Quantity { get; set; }
        public long? ItemIdPonas { get; set; }
        public Nullable<double> Quantityponas { get; set; }
        public DateTime? Datefrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string CustLevel { get; set; }

        public virtual Product Product { get; set; }
    }
}
