

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Vg_Receiving
    {
         
        public Vg_Receiving()
        {
            this.Item_Card = new HashSet<Item_Card>();
            this.Vg_Receiving_Item = new HashSet<Vg_Receiving_Item>();
        }
    
        public int Id { get; set; }
        public DateTime ADate { get; set; }
        public string ADate2 { get; set; }
        public int SupplierID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Customs_Clearance { get; set; }
        public string ContainersNO { get; set; }
        public string Driver { get; set; }
        public int ReceivingID { get; set; }
        public int Polisa_NO { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses1 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses3 { get; set; }
        public string Notes1 { get; set; }
        public string Notes2 { get; set; }
        public string Notes3 { get; set; }
        public bool Flag { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Internal_Rent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal External_Rent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal wages_Emp { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal wages_Ma3shek { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal cooling { get; set; }
        public long? ClearanceID { get; set; }
    
         
        public virtual ICollection<Item_Card> Item_Card { get; set; }
         
        public virtual ICollection<Vg_Receiving_Item> Vg_Receiving_Item { get; set; }
    }
}
