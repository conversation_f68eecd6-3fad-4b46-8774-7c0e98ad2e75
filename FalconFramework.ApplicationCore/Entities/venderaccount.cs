//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class venderaccount
    {
        public long id { get; set; }
        public long venderid { get; set; }
        public string year { get; set; }
        public string actionname { get; set; }
        public long? invno_ormony_id { get; set; }
        public DateTime? movedate { get; set; }
        public Nullable<double> maden { get; set; }
        public Nullable<double> daen { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Notes { get; set; }
        public string Invcomno { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Taxes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Stamps { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Others { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ChekNet { get; set; }
        public int? CurID { get; set; }
        public string DayName { get; set; }
        public int? MasrofaaaatID { get; set; }
        public int? MakbodaaaaatID { get; set; }
        public long? MakbodatID_Bank { get; set; }
        public long? MasrofaaaatID_Bank { get; set; }
        public long? Inv_com_ID { get; set; }
        public int? Inv_com_noTax_ID { get; set; }
        public int? Mardodat_Inv_Com_Id { get; set; }
        public int? Mardodat_Inv_Com_Nottax_ID { get; set; }
        public int? BoxMovID { get; set; }
        public int? Awrak_Daf3 { get; set; }
        public int? Awrak_kabd { get; set; }
        public int? EasweyaID { get; set; }
        public int? Esh3arID { get; set; }
        public int? Tasweyat_BankID { get; set; }
        public int? FristBalance { get; set; }
        public string TaswyaMasroof_ID { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
        public long? Journal { get; set; }
        public string DocNo { get; set; }
        public bool? IsStartedBalance { get; set; }
        public bool? Printed { get; set; }
        public bool? Flag { get; set; }
        public long? Costid { get; set; }
        public string financial_entity { get; set; }
        public long? financial_entity_Type { get; set; }
        public long? financial_entity_Id { get; set; }
    }
}
