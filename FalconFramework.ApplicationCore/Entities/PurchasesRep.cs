using System.ComponentModel.DataAnnotations.Schema;

public class PurchasesRep
{
    public int id { get; set; }
    public long InvoiceNo {get;set;}
	public string INvenderNO {get;set;}
	public DateTime idate {get;set;}
	public string suppliers_name {get;set;}
	public string fileNO {get;set;}
	public string NotePerLine {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Valitemtax {get;set;}
}