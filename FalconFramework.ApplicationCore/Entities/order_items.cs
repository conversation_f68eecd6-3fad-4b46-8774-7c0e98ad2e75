
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON>Key(nameof(orderid), nameof(serial))]
public class order_items
{
	public long orderid {get;set;}
	public long serial {get;set;}
	public long Item_Id {get;set;}
	public string item {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
	public string unit {get;set;}
	public double quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal sumitem {get;set;}
	public string DrawNo {get;set;}
	public double Qstor {get;set;}
	public double Qneed {get;set;}
	public double QRuning {get;set;}
	public string CustCode {get;set;}
	public string notes {get;set;}
	public string DateNeed {get;set;}
	public string Elmostalem {get;set;}
	public string Signeture {get;set;}
	public string RuningDateFinsh {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
	public bool State {get;set;}
	public string outdate {get;set;}
	public long invoice_no {get;set;}
	public bool Loked {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Pricecost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
	public string LokedUser {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Tahmilat_Perc {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price2 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Totalprice2 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Width {get;set;}
	public bool Fineshed {get;set;}
}