

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{


    [Table("Sections")]
    public partial class Sectors
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Sec_id { get; set; }
        public string Sec_name { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Short_Sec_Name { get; set; }
        public string Sec_name_En { get; set; }
        public long? Sec_Cost_ID { get; set; }
    }
}
