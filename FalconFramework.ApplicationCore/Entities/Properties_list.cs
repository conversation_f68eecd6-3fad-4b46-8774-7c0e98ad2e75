//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Properties_list
    {
        [Key]
      
        public long ID { get; set; }
        public string Name { get; set; }
        public int? TypeId { get; set; }
        public string PropertyNo { get; set; }
        public string SaleRent { get; set; }
        public long? CustomerID { get; set; }
        public int? FormId { get; set; }
        public Nullable<double> Area { get; set; }
        public Nullable<double> Price { get; set; }
        public int? Owner_or_Broker { get; set; }
        public int? TreeCode { get; set; }
        public long? CityID { get; set; }
        public bool? Ledelse { get; set; }
        public bool? Sold { get; set; }
        public Nullable<double> MeterPrice { get; set; }
        public int? RoonCount { get; set; }
        public int? PathRoomCount { get; set; }
        public int? Parking { get; set; }
        public string Notes { get; set; }
        public DateTime? DateAdd { get; set; }
        public string ImgPath { get; set; }
        public bool? Aproved { get; set; }
        public int? UserId { get; set; }
        public bool? IsRequest { get; set; }
        public string ImgPath2 { get; set; }
        public string ImgPath3 { get; set; }
        public string ImgPath4 { get; set; }
        public string ImgPath5 { get; set; }
        public string ImgPath6 { get; set; }
        public string VedioUrl { get; set; }
        public int? ViewsCount { get; set; }
        public int? Main_Properties_list_Id { get; set; }
        public long? DefualtRenter_Id { get; set; }
        public string SellerNote { get; set; }
        public string PropryType { get; set; }
        public Nullable<double> ParkingPrice { get; set; }
        public Nullable<double> GardenArea { get; set; }
        public Nullable<double> GardenPrice { get; set; }
        public Nullable<double> AllRealPrice { get; set; }
        public Nullable<double> ParkingMetrPrice { get; set; }
        public Nullable<double> GardenMetrPrice { get; set; }
    }
}
