//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class customer_contact
    {
        [Key]
        public long Contact_ID { get; set; }
        public long? serial { get; set; }
        public long? Customer_id { get; set; }
        public string contact_person { get; set; }
        public string titel_job { get; set; }
        public string mobile { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string department { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        public string BrancheName { get; set; }
    }
}
