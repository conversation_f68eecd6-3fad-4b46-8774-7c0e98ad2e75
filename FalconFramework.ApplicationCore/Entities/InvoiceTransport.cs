using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class InvoiceTransport
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long InvID {get;set;}
	public long CustID {get;set;}
	public DateTime MDate {get;set;}
	public string HDate {get;set;}
	public long ClearanceID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aTotal {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Discount_percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValDiscount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal saletax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal net {get;set;}
	public int Curns {get;set;}
	public int invtype {get;set;}
	public int BoxID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal monycome {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal monystill {get;set;}
	public string DocNO {get;set;}
	public long year {get;set;}
	public long Journal {get;set;}
}