//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class doctor
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int code_doc { get; set; }
        public string doc_arabic { get; set; }
        public string doc_english { get; set; }
        public string address { get; set; }
        public string email { get; set; }
        public string tel1 { get; set; }
        public string tel2 { get; set; }
        public string mobile { get; set; }
        public string code_dep { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? salary { get; set; }
        public bool? doc_flage { get; set; }
    }
}
