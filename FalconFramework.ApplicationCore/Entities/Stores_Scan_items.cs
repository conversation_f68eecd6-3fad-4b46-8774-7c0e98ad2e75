using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace FalconFramework.ApplicationCore.Entities{
[<PERSON><PERSON>ey(nameof(GardID), nameof(productId))]
public class Stores_Scan_items
{
     
    public long GardID {get;set;}

     [Column("Item_Id")]
     public long productId { get; set; }
 
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalByHande {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalByComputer {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal CalcCompAndHand {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValDeff {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceCome {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal inability {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal inabilityValue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal increase {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal increaseValue {get;set;}
    public string VariantName { get;set;}
    
   public virtual Stores_Scan Stores_Scan { get; set; }
   public virtual Product Product { get; set; }

}
}