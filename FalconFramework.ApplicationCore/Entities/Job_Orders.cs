//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Job_Orders
    {
        [Key]
        public long Order_Id { get; set; }
        public string Order_No { get; set; }
        public string Vehicle_No { get; set; }
        public string Vehicle_Descriptaion { get; set; }
        public string Customer_No { get; set; }
        public DateTime? Posting_Date { get; set; }
        public string Post_Code { get; set; }
        public int? City { get; set; }
        public int? Servies_Order_Type { get; set; }
        public string Location_Code { get; set; }
        public DateTime? Response_Date { get; set; }
        public string Response_Time { get; set; }
        public string Job_Priority { get; set; }
        public string Job_Status { get; set; }
        public string Release_Status { get; set; }
        public string Reading_KM { get; set; }
        public string Current_User_Name { get; set; }
        public string Mechanic_Code { get; set; }
        public string Mechanic_Code_2 { get; set; }
        public string Mechanic_Code_3 { get; set; }
        public string Job_Type_Creator_Code { get; set; }
        public bool? IsPosting { get; set; }
    }
}
