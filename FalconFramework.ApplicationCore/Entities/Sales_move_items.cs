
using System.ComponentModel.DataAnnotations.Schema;

public class Sales_move_items

{
    public int id { get; set; }
    public string from_date {get;set;}
	public string ToDate {get;set;}
	public string StoreName {get;set;}
	public string Item_Group {get;set;}
	public string Sup_group {get;set;}
	public int Item_Code {get;set;}
	public string ItemName {get;set;}
	public string Unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quntity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Avreg {get;set;}
	public string Cust_Name {get;set;}
	public string percent_q {get;set;}
	public string Percent_price {get;set;}
}