//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class inventory
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long inventoryid { get; set; }
        public int? itemid { get; set; }
        public int? storid { get; set; }
        public Nullable<double> quantity { get; set; }
        public Nullable<double> emp_id { get; set; }
        public DateTime? dateout { get; set; }
        public DateTime? datecome { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string notes { get; set; }
        public string Baknotes { get; set; }
    }
}
