//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class chartaccount
    {
        [Key]
        public long id { get; set; }
        public long? venderid { get; set; }
        public string year { get; set; }
        public string actionname { get; set; }
        public long? invno_ormony_id { get; set; }
        public DateTime? movedate { get; set; }
        public Nullable<double> maden { get; set; }
        public Nullable<double> daen { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        public int? ACCID { get; set; }
        public string ACC_Name { get; set; }
        public string Detail { get; set; }
        public Nullable<double> CuttingWillBake { get; set; }
        public int? ACCID2 { get; set; }
        public string ACC_Name2 { get; set; }
        public string Detail2 { get; set; }
        public Nullable<double> CuttingNotBack { get; set; }
        public string Notes { get; set; }
        public int? CurID { get; set; }
        public int? TaswyaID { get; set; }
        public string DayName { get; set; }
        public string cutaccname { get; set; }
        public int? MakbodatID { get; set; }
        public int? MakbodatID_Bank { get; set; }
        public int? MasrofaaaatID_Bank { get; set; }
        public long? TaswyaMasroof_ID { get; set; }
        public long? Journal { get; set; }
    }
}
