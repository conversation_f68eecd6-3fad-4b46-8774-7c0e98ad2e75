//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Notes_Setting
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Not_ID { get; set; }
        public string Note_Name { get; set; }
        public string Note_to { get; set; }
        public string Note_to_sefa { get; set; }
        public string Tahya { get; set; }
        public string Note_Content { get; set; }
        public string End1 { get; set; }
        public string End2 { get; set; }
        public string Sign_Name1 { get; set; }
        public string Sign1 { get; set; }
        public string Sign_Name2 { get; set; }
        public string Sign2 { get; set; }
        public string Sign_Name3 { get; set; }
        public string Sign3 { get; set; }
    }
}
