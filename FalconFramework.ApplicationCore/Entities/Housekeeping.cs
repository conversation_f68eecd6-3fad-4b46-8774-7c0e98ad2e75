//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Housekeeping
    {
        [Key]
        public long HCID { get; set; }
        public int? Room_ID { get; set; }
        public string RoomStates { get; set; }
        public string Housekeeping_Status { get; set; }
        public string Employee_Name { get; set; }
        public string Notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        public DateTime? Insirt_Date { get; set; }
    }
}
