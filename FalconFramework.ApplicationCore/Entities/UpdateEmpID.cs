using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class UpdateEmpID
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long ID {get;set;}
	public string DateFrom {get;set;}
	public string DateTo {get;set;}
	public string DateHijriFrom {get;set;}
	public string DateHigriTo {get;set;}
	public long EmpCode {get;set;}
	public string IqamaDate {get;set;}
	public string IqamaHijri {get;set;}
	public string WorK_Lic_NO {get;set;}
	public double Work_Lic_Value {get;set;}
	public double ID_Renewual_Value {get;set;}
}