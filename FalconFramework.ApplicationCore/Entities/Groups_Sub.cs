
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Groups_Sub
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int subid { get; set; }

        [ForeignKey("Groups")]
        public long? groupid { get; set; }
        public string subname { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string En_Sub_Name { get; set; }
        public string Short_Sub_Code { get; set; }
        public bool? ShowINCashier { get; set; }
        public string Colre { get; set; }
        public bool? ShowINinvoice { get; set; }
        public int? indix { get; set; }

        public virtual Groups Groups { get; set; }

    }
}
