//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Visa_Sub
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ID { get; set; }
        public int? Visa_NO_ID { get; set; }
        public string Visa_NO { get; set; }
        public int? NationalityID { get; set; }
        public int? jobid { get; set; }
        public int? Visa_Count { get; set; }
        public string Office { get; set; }
        public string Auth_issued { get; set; }
        public string Auth_Available { get; set; }
        public string Get_Visa { get; set; }
        public string Not_Get_Visa { get; set; }
        public string Notes { get; set; }
        public DateTime? aDate { get; set; }
    }
}
