
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Mandop_Moda_Omola
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ModaID { get; set; }
        public double? Modda { get; set; }
        public double? ModaPercent { get; set; }
        public int? MAndopID { get; set; }
    }
}
