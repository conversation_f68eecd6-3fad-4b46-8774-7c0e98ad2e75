

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
    public partial class Cutting
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int cut_id { get; set; }
        public int? cut_code_id { get; set; }
        public int? emp_id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? cut_val { get; set; }
        public string dateYear { get; set; }
        public string datemonth { get; set; }
        public DateTime? cut_date { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Notes { get; set; }
        public bool? NormalDiscount { get; set; }
        public bool? AdvancedPayment { get; set; }
        public bool? sick { get; set; }
        public bool? health { get; set; }
        public bool? solfa { get; set; }
    }
}
