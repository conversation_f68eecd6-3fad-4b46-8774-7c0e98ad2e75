
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class MenuUser
    {
        [Key]
        public int IDCount { get; set; }
        public int? MenuSystemID { get; set; }
        public int? UserID { get; set; }
        public bool? New { get; set; }
        public bool? Edit { get; set; }
        public bool? Read { get; set; }
        public bool? login { get; set; }
        public bool? IsHotKey { get; set; }
        public string systemcode { get; set; }
    }
}
