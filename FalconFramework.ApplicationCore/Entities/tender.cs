//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class tender
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long tender_no { get; set; }
        public long? Customer_id { get; set; }
        public DateTime? tender_date { get; set; }
        public DateTime? date_end { get; set; }
        public DateTime? date_open_maly { get; set; }
        public DateTime? date_open_fanny { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? price_tender { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? vlaue_insurance { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
