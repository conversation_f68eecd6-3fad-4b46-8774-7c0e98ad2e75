using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Form_Emp_Contract
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public string DayName {get;set;}
	public DateTime Date { get;set;}
	public int EmpCode {get;set;}
	public string EmpName {get;set;}
	public string Nationality {get;set;}
	public string StanyNO {get;set;}
	public string StanyFrom {get;set;}
	public DateTime StayEndDate {get;set;}
	public string WorkPlace {get;set;}
	public string EmpJop {get;set;}
	public string ContractTime {get;set;}
	public DateTime StartContact {get;set;}
	public DateTime EndContract {get;set;}
	public string Sallery {get;set;}
	public string Sallery2 {get;set;}
	public string Flat {get;set;}
	public string Flat2 {get;set;}
	public string Travel {get;set;}
	public string Travel2 {get;set;}
	public string TamenClass {get;set;}
	public string Person1 {get;set;}
	public string Person1type {get;set;}
	public string Person1Phone {get;set;}
	public string Person12 {get;set;}
	public string Person1type2 {get;set;}
	public string Person1Phone2 {get;set;}
	public string DayName_EN {get;set;}
	public string EmpName_EN {get;set;}
	public string Nationality_EN {get;set;}
	public string StanyFrom_EN {get;set;}
	public string WorkPlace_EN {get;set;}
	public string EmpJop_EN {get;set;}
	public string ContractTime_EN {get;set;}
	public string Sallery2_EN {get;set;}
	public string Flat2_EN {get;set;}
	public string Travel2_EN {get;set;}
	public string TamenClass_EN {get;set;}
	public string Person1_EN {get;set;}
	public string Person1type_EN {get;set;}
	public string Person1Phone_EN {get;set;}
	public string Person12_EN {get;set;}
	public string Person1type2_EN {get;set;}
	public string Person1Phone2_EN {get;set;}
	public string gender {get;set;}
	public string gender_EN {get;set;}
}