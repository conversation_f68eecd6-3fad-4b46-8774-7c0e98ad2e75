using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class priceList
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int pirce_listID {get;set;}
	public long ItemCode {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Percentage {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
	public int UnitCode {get;set;}
	public int PranchID {get;set;}
	public int DiscountPercent {get;set;}
}