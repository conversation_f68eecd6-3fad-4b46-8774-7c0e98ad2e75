using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Manufactur_Plan_TEMP
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long Srial {get;set;}
	public long ItemCode {get;set;}
	public string ItemName {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Total {get;set;}
	public string SN {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Balance {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Bounas {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Price {get;set;}
	public int CostID {get;set;}
	public string CostName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Defualt_ItemCost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal UnitCost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Balance {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Rate {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal CostAllItemOut {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal maksap {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PreviousQTY {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Tax {get;set;}
	public bool PriceFexed {get;set;}
	public bool itm_ismedicine {get;set;}
	public int Item_Unit_Id {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal saleTax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValSaleTax {get;set;}
	public bool Always_in_stock {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal allquantity {get;set;}
	public long Account_ID {get;set;}
	public string Account_Name {get;set;}
	public int Store_ID_Line {get;set;}
	public string Store_Name_Line {get;set;}
	public long Store_Account {get;set;}
	public string NotesPerLine {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal despecent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemDescount {get;set;}
	public double orginalPrice {get;set;}
	public double VatPrice {get;set;}
	public double TotalVatPrice {get;set;}
	public string Barcode {get;set;}
	public string TradeType {get;set;}
	public string VariantName {get;set;}
}