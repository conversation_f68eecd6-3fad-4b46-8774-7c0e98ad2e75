using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class Fixed_Aset_Calc_Add
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int AslID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Steep {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal MasrofEhlak {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Mojama3Ehlak_Sabek {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Mojama3Ehlak {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal NetVale {get;set;}
	public string lastdate {get;set;}
	public long DayCOunt {get;set;}
	public long P_DayCOunt {get;set;}
	public long CostACCID {get;set;}
	public long Jorrnal {get;set;}
}