//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Vg_SuplierBill
    {
         
        public Vg_SuplierBill()
        {
            this.Vg_SuplierBill_items = new HashSet<Vg_SuplierBill_items>();
        }
    
        public int Id { get; set; }
        public DateTime ADate { get; set; }
        public string ADate2 { get; set; }
        public int SupplierID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Customs_Clearance { get; set; }
        public string ContainersNO { get; set; }
        public string Driver { get; set; }
        public int ReceivingID { get; set; }
        public int Polisa_NO { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses1 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Other_Expenses3 { get; set; }
        public string Notes1 { get; set; }
        public string Notes2 { get; set; }
        public string Notes3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal TotalExpenses { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal TotalSales { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Net { get; set; }
        public string OnlyNet { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Internal_Rent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal External_Rent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal wages_Emp { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal wages_Ma3shek { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal commSales { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal commSalesValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal commQuntity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal commQuntityValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal TotalQuntity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Mand_Fixed { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Mand_commSales { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Mand_commSales_Value { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Mand_commQuntity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Mand_commQuntity_Value { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal cooling { get; set; }
    
         
        public virtual ICollection<Vg_SuplierBill_items> Vg_SuplierBill_items { get; set; }
    }
}
