﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{

    [Table("accounts")]
    public class FalconAccounts
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long AccCode { get; set; }
        public string AccAraName { get; set; }
        public bool? flag { get; set; }
        public int? COMP_ID { get; set; }

    }
}
