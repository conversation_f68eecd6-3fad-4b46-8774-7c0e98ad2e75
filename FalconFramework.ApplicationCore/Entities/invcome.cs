

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class invcome
    {
         
        public invcome()
        {
            this.invcomeitems = new HashSet<invcomeitem>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long InvoiceNo { get; set; }
        public string InooiceNOVender { get; set; }
        public int? BeurchisingID { get; set; }
        public DateTime? idate { get; set; }
        public long? ClientID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? saltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? generaltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string aArabicTotal { get; set; }
        public string invtype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monycome { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monystill { get; set; }
        public string notes { get; set; }
        public long? iyeer { get; set; }
        public int? store_id { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public long? InvEznNo { get; set; }
        public bool? EznEdafa { get; set; }
        public bool? EznEstekhrag { get; set; }
        public long? Journal { get; set; }
        public DateTime? CollectedDate { get; set; }
        public string Tahseel { get; set; }
        public long? cat_id { get; set; }
        public string CostCategoryName { get; set; }
        public long? CostId { get; set; }
        public string CostName { get; set; }
        public long? CostLeveilID { get; set; }
        public string CostLevelName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Extra_Tax { get; set; }
        public long? BoxID { get; set; }
        public Nullable<double> Isal_Vale { get; set; }
        public long? CollectedJournal { get; set; }
        public int? SheapingID { get; set; }
        public int? ProjectID { get; set; }
        public int? DriverID { get; set; }
        public long? Destination_Type { get; set; }
        public int? destTypeId { get; set; }

        public virtual ICollection<invcomeitem> invcomeitems { get; set; }
    }
}
