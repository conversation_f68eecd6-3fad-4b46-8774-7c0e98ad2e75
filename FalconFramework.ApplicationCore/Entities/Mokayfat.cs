//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Mokayfat
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long PerformaID { get; set; }
        public long? CustomerID { get; set; }
        public string Estehlal { get; set; }
        public string Greting { get; set; }
        public long? EmpID { get; set; }
        public string Intro { get; set; }
        public string DocNo { get; set; }
        public string Date { get; set; }
        public string Date2 { get; set; }
        public string DateEnd { get; set; }
        public string Moshtamalat { get; set; }
        public string Moasafat { get; set; }
        public string NotIN { get; set; }
        public string Grantee { get; set; }
        public string PayTerms { get; set; }
    }
}
