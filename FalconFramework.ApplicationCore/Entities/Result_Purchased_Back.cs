public class Result_Purchased_Back
{
    public int id { get; set; }
    public int InvoiceNo {get;set;}
	public string store_name {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
	public double PriceOne {get;set;}
	public double Q {get;set;}
	public double TotalPrice {get;set;}
	public DateTime idate {get;set;}
	public string suppliers_name {get;set;}
	public string InooiceNOVender {get;set;}
	public string TaxType {get;set;}
	public double saltax {get;set;}
	public double generaltax {get;set;}
	public double Fright {get;set;}
	public double Descount {get;set;}
	public double net {get;set;}
	public string Barcode {get;set;}
}