//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class InvoiceCollect
    {
        [Key]
        public long ID { get; set; }
        public int? MandopID { get; set; }
        public DateTime? aDate { get; set; }
        public long? InvoiceNo { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PayValue { get; set; }
        public long? Journal { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
    }
}
