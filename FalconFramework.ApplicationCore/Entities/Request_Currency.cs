
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Request_Currency
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Request_CurrencyID { get; set; }
        public int DepartID { get; set; }
        public int? Emp_Code { get; set; }
        public DateTime? Request_Date { get; set; }
        public string Request_Time { get; set; }
        public string Request_Type { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Request_Value { get; set; }
        public int? Currancy_ID { get; set; }
        public string Request_Only { get; set; }
        public string Request_Have { get; set; }
        public string Request_Notes { get; set; }
        public string userAdd { get; set; }
        public DateTime? DateAdd { get; set; }
        public string Accountsign { get; set; }
        public string Sign { get; set; }
        public string SignNotes { get; set; }
        public int? UserID { get; set; }
        public string MalyanNote { get; set; }
        public string BoxName { get; set; }
        public string Balance_After { get; set; }
        public string Malya_Note_For_User { get; set; }
        public string Attach_Count { get; set; }
        public string docNO { get; set; }
        public bool? Sigend { get; set; }
    }
}
