using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{

    [Table("emp")]
    public partial class Employee
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Column("emp_code")]
        public long Id { get; set; }
        public string BarCode { get; set; }

        [Column("emp_name")]
        public string NameAr { get; set; }

        [Column("SecondName")]
        public string NameEn { get; set; }
        [Column("emp_add")]
        public string Address { get; set; }

        [Column("emp_tel")]
        public string PhoneNumber { get; set; }
        [Column("emp_mob")]
        public string MobileNumber { get; set; }

        [Column("emp_mail")]
        public string Email { get; set; }

        [Column("emp_den")]
        public string ReligionName { get; set; }

        [Column("emp_kinde")]
        public string Gender { get; set; }

        [Column("emp_state")]
        public string MaritalStatus { get; set; }

        public byte[] emp_pic { get; set; }
        [Column("emp_pirth")]
        public string DateOfBirth { get; set; }


        [ForeignKey("Job")]
        public long? jobid { get; set; }

        [ForeignKey("Department")]
        public long depart_id { get; set; }
        [Column("emp_jopdate")]
        public DateTime? EmploymentStartDate { get; set; }

        [Column("emp_tamen_no")]
        public string InsuranceNumber { get; set; }

        [Column("emp_helth_no")]
        public string HealthCardNumber { get; set; }

        [Column("helthfrom")]
        public string HijriEntryDate { get; set; }
        [Column("helthdatestart")]
        public DateTime? SocialInsuranceStartDate { get; set; }


        [Column("helthdateend")]
        public DateTime? SocialInsuranceEndDate { get; set; }

        [Column("helthrayaNO")]
        public string HealthRecordNumber { get; set; }

        [Column("helthrayaFrom")]
        public string InsuranceValue { get; set; }

        [Column("helthrayaDateStart")]
        public string MedicalInsuranceStartDate { get; set; }

        [Column("helthrayaDateEnd")]
        public string MedicalInsuranceEndDate { get; set; }

        [Column("emp_learn")]
        public string QualificationName { get; set; }

        [Column("emp_meltary")]
        public string MilitaryStatus { get; set; }

        [Column("emp_idc")]
        public string IDExpiryDateHijri { get; set; }

        [Column("emp_idc_from")]
        public string PassportExpiryDateHijri { get; set; }

        [Column("emp_idc_date")]
        public string HiringDateHijri { get; set; }

        [Column("emp_idc_dateend")]
        public DateTime? EntryDateToCountry { get; set; }

        [Column("emp_lwork_no")]
        public string SocialInsuranceDateHijri { get; set; }

        [Column("emp_lworkfrom")]
        public string MedicalInsuranceDateHijri { get; set; }

        [Column("emp_lwork_date")]
        public string MedicalInsuranceEndDateHijri { get; set; }

        [Column("emp_lwork_enddate")]
        public DateTime? IDExpiryDate { get; set; }

        [Column("emp_tamensallery")]
        public Nullable<double> BasicSalary { get; set; }

        [Column("emp_tamensalleryCanged")]
        public Nullable<double> FixedTax { get; set; }

        [Column("emp_sallery")]
        public Nullable<double> FixedAllowance { get; set; }

        [Column("emp_Take_Home")]
        public Nullable<double> HousingAllowance { get; set; }

        [Column("emp_Take_Bus")]
        public Nullable<double> TransportationAllowance { get; set; }

        [Column("emp_Take_Food")]
        public Nullable<double> FoodAllowance { get; set; }

        [Column("emp_Take_other")]
        public Nullable<double> OtherAllowances { get; set; }

        [Column("RayaDescount")]
        public Nullable<double> HealthCareDiscount { get; set; }

        [Column("anohterDescount")]
        public Nullable<double> InsuranceSalary { get; set; }

        [Column("EmpBoxDescount")]
        public Nullable<double> EmployeeFundDeduction { get; set; }

        [Column("previousJob")]
        public string VisaJobTitleId { get; set; }

        [Column("previousJopPlace")]
        public string PreviousJobPlace { get; set; }

        [Column("WhyLivPrviousJop")]
        public string WhyLeavePreviousJob { get; set; }

        [Column("dateliv")]
        public DateTime? DateLeavePreviousJob { get; set; }

        [Column("hasbendJop")]
        public string SpouseName { get; set; }

        [Column("jophasbendplace")]
        public string VisaNumber { get; set; }

        [Column("hasbendphne")]
        public string SpousePhoneNumber { get; set; }

        [Column("OrignalContry")]
        public string BankName { get; set; }


        [Column("VacationAdress")]
        public string BirthDateHijri { get; set; }

        [Column("VacPhone")]
        public string EmployerName { get; set; }

        public string RelativeName { get; set; }
        public string RelativePhone { get; set; }
        public string SonCont { get; set; }
        public string Notes { get; set; }

        [ForeignKey("Shift")]
        public int? ShiftID { get; set; }
        public long? Planid { get; set; }
        [Column("ProunchID")]
        public int? branchId { get; set; }
        [Column("COMP_ID")]
        public int? companyId { get; set; }
        public bool? flag { get; set; }
        [Column("HavezEntetzam")]
        public Nullable<double> CommunicationAllowance { get; set; }
        public int? CountryID { get; set; }
        [ForeignKey("Nationality")]
        public int? NationalityID { get; set; }
        public string PassportID { get; set; }
        public string IqamaID { get; set; }
        public string Certified { get; set; }
        public string EmploymentCode { get; set; }
        public string EmploymentGDS { get; set; }
        public string City { get; set; }
        public string Zip { get; set; }
        public string SocialTitle { get; set; }
        public string GalileoSign { get; set; }
        public string AmadeusSign { get; set; }
        public string NationalityCity { get; set; }
        public string datehalthstart2 { get; set; }
        public string datehelthend2 { get; set; }
        public string datehelthrayaStart2 { get; set; }
        public string DateHelthrayaEnd2 { get; set; }
        [Column("dtbidc2")]
        public string TicketCount { get; set; }

        public string dtbidcend2 { get; set; }
        public string dtblwork2 { get; set; }
        public string dtblworkend2 { get; set; }
        [Column("Dengers")]
        public Nullable<double> WorkNatureAllowance { get; set; }

        [Column("datein")]
        public string PassportIssueDate { get; set; }

        [Column("officno")]
        public string LaborOfficeNumber { get; set; }

        [Column("hdodno")]
        public string BorderNumber { get; set; }

        [Column("coname")]
        public string CompanyNameLaborOffice { get; set; }

        [Column("co_no")]
        public string CompanyNumber { get; set; }

        public string driver_no { get; set; }
        public string driver_from { get; set; }
        public DateTime? date_driver1 { get; set; }
        public DateTime? date_driver_2 { get; set; }
        public string driver_1 { get; set; }
        public string driver_2 { get; set; }
        [Column("empState")]
        public string EmployeeStatus { get; set; }

        public string Statenotes { get; set; }
        [Column("Mohafzet_Badal")]
        public string SectorName { get; set; }

        [Column("DateTakharog")]
        public DateTime? GraduationDate { get; set; }

        [Column("DateMaash")]
        public DateTime? RetirementDate { get; set; }

        [Column("MawkefTameny")]
        public string InsuranceStatus { get; set; }

        [Column("MokefWazefy")]
        public string JobStatus { get; set; }

        public string ShortName { get; set; }
        [Column("solfa_Account")]
        public long? AdvanceAccountCode { get; set; }

        [Column("Ohda_Account")]
        public long? CustodyAccountCode { get; set; }

        public long? Sallary_Account { get; set; }

        [Column("solfa_Account_Name")]
        public string AdvanceAccount { get; set; }

        [Column("Ohda_Account_Name")]
        public string CustodyAccount { get; set; }

        public string Sallary_Account_Name { get; set; }

        [ForeignKey("Sector")]
        [Column("Sec_id")]
        public long? SectorId { get; set; }

        public int? UserID { get; set; }
        [Column("BankNo")]
        public string BankAccountNumber { get; set; }

        [Column("SalaryType")]
        public string SalaryGroup { get; set; }

        public byte[] emp_Sigen { get; set; }
        [Column("contract_period")]
        public long? ContractPeriod { get; set; }
        public long? YaerlyVacation { get; set; }
        [Column("YearlyTiket", TypeName = "decimal(18, 2)")]

        public decimal? YearlyTicket { get; set; }

        [Column("VacPrevious")]
        public int? PreviousVacations { get; set; }

        [Column("Jop_Eqama")]
        public string ResidencyJobTitle { get; set; }

        public int? Last_Month_DayCount { get; set; }
        [Column("TamenValue", TypeName = "decimal(18, 2)")]

        public decimal? MedicalInsuranceValue { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PastY1 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PastY2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PastY3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PastY4 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PastY5 { get; set; }

        //[ForeignKey("AnalyticAccount")]
        public long? CostID { get; set; }
        [Column("ShowInSolfa")]
        public bool? AllowSalaryAdvance { get; set; }

        [Column("ShowInOhda")]
        public bool? AllowCustody { get; set; }

        [Column("ShowIn_Badal_Sakan")]
        public bool? AllowHousingAllowance { get; set; }


        [ForeignKey("Kafeel")]
        public long? KafeelID { get; set; }

        [Column("Take_Schole")]
        public Nullable<double> SchoolAllowance { get; set; }

        [Column("Take_Percent")]
        public Nullable<double> PercentageAllowance { get; set; }


        public DateTime? StatusDate { get; set; }
        public bool? ISDriver { get; set; }
        public string WorK_Lic_NO { get; set; }
        public Nullable<double> Work_Lic_Value { get; set; }
        public Nullable<double> ID_Renewual_Value { get; set; }
        public int? StatusCaseID { get; set; }
        public bool? Eff_Badal_Ta3am { get; set; }
        public bool? Eff_Badal_Sakan { get; set; }
        public bool? Eff_Badal_Entekal { get; set; }
        public bool? Eff_Badal_Tabe3a { get; set; }
        public bool? Eff_Badal_Other { get; set; }
        public bool? Eff_Badal_tel { get; set; }
        public bool? Eff_Take_Schole { get; set; }
        public bool? VEff_Badal_Ta3am { get; set; }
        public bool? VEff_Badal_Sakan { get; set; }
        public bool? VEff_Badal_Entekal { get; set; }
        public bool? VEff_Badal_Tabe3a { get; set; }
        public bool? VEff_Badal_Other { get; set; }
        public bool? VEff_Badal_tel { get; set; }
        public bool? VEff_Take_Schole { get; set; }
        public bool? ES_Eff_Badal_Ta3am { get; set; }
        public bool? ES_Eff_Badal_Sakan { get; set; }
        public bool? ES_Eff_Badal_Entekal { get; set; }
        public bool? ES_Eff_Badal_Tabe3a { get; set; }
        public bool? ES_Eff_Badal_Other { get; set; }
        public bool? ES_Eff_Badal_tel { get; set; }
        public bool? ES_Eff_Take_Schole { get; set; }
        public bool? StopSallery { get; set; }
        public bool? StopNewID { get; set; }
        public bool? StopVacation { get; set; }
        public int? JobID_in_ID { get; set; }

        [ForeignKey("Qualification")]
        public long? QualificationID { get; set; }

        [ForeignKey("Religion")]

        [Column("DyanaID")]
        public int? religionId { get; set; }

        public string EmployeePassword { get; set; }
        public bool? Stop_To_Bank { get; set; }
        public int? EsdarIqama { get; set; }
        public bool? InsuranceDeduction { get; set; }
        public bool? Vacation30 { get; set; }
        public string Health_Number { get; set; }
        public DateTime? End_Health_Date { get; set; }
        [Column("Municipal")]
        public string Municipality { get; set; }

        public int? ReportedToEmpId { get; set; }
        public string Tier { get; set; }

        // موقفها لان انا هنا بحفظ كود الحساب مش البرايمري كى لحد ما اشوف
        //public virtual TblCostTree AnalyticAccount { get; set; }

        public virtual departments Department { get; set; }

        public virtual Branches Branch { get; set; }
        public virtual Companies Company { get; set; }
        public virtual Shift Shift { get; set; }
        public virtual jobs Job { get; set; }

        public virtual Nationality Nationality { get; set; }
        public virtual Sectors Sector { get; set; }

        public virtual Kafeel Kafeel { get; set; }

        public virtual Qualification Qualification { get; set; }

        public virtual Religion Religion { get; set; }
    }
}
