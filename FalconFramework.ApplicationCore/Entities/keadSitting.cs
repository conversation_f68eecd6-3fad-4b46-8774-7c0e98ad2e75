using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class keadSitting
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int id { get; set; }
    public long AccID {get;set;}
	public string AccName {get;set;}
	public string KedDescirpe {get;set;}
	public long ID {get;set;}
	public long EznID {get;set;}
	public long CustTypeID {get;set;}
	public long StoreID {get;set;}
	public long SuppTypeID {get;set;}
	public long BoxID {get;set;}
	public long SalesType {get;set;}
	public long BurshaseType {get;set;}
	public long bankAccountID {get;set;}
	public int typeID {get;set;}
	public int Asl_ID {get;set;}
	public long CostID {get;set;}
	public string MovType {get;set;}
	public int LetterTypeID {get;set;}
	public int DocumentTypeID {get;set;}
	public int LoanTypeID {get;set;}
	public int ProunchID {get;set;}
	public long Tender_Item_ID {get;set;}
	public long Tender_ID {get;set;}
	public int Sub_Goroup_ID {get;set;}
	public int SheapingID {get;set;}
	public int CustTypeID_Income {get;set;}
	public int PartnerId {get;set;}
	public int SaleManTypeId {get;set;}
}