//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Vg_SuplierBill_items
    {
        public long Id { get; set; }
        public long ItemCode { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Quantiry { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public int? Vg_SuplierBill_Id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Returns_mony { get; set; }
        public int? Q_Statuse { get; set; }
    
        public virtual Vg_SuplierBill Vg_SuplierBill { get; set; }
    }
}
