
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(serialitem))]

public class Mardodat_Byan_items
{
	public long InvoiceNo {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}

	public string SN {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Bonas {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal despecent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemDescount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal AllQuantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemCost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemMaksab {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal DefaltCost {get;set;}
	public long CostId {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Rate {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal CostAllItemOut {get;set;}
	public int Item_Unit_Id {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Defualt_ItemCost {get;set;}
	public int SaleUnitID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal maksap {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal UnitCost {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Unit_Balance {get;set;}
	public string VariantName {get;set;}
}