//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class UpDate_Salary
    {
        [Key]
        public long ID { get; set; }
        public long Emp_Code { get; set; }
        public string Emp_Name { get; set; }
        public bool? Signed { get; set; }
        public long? emp_tamensallery { get; set; }
        public long? emp_sallery { get; set; }
        public long? emp_tamensalleryCanged { get; set; }
        public long? emp_Take_Bus { get; set; }
        public long? emp_Take_Food { get; set; }
        public long? HavezEntetzam { get; set; }
        public long? emp_Take_Home { get; set; }
        public long? Take_Schole { get; set; }
        public long? emp_Take_other { get; set; }
        public long? Dengers { get; set; }
        public string Notes { get; set; }
        public DateTime? aDate { get; set; }
        public string GM_Note { get; set; }
    }
}
