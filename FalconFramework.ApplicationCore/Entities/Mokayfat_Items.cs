//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(PerformaID), nameof(Mserial))]
    public partial class Mokayfat_Items
    {
        public long PerformaID { get; set; }
        public string Mserial { get; set; }
        public string MDetails { get; set; }
        public string MType { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MPower { get; set; }
        public string MUnit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MPrice { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MQ { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Mtotal { get; set; }
    }
}
