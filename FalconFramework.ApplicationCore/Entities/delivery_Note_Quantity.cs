//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class delivery_Note_Quantity
    {
        public long Id { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Qout { get; set; }
        public long? InvoiceId { get; set; }
        public bool? TaxBill { get; set; }
        public string UserName { get; set; }
        public int? StoreId { get; set; }
        public DateTime? Dateout { get; set; }
        public long? ItemId { get; set; }
        public int? Srial { get; set; }
    }
}
