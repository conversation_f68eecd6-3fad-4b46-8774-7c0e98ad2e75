﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.Entities
{
    public class cv
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long CVID { get; set; }
        public string? companename { get; set; }
        public string? jop { get; set; }
        public string? dfrom { get; set; }
        public string? dto { get; set; }
        public string? salary { get; set; }
        public string? whyleftwork { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
    }
}
