using System.ComponentModel.DataAnnotations.Schema;

public class Cust_Follw
{
    public int id { get; set; }
    public string from_date {get;set;}
	public string ToDate {get;set;}
	public string StoreName {get;set;}
	public string CustId {get;set;}
	public string Cust_Name {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal sales {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal descount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal bak {get;set;}
	public string net {get;set;}

}