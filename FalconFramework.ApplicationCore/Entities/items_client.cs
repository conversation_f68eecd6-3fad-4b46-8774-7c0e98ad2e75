//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class items_client
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Customer_id { get; set; }
        public long? contact_person_id { get; set; }
        public long? serial { get; set; }
        public string describe { get; set; }
        public DateTime? come_date { get; set; }
        public DateTime? oute_date { get; set; }
        public string person_name { get; set; }
        public string id_no { get; set; }
        public string car_no { get; set; }
        public string notes { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
