
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(MID), nameof(SN))]
public class Mostakhlas_items
{
	public long MID {get;set;}
	public long SN {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemValue {get;set;}

	public string ItemDescripe {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_our_Percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_Our_Value {get;set;}
	public long VendorID {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_Ven_percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Item_ven_Value {get;set;}
}