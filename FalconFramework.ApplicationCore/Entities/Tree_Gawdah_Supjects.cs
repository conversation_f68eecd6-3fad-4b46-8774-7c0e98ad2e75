//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Tree_Gawdah_Supjects
    {
        [Key]
        public int auto_id { get; set; }
        public int? NodeID { get; set; }
        public DateTime? posted { get; set; }
        public string follow_by { get; set; }
        public DateTime? follow_date { get; set; }
        public string phone { get; set; }
        public string subject { get; set; }
        public string Contents { get; set; }
        public string status { get; set; }
        public byte[] upsize_ts { get; set; }
        public bool? archive { get; set; }
    }
}
