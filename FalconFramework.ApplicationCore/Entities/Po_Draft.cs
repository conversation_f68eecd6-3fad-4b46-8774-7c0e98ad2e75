//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Po_Draft
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long order_no { get; set; }
        public string depart { get; set; }
        public string Manager { get; set; }
        public string employee { get; set; }
        public DateTime? order_date { get; set; }
        public string order_Time { get; set; }
        public string GmSignature { get; set; }
        public string StorSignature { get; set; }
        public string purshasingSignature { get; set; }
        public bool? flag { get; set; }
        public DateTime? action_date { get; set; }
        public string userName { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string OrderType { get; set; }
        public string CustomerName { get; set; }
        public string ContactPerson { get; set; }
        public string TenderNO { get; set; }
        public DateTime? DateTender { get; set; }
        public string TenderTamen { get; set; }
        public string UsersWilMake { get; set; }
    }
}
