

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Rents
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long RentID { get; set; }
        public DateTime? RentDate { get; set; }
        public string Rname { get; set; }
        public string IdNo { get; set; }
        public DateTime? IdDate { get; set; }
        public string Nationality { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Total { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tamen { get; set; }
        public string Statebefor { get; set; }
        public string StetAfter { get; set; }
        public bool? krar { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? tamenback { get; set; }
        public string Imgfron { get; set; }
        public string imgback { get; set; }
        public string kararnote { get; set; }
        public long? StoreID { get; set; }
        public long? ClientID { get; set; }
        public long? Journal { get; set; }
        public DateTime? dateto { get; set; }
        public int? NationalityID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Discount { get; set; }
        public int? Year { get; set; }
        public int? ProunchID { get; set; }
        public Nullable<double> NetBeforTax { get; set; }
        public Nullable<double> NetAfterTax { get; set; }
        public Nullable<double> VAT { get; set; }
        public Nullable<double> VATPercent { get; set; }
    }
}
