using Microsoft.EntityFrameworkCore;

[PrimaryKey(nameof(emp_id), nameof(edafy_date))]
public class emp_edafy
{
	public long emp_id {get;set;}
	public DateTime edafy_date {get;set;}
	public string edafy_from {get;set;}
	public string edafy_to {get;set;}
	public double edafy_time {get;set;}
	public double HOURTO {get;set;}
	public double addvalue {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
}