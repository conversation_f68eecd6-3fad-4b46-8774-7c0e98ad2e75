//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class customer_account
    {
        [Key]
        public long id { get; set; }
        public long venderid { get; set; }
        public string year { get; set; }
        public string actionname { get; set; }
        public long? invno_ormony_id { get; set; }
        public DateTime? movedate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daen { get; set; }
        public int? ACCID { get; set; }
        public string ACC_Name { get; set; }
        public string Detail { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CuttingWillBake { get; set; }
        public int? ACCID2 { get; set; }
        public string ACC_Name2 { get; set; }
        public string Detail2 { get; set; }
        public Nullable<double> CuttingNotBack { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        public string Notes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Taxes { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Stamps { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Others { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ChekNet { get; set; }
        public int? CurID { get; set; }
        public long? Mandob_ID { get; set; }
        public long? Flag1 { get; set; }
        public long? Flag2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Flag3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Flag4 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        public string DayName { get; set; }
        public int? MasrofaaaatID { get; set; }
        public int? MakbodaaaaatID { get; set; }
        public long? MakbodatID_Bank { get; set; }
        public long? MasrofaaaatID_Bank { get; set; }
        public int? InvtaxID { get; set; }
        public int? ByanID { get; set; }
        public int? Mardodat_inv_ID { get; set; }
        public int? Mardoat_byan_id { get; set; }
        public int? BoxMovID { get; set; }
        public int? Awrak_Daf3 { get; set; }
        public int? Awrak_kabd { get; set; }
        public int? EasweyaID { get; set; }
        public int? Esh3arID { get; set; }
        public int? Tasweyat_BankID { get; set; }
        public int? FristBalance { get; set; }
        public long? TaswyaMasroof_ID { get; set; }
        public string contType { get; set; }
        public int? QestNO { get; set; }
        public int? ContNO { get; set; }
        public int? CaseID { get; set; }
        public string CaseName { get; set; }
        public long? Journal { get; set; }
        public string DocNo { get; set; }
        public bool? IsStartedBalance { get; set; }
        public long? InvtaxID_Guardians { get; set; }
        public int? ProjectID { get; set; }
        public string financial_entity { get; set; }
        public long? financial_entity_Type { get; set; }
        public long? financial_entity_Id { get; set; }
    }
}
