using Microsoft.EntityFrameworkCore;

[Primary<PERSON>ey(nameof(requierid), nameof(serial))]
public class customers_requirement_items
{
	public long requierid {get;set;}
	public long serial {get;set;}
	public string contact_person_id {get;set;}
	public string item {get;set;}
	public string state {get;set;}
	public string Notes {get;set;}
	public string user_id {get;set;}
	public DateTime action_date {get;set;}
	public bool flag {get;set;}
	public int COMP_ID {get;set;}
	public int ProunchID {get;set;}
	public DateTime date_done {get;set;}

}