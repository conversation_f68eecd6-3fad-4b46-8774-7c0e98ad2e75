//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class invoiceout_Guardians
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long InvoiceNo { get; set; }
        public DateTime? idate { get; set; }
        public long? ClientID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? aTotal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? saltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? generaltax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Fright { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? net { get; set; }
        public string aArabicTotal { get; set; }
        public string invtype { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monycome { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? monystill { get; set; }
        public string notes { get; set; }
        public long? iyeer { get; set; }
        public string servicetype { get; set; }
        public bool? flag { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Tahseel { get; set; }
        public string TahseelNote { get; set; }
        public DateTime? tahseldate { get; set; }
        public long? InvEznNo { get; set; }
        public bool? Isused { get; set; }
        public long? Journal { get; set; }
        public long? Contact_ID { get; set; }
        public DateTime? CollectedDate { get; set; }
        public DateTime? Fallowdate { get; set; }
        public DateTime? SuplayDate { get; set; }
        public string ChekDate { get; set; }
        public long? ChekNO { get; set; }
        public string BankName { get; set; }
        public bool? TaxDes { get; set; }
        public bool? TaxDoc { get; set; }
        public string UserColletMony { get; set; }
        public long? Isal_NO { get; set; }
        public Nullable<double> Isal_Vale { get; set; }
        public long? Defrent_Collect { get; set; }
        public long? cat_id { get; set; }
        public string CostCategoryName { get; set; }
        public long? CostId { get; set; }
        public string CostName { get; set; }
        public long? CostLeveilID { get; set; }
        public string CostLevelName { get; set; }
        public int? saletype { get; set; }
        public string Mandop_Name { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Extra_Tax { get; set; }
        public int? MandopID { get; set; }
        public long? BoxID { get; set; }
        public long? CollectedJournal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount_Percent { get; set; }
        public string DocNo { get; set; }
        public string DriverName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ExtraMoney_Percent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ExtraMoney_Value { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CardValue { get; set; }
        public string dayname { get; set; }
        public long? ProjectID { get; set; }
        public long? ExtractCount { get; set; }
        public long? ProjectID_Tender_Item_ID { get; set; }
        public int? SheapingID { get; set; }
        public int? invtypeID { get; set; }
    }
}
