//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class RentContract_List
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long Id { get; set; }
        public DateTime ContractDate { get; set; }
        public int Main_Properties_list_Id { get; set; }
        public long? Properties_list_Id { get; set; }
        public short LeaseType { get; set; }
        public Nullable<double> PaymentValue { get; set; }
        public double LeaseValue { get; set; }
        public Nullable<double> TaxPercent { get; set; }
        public Nullable<double> TaxAmount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime? ExtendDate { get; set; }
        public Nullable<double> ExtraFee { get; set; }
        public Nullable<double> ContractFee { get; set; }
        public Nullable<double> ContractFeeTaxAmount { get; set; }
        public Nullable<double> TotalValue { get; set; }
        public Nullable<double> Insurance { get; set; }
        public Nullable<double> Net { get; set; }
        public string NetOnly { get; set; }
        public string Term { get; set; }
        public bool? Terminated { get; set; }
        public bool Posted { get; set; }
        public Nullable<short> Printed { get; set; }
        public int? Journal { get; set; }
        public Nullable<short> Period { get; set; }
        public bool? Hold { get; set; }
        public bool Approved { get; set; }
        public int? ApprovedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public int? MandopId { get; set; }
        public long? CustomerId { get; set; }
        public string DayName { get; set; }
        public string DayName2 { get; set; }
        public string RentTo { get; set; }
        public string DicountPeriod { get; set; }
        public Nullable<double> DiscountPecent { get; set; }
        public Nullable<double> DiscountValue { get; set; }
        public DateTime? DiscountFrom { get; set; }
        public string DiscountFrom2 { get; set; }
        public DateTime? DiscountTo { get; set; }
        public string DiscountTo2 { get; set; }
        public string DicountOnly { get; set; }
        public string RentPeriodOnly { get; set; }
        public string VatOnly { get; set; }
        public string Tenant_ID { get; set; }
        public string Tenant_Name { get; set; }
        public DateTime? FreeFrom { get; set; }
        public int? CurID { get; set; }
        public string AdjustNote { get; set; }
        public string CancelNote { get; set; }
        public DateTime? CancelDate { get; set; }
    }
}
