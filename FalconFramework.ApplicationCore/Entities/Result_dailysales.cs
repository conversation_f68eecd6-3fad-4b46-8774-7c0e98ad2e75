using System.ComponentModel.DataAnnotations.Schema;

public class Result_dailysales
{
    public int id { get; set; }
    public int InvoiceNo {get;set;}
	public string store_name {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal aTotal {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public DateTime idate {get;set;}
	public string customer_name {get;set;}
	public string InooiceNOVender {get;set;}
	public string TaxType {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal saltax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal generaltax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Fright {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Descount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal net {get;set;}
	public long Journal {get;set;}
	public long SuppID {get;set;}
	public string servicetype {get;set;}
	public string invtype {get;set;}
	public string DriverName {get;set;}
	public string MandopName {get;set;}
	public double ExtraMoney_Value {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Valitemtax {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Items_Price {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Discount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Discount_Rate {get;set;}
	public string Barcode {get;set;}
	public int DiscountCalculated {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal CardValue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Isal_Vale {get;set;}
	public string notes {get;set;}
	public double BankAmount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal monycome {get;set;}
	public string BranchName {get;set;}
    public string ShippingCompany { get;set;}
    public string InvEznNo { get; set; }


}