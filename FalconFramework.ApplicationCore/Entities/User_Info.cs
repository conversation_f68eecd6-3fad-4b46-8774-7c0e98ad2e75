//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class User_Info
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public  Guid UserID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string Sex { get; set; }
        public DateTime BirthDate { get; set; }
        public int NationalityID { get; set; }
        public string MobilePhone { get; set; }
        public string Address { get; set; }
        public int CountryID { get; set; }
        public string City { get; set; }
        public string Zip { get; set; }
        public string SecretKey { get; set; }
        public DateTime? SecretKeyDate { get; set; }
        public string TempEmail { get; set; }
        public string TempMobilePhone { get; set; }
        public string TempPassword { get; set; }
        public string Title { get; set; }
        public string CustomerNumber { get; set; }
    }
}
