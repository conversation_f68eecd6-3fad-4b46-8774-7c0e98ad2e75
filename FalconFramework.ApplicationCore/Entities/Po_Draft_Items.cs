//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(order_no), nameof(serial))]
    public partial class Po_Draft_Items
    {
        public long order_no { get; set; }
        public int? ItemCode { get; set; }
        public long? serial { get; set; }
        public string item { get; set; }
        public string unit { get; set; }
        public long? quantity { get; set; }
        public int? mohla { get; set; }
        public DateTime? DateNeed { get; set; }
        public string notes { get; set; }
        public long? credit { get; set; }
        public long? weeneed { get; set; }
        public long? limet { get; set; }
        public int? quantitycom { get; set; }
        public int? quantitystill { get; set; }
        public bool? state { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string vendorname { get; set; }
        public Nullable<double> pricecome { get; set; }
        public long? CostId { get; set; }
        public string ConstControl { get; set; }
        public DateTime? ConstControlStart { get; set; }
        public DateTime? ConstControlStartEnd { get; set; }
        public string ConstControlNOtes { get; set; }
        public string ProjectManeger { get; set; }
        public DateTime? ProjectManegerStart { get; set; }
        public DateTime? ProjectManegerEnd { get; set; }
        public string ProjectManegerNOtes { get; set; }
        public string ExcutiveManager { get; set; }
        public DateTime? ExcutiveManagerStart { get; set; }
        public DateTime? ExcutiveManagerEnd { get; set; }
        public string ExcutiveManagerNotes { get; set; }
    }
}
