//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{


    [PrimaryKey(nameof(ID), nameof(srial))]
    public partial class Taqseet_Items
    {
        public long ID { get; set; }
        public long? srial { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? MonyValue { get; set; }
        public DateTime? CycleDate { get; set; }
        public string monyonly { get; set; }
    
        public virtual Taqseet Taqseet { get; set; }
    }
}
