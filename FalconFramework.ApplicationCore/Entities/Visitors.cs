
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Visitors
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int VisitID { get; set; }
        public string VisitDatetime { get; set; }
        public string VisitorsName { get; set; }
        public string VisitPurpose { get; set; }
        public DateTime? EnterTime { get; set; }
        public DateTime? ExitTime { get; set; }
        public string CarNum { get; set; }
        public string Note { get; set; }
    }
}
