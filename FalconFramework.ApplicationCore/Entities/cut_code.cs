//-----

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class cut_code
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int cut_code_id { get; set; }
        public string cut_code_name { get; set; }
        public bool? flag { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CutDay { get; set; }
        public string EnName { get; set; }
    }
}
