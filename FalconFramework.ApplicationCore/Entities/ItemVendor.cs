
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(Item_Id), nameof(suppliers_id))]
public class ItemVendor
{
	public long Item_Id {get;set;}
	public long suppliers_id {get;set;}
	public string itemsupid {get;set;}
	public double supprice {get;set;}
	public double venderbonas {get;set;}
	public double venderdescount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Quntity {get;set;}
	public long invoiceNo {get;set;}
}