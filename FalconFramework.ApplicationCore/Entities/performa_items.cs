using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class performa_items
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public long performa_id {get;set;}
	public long serial {get;set;}
	public long Item_Id {get;set;}
	public string item_name {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal price {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal quantity {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal total {get;set;}
	public int ProunchID {get;set;}
	public int COMP_ID {get;set;}
	public string Qty1 {get;set;}
	public string price1 {get;set;}
	public string Qty2 {get;set;}
	public string Price2 {get;set;}
	public string Qty3 {get;set;}
	public string Price3 {get;set;}
	public string Qty4 {get;set;}
	public string Price4 {get;set;}
	public string Qty5 {get;set;}
	public string Price5 {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ItemVAT {get;set;}
	public string Model {get;set;}
	public int CostId {get;set;}
	public string Prodctnote {get;set;}
	public string VariantName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Cost {get;set;}
	public string SN {get;set;}
}