

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Falcon_Sitting
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public int SitingID { get; set; }
        public bool? Effict_Stores_From_Invoices { get; set; }
        public bool? falcondata { get; set; }
        public bool? EPD { get; set; }
        public bool? Lenth { get; set; }
        public bool? QTY { get; set; }
        public bool? Normal { get; set; }
        public bool? usebox { get; set; }
        public bool? CachierForm { get; set; }
        public bool? Autoprint { get; set; }
        public bool? BarcodGeneral { get; set; }
        public bool? Autonew { get; set; }
        public bool? usetaxes { get; set; }
        public bool? Shobox { get; set; }
        public bool? PriceAsLevel { get; set; }
        public Nullable<double> ComfirstPayFirst { get; set; }
        public bool? printestlam { get; set; }
        public bool? printtahdeer { get; set; }
        public bool? printsarf { get; set; }
        public bool? fokasQuantity { get; set; }
        public bool? FokasPrice { get; set; }
        public bool? FokasSeial { get; set; }
        public bool? Sale_With_Percent { get; set; }
        public bool? LokYearly { get; set; }
        public bool? UseVacation { get; set; }
        public bool? GardDawry { get; set; }
        public bool? Badal_Ta3am { get; set; }
        public bool? Badal_Sakan { get; set; }
        public bool? Badal_thabet { get; set; }
        public bool? Badal_Entekal { get; set; }
        public bool? Badal_Tabe3a { get; set; }
        public bool? Badal_Other { get; set; }
        public bool? Badal_tel { get; set; }
        public bool? Take_Schole { get; set; }
        public bool? BudgetFromTblCostTree { get; set; }
        public byte[] FVTVCO { get; set; }
        public byte[] FLC { get; set; }
        public bool? PONOrmal { get; set; }
        public bool? RebateItemName { get; set; }
        public bool? AutoArchive { get; set; }
        public bool? EmpAutoNO { get; set; }
        public bool? OrderBySubGroups { get; set; }
        public bool? PriceList_Depending_On_Pranch { get; set; }
        public bool? Sell_by_PriceList { get; set; }
        public bool? Sell_by_each_store { get; set; }
        public bool? Sell_by_Quantiry { get; set; }
        public bool? Use_Car_Emp { get; set; }
        public int? QTY_Dgit { get; set; }
        public int? MNY_Dgit { get; set; }
        public bool? Auto_Plan_Emp { get; set; }
        public bool? UnionSrial { get; set; }
        public bool? Serial_Per_Branch { get; set; }
        public bool? UnionSrial_Recivable { get; set; }
        public bool? Price_Includes_Tax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? VAT_Rate { get; set; }
        public int? UseDefaultLanguage { get; set; }
        public bool? MandatoryCost { get; set; }
        public bool? Use_cost_Per_Line { get; set; }
        public bool? Use_Weight_InvoiceOut { get; set; }
        public bool? Link_InvCom_InvOut { get; set; }
        public bool? JournalVoucherCost { get; set; }
        public int? Show_Last_Purchase { get; set; }
        public bool? Ch_FirstStock { get; set; }
        public bool? Ch_Invoices_Come { get; set; }
        public bool? CH_Ezn_Edava { get; set; }
        public bool? Ch_StoreTeansfeer { get; set; }
        public bool? AccountStatementEffect { get; set; }
        public bool? StatementFromChart { get; set; }
        public bool? Use_cost_Per_Line_Sarf { get; set; }
        public bool? Use_ItemGorupValidity { get; set; }
        public bool? Use_StoreValidity { get; set; }
        public bool? Use_BoxValidity { get; set; }
        public bool? Use_WorkPlanValidity { get; set; }
        public bool? Use_EmpDepartmentValidity { get; set; }
        public bool? Journal_Detailes { get; set; }
        public bool? Eff_BlockAccounts { get; set; }
        public bool? POS_Is_Weight { get; set; }
        public int? POS_Weight_Digit { get; set; }
        public bool? InvComWithBarcode { get; set; }
        public bool? Effict_Purchases_Journal_From_PurchasesType { get; set; }
        public bool? Sort_by_Name { get; set; }
        public bool? SumAccount_theardpaty { get; set; }
        public string AccountPass { get; set; }
        public bool? JournalByItems { get; set; }
        public bool? UseChekDafter { get; set; }
        public bool? UseEmpIncomeTax { get; set; }
        public bool? useKyanInDaen { get; set; }
        public bool? UseCashControle { get; set; }
        public int? ReturnDayes { get; set; }
        public int? MessageingTypeID { get; set; }
        public bool? MsgSale { get; set; }
        public bool? MsgHR { get; set; }
        public bool? MsgPurchase { get; set; }
        public bool? MsgInventory { get; set; }
        public bool? MsgAccounting { get; set; }
        public bool? LastCustomerPrice { get; set; }
        public bool? PurchaseVariantGrid { get; set; }
        public bool? SalesVariantGrid { get; set; }

    }
}
