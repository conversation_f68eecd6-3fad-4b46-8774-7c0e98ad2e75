//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SalaryReport_Gosi
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long code { get; set; }
        public string name { get; set; }
        public string jop { get; set; }
        public string brnchs { get; set; }
        public string ksm { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal oth_sal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? others3 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? hawafez { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Salary { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal m_sal { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? b_ent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? b_tab { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? b_malbas { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? b_food { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? totalm { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? takher { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? solfa_cut { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? gyab { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? gaza { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ins { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? tax { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? totalkh { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? safy { get; set; }
        public long? brnchid { get; set; }
        public long? ksmid { get; set; }
        public int? month { get; set; }
        public int? year { get; set; }
        public int? Day { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? a_ins { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? d_ins { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? w_a_ins { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? w_d_ins { get; set; }

        public string accno { get; set; }
        public string compcode { get; set; }
        public string compName { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? houraddagaza { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daygazaa { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daytakher { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? daygyab { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? moneygazaa { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? moneytakher { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? moneygyab { get; set; }
        public long? planid { get; set; }
        public int? NationalityID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ed_agaza { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ed_sahar { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? other_add { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? elawa { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? mobile { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tel { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? nafaa { get; set; }
        public string SalaryType { get; set; }
        public bool? Lokked { get; set; }
        public Nullable<double> Take_Schole { get; set; }
        public Nullable<double> Take_Percent { get; set; }
        public string BarCode { get; set; }
        public string Nationality { get; set; }
        public Nullable<double> anohterDescount { get; set; }
        public Nullable<double> Count_Edafy_Hours { get; set; }
        public string Kafeel { get; set; }
        public string WorkPlan { get; set; }
        public DateTime? DateComBak { get; set; }
        public string Depart_EnglishName { get; set; }
        public string Notes { get; set; }
        public bool? Delivered { get; set; }
        public int? MonthDayCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? OwnerValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Sumary { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Paid { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? diffrent { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ins_Gosi { get; set; }
        public string EmpFix { get; set; }
        public string EmpChanged { get; set; }
        public string ComFix { get; set; }
        public string ComChanged { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Other_Penalty { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? safy2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? safy_Basic { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defrant_to_emp { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Tel_Basic { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Defrant_to_emp_Extra { get; set; }
    }
}
