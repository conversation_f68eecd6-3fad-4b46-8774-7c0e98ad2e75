using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(serialitem))]
public class invcomeitemNoTaxTemb
{
	public long InvoiceNo {get;set;}
	public long serialitem {get;set;}
	public long itemid {get;set;}
	public string ITEMS_ITEM_NAME {get;set;}
	public string unit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal PriceOne {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Q {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalPrice {get;set;}
	public string SN {get;set;}
	public double ponas_percent {get;set;}
	public double ponsa_val {get;set;}
	public double descount_percent {get;set;}
	public double descount_val {get;set;}
	public double total_quantity {get;set;}
	public bool Journal {get;set;}
}