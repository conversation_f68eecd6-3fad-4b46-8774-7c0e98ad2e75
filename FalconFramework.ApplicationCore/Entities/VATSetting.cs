
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class VATSetting
    {
        public int Id { get; set; }
        public int? Code { get; set; }
        public long? Accid { get; set; }
        public bool? Selected { get; set; }
        public bool? VAT { get; set; }
        public bool? Switch_to_positive { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Balance { get; set; }
    }
}
