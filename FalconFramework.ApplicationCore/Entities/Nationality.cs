
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
    public partial class Nationality
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int NationalityID { get; set; }
       [Column("Nationality")]
        public string Nationality_AR { get; set; }
        public bool? Flag { get; set; }
        public string Nationality_En { get; set; }
        public bool? Citizen { get; set; }
    }
}
