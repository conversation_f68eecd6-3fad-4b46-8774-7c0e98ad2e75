

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class Item_Card_Journal
    {
        [Key]
        public long ID { get; set; }
        public long? Item_ID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Maden { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Daen { get; set; }
        public long? AccID { get; set; }
        public string AccName { get; set; }
        public string Details { get; set; }
        public long? Costid { get; set; }
        public DateTime? aDate { get; set; }
        public int? year { get; set; }
        public long? Journal { get; set; }
        public int? Gro_Sub_ID { get; set; }
    }
}
