
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[<PERSON><PERSON><PERSON>(nameof(InvoiceNo), nameof(itemid))]
public class Loan_materials_Result
{
	public long InvoiceNo {get;set;}
	public long itemid {get;set;}
	public string Item_Name {get;set;}
	public double Q {get;set;}
	public double Qbak {get;set;}
	public double Qtotal {get;set;}
	public double QDiff {get;set;}
	public long Journal {get;set;}
	public long ClientID {get;set;}
	public string store_name {get;set;}
	public DateTime idate {get;set;}
	public string CustName {get;set;}
	public int StoreId {get;set;}
	public string StoreName {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Price {get;set;}
	public int Period {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal TotalAmount {get;set;}
	public long AccId {get;set;}
	public string AccName {get;set;}
	public long Cust_AccId {get;set;}
	public string Cust_AccName {get;set;}
	public int SubGruopID {get;set;}
	public DateTime DateFrom {get;set;}
	public DateTime DateTo {get;set;}
	public int diff {get;set;}
	public int Late {get;set;}
}