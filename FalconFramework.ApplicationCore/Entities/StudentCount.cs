using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class StudentCount
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public string MarhalaName {get;set;}
	public string SafName {get;set;}
	public int Fasl {get;set;}
	public int AllStu {get;set;}
	public int MMoslem {get;set;}
	public int Fmoslem {get;set;}
	public int MCrest {get;set;}
	public int FCrest {get;set;}
	public int Egyption {get;set;}
	public int anotherCountry {get;set;}
	public int NewStu {get;set;}
	public int TransStu {get;set;}
	public int MovdeStu {get;set;}
	public DateTime ActionStu {get;set;}
	public string UserName {get;set;}
}