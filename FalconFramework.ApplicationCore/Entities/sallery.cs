//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class sallery
    {
        [Key]
        public long Serial { get; set; }
        public string Emp_Name { get; set; }
        public int? month { get; set; }
        public int? year { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? BasicSallery { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ChangedSallery { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ElawaEgtmaya { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Bdlat { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Kader { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Menha { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? HSAAsasy { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? HSAMotghyer { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? HEAsasy { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? HEMotgher { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Safy { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Hafez { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Descount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Gezaa { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Edafat { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Net { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
