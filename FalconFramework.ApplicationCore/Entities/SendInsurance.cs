

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class SendInsurance
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int LID { get; set; }
        public DateTime? LaterDate { get; set; }
        public int? StudentCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Amount { get; set; }
        public string AmounAR { get; set; }
        public string Notes { get; set; }
        public DateTime? ActionDate { get; set; }
        public string UserName { get; set; }
        public string GMSign { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
    }
}
