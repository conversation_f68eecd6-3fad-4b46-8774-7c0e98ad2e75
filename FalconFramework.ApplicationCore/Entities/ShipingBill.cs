
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class ShipingBill
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long ShipingID { get; set; }
        public DateTime? Shiping_Bill_Date { get; set; }
        public DateTime? Expected_date_arrival { get; set; }
        public DateTime? Actual_arrival_date { get; set; }
        public DateTime? Shipping_date { get; set; }
        public long? ShippingPortID { get; set; }
        public string Place_Shipping { get; set; }
        public string DeliveryNO { get; set; }
        public DateTime? Delivery_Date { get; set; }
        public string Customs_Declaration { get; set; }
        public DateTime? CustomsDeclarationdate { get; set; }
        public string Numberportservices { get; set; }
        public DateTime? DateServives { get; set; }
        public string Guarantee_check { get; set; }
        public string Insurance { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Customs_Duties { get; set; }
        public DateTime? DateofPayment { get; set; }
        public string ContainerType { get; set; }
        public string ContainerKind { get; set; }
        public string Container_Size { get; set; }
        public string Other_Truck_note { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Exemption { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Storage_Fees { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Shipping_Cost { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Wages_discharge { get; set; }
        public bool? Bill { get; set; }
        public bool? Certificate_Origin { get; set; }
        public bool? Health_Certificate { get; set; }
        public bool? SpecificationList { get; set; }
        public bool? Other { get; set; }
        public string Othernote { get; set; }
        public string BillNumber { get; set; }
        public string CustomsBondNo { get; set; }
        public DateTime? BillDate { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Amount { get; set; }
        public string NoticeNumber { get; set; }
        public DateTime? NoticeDate { get; set; }
        public DateTime? ExpirationPaymentDate { get; set; }
        public string Exportingcompany { get; set; }
        public string General_Notes { get; set; }
        public long? Transport_OrderID { get; set; }
        public string ShiperName { get; set; }
        public long? ShiperID { get; set; }
        public string ConsigneeName { get; set; }
        public string ConsigneeAddress { get; set; }
        public string ConsigneeID_Iqama { get; set; }
        public string ConsigneeCity { get; set; }
        public string ConsigneePhone { get; set; }
        public long? ConsigneeID { get; set; }
        public bool? BillShiper { get; set; }
        public bool? Express { get; set; }
        public bool? BillConSiqner { get; set; }
        public bool? InsuranceService { get; set; }
        public string Note { get; set; }
        public string ReceiptNo { get; set; }
        public string Bill_of_Lading { get; set; }
        public bool? import { get; set; }
        public bool? export { get; set; }
        public string ReceverName2 { get; set; }
        public string ReceverAdd2 { get; set; }
        public string ReceverPhone2 { get; set; }
        public string VesselNO { get; set; }
    }
}
