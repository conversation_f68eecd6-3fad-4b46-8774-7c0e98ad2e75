using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

public class MandopOmola
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int ID {get;set;}
	public DateTime idate {get;set;}
	public string Tahseel {get;set;}
	public string servicetype {get;set;}
	public string customer_name {get;set;}
	public double TotalPrice {get;set;}
	public long store_id {get;set;}
	public DateTime tahseldate {get;set;}
	public long InvoiceNo {get;set;}
	public string Mandop_Name {get;set;}
	public double TotalCost {get;set;}
	public double ItemMaksab {get;set;}
	public string InvoiceType {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ValPonas {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Chargevalue {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal ExactCharge {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Omola {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Omola_Percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Net_Percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Net_Chartg {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Net_profit {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Indirect_Cost {get;set;}
	public int CollectCount {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal Indirect_Cost_Percent {get;set;}
    [Column(TypeName = "decimal(18, 2)")]
    public decimal NetCost {get;set;}
	public int MAndopID {get;set;}
}