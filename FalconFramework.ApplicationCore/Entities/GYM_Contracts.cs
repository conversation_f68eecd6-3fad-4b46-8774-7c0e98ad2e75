//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.Entities
{
     
     
    
    public partial class GYM_Contracts
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]

        public long Id { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? CustomerID { get; set; }
        public int? SalesTypeID { get; set; }
        public int? CurranceId { get; set; }
        public int? LogCount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Paied { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Monystill { get; set; }
        public int? Boxid { get; set; }
        public bool? Deleted { get; set; }
        public string UserDeleted { get; set; }
        public int? Spind { get; set; }
        public bool? Rplan { get; set; }
    }
}
