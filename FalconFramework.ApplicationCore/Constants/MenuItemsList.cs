﻿using FalconFramework.ApplicationCore.Entities;
using static FalconFramework.AppModules;

namespace FalconFramework.ApplicationCore.Constants
{
    public class MenuItemsList
    {
        public static List<MenuItem> Items
        {
            get
            {
                return new List<MenuItem>()
                { 
                    //inventory  / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 2,
                       TagId =(int)ModulesTags.Inventory,
                       Children = new List<MenuItem>
                        {
                            new MenuItem
                                   {
                                       NameAr = "المخازن",
                                       NameEn = "Warehouses",
                                       ModuleName = AppModules.Inventory.Warehouses,
                                       Route = "/inventory/configuration/warehouses",
                                   },
                            new MenuItem
                                   {
                                       NameAr = "انواع العمليات",
                                       NameEn = "Operations Types",
                                       ModuleName = AppModules.Inventory.Operations,
                                       Route = "/inventory/configuration/opteratinsTypes",
                                   },
                            new MenuItem
                                   {
                                       NameAr = "المجموعات الرئيسية",
                                       NameEn = "Product Categories",
                                        ModuleName = AppModules.Inventory.ProductCategories,
                                       Route = "/inventory/configuration/productsCategories",
                                   },
                            new MenuItem{ NameAr = "المجموعات الفرعية",
                                NameEn = "Product Sub Categories",
                                ModuleName = AppModules.Inventory.ProductSubCategories,Route="/inventory/configuration/product_sub_categories"},
                            new MenuItem
                                   {
                                       NameAr = "الخصائص",
                                       NameEn = "Attributes",
                                        ModuleName = AppModules.Inventory.ProductAttributes,
                                       Route = "/inventory/configuration/productAttributes",
                                   },
                            new MenuItem
                                   {
                                       NameAr = "قيم الخصائص",
                                       NameEn = "Attributes Value",
                                        ModuleName = AppModules.Inventory.ProductAttributes,
                                       Route = "/inventory/configuration/product_attributes_values",
                                   },
                            new MenuItem
                           {
                               NameAr = "المنتجات",
                               NameEn = "Products",
                               Route ="/inventory/configuration/products",
                               ModuleName = AppModules.Inventory.Products,
                            },

                            new MenuItem{ NameAr = "التصنيف التجارى",NameEn = "Trade Classification", ModuleName = AppModules.Inventory.TradeClassification,Route="/inventory/configuration/trade_classification"},

                            new MenuItem{
                                NameAr = "وحدات القياس",
                                NameEn = "Units",
                                ModuleName = AppModules.Inventory.Units,
                                Route="/inventory/configuration/units"},
                            new MenuItem{ NameAr = "الشركات المصنعة",NameEn = "Manufacturers", ModuleName = AppModules.Inventory.Manufacturers,Route="/inventory/configuration/manufacturers"},
                            new MenuItem{ NameAr = "ضيط الباركود",NameEn = "Barcode Generate", ModuleName = AppModules.Inventory.BarcodeGenerate,Route="/inventory/configuration/barcode_generate"},
                            new MenuItem{ NameAr = "البونص",NameEn = "Bonus", ModuleName = AppModules.Inventory.Bonus,Route="/inventory/configuration/bonus"},



                        }
                    },

                    //inventory   /Operations
                    new MenuItem
                    {
                       NameAr = "العمليات",
                       NameEn = "Operations",
                       Order = 3,
                       TagId =(int)ModulesTags.Inventory,
                       Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "الرصيد الافتتاحى",NameEn = "Opening", ModuleName = AppModules.Inventory.Openingbalance,Route="/inventory/operations/opening_balance"},
                            new MenuItem{ NameAr = "طلب استلام",NameEn = "Receipts Request", ModuleName = AppModules.Inventory.ReceiptsRequest,Route="/inventory/operations/receipts_request"},
                            new MenuItem{ NameAr = "طلب صرف",NameEn = "Delivery Request", ModuleName = AppModules.Inventory.DeliveryRequest,Route="/inventory/operations/delivery_request"},
                            new MenuItem{ NameAr = "اذن استلام",NameEn = "Receipts", ModuleName = AppModules.Inventory.Receipts,Route="/inventory/operations/receipts"},

                            new MenuItem{ NameAr = "اذن صرف",NameEn = "Delivery", ModuleName = AppModules.Inventory.Delivery,Route="/inventory/operations/delivery"},

                            new MenuItem{ NameAr = "التحويل بين المخازن",NameEn = "Transfers", ModuleName = AppModules.Inventory.Transfers,Route="/inventory/operations/transfers"},
                            new MenuItem{ NameAr = "تنفيذ الصرف",NameEn = "Delivery Execute", ModuleName = AppModules.Inventory.DeliveryExecute,Route="/inventory/operations/delivery_execute"},
                            new MenuItem{ NameAr = "تسليم الاصناف",NameEn = "Delivery Note", ModuleName = AppModules.Inventory.DeliveryNote,Route="/inventory/operations/delivery_note"},
                            new MenuItem{ NameAr = "اضافة عينات العملاء",NameEn = "Customers Samples", ModuleName = AppModules.Inventory.CustomersSamples,Route="/inventory/operations/customers_samples"},
                            new MenuItem{ NameAr = "عهد الموظفين",NameEn = "Employees Custody", ModuleName = AppModules.Inventory.EmployeesCustody,Route="/inventory/operations/employees_custody"},

                        }
                    } ,

                    //inventory Adjustments
                    new MenuItem
                    {
                       NameAr = "الجرد",
                       NameEn = "Inventory Adjustments",
                       Order = 4,
                       TagId =(int)ModulesTags.Inventory,
                       Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "محضر جرد",NameEn = "Inventory Adjustments", ModuleName = AppModules.Inventory.InventoryAdjustments,Route="/inventory/adjustments/inventory_adjustments"},
                            new MenuItem{ NameAr = "تسويه بالخصم",NameEn = "Deductions  Adjustments", ModuleName = AppModules.Inventory.DeductionsAdjustments,Route="/inventory/adjustments/deductions_adjustments"},
                            new MenuItem{ NameAr = "تسويه بالاضافة",NameEn = "Addition  Adjustments", ModuleName = AppModules.Inventory.AdditionAdjustments,Route="/inventory/adjustments/additionadjustments"},
                            new MenuItem{ NameAr = "صافى المستهلك",NameEn = "Net Consumer", ModuleName = AppModules.Inventory.NetConsumer,Route="/inventory/adjustments/netconsumer"},
                            new MenuItem{ NameAr = "تعيين متوسط التكلفة",NameEn = "Set Avrege Cost", ModuleName = AppModules.Inventory.SetAvregeCost,Route="/inventory/adjustments/setavregecost"},

                        }
                    } ,

                     //inventory Reports
                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 5,
                       TagId =(int)ModulesTags.Inventory,
                       Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "الاصناف",NameEn = "Products", ModuleName = AppModules.Inventory.ProductsReport,Route="/inventory/reports/products_report"},
                            new MenuItem{ NameAr = "البونص",NameEn = "Bonus", ModuleName = AppModules.Inventory.BonusReport,Route="/inventory/reports/bonus_report"},
                            new MenuItem{ NameAr = "كارت صنف بربحيه",NameEn = "Profitability", ModuleName = AppModules.Inventory.ProfitabilityReport,Route="/inventory/reports/profitability_report"},
                            new MenuItem{ NameAr = "تقييم المخزون",NameEn = "Valuation", ModuleName = AppModules.Inventory.ValuationReport,Route="/inventory/reports/valuation_report"},
                            new MenuItem{ NameAr = "تقييم المخزون اجمالي",NameEn = "Total Valuation", ModuleName = AppModules.Inventory.TotalValuationReport,Route="/inventory/reports/total_valuation_report"},
                            new MenuItem{ NameAr = "البحث عن سريال",NameEn = "Srial ", ModuleName = AppModules.Inventory.SrialReport,Route="/inventory/reports/srial_report"},
                            new MenuItem{ NameAr = "ارصدة الاصناف",NameEn = "Products Balance", ModuleName = AppModules.Inventory.ProductsBalanceReport,Route="/inventory/reports/products_balance_report"},
                            new MenuItem{ NameAr = "ارصدة الاصناف مجمع",NameEn = "Totals Products Balance", ModuleName = AppModules.Inventory.TotalsProductsBalanceReport,Route="/inventory/reports/totals_products_balance_report"},
                            new MenuItem{ NameAr = "المراجعة حسب الحركة",NameEn = "Movement By Operations", ModuleName = AppModules.Inventory.MovementByOperationsReport,Route="/inventory/reports/movement_by_operations_report"},
                            new MenuItem{ NameAr = "المراجعة حسب المخزن",NameEn = "Movement By Store", ModuleName = AppModules.Inventory.MovementByStoreReport,Route="/inventory/reports/movement_by_store_report"},
                            new MenuItem{ NameAr = "أصناف تجاوزت حد الطلب",NameEn = "Replenishment ", ModuleName = AppModules.Inventory.ReplenishmentReport,Route="/inventory/reports/replenishment_report"},
                            new MenuItem{ NameAr = "متابعة ركود الاصناف",NameEn = "Stagnant products", ModuleName = AppModules.Inventory.StagnantproductsReport,Route="/inventory/reports/stagnant_products_report"},
                            new MenuItem{ NameAr = "تاريخ الصلاحية",NameEn = "Expiration Date", ModuleName = AppModules.Inventory.ExpirationDateReport,Route="/inventory/reports/expiration_date_report"},
                            new MenuItem{ NameAr = "تكلفة مكونات صنف",NameEn = "Component Cost", ModuleName = AppModules.Inventory.ComponentCostReport,Route="/inventory/reports/component_cost_report"},
                            new MenuItem{ NameAr = "مراقبة اسعار الاصناف",NameEn = " Price Control", ModuleName = AppModules.Inventory. PriceControlReport,Route="/inventory/reports/price_control_report"},
                            new MenuItem{ NameAr = "مراقبة استهلاك الأصناف",NameEn = "Consumption", ModuleName = AppModules.Inventory.ConsumptionReport,Route="/inventory/reports/consumption_report"},
                            new MenuItem{ NameAr = "تقرير مراقبة العمليات",NameEn = "Operations Control", ModuleName = AppModules.Inventory.OperationsControlReport,Route="/inventory/reports/operations_control_report"},

                        }
                    } ,



                    //General
                    new MenuItem
                    {
                       NameAr = "الادارة",
                       NameEn = "Admin",
                       Order = 2,
                       TagId =(int)ModulesTags.Admin,
                       Children = new List<MenuItem>
                        {
                           new MenuItem
                           {
                               NameAr = "الدول",
                               NameEn = "Countries",
                               ModuleName = AppModules.Admin.Countries,
                               Route ="/general/countries"
                            },
                            new MenuItem
                           {
                               NameAr = "المدن",
                               NameEn = "Cityes",
                               ModuleName = AppModules.Admin.Cityes,
                               Route ="/general/Cityes"
                            },
                           new MenuItem
                           {
                               NameAr = "العملات",
                               NameEn = "Currencies",
                               ModuleName = AppModules.Admin.Currencies,
                               Route ="/general/currencies"
                            },
                           new MenuItem
                           {
                               NameAr = "الشركات",
                               NameEn = "Companies",
                               ModuleName = AppModules.Admin.Companies,
                               Route ="/general/company_list"
                            },
                           new MenuItem
                           {
                               NameAr = "الفروع",
                               NameEn = "Branches",
                               ModuleName = AppModules.Admin.Branches,
                               Route ="/general/branch_list"
                            }
                           ,
                           new MenuItem
                           {
                               NameAr = "اعدادات الشركه",
                               NameEn = "Company Info",
                               ModuleName = AppModules.Admin.Companies,
                               Route ="/general/company_info"
                            },
                           new MenuItem
                           {
                               NameAr = "الاعدادات العامه",
                               NameEn = "settings",
                               ModuleName = AppModules.Admin.Setting,
                               Route ="/general/settings"
                            },
                           new MenuItem
                           {
                               NameAr = "الجنسيات",
                               NameEn = "Nationality",
                               ModuleName = AppModules.Admin.Nationality,
                               Route ="/general/nationality"
                            },
                           new MenuItem
                           {
                               NameAr = "المستخدمين",
                               NameEn = "Users",
                               ModuleName = AppModules.Admin.Users,
                               Route ="/permissions/users"
                            }
                           ,
                           new MenuItem
                           {
                               NameAr = "الصلاحيات",
                               NameEn = "Permissions",
                               ModuleName = AppModules.Admin.Permissions,
                               Route ="/permissions"
                            }
                           ,
                           new MenuItem
                           {
                               NameAr = "الادوار",
                               NameEn = "Roles",
                               ModuleName = AppModules.Admin.Roles,
                               Route ="permissions/roles"
                            },
                           new MenuItem
                           {
                               NameAr = "الجلسات",
                               NameEn = "Sessions",
                               ModuleName = AppModules.Admin.Sessions,
                               Route ="general/sessions"
                            }
                        }
                    } ,


                    // HR / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                           new MenuItem{ NameAr = "ماكينات الحضور والانصراف",NameEn = "Attendance Machines", ModuleName = AppModules.HR.AttendanceMachines,Route="/hr/configuration/attendance_machines"},
                           new MenuItem{ NameAr = "التامينات",NameEn = "Insurances", ModuleName = AppModules.HR.Insurances,Route="/hr/configuration/insurances"},
                           new MenuItem{ NameAr = "الورديات",NameEn = "Shift", ModuleName = AppModules.HR.Shifs,Route="/hr/configuration/shift"},
                           new MenuItem{ NameAr = "الاجازات",NameEn = "Vacations", ModuleName = AppModules.HR.Vacations,Route="/hr/configuration/vacations"},
                           new MenuItem{ NameAr = "أنواع الإستحقاقات والإضافات",NameEn = "Additions Types", ModuleName = AppModules.HR.AdditionsTypes,Route="/hr/configuration/additions_types"},
                           new MenuItem{ NameAr = "أنواع الخصومات والاستقطاعات",NameEn = "Deductions Types", ModuleName = AppModules.HR.DeductionsTypes,Route="/hr/configuration/deductions_types"},
                           new MenuItem{ NameAr = "التوصيف الوظيفى",NameEn = "Job Descriptions", ModuleName = AppModules.HR.JobDescriptions,Route="/hr/configuration/job_descriptions"},
                           new MenuItem{ NameAr = "فئات العمل",NameEn = "Employees Categories", ModuleName = AppModules.HR.EmployeesCategories,Route="/hr/configuration/employees_categories"},
                           new MenuItem{ NameAr = "شركات الشحن",NameEn = "Shipping companies", ModuleName = AppModules.HR.ShippingCompanies,Route="/hr/configuration/shipping_companies"},
                           new MenuItem{ NameAr = "أنواع الوثائق",NameEn = "Documents Type", ModuleName = AppModules.HR.DocumentsType,Route="/hr/configuration/documents_type"},
                           new MenuItem{ NameAr = "الكفلاء",NameEn = "Sponsors", ModuleName = AppModules.HR.Sponsors,Route="/hr/configuration/sponsors"},
                           new MenuItem{ NameAr = "المؤهلات",NameEn = "Qualifications", ModuleName = AppModules.HR.Qualifications,Route="/hr/configuration/qualifications"},
                           new MenuItem{ NameAr = "موقع مراكز التكلفة",NameEn = "Analytic Accounts Location", ModuleName = AppModules.HR.Qualifications,Route="/hr/configuration/analytic_accounts_location"}
                       },

                    },
                    // HR / Employees
                    new MenuItem
                    {
                       NameAr = "الموظفين",
                       NameEn = "Employees",
                       Order = 2,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                           new MenuItem{ NameAr = "الموظفين",NameEn = "Employees", ModuleName = AppModules.HR.Employees,Route="/hr/employees"},
                           new MenuItem{ NameAr = "مفردات الراتب",NameEn = "Salary Information", ModuleName = AppModules.HR.SalaryInformation,Route="/hr/employees/salary_information"},
                           new MenuItem{ NameAr = "بيانات التابعين للموظف",NameEn = "Dependents", ModuleName = AppModules.HR.Dependents,Route="/hr/employees/dependents"},

                           new MenuItem{ NameAr = "تحديث بيانات الموظفين",NameEn = "Update Employee Data", ModuleName = AppModules.HR.UpdateEmployeeData,Route="/hr/employees/update_employee_data"},

                       },

                    },
                    // HR / Attendance Settings 
                    new MenuItem
                    {
                       NameAr = "اعدادات الحضور والانصراف",
                       NameEn = "Attendance Settings ",
                       Order = 3,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                           new MenuItem{ NameAr = "خطة الورديات",NameEn = "Shift plans", ModuleName = AppModules.HR.ShiftPlans,Route="/hr/attendance_settings/Shift_Plans"},

                           new MenuItem{ NameAr = "العطلات الرسمية",NameEn = "Official Holidays", ModuleName = AppModules.HR.OfficialHolidays,Route="/hr/attendance_settings/Official_Holidays"},

                           new MenuItem{ NameAr = "عدد ايام وساعات العمل",NameEn = "The number of days and hours of work", ModuleName = AppModules.HR.DaysAndHours,Route="/hr/attendance_settings/daysandhours"},

                           new MenuItem{ NameAr = "تعديل خطة الورديات",NameEn = "Edit Shift plans", ModuleName = AppModules.HR.EditShiftPlans,Route="/hr/attendance_settings/Edit_Shift_Plans"},

                           new MenuItem{ NameAr = "أوقات العمل ولائحة الجزاءات",NameEn = "Working hours and a list of penalties", ModuleName = AppModules.HR.WorkingHoursAndPenalties,Route="/hr/attendance_settings/working_hours_and_penalties"},

                    },

                    },
                    // HR / Attendance  
                    new MenuItem
                    {
                       NameAr = " الحضور والانصراف",
                       NameEn = "Attendance  ",
                       Order = 4,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                          new MenuItem{ NameAr = "مأمورية",NameEn = "Missions", ModuleName = AppModules.HR.Missions,Route="/hr/attendance/missions"},

                         new MenuItem{ NameAr = "اذونات الغيــــــاب",NameEn = "Absence Permissions ", ModuleName = AppModules.HR.AbsencePermissions,Route="/hr/attendance/absence_permissions"},

                         new MenuItem{ NameAr = "سحب الحركات",NameEn = "Download attendance", ModuleName = AppModules.HR.DownloadAttendance,Route="/hr/attendance/download_attendance"},
                         new MenuItem{ NameAr = "الوقت الاضافى",NameEn = "Over Time", ModuleName = AppModules.HR.OverTime,Route="/hr/attendance/over_time"},
                         new MenuItem{ NameAr = "الحركات اليدويه",NameEn = "Manual Attendance", ModuleName = AppModules.HR.ManualAttendance,Route="/hr/attendance/manual_attendance"},
                         new MenuItem{ NameAr = "أجــــازة",NameEn = "Vacation", ModuleName = AppModules.HR.Vacation,Route="/hr/attendance/vacation"},
                         new MenuItem{ NameAr = "الاذونات",NameEn = "Permissions", ModuleName = AppModules.HR.LatePermissions,Route="/hr/attendance/late_permissions"},

                       },

                    },
                  
                    // HR / Rewards
                    new MenuItem
                    {
                       NameAr = "الحوافز والمكافأت",
                       NameEn = "Rewards",
                       Order = 5,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                           new MenuItem{ NameAr = "طلب استحقاق او استقطاع",NameEn = "Benefit_Deduction Application", ModuleName = AppModules.HR.Benefit_DeductionApplication,Route="/hr/rewards/benefit_deduction_application"},

                           new MenuItem{ NameAr = "اضافات على الراتب",NameEn = "Salary Additions", ModuleName = AppModules.HR.SalaryAdditions,Route="/hr/rewards/salary_additions"},

                           new MenuItem{ NameAr = "الخصومات والاستقاطعات",NameEn = "Salary Deduction", ModuleName = AppModules.HR.SalaryDeduction,Route="/hr/rewards/salary_deduction"},

                       },

                    },
                   
                     // HR / Forms
                    new MenuItem
                    {
                       NameAr = "النماذج",
                       NameEn = "Forms",
                       Order = 7,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {

                            new MenuItem{ NameAr = "استلام المعقب معاملات",NameEn = " Receipt Transactions", ModuleName = AppModules.HR.ReceiptTransactions,Route="/hr/forms/receipt_transactions"},

                            new MenuItem{ NameAr = "عقد اعارة",NameEn = "Secondment Contract", ModuleName = AppModules.HR.SecondmentContract,Route="/hr/forms/secondment_contract"},

                            new MenuItem{ NameAr = "أمر تكليف",NameEn = "Assignment ", ModuleName = AppModules.HR.Assignment ,Route="/hr/forms/assignment"},

                            new MenuItem{ NameAr = "مسالة قانونية",NameEn = "Legal Issues", ModuleName = AppModules.HR.LegalIssues,Route="/hr/forms/legal_issues"},

                            new MenuItem{ NameAr = "طلب حجز تذاكر",NameEn = "Ticket Booking", ModuleName = AppModules.HR.TicketBooking,Route="/hr/forms/ticket_booking"},

                            new MenuItem{ NameAr = "قرار ادارى",NameEn = "Administrative Decision", ModuleName = AppModules.HR.AdministrativeDecision,Route="/hr/forms/administrative_decision"},

                            new MenuItem{ NameAr = "لفت نظر",NameEn = "Attention", ModuleName = AppModules.HR.Attention,Route="/hr/forms/attention"},

                            new MenuItem{ NameAr = "اشعار تعيين",NameEn = "Appointment Letter", ModuleName = AppModules.HR.AppointmentLetter,Route="/hr/forms/appointment_letter"},

                            new MenuItem{ NameAr = "تسليم عهدة",NameEn = "Custody Receipt", ModuleName = AppModules.HR.CustodyReceipt,Route="/hr/forms/custody_receipt"},

                            new MenuItem{ NameAr = "التامين الصحى",NameEn = "Health Insurance", ModuleName = AppModules.HR.HealthInsurance,Route="/hr/forms/health_insurance"},

                            new MenuItem{ NameAr = "مستحقات الموظفين",NameEn = "Entitlements", ModuleName = AppModules.HR.Entitlements,Route="/hr/forms/entitlements"},

                            new MenuItem{ NameAr = "شهادة خبرة",NameEn = "Certificate", ModuleName = AppModules.HR.Certificate,Route="/hr/forms/certificate"},

                            new MenuItem{ NameAr = "استقالة",NameEn = "Resignation", ModuleName = AppModules.HR.Resignation,Route="/hr/forms/resignation"},

                            new MenuItem{ NameAr = "انذار",NameEn = "Warning", ModuleName = AppModules.HR.Warning,Route="/hr/forms/warning"},

                            new MenuItem{ NameAr = "خطاب عرض على لجنة طبية",NameEn = "Medical Inspection Letter", ModuleName = AppModules.HR.MedicalInspectionLetter,Route="/hr/forms/medical_inspection_letter"},

                            new MenuItem{ NameAr = "نماذج الموارد البشرية",NameEn = "HR Forms", ModuleName = AppModules.HR.HRForms,Route="/hr/forms/hr_forms"},

                            new MenuItem{ NameAr = "عقد عمل",NameEn = "Contracts", ModuleName = AppModules.HR.Contracts,Route="/hr/forms/contracts"},

                       },

                    },
                     // HR / Employee Evaluation
                    new MenuItem
                    {
                       NameAr = "تقييم  الموظفين",
                       NameEn = "Employee Evaluation",
                       Order = 8,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                            new MenuItem{ NameAr = "تقييم الموظفين الجدد",NameEn = "New Employee Evaluation", ModuleName = AppModules.HR.NewEmployeeEvaluation,Route="/hr/employee_evaluation/new_employee_evaluation"},
                            new MenuItem{ NameAr = "التقييم الدورى للموظفين",NameEn = "Employee Evaluation", ModuleName = AppModules.HR.EmployeeEvaluation,Route="/hr/employee_evaluation/employee_evaluation"},
                            new MenuItem{ NameAr = "تقييم العاملين الشهري",NameEn = "Monthly Evaluation", ModuleName = AppModules.HR.MonthlyEvaluation,Route="/hr/employee_evaluation/monthly_evaluation"},

                       },

                    },
                     // HR / Employee Training
                    new MenuItem
                    {
                       NameAr = "التدريب",
                       NameEn = "Training",
                       Order = 9,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                             new MenuItem{ NameAr = "الاحتياجات التدريبية",NameEn = "Training Needs", ModuleName = AppModules.HR.TrainingNeeds,Route="/hr/employee_training/training_needs"},
                            new MenuItem{ NameAr = "خطة التدريب السنويه",NameEn = "Annual Training Plan", ModuleName = AppModules.HR.AnnualTrainingPlan,Route="/hr/employee_training/annual_training_plan"},
                            new MenuItem{ NameAr = "نموذج تدريب موظف",NameEn = "Employee Training Form", ModuleName = AppModules.HR.EmployeeTrainingForm,Route="/hr/employee_training/employee_training_form"},

                            new MenuItem{ NameAr = "تقييم فاعلية التدريب",NameEn = "Evaluate Training Effectiveness ", ModuleName = AppModules.HR.EvaluateTrainingEffectiveness ,Route="/hr/employee_training/evaluate_training_effectiveness"},

                            new MenuItem{ NameAr = "سجل جهات التدريب",NameEn = "Training Entities", ModuleName = AppModules.HR.TrainingEntities,Route="/hr/employee_training/training_entities"},

                       },

                    },
                     // HR / Security
                    new MenuItem
                    {
                       NameAr = "الامن",
                       NameEn = "Security",
                       Order = 10,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {

                            new MenuItem{ NameAr = "شحن البضائع",NameEn = "Trucking", ModuleName = AppModules.HR.Trucking,Route="/hr/security/trucking"},
                            new MenuItem{ NameAr = "حركة السيارات",NameEn = "Car Traffic", ModuleName = AppModules.HR.CarTraffic,Route="/hr/security/car_traffic"},
                            new MenuItem{ NameAr = "سجل الزائرين ",NameEn = "Visitors Record", ModuleName = AppModules.HR.VisitorsRecord ,Route="/hr/security/visitors_record"},


                       },

                    },

                      // HR / Employee preparation and Printing  salaries
                    new MenuItem
                    {
                       NameAr = "اعداد وطباعة الرواتب",
                       NameEn = "Salaries",
                       Order = 11,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                            new MenuItem{ NameAr = "اعداد الرواتب",NameEn = "Salaries Preparation", ModuleName = AppModules.HR.SalariesPreparation,Route="/hr/salaries/salaries_preparation"},
                            new MenuItem{ NameAr = "طباعة الرواتب",NameEn = "Print Salaries", ModuleName = AppModules.HR.PrintSalaries,Route="/hr/salaries/print_salaries"},
                            new MenuItem{ NameAr = "ضريبة الدخل",NameEn = "Income TAX", ModuleName = AppModules.HR.IncomeTAX,Route="/hr/salaries/income_tax"},
                            new MenuItem{ NameAr = "تكلفة الإقامة الشهرية",NameEn = "Monthly Iqama Cost", ModuleName = AppModules.HR.MonthlyIqamaCost,Route="/hr/salaries/monthly_iqama_cost"},

                       },

                    },

                    // HR /
                     new MenuItem
                    {
                       NameAr = "شئون الموظفين",
                       NameEn = "HR",
                       Order = 12,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                            new MenuItem{ NameAr = "التاشيرات",NameEn = "Visa", ModuleName = AppModules.HR.Visa,Route="/hr/human_resources/visa"},

                            new MenuItem{ NameAr = "نقل الموظفين بين المشاريع",NameEn = "Staff Transfer Between Projects", ModuleName = AppModules.HR.StaffTransferBetweenProjects,Route="/hr/human_resources/staff_transfer_between_projects"},

                            new MenuItem{ NameAr = "تعديل راتب موظف",NameEn = "Salary Update", ModuleName = AppModules.HR.SalaryUpdate,Route="/hr/human_resources/salary_update"},
                            new MenuItem{ NameAr = "تحديث تاريخ الاقامه",NameEn = "Iqama Update", ModuleName = AppModules.HR.IqamaUpdate,Route="/hr/human_resources/iqama_update"},
                            new MenuItem{ NameAr = "طلبات الموظفين",NameEn = "Employee Requests", ModuleName = AppModules.HR.EmployeeRequests,Route="/hr/human_resources/employee_requests"},
                            new MenuItem{ NameAr = "دفعات بدل السكن",NameEn = "Housing Allowance Payments", ModuleName = AppModules.HR.HousingAllowancePayments,Route="/hr/human_resources/housing_allowance_payments"},
                            new MenuItem{ NameAr = "تصفيه",NameEn = "Ending", ModuleName = AppModules.HR.Ending,Route="/hr/human_resources/ending"},
                            new MenuItem{ NameAr = "المستحقات السنوية",NameEn = "Annual Dues", ModuleName = AppModules.HR.AnnualDues,Route="/hr/human_resources/Annual_dues"},
                            new MenuItem{ NameAr = "مذكرات يدويه",NameEn = "Handwritten Note", ModuleName = AppModules.HR.HandwrittenNote,Route="/hr/human_resources/handwritten_note"},
                            new MenuItem{ NameAr = "تكاليف الموظفين",NameEn = "Staff Costs", ModuleName = AppModules.HR.StaffCosts,Route="/hr/human_resources/staff_costs"},
                            new MenuItem{ NameAr = "جدولة السلف",NameEn = "Schedule Advances", ModuleName = AppModules.HR.ScheduleAdvances,Route="/hr/human_resources/schedule_advances"},


                       },

                    },
                    // HR /Reports
                     new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 13,
                       TagId =(int)ModulesTags.HR,
                       Children = new List<MenuItem>
                       {
                            new MenuItem{ NameAr = "الخصومات",NameEn = "Deductions", ModuleName = AppModules.HR.DeductionsReports,Route="/hr/reports/deductions_reports"},
                            new MenuItem{ NameAr = "المكافآت والحوافز",NameEn = "Bonuses And Incentives", ModuleName = AppModules.HR.BonusesandincentivesReports,Route="/hr/reports/bonuses_and_incentives_reports"},
                            new MenuItem{ NameAr = "السلف",NameEn = "Loans", ModuleName = AppModules.HR.LoansReports,Route="/hr/reports/loans_reports"},
                            new MenuItem{ NameAr = "الاجازات",NameEn = "Vacations", ModuleName = AppModules.HR.VacationsReports,Route="/hr/reports/vacations_reports"},
                            new MenuItem{ NameAr = "الوقت الاضافى",NameEn = "Over Time", ModuleName = AppModules.HR.OverTimeReports,Route="/hr/reports/over_time_reports"},
                            new MenuItem{ NameAr = "الاذونات",NameEn = "Permissions", ModuleName = AppModules.HR.PermissionsReports,Route="/hr/reports/permissions_reports"},
                            new MenuItem{ NameAr = "الحضور والانصراف",NameEn = "Attendance", ModuleName = AppModules.HR.AttendanceReports,Route="/hr/reports/attendance_reports"},
                            new MenuItem{ NameAr = "الماموريات",NameEn = "Missions", ModuleName = AppModules.HR.MissionsReports,Route="/hr/reports/missions_reports"},
                            new MenuItem{ NameAr = "مستحقات التامينات",NameEn = "Insurances due", ModuleName = AppModules.HR.InsurancesdueReports,Route="/hr/reports/insurances_due_reports"},
                            new MenuItem{ NameAr = "موظفى ومديرى الاقسام",NameEn = "Managers", ModuleName = AppModules.HR.ManagersReports,Route="/hr/reports/managers_reports"},
                            new MenuItem{ NameAr = "حياة موظف",NameEn = "Employee life", ModuleName = AppModules.HR.EmployeelifeReports,Route="/hr/reports/employee_life_reports"},
                            new MenuItem{ NameAr = "الوثائق",NameEn = "Documents", ModuleName = AppModules.HR.DocumentsReports,Route="/hr/reports/documents_reports"},
                            new MenuItem{ NameAr = "تقارير السفريات",NameEn = "Travels ", ModuleName = AppModules.HR.TravelsReports,Route="/hr/reports/travels_reports"},
                            new MenuItem{ NameAr = "رخص القياده",NameEn = "Driving licences", ModuleName = AppModules.HR.DrivinglicencesReports,Route="/hr/reports/driving_licences_reports"},
                            new MenuItem{ NameAr = "اعداد الوظائف لكل جنسية",NameEn = "Nationality Jobs", ModuleName = AppModules.HR.NationalityJobsReports,Route="/hr/reports/nationality_jobs_reports"},
                            new MenuItem{ NameAr = "التنقلات خلال فترة",NameEn = "Movements During Period", ModuleName = AppModules.HR.MovementsDuringPeriodReports,Route="/hr/reports/movements_during_period_reports"},
                            new MenuItem{ NameAr = "المعينين خلال فترة",NameEn = "Recruited during Period", ModuleName = AppModules.HR.RecruitedduringPeriodReports,Route="/hr/reports/recruited_during_period_reports"},
                            new MenuItem{ NameAr = "رواتب مستحقة في شهر معين",NameEn = "Due Salaries", ModuleName = AppModules.HR.DueSalariesReports,Route="/hr/reports/due_salaries_reports"},
                            new MenuItem{ NameAr = "التصفيات",NameEn = "Playoffs", ModuleName = AppModules.HR.PlayoffsReports,Route="/hr/reports/playoffs_reports"},
                            new MenuItem{ NameAr = "الإستقطاعات الشهرية",NameEn = "Monthly Deductions", ModuleName = AppModules.HR.MonthlyDeductionsReports,Route="/hr/reports/monthly_deductions_reports"},

                       },

                    },


                    //Purchase  / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Purchase,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "أنواع المشتريات",NameEn = "Purchases Types", ModuleName = AppModules.Purchase.PurchasesTypes,Route="/purchases/configuration/purchases_types"},
                            new MenuItem{ NameAr = "تصنيف الموردين",NameEn = "Suppliers Classification", ModuleName = AppModules.Purchase.SuppliersClassification,Route="/purchases/configuration/suppliers_classification"},
                            new MenuItem{ NameAr = "شروط عروض الأسعار وأوامر الشراء",NameEn = "Purchases Terms", ModuleName = AppModules.Purchase.PurchasesTerms,Route="/purchases/configuration/purchases_terms"},
                            new MenuItem{ NameAr = "الموردين",NameEn = "Suppliers", ModuleName = AppModules.Purchase.Suppliers,Route="/purchases/configuration/suppliers"},
                            new MenuItem{ NameAr = "تقييم الموردين",NameEn = "Supplier Evaluation", ModuleName = AppModules.Purchase.SupplierEvaluation,Route="/purchases/configuration/supplier_evaluation"},


                        }
                    },

                      //Purchase  / orders
                    new MenuItem
                    {
                       NameAr = "طلبات الشراء",
                       NameEn = "orders",
                       Order = 2,
                       TagId =(int)ModulesTags.Purchase,
                        Children = new List<MenuItem>
                        {

                            new MenuItem{ NameAr = "طلب الشراء",NameEn = "Purchase Requisition", ModuleName = AppModules.Purchase.PurchaseRequisition,Route="/purchases/orders/purchase_requisition"},
                            new MenuItem{ NameAr = "أوامر الشراء",NameEn = "Purchase Orders", ModuleName = AppModules.Purchase.PurchaseOrders,Route="/purchases/orders/purchase_orders"},
                            new MenuItem{ NameAr = "متابعة الطلبات",NameEn = "Follow Up Requisition", ModuleName = AppModules.Purchase.FollowUpRequisition,Route="/purchases/orders/follow_up_requisition"},
                            new MenuItem{ NameAr = "طلب عرض سعر",NameEn = "RFQ", ModuleName = AppModules.Purchase.RFQ,Route="/purchases/orders/rfq"},
                            new MenuItem{ NameAr = "لجنة فحص المظاريف",NameEn = "RFQ Comparisons", ModuleName = AppModules.Purchase.RFQComparisons,Route="/purchases/orders/rfq_comparisons"},



                        }
                    },


                      //Purchase  / Vendor Bills
                    new MenuItem
                    {
                       NameAr = "فواتير المشتريات",
                       NameEn = "Vendor Bills",
                       Order = 3,
                       TagId =(int)ModulesTags.Purchase,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "فاتورة مشتريات",NameEn = "Bills", ModuleName = AppModules.Purchase.Bills,Route="/purchases/vendor_bills/bills"},
                            new MenuItem{ NameAr = "فاتورة خدمة ",NameEn = "Service Bills", ModuleName = AppModules.Purchase.ServiceBills,Route="/purchases/vendor_bills/service_bills"},
                            new MenuItem{ NameAr = "مردودات مشتريات",NameEn = "Bills Returns", ModuleName = AppModules.Purchase.BillsReturns,Route="/purchases/vendor_bills/bills_returns"},
                            new MenuItem{ NameAr = " مردودات خدمة ",NameEn = "Returns Service Bills", ModuleName = AppModules.Purchase.ReturnsServiceBills,Route="/purchases/vendor_bills/returns_service_bills"},

                        }
                    },


                      //Purchase  / Reports
                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 4,
                       TagId =(int)ModulesTags.Purchase,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "يومية المشتريات",NameEn = "Purchases", ModuleName = AppModules.Purchase.PurchasesReport,Route="/purchases/reports/purchases_report"},
                            new MenuItem{ NameAr = "مردودات المشتريات",NameEn = "Purchases Returns", ModuleName = AppModules.Purchase.PurchasesReturnsReport,Route="/purchases/reports/purchases_returns_report"},
                            new MenuItem{ NameAr = "متابعة الضرائب",NameEn = "Purchases Tax", ModuleName = AppModules.Purchase.PurchasesTaxReport,Route="/purchases/reports/purchases_tax_report"},
                            new MenuItem{ NameAr = "حساب مورد بالشهور",NameEn = "Suppliers Account In Mothes", ModuleName = AppModules.Purchase.SuppliersAccountInMothesReport,Route="/purchases/reports/suppliers_account_in_mothes_report"},
                            new MenuItem{ NameAr = "مشتريات الموردين خلال فترة",NameEn = "Purchases Periodically", ModuleName = AppModules.Purchase.PurchasesPeriodicallyReport,Route="/purchases/reports/purchases_periodically_report"},
                            new MenuItem{ NameAr = "المشتريات خلال فترة اصناف",NameEn = "purchased Products", ModuleName = AppModules.Purchase.purchasedProductsReport,Route="/purchases/reports/purchased_products_report"},
                            new MenuItem{ NameAr = "موقف الموردين اصناف",NameEn = "Supplierssuppliers products Report", ModuleName = AppModules.Purchase.Thepositionofsuppliers,Route="/purchases/reports/suppliers_products_report"},
                            new MenuItem{ NameAr = "تقرير طلبات الشراء",NameEn = "Purchase Requisitions", ModuleName = AppModules.Purchase.PurchaseRequisitionsReport,Route="/purchases/reports/purchase_requisitions_report"},
                            new MenuItem{ NameAr = "تقرير أوامر الشراء",NameEn = "Purchas Order", ModuleName = AppModules.Purchase.PurchasOrderReport,Route="/purchases/reports/purchas_order_report"},
                            new MenuItem{ NameAr = "دفعات اوامر الشراء",NameEn = "Purchase orders payments", ModuleName = AppModules.Purchase.PurchaseorderspaymentsReport,Route="/purchases/reports/purchase_orders_payments_report"},
                            new MenuItem{ NameAr = "مصروفات سند التكلفة",NameEn = "Landed Cost", ModuleName = AppModules.Purchase.LandedCostReport,Route="/purchases/reports/landed_cost_report"},
                            new MenuItem{ NameAr = "متابعة فواتير المشتريات",NameEn = "Follow Up bills", ModuleName = AppModules.Purchase.FollowUpbillsReport,Route="/purchases/reports/follow_up_bills_report"},

                        }
                    },




                       //Sales / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "فئات المندوبين",NameEn = "SalespersonCategories", ModuleName = AppModules.Sales.SalespersonCategories,Route="/sales/configuration/salesperson_categories"},

                            new MenuItem{ NameAr = "أسماء المندوبين",NameEn = "Salesperson", ModuleName = AppModules.Sales.Salesperson,Route="/sales/configuration/salesperson"},

                            new MenuItem{ NameAr = "انواع المبيعات",NameEn = "Sales Types", ModuleName = AppModules.Sales.SalesTypes,Route="/sales/configuration/sales_types"},

                            new MenuItem{ NameAr = "ايام التحصيل وشروط العمولات",NameEn = "Salesperson Commission", ModuleName = AppModules.Sales.SalespersonCommission,Route="/sales/configuration/salesperson_commission"},

                            new MenuItem{ NameAr = "تصنيف العملاء",NameEn = "Customers Category", ModuleName = AppModules.Sales.CustomersCategory,Route="/sales/configuration/customers_category"},

                            new MenuItem{ NameAr = "المبيعات وشروط العمولة",NameEn = "Sales And Commission Terms", ModuleName = AppModules.Sales.SalesAndCommissionTerms,Route="/sales/configuration/sales_and_commission_terms"},

                            new MenuItem{ NameAr = "الضامن",NameEn = "Guarantor", ModuleName = AppModules.Sales.Guarantor,Route="/sales/configuration/guarantor"},
                            new MenuItem{ NameAr = "اسماء العملاء الرئيسية",NameEn = "Main Customer", ModuleName = AppModules.Sales.MainCustomer,Route="/sales/configuration/main_customer"},

                            new MenuItem{ NameAr = "بيانات العملاء",NameEn = "Customers", ModuleName = AppModules.Sales.Customers,Route="/sales/configuration/customers"},

                            new MenuItem{ NameAr = "قوائم الاسعار",NameEn = "Pricelists", ModuleName = AppModules.Sales.Pricelists,Route="/sales/configuration/pricelists"},

                             new MenuItem{ NameAr = "فريق المبيعات",NameEn = "Sales Team", ModuleName = AppModules.Sales.SalesTeams,Route="/sales/configuration/sales_teams"},

                                new MenuItem{ NameAr = "شروط الدفع ",NameEn = "Payment Terms", ModuleName = AppModules.Sales.SalesPaymentTerms,Route="/sales/configuration/sales_payment_terms"},


                        }
                    },


                        //Sales /  Cars Showrooms
                    new MenuItem
                    {
                       NameAr = "معارض السيارات",
                       NameEn = "Cars Showrooms",
                       Order = 3,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "السيارات",NameEn = "Cars", ModuleName = AppModules.Sales.Cars,Route="/sales/cars_showrooms/cars"},
                            new MenuItem{ NameAr = "الأشخــاص",NameEn = "Persons", ModuleName = AppModules.Sales.Persons,Route="/sales/cars_showrooms/persons"},
                            new MenuItem{ NameAr = "محاضر رفع السيارات",NameEn = "Car Removal", ModuleName = AppModules.Sales.CarRemoval,Route="/sales/cars_showrooms/car_removal"},
                            new MenuItem{ NameAr = "سداد الأقساط",NameEn = "Installments Payment", ModuleName = AppModules.Sales.InstallmentsPayment,Route="/sales/cars_showrooms/installments_payment"},
                            new MenuItem{ NameAr = "حساب الاشخاص",NameEn = "Persons Accounts", ModuleName = AppModules.Sales.PersonsAccounts,Route="/sales/cars_showrooms/persons_accounts"},
                            new MenuItem{ NameAr = "مبيعات  معارض السيارات",NameEn = "Car  Sales", ModuleName = AppModules.Sales.CarSales,Route="/sales/cars_showrooms/car_sales"},
                            new MenuItem{ NameAr = "تقرير مبيعات  معارض السيارات",NameEn = "Car Sales Report", ModuleName = AppModules.Sales.CarSalesReport,Route="/sales/cars_showrooms/car_sales_report"},

                        }
                    },


                        //Sales /  Transportation
                    new MenuItem
                    {
                       NameAr = "نظام النقليات",
                       NameEn = "Transportation",
                       Order = 4,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "بوليصة نقل بري",NameEn = "Bill Of Lading", ModuleName = AppModules.Sales.BillOfLading,Route="/sales/transportation/bill_of_lading"},
                            new MenuItem{ NameAr = "بوليصة شحن بحري",NameEn = "Bill Of Sea Lading", ModuleName = AppModules.Sales.BillOfSeaLading,Route="/sales/transportation/bill_of_sea_lading"},
                            new MenuItem{ NameAr = "أمر نقل",NameEn = "Transfer Order", ModuleName = AppModules.Sales.TransferOrder,Route="/sales/transportation/transfer_order"},
                            new MenuItem{ NameAr = "بيانات السيارات",NameEn = "Cars Data", ModuleName = AppModules.Sales.CarsData,Route="/sales/transportation/cars_data"},
                            new MenuItem{ NameAr = "ربط السائقين بالسيارات",NameEn = "Cars Drivers", ModuleName = AppModules.Sales.CarsDrivers,Route="/sales/transportation/cars_drivers"},
                            new MenuItem{ NameAr = "المواني",NameEn = "ports", ModuleName = AppModules.Sales.ports,Route="/sales/transportation/ports"},
                            new MenuItem{ NameAr = "شركات النقل",NameEn = "Transportation Companies", ModuleName = AppModules.Sales.TransportationCompanies,Route="/sales/transportation/transportation_companies"},
                            new MenuItem{ NameAr = "شحن",NameEn = "Transportation", ModuleName = AppModules.Sales.Transportation,Route="/sales/transportation/transportation"},
                            new MenuItem{ NameAr = "ايرادات السيارات",NameEn = "Car Revenue", ModuleName = AppModules.Sales.CarRevenue,Route="/sales/transportation/car_revenue"},


                        }
                    },



                        //Sales /  Sales Orders
                    new MenuItem
                    {
                       NameAr = "المبيعات",
                       NameEn = "Sales",
                       Order = 5,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {

                            new MenuItem{ NameAr = "الضمانات",NameEn = "Warranties", ModuleName = AppModules.Sales.Warranties,Route="/sales/sales/warranties"},
                            new MenuItem{ NameAr = "عرض سعر ",NameEn = "Quotations", ModuleName = AppModules.Sales.Quotations,Route="/sales/sales/products_quotations"},

                            new MenuItem{ NameAr = "عروض خدمات",NameEn = "Services Quotations", ModuleName = AppModules.Sales.ServicesQuotations,Route="/sales/sales/services_quotations"},


                        }
                    },


                        //Sales /  Sales Invoice
                    new MenuItem
                    {
                       NameAr = "فواتير المبيعات",
                       NameEn = "Sales Invoice",
                       Order = 6,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {

                            new MenuItem{ NameAr = "فاتورة مبيعات ",NameEn = "Sales Tax Invoice", ModuleName = AppModules.Sales.SalesTaxInvoice,Route="/sales/invoice/taxInvoice-list"},
                            new MenuItem{ NameAr = "مردوات مبيعات  ",NameEn = "Sales Tax Returns", ModuleName = AppModules.Sales.SalesTaxReturns,Route="/sales/invoice/sales_tax_returns"},
                            new MenuItem{ NameAr = "فاتورة مبيعات خدمة ",NameEn = "Service Sales Tax Invoice", ModuleName = AppModules.Sales.ServiceSalesTaxInvoice,Route="/sales/invoice/service_sales_tax_invoice"},
                            new MenuItem{ NameAr = "مردودات مبيعات خدمة",NameEn = "Service Sales Tax Invoice Returns", ModuleName = AppModules.Sales.ServiceSalesTaxInvoiceReturns,Route="/sales/invoice/service_sales_tax_invoice_returns"},
                            new MenuItem{ NameAr = "بيان مبيعات ",NameEn = "Sales  Invoice", ModuleName = AppModules.Sales.SalesInvoice,Route="/sales/invoice/sales_invoice"},
                            new MenuItem{ NameAr = "مردوات بيان مبيعات  ",NameEn = "Sales  Returns", ModuleName = AppModules.Sales.SalesReturns,Route="/sales/invoice/sales_returns"},
                            new MenuItem{ NameAr = "بيان مبيعات خدمة ",NameEn = "Service Sales  Invoice", ModuleName = AppModules.Sales.ServiceSalesInvoice,Route="/sales/invoice/service_sales_invoice"},
                            new MenuItem{ NameAr = "مردودات بيان مبيعات خدمة",NameEn = "Service Sales  Invoice Returns", ModuleName = AppModules.Sales.ServiceSalesInvoiceReturns,Route="/sales/invoice/service_sales_invoice_returns"},

                                }
                            },


                     //Sales /  Sales Reports
                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 7,
                       TagId =(int)ModulesTags.Sales,
                        Children = new List<MenuItem>
                        {
                                    new MenuItem{ NameAr = "يومية المبيعات",NameEn = "Sales", ModuleName = AppModules.Sales.SalesReport,Route="/sales/reports/sales_report"},
                                    new MenuItem{ NameAr = "مرتد المبيعات",NameEn = "Sales Return", ModuleName = AppModules.Sales.SalesReturnReport,Route="/sales/reports/sales_return_report"},
                                    new MenuItem{ NameAr = "متابعة الفواتير",NameEn = "Invoices", ModuleName = AppModules.Sales.InvoicesReport,Route="/sales/reports/invoices_report"},
                                    new MenuItem{ NameAr = "المبيعات خلال فترة اصناف",NameEn = "Product Sale", ModuleName = AppModules.Sales.ProductSaleReport,Route="/sales/reports/product_sale_report"},
                                    new MenuItem{ NameAr = "موقف العملاء اصناف",NameEn = "Customer Products", ModuleName = AppModules.Sales.CustomerProducts,Route="/sales/reports/customer_products_report"},
                                    new MenuItem{ NameAr = "تحليل المبيعات سنوى اصناف",NameEn = "Annual  Sales Analysis", ModuleName = AppModules.Sales.AnnualSalesAnalysis,Route="/sales/reports/annual_sales_report"},
                                    new MenuItem{ NameAr = "اسماء العملاء لكل مندوب",NameEn = "Salesperson Customer", ModuleName = AppModules.Sales.SalespersonCustomer,Route="/sales/reports/salesperson_customer_report"},
                                    new MenuItem{ NameAr = "ارباح الفواتير",NameEn = "Invoice Earnings", ModuleName = AppModules.Sales.InvoiceEarningsReport,Route="/sales/reports/invoice_earnings_report"},
                                    new MenuItem{ NameAr = "تحصيلات وعمولات المندوبين نسبه",NameEn = "Salesperson Commissions Percentage", ModuleName = AppModules.Sales.SalespersonCommissionsPercentage,Route="/sales/reports/salesperson_commissions_percentage"},
                                    new MenuItem{ NameAr = "تحصيلات وعمولات المندوبين قيمه",NameEn = "Salesperson Commissions Value", ModuleName = AppModules.Sales.SalespersonCommissionsValue,Route="/sales/reports/salesperson_commissions_value"},
                                    new MenuItem{ NameAr = "اجمالى مبيعات العملاء خلال فترة",NameEn = "Total  Sales", ModuleName = AppModules.Sales.TotalSalesReport,Route="/sales/reports/total_sales_report"},
                                    new MenuItem{ NameAr = "متابعة اوامر التوريد",NameEn = "Delivery Order", ModuleName = AppModules.Sales.DeliveryOrderReport,Route="/sales/reports/delivery_order_report"},
                                    new MenuItem{ NameAr = "التاجير",NameEn = "Rent", ModuleName = AppModules.Sales.RentReport,Route="/sales/reports/rent_report"},
                                    new MenuItem{ NameAr = "الطلبيات",NameEn = "Product Orders", ModuleName = AppModules.Sales.ProductOrdersReport,Route="/sales/reports/product_orders_report"},
                                    new MenuItem{ NameAr = "صافى المبيعات",NameEn = "Net Sales", ModuleName = AppModules.Sales.NetSales,Route="/sales/reports/net_sales"},
                                    new MenuItem{ NameAr = "متابعة العقود",NameEn = "Contracts", ModuleName = AppModules.Sales.ContractsReport,Route="/sales/reports/contracts_report"},
                                    new MenuItem{ NameAr = "متابعة الضرائب",NameEn = "Sales Tax", ModuleName = AppModules.Sales.SalesTax,Route="/sales/reports/sales_tax"},
                                    new MenuItem{ NameAr = "تسليم المنتجات",NameEn = "Delivery", ModuleName = AppModules.Sales.DeliveryReport,Route="/sales/reports/delivery_report"},

                        }
                    },



                     new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.CRM,
                       Children = new List<MenuItem>
                       {

                            new MenuItem{ NameAr = "فريق المبيعات",NameEn = "Sales Team", ModuleName = AppModules.CRM.SalesTeams,Route="/sales/configuration/sales_teams"},

                            new MenuItem{ NameAr = "الانشطة التجارية",NameEn = "Commercial activities", ModuleName = AppModules.CRM.CommercialActivities,Route="/crm/configuration/commercial_activities"},

                            new MenuItem{ NameAr = "فئات العملاء",NameEn = "customer categories", ModuleName = AppModules.CRM.CustomerCategories,Route="/crm/configuration/customer_categories"},

                            new MenuItem{ NameAr = "القطاعات",NameEn = "sectors", ModuleName = AppModules.CRM.Sectors,Route="/crm/configuration/sectors"},

                            new MenuItem{ NameAr = "المنصات",NameEn = "Stages", ModuleName = AppModules.CRM.Stages,Route="/crm/configuration/stages"},







                       },

                    },

                    //CRM
                    new MenuItem
                    {
                       NameAr = "علاقات العملاء",
                       NameEn = "CRM",
                       Order = 2,
                       TagId =(int)ModulesTags.CRM,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "استطلاع راى العملاء",NameEn = "Customer Survey", ModuleName = AppModules.CRM.CustomerSurvey,Route="/crm/crm/customer_survey"},
                            new MenuItem{ NameAr = "طلبات العملاء",NameEn = "Customer Requests", ModuleName = AppModules.CRM.CustomerRequests,Route="/crm/crm/customer_requests"},
                            new MenuItem{ NameAr = "تسجيل الشكاوى",NameEn = "Complaints", ModuleName = AppModules.CRM.Complaints,Route="/crm/crm/complaints"},
                            new MenuItem{ NameAr = "دليل الشركات",NameEn = "Leads", ModuleName = AppModules.CRM.Leads,Route="/crm/crm/Leads"},
                            new MenuItem{ NameAr = "متابعة  العملاء",NameEn = "CRM", ModuleName = AppModules.CRM.SaleCRM,Route="/crm/crm/crm"},


                        }
                    },


                     //CRM Whatsapp
                    new MenuItem
                    {
                       NameAr = "واتس",
                       NameEn = "WhatsApp",
                       Order = 2,
                       TagId =(int)ModulesTags.CRM,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "حسابات الواتس",NameEn = "WhatsApp Instance", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/instance"},
                            new MenuItem{ NameAr = "Webhooks",NameEn = "Webhooks", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/webhooks"},

                            new MenuItem{ NameAr = "القوالب",NameEn = "Create Template", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/templates/create"},

                            new MenuItem{ NameAr = "ارسال",NameEn = "Initiate Conversation", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/templates/send"},

                            new MenuItem{ NameAr = "المحادثات",NameEn = "Conversation", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/messages"},

                            new MenuItem{ NameAr = "settings",NameEn = "settings", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/settings"},

                            new MenuItem{ NameAr = "contacts",NameEn = "contacts", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/contacts"},

                             new MenuItem{ NameAr = "autoreplies",NameEn = "autoreplies", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/autoreplies"},

                              new MenuItem{ NameAr = "campaigns",NameEn = "campaigns", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/campaigns"},

                               new MenuItem{ NameAr = "qrcodes",NameEn = "qrcodes", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/qrcodes"},

                               new MenuItem{ NameAr = "analytics",NameEn = "analytics", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/qrcodes"},

                               new MenuItem{ NameAr = "salla",NameEn = "salla", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/salla"},

                               new MenuItem{ NameAr = "zid",NameEn = "zid", ModuleName = AppModules.CRM.WhatsappInstance,Route="/crm/whatsapp/zid"},



                        }
                    },


                      //CRM Whatsapp
                    new MenuItem
                    {
                       NameAr = "البريد الالكترونى",
                       NameEn = "Email",
                       Order = 3,
                       TagId =(int)ModulesTags.CRM,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "اعدادات البريد",NameEn = "Email Instance", ModuleName = AppModules.CRM.EmailInstance,Route="/crm/email/instance"},

                             new MenuItem{ NameAr = "القوالب",NameEn = "Email Template", ModuleName = AppModules.CRM.EmailTemplate,Route="/crm/email/template"},

                             new MenuItem{ NameAr = "ارسال",NameEn = "Send Email", ModuleName = AppModules.CRM.SendEmail,Route="/crm/email/send"},

                        }
                    },



                      //CRM Whatsapp
                    new MenuItem
                    {
                       NameAr = "الرسائل النصيه",
                       NameEn = "SMS",
                       Order = 4,
                       TagId =(int)ModulesTags.CRM,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "حسابات الرسائل",NameEn = "SMS Instance", ModuleName = AppModules.CRM.SMSInstance,Route="/crm/sms/instance"},

                             new MenuItem{ NameAr = "القوالب",NameEn = "SMS Template", ModuleName = AppModules.CRM.SMSTemplate,Route="/crm/sms/template"},

                             new MenuItem{ NameAr = "ارسال",NameEn = "Send SMS", ModuleName = AppModules.CRM.SendSMS,Route="/crm/sms/send"},


                        }
                    },


                      //CRM Call Center
                    new MenuItem
                    {
                       NameAr = "نظام الاتصالات",
                       NameEn = "CallCenter",
                       Order = 5,
                       TagId =(int)ModulesTags.CRM,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "اعدادات الاتصالات",NameEn = "Call Instance", ModuleName = AppModules.CRM.CustomerSurvey,Route="/crm/whatsapp/instance"},

                        }
                    },


                    //Accounting  / Configuration
                         new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "صناديق النقديه",NameEn = "Cash Boxes", ModuleName = AppModules.Accounting.CashBoxes,Route="/accounting/configuration/cash_boxes"},
                            new MenuItem{ NameAr = "البنوك",NameEn = "Banks", ModuleName = AppModules.Accounting.Banks,Route="/accounting/configuration/banks"},

                            new MenuItem{ NameAr = "الحسابات البنكية",NameEn = "Bank Account", ModuleName = AppModules.Accounting.BankAccount,Route="/accounting/configuration/bank_account"},

                            new MenuItem{ NameAr = "اليومية",NameEn = "Journal", ModuleName = AppModules.Accounting.Journal,Route="/accounting/configuration/journal"},

                            new MenuItem{ NameAr = "أنواع الحسابات الختامية",NameEn = "Final Accounts Types", ModuleName = AppModules.Accounting.FinalAccountsTypes,Route="/accounting/configuration/final_accounts_types"},

                            new MenuItem{ NameAr = "اعداد تحليل القوائم المالية",NameEn = "Financial Analysis Preparation ", ModuleName = AppModules.Accounting.FinancialAnalysisPreparation,Route="/accounting/configuration/financial_analysis_preparation_"},

                            new MenuItem{ NameAr = "دليل الحسابات",NameEn = "Chart Of Accounts", ModuleName = AppModules.Accounting.ChartOfAccounts,Route="/accounting/configuration/chart_of_accounts"},

                            new MenuItem{ NameAr = "ربط الحسابات",NameEn = "Accounts Routing", ModuleName = AppModules.Accounting.AccountsRouting,Route="/accounting/configuration/accounts_routing"},

                            new MenuItem{ NameAr = "الموازنة التقديرية",NameEn = "Estimated Budget", ModuleName = AppModules.Accounting.EstimatedBudget,Route="/accounting/configuration/estimated_budget"},

                            new MenuItem{ NameAr = "اقفال الفترة",NameEn = "Closing Period", ModuleName = AppModules.Accounting.ClosingPeriod,Route="/accounting/configuration/closing_period"},
                            new MenuItem{ NameAr = "السنة المالية",NameEn = "Fiscal Year", ModuleName = AppModules.Accounting.FiscalYear,Route="/accounting/configuration/fiscal_year"},
                            new MenuItem{ NameAr = "أنواع التكلفة",NameEn = "Cost Types", ModuleName = AppModules.Accounting.CostTypes,Route="/accounting/configuration/cost_types"},

                            new MenuItem{ NameAr = "الشركاء",NameEn = "Partners", ModuleName = AppModules.Accounting.Partners,Route="/accounting/configuration/partners"},

                            new MenuItem{ NameAr = "مجموعة الضرائب",NameEn = "Tax Group", ModuleName = AppModules.Accounting.TaxGroup,Route="/accounting/configuration/tax_group"},
                            new MenuItem{ NameAr = "الضرائب",NameEn = "Taxes", ModuleName = AppModules.Accounting.Taxes,Route="/accounting/configuration/taxes"},

                        }
                    },

                   //Accounting  / Opening balance
                  
                       new MenuItem
                    {
                       NameAr = "الارصدة الافتتاحية",
                       NameEn = "Opening",
                       Order = 2,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            // كلهم كده ان شاء الله كترولر واحد 
                            new MenuItem{ NameAr = "صناديق النقدية",NameEn = "Cash Boxes Opening Balance", ModuleName = AppModules.Accounting.CashBoxesOpeningBalance,Route="/accounting/opening/cash_boxes_opening_balance"},
                            new MenuItem{ NameAr = "الموظفين",NameEn = "Employees Opening Balance", ModuleName = AppModules.Accounting.EmployeesOpeningBalance,Route="/accounting/opening/employees_opening_balance"},
                            new MenuItem{ NameAr = "البنوك",NameEn = "Banks Opening Balance", ModuleName = AppModules.Accounting.BanksOpeningBalance,Route="/accounting/opening/banks_opening_balance"},
                            new MenuItem{ NameAr = "العملاء",NameEn = "Customers Opening Balance", ModuleName = AppModules.Accounting.CustomersOpeningBalance,Route="/accounting/opening/customers_opening_balance"},
                            new MenuItem{ NameAr = "الموردين",NameEn = "Supplier Opening Balance", ModuleName = AppModules.Accounting.SupplierOpeningBalance,Route="/accounting/opening/supplier_opening_balance"},

                            // دا مع كنترولر قيود اليومية
                            new MenuItem{ NameAr = "القيود الافتتاحية واللإقفال",NameEn = "Opining and Closing Journals", ModuleName = AppModules.Accounting.OpiningandClosingJournals,Route="/accounting/opening/opining_and_closing_journals"},

                        }
                    },
                   //Accounting  /  Accounting

                            new MenuItem
                    {
                       NameAr = "الحسابات",
                       NameEn = "Accounting",
                       Order = 3,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "طلب صرف",NameEn = "Exchange Request", ModuleName = AppModules.Accounting.ExchangeRequest,Route="/accounting/accounting/exchange_request"},

                            new MenuItem{ NameAr = "قيد للمراجعة",NameEn = "Journal For Audit ", ModuleName = AppModules.Accounting.JournalForAudit,Route="/accounting/accounting/journal_for_audit_"},

                            new MenuItem{ NameAr = "مدفوعات نقدية",NameEn = "Cash Payments", ModuleName = AppModules.Accounting.CashPayments,Route="/accounting/accounting/cash_payments"},
                            new MenuItem{ NameAr = "مدفوعات بنكية",NameEn = "Bank Payments", ModuleName = AppModules.Accounting.BankPayments,Route="/accounting/accounting/bank_payments"},
                            new MenuItem{ NameAr = "مصروفات نقدية",NameEn = "Cash Receipts", ModuleName = AppModules.Accounting.CashReceipts,Route="/accounting/accounting/cash_receipts"},
                            new MenuItem{ NameAr = "مقبوضات بنكية",NameEn = "Bank Receipts", ModuleName = AppModules.Accounting.BankReceipts,Route="/accounting/accounting/bank_receipts"},
                            new MenuItem{ NameAr = "قيد يومية",NameEn = "Journal Entries", ModuleName = AppModules.Accounting.JournalEntries,Route="/accounting/accounting/journal_entries"},
                            new MenuItem{ NameAr = "الترحيل من نقاط البيع الي القيود",NameEn = "Posting From POS", ModuleName = AppModules.Accounting.PostingFromPOS,Route="/accounting/accounting/posting_from_pos"},

                            new MenuItem{ NameAr = "تسوية حسابات الموظفين",NameEn = "Custody settlement", ModuleName = AppModules.Accounting.Custodysettlement,Route="/accounting/accounting/custody_settlement"},


                            new MenuItem{ NameAr = "اشعارات الخصم والاضافة",NameEn = "Notices", ModuleName = AppModules.Accounting.Notices,Route="/accounting/accounting/notices"},

                            new MenuItem{ NameAr = "تسجيل دفتر شيكات",NameEn = "Cheques Book", ModuleName = AppModules.Accounting.ChequesBook,Route="/accounting/accounting/cheques_book"},
                            new MenuItem{ NameAr = "متابعة اوارق القبض",NameEn = "Notes Receivable", ModuleName = AppModules.Accounting.NotesReceivable,Route="/accounting/accounting/notes_receivable"},

                            new MenuItem{ NameAr = "متابعة اوراق الدفع",NameEn = "Notes Payable ", ModuleName = AppModules.Accounting.NotesPayable,Route="/accounting/accounting/notes_payable_"},


                        }
                    },
                  
                   //Accounting  / Letters Of Guarantee
                  
                                 new MenuItem
                    {
                       NameAr = "خطابات الضمان",
                       NameEn = "Letters",
                       Order = 4,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "أنواع خطابات الضمان",NameEn = "Letters Of GuaranteeTypes", ModuleName = AppModules.Accounting.LettersOfGuaranteeTypes,Route="/accounting/letters/letters_of_guaranteetypes"},

                            new MenuItem{ NameAr = "حدود خطابات الضمان",NameEn = "Letters Of Guarantee Limitations", ModuleName = AppModules.Accounting.LettersOfGuaranteeLimitations,Route="/accounting/letters/letters_of_guarantee_limitations"},

                            new MenuItem{ NameAr = "خطابات الضمان",NameEn = "Letters Of Guarantee", ModuleName = AppModules.Accounting.LettersOfGuarantee,Route="/accounting/letters/letters_of_guarantee"},

                            new MenuItem{ NameAr = "خطابات الضمان المستخدمة",NameEn = "Letters Of Guarantee Used", ModuleName = AppModules.Accounting.LettersOfGuaranteeUsed,Route="/accounting/letters/letters_of_guarantee_used"},

                            new MenuItem{ NameAr = "رصيد خطابات الضمان",NameEn = "Letters Of Guarantee Balance", ModuleName = AppModules.Accounting.LettersOfGuaranteeBalance,Route="/accounting/letters/letters_of_guarantee_balance"},

                            new MenuItem{ NameAr = "تجديد فترة الضمان",NameEn = "Renewal Warranty Period", ModuleName = AppModules.Accounting.RenewalWarrantyPeriod,Route="/accounting/letters/renewal_warranty_period"},

                            new MenuItem{ NameAr = "تغيير قيمة الضمان",NameEn = "Changing   Guarantee Value", ModuleName = AppModules.Accounting.ChangingGuaranteeValue,Route="/accounting/letters/changing_guarantee_value"},


                        }
                    },
                  
                   //Accounting  /  Documentary Credits
                  
                     new MenuItem
                    {
                       NameAr = "الاعتمادات البنكية",
                       NameEn = "Documentary Credits",
                       Order = 5,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "أنواع الاعتمادات المستندية",NameEn = "Documentary Credits Type", ModuleName = AppModules.Accounting.DocumentaryCreditsType,Route="/accounting/documentary/documentary_credits_type"},

                            new MenuItem{ NameAr = "حدود الاعتمادات المستندية",NameEn = "Documentary Credit Limits", ModuleName = AppModules.Accounting.DocumentaryCreditLimits,Route="/accounting/documentary/documentary_credit_limits"},

                            new MenuItem{ NameAr = "الاعتمادات المستندية",NameEn = "Documentary Credits", ModuleName = AppModules.Accounting.DocumentaryCredits,Route="/accounting/documentary/documentary_credits"},

                            new MenuItem{ NameAr = "رصيد الاعتمادات المستندية",NameEn = "Documentary Credits Balance", ModuleName = AppModules.Accounting.DocumentaryCreditsBalance,Route="/accounting/documentary/documentary_credits_balance"},

                            new MenuItem{ NameAr = "تجديد فترة الاعتماد",NameEn = "Renewal Accreditation Period", ModuleName = AppModules.Accounting.RenewalAccreditationPeriod,Route="/accounting/documentary/renewal_accreditation_period"},

                            new MenuItem{ NameAr = "تغيير قيمة الاعتماد",NameEn = "Change Credit Value", ModuleName = AppModules.Accounting.ChangeCreditValue,Route="/accounting/documentary/change_credit_value"},

                        }
                    },
                   //Accounting  /  Loans
                  
                     new MenuItem
                    {
                       NameAr = "القروض",
                       NameEn = "Loans",
                       Order =6,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "أنواع القروض",NameEn = "Loan Types", ModuleName = AppModules.Accounting.LoanTypes,Route="/accounting/loans/loan_types"},
                            new MenuItem{ NameAr = "حدود القروض",NameEn = "Loan Limits", ModuleName = AppModules.Accounting.LoanLimits,Route="/accounting/loans/loan_limits"},
                            new MenuItem{ NameAr = "القروض",NameEn = "Loans", ModuleName = AppModules.Accounting.Loans,Route="/accounting/loans/loans"},

                            new MenuItem{ NameAr = "تجديد فترة القرض",NameEn = "Renewal Loan Period", ModuleName = AppModules.Accounting.RenewalLoanPeriod,Route="/accounting/loans/renewal_loan_period"},

                            new MenuItem{ NameAr = "سداد القروض",NameEn = "Loans Repayment  ", ModuleName = AppModules.Accounting.LoansRepayment,Route="/accounting/loans/loans_repayment"},

                            new MenuItem{ NameAr = "أرصدة القرض حسب المشروع",NameEn = "Loan Balances", ModuleName = AppModules.Accounting.LoanBalances,Route="/accounting/loans/loan_balances"},


                        }
                    },

                   //Accounting  /  Assets

                  new MenuItem
                    {
                       NameAr = "الاصول ",
                       NameEn = "Assets",
                       Order = 7,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "قائمة الاصول",NameEn = "Assets List", ModuleName = AppModules.Accounting.AssetsList,Route="/accounting/assets/assets_list"},

                            new MenuItem{ NameAr = "اهلاك الاصول",NameEn = "Assets Depreciation  ", ModuleName = AppModules.Accounting.AssetsDepreciation,Route="/accounting/assets/assets_depreciation"},

                            new MenuItem{ NameAr = "بيان الاصول",NameEn = "Assets Statement", ModuleName = AppModules.Accounting.AssetsStatement,Route="/accounting/assets/assets_statement"},

                            new MenuItem{ NameAr = "بيان اهلاكات الاصول",NameEn = "Depreciation  List", ModuleName = AppModules.Accounting.DepreciationList,Route="/accounting/assets/depreciation_list"},

                            new MenuItem{ NameAr = "استبعاد اصل",NameEn = "Asset Disposal", ModuleName = AppModules.Accounting.AssetDisposal,Route="/accounting/assets/asset_disposal"},

                        }
                    },

                  
                   //Accounting  /  Analytic Accounts
                  
                       new MenuItem
                    {
                       NameAr = "مراكز التكلفة",
                       NameEn = "Analytics",
                       Order =8,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "دليل مراكز التكلفة",NameEn = "Analytic Accounts List", ModuleName = AppModules.Accounting.AnalyticAccountsList,Route="/accounting/analytics/analytic_accounts_list"},

                            new MenuItem{ NameAr = "اعدادات التكاليف الغير مباشرة",NameEn = "Indirect Costs Setting", ModuleName = AppModules.Accounting.IndirectCostsSetting,Route="/accounting/analytics/indirect_costs_setting"},

                            new MenuItem{ NameAr = "مجموعات مراكز التكلفة",NameEn = "Analytic Accounts Groups", ModuleName = AppModules.Accounting.AnalyticAccountsGroups,Route="/accounting/analytics/analytic_accounts_groups"},

                            new MenuItem{ NameAr = "مراكز تكلفة تفصيلي",NameEn = "Analytic Accounts Detailed ", ModuleName = AppModules.Accounting.AnalyticAccountsDetailed,Route="/accounting/analytics/analytic_accounts_detailed_"},

                            new MenuItem{ NameAr = "مراكز تكلفة اجمالي",NameEn = "Analytic Accounts Total", ModuleName = AppModules.Accounting.AnalyticAccountsTotal,Route="/accounting/analytics/analytic_accounts_total"},
                            new MenuItem{ NameAr = "مراكز تكلفة اجمالي بالشهور",NameEn = "Analytic Accounts Monthly", ModuleName = AppModules.Accounting.AnalyticAccountsMonthly,Route="/accounting/analytics/analytic_accounts_monthly"},
                            new MenuItem{ NameAr = "موقف مراكز التكلفة",NameEn = "Analytic Accounts", ModuleName = AppModules.Accounting.AnalyticAccountsReport,Route="/accounting/analytics/analytic_accounts_report"},
                            new MenuItem{ NameAr = "صافى المستهلك الشهري",NameEn = "Monthly Net Consumer", ModuleName = AppModules.Accounting.MonthlyNetConsumer,Route="/accounting/analytics/monthly_net_consumer"},
                            new MenuItem{ NameAr = "اجمالي المستهلك",NameEn = "Total Consumer", ModuleName = AppModules.Accounting.TotalConsumer,Route="/accounting/analytics/total_consumer"},
                            new MenuItem{ NameAr = "اومر توريد مراكز التكلفة",NameEn = "Analytic Accounts PO", ModuleName = AppModules.Accounting.AnalyticAccountsPO,Route="/accounting/analytics/analytic_accounts_po"},
                            new MenuItem{ NameAr = "طلبات شراء مراكز التكلفة",NameEn = "Analytic Accounts RFQ", ModuleName = AppModules.Accounting.AnalyticAccountsRFQ,Route="/accounting/analytics/analytic_accounts_rfq"},

                        }
                    },

                   //Accounting  /  Accounting  Reports

                   new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 9,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "حساب البنوك",NameEn = "Bank Accounts", ModuleName = AppModules.Accounting.BankAccountsReport,Route="/accounting/reports/bank_accounts_report"},
                            new MenuItem{ NameAr = "حساب العملاء",NameEn = "Customer Accounts", ModuleName = AppModules.Accounting.CustomerAccountsReport,Route="/accounting/reports/customer_accounts_report"},
                            new MenuItem{ NameAr = "حساب الموردين",NameEn = "Payables Accounts", ModuleName = AppModules.Accounting.PayablesAccountsReport,Route="/accounting/reports/payables_accounts_report"},
                            new MenuItem{ NameAr = "حساب استاذ",NameEn = "Ledger", ModuleName = AppModules.Accounting.LedgerReport,Route="/accounting/reports/ledger_report"},
                            new MenuItem{ NameAr = "حساب الموظفين",NameEn = "Staff Accounts", ModuleName = AppModules.Accounting.StaffAccountsReport,Route="/accounting/reports/staff_accounts_report"},
                            new MenuItem{ NameAr = "كشف حساب المندوب",NameEn = "Salesperson Account", ModuleName = AppModules.Accounting.SalespersonAccountReport,Route="/accounting/reports/salesperson_account_report"},
                            new MenuItem{ NameAr = "حساب صناديق النقدية",NameEn = "Cash Box Accounts", ModuleName = AppModules.Accounting.CashBoxAccountsReport,Route="/accounting/reports/cash_box_accounts_report"},
                            new MenuItem{ NameAr = "حساب الشركاء",NameEn = "Partners Accounts", ModuleName = AppModules.Accounting.PartnersAccountsReport,Route="/accounting/reports/partners_accounts_report"},
                            new MenuItem{ NameAr = "حساب شهري",NameEn = "Monthly Account", ModuleName = AppModules.Accounting.MonthlyAccountReport,Route="/accounting/reports/monthly_account_report"},
                            new MenuItem{ NameAr = "طباعة الرواتب",NameEn = "Salaries Print", ModuleName = AppModules.Accounting.SalariesPrintReport,Route="/accounting/reports/salaries_print_report"},
                            new MenuItem{ NameAr = "مراة الحسابات",NameEn = "Accounts Mirror", ModuleName = AppModules.Accounting.AccountsMirrorReport,Route="/accounting/reports/accounts_mirror_report"},
                            new MenuItem{ NameAr = "اقرار ضرائب المبيعات",NameEn = "Sales Taxes", ModuleName = AppModules.Accounting.SalesTaxesReport,Route="/accounting/reports/sales_taxes_report"},
                            new MenuItem{ NameAr = "قيود اليومية",NameEn = "Journal Entry", ModuleName = AppModules.Accounting.JournalEntryReport,Route="/accounting/reports/journal_entry_report"},
                            new MenuItem{ NameAr = "أعمار الديون عملاء",NameEn = "Aged Receivable", ModuleName = AppModules.Accounting.AgedReceivableReport,Route="/accounting/reports/aged_receivable_report"},
                            new MenuItem{ NameAr = "أعمار ديون الموردين",NameEn = "Aged Payable", ModuleName = AppModules.Accounting.AgedPayableReport,Route="/accounting/reports/aged_payable_report"},
                            new MenuItem{ NameAr = "حساب استاذ العام",NameEn = "General Ledger", ModuleName = AppModules.Accounting.GeneralLedgerReport,Route="/accounting/reports/general_ledger_report"},
                            new MenuItem{ NameAr = "حركات لم يتم ترحيلها",NameEn = "Not Posted Transactions", ModuleName = AppModules.Accounting.NotPostedTransactions,Route="/accounting/reports/not_posted_transactions"},
                            new MenuItem{ NameAr = "المصروفات المدفوعة مقدما",NameEn = "Prepaid Expenses", ModuleName = AppModules.Accounting.PrepaidExpenses,Route="/accounting/reports/prepaid_expenses"},
                            new MenuItem{ NameAr = "حركات سندات الصرف والقبض",NameEn = "Payments And Receipt ", ModuleName = AppModules.Accounting.PaymentsAndReceiptReport,Route="/accounting/reports/payments_and_receipt_report"},

                        }
                    },

                  
                   //Accounting  /  Final Accounts

                    new MenuItem
                    {
                       NameAr = "الحسابات الختامية",
                       NameEn = " Final Accounts",
                       Order = 10,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "ميزان المراجعة",NameEn = "Trial Balance", ModuleName = AppModules.Accounting.TrialBalanceReport,Route="/accounting/final_accounts/trial_balance_report"},
                            new MenuItem{ NameAr = "ميزان بالحسابات الرئيسية",NameEn = "Trial Balance  Levels", ModuleName = AppModules.Accounting.TrialBalanceLevelsReport,Route="/accounting/final_accounts/trial_balance_levels_report"},
                            new MenuItem{ NameAr = "تحليل ايرادات",NameEn = "Revenues Analysis", ModuleName = AppModules.Accounting.RevenuesAnalysisReport,Route="/accounting/final_accounts/revenues_analysis_report"},
                            new MenuItem{ NameAr = "قائمة الدخل",NameEn = "Profit And Loss", ModuleName = AppModules.Accounting.IncomeStatementReport,Route="/accounting/final_accounts/income_statement_report"},
                            new MenuItem{ NameAr = "قائمة المركز المالى",NameEn = "Balance Sheet", ModuleName = AppModules.Accounting.BalanceSheetReport,Route="/accounting/final_accounts/balance_sheet_report"},
                            new MenuItem{ NameAr = "قائمة التغير فى حقوق الشركاء",NameEn = "Equity Changes", ModuleName = AppModules.Accounting.EquityChangesReport,Route="/accounting/final_accounts/equity_changes_report"},
                            new MenuItem{ NameAr = "القوائم الماليه مخصص",NameEn = "Custom Financial Statements", ModuleName = AppModules.Accounting.CustomFinancialStatementsReport,Route="/accounting/final_accounts/custom_financial_statements_report"},
                            new MenuItem{ NameAr = "معدل انحراف الموازنة التقديرية",NameEn = "Estimated budget Deviation", ModuleName = AppModules.Accounting.EstimatedbudgetDeviationReport,Route="/accounting/final_accounts/estimated_budget_deviation_report"},
                            new MenuItem{ NameAr = "قائمة التدفقات النقدية",NameEn = "Cash Flow Statement", ModuleName = AppModules.Accounting.CashFlowsReport,Route="/accounting/final_accounts/cash_flows_report"},

                        }
                    },

                  
                   //Accounting  /  Financial Analysis

                     new MenuItem
                    {
                       NameAr = "التحليل المالي",
                       NameEn = "Analysis",
                       Order = 11,
                       TagId =(int)ModulesTags.Accounting,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "تمثيل حركة الايداعات ",NameEn = "Deposits Movement", ModuleName = AppModules.Accounting.DepositsMovementReport,Route="/accounting/analysis/deposits_movement_report"},
                            new MenuItem{ NameAr = "تحليل القوائم المالية",NameEn = "Financial Statement Analysis", ModuleName = AppModules.Accounting.FinancialStatementAnalysis,Route="/accounting/analysis/financial_statement_analysis"},
                            new MenuItem{ NameAr = "مقارنة الحسابات بالشهور",NameEn = "Compare Accounts  monthly", ModuleName = AppModules.Accounting.CompareAccountsmonthlyReport,Route="/accounting/analysis/compare_accounts_monthly_report"},
                            new MenuItem{ NameAr = "مقارنة الحسابات ربع سنوى",NameEn = "Comparing Accounts quarterly", ModuleName = AppModules.Accounting.ComparingAccountsquarterlyReport,Route="/accounting/analysis/comparing_accounts_quarterly_report"},
                            new MenuItem{ NameAr = "ملخص الحسابات",NameEn = "Accounts SummaryReport", ModuleName = AppModules.Accounting.AccountsSummaryReport,Route="/accounting/analysis/accounts_summary"},

                        }
                    },
  
                      
                    //Maintenance / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Maintenance,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "قائمة المعدات",NameEn = "Equipment List", ModuleName = AppModules.Maintenance.EquipmentList,Route="/maintenance/configuration/equipment_list"},
                            new MenuItem{ NameAr = "أنواع الصيانة",NameEn = "Maintenance Types", ModuleName = AppModules.Maintenance.MaintenanceTypes,Route="/maintenance/configuration/maintenance_types"},

                        }
                    },

                     //Maintenance / Preventive Maintenance
                      new MenuItem
                    {
                       NameAr = "الصيانة الوقائية",
                       NameEn = "Preventive Maintenance",
                       Order = 2,
                       TagId =(int)ModulesTags.Maintenance,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "الصيانة الوقائية",NameEn = "Maintenance Plan", ModuleName = AppModules.Maintenance.MaintenancePlan,Route="/maintenance/preventive/maintenance_plan"},

                        }
                    },
                     //Maintenance / Maintenance
                      new MenuItem
                    {
                       NameAr = "الصيانة",
                       NameEn = "Maintenance",
                       Order = 3,
                       TagId =(int)ModulesTags.Maintenance,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "تسجيل الاعطال",NameEn = "Malfunctions", ModuleName = AppModules.Maintenance.Malfunctions,Route="/maintenance/maintenance/malfunctions"},

                            new MenuItem{ NameAr = "معدل استهلاك الاقسام",NameEn = "Department Consumption Rate", ModuleName = AppModules.Maintenance.DepartmentConsumptionRate,Route="/maintenance/maintenance/department_consumption_rate"},

                            new MenuItem{ NameAr = "طلب صيانه",NameEn = "Maintenance Requests", ModuleName = AppModules.Maintenance.MaintenanceRequests,Route="/maintenance/maintenance/maintenance_requests"},

                            new MenuItem{ NameAr = "تسجيل المرور الدورى",NameEn = "Periodic traffic", ModuleName = AppModules.Maintenance.Periodictraffic,Route="/maintenance/maintenance/periodic_traffic"},

                            new MenuItem{ NameAr = "معايرة جهاز",NameEn = "Calibration Device", ModuleName = AppModules.Maintenance.CalibrationDevice,Route="/maintenance/maintenance/calibration_device"},


                        }
                    },
                    //Maintenance /   Contracts
                     new MenuItem
                    {
                       NameAr = "عقود الصيانة",
                       NameEn = "Contracts",
                       Order = 4,
                       TagId =(int)ModulesTags.Maintenance,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "انواع العقود",NameEn = "Contracts Types", ModuleName = AppModules.Maintenance.ContractsTypes,Route="/maintenance/contracts/contracts_types"},

                            new MenuItem{ NameAr = "انواع الكولات",NameEn = "Call Types", ModuleName = AppModules.Maintenance.CallTypes,Route="/maintenance/contracts/call_types"},

                            new MenuItem{ NameAr = "اعطال جهاز",NameEn = "Device Malfunction", ModuleName = AppModules.Maintenance.DeviceMalfunction,Route="/maintenance/contracts/device_malfunction"},

                            new MenuItem{ NameAr = "مراحل العطل",NameEn = "Faults Stages", ModuleName = AppModules.Maintenance.FaultsStages,Route="/maintenance/contracts/faults_stages"},

                            new MenuItem{ NameAr = "تشخيص وخطوات الاصلاح",NameEn = "Diagnosis  Steps", ModuleName = AppModules.Maintenance.DiagnosisSteps,Route="/maintenance/contracts/diagnosis_steps"},

                            new MenuItem{ NameAr = "اضافة عقد صيانة",NameEn = "Maintenance Contract", ModuleName = AppModules.Maintenance.MaintenanceContract,Route="/maintenance/contracts/maintenance_contract"},

                            new MenuItem{ NameAr = "فتح كول",NameEn = "Open Call", ModuleName = AppModules.Maintenance.OpenCall,Route="/maintenance/contracts/open_call"},


                            // تابع مديول صيانة السيارات ان شاء الله لما يتم تركيبه مع شاشات فيصل
                            new MenuItem{ NameAr = "أمر شغل",NameEn = "Job Order", ModuleName = AppModules.Maintenance.JobOrder,Route="/maintenance/contracts/job_order"},

                        }
                    },
                    //Maintenance /   Reports
                     new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 5,
                       TagId =(int)ModulesTags.Maintenance,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "كولات الصيانة",NameEn = "Calls", ModuleName = AppModules.Maintenance.CallsReport,Route="/maintenance/reports/calls_report"},
                            new MenuItem{ NameAr = "اعطال عميل",NameEn = "Client malfunctions", ModuleName = AppModules.Maintenance.ClientmalfunctionsReport,Route="/maintenance/reports/client_malfunctions_report"},

                        }
                    },

                    //Manufacturing / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Manufacturing,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "مراحل تشغيل المنتجات",NameEn = "Manufacturing Stages", ModuleName = AppModules.Manufacturing.ManufacturingStages,Route="/manufacturing/configuration/manufacturing_stages"},

                        }
                    },

                      //Manufacturing / Manufacturing Orders
                    new MenuItem
                    {
                       NameAr = "اوامر تصنيع",
                       NameEn = "Manufacturing Orders",
                       Order = 2,
                       TagId =(int)ModulesTags.Manufacturing,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "استلام امر شغل",NameEn = "Receipt Orders", ModuleName = AppModules.Manufacturing.ReceiptOrders,Route="/manufacturing/orders/receipt_orders"},
                            new MenuItem{ NameAr = "تكلفة أمر شغل",NameEn = "Order Cost", ModuleName = AppModules.Manufacturing.OrderCost,Route="/manufacturing/orders/order_cost"},
                            new MenuItem{ NameAr = "إضافة أمر شغل",NameEn = "Orders", ModuleName = AppModules.Manufacturing.Orders,Route="/manufacturing/orders/orders"},

                            new MenuItem{ NameAr = "كارت متابعة مراحل الانتاج",NameEn = "Production Stages Follow", ModuleName = AppModules.Manufacturing.ProductionStagesFollow,Route="/manufacturing/orders/production_stages_follow"},

                            new MenuItem{ NameAr = "التجميع والتفكيك",NameEn = "Assembly And Disassembly", ModuleName = AppModules.Manufacturing.AssemblyAndDisassembly,Route="/manufacturing/orders/assembly_and_disassembly"},

                            new MenuItem{ NameAr = "طلبات المواد",NameEn = "Material Requests", ModuleName = AppModules.Manufacturing.MaterialRequests,Route="/manufacturing/orders/material_requests"},

                            new MenuItem{ NameAr = "خطة التصنيع",NameEn = "Manufacturing Plan", ModuleName = AppModules.Manufacturing.ManufacturingPlan,Route="/manufacturing/orders/manufacturing_plan"},

                        }
                    },

                       //Manufacturing / Reports
                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 3,
                       TagId =(int)ModulesTags.Manufacturing,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "أوامر التشغيل",NameEn = "Orders", ModuleName = AppModules.Manufacturing.OrdersReport,Route="/manufacturing/reports/orders_report"},
                            new MenuItem{ NameAr = "تقرير فحص منتج",NameEn = "Quality", ModuleName = AppModules.Manufacturing.Qualityreport,Route="/manufacturing/reports/quality_report"},

                        }
                    },

                    //Project / Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Project,
                        Children = new List<MenuItem>
                        {
                            // read from customr controler from sales 
                            new MenuItem{ NameAr = "العملاء",NameEn = "Customers", ModuleName = AppModules.Project.Customers,Route="/project/configuration/customers"},

                            new MenuItem{ NameAr = "بيانات الاستشاريين",NameEn = "Consultants", ModuleName = AppModules.Project.Consultants,Route="/project/configuration/consultants"},

                            new MenuItem{ NameAr = "أنواع العطاءات",NameEn = "Tenders Type", ModuleName = AppModules.Project.TendersType,Route="/project/configuration/tenders_type"},

                            new MenuItem{ NameAr = "نوع موقف العطاء",NameEn = "Type Of Attitude Tender", ModuleName = AppModules.Project.TypeOfAttitudeTender,Route="/project/configuration/type_of_attitude_tender"},

                            new MenuItem{ NameAr = "انواع مراكز العطاء",NameEn = "Tender Arrange Types ", ModuleName = AppModules.Project.TenderArrangeTypes,Route="/project/configuration/tender_arrange_types"},

                            new MenuItem{ NameAr = "أنواع المناقصات",NameEn = " Tender Activity Types", ModuleName = AppModules.Project.TenderActivityTypes,Route="/project/configuration/_tender_activity_types"},


                            new MenuItem{ NameAr = "نوع قطاع الاعمال",NameEn = "Business Type", ModuleName = AppModules.Project.BusinessType,Route="/project/configuration/business_type"},

                            new MenuItem{ NameAr = "العطاءات",NameEn = "Tenders", ModuleName = AppModules.Project.Tenders,Route="/project/configuration/tenders"},

                            new MenuItem{ NameAr = "الإستقطاعات",NameEn = "Tenders Deductions", ModuleName = AppModules.Project.TendersDeductions,Route="/project/configuration/tenders_deductions"},

                            new MenuItem{ NameAr = "أنواع الشركات",NameEn = "Competitors Tyep", ModuleName = AppModules.Project.CompetitorsTyep,Route="/project/configuration/competitors_tyep"},

                            new MenuItem{ NameAr = "اسماء الشركات",NameEn = "Competitors", ModuleName = AppModules.Project.Competitors,Route="/project/configuration/competitors"},

                        }
                    },


                    //im here 

                     //Project / Tender Study

                    new MenuItem
                    {
                       NameAr = "دراسة العطاء",
                       NameEn = "Tender Study",
                       Order = 2,
                       TagId =(int)ModulesTags.Project,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "المجموعات الرئيسية للعطاء",NameEn = "Tender Groups Types", ModuleName = AppModules.Project.TenderGroupsTypes,Route="/project/tender_study/tender_groups_types"},

                            new MenuItem{ NameAr = "نماذج العطاءات",NameEn = "Tender Models", ModuleName = AppModules.Project.TenderModels,Route="/project/tender_study/tender_models"},

                            new MenuItem{ NameAr = "مكونات نماذج العطاء",NameEn = "Tender Components", ModuleName = AppModules.Project.TenderComponents,Route="/project/tender_study/tender_components"},

                            new MenuItem{ NameAr = "الاعمال الرئيسية",NameEn = "Main works", ModuleName = AppModules.Project.Mainworks,Route="/project/tender_study/main_works"},

                            new MenuItem{ NameAr = "الاعمال الفرعية",NameEn = "Sub Works", ModuleName = AppModules.Project.SubWorks,Route="/project/tender_study/sub_works"},

                            new MenuItem{ NameAr = "بنود تحليل الاسعار",NameEn = "Price Analysis Items", ModuleName = AppModules.Project.PriceAnalysisItems,Route="/project/tender_study/price_analysis_items"},

                            new MenuItem{ NameAr = "جدول الكميات",NameEn = "Bills of Materials", ModuleName = AppModules.Project.BillsofMaterials,Route="/project/tender_study/bills_of_materials"},

                            new MenuItem{ NameAr = "دراسة العطــاء",NameEn = "Bills of Materials Study", ModuleName = AppModules.Project.BillsofMaterialsStudy,Route="/project/tender_study/bills_of_materials_study"},

                        }
                    },


                    // i here

                      //Project / Operations

                    new MenuItem
                    {
                       NameAr = "العمليات",
                       NameEn = "Tender Operations",
                       Order = 3,
                       TagId =(int)ModulesTags.Project,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "متابعة المخططات",NameEn = "Drawing", ModuleName = AppModules.Project.Drawing,Route="/project/operations/drawing"},

                            new MenuItem{ NameAr = "جدول اعتماد العينة",NameEn = "Sample Approval", ModuleName = AppModules.Project.SampleApproval,Route="/project/operations/sample_approval"},

                            new MenuItem{ NameAr = "أمر إسناد",NameEn = "Assignment Order", ModuleName = AppModules.Project.AssignmentOrder,Route="/project/operations/assignment_order"},

                        }
                    },

                     //Project / Contracts And Extracts

                    new MenuItem
                    {
                       NameAr = "العقود والمستخلصات",
                       NameEn = "Contracts And Extracts",
                       Order = 4,
                       TagId =(int)ModulesTags.Project,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "انواع العقود",NameEn = "Contracts Types", ModuleName = AppModules.Project.ContractsTypes,Route="/project/contracts_and_extracts/contracts_types"},

                            new MenuItem{ NameAr = "عقود مقاولين الباطن",NameEn = "Subcontractors Contracts", ModuleName = AppModules.Project.SubcontractorsContracts,Route="/project/contracts_and_extracts/subcontractors_contracts"},

                            new MenuItem{ NameAr = "مستخلص مقاولين الباطن",NameEn = "Subcontractors Extracts", ModuleName = AppModules.Project.SubcontractorsExtracts,Route="/project/contracts_and_extracts/subcontractors_extracts"},

                            new MenuItem{ NameAr = "مستخلص عميل",NameEn = "Customers Extracts", ModuleName = AppModules.Project.CustomersExtracts,Route="/project/contracts_and_extracts/customers_extracts"},

                        }
                    },

                      //Project / Report

                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Report",
                       Order = 5,
                       TagId =(int)ModulesTags.Project,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "بيانات العطاءات الفنى والمالى",NameEn = "Tenders Technical And Financial", ModuleName = AppModules.Project.TendersTechnicalAndFinancialReport,Route="/project/reports/tenders_technical_and_financial_report"},
                            new MenuItem{ NameAr = "دراسة العطــاء",NameEn = "Tender Study", ModuleName = AppModules.Project.TenderStudyReport,Route="/project/reports/tender_study__report"},
                            new MenuItem{ NameAr = "إجمالي المشاريع",NameEn = "Total Project", ModuleName = AppModules.Project.TotalProject,Route="/project/reports/total_project"},
                            new MenuItem{ NameAr = "مركز مالي للمشروع",NameEn = "Project Financial", ModuleName = AppModules.Project.ProjectFinancial,Route="/project/reports/project_financial"},
                            new MenuItem{ NameAr = "قيمة المشروع حسب المجموعات",NameEn = "Project Value", ModuleName = AppModules.Project.ProjectValue,Route="/project/reports/project_value"},
                            new MenuItem{ NameAr = "المستخلصات",NameEn = "Extracts", ModuleName = AppModules.Project.Extracts,Route="/project/reports/extracts"},
                            new MenuItem{ NameAr = "إجمالي بند مشروع",NameEn = "Total Project Item", ModuleName = AppModules.Project.TotalProjectItem,Route="/project/reports/total_project_item"},
                            new MenuItem{ NameAr = "تفصيلي بند مشروع",NameEn = "Detailed  Project Item", ModuleName = AppModules.Project.DetailedProjectItem,Route="/project/reports/detailed__project_item"},

                        }
                    },


                    // im here 


                   //Realestate  / Configuration

                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Realestate,
                        Children = new List<MenuItem>
                        {

                          //  استخدام المندوبين ف المبيعات
                                new MenuItem{ NameAr = "أسماء المندوبين",NameEn = "Salesperson", ModuleName = AppModules.Sales.Salesperson,Route="/sales/configuration/salesperson"},

                               new MenuItem{ NameAr = "انواع المبيعات",NameEn = "Sales Types", ModuleName = AppModules.Sales.SalesTypes,Route="/sales/configuration/sales_types"},

                                new MenuItem{ NameAr = "تصنيف العملاء",NameEn = "Customers Category", ModuleName = AppModules.Sales.CustomersCategory,Route="/sales/configuration/customers_category"},

                                 new MenuItem{ NameAr = "اسماء العملاء الرئيسية",NameEn = "Main Customer", ModuleName = AppModules.Sales.MainCustomer,Route="/sales/configuration/main_customer"},

                            new MenuItem{ NameAr = "بيانات العملاء",NameEn = "Customers", ModuleName = AppModules.Sales.Customers,Route="/sales/configuration/customers"},

                             new MenuItem{ NameAr = "العقار الرئيسيى",NameEn = "Main Property", ModuleName = AppModules.Realestate.MainProperty,Route="/realestate/configuration/main-property"},

                                new MenuItem{ NameAr = "الدليل العقارى",NameEn = "Sub Property", ModuleName = AppModules.Realestate.RealEstateUnits,Route="/realestate/configuration/real_estate_units"},

                        }
                    },

                     //Realestate  / Contracts

                     new MenuItem
                    {
                       NameAr = "العقود",
                       NameEn = "Realestate Contracts",
                       Order = 2,
                       TagId =(int)ModulesTags.Realestate,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "عقود الايجار",NameEn = "Rent Contruct", ModuleName = AppModules.Realestate.RentContruct,Route="/realestate/contracts/rent_contruct"},

                            new MenuItem{ NameAr = "ملحق عقد الإيجار",NameEn = "Rent Contruct Extension", ModuleName = AppModules.Realestate.RentContructExtension,Route="/realestate/contracts/rent_contruct_extension"},

                            new MenuItem{ NameAr = "سند قبض الايجار",NameEn = "Rent Receipt Voucher", ModuleName = AppModules.Realestate.RentReceiptVoucher,Route="/realestate/contracts/rent_receipt_voucher"},

                            new MenuItem{ NameAr = "تسوية عقد الايجار",NameEn = "Rent Contruct Settlement", ModuleName = AppModules.Realestate.RentContructSettlement,Route="/realestate/contracts/rent_contruct_settlement"},

                            new MenuItem{ NameAr = "الصيانة والخدمات",NameEn = "Maintenance And Services", ModuleName = AppModules.Realestate.MaintenanceAndServices,Route="/realestate/contracts/maintenance_and_services"},

                            new MenuItem{ NameAr = "الحجز والتعاقد",NameEn = "Booking And Contracting", ModuleName = AppModules.Realestate.BookingAndContracting,Route="/realestate/contracts/booking_and_contracting"},

                            new MenuItem{ NameAr = "سند قبض قسط",NameEn = "Installment Receipt Voucher", ModuleName = AppModules.Realestate.InstallmentReceiptVoucher,Route="/realestate/contracts/installment_receipt_voucher"},

                        }
                    },

                     //Realestate  / Reports

                      new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 3,
                       TagId =(int)ModulesTags.Realestate,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "العقود المنتهية خلال فترة",NameEn = "Expired Contracts", ModuleName = AppModules.Realestate.ExpiredContractsReport,Route="/realestate/reports/expired_contracts_report"},

                            new MenuItem{ NameAr = "الدليل العقارى",NameEn = "Units", ModuleName = AppModules.Realestate.UnitsReport,Route="/realestate/reports/units_report"},

                            new MenuItem{ NameAr = "كشف حساب إجمالي",NameEn = "Total Account Statement", ModuleName = AppModules.Realestate.TotalAccountStatementReport,Route="/realestate/reports/total_account_statement_report"},

                            new MenuItem{ NameAr = "كشف حساب",NameEn = "Account Statement", ModuleName = AppModules.Realestate.AccountStatementReport,Route="/realestate/reports/account_statement_report"},

                            new MenuItem{ NameAr = "العقود",NameEn = "Contracts", ModuleName = AppModules.Realestate.ContractsReport,Route="/realestate/reports/contracts_report"},

                            new MenuItem{ NameAr = "كشف حساب عقار",NameEn = "Unit Account", ModuleName = AppModules.Realestate.UnitAccountReport,Route="/realestate/reports/unit_account_report"},

                            new MenuItem{ NameAr = "الدفعات الغير محصلة",NameEn = "Uncollected Payments", ModuleName = AppModules.Realestate.UncollectedPaymentsReport,Route="/realestate/reports/uncollected_payments_report"},

                        }
                    },

                    //Fleet /  Configuration
                    new MenuItem
                    {
                       NameAr = "البيانات الاساسية",
                       NameEn = "Configuration",
                       Order = 1,
                       TagId =(int)ModulesTags.Fleet,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "الموظفين",NameEn = "Employees", ModuleName = AppModules.Fleet.Employees,Route="/fleet/configuration/employees"},
                            new MenuItem{ NameAr = "السائقين",NameEn = "Drivers", ModuleName = AppModules.Fleet.Drivers,Route="/fleet/configuration/drivers"},
                            new MenuItem{ NameAr = "المعدات",NameEn = "Equipment", ModuleName = AppModules.Fleet.Equipment,Route="/fleet/configuration/equipment"},

                        }
                    },

                      //Fleet  / Equipment Movement
                    new MenuItem
                    {
                       NameAr = "حركة المعدات",
                       NameEn = "Equipment Movement",
                       Order = 2,
                       TagId =(int)ModulesTags.Fleet,
                        Children = new List<MenuItem>
                        {

                            new MenuItem{ NameAr = "حركة المعدات",NameEn = "Equipment Movement", ModuleName = AppModules.Fleet.EquipmentMovement,Route="/fleet/equipment_movement/equipmentmovement"},
                            new MenuItem{ NameAr = "حركة السيارات",NameEn = "Cars Movement", ModuleName = AppModules.Fleet.CarsMovement,Route="/fleet/equipment_movement/carsmovement"},


                        }
                    },

                    //Fleet  / Equipment Accounts
                    new MenuItem
                    {
                       NameAr = "حسابات المعدات",
                       NameEn = "Equipment Accounts",
                       Order = 3,
                       TagId =(int)ModulesTags.Fleet,
                        Children = new List<MenuItem>
                        {

                            new MenuItem{ NameAr = "ترحيل المديونية",NameEn = "Debt Transfer", ModuleName = AppModules.Fleet.DebtTransfer,Route="/fleet/equipment_accounts/debt_transfer"},
                            new MenuItem{ NameAr = "اصدارة فاتورة السيارات",NameEn = "Vehicle Invoice", ModuleName = AppModules.Fleet.VehicleInvoice,Route="/fleet/equipment_accounts/vehicle_invoice"},
                            new MenuItem{ NameAr = "إصدار فاتورة المعدات",NameEn = "Equipment Invoice", ModuleName = AppModules.Fleet.EquipmentInvoice,Route="/fleet/equipment_accounts/equipment_invoice"},


                        }
                    },

                     //Fleet  / Equipment Report
                    new MenuItem
                    {
                       NameAr = "التقارير",
                       NameEn = "Reports",
                       Order = 4,
                       TagId =(int)ModulesTags.Fleet,
                        Children = new List<MenuItem>
                        {
                            new MenuItem{ NameAr = "تقرير إنتهاء التأمين والفحص والإستمارة",NameEn = "Equipment", ModuleName = AppModules.Fleet.EquipmentReport,Route="/fleet/reports/equipment_report"},
                            new MenuItem{ NameAr = "رصيد المعدات والسارات",NameEn = "Equipment Balance", ModuleName = AppModules.Fleet.EquipmentBalanceReport,Route="/fleet/reports/equipment_balance_report"},


                        }
                    },

                    //POS
                    new MenuItem
                    {
                       NameAr = "نقاط البيع",
                       NameEn = "POS",
                       Order = 2,
                       TagId =(int)ModulesTags.POS,
                        Children = new List<MenuItem>
                        {
                             new MenuItem{ NameAr = "نقاط البيع",NameEn = "POS", ModuleName = AppModules.POS.POSPOS,Route="/pos/pos"},
                              new MenuItem{ NameAr = "المطبعم",NameEn = "POS", ModuleName = AppModules.POS.POSRestaurant,Route="/pos/restaurant"},

                        }
                    },


                     //Hotel
                    new MenuItem
                    {
                       NameAr = "Hotel",
                       NameEn = "Hotel",
                       Order = 2,
                       TagId =(int)ModulesTags.Hotel,
                        Children = new List<MenuItem>
                        {
                             new MenuItem{ NameAr = "الغرف",NameEn = "POS", ModuleName = AppModules.Hotel.Rooms,Route="/hotel/rooms"},


                        }
                    }

                    ,
                      //StaffAssistant 
                    new MenuItem
                    {
                       NameAr = "مساعد فريق العمل",
                       NameEn = "Staff Assistant",
                       Order = 2,
                       TagId =(int)ModulesTags.StaffAssistant,
                        Children = new List<MenuItem>
                        {
                             new MenuItem{ NameAr = "الحضور والانصراف",NameEn = "Attendance", ModuleName = AppModules.StaffAssistant.Attendance,Route="/staffassistant/attendance"},

                             new MenuItem{ NameAr = "طلباتى",NameEn = "My requests", ModuleName = AppModules.StaffAssistant.MyRequests,Route="/staffassistant/myrequests"},

                             new MenuItem{ NameAr = "حساباتى",NameEn = "My Accounts", ModuleName = AppModules.StaffAssistant.MyAccounts,Route="/staffassistant/myaccounts"},
                        }
                    },

                };
            }
        }
    }
}
