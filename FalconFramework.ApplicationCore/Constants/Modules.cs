﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace FalconFramework
{
    public class AppModules
    {
        public static class Inventory
        {


            //Inventory  / Basic data
            public const string InventoryOverview = "Inventory.InventoryOverview";
            public const string Warehouses = "Inventory.Warehouses";
            public const string Products = "Inventory.Products";
            public const string Operations = "Inventory.Operations";
            public const string TradeClassification = "Inventory.TradeClassification";
            public const string ProductCategories = "Inventory.ProductCategories";
            public const string ProductSubCategories = "Inventory.ProductSubCategories";
            public const string Units = "Inventory.Units";
            public const string Manufacturers = "Inventory.Manufacturers";
            public const string BarcodeGenerate = "Ivnentory.BarcodeGenerate";
            public const string Bonus = "Inventory.Bonus";
            public const string ProductAttributes = "Inventory.ProductAttributes";

            //Inventory   /Operations
            public const string Openingbalance = "Inventory.Openingbalance";
            public const string ReceiptsRequest = "Inventory.ReceiptsRequest";
            public const string DeliveryRequest = "Inventory.DeliveryRequest";
            public const string Receipts = "Inventory.Receipts";
            public const string Delivery = "Inventory.Delivery";
            public const string Transfers = "Inventory.Transfers";
            public const string DeliveryExecute = "Inventory.DeliveryExecute";
            public const string DeliveryNote = "Inventory.DeliveryNote";
            public const string CustomersSamples = "Inventory.CustomersSamples";
            public const string EmployeesCustody = "Inventory.EmployeesCustody";

            //Inventory Adjustments
            public const string InventoryAdjustments = "Inventory.InventoryAdjustments";
            public const string DeductionsAdjustments = "Inventory.DeductionsAdjustments";
            public const string AdditionAdjustments = "Inventory.AdditionAdjustments";
            public const string NetConsumer = "Inventory.NetConsumer";
            public const string SetAvregeCost = "Inventory.SetAvregeCost";

            //Inventory Reports

            public const string ProductsReport = "Inventory.ProductsReport";
            public const string BonusReport = "Inventory.BonusReport";
            public const string ProfitabilityReport = "Inventory.ProfitabilityReport";
            public const string ValuationReport = "Inventory.ValuationReport";
            public const string TotalValuationReport = "Inventory.TotalValuationReport";
            public const string SrialReport = "Inventory.SrialReport";
            public const string ProductsBalanceReport = "Inventory.ProductsBalanceReport";
            public const string TotalsProductsBalanceReport = "Inventory.TotalsProductsBalanceReport";
            public const string MovementByOperationsReport = "MovementByOperationsReport";
            public const string MovementByStoreReport = "MovementByStoreReport";
            public const string ReplenishmentReport = "Inventory.ReplenishmentReport";
            public const string StagnantproductsReport = "Inventory.StagnantproductsReport";
            public const string ExpirationDateReport = "ExpirationDateReport";
            public const string ComponentCostReport = "ComponentCostReport";
            public const string PriceControlReport = " PriceControlReport";
            public const string ConsumptionReport = "ConsumptionReport";
            public const string OperationsControlReport = "OperationsControlReport";





        }

        public static class Admin
        {
            public const string Roles = "Admin.Role";
            public const string Users = "Admin.Users";
            public const string Permissions = "Admin.Permissions";
            public const string Sessions = "Admin.Sessions";
            public const string Companies = "Admin.Companies";
            public const string Currencies = "Admin.Currencies";
            public const string Countries = "Admin.Countries";
            public const string Branches = "Admin.Branches";
            public const string Setting = "Admin.Setting";
            public const string Cityes = "Admin.Cityes";
            public const string Nationality = "Admin.Nationality";
            public const string Departments = "Admin.Departments";
        }
        public static class HR
        {
            //Basic data
            public const string AttendanceMachines = "HR.AttendanceMachines";
            public const string Insurances = "HR.Insurances";
            public const string Shifs = "HR.Shifs";
            public const string Vacations = "HR.Vacations";
            public const string AdditionsTypes = "HR.AdditionsTypes";
            public const string DeductionsTypes = "HR.DeductionsTypes";
            public const string JobDescriptions = "HR.JobDescriptions";
            public const string EmployeesCategories = "HR.EmployeesCategories";
            public const string ShippingCompanies = "HR.ShippingCompanies";
            public const string DocumentsType = "HR.DocumentsType";
            public const string Sponsors = "HR.Sponsors";
            public const string Qualifications = "HR.Qualifications";

            //Employees
            public const string Employees = "HR.Employees";
            public const string SalaryInformation = "HR.SalaryInformation";
            public const string Dependents = "HR.Dependents";
      
            public const string UpdateEmployeeData = "HR.UpdateEmployeeData";

            //Attendance Settings 
            public const string ShiftPlans = "HR.ShiftPlans";
            public const string OfficialHolidays = "HR.OfficialHolidays";
            public const string DaysAndHours = "HR.DaysAndHours";
            public const string EditShiftPlans = "HR.EditShiftPlans";
            public const string WorkingHoursAndPenalties = "HR.WorkingHoursAndPenalties";


            //Attendance 
            public const string Missions = "HR.Missions";
            public const string AbsencePermissions = "HR.AbsencePermissions";
            public const string DownloadAttendance = "HR.DownloadAttendance";
            public const string OverTime = "HR.OverTime";
            public const string ManualAttendance = "HR.ManualAttendance";
            public const string Vacation = "HR.Vacation";
            public const string LatePermissions = "HR.LatePermissions";
            // HR / Benefit Deduction Application
            public const string Benefit_DeductionApplication = "HR.Benefit_DeductionApplication";
            public const string SalaryAdditions = "HR.SalaryAdditions";
            public const string SalaryDeduction = "HR.SalaryDeduction";

            // hr forms

            public const string ReceiptTransactions = "HR.ReceiptTransactions";
            public const string SecondmentContract = "HR.SecondmentContract";
            public const string Assignment = "HR.Assignment";
            public const string LegalIssues = "HR.LegalIssues";
            public const string TicketBooking = "HR.TicketBooking";
            public const string AdministrativeDecision = "HR.AdministrativeDecision";
            public const string Attention = "HR.Attention";
            public const string AppointmentLetter = "HR.AppointmentLetter";
            public const string CustodyReceipt = "HR.CustodyReceipt";
            public const string HealthInsurance = "HR.HealthInsurance";
            public const string Entitlements = "HR.Entitlements";
            public const string Certificate = "HR.Certificate";
            public const string Resignation = "HR.Resignation";
            public const string Warning = "HR.Warning";
            public const string MedicalInspectionLetter = "HR.MedicalInspection Letter";
            public const string HRForms = "HR.HRForms";
            public const string Contracts = "HR.Contracts";

            // Hr transathons

            public const string Visa = "HR.Visa";
            public const string StaffTransferBetweenProjects = "HR.StaffTransferBetweenProjects";
            public const string SalaryUpdate = "HR.SalaryUpdate";
            public const string IqamaUpdate = "IqamaUpdate";
            public const string EmployeeRequests = "HR.EmployeeRequests";
            public const string HousingAllowancePayments = "HR.HousingAllowancePayments";
            public const string Ending = "HR.Ending";
            public const string AnnualDues = "HR.AnnualDues";
            public const string HandwrittenNote = "HR.HandwrittenNote";
            public const string StaffCosts = "StaffCosts";
            public const string ScheduleAdvances = "HR.ScheduleAdvances";

            // HR / Employee Evaluation
            public const string NewEmployeeEvaluation = "HR.NewEmployeeEvaluation";
            public const string EmployeeEvaluation = "HR.EmployeeEvaluation";
            public const string MonthlyEvaluation = "HR.MonthlyEvaluation";
            // HR / Employee Training
            public const string TrainingNeeds = "HR.TrainingNeeds";
            public const string AnnualTrainingPlan = "HR.AnnualTrainingPlan";
            public const string EmployeeTrainingForm = "HR.EmployeeTrainingForm";
            public const string EvaluateTrainingEffectiveness = "HR.EvaluateTrainingEffectiveness";
            public const string TrainingEntities = "HR.TrainingEntities";

            // HR / Security
            public const string Trucking = "HR.Trucking";
            public const string VisitorsRecord = "HR.VisitorsRecord";
            public const string CarTraffic = "HR.CarTraffic";
            // HR / Employee preparation and Printing  salaries
            public const string SalariesPreparation = "HR.SalariesPreparation";
            public const string PrintSalaries = "HR.PrintSalaries";
            public const string IncomeTAX = "HR.IncomeTAX";
            public const string MonthlyIqamaCost = "HR.MonthlyIqamaCost";
            // HR /Reports
            public const string DeductionsReports = "HR.DeductionsReports";
            public const string BonusesandincentivesReports = "HR.BonusesandincentivesReports";
            public const string LoansReports = "HR.LoansReports";
            public const string VacationsReports = "HR.VacationsReports";
            public const string OverTimeReports = "HR.OverTimeReports";
            public const string PermissionsReports = "HR.PermissionsReports";
            public const string AttendanceReports = "HR.AttendanceReports";
            public const string MissionsReports = "HR.MissionsReports";
            public const string InsurancesdueReports = "HR.InsurancesdueReports";
            public const string ManagersReports = "HR.ManagersReports";
            public const string EmployeelifeReports = "HR.EmployeelifeReports";
            public const string DocumentsReports = "HR.DocumentsReports";
            public const string TravelsReports = "HR.TravelsReportsReports";
            public const string DrivinglicencesReports = "HR.DrivinglicencesReports";
            public const string NationalityJobsReports = "HR.NationalityJobsReports";
            public const string Nationality = "HR.Nationality";
            public const string MovementsDuringPeriodReports = "HR.MovementsDuringPeriodReports";
            public const string RecruitedduringPeriodReports = "HR.RecruitedduringPeriodReports";
            public const string DueSalariesReports = "HR.DueSalariesReports";
            public const string PlayoffsReports = "HR.PlayoffsReports";
            public const string MonthlyDeductionsReports = "HR.MonthlyDeductionsReports";





        }
        public static class Accounting
        {
            //Accounting  / Basic data

            public const string CashBoxes = "Accounting.CashBoxes";
            public const string Banks = "Accounting.Banks";
            public const string BankAccount = "Accounting.BankAccount";
            public const string Journal = "Accounting.Journal";
            public const string FinalAccountsTypes = "Accounting.FinalAccountsTypes";
            public const string FinancialAnalysisPreparation = "Accounting.FinancialAnalysisPreparation";
            public const string ChartOfAccounts = "Accounting.ChartOfAccounts";
            public const string AccountsRouting = "Accounting.AccountsRouting";
            public const string EstimatedBudget = "Accounting.EstimatedBudget";
            public const string ClosingPeriod = "Accounting.ClosingPeriod";
            public const string FiscalYear = "Accounting.FiscalYear";
            public const string CostTypes = "Accounting.CostTypes";
            public const string Companies = "Accounting.Companies";
            public const string Partners = "Accounting.Partners";
            public const string TaxGroup = "Accounting.TaxGroup";
            public const string Taxes = "Accounting.Taxes";


            //Accounting  / Opening balance

            public const string CashBoxesOpeningBalance = "Accounting.CashBoxesOpeningBalance";
            public const string EmployeesOpeningBalance = "Accounting.EmployeesOpeningBalance";
            public const string BanksOpeningBalance = "Accounting.BanksOpeningBalance";
            public const string CustomersOpeningBalance = "Accounting.CustomersOpeningBalance";
            public const string SupplierOpeningBalance = "Accounting.SupplierOpeningBalance";
            public const string OpiningandClosingJournals = "Accounting.OpiningandClosingJournals";


            //Accounting  /  Accounting

            public const string ExchangeRequest = "Accounting.ExchangeRequest";
            public const string JournalForAudit = "Accounting.JournalForAudit";
            public const string CashPayments = "Accounting.CashPayments";
            public const string BankPayments = "Accounting.BankPayments";
            public const string CashReceipts = "Accounting.CashReceipts";
            public const string BankReceipts = "Accounting.BankReceipts";
            public const string JournalEntries = "Accounting.JournalEntries";
            public const string PostingFromPOS = "Accounting.PostingFromPOS";
            public const string Custodysettlement = "Accounting.Custodysettlement";
          
            public const string Notices = "Accounting.Notices";
            public const string ChequesBook = "Accounting.ChequesBook";
            public const string NotesReceivable = "Accounting.NotesReceivable";
            public const string NotesPayable = "Accounting.NotesPayable";


            //Accounting  / Letters Of Guarantee

            public const string LettersOfGuaranteeTypes = "Accounting.LettersOfGuaranteeTypes";
            public const string LettersOfGuaranteeLimitations = "Accounting.LettersOfGuaranteeLimitations";
            public const string LettersOfGuarantee = "Accounting.LettersOfGuarantee";
            public const string LettersOfGuaranteeUsed = "Accounting.LettersOfGuaranteeUsed";
            public const string LettersOfGuaranteeBalance = "Accounting.LettersOfGuaranteeBalance";
            public const string RenewalWarrantyPeriod = "Accounting.RenewalWarrantyPeriod";
            public const string ChangingGuaranteeValue = "Accounting.ChangingGuaranteeValue";


            //Accounting  /  Documentary Credits

            public const string DocumentaryCreditsType = "Accounting.DocumentaryCreditsType";
            public const string DocumentaryCreditLimits = "Accounting.DocumentaryCreditLimits";
            public const string DocumentaryCredits = "Accounting.DocumentaryCredits";
            public const string DocumentaryCreditsBalance = "Accounting.DocumentaryCreditsBalance";
            public const string RenewalAccreditationPeriod = "Accounting.RenewalAccreditationPeriod";
            public const string ChangeCreditValue = "Accounting.ChangeCreditValue";


            //Accounting  /  Loans

            public const string LoanTypes = "Accounting.LoanTypes";
            public const string LoanLimits = "Accounting.LoanLimits";
            public const string Loans = "Accounting.Loans";
            public const string RenewalLoanPeriod = "Accounting.RenewalLoanPeriod";
            public const string LoansRepayment = "Accounting.LoansRepayment";
            public const string LoanBalances = "Accounting.LoanBalances";


            //Accounting  /  Assets

            public const string AssetsList = "Accounting.AssetsList";
            public const string AssetsDepreciation = "Accounting.AssetsDepreciation";
            public const string AssetsStatement = "Accounting.AssetsStatement";
            public const string DepreciationList = "Accounting.DepreciationList";
            public const string AssetDisposal = "Accounting.AssetDisposal";


            //Accounting  /  Analytic Accounts

            public const string AnalyticAccountsList = "Accounting.AnalyticAccountsList";
            public const string IndirectCostsSetting = "Accounting.IndirectCostsSetting";
            public const string AnalyticAccountsGroups = "Accounting.AnalyticAccountsGroups";
            public const string AnalyticAccountsDetailed = "Accounting.AnalyticAccountsDetailed";
            public const string AnalyticAccountsTotal = "Accounting.AnalyticAccountsTotal";
            public const string AnalyticAccountsMonthly = "Accounting.AnalyticAccountsMonthly";
            public const string AnalyticAccountsReport = "Accounting.AnalyticAccountsReport";
            public const string MonthlyNetConsumer = "Accounting.MonthlyNetConsumer";
            public const string TotalConsumer = "Accounting.TotalConsumer";
            public const string AnalyticAccountsPO = "Accounting.AnalyticAccountsPO";
            public const string AnalyticAccountsRFQ = "Accounting.AnalyticAccountsRFQ";



            //Accounting  /  Accounting  Reports

            public const string BankAccountsReport = "Accounting.BankAccountsReport";
            public const string CustomerAccountsReport = "Accounting.CustomerAccountsReport";
            public const string PayablesAccountsReport = "Accounting.PayablesAccountsReport";
            public const string LedgerReport = "Accounting.LedgerReport";
            public const string StaffAccountsReport = "Accounting.StaffAccountsReport";
            public const string SalespersonAccountReport = "Accounting.SalespersonAccountReport";
            public const string CashBoxAccountsReport = "Accounting.CashBoxAccountsReport";
            public const string PartnersAccountsReport = "Accounting.PartnersAccountsReport";
            public const string MonthlyAccountReport = "Accounting.MonthlyAccountReport";
            public const string SalariesPrintReport = "Accounting.SalariesPrintReport";
            public const string AccountsMirrorReport = "Accounting.AccountsMirrorReport";
            public const string SalesTaxesReport = "Accounting.SalesTaxesReport";
            public const string JournalEntryReport = "Accounting.JournalEntryReport";
            public const string AgedReceivableReport = "Accounting.AgedReceivableReport";
            public const string AgedPayableReport = "Accounting.AgedPayableReport";
            public const string GeneralLedgerReport = "Accounting.GeneralLedgerReport";
            public const string NotPostedTransactions = "Accounting.NotPostedTransactions";
            public const string PrepaidExpenses = "Accounting.PrepaidExpenses";
            public const string PaymentsAndReceiptReport = "Accounting.PaymentsAndReceiptReport";


            //Accounting  /  Final Accounts

            public const string TrialBalanceReport = "Accounting.TrialBalanceReport";
            public const string TrialBalanceLevelsReport = "Accounting.TrialBalanceLevelsReport";
            public const string RevenuesAnalysisReport = "Accounting.RevenuesAnalysisReport";
            public const string IncomeStatementReport = "Accounting.IncomeStatementReport";
            public const string BalanceSheetReport = "Accounting.BalanceSheetReport";
            public const string EquityChangesReport = "Accounting.EquityChangesReport";
            public const string CustomFinancialStatementsReport = "Accounting.CustomFinancialStatementsReport";
            public const string EstimatedbudgetDeviationReport = "Accounting.EstimatedbudgetDeviationReport";
            public const string CashFlowsReport = "Accounting.CashFlowsReport";
            public const string FinancialSituationReport = "Accounting.FinancialSituationReport";


            //Accounting  /  Financial Analysis

            public const string DepositsMovementReport = "Accounting.DepositsMovementReport";
            public const string FinancialStatementAnalysis = "Accounting.FinancialStatementAnalysis";
            public const string CompareAccountsmonthlyReport = "Accounting.CompareAccountsmonthlyReport";
            public const string ComparingAccountsquarterlyReport = "Accounting.ComparingAccountsquarterlyReport";
            public const string AccountsSummaryReport = "Accounting.AccountsSummaryReport";



        }
        public static class Fleet
        {
            //Fleet  / Basic data

            public const string Employees = "Feet.Employees";
            public const string Drivers = "Feet.Drivers";
            public const string Equipment = "Feet.Equipment";

            //Fleet  / Equipment Movement

            public const string EquipmentMovement = "Fleet.Equipment Movement";
            public const string CarsMovement = "Fleet.Cars Movement";

            //Fleet  / Equipment Accounts

            public const string DebtTransfer = "Fleet.DebtTransfer";
            public const string VehicleInvoice = "Fleet.VehicleInvoice";
            public const string EquipmentInvoice = "Fleet.EquipmentInvoice";


            //Fleet  / Equipment Report

            public const string EquipmentReport = "Fleet.EquipmentReport";
            public const string EquipmentBalanceReport = "Fleet.EquipmentBalanceReport";


        }
        public static class Maintenance
        {

            //Maintenance / Basic Data

            public const string EquipmentList = "Maintenance.EquipmentList";
            public const string MaintenanceTypes = "Maintenance.MaintenanceTypes";


            //Maintenance / Preventive Maintenance

            public const string MaintenancePlan = "Maintenance.MaintenancePlan";


            //Maintenance / Maintenance

            public const string Malfunctions = "Maintenance.Malfunctions";
            public const string DepartmentConsumptionRate = "Maintenance.DepartmentConsumptionRate";
            public const string MaintenanceRequests = "Maintenance.MaintenanceRequests";
            public const string Periodictraffic = "Maintenance.Periodictraffic";
            public const string CalibrationDevice = "Maintenance.CalibrationDevice";


            //Maintenance /   Contracts

            public const string ContractsTypes = "Maintenance.ContractsTypes";
            public const string CallTypes = "Maintenance.CallTypes";
            public const string DeviceMalfunction = "Maintenance.DeviceMalfunction";
            public const string FaultsStages = "Maintenance.FaultsStages";
            public const string DiagnosisSteps = "Maintenance.DiagnosisSteps";
            public const string MaintenanceContract = "Maintenance.MaintenanceContract";
            public const string OpenCall = "Maintenance.OpenCall";
            public const string JobOrder = "Maintenance.JobOrder";


            //Maintenance /   Reports

            public const string CallsReport = "Maintenance.CallsReport";
            public const string ClientmalfunctionsReport = "Maintenance.ClientmalfunctionsReport";


        }
        public static class Manufacturing
        {
            //Manufacturing / Basic Data
            public const string ManufacturingStages = "Manufacturing.ManufacturingStages";

            //Manufacturing / Manufacturing Orders

            public const string ReceiptOrders = "Manufacturing.ReceiptOrders";
            public const string OrderCost = "Manufacturing.OrderCost";
            public const string Orders = "Manufacturing.Orders";
            public const string ProductionStagesFollow = "Manufacturing.ProductionStagesFollow";
            public const string AssemblyAndDisassembly = "Manufacturing.AssemblyAndDisassembly";
            public const string MaterialRequests = "Manufacturing.MaterialRequests";
            public const string ManufacturingPlan = "Manufacturing.ManufacturingPlan";


            //Manufacturing / Reports
            public const string OrdersReport = "Manufacturing.OrdersReport";
            public const string Qualityreport = "Manufacturing.Qualityreport";


        }
        public static class Project
        {

            //Project / Basic Data

            public const string Customers = "Project.Customers";
            public const string Consultants = "Project.Consultants";
            public const string TendersType = "Project.TendersType";
            public const string TypeOfAttitudeTender = "Project.TypeOfAttitudeTender";
            public const string TenderArrangeTypes = "Project.TenderArrangeTypes";
            public const string TenderActivityTypes = "Project.TenderActivityTypes";
            public const string BusinessType = "Project.BusinessType";
            public const string Tenders = "Project.Tenders";
            public const string TendersDeductions = "Project.TendersDeductions";
            public const string CompetitorsTyep = "Project.CompetitorsTyep";
            public const string Competitors = "Project.Competitors";


            //Project / Tender Study

            public const string TenderGroupsTypes = "Project.TenderGroupsTypes";
            public const string TenderModels = "Project.TenderModels";
            
            public const string TenderComponents = "Project.TenderComponents";
            public const string Mainworks = "Project.Mainworks";
            public const string SubWorks = "Project.SubWorks";
            public const string PriceAnalysisItems = "Project.PriceAnalysisItems";
            public const string BillsofMaterials = "BillsofMaterials";
            public const string BillsofMaterialsStudy = "Project.BillsofMaterialsStudy";


            //Project / Operations

            public const string Drawing = "Project.Drawing";
            public const string SampleApproval = "Project.SampleApproval";
            public const string AssignmentOrder = "Project.AssignmentOrder";
           
            


            //Project / Contracts And Extracts

            public const string ContractsTypes = "ContractsTypes";
            public const string SubcontractorsContracts = "SubcontractorsContracts";
            public const string SubcontractorsExtracts = "SubcontractorsExtracts";
            public const string CustomersExtracts = "CustomersExtracts";
           


            //Project / Report
            public const string TendersTechnicalAndFinancialReport = "Project.TendersTechnicalAndFinancialReport";
            public const string TenderStudyReport = "Project.TenderStudyReport";
            public const string TotalProject = "Project.TotalProject";
            public const string ProjectFinancial = "Project.ProjectFinancial";
            public const string ProjectValue = "Project.ProjectValue";
            public const string Extracts = "Project.Extracts";
            public const string TotalProjectItem = "Project.TotalProjectItem";
            public const string DetailedProjectItem = "Project.DetailedProjectItem";


        }
        public static class Purchase
        {
            //Purchase  / Basic data
            public const string PurchasesTypes = "Purchases.PurchasesTypes";
            public const string SuppliersClassification = "Purchases.SuppliersClassification";
            public const string PurchasesTerms = "Purchases.PurchasesTerms";
            public const string Suppliers = "Purchases.Suppliers";
            public const string SupplierEvaluation = "Purchases.SupplierEvaluation";
            

            //Purchase   /orders
            public const string PurchaseRequisition = "Purchases.PurchaseRequisition";
            public const string PurchaseOrders = "Purchases.PurchaseOrders";
            public const string FollowUpRequisition = "Purchases.FollowUpRequisition";
            public const string RFQ = "Purchases.RFQ";
            public const string RFQComparisons = "Purchases.RFQComparisons";

            //Purchase   /Vendor Bills

            public const string Bills = "Purchases.Bills";
            public const string ServiceBills = "Purchases.ServiceBills";
            public const string BillsReturns = "Purchases.BillsReturns";
            public const string ReturnsServiceBills = "Purchases.ReturnsServiceBills";

            //Purchase   /Reports

            public const string PurchasesReport = "Purchases.PurchasesReport";
            public const string PurchasesReturnsReport = "Purchases.PurchasesReturnsReport";
            public const string PurchasesTaxReport = "Purchases.PurchasesTaxReport";
            public const string SuppliersAccountInMothesReport = "Purchases.SuppliersAccountInMothesReport";
            public const string PurchasesPeriodicallyReport = "Purchases.PurchasesPeriodicallyReport";
            public const string purchasedProductsReport = "Purchases.purchasedProductsReport";
            public const string Thepositionofsuppliers = "Purchases.Thepositionofsuppliers";
            public const string PurchaseRequisitionsReport = "Purchases.PurchaseRequisitionsReport";
            public const string PurchasOrderReport = "Purchases.PurchasOrderReport";
            public const string PurchaseorderspaymentsReport = "Purchases.PurchaseorderspaymentsReport";
            public const string LandedCostReport = "Purchases.LandedCostReport";
            public const string FollowUpbillsReport = "Purchases.FollowUpbillsReport";


        }
        public static class Realestate
        {
            //Realestate  / Basic data
             
            public const string RealEstateUnits = "Realestate.RealEstateUnits";
            public const string MainProperty = "Realestate.MainProperty";

            //Realestate  / Contracts

            public const string RentContruct = "Realestate.RentContruct";
            public const string RentContructExtension = "Realestate.RentContructExtension";
            public const string RentReceiptVoucher = "Realestate.RentReceiptVoucher";
            public const string RentContructSettlement = "Realestate.RentContructSettlement";
            public const string MaintenanceAndServices = "Realestate.MaintenanceAndServices";
            public const string BookingAndContracting = "Realestate.BookingAndContracting";
            public const string InstallmentReceiptVoucher = "Realestate.InstallmentReceiptVoucher";


            //Realestate  / Reports

            public const string ExpiredContractsReport = "Realestate.ExpiredContractsReport";
            public const string UnitsReport = "Realestate.UnitsReport";
            public const string TotalAccountStatementReport = "Realestate.TotalAccountStatementReport";
            public const string AccountStatementReport = "Realestate.AccountStatementReport";
            public const string ContractsReport = "Realestate.ContractsReport";
            public const string UnitAccountReport = "Realestate.UnitAccountReport";
            public const string UncollectedPaymentsReport = "Realestate.UncollectedPaymentsReport";


        }
        public static class Sales
        {
            //Sales  / Basic data

            public const string Salesperson = "Salse.Salesperson";
            public const string SalesTypes = "Salse.SalesTypes";
            public const string SalespersonCommission = "Salse.SalespersonCommission";
            public const string CustomersCategory = "Salse.CustomersCategory";
            public const string SalesAndCommissionTerms = "Salse.SalesAndCommissionTerms";
            public const string Guarantor = "Salse.Guarantor";
            public const string MainCustomer = "Salse.MainCustomer";
            public const string Customers = "Salse.Customers";
            public const string Pricelists = "Salse.Pricelists";
            public const string SalesTeams = "Salse.SalesTeams";
            public const string SalespersonCategories = "Salse.SalespersonCategories";
            public const string SalesPaymentTerms = "Salse.SalesPaymentTerms";


            //Sales   /Cars Showrooms

            public const string Cars = "Salse.Cars";
            public const string Persons = "Salse.Persons";
            public const string CarRemoval = "Salse.CarRemoval";
            public const string InstallmentsPayment = "Salse.InstallmentsPayment";
            public const string PersonsAccounts = "Salse.PersonsAccounts";
            public const string CarSales = "Salse.CarSales";
            public const string CarSalesReport = "Salse.CarSalesReport";

            //Sales   /Ttansportations

            public const string BillOfLading = "Salse.BillOfLading";
            public const string BillOfSeaLading = "Salse.BillOfSeaLading";
            public const string TransferOrder = "Salse.TransferOrder";
            public const string CarsData = "Salse.CarsData";
            public const string CarsDrivers = "Salse.CarsDrivers";
            public const string ports = "Salse.ports";
            public const string TransportationCompanies = "Salse.TransportationCompanies";
            public const string Transportation = "Salse.Transportation";
            public const string CarRevenue = "Salse.CarRevenue";



            //Sales   /Orders

            
            public const string Warranties = "Salse.Warranties";
         
            public const string ServicesQuotations = "Salse.ServicesQuotations";
            public const string Quotations = "Salse.Quotations";


            //Sales   /Customer invoice


            public const string SalesTaxInvoice = "Salse.SalesTaxInvoice";
            public const string SalesTaxReturns = "Salse.SalesTaxReturns";
            public const string ServiceSalesTaxInvoice = "Salse.ServiceSalesTaxInvoice";
            public const string ServiceSalesTaxInvoiceReturns = "Salse.ServiceSalesTaxInvoiceReturns";
            public const string SalesInvoice = "Salse.SalesInvoice";
            public const string SalesReturns = "Salse.SalesReturns";
            public const string ServiceSalesInvoice = "Salse.ServiceSalesInvoice";
            public const string ServiceSalesInvoiceReturns = "Salse.ServiceSalesInvoiceReturns";


            //Sales   /Reports

            public const string SalesReport = "Salse.SalesReport";
            public const string SalesReturnReport = "SalesReturnReport";
            public const string InvoicesReport = "InvoicesReport";
            public const string ProductSaleReport = "Salse.ProductSaleReport";
            public const string CustomerProducts = "CustomerProducts";
            public const string AnnualSalesAnalysis = "Salse.AnnualSalesAnalysis";
            public const string SalespersonCustomer = "SalespersonCustomer";
            public const string InvoiceEarningsReport = "Salse.InvoiceEarningsReport";
            public const string SalespersonCommissionsPercentage = "SalespersonCommissionsPercentage";
            public const string SalespersonCommissionsValue = "SalespersonCommissionsValue";
            public const string TotalSalesReport = "Salse.TotalSalesReport";
            public const string DeliveryOrderReport = "DeliveryOrderReport";
            public const string RentReport = "Salse.RentReport";
            public const string ProductOrdersReport = "ProductOrdersReport";
            public const string NetSales = "NetSales";
            public const string ContractsReport = "Salse.ContractsReport";
            public const string SalesTax = "Salse.SalesTax";
            public const string DeliveryReport = "Salse.DeliveryReport";



        }
        public static class CRM
        {
            public const string CustomerSurvey = "CRM.Customer Survey";
            public const string CustomerRequests = "CRM.Customer Requests";
            public const string Complaints = "CRM.Complaints";
            public const string Leads = "CRM.Leads";
            public const string SaleCRM = "CRM.SaleCRM";
            public const string WhatsappInstance = "CRM.WhatsappInstance";
            public const string SalesTeams = "CRM.SalesTeams";
            public const string CommercialActivities = "CRM.CommercialActivities";
            public const string CustomerCategories = "CRM.CustomerCategories";
            public const string Sectors = "CRM.Sectors";
            public const string complaints = "CRM.complaints";
            public const string CRMCRM = "CRM.CRM";
            public const string Stages = "CRM.Stages";
            public const string SendSMS = "CRM.SendSMS";
            public const string SMSInstance = "CRM.SMSInstance";
            public const string SendEmail = "CRM.SendEmail";
            public const string SMSTemplate = "CRM.SMSTemplate";
            public const string EmailTemplate = "CRM.EmailTemplate";
            public const string EmailInstance = "CRM.EmailInstance";

        }

        public static class POS
        {

            public const string POSPOS = "POS.POS";
            public const string POSRestaurant = "POS.Restaurant";

        }

        public static class Hotel
        {

            public const string Rooms = "Hotel.Rooms";


        }

        public static class StaffAssistant
        {

            public const string Attendance = "StaffAssistant.Attendance";
            public const string MyRequests = "StaffAssistant.MyRequests";
            public const string MyAccounts = "StaffAssistant.MyAccounts";


        }

        

    }
}
