﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs
{

    public class InputPermissionModel
    {
        [Required]
        public string Role { get; set; }
        [Required]
        public string Module { get; set; }

        public bool PrintAction { get; set; }
        public bool DisplayAction { get; set; }
        public bool DeleteAction { get; set; }
        public bool CreateAction { get; set; }
        public bool UpdateAction { get; set; }

        // public IEnumerable<ActionDetails> Actions { get; set; }
    }
    public class PermissionViewModel : InputPermissionModel
    {

    }

    public class ActionDetails
    {
        public bool PrintAction { get; set; }
        public bool DisplayAction { get; set; }
        public bool DeleteAction { get; set; }
        public bool CreateAction { get; set; }
        public bool UpdateAction { get; set; }
    }
}
