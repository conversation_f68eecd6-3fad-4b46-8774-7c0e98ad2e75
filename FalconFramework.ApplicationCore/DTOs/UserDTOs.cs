﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace FalconFramework.ApplicationCore.DTOs
{
    public class InputUser
    {
        public InputUser()
        {
            Roles = new List<string>();
        }

        [Required]
        public string Email { get; set; }
        [Required]
        public string Password { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string PhoneNumber { get; set; }
        public List<string> Roles { get; set; }

        public int? FalconUserId { get; set; }
        public int? EmployeeId { get; set; }


    }

    public class InputUserToUpdate
    {
        [Required]
        public string LastName { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string PhoneNumber { get; set; }
        public List<string> Roles { get; set; }
        public int? FalconUserId { get; set; }
        public int? EmployeeId { get; set; }
    }

    public class UserViewModel
    {
        public string FullName { get; set; }
        public bool IsBlocked { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string Id { get; set; }
        public IList<string> Roles { get; set; }
        public string CompanyId { get; set; }
        public int? FalconUserId { get; set; }
        public int? EmployeeId { get; set; }
    }

    public class UserInfoViewModel
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        //public string UserName { get; set; }
        //public string CompanyId { set; get; }
        //public string? CompanyName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public bool IsBlocked { get; set; }
        public string FullName { get; set; }

        public IEnumerable<RoleViewModel> Roles { get; set; }
        public int? FalconUserId { get; set; }
        public int? EmployeeId { get; set; }
    }

    public class UserFilterModel
    {
        public string CompanyId { get; set; }
        public string RoleName { get; set; }
    }


    public class UserBasicInfo
    {
        public string Id { get; set; }
        public string FullName { get; set; }
    }
}