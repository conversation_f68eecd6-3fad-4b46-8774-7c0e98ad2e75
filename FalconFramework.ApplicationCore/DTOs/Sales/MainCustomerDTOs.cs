using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class MainCustomerViewModel: InputMainCustomer
    {
      

    }
    public class InputMainCustomer
    {
        public long id { get; set; }
        public string name { get; set; }
        public string PriceList { get; set; }
        public string DescountPercent { get; set; }
        

    }

     public class MainCustomerFilterModel
    {
        public string CompanyId { get; set; }
    }

}
