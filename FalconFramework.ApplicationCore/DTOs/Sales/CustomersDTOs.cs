namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class CustomerViewModel : InputCustomer
    {
    }

    public class InputCustomer
    {
        public long Id { get; set; }

        public long? SalesPersonId { get; set; }

        public string NameAr { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string fax { get; set; }
        public string mobile { get; set; }
        public string site { get; set; }

        public string CustomerActivity { get; set; }

        public string Notes { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }

        public long? customerGroupId { get; set; }

        public long? MainCustomerId { get; set; }
        public long? CityID { get; set; }
        public bool? flag { get; set; }

        public int? branchId { get; set; }

        public int? companyId { get; set; }
        public string custtime { get; set; }
        public string custVacaiotn { get; set; }

        public string TaxStatus { get; set; }

        public string Collection { get; set; }

        public string PaymentMethod { get; set; }

        public int? PriceList { get; set; }

        public int? CollectionPeriod { get; set; }

        public string NameEn { get; set; }
        public string Short_Name_Ar { get; set; }
        public string Short_Name_EN { get; set; }

        public int? SupervisorId { get; set; }

        public double? Limet_money { get; set; }
        public string Nationality { get; set; }

        public DateTime? BirthDate { get; set; }

        public string Discount_percent { get; set; }

        public int? OperatorID { get; set; }
        public long? ID_Number { get; set; }
        public string ID_Date_start { get; set; }
        public string ID_Date_End { get; set; }
        public string ID_From { get; set; }

        public string IBAN_NO { get; set; }
        public string BankName { get; set; }
        public string EMail { get; set; }
        public decimal? Increase_Rate { get; set; }
        public long? Tax_registration_Number { get; set; }
        public bool? IsRealEstateCustomer { get; set; }
        public string LoginPassword { get; set; }
        public string LoginPasswordSalt { get; set; }
        public string CommercialRegisterNumber { get; set; }

        public string Qualification { get; set; }
        public string PersonalMail { get; set; }
        public string WorkPhone { get; set; }
        public string HomePhone { get; set; }
        public string RelativeName_1 { get; set; }
        public string RelativeType_1 { get; set; }
        public string RelativePhone_1 { get; set; }
        public string RelativeName_2 { get; set; }
        public string RelativeType_2 { get; set; }
        public string RelativePhone_2 { get; set; }
        public string RelativeName_3 { get; set; }
        public string RelativeType_3 { get; set; }
        public string RelativePhone_3 { get; set; }
        public string EmergencyAddress { get; set; }
        public long? IncomAccountCode { get; set; }
        public string IncomAccountName { get; set; }
        public long? TaxAccountCode { get; set; }
        public string TaxAccountName { get; set; }
        public long? DiscountAccount { get; set; }
        public long? DeductionsAccount { get; set; }
        public string DiscountAccountName { get; set; }
        public string DeductionsAccountName { get; set; }
        public int? CustcountryID { get; set; }

        public string Governorate { get; set; }

        public string City { get; set; }

        public string buildingNumber { get; set; }
        public string postalCode { get; set; }
        public string Custfloor { get; set; }
        public string room { get; set; }
        public string landmark { get; set; }
        public string additionalInformation { get; set; }
        public string ActivityCode { get; set; }
        public long? MaintenanceAccID { get; set; }
        public string MaintenanceName { get; set; }
        public string district { get; set; }
        public string street { get; set; }
        public string Business_personal { get; set; }
        public string IdentityType { get; set; }
        public string IdentityNumber { get; set; }
    }
}
