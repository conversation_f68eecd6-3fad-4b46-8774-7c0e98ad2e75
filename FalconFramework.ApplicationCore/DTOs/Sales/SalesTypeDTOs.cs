﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class SalesTypeViewModel
    {
        public int id { get; set; }
        public string nameAr { get; set; }
        public string nameEn { get; set; }
        public string Branch { get; set; }
        public string Company { get; set; }
        

    }
    public class InputSalesType 
    {

        public int id { get; set; }
        [Required]
        public string nameAr { get; set; }
        public string nameEn { get; set; }
        public int? BranchID { get; set; }
        public int? CompanyID { get; set; }
        public int? invType { get; set; }
        public bool? IsRealEstate_Type { get; set; }
    }
}
