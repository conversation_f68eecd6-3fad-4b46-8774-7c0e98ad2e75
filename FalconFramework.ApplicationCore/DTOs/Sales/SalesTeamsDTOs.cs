using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class SalesTeamsViewModel
    {
        public int id { get; set; }
        public string name { get; set; }
        public bool use_leads { get; set; }
        public bool use_opportunities { get; set; }
        public string email { get; set; }
        public int? user_id { get; set; }

    }
    public class InputSalesTeams : SalesTeamsViewModel
    {
        
    }

     public class SalesTeamsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
