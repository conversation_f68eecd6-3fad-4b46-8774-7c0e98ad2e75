using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class SalespersonCommissionsViewModel: InputSalespersonCommissions
    {
       
    }
    public class InputSalespersonCommissions
    {
        public int id { get; set; }
        public double CollectionPeriod { get; set; }
        public double CollectionPercent { get; set; }
        public int SalespersonId { get; set; }


    }

     public class SalespersonCommissionsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
