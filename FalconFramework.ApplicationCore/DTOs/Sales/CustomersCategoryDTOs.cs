using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class CustomersCategoryViewModel: InputCustomersCategory
    {
       
    }
    public class InputCustomersCategory
    {
        public long id { get; set; }
        public string nameAr { get; set; }
        public int? BranchID { get; set; }
        public int? ComanyID { get; set; }
        public string nameEn { get; set; }

    }

     public class CustomersCategoryFilterModel
    {
        public string CompanyId { get; set; }
    }

}
