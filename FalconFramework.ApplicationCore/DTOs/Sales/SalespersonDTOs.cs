using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Sales
{
    public class SalespersonViewModel
    {
        public int id { get; set; }
        public string nameAr { get; set; }
        public string nameEn { get; set; }
        public string UserName { get; set; }
        public string CategoryName { get; set; }

    }
    public class InputSalesperson
    {
        public int id { get; set; }
        public int? SalesManTypeId { get; set; }
        public string nameAr { get; set; }
        public string nameEn { get; set; }

        public double? PercentVaue { get; set; }
        public bool? IsFrom_Mksab { get; set; }
        public bool? IsFrom_Egmaly { get; set; }

        public double? ServicePercent { get; set; }

        public bool? srv_IsFrom_Mksab { get; set; }
        public bool? srv_IsFrom_Egmaly { get; set; }


        public double? TargetValue { get; set; }

        public int? UserID { get; set; }
        public byte[] MandopImag { get; set; }



    }

    public class SalespersonFilterModel
    {
        public string CompanyId { get; set; }
    }

}
