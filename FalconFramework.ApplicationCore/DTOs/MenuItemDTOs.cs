﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs
{
    public class InputMenuItem
    {
        [Required]
        public string NameAr { get; set; }
        [Required]
        public string NameEn { get; set; }
        public int TagId { get; set; }
        public int? ParentId { get; set; }

        public int Order { get; set; }
        public string Route { get; set; }

    }

    public class MenuItemViewModel : InputMenuItem
    {
        public MenuItemViewModel()
        {
            Children = new List<MenuItemViewModel>();
        }
        public int Id { get; set; }

        public bool HasChildren
        {
            get
            {
                return Children.Count() > 0;
            }
        }
        public IEnumerable<MenuItemViewModel> Children { get; set; }
        public string ModuleName { get; set; }
    }
}
