﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs
{
    public class InputNotification
    {
        public string Title { set; get; }
        public string TargetUserId { set; get; }
        public string ResourceId { set; get; }
        public string ResourceType { set; get; }
        public string SenderUserId { set; get; }
        public string Body { set; get; }
    }

    public class NotificationViewModel
    {
        public string Id { get; set; }
        public string Title { set; get; }
        public string Body { set; get; }
        public string ResourceId { set; get; }
        public string ResourceType { set; get; }
        public bool IsRead { get; set; }
        public string SentDate { get; set; }
        public string ResourceDescription { get; set; }
        public DateTime ResourceVisitDate { get; set; }
        public int ResourceNumber { get; set; }
    }
}
