using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Manufacturing
{
    public class AssemblyAndDisassemblyViewModel
    {
          public int id { get; set; }
    }
    public class InputAssemblyAndDisassembly
    {
          public int id { get; set; }
    }

     public class AssemblyAndDisassemblyFilterModel
    {
        public string CompanyId { get; set; }
    }

}
