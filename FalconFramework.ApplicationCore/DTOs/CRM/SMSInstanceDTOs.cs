using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.CRM
{
    public class SMSInstanceViewModel : InputSMSInstance
    {
          
    }
    public class InputSMSInstance
    {
        public int Id { get; set; }
        public int CaseID { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string SenderName { get; set; }
        public string DefulatNumbers { get; set; }
        public bool? IsMain { get; set; }
    }

     public class SMSInstanceFilterModel
    {
        public string CompanyId { get; set; }
    }

}
