using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.CRM
{
    public class EmailInstanceViewModel
    {
        public int id { get; set; }
        public string Name { get; set; }
        public string EmailAddress { get; set; }
        public string UserName { get; set; }
        public bool? POP3 { get; set; }
        public bool? IMAP4 { get; set; }
       
    }
    public class InputEmailInstance
    {

        public int id { get; set; }
        public string Name { get; set; }
        public string PassWord { get; set; }
        public string EmailAddress { get; set; }
        public string UserName { get; set; }
        public string IncommingEmailServer { get; set; }
        public string OutgoingEmailServer { get; set; }
        public bool POP3 { get; set; }
        public bool IMAP4 { get; set; }
        public string IncommingPort { get; set; }
        public string OutgoingPort { get; set; }
        public bool OutgoingRequiresAuthentication { get; set; }
        public bool UseUserPasswordForSend { get; set; }
        public bool IncommingRequireSSL { get; set; }
        public bool OutgoingRequireSSL { get; set; }

    }

     public class EmailInstanceFilterModel
    {
        public string CompanyId { get; set; }
    }

}
