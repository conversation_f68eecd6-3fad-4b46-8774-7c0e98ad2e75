using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class LeadsViewModel
    {
         public int id { get; set; }
         public string NameAr { get; set; }
         public string NameEn { get; set; }
         public string ScopeDescripe { get; set; }
         public string ScopeDescripeEn { get; set; }

        public string Address { get; set; }
        public string AddressEn { get; set; }

        public string Phone { get; set; }

        public string Fax { get; }
        public string Email { get; set; }
    }
    public class InputLeads:LeadsViewModel
    {
         
        public int RoomID { get; set; }
        public int MemberID { get; set; }
        public int CategoryID { get; set; }
       
       
      
        public int CountryID { get; set; }
        public string SiteUrl { get; set; }
        public int CityID { get; set; }
       
        public string OwnerName { get; set; }
        public string Representative { get; set; }
      
        public string Administration { get; set; }
        public string AdministrationtPhone { get; set; }
        public string AdministrationFax { get; }
        public string AddressAdministration { get; set; }
        public string Sector { get; }
        public string Elkesm { get; set; }

    }

     public class LeadsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
