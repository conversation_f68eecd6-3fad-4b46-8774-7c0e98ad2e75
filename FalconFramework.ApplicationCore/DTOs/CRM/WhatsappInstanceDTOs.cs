using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.CRM
{
    public class WhatsappInstanceViewModel : InputWhatsappInstance
    {

    }
    public class InputWhatsappInstance
    {

        public int id { get; set; }
        public string Name { get; set; }
        public string AccessToken { get; set; }
        public string BusinessAccount_Id { get; set; }
        public string PhoneNumber_Id { get; set; }
        public string Business_Id { get; set; }
        public bool IsPrimary { get; set; }
        public string DefaultReceiptNumber { get; set; }

    }

    public class WhatsappInstanceFilterModel
    {
        public string CompanyId { get; set; }
    }

}
