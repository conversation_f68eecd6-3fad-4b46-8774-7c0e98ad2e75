
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class StagesViewModel
    {

        public int id { get; set; }
        public string nameAr { get; set; }
        public string nameEn { get; set; }
        public bool? IsWonStage { get; set; }
        public string Requirements { get; set; }

    }
    public class InputStages:StagesViewModel
    {

    }

     public class StagesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
