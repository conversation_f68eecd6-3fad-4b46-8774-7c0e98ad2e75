using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.StaffAssistant
{
    public class StaffattendanceViewModel  
    {
        public long id { get; set; }
        public DateTime Date { get; set; }
        public string DayName { get; set; }
        public string CheckIn { get; set; }
        public string CheckOut { get; set; }

        public string BreakIn { get; set; }
        public string BreakOut { get; set; }

        public double OverTime { get; set; }
        public decimal Hours { get; set; }

        public string AnalyticCode { get; set; }
        //public string AnalyticNameAr { get; set; }
        
        public string CheckInNotes { get; set; }
        public string CheckoutNotes { get; set; }

    }

    public class StaffattendanceCheckIn
    {

        public int CostID { get; set; }
        public string CheckIn { get; set; }
        public string clientDeviceInfo { get; set; }
       
    }

    public class StaffattendanceCheckOut
    {

        public int CostID { get; set; }
        public string CheckOut { get; set; }
        public string clientDeviceInfo { get; set; }

    }
  public class StaffattendanceBreakIn
    {

        public int CostID { get; set; }
        public string BreakIn { get; set; }
    }
  public class StaffattendanceBreakOut
    {

        public int CostID { get; set; }
        public string BreakOut { get; set; }

    }
    public class StaffattendanceFilterModel
    {
        public string CompanyId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

}
