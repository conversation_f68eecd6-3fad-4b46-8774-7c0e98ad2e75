using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Purchase
{
    public class SuppliersViewModel:InputSuppliers
    {
        
    }
    public class InputSuppliers
    {
        public long Id { get; set; }
        public string NameAr { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string fax { get; set; }
        public string mobile { get; set; }
        public string site { get; set; }
        public string email { get; set; }
        public string SupplierActivity { get; set; }
        public string CommercialRegisterNumber { get; set; }
        public string VATNumber { get; set; }
        public string TaxCardNumber { get; set; }
        public string TaxAuthorityOffice { get; set; }
        public string TaxFileNumber { get; set; }

        public string notes { get; set; }
        public string user_id { get; set; }
        public DateTime? action_date { get; set; }
        public bool? flag { get; set; }
        public int? branchId { get; set; }
        public int? companyId { get; set; }
        public int? CollectionDuration { get; set; }

        public string Short_Name_Ar { get; set; }
        public string NameEn { get; set; }

        public string Short_Name_EN { get; set; }
        public long? SupplierGroupID { get; set; }

        public int? SuppliercountryID { get; set; }
        public string Governorate { get; set; }

        public string City { get; set; }

        public string buildingNumber { get; set; }
        public string postalCode { get; set; }
        public string Custfloor { get; set; }
        public string room { get; set; }
        public string landmark { get; set; }
        public string additionalInformation { get; set; }
        public string ActivityCode { get; set; }
    }

     public class SuppliersFilterModel
    {
        public string CompanyId { get; set; }
    }

}
