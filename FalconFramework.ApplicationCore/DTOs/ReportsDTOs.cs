﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework
{
    public class PartnerLeadger
    {
        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int? CurrencyId { get; set; }
        public bool? IsDetails { get; set; }
        public int PartnerId { get; set; }

        public bool ShowPreviousBalance { get; set; }
        public string Language { get; set; }
    }

    public class SalesmanStatement
    {
        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int? CurrencyId { get; set; }
        public bool? IsDetails { get; set; }

        public bool? ShowPreviousBalance { get; set; }
        public int EmployeeId { get; set; }
        public int? BranchId { get; set; }
        public int? CustomerId { get; set; }
        public bool Alphabetical { get; set; }
        //public bool PreviousBalance { get; set; }
    }

    public class LeadgerReport
    {

        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? AccountCode { get; set; }


        public bool? IsDetails { get; set; }

        public int? CaseId { get; set; }
        public int IsTotal { get; set; }
        public int? AnalyticAccountId { get; set; }
        public List<int?> AnalyticAccountCodeList { get; set; }
        public List<int?> AccountCodeList { get; set; }

        public bool? ShowPreviousBalance { get; set; }

        public int FinancialEntityTypeId { get; set; }
        public int FinancialEntityId { get; set; }

        public int AnalyticAccountTypeId { get; set; }
        public int ThirdPartyId { get; set; }
        public int CurrencyId { get; set; }

        public int Branchid { get; set; }

        public int CompanyId { get; set; }

        public string Language { get; set; }
    }

    public class StaffAccountsViewModel
    {
        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int CurrencyId { get; set; }
        public bool? IsDetails { get; set; }

        public bool? ShowPreviousBalance { get; set; }
        public int EmployeeId { get; set; }
    }


    public class MonthlyAccount
    {
        public int CurrencyId { get; set; }
        public int Year { get; set; }
        public int MainAccountCode { get; set; }
        public int FromAccountCode { get; set; }
        public int ToAccountCode { get; set; }
        public long? CostID { get; set; }
    }

    public class CashBoxLedger
    {
        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int CaseId { get; set; }
        public int CurrencyId { get; set; }

        public int CashBoxId { get; set; }
    }

    public class CustomerLedger
    {

        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int CustomerId { get; set; }

        public int CurrencyId { get; set; }

        public int MainCustomerId { get; set; }
        public bool? IsDetails { get; set; }

        public bool? ShowPreviousBalance { get; set; }

        public int CustomerCategoryID { get; set; }
        //public int CompanyId { get; set; }
        public int BranchId { get; set; }
        public int Branchid { get; set; }
        public int SupplierId { get; set; }



    }

    public class BankLedger
    {

        public int Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsDetails { get; set; }
        public int? CaseId { get; set; }

        public int Branchid { get; set; }

        //public int CompanyId { get; set; }
        public int BankId { get; set; }
        public int BankAccountId { get; set; }
        public string Language { get; set; }

        public int CurrencyId { get; set; }

        public bool? ShowPreviousBalance { get; set; }

    }

    public class SupplierLedger
    {

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
        public int? CaseId { get; set; }
        public int SupplierId { get; set; }
        public int CurrencyId { get; set; }
        public int SupplierCategoryID { get; set; }
        public bool? IsDetails { get; set; }
        public string Language { get; set; }
        public bool? ShowPreviousBalance { get; set; }
        public int? CustomerId { get; set; }

        //public int Branchid { get; set; }

        //public int CompanyId { get; set; }


    }

    public enum ReportType
    {
        StoredProcedure,
        Query
    }

    public class DeductionReportViwModel
    {
        public long code { get; set; }
        public DateTime date { get; set; }
        public string name { get; set; }
        public int? EmpVac1 { get; set; }
        public int? EmpVac2 { get; set; }
        public int? EmpVac3 { get; set; }
        public string Holiday { get; set; }
        public int? Flage { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? val { get; set; }
        public string Cut_Type { get; set; }
    }

    public class SalariesViewModel
    {
        public int id;

        public string SalaryGroup { get; set; }
        public List<int?> EmployeeCodeList { get; set; }

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Language { get; set; }
    }

    public class AccountsMirrorModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class SalesTaxesReportModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
    }

    public class JournalEntryViewModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
        public bool? Journals { get; set; }
        public bool? UnbalcedJournals { get; set; }
        public bool? MissingJournals { get; set; }
    }

    public class AgedReceivableViewModel
    {
        public int FromCustomerID { get; set; }
        public int ToCustomerID { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
        public int? CaseId { get; set; }
    }
    public class AgedPayableViewModel
    {
        public int FromSupplierId { get; set; }
        public int ToSupplierId { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
        public int? CaseId { get; set; }
    }
    public class UnpostedEntriesViewModel
    {
        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string Language { get; set; }
        public bool? JournalEntries { get; set; }
        public bool? InventoryMovement { get; set; }
    }
    public class GeneralLedgerViewModel
    {

        public DateTime? FromDate { get; set; }
        public int Year { get; set; }
        //public int? CaseId { get; set; }
        public DateTime? ToDate { get; set; }
        public string Language { get; set; }
        public int? PatentCode { get; set; }
        public int? CurrencyId { get; set; }
        public bool IncludeZeroBalances { get; set; }
        public int? FinancialEntityTypeId { get; set; }
        public int? FinancialEntityId { get; set; }
        public bool GroupByFinancialEntity { get; set; }
        public string FinancialEntityName { get; set; }
        public bool GroupBySubAccount { get; set; }
        public List<int?> CostList { get; set; }
        public List<int?> AccountCodeList { get; set; }
        public int? AnalyticAccountId { get; set; }
        public int? AccountCode { get; set; }
    }


    public class PrepaidExpensesViewModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
    public class ReceivableAndPayableVoucherViewModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int AcctionType { get; set; }
    }

    public class TrialBalanceViewModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int CurrencyId { get; set; }
        public string Language { get; set; }
        public bool ShowZeroBalance { get; set; }
        public string Year { get; set; }
        public long? CostID { get; set; }
        public long? CostGroupID { get; set; }
        public int BrunchId { get; set; }
    }

    public class TrialBalanceLevelsViewModel
    {
        public string Language { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Year { get; set; }
        public long? CostID { get; set; }
        public long? CostGroupID { get; set; }
        public bool ShowZeroBalance { get; set; }
        public int? BrunchId { get; set; }
        public int? CurrencyId { get; set; }
    }

    public class RevenueAnalysisViewModel
    {

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class ProfetAndLoseViewModel
    {
        public string Language { get; set; }
        public long? CostID { get; set; } = 0;
        public long? CostGroupID { get; set; } = 0;
        public bool ShowZeroBalance { get; set; } = false;
        public int? BrunchId { get; set; } = 0;
        public int? CurrencyId { get; set; } = 1;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? FromPrevDate { get; set; }
        public DateTime? ToPrevDate { get; set; }
    }

    public class BalanceSheetViewModel
    {
        public string Language { get; set; }
        public string Year { get; set; }
        public long? CostID { get; set; }
        public long? CostGroupID { get; set; }
        public bool ShowZeroBalance { get; set; }
        public int? BrunchId { get; set; }
        public int? CurrencyId { get; set; }
    }


    public class EquityChangesViewModel
    {
        public string Year { get; set; }
        public string PastYear { get; set; }
    }


    public class CustomFinancialStatementsViewModel
    {
        public int CurrencyId { get; set; }
        public int Year { get; set; }
        public int PastYear { get; set; }
        public int MezanyaId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? FromPrevDate { get; set; }
        public DateTime? ToPrevDate { get; set; }
        public int GroupId { get; set; }
        public long? CostID { get; set; }
        public long? CostGroupID { get; set; }
        public int BranchId { get; set; }
        public string Language { get; set; } = "AR";

    }

    public class EstimatedBudgetDeviationViewModel
    {
        public int FromMonth { get; set; }
        public int ToMonth { get; set; }
        public string Year { get; set; }
        public long? CostID { get; set; } = 0;
    }


    public class CashFlowStatementViewModel
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Year { get; set; }
        //public int CompanyID { get; set; }
        public int BranchId { get; set; }
        public int CurrencyId { get; set; }
    }

    public class DepositsMovementViewModel
    {
        public int Year { get; set; }
        public int FromExtract { get; set; } = 0;
    }

    public class FinancialStatementAnalysisViewModel
    {
        public int Year { get; set; }
    }


    public class MonthlyAccountComparisonViewModel
    {
        public int CurrencyId { get; set; }
        public string Year { get; set; }
        public List<string> AccountCodes { get; set; }
    }

    public class QuarterlyAccountComparisonViewModel
    {
        public int CurrencyId { get; set; }
        public string Year { get; set; }
        public List<string> AccountCodes { get; set; }
    }

    public class AccountsSummaryViewModel
    {
        public int CurrencyId { get; set; }
        public string Year { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class AttendanceReportInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class PlayoffsReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class PermissionsReportInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class OverTimeReportInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class Nationality_JobsInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
    public class Movements_DuringInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

    }
    public class DueSalariesReportsInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
    }

    public class DeductionsInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class BonusesInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class LoansInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class VacationsReportInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class DocumentsReportsInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class DrivingLicencesInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
    public class Employee_lifeInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class CalcSalariesInput
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
        public string Language { get; set; }
        public string SalaryGruop { get; set; }

        public bool AllInWork { get; set; }
    }

    public class SalariesInput
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
        public string Language { get; set; }
        public string SalaryGruop { get; set; }
        //public int? SectorId { get; set; }

    }
    public class SalariesViewModels
    {

    }
    public class Insurances_DueInputReport
    {

    }

    public class ManagersReportInputReport
    {

        public int? DepartId { get; set; }
    }
    public class TravelsReportsInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? EmpCode { get; set; }
        public bool? ExpirationDate { get; set; }

    }


    public class RecruitedReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
    public class MissionsReportInputReport
    {
        public bool state;

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }


    public class Monthly_DeductionsInputReport
    {

        public string Language { get; set; } = "AR";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class CrmSalesInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class EquipmentBalanceReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }

    public class products_reportInputReport
    {
        public bool? ShowLastPrice;

        public int Year { get; set; }
    }

    public class BonusReportInputReport
    {

    }
    public class ComponentCostReportInputReport
    {
        public DateTime? date;

        public int WarehoseId { get; set; }
        public long ProdcutId { get; set; }
        public bool? IsDetails { get; set; }
        public int Year { get; set; }
    }
    public class ExpirationDateReportInputReport
    {
        public DateTime? date;

        public int WarehoseId { get; set; }
        public long ProdcutId { get; set; }
        public int? CategoryId { get; set; }
        public int? SubCategoryId { get; set; }
        public int Year { get; set; }
    }
    public class MovementByOperationsReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int? CategoryId { get; set; }
        public bool? IsTotal { get; set; }
        public int? WarehoseId { get; set; }
        public long? ProdcutId { get; set; }
    }

    public class MovementByStoreReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CaseId { get; set; }
        public int? WarehoseId { get; set; }
        public bool? IsTotal { get; set; }
    }
    public class OperationsControlReportInputReport
    {
        public int? Month { get; set; }
        public int? Year { get; set; }
        public int? AnalyticAccountId { get; set; }
    }
    public class PriceControlReportInputReport
    {

        public int? CategoryId { get; set; }
        public int? SubCategoryId { get; set; }

    }


    public class ProductsBalanceReportInputReport
    {


        public DateTime? ToDate { get; set; }
        public int? Year { get; set; }
        public int? WarehoseId { get; set; }
        public string Language { get; set; } = "AR";

        public bool? UseVariants { get; set; }


    }

    public class ProfitabilityReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? WarehoseId { get; set; }
        public long? ProdcutId { get; set; }
        public string Language { get; set; }
        public int Year { get; set; }
    }

    public class ReplenishmentReportInputReport
    {
        public int? CategoryId { get; set; }
        public int? SubCategoryId { get; set; }
        public int? WarehoseId { get; set; }
        public int? TradeTypeId { get; set; }
    }


    public class SrialReportInputReport
    {
        public string ProductSN { get; set; }
    }

    public class StagnantproductsReportInputReport
    {
        public int? CategoryId { get; set; }
        public int? SubCategoryId { get; set; }
        public int? WarehoseId { get; set; }
        public int? NumberOfDays { get; set; }
        public bool? Sales { get; set; }
        public bool? Purchases { get; set; }
        public bool? OpeningBalance { get; set; }
        public bool? Transfeer { get; set; }
        public bool? StockIn { get; set; }
        public bool? StockOut { get; set; }

    }

    public class TotalsProductsBalanceReportInputReport
    {

        public string Language { get; set; }
        public int Year { get; set; }

    }

    public class ValuationReportInputReport
    {
        public int ValuationTypeid { get; set; }
        public int? CategoryId { get; set; }
        public int? SubCategoryId { get; set; }
        public int? TradeTypeId { get; set; }
        public int? WarehoseId { get; set; }
        public DateTime? ToDate { get; set; }
        public string Language { get; set; }
        public int Year { get; set; }
    }


    public class ContactsDropdownInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }
    public class InvoiceListInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int?> Employees { get; set; }
    }



    public class SalesReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsDetails { get; set; }
        public bool? ShowGoodInvoices { get; set; }
        public bool? ShowServiceInvoces { get; set; }
        public bool? ShowTaxInvoices { get; set; }
        public bool? ShowNonTaxInvoices { get; set; }
        public int? ProdcutId { get; set; }
        public int? CustomerId { get; set; }
        public long? WarehouseId { get; set; }
        public int? SalespesronId { get; set; }
        public bool? IsAdmin { get; set; }

    }


    public class SalesReturnReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsDetails { get; set; }
        public bool? ShowGoodInvoices { get; set; }
        public bool? ShowServiceInvoces { get; set; }
        public bool? ShowTaxInvoices { get; set; }
        public bool? ShowNonTaxInvoices { get; set; }
        public long? ProdcutId { get; set; }
        public int? CustomerId { get; set; }
        public long? WarehouseId { get; set; }
        public int? SalespesronId { get; set; }

    }

    public class InvoicesReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? InvoceCollectionStatus { get; set; }
        public long? Journal { get; set; }
        public long? CustomerId { get; set; }
        public long? CustomerType { get; set; }
        public long? InvoiceNo { get; set; }
    }

    public class ProductSaleReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? WarehouseId { get; set; }
        public long? GroupId { get; set; }
        public long? SubGroupId { get; set; }
        public long? ProdcutId { get; set; }



    }


    public class CustomerProductsReportInputReport
    {

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? WarehouseId { get; set; }
        public long? GroupId { get; set; }
        public long? SubGroupId { get; set; }
        public long? CustomerId { get; set; }
        public long? ProdcutId { get; set; }
        public bool IsTotaly { get; set; }

    }

    public class AnnualSalesReportInputReport
    {

        public int? Year { get; set; }


        public bool? IsQuantity { get; set; }


        public string CustomerId { get; set; }


        public int? GroupId { get; set; }


        public int? SubGroupId { get; set; }


        public long? ProdcutId { get; set; }


        public int? FromWarehouseId { get; set; }


        public int? ToWarehouseId { get; set; }


    }



    public class SalespersonCustomerReportInputReport
    {
        public int? SalespersonId { get; set; }

    }

    public class InvoiceEarningsReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? SalespersonId { get; set; }
        public decimal? IndirectCost { get; set; }




    }

    public class SalespersonCommissionsPercentageInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? SalespersonId { get; set; }
        public decimal? IndirectCost { get; set; }
    }

    public class SalespersonCommissionsValueInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

    }

    public class TotalSalesReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        public long? WarehouseId { get; set; }
        public int BranchId { get; set; }
        public int? SalespersonId { get; set; }
        public string UserLanguage { get; set; }


    }

    public class DeliveryOrderReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? SalesOrder { get; set; }
        public bool? Draft { get; set; }
        public bool? Done { get; set; }
        public bool? NotCompleted { get; set; }
        public bool? AllSalesOrder { get; set; }
        public bool? AllOrders { get; set; }
        public int? OrderNO { get; set; }
        public int? InvoiceNO { get; set; }


    }

    public class RentReportInputReport
    {
        public int? Id { get; set; }
        public int? CustomerId { get; set; }
        public int? RentId { get; set; }
        public int? BranchId { get; set; }
    }

    public class ProductOrdersReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? SystemOrders { get; set; }
        public bool? SallahOrders { get; set; }
        public bool? ZidOrders { get; set; }
        public int? BranchId { get; set; }
        public bool? IsInvoiced { get; set; }

        public bool? IsReserved { get; set; }
        public bool? IsNotInvoiced { get; set; }
    }

    public class NetSalesInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
    public class DeliveryReportInputReport
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? DeliveryPersonId { get; set; }
        public object CaseId { get; set; }
    }

    public class ContractsReportInputReport
    {

        public DateTime? ToDate { get; set; }
        public int? GuarantorId { get; set; }

        public long? Supplierid { get; set; }
    }


    /////

    namespace ReportModels
    {
        public class PurchasesReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public bool? IsDetails { get; set; }
            public bool? ShowGoodInvoices { get; set; }
            public bool? ShowServiceInvoces { get; set; }
            public bool? ShowTaxInvoices { get; set; }
            public bool? ShowNonTaxInvoices { get; set; }
            public int? BranchId { get; set; }
            public int? SupplierId { get; set; }
            public int? ProductId { get; set; }
            public long? WarehouseId { get; set; }

        }

        public class PurchasesReturnsReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public bool? IsDetails { get; set; }
            public bool? ShowGoodInvoices { get; set; }
            public bool? ShowServiceInvoces { get; set; }
            public bool? ShowTaxInvoices { get; set; }
            public bool? ShowNonTaxInvoices { get; set; }

            public int? SupplierId { get; set; }
            public int? ProductId { get; set; }
            public long? WarehouseId { get; set; }



        }

        public class PurchasesTaxReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public bool? ShowGoodInvoices { get; set; }

            public long? WarehouseId { get; set; }
            public int? BranchId { get; set; }

            public int? BerchisingTypeId { get; set; }

            public string Language { get; set; }
        }

        public class SuppliersAccountInMonthsReportInputModel
        {

            public int? SupplierId { get; set; }
            public int? Year { get; set; }
            public int? CurrencyId { get; set; }
            public bool? ByNetAmounts { get; set; }
        }

        public class PurchasesPeriodicallyReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }


            public long? WarehouseId { get; set; }
            public int? SupplierId { get; set; }
            public int? SupplierTypeId { get; set; }
        }

        public class PurchasedProductsReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public int? SupplierId { get; set; }
            public long? WarehouseId { get; set; }

            public string WarehousesName { get; set; }
            public string SuplierName { get; set; }
        }

        public class SuppliersProductsReportInputModel
        {

            public int? SupplierId { get; set; }
            public int? ProductId { get; set; }
        }


        public class PurchaseRequisitionsReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public int? CostId { get; set; }
            public string TenderItemNo { get; set; }
        }

        public class PurchaseOrderReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public int? CostId { get; set; }

            public string TenderItemNo { get; set; }

            public int? ProjectId { get; set; }

            public int? SupplierId { get; set; }

            public int? BranchId { get; set; }

            public int? DepartId { get; set; }

        }

        public class PurchaseOrdersPaymentsReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }

        }


        public class LandedCostReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }

        }

        public class FollowUpBillsReportInputModel
        {
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public int? SupplierId { get; set; }
            public bool? DueOrNot { get; set; }

            public int? InvoceCollectionStatus { get; set; }

            public int? Journal { get; set; }
        }

    }

}
