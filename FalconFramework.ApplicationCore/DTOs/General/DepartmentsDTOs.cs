using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.General
{
    public class DepartmentsViewModel
    {
         public long id { get; set; }
        public string NameAr { get; set; }
         public string NameEn { get; set; }
         public string Code { get; set; }
    }
    public class InputDepartments : DepartmentsViewModel
    {
        public int CompanyId { get; set; }
        public int BranchId { get; set; }
    }

     public class DepartmentsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
