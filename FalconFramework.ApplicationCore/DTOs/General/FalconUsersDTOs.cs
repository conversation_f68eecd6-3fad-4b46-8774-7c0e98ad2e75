﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class FalconUsersViewModel
    {
        public int id { get; set; }
        public string Name { get; set; }
        public long? EmpCOde { get; set; }
        public bool? IsAdmin { get; set; }
        public bool? IsSupervisor { get; set; }
        public bool? IsUser { get; set; }
        public bool? ShowPrice { get; set; }
        public bool? Journal { get; set; }

    }
    public class InputFalconUsers
    {
        [Required]
        public int id { get; set; }
        public string Name { get; set; }
        public string Password { get; set; }
        public string UserSkin { get; set; }
        public string UserColore { get; set; }
        public string GridColor { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string Mnue { get; set; }
        public bool? IsAdmin { get; set; }
        public bool? IsSupervisor { get; set; }
        public bool? IsUser { get; set; }
        public bool? ShowPrice { get; set; }
        public bool? Journal { get; set; }
        public string CD_PortName { get; set; }
        public string CD_Parity { get; set; }
        public string CD_Baudrate { get; set; }
        public string CD_StopBits { get; set; }
        public string CD_DataBits { get; set; }
        public bool? UseItemWithowyutBalence { get; set; }
        public bool? RebateItems { get; set; }
        public bool? Show_Item_Palance { get; set; }
        public bool? zeropic { get; set; }
        public bool? stopmessages { get; set; }
        public bool? IsOnline { get; set; }
        public bool? useLoked { get; set; }
        public byte[] Signe { get; set; }
        public bool? Pos { get; set; }
        public bool? orderPos { get; set; }
        public string UserLang { get; set; }
        public bool? Box_Journal { get; set; }
        public bool? Bank_Journal { get; set; }
        public bool? Invoice_Come_Journal { get; set; }
        public bool? Invoice_Out_Journal { get; set; }
        public bool? Store_Journal { get; set; }
        public bool? Effict_Stores { get; set; }
        public bool? Effict_Stores_From_Invoices { get; set; }
        public bool? Journl_Store_Sarf { get; set; }
        public bool? Journl_ConvertBetweenStores { get; set; }
        public bool? Effict_Store_Sarf { get; set; }
        public bool? Effict_ConvertBetweenStores { get; set; }
        public bool? LittersJournal { get; set; }
        public Nullable<double> DiscountPercent { get; set; }
        public bool? SaveNotEqulizeJornal { get; set; }
        public bool? Asits_Journal { get; set; }
        public bool? Chek_Journal { get; set; }
        public bool? Effict_Stores_Mardodat_Invoices { get; set; }
        public bool? Store_Journal_Taswya_Edafa { get; set; }
        public bool? Store_Journal_Taswya_Sarf { get; set; }
        public bool? Effict_Stores_Taswya_Edafa { get; set; }
        public bool? Effict_Store_Taswya_Sarf { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public int? MandopId { get; set; }
        public string Image { get; set; }
        public bool? NotTaxProcess { get; set; }
        public bool? HideSalary { get; set; }
        public bool? SMSActivation { get; set; }
        public string PcName { get; set; }
        public bool? ShowMinPrice { get; set; }
        public bool? See_the_Profits_of_Invoices { get; set; }
        public bool? useAdvancedScreen { get; set; }
        public bool? IssueChque { get; set; }
        public bool? IssueTransfer { get; set; }

    }
}
