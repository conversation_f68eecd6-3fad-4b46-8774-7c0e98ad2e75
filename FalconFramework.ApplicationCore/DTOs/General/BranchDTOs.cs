﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class BranchViewModel
    {
        public int id { get; set; }
        public string Name { get; set; }

    }
    public class InputBranch
    {
        [Required]
        public int id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public int? COM_ID { get; set; }
        public bool? flag { get; set; }
        public string BranchCode { get; set; }
        public string Description { get; set; }
        public string IATACode { get; set; }
        public string GalileoCode { get; set; }
        public string AMADUESCode { get; set; }
        public string Address { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public Guid? ManagerID { get; set; }
        public bool? AllowSMS { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SMSPrice { get; set; }
        public int? SearchesCount { get; set; }
        public int? SearchPeriod { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Discount { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Markup { get; set; }
        public int? TradeType { get; set; }
        public long? CostID { get; set; }
        public string CodeID { get; set; }
        public string IdentityID { get; set; }
        public string DropBoxEmail { get; set; }
        public string DrobBoxToken { get; set; }
        public string DrobBoxPass { get; set; }
        public string OdooHost { get; set; }
        public string OdooUser { get; set; }
        public string OdooPass { get; set; }
        public string OdooDb { get; set; }
        public string OdooPort { get; set; }
        public string EinvoiceType { get; set; }
        public string TokenCertName { get; set; }
        public string SallahClientID { get; set; }
        public string SallaClientSecretKey { get; set; }
        public string SallaClientToken { get; set; }
        public string SmsaPassKey { get; set; }
    }
}
