﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{

    public class InputInsurances
    {
    public int id {get;set;}
	public double EmpFix {get;set;}
	public double EmpChanged {get;set;}
	public double ComFix {get;set;}
	public double ComChanged {get;set;}
	public bool Badal_Sakan {get;set;}


    }

    public class InsurancesViewModel : InputInsurances
    {

    }

    public class InsurancesFilterModel
    {
        public string CompanyId { get; set; }
    }
}