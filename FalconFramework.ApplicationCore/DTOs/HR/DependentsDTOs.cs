using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class DependentsViewModel
    {
        public long id { get; set; }
        public string name { get; set; }
    }
    public class InputDependents: DependentsViewModel
    {
    }

     public class DependentsFilterModel
    {
        public int CompanyId { get; set; }
        public long EmployeeId { get; set; }
    }

}
