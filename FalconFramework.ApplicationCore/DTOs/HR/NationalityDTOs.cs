﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{

    public class InputNationality
    {
        public int id { get; set; }

        public string NameAr { get; set; }

        public string NameEn { get; set; }

        public bool? Citizen { get; set; }

    }

    public class NationalityViewModel : InputNationality
    {

    }

    public class NationalityFilterModel
    {
        public string CompanyId { get; set; }
    }
}