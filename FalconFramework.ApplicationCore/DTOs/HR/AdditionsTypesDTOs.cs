using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class AdditionsTypesViewModel
    {
        public int id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
    }
    public class InputAdditionsTypes
    {
        public int id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public int? CompanyID { get; set; }
        public int? BranchID { get; set; }
       
    }

     public class AdditionsTypesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
