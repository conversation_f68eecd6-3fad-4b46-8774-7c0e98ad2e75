using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class JobDescriptionsViewModel
    {
        public long id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
    }
    public class InputJobDescriptions
    {

        public long id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public string Descriptions { get; set; }
        public string Responsibilities { get; set; }
        public string Conditions { get; set; }
        

    }

     public class JobDescriptionsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
