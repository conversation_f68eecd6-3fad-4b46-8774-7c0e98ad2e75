﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{

    public class InputAttendanceMachines
    {
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? Enabled { get; set; }
        public string IP { get; set; }
        public int? Port { get; set; }
        public string Function { get; set; }
    }

    public class AttendanceMachinesViewModel : InputAttendanceMachines
    {

    }

    public class AttendanceMachinesFilterModel
    {
        public string CompanyId { get; set; }
    }
}