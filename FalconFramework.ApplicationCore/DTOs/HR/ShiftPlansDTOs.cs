using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class ShiftPlansViewModel
    {
        public long Id { get; set; }
        public string ArName { get; set; }
        public string EnName { get; set; }
        public DateTime Date { get; set; }
        public string Dayname { get; set; }
        public long ShiftId { get; set; }
        public string ShiftName { get; set; }

        public long planId { get; set; }
        public string planName { get; set; }

    }
    public class InputShiftPlans
    {
        public long id { get; set; }
    }

    public class ShiftPlansFilterModel
    {
        public string CompanyId { get; set; }
    }

}
