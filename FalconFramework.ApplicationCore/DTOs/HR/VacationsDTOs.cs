using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class VacationsViewModel
    {
        public int id { get; set; }
        public string NameAr { get; set; }
        public int? Balance { get; set; }
        public string NameEn { get; set; }
    }
    public class InputVacations
    {
        public int id { get; set; }
        public string NameAr { get; set; }
        public int? Balance { get; set; }
        public int? BranchID { get; set; }
        public int? CompanyID { get; set; }
        public string NameEn { get; set; }

    }

     public class VacationsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
