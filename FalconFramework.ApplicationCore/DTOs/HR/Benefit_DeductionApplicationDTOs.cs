using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class Benefit_DeductionApplicationViewModel
    {
        public long id { get; set; }
        public string name { get; set;}
    }
    public class InputBenefit_DeductionApplication: Benefit_DeductionApplicationViewModel
    {
    }

     public class Benefit_DeductionApplicationFilterModel
    {
        public string CompanyId { get; set; }
    }

}
