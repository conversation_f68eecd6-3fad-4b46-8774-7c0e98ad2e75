using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class DeductionsTypesViewModel
    {

        public int id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DeductionDays { get; set; }
      

    }
    public class InputDeductionsTypes
    {
        public int id { get; set; }
        public string NameAr { get; set; }
         public string NameEn { get; set; }
        public int? CompanyID { get; set; }
        public int? BranchID { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? DeductionDays { get; set; }
       
    }

     public class DeductionsTypesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
