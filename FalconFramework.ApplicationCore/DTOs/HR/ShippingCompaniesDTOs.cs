using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class ShippingCompaniesViewModel
    {
        public long id { get; set; }

        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Mobile { get; set; }
        public string Website { get; set; }
        public string Scope { get; set; }
        public string Notes { get; set; }
    }
    public class InputShippingCompanies
    {
        public long id { get; set; }
        
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Mobile { get; set; }
        public string Website { get; set; }
        public string Scope { get; set; }
        public string Notes { get; set; }
        public string UserCeate { get; set; }
        public long? CityId { get; set; }
        public int? CompanyId { get; set; }
        public int? BranchId { get; set; }

    }

     public class ShippingCompaniesFilterModel
    {
        public string Name { get; set; }
        public string Address { get; set; }
    }

}
