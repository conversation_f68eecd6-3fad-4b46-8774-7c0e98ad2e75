using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class EmployeeViewModel : InputEmployee
    {

        public string? DepartmentName { get; set; }
        public string? BranchName { get; set; }
        public string? CompanyName { get; set; }
        public string? ShiftName { get; set; }
        public string? job { get; set; }

        public string? Sector { get; set; }
        public string? KafeelName { get; set; }

        public string? Qualification { get; set; }

        public string? Religion { get; set; }

        public string? CountryName { get; set; }
        public string? NationalityName { get; set; }


    }
    public class InputEmployee
    {

        public long Id { get; set; }
        public string? BarCode { get; set; }


        public string? NameAr { get; set; }

        public string? NameEn { get; set; }

        public string? Address { get; set; }


        public string? PhoneNumber { get; set; }

        public string? MobileNumber { get; set; }


        public string? Email { get; set; }


        public string? ReligionName { get; set; }


        public string? Gender { get; set; }


        public string? MaritalStatus { get; set; }

        public string? DateOfBirth { get; set; }



        public long? jobid { get; set; }


        public long? depart_id { get; set; }

        public DateTime? EmploymentStartDate { get; set; }


        public string? InsuranceNumber { get; set; }

        public string? HealthCardNumber { get; set; }


        public string? HijriEntryDate { get; set; }

        public DateTime? SocialInsuranceStartDate { get; set; }



        public DateTime? SocialInsuranceEndDate { get; set; }


        public string? HealthRecordNumber { get; set; }


        public string? InsuranceValue { get; set; }

        public string? MedicalInsuranceStartDate { get; set; }

        public string? MedicalInsuranceEndDate { get; set; }


        public string? QualificationName { get; set; }

        public string? MilitaryStatus { get; set; }


        public string? IDExpiryDateHijri { get; set; }


        public string? PassportExpiryDateHijri { get; set; }


        public string? HiringDateHijri { get; set; }


        public DateTime? EntryDateToCountry { get; set; }

        public string? SocialInsuranceDateHijri { get; set; }


        public string? MedicalInsuranceDateHijri { get; set; }


        public string? MedicalInsuranceEndDateHijri { get; set; }


        public DateTime? IDExpiryDate { get; set; }

        public Nullable<double> BasicSalary { get; set; }

        public Nullable<double> FixedTax { get; set; }


        public Nullable<double> FixedAllowance { get; set; }


        public Nullable<double> HousingAllowance { get; set; }


        public Nullable<double> TransportationAllowance { get; set; }


        public Nullable<double> FoodAllowance { get; set; }


        public Nullable<double> OtherAllowances { get; set; }


        public Nullable<double> HealthCareDiscount { get; set; }


        public Nullable<double> InsuranceSalary { get; set; }

        public Nullable<double> EmployeeFundDeduction { get; set; }


        public string? VisaJobTitleId { get; set; }


        public string? PreviousJobPlace { get; set; }


        public string? WhyLeavePreviousJob { get; set; }


        public DateTime? DateLeavePreviousJob { get; set; }


        public string? SpouseName { get; set; }

        public string? VisaNumber { get; set; }

        public string? SpousePhoneNumber { get; set; }

        public string? BankName { get; set; }



        public string? BirthDateHijri { get; set; }


        public string? EmployerName { get; set; }

        public string? RelativeName { get; set; }
        public string? RelativePhone { get; set; }
        public string? SonCont { get; set; }
        public string? Notes { get; set; }

        public int? ShiftID { get; set; }
        public long? Planid { get; set; }

        public int? branchId { get; set; }

        public int? companyId { get; set; }
        public bool? flag { get; set; }

        public Nullable<double> CommunicationAllowance { get; set; }



        public int? CountryID { get; set; }

        public int? NationalityID { get; set; }
        public string? PassportID { get; set; }
        public string? IqamaID { get; set; }
        public string? Certified { get; set; }
        public string? EmploymentCode { get; set; }



        public string? TicketCount { get; set; }



        public Nullable<double> WorkNatureAllowance { get; set; }


        public string? PassportIssueDate { get; set; }


        public string? LaborOfficeNumber { get; set; }


        public string? BorderNumber { get; set; }


        public string? CompanyNameLaborOffice { get; set; }


        public string? CompanyNumber { get; set; }





        public string? EmployeeStatus { get; set; }
        public string? Statenotes { get; set; }

        public string? SectorName { get; set; }

        public DateTime? GraduationDate { get; set; }

        public DateTime? RetirementDate { get; set; }


        public string? InsuranceStatus { get; set; }


        public string? JobStatus { get; set; }

        public string? ShortName { get; set; }

        public long? AdvanceAccountCode { get; set; }

        public long? CustodyAccountCode { get; set; }

        public long? Sallary_Account { get; set; }

        public string? AdvanceAccount { get; set; }

        public string? CustodyAccount { get; set; }

        public string? Sallary_Account_Name { get; set; }

        public long? SectorId { get; set; }

        public int? UserID { get; set; }

        public string? BankAccountNumber { get; set; }


        public string? SalaryGroup { get; set; }



        public long? ContractPeriod { get; set; }
        public long? YaerlyVacation { get; set; }


        public decimal? YearlyTicket { get; set; }



        public string? ResidencyJobTitle { get; set; }


        public decimal? MedicalInsuranceValue { get; set; }

        public long? CostID { get; set; }

        public bool? AllowSalaryAdvance { get; set; }


        public bool? AllowCustody { get; set; }

        public bool? AllowHousingAllowance { get; set; }

        public long? KafeelID { get; set; }


        public Nullable<double> SchoolAllowance { get; set; }

        public Nullable<double> PercentageAllowance { get; set; }


        public DateTime? StatusDate { get; set; }
        public bool? ISDriver { get; set; }
        public string? WorK_Lic_NO { get; set; }
        public Nullable<double> Work_Lic_Value { get; set; }
        public Nullable<double> ID_Renewual_Value { get; set; }
        public int? StatusCaseID { get; set; }
        public bool? Eff_Badal_Ta3am { get; set; }
        public bool? Eff_Badal_Sakan { get; set; }
        public bool? Eff_Badal_Entekal { get; set; }
        public bool? Eff_Badal_Tabe3a { get; set; }
        public bool? Eff_Badal_Other { get; set; }
        public bool? Eff_Badal_tel { get; set; }
        public bool? Eff_Take_Schole { get; set; }
        public bool? VEff_Badal_Ta3am { get; set; }
        public bool? VEff_Badal_Sakan { get; set; }
        public bool? VEff_Badal_Entekal { get; set; }
        public bool? VEff_Badal_Tabe3a { get; set; }
        public bool? VEff_Badal_Other { get; set; }
        public bool? VEff_Badal_tel { get; set; }
        public bool? VEff_Take_Schole { get; set; }
        public bool? ES_Eff_Badal_Ta3am { get; set; }
        public bool? ES_Eff_Badal_Sakan { get; set; }
        public bool? ES_Eff_Badal_Entekal { get; set; }
        public bool? ES_Eff_Badal_Tabe3a { get; set; }
        public bool? ES_Eff_Badal_Other { get; set; }
        public bool? ES_Eff_Badal_tel { get; set; }
        public bool? ES_Eff_Take_Schole { get; set; }
        public bool? StopSallery { get; set; }
        public bool? StopNewID { get; set; }
        public bool? StopVacation { get; set; }
        public int? JobID_in_ID { get; set; }


        public long? QualificationID { get; set; }


        public int? religionId { get; set; }
        public string? EmployeePassword { get; set; }
        public bool? Stop_To_Bank { get; set; }
        public int? EsdarIqama { get; set; }
        public bool? InsuranceDeduction { get; set; }
        public bool? Vacation30 { get; set; }
        public string? Health_Number { get; set; }
        public DateTime? End_Health_Date { get; set; }

        public string? Municipality { get; set; }

        public int? ReportedToEmpId { get; set; }
        public string? Tier { get; set; }

    }

    public class EmployeeSalariesViewModel : EmployeeViewModel
    {
        public double? BasicSalary { get; set; }
        public double? ChangedSalary { get; set; }
        public double? MealAllowance { get; set; }
        public double? TransportationAllowance { get; set; }
        public double? HousingAllowance { get; set; }
        public double? OtherAllowances { get; set; }
        public double? FixedAllowance { get; set; }
        public double? LocationAllowance { get; set; }
        public double? SchoolAllowance { get; set; }
        public double? TelephoneAllowance { get; set; }


    }

    public class EmployeeFilterModel
    {
        public int? CompanyId { get; set; }
        public int? DepartId { get; set; }
        public int? BranchId { get; set; }

        public int? SectortId { get; set; }
        public int? JobId { get; set; }
    }

    public class NationalIdentity
    {
        [Required]
        public long EmployeeId { get; set; }
        public long? NationalIdentityNo { get; set; }
        public int? YearsAdded { get; set; }
    }
}
