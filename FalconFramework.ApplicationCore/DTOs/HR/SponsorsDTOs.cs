using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.HR
{
    public class SponsorsViewModel: InputSponsors
    {
        
    }
    public class InputSponsors
    {
        public long id { get; set; }
        public string NameAr { get; set; }
        public string TypeName { get; set; }
        public string Phone { get; set; }
        public string NameEn { get; set; }
    }

     public class SponsorsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
