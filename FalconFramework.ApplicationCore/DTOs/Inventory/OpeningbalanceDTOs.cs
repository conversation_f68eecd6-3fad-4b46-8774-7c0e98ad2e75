using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class OpeningbalanceViewModel : InputOpeningbalance
    {




        public string Warehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }
        public string MovementType { get; set; }



    }
    public class InputOpeningbalance
    {
        [Required]
        public long Id { get; set; }
        public long WarehouseId { get; set; }
        public long? Customer_id { get; set; }
        public long? InvoiceNO { get; set; }
        [Required]
        public DateTime effectiveDate { get; set; }
        [Required]
        public long? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public long CompanyId { get; set; }
        [Required]
        public long BranchId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public long? ConsumerID { get; set; }
        public long? InVUsed { get; set; }
        public string DocNO { get; set; }
        public long? TO_WarehouseId { get; set; }
        public long? ProjectID { get; set; }
        public long? ToSupplierId { get; set; }
        public long? ToEmpId { get; set; }
        public long? Destination_Type { get; set; }
        public long? destTypeId { get; set; }

        public virtual ICollection<OpeningbalanceLines> OpeningbalanceLines { get; set; }
    }

    public class OpeningbalanceFilterModel
    {
        public long? CompanyId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? BranchId { get; set; }
        public long? WarehouseId { get; set; }



    }

    public class OpeningbalanceLines
    {

        public long DeliveryId { get; set; }
        public long? LineNo { get; set; }
        public long? ProductId { get; set; }
        public string productName { get; set; }
        public long? unitId { get; set; }
        public string UnitName { get; set; }

        public decimal? Price { get; set; }

        public decimal? Quantity { get; set; }

        public decimal? Amount { get; set; }
        public string SerialNumber { get; set; }

        public decimal? Bonus { get; set; }

        public decimal? UnitQuantity { get; set; }

        public decimal? UnitPrice { get; set; }

        public decimal? DefualtItemCost { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? UnitBalance { get; set; }

        public decimal? Rate { get; set; }

        public decimal? CostAllItemOut { get; set; }

        public decimal? Margin { get; set; }

        public long? CostId { get; set; }

        public long? Account_ID { get; set; }
        public long? WarehouseId { get; set; }
        public long? Store_Account { get; set; }
        public string NotesPerLine { get; set; }
        public string VariantName { get; set; }

        public long ManufacturingOrder { get; set; }
        public long deliveryOrder { get; set; }
        public long Scrap { get; set; }

        public string AnalyticAccountId { get; set; }
        public string AnalyticAccountName { get; set; }

    }

}
