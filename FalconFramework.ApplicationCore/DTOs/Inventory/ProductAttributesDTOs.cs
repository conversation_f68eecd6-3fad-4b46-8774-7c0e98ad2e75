using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class ProductAttributesViewModel: InputProductAttributes
    {
         
    }
    public class InputProductAttributes 
    {
        public int id { get; set; }
        public string Name { get; set; }
        public string WooCommerceCode { get; set; }

    }

     public class ProductAttributesFilterModel
    {
        public string CompanyId { get; set; }
    }


    public class ProductAttributesValuesViewModel : InputProductAttributesValues
    {

    }
    public class InputProductAttributesValues
    {
        public int id { get; set; }
        public string Name { get; set; }
        public int VariantNameId { get; set; }

    }

    public class ProductAttributesValuesFilterModel
    {
        public string CompanyId { get; set; }
    }


}
