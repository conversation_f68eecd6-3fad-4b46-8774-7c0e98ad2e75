﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputProductSubCategory
    {
        public int Subid { get; set; }
        public long? groupid { get; set; }
        public string subname { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string En_Sub_Name { get; set; }
        public string Short_Sub_Code { get; set; }
        public bool? ShowINCashier { get; set; }
        public string Colre { get; set; }
        public bool? ShowINinvoice { get; set; }

        public virtual ProductCategoryViewModel Groups { get; set; }
    }

    public class ProductSubCategoryViewModel : InputProductSubCategory
    {

    }
}
