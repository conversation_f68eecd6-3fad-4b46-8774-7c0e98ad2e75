namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputUnit
    {
        public long Id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public string Code { get; set; }
    }

    public class UnitViewModel : InputUnit
    {

    }

    public class UnitViewModelList
    {
        public long UnitId { get; set; }
        public string UnitName { get; set; }
        public string Code { get; set; }
    }
}
