﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputProductCategory
    {
        public long groupid { get; set; }
        [Required]
        public string groupname { get; set; }
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public string En_Name { get; set; }
        public string Short_Code { get; set; }
        public bool? ShowINCashier { get; set; }
        public int? ClassificationID { get; set; }

        public virtual IEnumerable<ProductSubCategoryViewModel> productSubCategory { get; set; }
    }

    public class ProductCategoryViewModel : InputProductCategory
    {

    }
}
