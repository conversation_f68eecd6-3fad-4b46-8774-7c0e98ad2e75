using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class TransfersViewModel : InputTransfer
    {
        public string FromWarehouse { get; set; }
        public string ToWarehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }
        public string MovementType { get; set; }



    }
    public class InputTransfer
    {
        [Required]
        public long Id { get; set; }
        public long FromWarehouseId { get; set; }

        public long ToWarehouseId { get; set; }
        [Required]
        public DateTime effectiveDate { get; set; }
        [Required]
        public int? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public int CompanyId { get; set; }
        [Required]
        public int BranchId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public int? ConsumerID { get; set; }

        public virtual ICollection<TransferLines> TransferLines { get; set; }

    }

    public class TransferFilterModel
    {
        public long Id { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int CompanyId { get; set; }
        public int BranchId { get; set; }
        public long FromWarehouseId { get; set; }
        public long ToWarehouseId { get; set; } // تم تغييرها من int إلى long
    }


    public class TransferLines
    {
        public long TransferId { get; set; }
        public int? LineNo { get; set; }
        public int? ProductId { get; set; }
        public string productName { get; set; }
        public int? unitId { get; set; }
        public string UnitName { get; set; }
        public string Unite { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }

        public double? Quantity { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Amount { get; set; }

        public string SerialNumber { get; set; }

        public double? Bonus { get; set; }

        public double? UnitQuantity { get; set; }

        public double? UnitPrice { get; set; }

        public double? DefualtItemCost { get; set; }

        public double? UnitCost { get; set; }

        public double? UnitBalance { get; set; }

        public double? Rate { get; set; }

        public double? CostAllItemOut { get; set; }

        public double? Margin { get; set; }

        public long? CostId { get; set; }

        public long? Account_ID { get; set; }
        public long? WarehouseId { get; set; }
        public long? Store_Account { get; set; }
        public string NotesPerLine { get; set; }
        public string VariantName { get; set; }

        public long? ManufacturingOrder { get; set; }
        public long? deliveryOrder { get; set; }
        public long? Scrap { get; set; }

        public string AnalyticAccountId { get; set; }
        public string AnalyticAccountName { get; set; }
    }
}
