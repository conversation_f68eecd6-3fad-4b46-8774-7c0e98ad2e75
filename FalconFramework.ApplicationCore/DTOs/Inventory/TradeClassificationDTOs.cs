﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputTradeClassification
    {
        [Required]
        public string Name { get; set; }
        public int Id { get; set; }
    }

    public class TradeClassificationViewModel : InputTradeClassification
    {

    }
}
