using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InventoryAdjustmentsViewModel : InputInventoryAdjustments
    {
        public string Warehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }

    }
    public class InputInventoryAdjustments
    {
        [Required]
        public long Id { get; set; }
        public long WarehouseId { get; set; }
        [Required]
        public DateTime effectiveDate { get; set; }
        public int? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public int branchId { get; set; }
        [Required]
        public int companyId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public int? ConsumerID { get; set; }
        public bool? Lokked { get; set; }

        public virtual ICollection<InventoryAdjustmentsLines> InventoryAdjustmentsLines { get; set; }

    }

    public class InventoryAdjustmentsFilterModel
    {
        public long Id { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int CompanyId { get; set; }
        public int BranchId { get; set; }
        public long WarehouseId { get; set; }
    }


    public class InventoryAdjustmentsLines
    {

        public long InventoryAdjustmentsId { get; set; }
        public long productId { get; set; }

        public decimal TotalByHande { get; set; }

        public decimal TotalByComputer { get; set; }

        public decimal CalcCompAndHand { get; set; }

        public decimal ValDeff { get; set; }

        public decimal PriceCome { get; set; }

        public decimal inability { get; set; }

        public decimal inabilityValue { get; set; }

        public decimal increase { get; set; }

        public decimal increaseValue { get; set; }
        public string VariantName { get; set; }


    }


}
