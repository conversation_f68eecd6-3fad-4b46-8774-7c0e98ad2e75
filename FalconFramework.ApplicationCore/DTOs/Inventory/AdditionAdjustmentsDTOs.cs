using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class AdditionAdjustmentsViewModel : InputAdditionAdjustments
    {
        public string Warehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }
        public string MovementType { get; set; }

    }
    public class InputAdditionAdjustments
    {
        [Required]
        public long Id { get; set; }
        public long WarehouseId { get; set; }
        [Required]
        public DateTime effectiveDate { get; set; }
        [Required]
        public int? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public int CompanyId { get; set; }
        [Required]
        public int BranchId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public int? AdjustmentNo { get; set; }
        public int? ConsumerID { get; set; }

        public virtual ICollection<AdditionAdjustmentsLines> AdditionAdjustmentsLines { get; set; }

    }


    public class AdditionAdjustmentsFilterModel
    {
        public long Id { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int CompanyId { get; set; }
        public int BranchId { get; set; }
        public long WarehouseId { get; set; }
    }

    public class AdditionAdjustmentsLines
    {

        public long AdjustmentId { get; set; }
        public long? LineNo { get; set; }
        public long? ProductId { get; set; }
        public string productName { get; set; }
        public long? unitId { get; set; }
        public string UnitName { get; set; }

        public decimal? Price { get; set; }

        public decimal? Quantity { get; set; }

        public decimal? Amount { get; set; }
        public string SerialNumber { get; set; }

        public decimal? Bonus { get; set; }

        public decimal? UnitQuantity { get; set; }

        public decimal? UnitPrice { get; set; }

        public decimal? DefualtItemCost { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? UnitBalance { get; set; }

        public decimal? Rate { get; set; }

        public decimal? CostAllItemOut { get; set; }

        public decimal? Margin { get; set; }

        public long? CostId { get; set; }

        public long? Account_ID { get; set; }
        public long? WarehouseId { get; set; }
        public long? Store_Account { get; set; }
        public string NotesPerLine { get; set; }
        public string VariantName { get; set; }

        public long ManufacturingOrder { get; set; }
        public long deliveryOrder { get; set; }
        public long Scrap { get; set; }

        public string AnalyticAccountId { get; set; }
        public string AnalyticAccountName { get; set; }


    }



}
