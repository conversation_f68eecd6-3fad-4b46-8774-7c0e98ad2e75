using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class EmployeesCustodyViewModel:InputEmployeesCustody
    {
           public string Warehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }
        public string MovementType { get; set; }
    }
    public class InputEmployeesCustody
    {
        [Required]
        public long Id { get; set; }
        public long WarehouseId { get; set; }
        public int EmployeeID { get; set; }
       
        [Required]
        public DateTime effectiveDate { get; set; }
        [Required]
        public int? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public int CompanyId { get; set; }
        [Required]
        public int BranchId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
       
        public long? InVUsed { get; set; }
        public string DocNO { get; set; } 

        public bool? IsDelivery { get; set; }

        public virtual ICollection<EmployeesCustodyLines> EmployeesCustodyLines { get; set; }


       

    }

     public class EmployeesCustodyFilterModel
    {
        public string CompanyId { get; set; }
    }

  public class EmployeesCustodyLines
    {

        public long EmployeesCustodyId { get; set; }
        public int? LineNo { get; set; }
        public int? ProductId { get; set; }
        public string SaleUnitName { get; set; }

        public decimal? Price { get; set; }

        public decimal? Quantity { get; set; }

        public decimal? Amount { get; set; }
        public string SerialNumber { get; set; }

        public decimal? UnitQuantity { get; set; }

        public decimal? UnitPrice { get; set; }

        public decimal? DefualtItemCost { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? UnitBalance { get; set; }

        public decimal? Rate { get; set; }

        public decimal? CostAllItemOut { get; set; }


        public int? SalesUnitId { get; set; }
        public long? CostId { get; set; }

        public long? Account_ID { get; set; }
        public int? WarehouseId { get; set; }
        public long? Store_Account { get; set; }
        public string NotesPerLine { get; set; }
        public string VariantName { get; set; }


    }

}
