using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class CustomersSamplesViewModel:InputCustomersSamples
    {
        public string Company { get; set; }
        public string Branch { get; set; }

    }
    public class InputCustomersSamples
    {
        public long id { get; set; }
        public long? customerid { get; set; }
        public string itemdescripe { get; set; }
        public long? empid { get; set; }
        public DateTime? datecome { get; set; }
        public DateTime? dateout { get; set; }      
        public string Recipient { get; set; }
        public string idcard { get; set; }
        public string carno { get; set; }
        public string notes { get; set; }

        public int branchId { get; set; }
        public int companyId { get; set; }
    }

     public class CustomersSamplesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
