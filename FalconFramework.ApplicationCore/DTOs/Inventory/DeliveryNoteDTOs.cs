using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class DeliveryNoteViewModel:InputDeliveryNote
    {
          
    }
    public class InputDeliveryNote
    {
        public long Delivery_ID { get; set; }
        public long InvoiceNo { get; set; }
        public long ItemCode { get; set; }
         
        public decimal? Current_Q { get; set; }
        public DateTime? aDate { get; set; }
        public int? year { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public bool? Delivered { get; set; }
        public long Id { get; set; }
        public int? Charge { get; set; }
        public string DriverName { get; set; }
        public long? InvoiceNoTax { get; set; }
        public int? PrintCount { get; set; }
        public DateTime? DeliveryTime { get; set; }
        
    }

     public class DeliveryNoteFilterModel
    {
        public string CompanyId { get; set; }
    }

}
