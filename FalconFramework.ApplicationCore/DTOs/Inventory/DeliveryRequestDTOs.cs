using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class DeliveryRequestViewModel : InputDeliveryRequest
    {

        public string Warehouse { get; set; }
        public string Company { get; set; }
        public string Branch { get; set; }
        public string MovementType { get; set; }
        public string UserName { get; set; }

    }
    public class InputDeliveryRequest
    {
        [Required]
        public long Id { get; set; }
        public long WarehouseId { get; set; }
        public int? Customer_id { get; set; }
        public long? InvoiceNO { get; set; }
        [Required]
        public DateTime effectiveDate { get; set; }
        [Required]
        public long? Year { get; set; }
        public string Notes { get; set; }
        public DateTime ActionDate { get; set; }
        public string UserName { get; set; }
        [Required]
        public int? CompanyId { get; set; }
        [Required]
        public int? BranchId { get; set; }
        public long? Journal { get; set; }
        public long? MovementTypeId { get; set; }
        public long? ConsumerID { get; set; }
        public long? InVUsed { get; set; }
        public string DocNO { get; set; }
        public int? ProjectID { get; set; }
        public bool? Signed { get; set; }
        public string SigneNotes { get; set; }
        public string UserSigend { get; set; }
        public DateTime? SigneDateTime { get; set; }
        public bool? Lokked { get; set; }
        public long? ToSupplierId { get; set; }
        public long? ToEmpId { get; set; }
        public long? Destination_Type { get; set; }
        public long? destTypeId { get; set; }

        public virtual ICollection<DeliveryRequestLine> DeliveryRequestLines { get; set; }
    }

    public class DeliveryRequestFilterModel
    {
        public int? CompanyId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? BranchId { get; set; }
        public long? WarehouseId { get; set; }

    }

    public class DeliveryRequestLine
    {

        public long DeliveryRequestId { get; set; }
        public int? LineNo { get; set; }
        public long? ProductId { get; set; }
        public string productName { get; set; }
        public int? unitId { get; set; }
        public string UnitName { get; set; }

        public double? Price { get; set; }

        public double? Quantity { get; set; }

        public double? Amount { get; set; }
        public string SerialNumber { get; set; }

        public double? Bonus { get; set; }

        public double? UnitQuantity { get; set; }

        public double? UnitPrice { get; set; }

        public double? DefualtItemCost { get; set; }

        public double? UnitCost { get; set; }

        public double? UnitBalance { get; set; }

        public double? Rate { get; set; }

        public double? CostAllItemOut { get; set; }

        public double? Margin { get; set; }

        public long? CostId { get; set; }

        public long? Account_ID { get; set; }
        public int? WarehouseId { get; set; }
        public long? Store_Account { get; set; }
        public string NotesPerLine { get; set; }
        public string VariantName { get; set; }
        public decimal? PreviousQuantity { get; set; }
        public decimal? ReturnQuantity { get; set; }
        public decimal? NetConsumer { get; set; }
        public decimal? Remaining { get; set; }

        public string AnalyticAccountId { get; set; }
        public string AnalyticAccountName { get; set; }
    }

}
