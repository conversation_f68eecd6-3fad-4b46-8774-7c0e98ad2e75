﻿using FalconFramework.ApplicationCore.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputWarehouse
    {
        [Required]
        public long Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Address { get; set; }
        public string keepber { get; set; }
        public string City { get; set; }
        public bool? IsMain { get; set; }
        [Required]
        public int CompanyId { get; set; }
        [Required]
        public int BranchId { get; set; }

    }

    public class WarehouseViewModel : InputWarehouse
    {
        public long Id { get; set; }
        //public DateTime CreatedDate { get; set; }
    }

    public class WarehouseFilterModel
    {
        public string CompanyId { get; set; }
        
    }
}
