using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
   
   public class BonusViewModel : InputBonus
    {
      
        public string Product { get; set; }

      

    }
    public class InputBonus
    {
        [Required]
        public long Id { get; set; }
       
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
       

      

    }


    public class BonusFilterModel
    {
        public long Id { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

    }

    


}
