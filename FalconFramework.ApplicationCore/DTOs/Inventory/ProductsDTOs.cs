using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Inventory
{
    public class InputProducts
    {
        [Required]
        public long Id { get; set; }
        public string? ShortCode { get; set; }
        public string? Barcode { get; set; }

        public int? IsStorable { get; set; }

        public int? UnitId { get; set; }


        public int? SecondaryUnitId { get; set; }


        public int? ThirdUnitId { get; set; }


        public decimal? SecondaryUnitRate { get; set; }


        public decimal? ThirdUnitRate { get; set; }


        public string? SecondaryUnit { get; set; }


        public long? CategoryId { get; set; }


        public int? SubCategoryId { get; set; }


        public int? classificationId { get; set; }



        public string? NameAr { get; set; }


        public string? NameEn { get; set; }


        public int? ItmRequestLimit { get; set; }

        public int? MaxQuantity { get; set; }


        public int? MinQuantity { get; set; }


        public double? Price { get; set; }

        public double? WholesalePrice { get; set; }


        public bool? PruchasePriceFexed { get; set; }

        public int? Tax { get; set; }

        public int? ProfitMargin { get; set; }


        public string? AdditionalNotes { get; set; }


        public DateTime? ActionDate { get; set; }

        public string? UserName { get; set; }

        public int? branchId { get; set; }

        public int? companyId { get; set; }

        public bool? IsFixedPrice { get; set; }

        public double? PurchasePrice { get; set; }

        public double? WholesaleMargin { get; set; }


        public double? TenderMargin { get; set; }

        public double? TenderPrice { get; set; }

        public double? HighestPriceMargin { get; set; }


        public double? HighestPrice { get; set; }


        public double? MediumPriceMargin { get; set; }


        public double? MediumPrice { get; set; }


        public double? LowPriceMargin { get; set; }


        public double? LowPrice { get; set; }

        public string? PartNo { get; set; }
        public double? DescountPercent { get; set; }

        public double? BonusPercentage { get; set; }



        public decimal? ManufacturerId { get; set; }

        public string? ManufacturerItemCode { get; set; }


        public bool? HasExpiryDate { get; set; }


        public bool? IsActive { get; set; }

        public bool? IsFrozen { get; set; }

        public int? itm_stop_sell { get; set; }
        public int? itm_stop_pur { get; set; }
        public int? itm_print_barcode { get; set; }
        public int? itm_allow_discount { get; set; }
        public Nullable<double> itm_max_disc_per { get; set; }
        public Nullable<double> itm_max_disc_val { get; set; }
        public int? itm_print_name { get; set; }


        public decimal? itm_sell_unit { get; set; }

        public string? StorePlace { get; set; }
        public decimal? Waigt { get; set; }

        public bool? flag { get; set; }

        public decimal? Purchase { get; set; }
        public string? Quntity_Price { get; set; }

        public Nullable<double> Width { get; set; }
        public Nullable<double> highet { get; set; }

        public decimal? Thikness { get; set; }

        public decimal? CountInBox { get; set; }
        public string? ImagePha { get; set; }
        public bool? IsOffer { get; set; }
        public bool? Always_in_stock { get; set; }
        public string? ImagePha2 { get; set; }
        public string? Barcode2 { get; set; }
        public string? Barcode3 { get; set; }
        public string? ItemPrintName { get; set; }
        public string? ItemPrintNameEN { get; set; }
        public long? AccountCode { get; set; }
        public bool? IsWeight { get; set; }
        public Nullable<double> AnotherVat { get; set; }
        public string? etaCodeType { get; set; }

        public string? WooCommerceCode { get; set; }
        public string? SallaCode { get; set; }
        public string? ZidCode { get; set; }
        public string? EtaCodeType2 { get; set; }
        public string? EtaCodeType3 { get; set; }
        public string? EtaCodeType4 { get; set; }
        public string? EtaCodeType5 { get; set; }
        public string? EtaCodeType6 { get; set; }
        public string? EtaCodeType7 { get; set; }
        public string? EtaCodeType8 { get; set; }
        public string? EtaCodeType9 { get; set; }
        public string? EtaCodeType10 { get; set; }
        public string? EtaCode2 { get; set; }
        public string? EtaCode3 { get; set; }
        public string? EtaCode4 { get; set; }
        public string? EtaCode5 { get; set; }
        public string? EtaCode6 { get; set; }
        public string? EtaCode7 { get; set; }
        public string? EtaCode8 { get; set; }
        public string? EtaCode9 { get; set; }
        public string? EtaCode10 { get; set; }
        public string? VAT_Type { get; set; }
        public string? TaxCategoryReasonCode { get; set; }
        public string? TaxCategoryReason { get; set; }
        public bool? Taxable { get; set; }

    }

    public class ProductsViewModel:InputProducts
    {
        public string? Category { get; set; }
        public string? SubCategory { get; set; }
        public string? Branch { get; set; }
        public string? Company { get; set; }
        public string? Classification { get; set; }
        public string? Unit1 { get; set; }
        public int? Unit1Id { get; set; }
        public bool? Product { get; set; }
        public double? Cost { get; set; }
        public int? BranchId { get; set; }
        public int? CompanyId { get; set; }
        public int? ClassificationId { get; set; }
    }


}
