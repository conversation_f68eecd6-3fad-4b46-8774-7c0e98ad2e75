using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs
{
    public class InputCompany
    {
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string CRN { get; set; }
        public string Address { get; set; }
        public string Logo { get; set; }
        public string Header { get; set; }
        public string Footer { get; set; }


        [Required]
        public int id { get; set; }
        public string name { get; set; }
        public string COMP_ADDRESS { get; set; }
        public string COMP_CITY { get; set; }
        public string COMP_GOVER { get; set; }
        public string COMP_COUNTRY { get; set; }
        public string COMP_POB { get; set; }
        public string COMP_PHONE { get; set; }
        public string COMP_FAX { get; set; }
        public string LINKID { get; set; }
        public string EMAIL { get; set; }
        public string TRADENO { get; set; }
        public string VATNO { get; set; }
        public byte[] COMP_LOGO { get; set; }
        public int ProunchID { get; set; }
        public string FilenNO { get; set; }
        public string SalesNO { get; set; }
        public string Mamorya { get; set; }
        public string GM { get; set; }
        public string maglesedara { get; set; }
        public string SaleManger { get; set; }
        public string AcontManger { get; set; }
        public string BurchaseManger { get; set; }
        public string StoreManager { get; set; }
        public string HRManager { get; set; }
        public string ProductManeger { get; set; }
        public string mentManger { get; set; }
        public string CountryName { get; set; }
        public int? GM_2 { get; set; }
        public int? maglesedara_2 { get; set; }
        public int? SaleManger_2 { get; set; }
        public int? AcontManger_2 { get; set; }
        public int? BurchaseManger_2 { get; set; }
        public int? StoreManager_2 { get; set; }
        public int? HRManager_2 { get; set; }
        public int? ProductManeger_2 { get; set; }
        public int? mentManger_2 { get; set; }
        public byte[] Picture_BG { get; set; }
        public byte[] Picture_head { get; set; }
        public byte[] Picture_Footer { get; set; }
        public string LaborNO { get; set; }
        public string FalconNO { get; set; }
        public string GM_IDNO { get; set; }
        public string maglesedara_IDNO { get; set; }
        public string SaleManger_IDNO { get; set; }
        public string AcontManger_IDNO { get; set; }
        public string BurchaseManger_IDNO { get; set; }
        public string StoreManager_IDNO { get; set; }
        public string HRManager_IDNO { get; set; }
        public string ProductManeger_IDNO { get; set; }
        public string mentManger_IDNO { get; set; }
        public string TofficeGM { get; set; }
        public int? TofficeGM_2 { get; set; }
        public string TofficeGM_IDNO { get; set; }
        public string FGM { get; set; }
        public int? FGM_2 { get; set; }
        public string FGM_IDNO { get; set; }
        public string AccReviewGM { get; set; }
        public int? AccReviewGM_2 { get; set; }
        public string AccReviewGM_IDNO { get; set; }
        public string ProjectGM { get; set; }
        public int? ProjectGM_2 { get; set; }
        public string ProjectGM_IDNO { get; set; }
        public string GM_Name { get; set; }
        public string GM_ID { get; set; }
        public DateTime? BirthDay { get; set; }
        public string BirthDay2 { get; set; }
        public string PostalCode { get; set; }
        public string BuildingNO { get; set; }
        public string Private_ZakatNO { get; set; }
        public string ZakatNO { get; set; }
        public DateTime? ZakatEnd { get; set; }
        public string ZakatEnd2 { get; set; }
        public string MunicipalLicense { get; set; }
        public DateTime? MunicipalLicense_Start { get; set; }
        public string MunicipalLicense_Start2 { get; set; }
        public DateTime? MunicipalLicenseEnd { get; set; }
        public string MunicipalLicenseEnd2 { get; set; }
        public DateTime? TradeNoStart { get; set; }
        public string TradeNoStart2 { get; set; }
        public DateTime? TradeNoEnd { get; set; }
        public string TradeNoEnd2 { get; set; }
        public string InsuranceNo { get; set; }
        public string ClassificationCertificate { get; set; }
        public DateTime? ClassificationCertificateEnd { get; set; }
        public string ClassificationCertificateEnd2 { get; set; }
        public string FproID { get; set; }
        public string UserCount { get; set; }
        public string ServerName { get; set; }
        public string DataName { get; set; }
        public string LicCoName { get; set; }
        public string eta_client_id { get; set; }
        public string eta_client_secret_1 { get; set; }
        public string eta_client_secret_2 { get; set; }
        public string eta_code { get; set; }
        public int? ContryID { get; set; }
        public string myfloor { get; set; }
        public string Myroom { get; set; }
        public string mylandmark { get; set; }
        public string myadditionalInformation { get; set; }
        public string FalconSignerKey { get; set; }
        public string FalconSignerURL { get; set; }
        public string DocumentTypeVersion { get; set; }
        public string EstampPIN { get; set; }
    }

    public class CompanyViewModel: InputCompany
    {
        public int Id { get; set; }
    }

    public class InputCompanyList
    {
        public int id { get; set; }
        public string name { get; set; }
       

    }

    public class CompanyListViewModel
    {
        public int id { get; set; }
        public string name { get; set; }
    }

}
