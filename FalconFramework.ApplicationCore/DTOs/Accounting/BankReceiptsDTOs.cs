using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class BankReceiptsViewModel
    {
          public int id { get; set; }
    }
    public class InputBankReceipts
    {
          public int id { get; set; }
    }

     public class BankReceiptsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
