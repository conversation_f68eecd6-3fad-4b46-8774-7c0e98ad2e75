using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class TaxGroupViewModel 
    {
        public int id { get; set; }
        public string Name { get; set; }
    }
    public class InputTaxGroup : TaxGroupViewModel
    {
         
    }

     public class TaxGroupFilterModel
    {
        public string CompanyId { get; set; }
    }

}
