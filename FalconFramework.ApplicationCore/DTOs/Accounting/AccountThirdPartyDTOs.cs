using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class AccountThirdPartyViewModel
    {
        public int id { get; set; }
        public int? CaseId { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public int? TypId { get; set; }
        public string TypeName { get; set; }
    }
    public class InputAccountThirdParty
    {
        public int id { get; set; }
        public int? CaseId { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public int? TypId { get; set; }
        public string TypeName { get; set; }
        

    }

     public class AccountThirdPartyFilterModel
    {
        public string CompanyId { get; set; }
    }

}
