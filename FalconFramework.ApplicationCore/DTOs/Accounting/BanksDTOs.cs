﻿using System;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{

    public class InputBanks
    {
        
        [Required]
        public int id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Bank_Location { get; set; }
        public int? COMP_ID { get; set; }
        public int? ProunchID { get; set; }
        public bool? flag { get; set; }
    }


    public class BanksViewModel
    {
        public int id { get; set; }
        public string Name { get; set; }

        
    }


}