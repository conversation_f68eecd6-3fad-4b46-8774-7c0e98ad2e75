using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class CostTypesViewModel
    {
          public int id { get; set; }
     
        public string NameAr { get; set; }
        public string NameEn { get; set; }
    }
    public class InputCostTypes : CostTypesViewModel
    {
           
    }

     public class CostTypesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
