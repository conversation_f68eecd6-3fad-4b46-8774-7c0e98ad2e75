﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class InputJournal
    {
        public int id { get; set; }
        public string Name { get; set; }
        public bool flag { get; set; }
        public int BranchID { get; set; }
        public int COMP_ID { get; set; }
        public int UserID { get; set; }
    }
    public class JournalViewModel
    {
        public int id { get; set; }
        public string Name { get; set; }
        public int Branch { get; set; }
        public int Company { get; set; }
        public int User { get; set; }
    }
}
