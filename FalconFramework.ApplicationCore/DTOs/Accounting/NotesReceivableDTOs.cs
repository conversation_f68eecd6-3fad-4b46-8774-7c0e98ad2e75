using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class NotesReceivableViewModel
    {
          public int id { get; set; }
    }
    public class InputNotesReceivable
    {
          public int id { get; set; }
    }

     public class NotesReceivableFilterModel
    {
        public string CompanyId { get; set; }
    }

}
