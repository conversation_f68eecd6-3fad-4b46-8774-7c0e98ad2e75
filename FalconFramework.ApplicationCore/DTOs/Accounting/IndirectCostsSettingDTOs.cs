using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class IndirectCostsSettingViewModel
    {
          public int id { get; set; }
    }
    public class InputIndirectCostsSetting
    {
          public int id { get; set; }
    }

     public class IndirectCostsSettingFilterModel
    {
        public string CompanyId { get; set; }
    }

}
