﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
   

    public class inputChartofAccounts
    {

        [Required]
        public long id { get; set; }
        [Required]
        public long? Code { get; set; }
        [Required]
        public string NameAr { get; set; }
        [Required]
        public long? ParentID { get; set; }
        [Required]
        public bool? AccType { get; set; }
        [Required]
        public int? AccLevel { get; set; }
        [Required]
        public bool? ISmain { get; set; }
        [Required]
        public int? MezanyaID { get; set; }
        public int? ACC_Type_ID { get; set; }
        public string LongCode { get; set; }
        public string NameEn { get; set; }
        public string AccountGroup_ID { get; set; }
        public long? ClassificationId { get; set; }
        public long? SortID { get; set; }
        public bool? Machine_Mandatory { get; set; }
        public bool? IsMadeen { get; set; }
        public long? CostID { get; set; }

    }
     
    public class ChartofAccountsViewModel
    {

        public long id { get; set; }
        public long Code { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public long? ParentID { get; set; }
        public bool? ISmain { get; set; }
    }


}
