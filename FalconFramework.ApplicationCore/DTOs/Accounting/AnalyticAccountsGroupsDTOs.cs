using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class AnalyticAccountsGroupsViewModel
    {
          public int id { get; set; }
        public string Name { get; set; }
    }
    public class InputAnalyticAccountsGroups : AnalyticAccountsGroupsViewModel
    {
          
    }

     public class AnalyticAccountsGroupsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
