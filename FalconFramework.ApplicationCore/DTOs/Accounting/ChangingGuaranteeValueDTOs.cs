using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class ChangingGuaranteeValueViewModel
    {
          public int id { get; set; }
    }
    public class InputChangingGuaranteeValue
    {
          public int id { get; set; }
    }

     public class ChangingGuaranteeValueFilterModel
    {
        public string CompanyId { get; set; }
    }

}
