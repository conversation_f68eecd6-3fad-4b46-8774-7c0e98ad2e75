﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
   

    public class InputFinalAccounts
    {

        [Required]
        public int id { get; set; }
        [Required]
        public string NameAr { get; set; }
        public string NameEn { get; set; }

    }

    public class FinalAccountsViewModel
    {

        public int id { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set;}
    }

}
