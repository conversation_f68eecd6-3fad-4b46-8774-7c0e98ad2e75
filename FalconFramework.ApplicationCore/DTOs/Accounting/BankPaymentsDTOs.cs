using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class BankPaymentsViewModel
    {
          public int id { get; set; }
    }
    public class InputBankPayments
    {
          public int id { get; set; }
    }

     public class BankPaymentsFilterModel
    {
        public string CompanyId { get; set; }
    }

}
