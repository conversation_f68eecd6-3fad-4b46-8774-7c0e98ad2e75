using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class BankAccountViewModel 
    {
        public int id { get; set; }
       
        public int BankId { get; set; }
        public string NameAr { get; set; }
        public string Location { get; set; }
        public string AccountNumber { get; set; }
        public int? CompanyId { get; set; }
        public int? BranchId { get; set; }
    }
    public class InputBankAccount 
    {
        public int id { get; set; }

        public int BankId { get; set; }
        public string NameAr { get; set; }
        public string Location { get; set; }
        public string AccountNumber { get; set; }
        public int? CompanyId { get; set; }
        public int? BranchId { get; set; }
    }

    public class BankAccountFilterModel
    {
        public string CompanyId { get; set; }
    }

}
