using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class TaxesViewModel
    {
        public int id { get; set; }
        public string Code { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public int? GruopId { get; set; }

    }
    public class InputTaxes: TaxesViewModel
    {
           
    }

     public class TaxesFilterModel
    {
        public string CompanyId { get; set; }
    }

}
