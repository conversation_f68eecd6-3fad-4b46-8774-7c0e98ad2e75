using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class InputAnalyticAccounts
    {
        public int id { get; set; }
        public string Code { get; set; }
        public string NameAr { get; set; }
        public int ParentID { get; set; }
        public bool? AccType { get; set; }
        public int AccLevel { get; set; }
        public bool? ISmain { get; set; }
        public long? CustID { get; set; }
        public string ShortCode { get; set; }
        public string LongCode { get; set; }
        public bool? Flag { get; set; }
        public Nullable<double> Percent_Allawonce { get; set; }
        public string NameEn { get; set; }
        public int? ClassificationId { get; set; }
        public int? SortID { get; set; }

    }
    public class AnalyticAccountsViewModel
    {


        public int id { get; set; }
        public string Code { get; set; }

        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public int? ParentID { get; set; }
        public bool? ISmain { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }


    public class AnalyticAccountsLocation
    {


        public string Code { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }

    public class AnalyticAccountsViewList
    {

        public string AnalyticAccountId { get; set; }

        public string AnalyticAccountName { get; set; }

    }

}
