using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class JournalForAuditViewModel
    {
          public int id { get; set; }
    }
    public class InputJournalForAudit
    {
          public int id { get; set; }
    }

     public class JournalForAuditFilterModel
    {
        public string CompanyId { get; set; }
    }

}
