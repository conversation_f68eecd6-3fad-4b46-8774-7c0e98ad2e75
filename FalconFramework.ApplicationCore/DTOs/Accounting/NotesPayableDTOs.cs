using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class NotesPayableViewModel
    {
          public int id { get; set; }
    }
    public class InputNotesPayable
    {
          public int id { get; set; }
    }

     public class NotesPayableFilterModel
    {
        public string CompanyId { get; set; }
    }

}
