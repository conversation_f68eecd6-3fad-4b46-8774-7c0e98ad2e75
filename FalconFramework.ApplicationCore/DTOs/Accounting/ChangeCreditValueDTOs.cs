using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class ChangeCreditValueViewModel
    {
          public int id { get; set; }
    }
    public class InputChangeCreditValue
    {
          public int id { get; set; }
    }

     public class ChangeCreditValueFilterModel
    {
        public string CompanyId { get; set; }
    }

}
