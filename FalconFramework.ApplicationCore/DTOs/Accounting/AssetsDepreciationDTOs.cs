using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class AssetsDepreciationViewModel
    {
          public int id { get; set; }
    }
    public class InputAssetsDepreciation
    {
          public int id { get; set; }
    }

     public class AssetsDepreciationFilterModel
    {
        public string CompanyId { get; set; }
    }

}
