using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{
    public class AccountsRoutingViewModel
    {
          public int id { get; set; }
    }
    public class InputAccountsRouting
    {
          public int id { get; set; }
    }

     public class AccountsRoutingFilterModel
    {
        public string CompanyId { get; set; }
    }

}
