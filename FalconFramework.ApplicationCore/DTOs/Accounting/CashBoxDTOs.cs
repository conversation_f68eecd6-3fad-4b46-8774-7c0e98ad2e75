﻿using System;
using System.ComponentModel.DataAnnotations;

namespace FalconFramework.ApplicationCore.DTOs.Accounting
{

    public class InputCashBox
    {
        [Required]
        public int id { get; set; }
        [Required]
        public string NameAr { get; set; }
        public bool? flag { get; set; }
        [Required]
        public int? ProunchID { get; set; }
        public int? COMP_ID { get; set; }
        public bool? IsMain { get; set; }
    }


    public class CashBoxViewModel
    {
        public int id { get; set; }
        public string NameAr { get; set; }

    }


}