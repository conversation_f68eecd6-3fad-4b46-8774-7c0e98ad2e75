{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"FalconERPApp": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "2mb"}], "outputPath": "dist/FalconFramework", "allowedCommonJsDependencies": ["object-path", "apexcharts", "clipboard.js", "devextreme-aspnet-data-nojquery", "prismjs", "exceljs", "sweetalert2", "devextreme-quill", "devexpress-gantt", "devexpress-diagram", "j<PERSON><PERSON>", "rgbcolor", "raf", "core-js/modules/web.dom-collections.iterator.js", "core-js/modules/es.string.trim.js", "core-js/modules/es.string.starts-with.js", "core-js/modules/es.string.split.js", "core-js/modules/es.string.replace.js", "core-js/modules/es.string.match.js", "core-js/modules/es.string.includes.js", "core-js/modules/es.string.ends-with.js", "core-js/modules/es.regexp.to-string.js", "core-js/modules/es.promise.js", "core-js/modules/es.array.reverse.js", "core-js/modules/es.array.reduce.js", "core-js/modules/es.array.iterator.js", "core-js/modules/es.array.index-of.js", "howler", "moment", "awesome-image-viewer", "html2canvas", "jspdf", "dompurify", "recordrtc", "tough-cookie", "node-fetch", "fetch-cookie", "abort-controller", "ws", "eventsource"], "index": "src/index.html", "polyfills": ["src/polyfills.ts", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/robots.txt", "src/sitemap.xml", "src/manifest.json", "src/.htaccess", "src/google-analytics.js", "src/performance-optimization.js"], "styles": ["node_modules/devextreme/dist/css/dx.light.css", "node_modules/@ctrl/ngx-emoji-mart/picker.css", "node_modules/ag-grid-community/styles/ag-grid.css", "node_modules/ag-grid-community/styles/ag-theme-alpine.css", "src/styles.scss", "node_modules/ngx-toastr/toastr.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "20mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "FalconERPApp:build:production"}, "development": {"proxyConfig": "proxy.conf.js", "buildTarget": "FalconERPApp:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "FalconERPApp:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/robots.txt", "src/sitemap.xml", "src/manifest.json", "src/.htaccess", "src/google-analytics.js", "src/performance-optimization.js"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}